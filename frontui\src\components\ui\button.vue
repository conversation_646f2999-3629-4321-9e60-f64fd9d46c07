<template>
  <el-button
    :type="mapVariantToType"
    :size="mapSizeToElSize"
    :disabled="disabled"
    :plain="variant === 'outline'"
    :link="variant === 'link'"
    :text="variant === 'ghost'"
    v-bind="$attrs"
  >
    <slot />
  </el-button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'sm', 'lg', 'icon'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Map shadcn-vue variants to Element Plus types
const mapVariantToType = computed(() => {
  const mapping = {
    default: 'primary',
    destructive: 'danger',
    outline: 'info',
    secondary: 'success',
    ghost: 'default',
    link: 'default'
  }
  return mapping[props.variant] || 'default'
})

// Map shadcn-vue sizes to Element Plus sizes
const mapSizeToElSize = computed(() => {
  const mapping = {
    default: 'default',
    sm: 'small',
    lg: 'large',
    icon: 'default'
  }
  return mapping[props.size] || 'default'
})
</script>