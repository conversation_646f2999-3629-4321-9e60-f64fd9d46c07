<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="教材详情"
    activePage="content-creation"
    activeSubPage="textbook"
  >
    <div class="flex flex-col h-[calc(100vh-80px)]">
      <!-- 工具栏 -->
      <div class="flex justify-between items-center px-4 py-2 border-b border-gray-200 bg-white">
        <div class="flex items-center">
          <h2 class="text-lg font-bold mr-4">{{ textbookData.title }}</h2>
          <span class="text-sm text-gray-500">{{ getSubjectName(textbookData.subject) }}</span>
          <div class="ml-4 flex items-center gap-2">
            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-md">{{ textbookData.targetType === 'high' ? '高职' : '中职' }}</span>
            <span class="px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-md">{{ textbookData.type === 'electronic' ? '电子教材' : '纸质教材' }}</span>
          </div>
        </div>
        
        <div class="flex items-center gap-3">
          <button class="flex items-center gap-1 text-blue-600 hover:text-blue-800 px-3 py-1.5 rounded-md hover:bg-blue-50">
            <i class="material-icons text-base">download</i>
            导出
          </button>
          <button @click="editProject" class="flex items-center gap-1 text-green-600 hover:text-green-800 px-3 py-1.5 rounded-md hover:bg-green-50">
            <i class="material-icons text-base">edit</i>
            编辑
          </button>
          <button @click="backToList" class="flex items-center gap-1 text-gray-600 hover:text-gray-800 px-3 py-1.5 rounded-md hover:bg-gray-50">
            <i class="material-icons text-base">arrow_back</i>
            返回列表
          </button>
        </div>
      </div>
      
      <!-- 详情内容区域 -->
      <div class="flex flex-1 overflow-hidden">
        <!-- 左侧：目录树 -->
        <div class="w-64 bg-gray-50 border-r border-gray-200 flex flex-col h-full overflow-auto">
          <div class="p-3 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-medium text-sm">教材目录</h3>
          </div>
          
          <div class="flex-1 overflow-y-auto p-2">
            <div class="toc-tree">
              <template v-for="(chapter, chIdx) in textbookData.toc" :key="'ch'+chIdx">
                <!-- 章节 -->
                <div 
                  class="toc-chapter p-2 rounded-md cursor-pointer mb-1 font-medium"
                  :class="{'bg-blue-50 text-blue-700': activeChapter === chIdx && activeSection === -1}"
                  @click="selectChapter(chIdx)"
                >
                  {{ chIdx + 1 }}. {{ chapter.title }}
                </div>
                
                <!-- 小节 -->
                <div class="toc-sections ml-4 mb-2">
                  <div 
                    v-for="(section, secIdx) in chapter.sections" 
                    :key="'sec'+secIdx"
                    class="toc-section p-1.5 rounded-md cursor-pointer text-sm"
                    :class="{'bg-blue-50 text-blue-700': activeChapter === chIdx && activeSection === secIdx}"
                    @click="selectSection(chIdx, secIdx)"
                  >
                    {{ chIdx + 1 }}.{{ secIdx + 1 }} {{ section.title }}
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
        
        <!-- 中间：内容展示区 -->
        <div class="flex-1 flex flex-col h-full overflow-hidden bg-white">
          <!-- 当前查看位置指示 -->
          <div class="p-3 border-b border-gray-200 bg-gray-50 flex items-center justify-between">
            <div class="text-sm text-gray-500">
              <span>{{ getCurrentLocation() }}</span>
            </div>
            <div class="text-xs text-gray-500">
              <span>仅查看模式</span>
            </div>
          </div>
          
          <!-- 内容区域 -->
          <div class="flex-1 overflow-auto p-6">
            <div v-if="activeChapter >= 0">
              <!-- 内容标题 -->
              <h2 class="text-xl font-bold mb-4">
                {{ activeSection >= 0 
                    ? `${activeChapter + 1}.${activeSection + 1} ${textbookData.toc[activeChapter].sections[activeSection].title}` 
                    : `${activeChapter + 1}. ${textbookData.toc[activeChapter].title}` }}
              </h2>
              
              <!-- 正文内容 -->
              <div class="prose max-w-none mb-6">
                <div class="min-h-[300px] border border-gray-100 bg-gray-50 rounded-md p-4">
                  {{ getCurrentContent() }}
                </div>
              </div>
              
              <!-- 元数据信息 -->
              <div class="flex justify-between items-center mt-4 border-t border-gray-200 pt-4 text-sm text-gray-500">
                <div>
                  <span>最后更新: 2024-06-10</span>
                </div>
                
                <div>
                  <button @click="editProject" class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
                    <i class="material-icons text-base">edit</i>
                    编辑此内容
                  </button>
                </div>
              </div>
            </div>
            
            <div v-else class="flex flex-col items-center justify-center h-full text-gray-500">
              <i class="material-icons text-5xl mb-4">description</i>
              <p>请从左侧目录选择要查看的章节</p>
            </div>
          </div>
        </div>
        
        <!-- 右侧：教材信息区 -->
        <div class="w-80 border-l border-gray-200 flex flex-col h-full bg-gray-50">
          <div class="p-3 border-b border-gray-200">
            <h3 class="font-medium text-sm">教材信息</h3>
          </div>
          
          <div class="flex-1 overflow-y-auto p-4">
            <div class="space-y-6">
              <!-- 基本信息 -->
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">基本信息</h4>
                <div class="bg-white rounded-md p-3 border border-gray-200">
                  <div class="grid grid-cols-3 gap-2 text-sm">
                    <div class="text-gray-500">标题:</div>
                    <div class="col-span-2 font-medium">{{ textbookData.title }}</div>
                    
                    <div class="text-gray-500">学科:</div>
                    <div class="col-span-2">{{ getSubjectName(textbookData.subject) }}</div>
                    
                    <div class="text-gray-500">面向对象:</div>
                    <div class="col-span-2">{{ textbookData.targetType === 'high' ? '高职' : '中职' }}</div>
                    
                    <div class="text-gray-500">教材类型:</div>
                    <div class="col-span-2">{{ textbookData.type === 'electronic' ? '电子教材' : '纸质教材' }}</div>
                  </div>
                </div>
              </div>
              
              <!-- 适用年级 -->
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">适用年级</h4>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="year in textbookData.years" 
                    :key="year"
                    class="px-2 py-1 bg-blue-50 text-blue-700 rounded-md text-xs"
                  >
                    {{ getYearLabel(year) }}
                  </span>
                </div>
              </div>
              
              <!-- 教材结构 -->
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">教材结构</h4>
                <div class="bg-white rounded-md p-3 border border-gray-200">
                  <div class="text-sm space-y-1">
                    <div>总章节数: {{ textbookData.toc.length }} 章</div>
                    <div>总小节数: {{ getTotalSections() }} 节</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 底部按钮 -->
          <div class="p-3 border-t border-gray-200">
            <button 
              @click="editProject" 
              class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-md flex items-center justify-center gap-2"
            >
              <i class="material-icons text-sm">edit</i>
              编辑教材
            </button>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

const router = useRouter()
const route = useRoute()

// 教师数据
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar
})

// 教材数据
const textbookData = ref({
  id: 1,
  title: '计算机编程基础',
  subject: 'computer',
  targetType: 'high',
  years: ['year1', 'year2'],
  type: 'electronic',
  toc: [
    {
      title: '绪论',
      content: '本章介绍计算机编程的基本概念和历史发展。\n\n计算机编程是人类与计算机交流的方式，通过编程语言将人类的思维转化为计算机可以理解和执行的指令。编程的历史可以追溯到19世纪中叶，从最早的打孔卡片，到现代的高级编程语言，编程技术不断发展和完善。\n\n本教材将系统地介绍编程的基本原理、实践方法和应用场景，帮助学生建立编程思维，掌握编程技能。',
      sections: [
        { 
          title: '课程背景与目标',
          content: '编程是现代信息社会的重要技能，本课程旨在培养学生的编程思维和实践能力。\n\n在数字化转型的大背景下，编程技能已成为各行各业的基本需求。无论是数据分析、网站开发、智能硬件控制，还是人工智能应用，都离不开编程。\n\n本课程的核心目标是：\n1. 建立编程思维和问题解决能力\n2. 掌握基本的编程语法和结构\n3. 能够独立设计和实现简单的程序\n4. 具备进一步学习更高级编程技术的基础'
        },
        { 
          title: '学习方法指导',
          content: '学习编程需要理论与实践相结合，建议同学们在学习理论的同时多动手编写代码。\n\n有效的编程学习方法包括：\n\n1. 循序渐进：从简单概念开始，逐步过渡到复杂内容\n2. 实践导向：每学习一个新概念，立即通过编写代码进行实践\n3. 项目驱动：设定小目标，完成实际的编程项目\n4. 错误学习：把调试错误视为学习过程，从错误中汲取经验\n5. 协作交流：与同学讨论编程问题，相互启发\n\n记住，编程是一项实践性很强的技能，多写代码是提高的唯一途径。'
        }
      ]
    },
    {
      title: '基础概念',
      content: '计算机程序由一系列指令组成，这些指令按照特定的顺序执行以完成特定的任务。\n\n在开始编写程序之前，我们需要理解几个基本概念，如变量、常量、数据类型、运算符等。这些是构建程序的基本元素，就像建筑中的砖块一样。\n\n本章将详细介绍这些基础概念，为后续学习打下坚实基础。',
      sections: [
        { 
          title: '核心术语定义',
          content: '变量：用于存储数据的命名存储空间。变量的值可以在程序执行过程中改变。\n\n常量：程序运行期间值不变的数据。常量一经定义，其值就不能被修改。\n\n数据类型：定义了变量可以存储的数据种类，如整数、浮点数、字符串等。\n\n运算符：用于执行特定操作的符号，如加法(+)、减法(-)、赋值(=)等。\n\n表达式：由变量、常量、运算符组成的式子，会产生一个结果值。\n\n语句：程序中的一个完整指令，以分号或换行结束。'
        },
        { 
          title: '技术发展历程',
          content: '从最早的机器语言到汇编语言，再到高级编程语言，编程技术经历了重大发展。\n\n1. 机器语言：直接使用二进制代码编程，是计算机能直接理解的语言\n2. 汇编语言：使用助记符代替二进制指令，但仍需转换为机器码\n3. 高级语言：更接近人类自然语言，包括：\n   - 过程式语言：如C语言、Pascal\n   - 面向对象语言：如Java、C++、Python\n   - 函数式语言：如Haskell、Lisp\n   - 脚本语言：如JavaScript、PHP\n\n现代编程还涉及多种范式，如声明式编程、事件驱动编程等，程序员可以根据不同问题选择最合适的编程方法。'
        },
        { 
          title: '应用场景分析',
          content: '编程技术在科学计算、商业应用、游戏开发等多个领域有广泛应用。\n\n1. 科学计算与数据分析：使用Python、R等语言进行数据处理、模型训练和可视化\n\n2. 商业应用开发：使用Java、C#等构建企业级应用系统，如ERP、CRM等\n\n3. 游戏开发：使用C++、C#等开发游戏引擎和游戏逻辑\n\n4. Web开发：前端使用HTML、CSS、JavaScript，后端使用PHP、Python、Ruby等\n\n5. 移动应用开发：Android使用Java/Kotlin，iOS使用Swift/Objective-C\n\n6. 嵌入式系统：使用C语言开发物联网设备、智能家居等\n\n不同的应用场景对编程语言和技术栈有不同的要求，但基本的编程思维和原则是通用的。'
        }
      ]
    },
    {
      title: '基本原理',
      content: '本章介绍编程的基本原理和核心概念。\n\n计算机程序的执行遵循一定的规则和原理，理解这些基本原理对于掌握编程至关重要。本章将探讨程序执行的底层机制、不同编程范式的理论基础，以及编程语言实现的技术细节。',
      sections: [
        { 
          title: '理论基础',
          content: '计算机程序的执行基于冯·诺依曼架构，包括存储程序的概念。\n\n冯·诺依曼架构的核心特点是：\n1. 程序和数据存储在同一存储器中\n2. 程序按顺序存储和执行\n3. CPU根据指令地址读取和执行指令\n\n这种架构的优势在于灵活性，缺点是可能存在冯·诺依曼瓶颈（CPU和内存之间的数据传输限制）。\n\n除了冯·诺依曼架构外，还有哈佛架构（指令和数据分开存储）和量子计算等新兴计算模型，这些都为编程提供了不同的理论基础。'
        },
        { 
          title: '计算模型',
          content: '编程语言实现了不同的计算模型，包括过程式编程、面向对象编程等。\n\n主要的编程范式包括：\n\n1. 过程式编程：基于过程调用的概念，程序被组织为一系列的过程或函数\n   - 代表语言：C、Pascal\n   - 特点：顺序执行、模块化、使用变量存储状态\n\n2. 面向对象编程：基于对象和类的概念，将数据和行为封装在一起\n   - 代表语言：Java、C++、Python\n   - 特点：封装、继承、多态\n\n3. 函数式编程：基于数学函数的概念，避免状态变化和可变数据\n   - 代表语言：Haskell、Lisp、部分JavaScript\n   - 特点：不可变数据、函数是一等公民、无副作用\n\n4. 逻辑编程：基于形式逻辑，程序由逻辑关系集合组成\n   - 代表语言：Prolog\n   - 特点：声明式、基于规则推理\n\n每种范式都有其优势和适用场景，现代编程语言往往融合多种范式的特性。'
        },
        { 
          title: '实现方法',
          content: '编程语言通过编译器或解释器将源代码转换为机器可执行的指令。\n\n编译型语言的处理流程：\n1. 源代码 → 编译器 → 目标代码(机器码) → 执行\n2. 优点：执行速度快\n3. 缺点：平台依赖性强\n4. 例如：C、C++、Rust\n\n解释型语言的处理流程：\n1. 源代码 → 解释器 → 直接执行\n2. 优点：跨平台性好、开发效率高\n3. 缺点：执行速度相对较慢\n4. 例如：Python、JavaScript、Ruby\n\n混合型语言：\n1. 源代码 → 编译为中间代码 → 虚拟机解释执行\n2. 平衡了性能和跨平台性\n3. 例如：Java(JVM)、C#(.NET)\n\n现代编程语言实现还涉及垃圾回收、即时编译(JIT)等技术，使得语言既高效又易用。'
        }
      ]
    }
  ]
})

// 学科列表（模拟数据，与创建页面保持一致）
const subjects = ref([
  { id: 'computer', name: '计算机科学' },
  { id: 'math', name: '数学' },
  { id: 'english', name: '英语' },
  { id: 'management', name: '工商管理' },
  { id: 'ai', name: '人工智能' }
])

// 年级选项
const years = [
  { label: '一年级', value: 'year1' },
  { label: '二年级', value: 'year2' },
  { label: '三年级', value: 'year3' },
  { label: '四年级', value: 'year4' },
  { label: '五年级', value: 'year5' }
]

// 获取学科名称
const getSubjectName = (subjectId) => {
  const subject = subjects.value.find(s => s.id === subjectId)
  return subject ? subject.name : '未知学科'
}

// 获取年级标签
const getYearLabel = (yearValue) => {
  const year = years.find(y => y.value === yearValue)
  return year ? year.label : '未知年级'
}

// 获取总小节数
const getTotalSections = () => {
  return textbookData.value.toc.reduce((total, chapter) => {
    return total + (chapter.sections ? chapter.sections.length : 0)
  }, 0)
}

// 浏览相关
const activeChapter = ref(0)
const activeSection = ref(-1)

const selectChapter = (chapterIndex) => {
  activeChapter.value = chapterIndex
  activeSection.value = -1
}

const selectSection = (chapterIndex, sectionIndex) => {
  activeChapter.value = chapterIndex
  activeSection.value = sectionIndex
}

const getCurrentLocation = () => {
  if (activeChapter.value === -1) {
    return '未选择章节'
  }
  
  if (activeSection.value === -1) {
    return `第${activeChapter.value + 1}章：${textbookData.value.toc[activeChapter.value].title}`
  }
  
  return `第${activeChapter.value + 1}章 > ${activeChapter.value + 1}.${activeSection.value + 1} ${textbookData.value.toc[activeChapter.value].sections[activeSection.value].title}`
}

const getCurrentContent = () => {
  if (activeChapter.value === -1) {
    return ''
  }
  
  if (activeSection.value === -1) {
    return textbookData.value.toc[activeChapter.value].content || ''
  }
  
  return textbookData.value.toc[activeChapter.value].sections[activeSection.value].content || ''
}

// 返回列表
const backToList = () => {
  router.push('/teacher/textbook-projects')
}

// 编辑项目
const editProject = () => {
  router.push(`/teacher/textbook/${textbookData.value.id}/edit`)
}

onMounted(() => {
  // 实际应用中，这里应该通过API获取教材数据
  const id = route.params.id
  console.log('加载教材ID:', id)
})
</script>

<style scoped>
.toc-chapter:hover, .toc-section:hover {
  background-color: #f0f9ff;
}

.prose {
  max-width: 65ch;
  color: #374151;
  line-height: 1.6;
}
</style> 