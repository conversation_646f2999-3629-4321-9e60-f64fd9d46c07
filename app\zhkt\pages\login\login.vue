<template>
  <view class="container">
    <view class="login-box">
      <!-- 头部 -->
      <view class="header">
        <text class="title">智慧课堂</text>
        <text class="subtitle">知识改变命运，智慧点亮未来</text>
      </view>
      
      <!-- 登录表单 -->
      <view class="form">
        <view class="form-item">
          <input 
            class="input" 
            type="text" 
            v-model="formData.username" 
            placeholder="请输入账号"
          />
        </view>
        
        <view class="form-item">
          <input 
            class="input" 
            :type="showPassword ? 'text' : 'password'" 
            v-model="formData.password" 
            placeholder="请输入密码"
          />
        </view>
        
        <view class="options">
          <label class="remember">
            <switch 
              :checked="formData.rememberMe"
              @change="handleRememberMeChange"
              color="#07c160"
              style="transform:scale(0.7)"
            />
            <text class="remember-text">记住密码</text>
          </label>
        </view>
        
        <button class="login-btn" @click="handleLogin">登 录</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import request from '@/utils/request'

const userStore = useUserStore()
const showPassword = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 加载保存的登录信息
const loadSavedLoginInfo = () => {
  const savedInfo = uni.getStorageSync('loginInfo')
  if (savedInfo) {
    try {
      const { username, password, rememberMe } = JSON.parse(savedInfo)
      formData.username = username
      formData.password = password
      formData.rememberMe = rememberMe
    } catch (e) {
      console.error('加载保存的登录信息失败:', e)
    }
  }
}

// 保存登录信息
const saveLoginInfo = () => {
  if (formData.rememberMe) {
    const loginInfo = {
      username: formData.username,
      password: formData.password,
      rememberMe: true
    }
    uni.setStorageSync('loginInfo', JSON.stringify(loginInfo))
  } else {
    uni.removeStorageSync('loginInfo')
  }
}

// 处理登录
const handleLogin = async () => {
  if (!formData.username || !formData.password) {
    uni.showToast({
      title: '请输入账号和密码',
      icon: 'none'
    })
    return
  }
  
  try {
    const response = await request({
      url: '/users/login/',
      method: 'POST',
      data: {
        username: formData.username.trim(),
        password: formData.password.trim()
      }
    })
    
    // 保存登录信息
    saveLoginInfo()
    
    // 保存token
    userStore.setToken(response.access)
    
    // 获取用户信息
    await userStore.getUserInfo()
    
    // 跳转到首页
    uni.reLaunch({
      url: '/pages/index/index'
    })
    
  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '用户名或密码错误',
      icon: 'none'
    })
  }
}

// 修改处理函数
const handleRememberMeChange = (e) => {
  console.log('switch change event:', e)
  formData.rememberMe = e.detail.value
  console.log('rememberMe after change:', formData.rememberMe)
}

onMounted(() => {
  // 加载保存的登录信息
  loadSavedLoginInfo()
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 60rpx 60rpx;
}

.login-box {
  width: 100%;
  padding: 0;
  box-shadow: none;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
  margin-top: 60rpx;
  
  .title {
    font-size: 56rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 24rpx;
    display: block;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #999999;
    display: block;
  }
}

.form {
  .form-item {
    margin-bottom: 40rpx;
    padding: 0rpx;
    
    .input {
      width: 100%;
      height: 96rpx;
      background: #f7f7f7;
      border-radius: 12rpx;
      padding: 0 32rpx;
      font-size: 32rpx;
      color: #333333;
      box-sizing: border-box;
      
      &::placeholder {
        color: #999999;
      }
    }
  }
  
  .options {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 60rpx;
    padding: 0rpx;
    
    .remember {
      display: flex;
      align-items: center;
      
      .switch {
        margin-right: 16rpx;
      }
      
      .remember-text {
        font-size: 28rpx;
        color: #666666;
      }
    }
  }
  
  .login-btn {
    width: 100%;
    height: 96rpx;
    background: #07c160;
    border-radius: 12rpx;
    color: #ffffff;
    font-size: 34rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    
    &:active {
      opacity: 0.9;
    }
  }
}
</style> 