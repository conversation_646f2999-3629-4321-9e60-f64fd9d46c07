<template>
  <el-card :shadow="shadow" :body-style="bodyStyle" v-bind="$attrs">
    <template v-if="$slots.header" #header>
      <slot name="header"></slot>
    </template>
    <slot></slot>
  </el-card>
</template>

<script setup>
defineProps({
  shadow: {
    type: String,
    default: 'always',
    validator: (val) => ['always', 'hover', 'never'].includes(val)
  },
  bodyStyle: {
    type: Object,
    default: () => ({})
  }
})
</script>

<script>
// Define Card subcomponents for Element Plus migration
export const CardHeader = {
  name: 'CardHeader',
  setup(_, { slots }) {
    return () => (
      <div class="card-header">
        {slots.default?.()}
      </div>
    )
  }
}

export const CardTitle = {
  name: 'CardTitle',
  setup(_, { slots }) {
    return () => (
      <h3 class="card-title text-xl font-bold">
        {slots.default?.()}
      </h3>
    )
  }
}

export const CardDescription = {
  name: 'CardDescription',
  setup(_, { slots }) {
    return () => (
      <p class="card-description text-sm text-gray-500">
        {slots.default?.()}
      </p>
    )
  }
}

export const CardContent = {
  name: 'CardContent',
  setup(_, { slots }) {
    return () => (
      <div class="card-content">
        {slots.default?.()}
      </div>
    )
  }
}

export const CardFooter = {
  name: 'CardFooter',
  setup(_, { slots }) {
    return () => (
      <div class="card-footer flex justify-end mt-4">
        {slots.default?.()}
      </div>
    )
  }
}
</script>