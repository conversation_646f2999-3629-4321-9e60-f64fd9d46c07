<template>
	<view class="container">
		<!-- 顶部搜索栏 -->
		<view class="search-bar">
			<view class="search-input">
				<uni-icons type="search" size="16" color="#999"></uni-icons>
				<input type="text" placeholder="搜索课程" @confirm="onSearch" />
			</view>
			<view class="scan-btn" @tap="goToScan">
				<uni-icons type="scan" size="24" color="#fff"></uni-icons>
			</view>
		</view>
		
		<!-- 功能网格 -->
		<view class="grid-container">
			<view class="grid-item" v-for="(item, index) in gridItems" :key="index" @click="navigateTo(item.path)">
				<image :src="item.icon" mode="aspectFit"></image>
				<text>{{ item.text }}</text>
			</view>
		</view>
		
		<!-- 我的课程列表 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">我的课程</text>
				<text class="more" @click="navigateTo('/pages/course/list')">更多</text>
			</view>
			<view class="course-list">
				<view class="course-item" v-for="(course, index) in courses" :key="course.id" @click="navigateTo(`/pages/course/detail?id=${course.id}`)">
					<image :src="course.cover_image" mode="aspectFill"></image>
					<view class="course-info">
						<text class="course-name">{{ course.name }}</text>
						<text class="teacher-name">{{ course.teacher.title }}</text>
						<view class="progress-bar">
							<view class="progress" :style="{ width: course.progress + '%' }"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 我的作业列表 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">我的作业</text>
				<text class="more" @click="navigateTo('/pages/homework/list')">更多</text>
			</view>
			<view class="homework-list">
				<view class="homework-item" v-for="(homework, index) in homeworks" :key="homework.id" @click="navigateTo(`/pages/homework/detail?id=${homework.id}`)">
					<view class="homework-info">
						<text class="homework-name">{{ homework.title }}</text>
						<text class="deadline">截止时间：{{ homework.end_time }}</text>
					</view>
					<text class="status" :class="homework.status">{{ homework.statusText }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/modules/user'
import request from '@/utils/request'

const userStore = useUserStore()

// 功能网格数据
const gridItems = ref([
	{ text: '我的课程', icon: '/static/images/icons/course.png', path: '/pages/course/list' },
	{ text: '我的作业', icon: '/static/images/icons/homework.png', path: '/pages/homework/list' },
	{ text: 'AI问答', icon: '/static/images/icons/ai.png', path: '/pages/ai/chat' },
	{ text: '积分商城', icon: '/static/images/icons/points.png', path: '/pages/points/mall' }
])

// 课程列表数据
const courses = ref([])
const homeworks = ref([])

// 获取课程列表
const getCourses = async (studentId) => {
	try {
		const response = await request({
			url: `/students/${studentId}/courses/?page=1&page_size=5`,
			method: 'GET'
		})
		courses.value = response.results
	} catch (error) {
		uni.showToast({
			title: '获取课程列表失败',
			icon: 'none'
		})
	}
}

// 获取作业列表
const getHomeworks = async () => {
	try {
		const response = await request({
			url: '/homeworks/student_assignments/?page=1&page_size=5&course_id=&status=&sort=dueDateDesc&search=',
			method: 'GET'
		})
		if (response && response.results) {
			homeworks.value = response.results.map(item => ({
				...item,
				statusText: getHomeworkStatus(item.status)
			}))
		} else {
			console.log('暂无作业数据')
		}
	} catch (error) {
		console.log('获取作业列表失败',error)
		uni.showToast({
			title: '获取作业列表失败',
			icon: 'none'
		})
	}
}

// 获取作业状态文本
const getHomeworkStatus = (status) => {
	const statusMap = {
		'pending': '待完成',
		'submitted': '已提交',
		'graded': '已批改',
		'overdue': '已逾期'
	}
	return statusMap[status] || status
}

// 页面跳转
const navigateTo = (path) => {
	uni.navigateTo({ url: path })
}

// 搜索功能
const onSearch = (e) => {
	const keyword = e.detail.value
	uni.navigateTo({
		url: `/pages/course/search?keyword=${keyword}`
	})
}

// 添加扫码按钮到合适的位置
const goToScan = () => {
	uni.navigateTo({
		url: '/pages/scan/index'
	})
}

// 页面显示时加载数据
onShow(() => {
	getCourses(userStore.userInfo.role_info.id)
	getHomeworks()
})
</script>

<style lang="scss">
.container {
	padding: 20rpx;
}

.search-bar {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	
	.search-input {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		margin-right: 20rpx;
		
		input {
			flex: 1;
			margin-left: 10rpx;
			font-size: 28rpx;
		}
	}
	
	.scan-btn {
		position: fixed;
		right: 30rpx;
		bottom: 200rpx;
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(to right, #007AFF, #00BFFF);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
		z-index: 100;
	}
}

.grid-container {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx;
	padding: 20rpx 0;
	
	.grid-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		image {
			width: 80rpx;
			height: 80rpx;
			margin-bottom: 10rpx;
		}
		
		text {
			font-size: 24rpx;
			color: #333;
		}
	}
}

.section {
	margin-bottom: 30rpx;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.more {
			font-size: 24rpx;
			color: #666;
		}
	}
}

.course-list {
	.course-item {
		display: flex;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		image {
			width: 200rpx;
			height: 150rpx;
		}
		
		.course-info {
			flex: 1;
			padding: 20rpx;
			
			.course-name {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.teacher-name {
				font-size: 24rpx;
				color: #666;
				margin-bottom: 20rpx;
			}
			
			.progress-bar {
				height: 6rpx;
				background: #eee;
				border-radius: 3rpx;
				
				.progress {
					height: 100%;
					background: #3cc51f;
					border-radius: 3rpx;
				}
			}
		}
	}
}

.homework-list {
	.homework-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.homework-info {
			.homework-name {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.deadline {
				font-size: 24rpx;
				color: #999;
			}
		}
		
		.status {
			font-size: 24rpx;
			padding: 6rpx 16rpx;
			border-radius: 20rpx;
			
			&.pending {
				color: #ff9900;
				background: rgba(255, 153, 0, 0.1);
			}
			
			&.submitted {
				color: #3cc51f;
				background: rgba(60, 197, 31, 0.1);
			}
			
			&.graded {
				color: #007aff;
				background: rgba(0, 122, 255, 0.1);
			}
			
			&.overdue {
				color: #ff3b30;
				background: rgba(255, 59, 48, 0.1);
			}
		}
	}
}
</style>
