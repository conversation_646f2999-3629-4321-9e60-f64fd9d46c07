<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="编辑教材"
    activePage="content-creation"
    activeSubPage="textbook"
  >
    <div class="flex flex-col h-[calc(100vh-80px)]">
      <!-- 工具栏 -->
      <div class="flex justify-between items-center px-4 py-2 border-b border-gray-200 bg-white">
        <div class="flex items-center">
          <h2 class="text-lg font-bold mr-4">{{ textbookData.title }}</h2>
          <span class="text-sm text-gray-500">{{ getSubjectName(textbookData.subject) }}</span>
        </div>
        
        <div class="flex items-center gap-3">
          <button class="flex items-center gap-1 text-blue-600 hover:text-blue-800 px-3 py-1.5 rounded-md hover:bg-blue-50">
            <i class="material-icons text-base">preview</i>
            预览
          </button>
          <button class="flex items-center gap-1 text-green-600 hover:text-green-800 px-3 py-1.5 rounded-md hover:bg-green-50">
            <i class="material-icons text-base">save</i>
            保存
          </button>
          <button class="flex items-center gap-1 text-gray-600 hover:text-gray-800 px-3 py-1.5 rounded-md hover:bg-gray-50">
            <i class="material-icons text-base">download</i>
            导出
          </button>
          <button @click="backToList" class="flex items-center gap-1 text-gray-600 hover:text-gray-800 px-3 py-1.5 rounded-md hover:bg-gray-50">
            <i class="material-icons text-base">arrow_back</i>
            返回列表
          </button>
        </div>
      </div>
      
      <!-- 主编辑区域 -->
      <div class="flex flex-1 overflow-hidden">
        <!-- 左侧：目录树 -->
        <div class="w-64 bg-gray-50 border-r border-gray-200 flex flex-col h-full overflow-auto">
          <div class="p-3 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-medium text-sm">教材目录</h3>
            <button class="text-gray-500 hover:text-gray-700">
              <i class="material-icons text-base">edit</i>
            </button>
          </div>
          
          <div class="flex-1 overflow-y-auto p-2">
            <div class="toc-tree">
              <template v-for="(chapter, chIdx) in textbookData.toc" :key="'ch'+chIdx">
                <!-- 章节 -->
                <div 
                  class="toc-chapter p-2 rounded-md cursor-pointer mb-1 font-medium"
                  :class="{'bg-blue-50 text-blue-700': activeChapter === chIdx && activeSection === -1}"
                  @click="selectChapter(chIdx)"
                >
                  {{ chIdx + 1 }}. {{ chapter.title }}
                </div>
                
                <!-- 小节 -->
                <div class="toc-sections ml-4 mb-2">
                  <div 
                    v-for="(section, secIdx) in chapter.sections" 
                    :key="'sec'+secIdx"
                    class="toc-section p-1.5 rounded-md cursor-pointer text-sm"
                    :class="{'bg-blue-50 text-blue-700': activeChapter === chIdx && activeSection === secIdx}"
                    @click="selectSection(chIdx, secIdx)"
                  >
                    {{ chIdx + 1 }}.{{ secIdx + 1 }} {{ section.title }}
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
        
        <!-- 中间：内容编辑区 -->
        <div class="flex-1 flex flex-col h-full overflow-hidden bg-white">
          <!-- 当前编辑位置指示 -->
          <div class="p-3 border-b border-gray-200 bg-gray-50 flex items-center">
            <div class="text-sm text-gray-500">
              <span>{{ getCurrentLocation() }}</span>
            </div>
          </div>
          
          <!-- 编辑器区域 -->
          <div class="flex-1 overflow-auto p-6">
            <div v-if="activeChapter >= 0">
              <!-- 编辑器标题 -->
              <h2 class="text-xl font-bold mb-4">
                {{ activeSection >= 0 
                    ? `${activeChapter + 1}.${activeSection + 1} ${textbookData.toc[activeChapter].sections[activeSection].title}` 
                    : `${activeChapter + 1}. ${textbookData.toc[activeChapter].title}` }}
              </h2>
              
              <!-- 正文内容 -->
              <div class="prose max-w-none mb-6">
                <div 
                  class="min-h-[300px] border border-gray-200 rounded-md p-4"
                  contenteditable="true"
                  @input="updateContent"
                >
                  {{ getCurrentContent() }}
                </div>
              </div>
              
              <!-- 内容小工具栏 -->
              <div class="flex justify-between items-center mt-4 border-t border-gray-200 pt-4">
                <div class="flex gap-3">
                  <button class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
                    <i class="material-icons text-base">add_chart</i>
                    插入图表
                  </button>
                  <button class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
                    <i class="material-icons text-base">image</i>
                    插入图片
                  </button>
                  <button class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
                    <i class="material-icons text-base">table_chart</i>
                    插入表格
                  </button>
                </div>
                
                <button class="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-md">
                  应用AI生成内容
                </button>
              </div>
            </div>
            
            <div v-else class="flex flex-col items-center justify-center h-full text-gray-500">
              <i class="material-icons text-5xl mb-4">description</i>
              <p>请从左侧目录选择要编辑的章节</p>
            </div>
          </div>
        </div>
        
        <!-- 右侧：AI助手对话区 -->
        <div class="w-80 border-l border-gray-200 flex flex-col h-full bg-gray-50">
          <div class="p-3 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-medium text-sm">AI助手</h3>
            <button @click="toggleAssistant" class="text-gray-500 hover:text-gray-700">
              <i class="material-icons text-base">{{ showAssistant ? 'close' : 'chat' }}</i>
            </button>
          </div>
          
          <div v-if="showAssistant" class="flex-1 flex flex-col">
            <!-- 消息历史区 -->
            <div class="flex-1 overflow-y-auto p-3">
              <div class="space-y-4">
                <div v-for="(msg, idx) in chatMessages" :key="idx" :class="msg.role === 'user' ? 'text-right' : ''">
                  <div 
                    class="inline-block rounded-lg p-3 max-w-xs sm:max-w-sm"
                    :class="msg.role === 'user' 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-white text-gray-800 border border-gray-200'"
                  >
                    {{ msg.content }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 输入区 -->
            <div class="border-t border-gray-200 p-3">
              <div class="relative">
                <textarea 
                  v-model="newMessage" 
                  class="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  placeholder="例如: 为当前章节生成内容..."
                  rows="3"
                  @keydown.enter.prevent="sendMessage"
                ></textarea>
                <button 
                  @click="sendMessage"
                  class="absolute right-2 bottom-2 text-blue-600 hover:text-blue-800"
                >
                  <i class="material-icons">send</i>
                </button>
              </div>
              
              <div class="mt-2 flex gap-2 text-xs">
                <button class="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 whitespace-nowrap">
                  生成教学案例
                </button>
                <button class="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 whitespace-nowrap">
                  创建习题
                </button>
                <button class="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700 whitespace-nowrap">
                  补充技术细节
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

const router = useRouter()
const route = useRoute()

// 教师数据
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar
})

// 教材数据
const textbookData = ref({
  id: 1,
  title: '计算机编程基础',
  subject: 'computer',
  targetType: 'high',
  years: ['year1', 'year2'],
  type: 'electronic',
  toc: [
    {
      title: '绪论',
      content: '本章介绍计算机编程的基本概念和历史发展...',
      sections: [
        { 
          title: '课程背景与目标',
          content: '编程是现代信息社会的重要技能，本课程旨在培养学生的编程思维和实践能力...'
        },
        { 
          title: '学习方法指导',
          content: '学习编程需要理论与实践相结合，建议同学们在学习理论的同时多动手编写代码...'
        }
      ]
    },
    {
      title: '基础概念',
      content: '计算机程序由一系列指令组成，这些指令按照特定的顺序执行以完成特定的任务...',
      sections: [
        { 
          title: '核心术语定义',
          content: '变量：用于存储数据的命名存储空间...\n常量：程序运行期间值不变的数据...'
        },
        { 
          title: '技术发展历程',
          content: '从最早的机器语言到汇编语言，再到高级编程语言，编程技术经历了重大发展...'
        },
        { 
          title: '应用场景分析',
          content: '编程技术在科学计算、商业应用、游戏开发等多个领域有广泛应用...'
        }
      ]
    },
    {
      title: '基本原理',
      content: '本章介绍编程的基本原理和核心概念...',
      sections: [
        { 
          title: '理论基础',
          content: '计算机程序的执行基于冯·诺依曼架构，包括存储程序的概念...'
        },
        { 
          title: '计算模型',
          content: '编程语言实现了不同的计算模型，包括过程式编程、面向对象编程等...'
        },
        { 
          title: '实现方法',
          content: '编程语言通过编译器或解释器将源代码转换为机器可执行的指令...'
        }
      ]
    }
  ]
})

// 学科列表（模拟数据，与创建页面保持一致）
const subjects = ref([
  { id: 'computer', name: '计算机科学' },
  { id: 'math', name: '数学' },
  { id: 'english', name: '英语' },
  { id: 'management', name: '工商管理' },
  { id: 'ai', name: '人工智能' }
])

// 获取学科名称
const getSubjectName = (subjectId) => {
  const subject = subjects.value.find(s => s.id === subjectId)
  return subject ? subject.name : '未知学科'
}

// 编辑相关
const activeChapter = ref(0)
const activeSection = ref(-1)

const selectChapter = (chapterIndex) => {
  activeChapter.value = chapterIndex
  activeSection.value = -1
}

const selectSection = (chapterIndex, sectionIndex) => {
  activeChapter.value = chapterIndex
  activeSection.value = sectionIndex
}

const getCurrentLocation = () => {
  if (activeChapter.value === -1) {
    return '未选择章节'
  }
  
  if (activeSection.value === -1) {
    return `第${activeChapter.value + 1}章：${textbookData.value.toc[activeChapter.value].title}`
  }
  
  return `第${activeChapter.value + 1}章 > ${activeChapter.value + 1}.${activeSection.value + 1} ${textbookData.value.toc[activeChapter.value].sections[activeSection.value].title}`
}

const getCurrentContent = () => {
  if (activeChapter.value === -1) {
    return ''
  }
  
  if (activeSection.value === -1) {
    return textbookData.value.toc[activeChapter.value].content || ''
  }
  
  return textbookData.value.toc[activeChapter.value].sections[activeSection.value].content || ''
}

const updateContent = (event) => {
  if (activeChapter.value === -1) {
    return
  }
  
  if (activeSection.value === -1) {
    textbookData.value.toc[activeChapter.value].content = event.target.innerText
  } else {
    textbookData.value.toc[activeChapter.value].sections[activeSection.value].content = event.target.innerText
  }
}

// AI助手相关
const showAssistant = ref(true)
const newMessage = ref('')
const chatMessages = ref([
  { role: 'assistant', content: '你好！我是你的教材编辑助手。我可以帮你生成内容、创建习题或补充相关信息。你正在编辑"计算机编程基础"教材，需要什么帮助？' },
  { role: 'user', content: '为当前章节生成一个案例' },
  { role: 'assistant', content: '好的，以下是一个关于变量和常量的案例：\n\n案例：小型图书管理系统\n在这个案例中，我们将创建一个简单的图书管理系统，演示变量和常量的使用。\n\n常量：MAX_BOOKS = 100（图书馆最大藏书量）\n变量：currentBooks（当前藏书数量）\n\n你可以在案例中介绍如何通过变量跟踪图书借阅状态，以及如何使用常量来设置系统限制。' }
])

const toggleAssistant = () => {
  showAssistant.value = !showAssistant.value
}

const sendMessage = () => {
  if (!newMessage.value.trim()) return
  
  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content: newMessage.value.trim()
  })
  
  // 清空输入
  const userMessage = newMessage.value.trim()
  newMessage.value = ''
  
  // 模拟AI响应（实际应用中应调用AI服务）
  setTimeout(() => {
    let response = ''
    
    if (userMessage.includes('习题') || userMessage.includes('练习')) {
      response = '以下是针对本章内容的三道练习题：\n\n1. 分析题：请解释变量和常量的区别，并各举一个实际应用场景。\n\n2. 编程题：编写一个程序，使用变量存储学生成绩，并计算平均分。\n\n3. 讨论题：在实际编程中，如何选择合适的变量命名方式？为什么良好的命名规范很重要？'
    } else if (userMessage.includes('案例') || userMessage.includes('实例')) {
      response = '案例：学生信息管理系统\n\n这个案例展示了如何使用变量来存储和管理学生信息。一个学生信息系统需要存储学号、姓名、年龄、成绩等数据，这些都是典型的变量应用场景。通过这个案例，可以向学生展示变量的声明、赋值、修改和使用过程。\n\n你可以在案例中加入代码示例，展示如何声明不同类型的变量（整型、字符串等）来存储不同类型的学生信息。'
    } else {
      response = '我已经理解你的需求。针对当前章节内容，我建议增加以下内容：\n\n1. 增加更多日常生活中的编程应用示例，帮助学生建立实际联系\n\n2. 补充一些最新的技术发展趋势，保持教材的时效性\n\n3. 考虑加入一些有趣的编程小故事或轶事，增加学习乐趣\n\n需要我为你具体生成某一部分的内容吗？'
    }
    
    chatMessages.value.push({
      role: 'assistant',
      content: response
    })
  }, 1000)
}

// 返回列表
const backToList = () => {
  router.push('/teacher/textbook-projects')
}

onMounted(() => {
  // 实际应用中，这里应该通过API获取教材数据
  const id = route.params.id
  console.log('加载教材ID:', id)
})
</script>

<style scoped>
.toc-chapter:hover, .toc-section:hover {
  background-color: #f0f9ff;
}

.prose {
  max-width: 65ch;
  color: #374151;
  line-height: 1.6;
}
</style> 