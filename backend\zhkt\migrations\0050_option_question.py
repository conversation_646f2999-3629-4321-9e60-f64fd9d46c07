# Generated by Django 3.2.20 on 2025-06-05 09:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0049_course_class_groups'),
    ]

    operations = [
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=500, verbose_name='题目标题')),
                ('question_type', models.CharField(choices=[('single_choice', '单选题'), ('multiple_choice', '多选题'), ('true_false', '判断题'), ('fill_blank', '填空题'), ('short_answer', '简答题')], max_length=20, verbose_name='题目类型')),
                ('difficulty', models.CharField(choices=[('easy', '简单'), ('medium', '中等'), ('hard', '困难')], max_length=10, verbose_name='难度等级')),
                ('content', models.TextField(verbose_name='题目内容')),
                ('answer', models.TextField(verbose_name='参考答案')),
                ('analysis', models.TextField(blank=True, null=True, verbose_name='题目解析')),
                ('score', models.DecimalField(decimal_places=2, default=1.0, max_digits=5, verbose_name='分值')),
                ('tags', models.CharField(blank=True, max_length=200, null=True, verbose_name='标签')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='zhkt.course', verbose_name='所属课程')),
            ],
            options={
                'verbose_name': '题目',
                'verbose_name_plural': '题目',
                'db_table': 'zhkt_question',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='Option',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.CharField(max_length=500, verbose_name='选项内容')),
                ('is_correct', models.BooleanField(default=False, verbose_name='是否正确答案')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='zhkt.question', verbose_name='所属题目')),
            ],
            options={
                'verbose_name': '题目选项',
                'verbose_name_plural': '题目选项',
                'db_table': 'zhkt_question_option',
                'ordering': ['order'],
            },
        ),
    ]
