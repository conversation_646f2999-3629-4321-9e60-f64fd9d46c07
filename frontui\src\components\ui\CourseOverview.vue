<template>
  <div class="course-overview">
    <h2 class="section-title">{{ currentSection }}</h2>
    <p class="section-description">
      {{ description }}
    </p>
    
    <!-- 课程数据信息 -->
    <div class="course-stats">
      <el-card shadow="hover" class="stat-card">
        <div class="stat-card-content">
          <el-icon class="stat-icon"><Clock /></el-icon>
          <div class="stat-info">
            <span class="stat-value">{{ duration }}</span>
            <span class="stat-label">课程时长</span>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" class="stat-card">
        <div class="stat-card-content">
          <el-icon class="stat-icon"><User /></el-icon>
          <div class="stat-info">
            <span class="stat-value">{{ formatNumber(studentsCount) }}</span>
            <span class="stat-label">学习人数</span>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" class="stat-card">
        <div class="stat-card-content">
          <el-icon class="stat-icon"><Star /></el-icon>
          <div class="stat-info">
            <span class="stat-value">{{ rating }} <small>({{ ratingCount }}人评价)</small></span>
            <span class="stat-label">课程评分</span>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 学习要点 -->
    <div class="learning-points">
      <h3 class="subsection-title">
        <el-icon><CircleCheck /></el-icon>
        本节要点
      </h3>
      <ul class="learning-points-list">
        <li v-for="(point, index) in keyPoints" :key="index" class="learning-point-item">
          {{ point }}
        </li>
      </ul>
    </div>
    
    <!-- 重点知识点 -->
    <div class="key-concept">
      <h3 class="subsection-title">
        <el-icon><InfoFilled /></el-icon>
        重点知识点
      </h3>
      <div class="key-concept-content" v-html="keyConceptHTML"></div>
    </div>
    
    <!-- 代码示例 -->
    <div class="code-example">
      <h3 class="subsection-title">
        <el-icon><Document /></el-icon>
        代码示例
      </h3>
      <div class="code-block">
        <pre><code>{{ codeExample }}</code></pre>
        <el-button 
          size="small" 
          type="primary" 
          class="copy-code-btn"
          @click="copyCodeToClipboard"
        >
          <el-icon><CopyDocument /></el-icon>
          复制代码
        </el-button>
      </div>
    </div>
    
    <!-- 注意事项 -->
    <div class="note-warning">
      <h3 class="subsection-title warning">
        <el-icon><Warning /></el-icon>
        注意事项
      </h3>
      <div class="warning-content" v-html="warningHTML"></div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { Clock, User, Star, CircleCheck, InfoFilled, Document, CopyDocument, Warning } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'CourseOverview',
  components: {
    Clock,
    User, 
    Star,
    CircleCheck,
    InfoFilled,
    Document,
    CopyDocument,
    Warning
  },
  props: {
    currentSection: {
      type: String,
      default: '课程章节标题'
    },
    description: {
      type: String,
      default: '课程详细讲解内容，帮助学生掌握相关知识点。'
    },
    duration: {
      type: String,
      default: '45分钟'
    },
    studentsCount: {
      type: Number,
      default: 0
    },
    rating: {
      type: Number,
      default: 4.5
    },
    ratingCount: {
      type: Number,
      default: 0
    },
    keyPoints: {
      type: Array,
      default: () => []
    },
    keyConcept: {
      type: String,
      default: ''
    },
    codeExample: {
      type: String,
      default: '// 示例代码\nconsole.log("Hello World");'
    },
    warning: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const keyConceptHTML = ref(convertToHTML(props.keyConcept));
    const warningHTML = ref(convertToHTML(props.warning));
    
    // 辅助函数：格式化数字，添加千位分隔符
    const formatNumber = (num) => {
      return num.toLocaleString();
    };
    
    // 辅助函数：将文本中的<strong>等标签转换为HTML
    function convertToHTML(text) {
      if (!text) return '';
      
      // 替换<strong>标签
      let html = text.replace(/<strong>(.*?)<\/strong>/g, '<strong>$1</strong>');
      
      // 替换换行符为<br>
      html = html.replace(/\n/g, '<br>');
      
      return html;
    }
    
    // 复制代码到剪贴板
    const copyCodeToClipboard = () => {
      navigator.clipboard.writeText(props.codeExample)
        .then(() => {
          ElMessage({
            message: '代码已复制到剪贴板',
            type: 'success',
            duration: 2000
          });
        })
        .catch(err => {
          ElMessage({
            message: '复制失败: ' + err,
            type: 'error',
            duration: 2000
          });
        });
    };
    
    return {
      keyConceptHTML,
      warningHTML,
      formatNumber,
      copyCodeToClipboard
    };
  }
}
</script>

<style scoped>
.course-overview {
  padding: 0 16px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
}

.section-description {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 32px;
  color: var(--el-text-color-regular);
}

.course-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 36px;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 180px;
  transition: all 0.3s;
}

.stat-card-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 24px;
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  padding: 12px;
  border-radius: 12px;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.subsection-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.subsection-title.warning {
  color: var(--el-color-danger);
}

.subsection-title :deep(.el-icon) {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  padding: 8px;
  border-radius: 8px;
  font-size: 16px;
}

.subsection-title.warning :deep(.el-icon) {
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

.learning-points {
  margin-bottom: 36px;
}

.learning-points-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.learning-point-item {
  position: relative;
  padding-left: 24px;
  margin-bottom: 12px;
  line-height: 1.6;
}

.learning-point-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
}

.key-concept {
  margin-bottom: 36px;
  background-color: var(--el-color-info-light-9);
  padding: 20px;
  border-radius: 12px;
}

.key-concept-content {
  line-height: 1.6;
}

.key-concept-content :deep(strong) {
  color: var(--el-color-primary);
  font-weight: 600;
}

.code-example {
  margin-bottom: 36px;
}

.code-block {
  position: relative;
  background-color: #282c34;
  border-radius: 12px;
  padding: 20px;
  overflow: auto;
}

.code-block pre {
  margin: 0;
  color: #abb2bf;
  font-family: 'Fira Code', 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.6;
}

.copy-code-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.code-block:hover .copy-code-btn {
  opacity: 1;
}

.note-warning {
  background-color: var(--el-color-danger-light-9);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 36px;
}

.warning-content {
  line-height: 1.6;
}

.warning-content :deep(i) {
  color: var(--el-color-danger);
  margin-right: 8px;
}

@media (max-width: 768px) {
  .course-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-card {
    width: 100%;
  }
}
</style> 