# Generated by Django 3.2.20 on 2025-06-17 15:29

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0063_auto_20250617_1139'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='audiomodel',
            name='sample_audio_url',
        ),
        migrations.AddField(
            model_name='audiomodel',
            name='audio_url',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='音频URL'),
        ),
        migrations.AddField(
            model_name='audiomodel',
            name='avatar_url',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='头像URL'),
        ),
        migrations.AddField(
            model_name='audiomodel',
            name='clone_audio_url',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='克隆音频URL'),
        ),
        migrations.AddField(
            model_name='student',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='创建时间'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='audiogenerationhistory',
            name='text_content',
            field=models.TextField(verbose_name='文本内容'),
        ),
        migrations.AlterField(
            model_name='audiogenerationhistory',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='voice_generations', to='zhkt.user', verbose_name='所属用户'),
        ),
        migrations.DeleteModel(
            name='CourseEnrollment',
        ),
    ]
