from django.db import models
from .course import Course
from .user import User

class Comment(models.Model):
    """课程评论"""
    content = models.TextField(verbose_name='评论内容')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    deleted_at = models.DateTimeField(verbose_name='删除时间', null=True, blank=True)
    likes = models.IntegerField(default=0, verbose_name='点赞数')
    
    # 关联关系
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='comments', verbose_name='课程')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comments', verbose_name='评论用户')
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='replies', verbose_name='父评论')
    
    # 点赞用户
    liked_by = models.ManyToManyField(User, related_name='liked_comments', blank=True, verbose_name='点赞用户')

    class Meta:
        db_table = 'zhkt_comment'
        ordering = ['-created_at']
        verbose_name = '评论'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.user.username}的评论: {self.content[:20]}' 