<template>
  <div class="bg-gray-50 flex items-center justify-center min-h-screen">
    <div class="max-w-md w-full mx-auto">
      <el-card class="box-card">
        <!-- 头部 -->
        <div class="p-6 bg-blue-500 text-center">
          <h1 class="text-2xl font-bold text-white">{{ $t('system.title') }}</h1>
          <p class="text-blue-100 mt-2">{{ $t('system.slogan') }}</p>
        </div>
        
        <!-- 登录表单 -->
        <div class="p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-6 text-center">{{ $t('login.title') }}</h2>
          
          <el-form 
            @submit.native.prevent="handleLogin" 
            :model="formData" 
            ref="formRef"
          >
            <!-- 用户名/手机/邮箱 -->
            <el-form-item prop="username">
              <el-input 
                v-model="formData.username" 
                :placeholder="$t('login.username')"
                prefix-icon="User"
              />
            </el-form-item>
            
            <!-- 密码 -->
            <el-form-item prop="password">
              <el-input 
                v-model="formData.password" 
                :type="showPassword ? 'text' : 'password'" 
                :placeholder="$t('login.password')"
                prefix-icon="Lock"
                show-password
                @keyup.enter="handleLogin"
              />
            </el-form-item>
            
            <!-- 选择身份 -->
            <!--
            <el-form-item prop="role">
              <el-radio-group v-model="formData.role" size="large" class="flex justify-between w-full">
                <el-radio-button v-for="role in roles" :key="role.value" :label="role.value" class="flex-1 text-center role-btn">
                  <div class="flex flex-col items-center">
                    <el-icon class="mb-1">
                      <School v-if="role.value === 'student'" />
                      <Reading v-else-if="role.value === 'teacher'" />
                      <Setting v-else-if="role.value === 'admin'" />
                    </el-icon>
                    <span class="text-xs">{{ role.label }}</span>
                  </div>
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            -->
            
            <!-- 记住密码和忘记密码 -->
            <div class="flex items-center justify-between mb-6">
              <el-checkbox v-model="formData.rememberMe">
                <span class="flex items-center text-sm text-gray-700">
                  <el-icon class="mr-1"><Download /></el-icon> {{ $t('login.remember') }}
                </span>
              </el-checkbox>
              <router-link to="/auth/forgot-password" class="font-medium text-blue-600 hover:text-blue-500 flex items-center">
                <el-icon class="mr-1"><Key /></el-icon> {{ $t('login.forgot') }}
              </router-link>
            </div>
            
            <!-- 登录按钮 -->
            <el-button type="primary" @click="handleLogin" class="w-full" :loading="loading">
              <el-icon class="mr-2"><Right /></el-icon> {{ $t('login.button') }}
            </el-button>
          </el-form>
          
          <!-- 分割线 -->
          <el-divider content-position="center">{{ $t('login.otherMethods') }}</el-divider>
          
          <!-- 其他登录方式 -->
          <div class="mt-6 grid grid-cols-3 gap-3">
            <router-link to="/auth/wechat-login">
              <el-button class="w-full social-btn">
                <i class="fa-brands fa-weixin text-green-600 text-2xl"></i>
              </el-button>
            </router-link>
            <router-link to="/auth/qq-login">
              <el-button class="w-full social-btn">
                <i class="fa-brands fa-qq text-blue-500 text-2xl"></i>
              </el-button>
            </router-link>
            <router-link to="/auth/mobile-login">
              <el-button class="w-full social-btn">
                <el-icon><Iphone /></el-icon>
              </el-button>
            </router-link>
          </div>
          
          <!-- 注册链接 -->
          <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
              {{ $t('login.noAccount') }} <router-link to="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">{{ $t('login.register') }}</router-link>
            </p>
          </div>
        </div>
      </el-card>
      
      <!-- 页脚 -->
      <div class="mt-4 text-center">
        <div class="mb-2">
          <LanguageSwitcher />
        </div>
        <p class="text-xs text-gray-500">
          {{ $t('system.copyright') }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import LanguageSwitcher from '@/components/common/LanguageSwitcher.vue'
import { 
  User, 
  Lock, 
  School, 
  Reading, 
  Setting, 
  Download, 
  Key, 
  Right, 
  Iphone
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()
const showPassword = ref(false)
const loading = ref(false)

// 角色数据
const roles = [
  { value: 'student', label: t('role.student'), icon: 'fa-user-graduate', iconClass: 'fas fa-user-graduate', materialIcon: 'school' },
  { value: 'teacher', label: t('role.teacher'), icon: 'fa-chalkboard-teacher', iconClass: 'fas fa-chalkboard-teacher', materialIcon: 'cast_for_education' },
  { value: 'admin', label: t('role.admin'), icon: 'fa-user-cog', iconClass: 'fas fa-user-cog', materialIcon: 'admin_panel_settings' }
]

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  role: 'student',
  rememberMe: false
})

// 表单ref
const formRef = ref(null)

// 加载保存的登录信息
const loadSavedLoginInfo = () => {
  const savedInfo = localStorage.getItem('loginInfo')
  if (savedInfo) {
    try {
      const { username, password, role, rememberMe } = JSON.parse(savedInfo)
      formData.username = username
      formData.password = password
      formData.rememberMe = rememberMe
    } catch (e) {
      console.error('加载保存的登录信息失败:', e)
    }
  }
}

// 保存登录信息
const saveLoginInfo = () => {
  if (formData.rememberMe) {
    const loginInfo = {
      username: formData.username,
      password: formData.password,
      rememberMe: true
    }
    localStorage.setItem('loginInfo', JSON.stringify(loginInfo))
  } else {
    localStorage.removeItem('loginInfo')
  }
}

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 处理登录
const handleLogin = async () => {
  if (!formRef.value) return
  
  try {
    loading.value = true
    
    const success = await authStore.login(formData.username.trim(), formData.password.trim())
    
    if (success) {
      // 保存登录信息
      saveLoginInfo()

      const userRole = authStore.userRole
      const rolesList = userRole.map(role => role.code)
      console.log('rolesList',rolesList);
      if(rolesList.includes('student')){
        router.push('/student/dashboard')
      }else if(rolesList.includes('teacher')){
        router.push('/teacher/dashboard')
      }else if(rolesList.includes('admin')){
        router.push('/admin/dashboard')
      }else{
        ElMessage.error(t('errors.roleError'))
      }
    } else {
      ElMessage.error(t('errors.wrongCredentials'))
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error(t('errors.loginFailed'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 加载保存的登录信息
  loadSavedLoginInfo()
})
</script>

<style scoped>
.header-bg {
  background-color: #2680EB; /* Adjust this to match the exact blue from the screenshot */
}

.role-btn :deep(.el-radio-button__inner) {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: auto;
  padding: 8px;
  color: #606266;
}

.role-btn :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #2680EB;
  border-color: #2680EB;
  box-shadow: -1px 0 0 0 #2680EB;
  color: white;
}

/* Update primary button styling */
:deep(.el-button--primary) {
  background-color: #2680EB !important;
  border-color: #2680EB !important;
}

:deep(.el-button--primary:hover), :deep(.el-button--primary:focus) {
  background-color: #1a6bca !important;
  border-color: #1a6bca !important;
}

/* Custom checkbox styling */
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #2680EB;
  border-color: #2680EB;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #2680EB;
}

.social-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
}
</style> 