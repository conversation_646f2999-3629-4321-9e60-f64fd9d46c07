# -*- coding: utf-8 -*-
"""
内容创建服务
负责要点提取、知识增强、HTML内容生成等功能
"""

import concurrent.futures
import json
import logging
import random
import re
import time
from typing import List, Tuple

from ...prompt.ai_lecture_prompts import AILecturePromptManager
from ...utils.deepseek_api import DeepSeekAPI
from ...utils.knowledge_utils import KnowledgeBaseAPI
from .exceptions import ContentCreationException

logger = logging.getLogger(__name__)

# AI模型配置
AI_MODEL = "deepseek-chat"


class ContentCreator:
    """内容创建器 - 负责教学内容的创建和增强"""
    
    def __init__(self):
        self.deepseek_api = DeepSeekAPI()
        self.knowledge_api = KnowledgeBaseAPI()
        self.prompt_manager = AILecturePromptManager()
        self.logger = logger

    def extract_key_points_from_text(self, chapter_text: str) -> List[str]:
        """
        从章节文本中提取要点
        
        Args:
            chapter_text: 章节文本内容
            
        Returns:
            List[str]: 提取的要点列表
            
        Raises:
            ContentCreationException: 提取失败时抛出
        """
        try:
            system_messages = self.prompt_manager.get_system_messages()
            extraction_prompt = self.prompt_manager.get_points_extraction_prompt(chapter_text)
            
            messages = [
                DeepSeekAPI.create_system_message(system_messages['ppt_content_designer']),
                DeepSeekAPI.create_user_message(extraction_prompt)
            ]
            
            ai_response = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            
            # 尝试解析JSON格式的要点
            json_match = re.search(r'\[.*\]', ai_response, re.DOTALL)
            if json_match:
                key_points = json.loads(json_match.group(0))
            else:
                try:
                    key_points = json.loads(ai_response)
                except json.JSONDecodeError:
                    self.logger.error(f"要点JSON解析失败，原始响应: {ai_response}")
                    key_points = []
            
            return key_points
            
        except Exception as e:
            error_msg = f"提取要点失败: {e}"
            self.logger.error(error_msg)
            raise ContentCreationException(error_msg)

    def enhance_point_with_knowledge_base(self, original_point: str, dataset_ids: List[str], 
                                        document_ids: List[str], max_retries: int = 3, 
                                        base_delay: float = 1.0) -> str:
        """
        使用知识库增强要点内容，支持重试机制
        
        Args:
            original_point: 原始要点
            dataset_ids: 数据集ID列表
            document_ids: 文档ID列表
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            
        Returns:
            str: 增强后的要点
        """
        for attempt in range(max_retries):
            try:
                # 重试时添加随机延迟，避免并发压力
                if attempt > 0:
                    delay = base_delay * (2 ** (attempt - 1)) + random.uniform(0, 1)
                    self.logger.info(f"要点增强重试 {attempt + 1}/{max_retries}，延迟 {delay:.2f}s")
                    time.sleep(delay)
                
                # 从知识库检索相关内容
                retrieval_results = self.knowledge_api.retrieve(
                    question=original_point,
                    dataset_ids=dataset_ids,
                    document_ids=document_ids,
                    similarity_threshold=0.5,
                    top_k=5
                )
                
                # 提取检索内容
                knowledge_contents = []
                if "data" in retrieval_results and "chunks" in retrieval_results["data"]:
                    for item in retrieval_results["data"]["chunks"]:
                        if "content" in item:
                            knowledge_contents.append(item["content"])
                
                if not knowledge_contents:
                    raise Exception("没有找到相关知识")
                
                # 使用AI增强要点
                system_messages = self.prompt_manager.get_system_messages()
                enhancement_prompt = self.prompt_manager.get_knowledge_enhancement_prompt(
                    original_point, ' '.join(knowledge_contents[:5])
                )
                
                messages = [
                    DeepSeekAPI.create_system_message(system_messages['ppt_content_expert']),
                    DeepSeekAPI.create_user_message(enhancement_prompt)
                ]
                enhanced_point = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
                
                # 成功记录
                if attempt > 0:
                    self.logger.info(f"要点 '{original_point[:30]}...' 在第 {attempt + 1} 次尝试后成功增强")
                
                return enhanced_point.strip()
                
            except Exception as e:
                error_message = str(e)
                if attempt < max_retries - 1:
                    # 判断是否为网络错误，决定是否继续重试
                    network_error_keywords = [
                        'connection aborted', 'remote end closed', 'timeout', 
                        'connection error', 'read timeout', 'connection reset'
                    ]
                    if any(keyword in error_message.lower() for keyword in network_error_keywords):
                        self.logger.warning(f"要点 '{original_point[:30]}...' 网络错误，第 {attempt + 1} 次尝试失败: {error_message}")
                        continue
                    else:
                        # 非网络错误，停止重试
                        self.logger.warning(f"要点 '{original_point[:30]}...' 业务错误，停止重试: {error_message}")
                        break
                else:
                    # 最后一次尝试失败
                    self.logger.error(f"要点 '{original_point[:30]}...' 知识库增强失败，已用尽 {max_retries} 次重试: {error_message}")
        
        # 所有重试都失败，返回原要点
        self.logger.warning(f"要点 '{original_point[:30]}...' 增强失败，返回原始内容")
        return original_point

    def extract_and_enhance_points_batch(self, chapter_text: str) -> List[Tuple[str, str]]:
        """
        批量提取章节要点并进行知识增强
        
        Args:
            chapter_text: 章节文本内容
            
        Returns:
            List[Tuple[str, str]]: [(原要点, 增强要点)]列表
            
        Raises:
            ContentCreationException: 处理失败时抛出
        """
        try:
            # 提取要点
            key_points = self.extract_key_points_from_text(chapter_text)
            
            # 硬编码的知识库配置（后续可改为配置化）
            dataset_ids = ["66c954582efd11f0b43c0242ac150002"]
            document_ids = ["70f3df982efd11f0bb050242ac150002"]
            
            enhanced_results = []
            
            # 减少并发数量，避免对知识库API造成过大压力
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                future_to_point = {
                    executor.submit(
                        self.enhance_point_with_knowledge_base, 
                        point, 
                        dataset_ids, 
                        document_ids,
                        max_retries=3,
                        base_delay=1.0
                    ): point
                    for point in key_points
                }
                
                for future in concurrent.futures.as_completed(future_to_point):
                    original_point = future_to_point[future]
                    try:
                        enhanced_point = future.result()
                    except Exception as e:
                        self.logger.error(f"要点增强异常: {e}")
                        enhanced_point = original_point
                    enhanced_results.append((original_point, enhanced_point))
            
            return enhanced_results
            
        except Exception as e:
            error_msg = f"批量处理要点失败: {e}"
            self.logger.error(error_msg)
            raise ContentCreationException(error_msg)

    def generate_html_slide_content(self, enhanced_point: str, chapter_title: str) -> str:
        """
        生成HTML幻灯片内容
        
        Args:
            enhanced_point: 增强后的要点
            chapter_title: 章节标题
            
        Returns:
            str: HTML代码
            
        Raises:
            ContentCreationException: 生成失败时抛出
        """
        try:
            system_messages = self.prompt_manager.get_system_messages()
            html_generation_prompt = self.prompt_manager.get_html_generation_prompt(
                chapter_title, enhanced_point
            )
            
            messages = [
                DeepSeekAPI.create_system_message(system_messages['ppt_designer_frontend']),
                DeepSeekAPI.create_user_message(html_generation_prompt)
            ]
            
            # 生成初始HTML代码
            html_response = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            
            # 提取HTML代码块
            html_match = re.search(r'```html\n(.*?)\n```', html_response, re.DOTALL)
            if html_match:
                html_code = html_match.group(1)
            else:
                html_code = html_response

            return html_code

            # 注意：HTML溢出校验功能暂时注释，如需要可以启用
            # try:
            #     self.logger.info("开始进行HTML页面溢出校验...")
            #     checked_html_code = self._validate_and_fix_html_overflow(html_code)
            #     self.logger.info("HTML页面溢出校验完成")
            #     return checked_html_code
            # except Exception as e:
            #     self.logger.warning(f"HTML页面溢出校验失败，返回原始HTML: {e}")
            #     return html_code
            
        except Exception as e:
            error_msg = f"生成HTML内容失败: {e}"
            self.logger.error(error_msg)
            raise ContentCreationException(error_msg)

    def _validate_and_fix_html_overflow(self, html_content: str) -> str:
        """
        验证HTML页面并修复溢出问题
        
        Args:
            html_content: HTML内容
            
        Returns:
            str: 修复后的HTML内容
        """
        try:
            # 获取溢出校验提示词
            overflow_check_prompt = self.prompt_manager.get_html_overflow_check_prompt(html_content)
            
            # 构建校验消息
            messages = [
                DeepSeekAPI.create_system_message("你是一个专业的PPT页面质量检查员，负责检查和修复HTML页面的溢出问题。"),
                DeepSeekAPI.create_user_message(overflow_check_prompt)
            ]
            
            # 调用AI进行溢出校验和修复
            validation_response = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            
            # 提取修复后的HTML代码
            html_match = re.search(r'```html\n(.*?)\n```', validation_response, re.DOTALL)
            if html_match:
                fixed_html = html_match.group(1)
                self.logger.info("HTML页面溢出校验发现问题并已修复")
                return fixed_html
            else:
                # 如果没有找到HTML代码块，可能是没有问题或者格式不对
                self.logger.info("HTML页面溢出校验未发现严重问题，返回原始HTML")
                return html_content
                
        except Exception as e:
            error_msg = f"HTML溢出校验失败: {e}"
            self.logger.error(error_msg)
            raise ContentCreationException(error_msg)

    def extract_page_titles_from_chapter(self, chapter_text: str, chapter_title: str) -> List[str]:
        """
        从章节内容中提取页面标题
        
        Args:
            chapter_text: 章节文本内容
            chapter_title: 章节标题
            
        Returns:
            List[str]: 页面标题列表
        """
        try:
            system_message = "你是一名专业的教学内容分析师，擅长从教学材料中提取结构化的页面标题。"
            
            user_prompt = f"""
            请从以下章节内容中提取页面标题，用于制作教学讲义。
            
            要求：
            1. 提取出逻辑清晰的页面标题（3-8个）
            2. 标题要简洁明了，突出重点（≤15字）
            3. 标题要有层次感，符合教学逻辑
            4. 标题要能概括对应内容的核心要点
            5. 保持学术严谨性
            
            【章节标题】
            {chapter_title}
            
            【章节内容】
            {chapter_text}
            
            请以JSON格式返回，格式如下：
            {{
              "page_titles": [
                "页面标题1",
                "页面标题2",
                "页面标题3"
              ]
            }}
            """
            
            messages = [
                self.deepseek_api.create_system_message(system_message),
                self.deepseek_api.create_user_message(user_prompt)
            ]
            
            response = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            
            # 提取JSON内容
            json_pattern = r"```json\n(.*?)\n```"
            match = re.search(json_pattern, response, re.DOTALL)
            if match:
                response = match.group(1)
            
            try:
                result = json.loads(response)
                page_titles = result.get("page_titles", [])
                
                if not page_titles:
                    self.logger.warning(f"章节 {chapter_title} 未提取到页面标题")
                    return []
                
                self.logger.info(f"章节 {chapter_title} 提取到 {len(page_titles)} 个页面标题")
                return page_titles
                
            except json.JSONDecodeError as e:
                self.logger.error(f"解析页面标题JSON失败: {e}, 响应内容: {response}")
                return []
                
        except Exception as e:
            self.logger.error(f"提取章节 {chapter_title} 页面标题失败: {e}")
            return []

    def generate_points_from_titles(self, page_titles: List[str], chapter_text: str, 
                                  chapter_title: str) -> List[str]:
        """
        基于页面标题生成要点
        
        Args:
            page_titles: 页面标题列表
            chapter_text: 章节文本内容
            chapter_title: 章节标题
            
        Returns:
            List[str]: 要点列表
        """
        try:
            system_message = "你是一名专业的教学内容生成师，擅长根据页面标题和教学材料生成详细的教学要点。"
            
            titles_text = "\n".join(f"{i+1}. {title}" for i, title in enumerate(page_titles))
            
            user_prompt = f"""
            请根据以下页面标题和章节内容，为每个标题生成详细的教学要点。
            
            要求：
            1. 每个页面标题对应1-3个要点
            2. 要点要具体详细，适合教学讲解（50-100字）
            3. 要点要准确反映标题内容，避免空泛
            4. 要点要有教学价值，便于学生理解
            5. 保持学术严谨性和逻辑性
            
            【章节标题】
            {chapter_title}
            
            【页面标题】
            {titles_text}
            
            【章节内容】
            {chapter_text}
            
            请以JSON格式返回，格式如下：
            {{
              "points": [
                "要点1：详细描述...",
                "要点2：详细描述...",
                "要点3：详细描述..."
              ]
            }}
            """
            
            messages = [
                self.deepseek_api.create_system_message(system_message),
                self.deepseek_api.create_user_message(user_prompt)
            ]
            
            response = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            
            # 提取JSON内容
            json_pattern = r"```json\n(.*?)\n```"
            match = re.search(json_pattern, response, re.DOTALL)
            if match:
                response = match.group(1)
            
            try:
                result = json.loads(response)
                points = result.get("points", [])
                
                if not points:
                    self.logger.warning(f"章节 {chapter_title} 未生成要点")
                    return []
                
                self.logger.info(f"章节 {chapter_title} 基于 {len(page_titles)} 个标题生成了 {len(points)} 个要点")
                return points
                
            except json.JSONDecodeError as e:
                self.logger.error(f"解析要点JSON失败: {e}, 响应内容: {response}")
                return []
                
        except Exception as e:
            self.logger.error(f"为章节 {chapter_title} 生成要点失败: {e}")
            return []

    def generate_chapter_points(self, chapter_text: str, chapter_title: str, 
                               dataset_ids: list = None, document_ids: list = None) -> List[str]:
        """
        为章节生成要点
        
        Args:
            chapter_text: 章节文本内容
            chapter_title: 章节标题
            dataset_ids: 知识库数据集ID列表（可选）
            document_ids: 知识库文档ID列表（可选）
            
        Returns:
            List[str]: 章节要点列表
        """
        try:
            # 如果提供了知识库参数，使用知识库增强的方式
            if dataset_ids or document_ids:
                return self._generate_chapter_points_with_knowledge(
                    chapter_text, chapter_title, dataset_ids, document_ids
                )
            else:
                # 否则使用基础的要点提取方法
                return self.extract_key_points_from_text(chapter_text)
                
        except Exception as e:
            self.logger.error(f"为章节 {chapter_title} 生成要点失败: {e}")
            return []

    def _generate_chapter_points_with_knowledge(self, chapter_text: str, chapter_title: str,
                                              dataset_ids: list, document_ids: list) -> List[str]:
        """
        使用知识库增强的方式生成章节要点
        
        Args:
            chapter_text: 章节文本内容
            chapter_title: 章节标题
            dataset_ids: 知识库数据集ID列表
            document_ids: 知识库文档ID列表
            
        Returns:
            List[str]: 增强后的章节要点列表
        """
        try:
            # 首先提取基础要点
            basic_points = self.extract_key_points_from_text(chapter_text)
            
            if not basic_points:
                self.logger.warning(f"章节 {chapter_title} 未提取到基础要点")
                return []
            
            # 使用知识库增强每个要点
            enhanced_points = []
            for point in basic_points:
                try:
                    enhanced_point = self.enhance_point_with_knowledge_base(
                        point, dataset_ids, document_ids
                    )
                    enhanced_points.append(enhanced_point)
                except Exception as e:
                    self.logger.warning(f"增强要点 '{point[:30]}...' 失败: {e}，使用原要点")
                    enhanced_points.append(point)
            
            self.logger.info(f"章节 {chapter_title} 生成了 {len(enhanced_points)} 个增强要点")
            return enhanced_points
            
        except Exception as e:
            self.logger.error(f"使用知识库增强章节 {chapter_title} 要点失败: {e}")
            # 降级到基础要点提取
            return self.extract_key_points_from_text(chapter_text) 