# 智慧教育平台开发脚本

本目录包含用于智慧教育平台开发、构建和部署的脚本工具。

## 可用脚本

### dev.js

智慧教育平台开发工具脚本，提供项目初始化、模块生成、任务管理等功能。

#### 使用方法

```bash
# 显示帮助信息
node scripts/dev.js help

# 初始化项目结构
node scripts/dev.js init

# 生成模块组件
node scripts/dev.js generate <类型> <名称>
# 例如: node scripts/dev.js generate component StudentDashboard

# 分析项目结构与依赖
node scripts/dev.js analyze

# 任务管理 (通过Task Master)
node scripts/dev.js task <task-master命令>
# 例如: node scripts/dev.js task list

# 构建项目
node scripts/dev.js build <环境>
# 例如: node scripts/dev.js build prod
```

### generate-tasks.js

任务文件生成脚本，可以根据tasks.json文件自动生成或更新任务文件。

#### 使用方法

```bash
# 生成任务文件
node scripts/generate-tasks.js
```

该脚本会读取tasks/tasks.json文件，并为每个任务生成对应的task_XXX.txt文件。当你修改了tasks.json文件后，可以运行此脚本来同步更新所有任务文件。

### 产品需求文档

目录中包含以下产品需求相关文件：

- `prd.txt`: 智慧教育平台的产品需求文档，详细描述了平台的功能、架构、目标用户等信息
- `example_prd.txt`: 产品需求文档模板，可用于创建新的PRD文档

## 开发工作流程

1. **初始化项目结构**：
   ```bash
   node scripts/dev.js init
   ```
   创建必要的目录结构，包括按角色分类的视图、组件、存储等

2. **解析PRD创建任务**：
   ```bash
   # 使用Task Master解析PRD文档
   node scripts/dev.js task parse-prd
   ```

3. **查看任务列表**：
   ```bash
   node scripts/dev.js task list
   ```

4. **更新任务文件**：
   ```bash
   # 修改tasks.json后，生成更新任务文件
   node scripts/generate-tasks.js
   ```

5. **生成功能模块**：
   ```bash
   # 为特定功能生成组件和视图
   node scripts/dev.js generate view StudentDashboard
   node scripts/dev.js generate component CourseCard
   ```

6. **构建项目**：
   ```bash
   # 开发环境构建
   node scripts/dev.js build dev
   
   # 生产环境构建
   node scripts/dev.js build prod
   ```

## 角色功能参考

开发过程中可以参考下列文档了解各角色的核心功能：

- [学生功能分析](../黄义策/学生功能.md) - 学习管理、互动辅助、学习分析等功能详解
- [教师功能分析](../黄义策/教师功能.md) - 教学内容创建、数字人视频制作、课程管理等功能详解
- [管理员功能分析](../黄义策/管理员功能.md) - 系统管理、数据分析、资源管理等功能详解

## 环境变量配置

项目使用`.env`文件管理环境变量，可以创建以下文件进行环境配置：

- `.env`: 默认环境变量，适用于所有环境
- `.env.development`: 开发环境特定变量
- `.env.production`: 生产环境特定变量
- `.env.test`: 测试环境特定变量

## 注意事项

- 确保Node.js版本 >= 14.0.0
- 使用`npm install`安装所有依赖
- 开发前请先运行初始化脚本
- 遵循项目中定义的代码规范
- 提交代码前进行代码检查`npm run lint`
