<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="PPT在线编辑"
    activePage="content-creation"
    activeSubPage="ppt"
  >
    <div class="p-4">
      <!-- 面包屑导航 -->
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <router-link to="/teacher/dashboard" class="text-gray-700 hover:text-blue-600">
              <el-icon class="mr-2"><HomeFilled /></el-icon>首页
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <el-icon class="text-gray-400 mx-2"><ArrowRight /></el-icon>
              <router-link to="/teacher/content-creation" class="text-gray-700 hover:text-blue-600">教学内容制作</router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <el-icon class="text-gray-400 mx-2"><ArrowRight /></el-icon>
              <router-link to="/teacher/content-ppt-projects" class="text-gray-700 hover:text-blue-600">PPT项目管理</router-link>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <el-icon class="text-gray-400 mx-2"><ArrowRight /></el-icon>
              <span class="text-gray-500">{{ editMode ? 'PPT编辑' : 'PPT创建' }}</span>
            </div>
          </li>
        </ol>
      </nav>
      
      <!-- 文多多AiPPT编辑器 -->
      <div id="docmee-container" style="width: 100%; height: calc(100vh - 180px); border-radius: 12px; box-shadow: 0 0 12px rgba(120, 120, 120, 0.3); overflow: hidden; background: linear-gradient(-157deg, #f57bb0, #867dea);"></div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { DocmeeUI } from '@docmee/sdk-ui'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { ArrowRight, HomeFilled } from '@element-plus/icons-vue'
import { pptApi } from '@/api/ppt'

const router = useRouter()
const route = useRoute()

// 从路由参数获取数据
const editMode = computed(() => Boolean(route.query.editMode === 'true'))
const pptId = computed(() => route.query.pptId || '')
const pptTitle = computed(() => route.query.title || '')

// 老师数据
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

let docmeeUI = null
let markdownContent = ref('')
let slides = ref([])

// 初始化DocmeeUI
const initDocmeeUI = async () => {
  try {
    // 如果已经存在实例，先销毁
    if (docmeeUI) {
      docmeeUI.destroy()
      docmeeUI = null
    }
    
    // 初始化DocmeeUI
    const container = document.getElementById('docmee-container')
    if (!container) {
      console.error('找不到DocmeeUI容器元素')
      return
    }
    
    // 获取API Token
    const token = await getApiToken()
    
    // 配置项
    const config = {
      token: token,
      container: container,
      page: editMode.value ? "editor" : "creator", // 根据editMode决定使用creator还是editor页面
      pptId: editMode.value ? pptId.value : null, // 编辑模式需要传入pptId
      creatorVersion: "v2", // 指定v2版本
      lang: 'zh',
      mode: 'light',
      isMobile: false,
      background: 'linear-gradient(-157deg, #f57bb0, #867dea)',
      padding: '20px',
      onMessage: handleDocmeeMessage
    }
    
    // 如果是创建模式，添加创建相关的配置
    if (!editMode.value) {
      console.log('创建模式参数:', route.query);
      
      // 检查创建类型
      const creatorType = route.query.creatorType;
      
      if (creatorType === '1') {
        // 如果是空白模板创建
        config.creatorData = {
          type: 1,
          subject: route.query.subject,
        }
        console.log('使用空白模板创建:', config.creatorData);
      } else {
        // 从URL查询参数获取Markdown内容
        if (route.query.markdown) {
          try {
            markdownContent.value = decodeURIComponent(route.query.markdown)
          } catch (error) {
            console.error('解码Markdown内容失败:', error)
            markdownContent.value = ''
          }
        }
        
        config.creatorData = {
          type: 7, // 根据是否有Markdown内容决定类型
          createNow: true, // 自动开始生成
          content: markdownContent.value // 使用markdown内容
        }
        console.log('使用Markdown内容创建，内容长度:', markdownContent.value.length);
      }
    }
    
    // 创建DocmeeUI实例
    docmeeUI = new DocmeeUI(config)
    
    console.log(`DocmeeUI初始化成功，模式: ${editMode.value ? '编辑' : '创建'}`)
  } catch (error) {
    console.error('初始化DocmeeUI失败:', error)
  }
}

// 获取API Token
const getApiToken = async () => {
  try {
    // 调用后端接口获取token
    const response = await pptApi.getToken()
    
    if (response && (response.code === 0 || response.code === 200) && response.data && response.data.token) {
      console.log('成功获取Docmee API Token')
      return response.data.token
    } else {
      throw new Error(`获取Token失败: ${response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('获取API Token失败:', error)
    console.warn('无法获取API Token，请联系管理员')
    
    // 开发环境下可以使用临时token（生产环境不会执行到这里，因为后端会处理错误）
    return ''
  }
}

// 处理DocmeeUI消息
const handleDocmeeMessage = (message) => {
  console.log('DocmeeUI消息:', message)
  
  if (message.type === 'invalid-token') {
    console.warn('Token无效，请刷新页面重试')
  } else if (message.type === 'afterGenerate') {
    console.log('PPT生成完成')
    
    // 如果需要自动进入模板选择，可以在这里发送导航消息
    if (docmeeUI) {
      docmeeUI.navigate({
        page: 'editor',
        pptId: message.data.id
      })
    }
  } else if (message.type === 'charge') {
    // 处理PPT生成完毕扣费事件，保存ppt_id到数据库
    console.log('PPT生成完毕扣费事件:', message.data)
    const pptId = message.data.id
    if (pptId) {
      // 调用保存API
      savePptId(pptId)
    }
  } else if (message.type === 'beforeGenerate') {
    // 如果是大纲生成前的事件，可以在这里做一些处理
    const { subtype, fields } = message.data;
    if (subtype === 'outline') {
      console.log('即将生成ppt大纲', fields)
    } else if (subtype === 'ppt') {
      console.log('即将生成ppt', fields)
      if (docmeeUI) {
        docmeeUI.sendMessage({
          type: 'success',
          content: '继续生成PPT'
        })
      }
    }
    return true // 允许继续生成
  } else if (message.type === 'beforeCreateCustomTemplate') {
    const { file, totalPptCount } = message.data;
    // 是否允许用户继续制作PPT
    console.log('用户自定义完整模版，PPT文件：', file.name);
    if (totalPptCount < 2) {
      console.log('用户生成积分不足，不允许制作自定义完整模版');
      return false;
    }
    return true;
  } else if (message.type === 'beforeDownload') {
    // 自定义下载PPT的文件名称
    const { id, subject } = message.data;
    return `PPT_${subject}.pptx`;
  } else if (message.type === 'error') {
    if (message.data.code === 88) {
      // 创建token传了limit参数可以限制使用次数
      console.warn('您的次数已用完')
    } else {
      console.error('发生错误:', message.data?.message || '未知错误')
    }
  } else if (message.type === 'pageChange') {
    console.log('页面切换:', message.data.page)
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (docmeeUI) {
    docmeeUI.destroy()
    docmeeUI = null
  }
})

// 保存PPT ID到数据库
const savePptId = async (docmeePptId) => {
  try {
    // 从路由参数获取其他信息
    const title = route.query.title || '新建PPT'
    const subjectId = route.query.subjectId || '1'
    const description = route.query.description || ''
    
    // 调用API保存
    const response = await pptApi.savePptId({
      ppt_id: docmeePptId,
      title: title,
      subject_id: subjectId,
      description: description
    })
    
    if (response && (response.code === 0 || response.code === 200)) {
      console.log('PPT ID保存成功:', response.data)
    } else {
      console.error('PPT ID保存失败:', response.message)
    }
  } catch (error) {
    console.error('保存PPT ID失败:', error)
  }
}

// 在组件挂载时初始化编辑器
onMounted(() => {
  initDocmeeUI()
})
</script>

<style scoped>
/* 添加自定义样式 */
#docmee-container {
  color: white;
}
</style> 