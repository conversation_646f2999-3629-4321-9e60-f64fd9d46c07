<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="PPT制作"
    activePage="content-creation"
    activeSubPage="ppt"
  >
    <div class="space-y-6">
      <div class="flex flex-wrap justify-between items-center gap-4 mb-6">
        
        <!-- 搜索与筛选区域 -->
        <div class="flex flex-wrap md:flex-nowrap items-center gap-3 w-full">
          <!-- 搜索与筛选组件 -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-100 flex-grow">
            <div class="flex flex-col md:flex-row p-4 gap-4">
              <!-- 搜索框区域 -->
              <div class="flex-1 min-w-[200px]">
                <div class="relative flex items-center">
                  <div class="relative flex-1">
                    <input 
                      type="text" 
                      v-model="searchQuery"
                      @input="handleSearchInput"
                      @keyup.enter="fetchProjects"
                      placeholder="搜索PPT..." 
                      class="border border-gray-200 rounded-lg pl-10 pr-10 py-2.5 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                    <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</i>
                    
                    <!-- 加载指示器 -->
                    <div v-if="searchLoading" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                    
                    <!-- 清空按钮 -->
                    <button 
                      v-if="searchQuery && !searchLoading" 
                      @click="clearSearch"
                      class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="清空搜索"
                    >
                      <i class="material-icons text-lg">close</i>
                    </button>
                  </div>
                  
                  <!-- 搜索按钮 -->
                  <button 
                    @click="fetchProjects"
                    class="ml-3 px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center shadow-sm transition-colors"
                    :disabled="searchLoading"
                  >
                    <span>搜索</span>
                  </button>
                </div>
              </div>
              
              <!-- 筛选选项 -->
              <div class="flex flex-wrap gap-3 items-center">
                <!-- 学科筛选 -->
                <div class="relative group">
                  <select 
                    v-model="subjectFilter" 
                    @change="handleFilterChange"
                    class="appearance-none bg-gray-50 border border-gray-200 rounded-lg px-4 py-2.5 pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all cursor-pointer"
                  >
                    <option value="all">所有学科</option>
                    <option v-for="subject in subjectList" :key="subject.id" :value="subject.id">{{ subject.name }}</option>
                  </select>
                  <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">category</i>
                  <i class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">expand_more</i>
                </div>
                
                <!-- 排序选项 -->
                <div class="relative">
                  <select 
                    v-model="sortOption" 
                    @change="handleFilterChange"
                    class="appearance-none bg-gray-50 border border-gray-200 rounded-lg px-4 py-2.5 pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all cursor-pointer"
                  >
                    <option value="recent">最近更新</option>
                    <option value="oldest">最早创建</option>
                    <option value="az">名称 A-Z</option>
                    <option value="za">名称 Z-A</option>
                  </select>
                  <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">sort</i>
                  <i class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">expand_more</i>
                </div>
                
                <!-- 清空搜索按钮 -->
                <button 
                  v-if="searchQuery" 
                  @click="clearSearch"
                  class="bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg px-4 py-2.5 flex items-center transition-colors"
                >
                  <i class="material-icons mr-1 text-sm">backspace</i>
                  清空搜索
                </button>
              </div>
            </div>
            
          </div>
          
          <!-- 新建PPT按钮 -->
          <button 
            class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-2.5 px-6 rounded-lg flex items-center gap-2 shadow-sm transition-all duration-200 transform hover:scale-105 whitespace-nowrap h-[46px]"
            @click="createNewPPT"
          >
            <i class="material-icons text-sm">add</i>
            新建PPT
          </button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <!-- <div class="flex flex-wrap gap-4">
        <div class="bg-purple-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">PPT总数</p>
              <p class="text-2xl font-bold text-gray-800">18</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
              <i class="material-icons text-purple-600 text-xl">slideshow</i>
            </div>
          </div>
        </div>
        <div class="bg-blue-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">进行中</p>
              <p class="text-2xl font-bold text-gray-800">12</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <i class="material-icons text-blue-600 text-xl">check_circle</i>
            </div>
          </div>
        </div>
        <div class="bg-green-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">已完成</p>
              <p class="text-2xl font-bold text-gray-800">5</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
              <i class="material-icons text-green-600 text-xl">check_circle</i>
            </div>
          </div>
        </div>
        <div class="bg-red-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm"></p>
              <p class="text-2xl font-bold text-gray-800"></p>
            </div>
            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
              <i class="material-icons text-red-600 text-xl">edit</i>
            </div>
          </div>
        </div>
      </div> -->

      <!-- 项目列表 -->
      <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex justify-center items-center h-40 w-full">
          <div class="flex flex-col items-center">
            <svg class="animate-spin h-10 w-10 text-blue-500 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p class="text-gray-500">正在加载PPT项目...</p>
          </div>
        </div>
        
        <!-- 项目表格 -->
        <table v-else class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                PPT名称
              </th>
              <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学科
              </th>
              <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                幻灯片数量
              </th>
              <th scope="col" class="px-6 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                更新时间
              </th>
              <th scope="col" class="px-6 py-3.5 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="project in paginatedProjects" :key="project.id" class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 w-1.5 h-8 rounded-sm" :class="getSubjectColorClass(project.subject?.name)"></div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ project.title }}</div>
                    <div class="text-xs text-gray-500 max-w-md truncate">{{ project.description }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span :class="`w-6 h-6 rounded-full flex items-center justify-center ${getSubjectBgClass(project.subject?.name)}`">
                    <i class="material-icons text-xs">{{ getSubjectIconClass(project.subject?.name) }}</i>
                  </span>
                  <span class="ml-2 text-sm text-gray-700">{{ project.subject?.name }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ project.slides }} 张</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ project.updatedAt }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  class="text-blue-600 hover:text-blue-900 transition-colors inline-flex items-center"
                  @click="openDocmeeEditor(project.ppt_id)"
                >
                  <i class="material-icons text-sm mr-1">edit</i> 编辑
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 - 搜索无结果 -->
      <div v-if="!loading && searchQuery && paginatedProjects.length === 0" class="bg-white rounded-lg p-12 shadow-sm border border-gray-100 text-center">
        <div class="w-20 h-20 mx-auto bg-blue-50 rounded-full flex items-center justify-center mb-4">
          <i class="material-icons text-blue-400 text-3xl">search_off</i>
        </div>
        <h3 class="text-xl font-medium text-gray-800 mb-2">未找到匹配的PPT</h3>
        <p class="text-gray-600 mb-6">找不到包含 "{{ searchQuery }}" 的PPT项目</p>
        <div class="flex justify-center space-x-4">
          <button 
            class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2.5 px-5 rounded-lg flex items-center transition-colors"
            @click="clearSearch"
          >
            <i class="material-icons mr-2">backspace</i>
            清除搜索
          </button>
          <button 
            class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-2.5 px-5 rounded-lg flex items-center transition-all shadow-sm"
            @click="createNewPPT"
          >
            <i class="material-icons mr-2">add</i>
            新建PPT
          </button>
        </div>
      </div>

      <!-- 空状态 - 无项目 -->
      <div v-if="!loading && !searchQuery && paginatedProjects.length === 0" class="bg-white rounded-lg p-12 shadow-sm border border-gray-100 text-center">
        <div class="w-20 h-20 mx-auto bg-blue-50 rounded-full flex items-center justify-center mb-4">
          <i class="material-icons text-blue-400 text-3xl">slideshow</i>
        </div>
        <h3 class="text-xl font-medium text-gray-800 mb-2">暂无PPT项目</h3>
        <p class="text-gray-600 mb-6">开始创建您的第一个PPT吧</p>
        <button 
          class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-2.5 px-5 rounded-lg flex items-center mx-auto transition-all shadow-sm"
          @click="createNewPPT"
        >
          <i class="material-icons mr-2">add</i>
          新建PPT
        </button>
      </div>

      <!-- 分页控制 -->
      <div v-if="!loading && paginatedProjects.length > 0" class="mt-5 flex justify-between items-center bg-white rounded-lg p-4 shadow-sm">
        <div class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ startItem }}-{{ endItem }}</span> 条，共 <span class="font-medium">{{ totalItems }}</span> 条
        </div>
        <div class="flex items-center space-x-2">
          <button 
            class="border border-gray-200 rounded-lg px-3.5 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            :disabled="currentPage === 1"
            @click="currentPage--; fetchProjects();"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            <i class="material-icons text-sm">chevron_left</i>
          </button>
          
          <div v-for="pageNumber in displayedPageNumbers" :key="pageNumber" class="hidden md:block">
            <button 
              v-if="pageNumber !== '...'"
              @click="currentPage = pageNumber; fetchProjects();"
              class="px-3.5 py-2 rounded-lg text-sm font-medium transition-colors"
              :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'"
            >
              {{ pageNumber }}
            </button>
            <span v-else class="text-gray-500 px-2">...</span>
          </div>
          
          <button 
            class="border border-gray-200 rounded-lg px-3.5 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            :disabled="currentPage === totalPages"
            @click="currentPage++; fetchProjects();"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            <i class="material-icons text-sm">chevron_right</i>
          </button>
          
          <div class="flex items-center ml-2">
            <span class="text-sm text-gray-700 mr-2 hidden md:block">前往</span>
            <input 
              type="number" 
              v-model.number="goToPage" 
              min="1" 
              :max="totalPages"
              class="w-14 border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="text-sm text-gray-700 mx-2 hidden md:block">页</span>
            <button 
              @click="jumpToPage"
              class="border border-gray-200 rounded-lg px-3.5 py-2 bg-white text-sm font-medium text-blue-700 hover:bg-blue-50 transition-colors"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>

    
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { pptApi } from '@/api/ppt'
import { debounce } from 'lodash-es'

// Router
const router = useRouter()

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 搜索和筛选
const searchQuery = ref('')
const subjectFilter = ref('all')
const sortOption = ref('recent')

// 分页
const currentPage = ref(1)
const pageSize = ref(9)
const goToPage = ref(1)
const totalItems = ref(0)

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)

// 学科列表
const subjectList = ref([])

// PPT项目列表
const pptProjects = ref([])

// 获取学科列表
const fetchSubjects = async () => {
  try {
    const response = await pptApi.getSubjects()
    if (response.code === 200) {
      subjectList.value = response.data || []
    }
  } catch (error) {
    console.error('获取学科列表失败:', error)
  }
}

// 防抖的搜索处理函数
const debouncedSearch = debounce(() => {
  currentPage.value = 1
  searchLoading.value = true
  fetchProjects()
}, 500)

// 获取PPT项目列表
const fetchProjects = async () => {
  loading.value = true
  try {
    const params = {
      search: searchQuery.value,
      subject_id: subjectFilter.value,
      sort: sortOption.value,
      page: currentPage.value,
      page_size: pageSize.value
    }
    
    const response = await pptApi.getProjects(params)
    if (response.code === 200 && response.data) {
      pptProjects.value = response.data.list || []
      totalItems.value = response.data.total || 0
      
      // 如果当前页码大于总页数，则跳转到第一页
      if (currentPage.value > Math.ceil(totalItems.value / pageSize.value) && totalItems.value > 0) {
        currentPage.value = 1
        fetchProjects()
      }
    }
  } catch (error) {
    console.error('获取PPT项目列表失败:', error)
  } finally {
    loading.value = false
    searchLoading.value = false
  }
}

// 监听筛选条件变化，重置页码并重新获取数据
const handleFilterChange = () => {
  currentPage.value = 1
  fetchProjects()
}

// 处理搜索框输入
const handleSearchInput = () => {
  searchLoading.value = true
  debouncedSearch()
}

// 清空搜索
const clearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
  fetchProjects()
}

// 初始化数据
onMounted(() => {
  fetchSubjects()
  fetchProjects()
})

// 根据筛选条件过滤项目
const filteredProjects = computed(() => {
  return pptProjects.value
})

// 分页后的项目列表
const paginatedProjects = computed(() => {
  return filteredProjects.value
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(totalItems.value / pageSize.value) || 1
})

// 显示项目范围
const startItem = computed(() => {
  return (currentPage.value - 1) * pageSize.value + 1
})

const endItem = computed(() => {
  return Math.min(currentPage.value * pageSize.value, totalItems.value)
})

// 显示页码
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 跳转到指定页面
const jumpToPage = () => {
  if (goToPage.value >= 1 && goToPage.value <= totalPages.value) {
    currentPage.value = goToPage.value
    fetchProjects()
  } else {
    goToPage.value = currentPage.value
  }
}

// 获取状态标签和样式
const getStatusLabel = (status) => {
  const statusMap = {
    'completed': '已完成',
    'in-progress': '进行中',
    'review': '审核中',
    'draft': '草稿'
  }
  return statusMap[status] || status
}

const getStatusClasses = (status) => {
  const statusClassMap = {
    'completed': 'bg-green-100 text-green-800',
    'in-progress': 'bg-blue-100 text-blue-800',
    'review': 'bg-purple-100 text-purple-800',
    'draft': 'bg-red-100 text-red-800'
  }
  return statusClassMap[status] || 'bg-gray-100 text-gray-800'
}

// 获取学科相关样式
const getSubjectColorClass = (subject) => {
  const subjectColorMap = {
    'Python': 'bg-blue-500',
    '数学': 'bg-green-500',
    '英语': 'bg-yellow-500',
    '电子商务': 'bg-purple-500',
    '人工智能': 'bg-red-500'
  }
  return subjectColorMap[subject] || 'bg-gray-500'
}

const getSubjectBgClass = (subject) => {
  const subjectBgMap = {
    'Python': 'bg-blue-100 text-blue-600',
    '数学': 'bg-green-100 text-green-600',
    '英语': 'bg-yellow-100 text-yellow-600',
    '电子商务': 'bg-purple-100 text-purple-600',
    '人工智能': 'bg-red-100 text-red-600'
  }
  return subjectBgMap[subject] || 'bg-gray-100 text-gray-600'
}

const getSubjectIconClass = (subject) => {
  const subjectIconMap = {
    'Python': 'code',
    '数学': 'calculate',
    '英语': 'translate',
    '电子商务': 'shopping_cart',
    '人工智能': 'smart_toy'
  }
  return subjectIconMap[subject] || 'book'
}

// 项目操作方法
const createNewPPT = () => {
  console.log('创建新PPT')
  // 跳转到创建页面
  router.push('/teacher/ppt/create')
}

// 定义处理PPT生成扣费事件的方法
const handlePptCharge = async (data) => {
  try {
    console.log('处理PPT生成扣费事件:', data)
    if (data.pptId) {
      // 保存PPT ID到数据库
      const response = await pptApi.savePptId({
        ppt_id: data.pptId,
        title: '新建PPT', // 可以根据实际情况调整
        subject_id: subjectFilter.value === 'all' ? subjectList.value[0]?.id : subjectFilter.value,
        description: '通过文多多生成的PPT'
      })
      
      if (response.code === 200) {
        console.log('PPT保存成功:', response.data)
        // 可选：刷新项目列表
        fetchProjects()
      } else {
        console.error('PPT保存失败:', response.message)
      }
    }
  } catch (error) {
    console.error('保存PPT ID失败:', error)
  }
}

const viewProject = (projectId) => {
  console.log('查看PPT:', projectId)
  // 跳转到查看页面
  router.push(`/teacher/ppt/${projectId}`)
}

const editProject = (projectId) => {
  console.log('编辑PPT:', projectId)
  // 跳转到编辑页面
  router.push(`/teacher/ppt/${projectId}/edit`)
}

// 文多多编辑器相关
const showDocmeeEditorModal = ref(false)
const selectedPptId = ref(null)
const docmeeEditorRef = ref(null)

// 打开文多多编辑器
const openDocmeeEditor = (projectId) => {
  console.log('使用文多多编辑PPT:', projectId)
  
  // 跳转到DocmeePPTEditorView页面，传递必要参数
  router.push({
    path: '/teacher/ppt/editor',
    query: {
      editMode: 'true',
      pptId: projectId
    }
  })
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
