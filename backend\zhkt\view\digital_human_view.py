from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.forms.models import model_to_dict
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from ..services.digital_human_service import DigitalHumanService
from ..utils.file_utils import FileUtils


@method_decorator(csrf_exempt, name='dispatch')
class DigitalHumanViewSet(viewsets.ViewSet):
    """数字人管理视图集"""
    
    def _format_digital_human(self, digital_human):
        """格式化数字人数据"""
        data = model_to_dict(digital_human)
        data['avatar_url'] = FileUtils.get_file_url(digital_human.avatar_url) if digital_human.avatar_url else None
        data['video_url'] = FileUtils.get_file_url(digital_human.video_url) if digital_human.video_url else None
        data['created_at'] = digital_human.created_at.isoformat() if digital_human.created_at else None
        data['updated_at'] = digital_human.updated_at.isoformat() if digital_human.updated_at else None
        data['createdAt'] = digital_human.created_at.strftime('%Y-%m-%d') if digital_human.created_at else None
        data['usageCount'] = digital_human.usage_count or 0
        return data
    

    
    @action(detail=False, methods=['get'], url_path='list')
    def get_digital_humans(self, request):
        """
        获取数字人列表
        
        Query Parameters:
        - page: 当前页码，从1开始，默认1
        - page_size: 每页记录数，默认10
        - search: 搜索关键词，可选
        """
        try:
            user_id = getattr(request.user, 'id', 1)  # 临时使用用户ID 1
            
            # 支持新旧两种分页参数
            if 'page' in request.query_params:
                page = max(1, int(request.query_params.get('page', 1)))
                page_size = max(1, int(request.query_params.get('page_size', 10)))
                skip = (page - 1) * page_size
                limit = page_size
            else:
                skip = int(request.query_params.get('skip', 0))
                limit = int(request.query_params.get('limit', 10))
                page = (skip // limit) + 1 if limit > 0 else 1
                page_size = limit
            
            search = request.query_params.get('search', None)
            
            result = DigitalHumanService.get_digital_humans(
                user_id=user_id,
                skip=skip,
                limit=limit,
                search=search
            )
            
            return Response({
                "code": 200,
                "message": "获取数字人列表成功",
                "data": {
                    "total": result["total"],
                    "digital_humans": [self._format_digital_human(dh) for dh in result["digital_humans"]],
                    "page": page,
                    "page_size": page_size,
                    "pages": (result["total"] + page_size - 1) // page_size if page_size > 0 else 0
                }
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"获取数字人列表失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='active-list')
    def get_active_digital_humans(self, request):
        """
        获取可用数字人列表（仅限已激活状态）
        用于视频生成等需要可用数字人的场景
        
        Query Parameters:
        - skip: 跳过的记录数，默认0
        - limit: 返回的记录数，默认10
        - search: 搜索关键词，可选
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            skip = int(request.query_params.get('skip', 0))
            limit = int(request.query_params.get('limit', 10))
            search = request.query_params.get('search', None)
            
            result = DigitalHumanService.get_active_digital_humans(
                user_id=user_id,
                skip=skip,
                limit=limit,
                search=search
            )
            
            return Response({
                "code": 200,
                "message": "获取可用数字人列表成功",
                "data": {
                    "total": result["total"],
                    "digital_humans": [self._format_digital_human(dh) for dh in result["digital_humans"]],
                    "page": result["page"],
                    "size": result["size"],
                    "pages": result["pages"]
                }
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"获取可用数字人列表失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'], url_path='detail')
    def get_digital_human_detail(self, request, pk=None):
        """获取数字人详情"""
        try:
            user_id = getattr(request.user, 'id', 1)
            
            # 确保pk参数为整数
            if pk is None:
                return Response({
                    "code": 400,
                    "message": "数字人ID不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                digital_human_id = int(pk)
            except ValueError:
                return Response({
                    "code": 400,
                    "message": "数字人ID必须为整数",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
                
            digital_human = DigitalHumanService.get_digital_human_detail(
                user_id=user_id,
                digital_human_id=digital_human_id
            )
            
            if not digital_human:
                return Response({
                    "code": 404,
                    "message": "数字人不存在",
                    "data": None
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                "code": 200,
                "message": "获取数字人详情成功",
                "data": self._format_digital_human(digital_human)
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"获取数字人详情失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'], url_path='create')
    def create_digital_human(self, request):
        """
        创建数字人
        
        Body Parameters:
        - name: 数字人名称（必填）
        - description: 数字人描述（可选）
        - video: 训练视频文件（可选，如果提供将自动从第三帧截取头像）
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            name = request.data.get('name')
            description = request.data.get('description', '')
            video = request.FILES.get('video')
            
            if not name:
                return Response({
                    "code": 400,
                    "message": "数字人名称不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证视频文件
            if video:
                # 检查文件类型是否为MP4
                is_mp4 = (video.content_type == 'video/mp4' or 
                         video.name.lower().endswith('.mp4'))
                if not is_mp4:
                    return Response({
                        "code": 400,
                        "message": "仅支持MP4格式的视频文件",
                        "data": None
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            digital_human = DigitalHumanService.create_digital_human(
                user_id=user_id,
                name=name,
                description=description,
                video=video
            )
            
            return Response({
                "code": 200,
                "message": "数字人创建成功",
                "data": self._format_digital_human(digital_human)
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"创建数字人失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['put'], url_path='update')
    def update_digital_human(self, request, pk=None):
        """
        更新数字人
        
        Body Parameters:
        - name: 数字人名称（可选）
        - description: 数字人描述（可选）
        - video: 训练视频文件（可选，如果提供将自动从第三帧截取头像）
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            name = request.data.get('name')
            description = request.data.get('description')
            video = request.FILES.get('video')
            
            # 确保pk参数为整数
            if pk is None:
                return Response({
                    "code": 400,
                    "message": "数字人ID不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                digital_human_id = int(pk)
            except ValueError:
                return Response({
                    "code": 400,
                    "message": "数字人ID必须为整数",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证视频文件
            if video:
                # 检查文件类型是否为MP4
                is_mp4 = (video.content_type == 'video/mp4' or 
                         video.name.lower().endswith('.mp4'))
                if not is_mp4:
                    return Response({
                        "code": 400,
                        "message": "仅支持MP4格式的视频文件",
                        "data": None
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            digital_human = DigitalHumanService.update_digital_human(
                user_id=user_id,
                digital_human_id=digital_human_id,
                name=name,
                description=description,
                video=video
            )
            
            if not digital_human:
                return Response({
                    "code": 404,
                    "message": "数字人不存在",
                    "data": None
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                "code": 200,
                "message": "数字人更新成功",
                "data": self._format_digital_human(digital_human)
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"更新数字人失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['delete'], url_path='delete')
    def delete_digital_human(self, request, pk=None):
        """删除数字人"""
        try:
            user_id = getattr(request.user, 'id', 1)
            
            # 确保pk参数为整数
            if pk is None:
                return Response({
                    "code": 400,
                    "message": "数字人ID不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                digital_human_id = int(pk)
            except ValueError:
                return Response({
                    "code": 400,
                    "message": "数字人ID必须为整数",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            success = DigitalHumanService.delete_digital_human(
                user_id=user_id,
                digital_human_id=digital_human_id
            )
            
            if not success:
                return Response({
                    "code": 404,
                    "message": "数字人不存在或删除失败",
                    "data": None
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                "code": 200,
                "message": "数字人删除成功",
                "data": None
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"删除数字人失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'], url_path='system-presets')
    def get_system_presets(self, request):
        """获取系统预设数字人列表"""
        try:
            presets = DigitalHumanService.get_system_presets()
            
            return Response({
                "code": 200,
                "message": "获取系统预设成功",
                "data": presets
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"获取系统预设失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'], url_path='use-preset')
    def use_system_preset(self, request):
        """
        使用系统预设创建数字人
        
        Body Parameters:
        - preset_id: 预设ID（必填）
        - name: 数字人名称（必填）
        - description: 数字人描述（可选）
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            preset_id = request.data.get('preset_id')
            name = request.data.get('name')
            description = request.data.get('description', '')
            
            if not preset_id:
                return Response({
                    "code": 400,
                    "message": "预设ID不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not name:
                return Response({
                    "code": 400,
                    "message": "数字人名称不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            digital_human = DigitalHumanService.use_system_preset(
                user_id=user_id,
                preset_id=preset_id,
                name=name,
                description=description
            )
            
            return Response({
                "code": 200,
                "message": "基于预设创建数字人成功",
                "data": self._format_digital_human(digital_human)
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"基于预设创建数字人失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
 