<template>
  <div class="language-switcher">
    <el-dropdown trigger="click" @command="changeLanguage">
      <span class="language-dropdown-link">
        <el-icon><Collection /></el-icon>
        <span class="lang-text">{{ currentLangDisplay }}</span>
        <el-icon class="el-icon--right">
          <arrow-down />
        </el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="zh">简体中文</el-dropdown-item>
          <el-dropdown-item command="en">English</el-dropdown-item>
          <el-dropdown-item command="vi">Tiếng Việt</el-dropdown-item>
          <el-dropdown-item command="id">Bahasa Indonesia</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ArrowDown, Collection } from '@element-plus/icons-vue'
import axios from 'axios'

const { locale } = useI18n()
const currentLang = ref(locale.value)

// 显示当前语言名称
const currentLangDisplay = computed(() => {
  const langMap = {
    'zh': '简体中文',
    'en': 'English',
    'vi': 'Tiếng Việt',
    'id': 'Bahasa Indonesia'
  }
  return langMap[currentLang.value] || langMap['zh']
})

// 切换语言
function changeLanguage(lang) {
  currentLang.value = lang
  locale.value = lang
  localStorage.setItem('userLanguage', lang)
  
  // 设置API请求的语言头
  axios.defaults.headers.common['Accept-Language'] = lang
  
  // 添加页面刷新功能
  setTimeout(() => {
    window.location.reload()
  }, 100) // 短暂延时确保语言设置已保存
}

onMounted(() => {
  // 初始化时从本地存储获取语言设置
  const savedLang = localStorage.getItem('userLanguage')
  if (savedLang) {
    currentLang.value = savedLang
    locale.value = savedLang
  }
  
  // 设置API请求的语言头
  axios.defaults.headers.common['Accept-Language'] = currentLang.value
})
</script>

<style scoped>
.language-switcher {
  display: inline-flex;
  align-items: center;
}

.language-dropdown-link {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-color-primary);
  background-color: rgba(64, 158, 255, 0.1);
  padding: 6px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.language-dropdown-link:hover {
  background-color: rgba(64, 158, 255, 0.2);
}

.lang-text {
  margin: 0 4px;
}

.el-dropdown-menu__item:hover {
  color: var(--el-color-primary);
}
</style> 