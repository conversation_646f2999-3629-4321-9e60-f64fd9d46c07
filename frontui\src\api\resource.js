import request from '@/utils/request'

export const resourceApi = {
  /**
   * 更新资源信息
   * @param {number} id - 资源ID
   * @param {Object} data - 更新的资源数据
   * @returns {Promise} 包含更新结果的Promise
   */
  updateResource(id, data) {
    return request({
      url: `/resources/${id}/`,
      method: 'put',
      data
    })
  },

    /**
   * 部分更新资源信息
   * @param {number} id - 资源ID
   * @param {Object} data - 更新的资源数据
   * @returns {Promise} 包含更新结果的Promise
   */
  partialUpdateResource(id, data) {
    return request({
      url: `/resources/${id}/`,
      method: 'patch',
      data
    })
  },


  /**
   * 获取资源详情
   * @param {number} id - 资源ID
   * @returns {Promise} 包含资源详情的Promise
   */
  getResource(id) {
    return request({
      url: `/resources/${id}/`,
      method: 'get'
    })
  },

  /**
   * 删除资源
   * @param {number} id - 资源ID
   * @returns {Promise} 包含删除结果的Promise
   */
  deleteResource(id) {
    return request({
      url: `/resources/${id}/`,
      method: 'delete'
    })
  },

  /**
   * 获取资源列表
   * @param {Object} params - 查询参数
   * @param {number} [params.lesson_id] - 课时ID
   * @param {string} [params.resource_type] - 资源类型
   * @returns {Promise} 包含资源列表的Promise
   */
  getResourcesByLesson(params) {
    return request({
      url: '/resources/',
      method: 'get',
      params
    })
  },

  /**
   * 根据课时id 章节id获取资源列表
   * @param {Object} params - 查询参数
   * @param {string} [params.resource_type] - 资源类型
   * @returns {Promise} 包含资源列表的Promise
   */
  getResourceByChapterAndLesson: async (params) => {
    return request({
      url: '/resources/getResourceByChapterAndLesson/',
      method: 'post',
      params
    })
  }
} 