import request from '@/utils/request'

export const courseApi = {
  /**
   * 获取课程列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 包含课程列表的Promise
   */
  getCourses(params) {
    return request({
      url: '/courses/',
      method: 'get',
      params
    })
  },

  /**
   * 获取课程详情
   * @param {number} id - 课程ID
   * @returns {Promise} 包含课程详情的Promise
   */
  getCourseById(id) {
    return request({
      url: `/courses/${id}/`,
      method: 'get'
    })
  },

  /**
   * 创建新课程
   * @param {Object} data - 课程数据
   * @returns {Promise} 包含创建结果的Promise
   */
  createCourse(data) {
    return request({
      url: '/courses/',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      transformRequest: [(data) => {
        return data
      }]
    })
  },

  /**
   * 更新课程信息
   * @param {number} id - 课程ID
   * @param {Object} data - 更新的课程数据
   * @returns {Promise} 包含更新结果的Promise
   */
  updateCourse(id, data) {
    return request({
      url: `/courses/${id}/`,
      method: 'put',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      transformRequest: [(data) => {
        return data
      }]
    })
  },

  /**
   * 删除课程
   * @param {number} id - 课程ID
   * @returns {Promise} 包含删除结果的Promise
   */
  deleteCourse(id) {
    return request({
      url: `/courses/${id}/`,
      method: 'delete'
    })
  },

  /**
   * 部分更新课程信息
   * @param {number} id - 课程ID
   * @param {Object} data - 要更新的字段
   * @returns {Promise} 包含更新结果的Promise
   */
  partialUpdateCourse(id, data) {
    return request({
      url: `/courses/${id}/`,
      method: 'patch',
      data
    })
  },

  /**
   * 创建课程章节
   * @param {Object} data - 章节数据
   * @returns {Promise} 包含创建结果的Promise
   */
  createChapter(data) {
    return request({
      url: '/chapters/',
      method: 'post',
      data
    })
  },

  /**
   * 更新课程章节
   * @param {number} id - 章节ID
   * @param {Object} data - 章节数据
   * @returns {Promise} 包含更新结果的Promise
   */
  updateChapter(id, data) {
    return request({
      url: `/chapters/${id}/`,
      method: 'put',
      data
    })
  },

  /**
   * 删除课程章节
   * @param {number} id - 章节ID
   * @returns {Promise} 包含删除结果的Promise
   */
  deleteChapter(id) {
    return request({
      url: `/chapters/${id}/`,
      method: 'delete'
    })
  },

  /**
   * 获取课程的所有章节
   * @param {number} courseId - 课程ID
   * @returns {Promise} 包含章节列表的Promise
   */
  getChaptersByCourse(courseId) {
    return request({
      url: `/chapters/?course_id=${courseId}`,
      method: 'get'
    })
  },

  /**
   * 获取课程详情
   * @param {number} id - 课程ID
   * @returns {Promise} 包含课程详情的Promise
   */
  getCourseDetail(id) {
    return request({
      url: `/courses/${id}/`,
      method: 'get'
    })
  },

  /**
   * 上传课程封面
   * @param {number} id - 课程ID
   * @param {FormData} data - 包含文件的FormData对象
   * @returns {Promise} 包含上传结果的Promise
   */
  uploadCourseCover(id, data) {
    return request({
      url: `/courses/${id}/upload_cover/`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 为课程评分
   * @param {number} courseId - 课程ID
   * @param {Object} data - 评分数据
   * @param {number} data.rating - 评分值(0-5)
   * @param {string} data.comment - 评价内容(可选)
   * @returns {Promise} 包含评分结果的Promise
   */
  rateCourse(courseId, data) {
    return request({
      url: `/courses/${courseId}/rate/`,
      method: 'post',
      data
    })
  },

  /**
   * 获取课程评分列表
   * @param {number} courseId - 课程ID
   * @returns {Promise} 包含评分列表的Promise
   */
  getCourseRatings(courseId) {
    return request({
      url: `/courses/${courseId}/ratings/`,
      method: 'get'
    })
  },

  /**
   * 获取课程概述
   * @param {number} courseId - 课程ID
   * @returns {Promise} 包含课程概述的Promise
   */
  getCourseOverview(courseId) {
    return request({
      url: `/courses/${courseId}/overview/`,
      method: 'get'
    })
  },

  /**
   * 更新课程概述
   * @param {number} courseId - 课程ID
   * @param {Object} data - 课程概述数据
   * @returns {Promise} 包含更新结果的Promise
   */
  updateCourseOverview(courseId, data) {
    return request({
      url: `/courses/${courseId}/overview/`,
      method: 'put',
      data
    })
  }
}

export default courseApi

