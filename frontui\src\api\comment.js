import request from '@/utils/request'

export const commentApi = {
  // 获取课程评论列表
  getComments(courseId) {
    return request({
      url: '/comments/',
      method: 'get',
      params: { course_id: courseId }
    })
  },

  // 创建评论
  createComment(data) {
    return request({
      url: '/comments/',
      method: 'post',
      data
    })
  },

  // 回复评论
  replyToComment(commentId, data) {
    return request({
      url: `/comments/${commentId}/reply/`,
      method: 'post',
      data
    })
  },

  // 点赞/取消点赞评论
  likeComment(commentId) {
    return request({
      url: `/comments/${commentId}/like/`,
      method: 'post'
    })
  },

  // 删除评论
  deleteComment(commentId) {
    return request({
      url: `/comments/${commentId}/`,
      method: 'delete'
    })
  }
} 