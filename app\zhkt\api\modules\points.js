import request from '@/utils/request'

// 获取商品列表
export function getGoodsList(params) {
  return request({
    url: `/products/?search=${params.keyword}&sort=${params.sort}&category=${params.category}&page=${params.page}`,
    method: 'get'
  })
}

// 获取商品详情
export const getGoodsDetail = (id) => {
  return request({
    url: `/products/${id}`,
    method: 'GET'
  })
}

// 兑换商品
export const exchangeGoods = (data) => {
  return request({
    url: '/orders/',
    method: 'POST',
    data
  })
}

// 获取积分记录
export const getPointsRecord = (params) => {
  return request({
    url: '/points/record',
    method: 'GET',
    data: params
  })
}

// 获取兑换记录
export const getExchangeRecord = (params) => {
  return request({
    url: '/points/exchange/record',
    method: 'GET',
    data: params
  })
} 