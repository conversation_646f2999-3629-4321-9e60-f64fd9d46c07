<template>
  <StudentLayout 
    pageTitle="积分商城" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    
    <div class="points-mall">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">
                <span>总兑换次数</span>
                <el-icon><Medal /></el-icon>
              </div>
            </template>
            <div class="stat-value">{{ exchangeStats.total }}</div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">
                <span>待处理订单</span>
                <el-icon><Plus /></el-icon>
              </div>
            </template>
            <div class="stat-value">{{ exchangeStats.pending }}</div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <template #header>
              <div class="card-header">
                <span>已完成订单</span>
                <el-icon><Coin /></el-icon>
              </div>
            </template>
            <div class="stat-value">{{ exchangeStats.completed }}</div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 热门商品 -->
      <div class="section-title mb-4">热门商品</div>
      <el-row :gutter="20" class="mb-6">
        <el-col :span="6" v-for="product in hotProducts" :key="product.id">
          <el-card shadow="hover" class="product-card">
            <el-image 
              :src="getProductImage(product.image)" 
              class="product-image"
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
              <p class="product-desc">{{ product.description }}</p>
              <div class="product-meta">
                <el-tag v-if="product.tag" :type="product.tag === '热门' ? 'danger' : 'warning'" size="small">
                  {{ product.tag }}
                </el-tag>
                <span class="points">{{ product.points_price }} 积分</span>
              </div>
              <div class="product-stock">{{ product.stock }}</div>
              <el-button type="primary" @click="showExchangeDialog(product)" :disabled="studentStore.studentData.points < product.points_price">
                立即兑换
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 商品列表 -->
      <div class="product-section">
        <div class="section-header">
          <div class="section-title">全部商品</div>
          <div class="filter-section">
            <el-input
              v-model="searchQuery"
              placeholder="搜索商品"
              class="search-input"
              :prefix-icon="Search"
            />
            <el-radio-group v-model="currentCategory" class="category-radio-group ml-4">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="physical">实物商品</el-radio-button>
              <el-radio-button label="virtual">虚拟商品</el-radio-button>
              <el-radio-button label="learning">学习资源</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="6" v-for="product in filteredProducts" :key="product.id">
            <el-card shadow="hover" class="product-card">
              <el-image 
                :src="getProductImage(product.image)" 
                class="product-image"
                fit="cover"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>加载失败</span>
                  </div>
                </template>
              </el-image>
              <div class="product-info">
                <h3 class="product-name">{{ product.name }}</h3>
                <p class="product-desc">{{ product.description }}</p>
                <div class="product-meta">
                  <span class="points">{{ product.points_price }} 积分</span>
                </div>
                <div class="product-stock">{{ product.stock }}</div>
                <el-button type="primary" @click="showExchangeDialog(product)" :disabled="studentStore.studentData.points < product.points_price">
                  立即兑换
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 36, 48]"
            :total="totalProducts"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 兑换弹窗 -->
      <el-dialog
        v-model="exchangeDialogVisible"
        title="商品兑换"
        width="500px"
        @close="handleClose"
      >
        <div v-if="selectedProduct" class="exchange-dialog-content">
          <div class="selected-product-info">
            <el-image 
              :src="getProductImage(selectedProduct.image)" 
              class="selected-product-image"
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
            <div class="selected-product-details">
              <h3>{{ selectedProduct.name }}</h3>
              <p>所需积分：{{ selectedProduct.points_price }}</p>
              <p>当前库存：{{ selectedProduct.stock }}</p>
            </div>
          </div>
          
          <el-form :model="exchangeForm" label-width="80px" class="mt-4">
            <el-form-item label="收货人">
              <el-input v-model="exchangeForm.name" placeholder="请输入收货人姓名" />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="exchangeForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="收货地址">
              <el-input
                v-model="exchangeForm.address"
                type="textarea"
                placeholder="请输入详细收货地址"
              />
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="confirmExchange">确认兑换</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search, Coin, Plus, Medal, Picture } from '@element-plus/icons-vue'
import { useStudentStore } from '@/stores/student'
import { ElMessage } from 'element-plus'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { getProducts, getHotProducts, exchangeProduct, getOrders } from '@/api/points'
import defaultProductImage from '@/assets/images/products/coffee.jpg'

const studentStore = useStudentStore()

// 统计数据
const exchangeStats = ref({
  total: 0,
  pending: 0,
  completed: 0
})

// 分类和搜索
const currentCategory = ref('all')
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const totalProducts = ref(100)

// 热门商品数据
const hotProducts = ref([])

// 商品数据
const products = ref([])

// 筛选商品
const filteredProducts = computed(() => {
  let result = products.value
  
  if (currentCategory.value !== 'all') {
    result = result.filter(p => p.category === currentCategory.value)
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(p => 
      p.name.toLowerCase().includes(query) || 
      p.description.toLowerCase().includes(query)
    )
  }
  
  return result
})

// 兑换相关
const exchangeDialogVisible = ref(false)
const selectedProduct = ref(null)
const exchangeForm = ref({
  name: '',
  phone: '',
  address: ''
})

// 获取商品图片
const getProductImage = (image) => {
  return image || defaultProductImage
}

// 显示兑换弹窗
const showExchangeDialog = (product) => {
  selectedProduct.value = product
  exchangeDialogVisible.value = true
}

// 关闭弹窗
const handleClose = () => {
  exchangeDialogVisible.value = false
  exchangeForm.value = {
    name: '',
    phone: '',
    address: ''
  }
}

// 确认兑换
const confirmExchange = async () => {
  if (!exchangeForm.value.name || !exchangeForm.value.phone || !exchangeForm.value.address) {
    ElMessage.warning('请填写完整的收货信息')
    return
  }
  
  try {
    await exchangeProduct({
      product_id: selectedProduct.value.id,
      shipping_info: {
        name: exchangeForm.value.name,
        phone: exchangeForm.value.phone,
        address: exchangeForm.value.address
      }
    })
    
  ElMessage.success('兑换成功！')
  exchangeDialogVisible.value = false
  
    // 重新加载数据
    loadProducts()
    loadHotProducts()
    loadExchangeStats()
  } catch (error) {
    ElMessage.error(error.response?.data?.error || '兑换失败')
  }
}

// 加载商品数据
const loadProducts = async () => {
  try {
    const res = await getProducts({
      page: currentPage.value,
      page_size: pageSize.value,
      category: currentCategory.value === 'all' ? '' : currentCategory.value,
      search: searchQuery.value
    })
    products.value = res.results
    totalProducts.value = res.count
  } catch (error) {
    console.log('获取商品列表失败',error);
    ElMessage.error('获取商品列表失败')
  }
}

// 加载热门商品
const loadHotProducts = async () => {
  try {
    const res = await getHotProducts()
    hotProducts.value = res
  } catch (error) {
    console.log('获取热门商品失败',error);
    ElMessage.error('获取热门商品失败')
  }
}

// 加载订单统计
const loadExchangeStats = async () => {
  try {
    const res = await getOrders()
    exchangeStats.value = {
      total: res.results.length,
      pending: res.results.filter(order => order.status === 'PENDING').length,
      completed: res.results.filter(order => order.status === 'COMPLETED').length
    }
  } catch (error) {
    console.log('获取兑换记录失败',error);
    ElMessage.error('获取兑换记录失败')
  }
}

// 监听分类和搜索变化
watch([currentCategory, searchQuery], () => {
  currentPage.value = 1
  loadProducts()
})

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadProducts()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadProducts()
}

// 初始化加载
onMounted(async () => {
  if(!studentStore.studentData.id) {
    await studentStore.fetchCurrentStudentInfo()
  }
  loadProducts()
  loadHotProducts()
  loadExchangeStats()
})
</script>

<style scoped>
.points-mall {
  padding: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.ml-4 {
  margin-left: 16px;
}

.stat-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-color-primary);
    text-align: center;
  }
}

.product-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;

  &:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 4px;
  }

  .product-info {
    padding: 12px 0;

    .product-name {
      font-size: 16px;
      font-weight: bold;
      margin: 8px 0;
    }

    .product-desc {
      font-size: 14px;
      color: #666;
      margin: 8px 0;
      height: 40px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .product-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 8px 0;

      .points {
        color: #f56c6c;
        font-weight: bold;
      }
    }

    .product-stock {
      font-size: 12px;
      color: #999;
      margin-bottom: 8px;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .filter-section {
    display: flex;
    align-items: center;

    .search-input {
      width: 200px;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.exchange-dialog-content {
  .selected-product-info {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;

    .selected-product-image {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }

    .selected-product-details {
      h3 {
        margin: 0 0 8px 0;
      }

      p {
        margin: 4px 0;
        color: #666;
      }
    }
  }
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  height: 100%;
  background-color: var(--el-fill-color-lighter);
}

.image-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style> 