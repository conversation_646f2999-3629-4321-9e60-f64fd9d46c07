<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input type="text" placeholder="搜索课程" v-model="keyword" @confirm="onSearch" />
      </view>
    </view>
    
    <!-- 课程分类 -->
    <scroll-view class="category-list" scroll-x>
      <view 
        class="category-item" 
        :class="{ active: currentCategory === item.id }"
        v-for="item in categories" 
        :key="item.id"
        @click="switchCategory(item.id)"
      >
        {{ item.name }}
      </view>
    </scroll-view>
    
    <!-- 课程列表 -->
    <scroll-view class="course-list" scroll-y @scrolltolower="loadMore">
      <view class="course-item" v-for="course in courseList" :key="course.id" @click="navigateToCourse(course)">
        <image :src="course.cover_image" mode="aspectFill" class="course-image"></image>
        <view class="course-info">
          <text class="course-name">{{ course.name }}</text>
          <view class="course-meta">
            <text class="teacher">{{ course.teacher.title }}</text>
            <text class="students">{{ course.student_count }}人学习</text>
          </view>
          <view class="progress-bar" v-if="course.progress">
            <view class="progress" :style="{ width: course.progress + '%' }"></view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <uni-load-more :status="loadMoreStatus"></uni-load-more>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()

// 搜索关键词
const keyword = ref('')

// 课程分类
const categories = ref([
  { id: 0, name: '全部' },
  { id: 1, name: '必修课' },
  { id: 2, name: '选修课' },
])
const currentCategory = ref(0)

// 课程列表
const courseList = ref([])
const page = ref(1)
const loadMoreStatus = ref('more')

// 获取课程列表
const getCourseList = async (refresh = false) => {
  if (refresh) {
    page.value = 1
    courseList.value = []
  }
  
  try {
    const studentId = userStore.userInfo.role_info.id
    loadMoreStatus.value = 'loading'
    const response = await request({
      url: `/students/${studentId}/courses/?page=${page.value}&page_size=5&course_type=${currentCategory.value}&keyword=${keyword.value}`,
      method: 'GET'
    })
    
    const { results, next } = response
    courseList.value = [...courseList.value, ...results]
    loadMoreStatus.value = next ? 'more' : 'noMore'
  } catch (error) {
    loadMoreStatus.value = 'more'
    uni.showToast({
      title: '获取课程列表失败',
      icon: 'none'
    })
  }
}

// 切换分类
const switchCategory = (categoryId) => {
  currentCategory.value = categoryId
  getCourseList(true)
}

// 搜索课程
const onSearch = () => {
  getCourseList(true)
}

// 加载更多
const loadMore = () => {
  if (loadMoreStatus.value === 'loading' || loadMoreStatus.value === 'noMore') return
  page.value++
  getCourseList()
}

// 跳转到课程详情
const navigateToCourse = (course) => {
  uni.navigateTo({
    url: `/pages/course/detail?id=${course.id}`
  })
}

onMounted(() => {
  getCourseList()
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
  overflow-x: hidden;
  box-sizing: border-box;
}

.search-bar {
  padding: 20rpx;
  background: #fff;
  
  .search-input {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    padding: 10rpx 20rpx;
    border-radius: 30rpx;
    
    input {
      flex: 1;
      margin-left: 10rpx;
      font-size: 28rpx;
    }
  }
}

.category-list {
  white-space: nowrap;
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  
  .category-item {
    display: inline-block;
    padding: 10rpx 30rpx;
    margin-right: 20rpx;
    font-size: 28rpx;
    color: #666;
    background: #f5f5f5;
    border-radius: 30rpx;
    
    &.active {
      color: #fff;
      background: #3cc51f;
    }
    
    &:last-child {
      margin-right: 0;
    }
  }
}

.course-list {
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
  
  .course-item {
    background: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
    
    .course-image {
      width: 100%;
      height: 300rpx;
      display: block;
    }
    
    .course-info {
      padding: 20rpx;
      
      .course-name {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        margin-bottom: 10rpx;
      }
      
      .course-meta {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        .teacher {
          font-size: 24rpx;
          color: #666;
          margin-right: 20rpx;
        }
        
        .students {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .progress-bar {
        height: 6rpx;
        background: #eee;
        border-radius: 3rpx;
        
        .progress {
          height: 100%;
          background: #3cc51f;
          border-radius: 3rpx;
          transition: width 0.3s;
        }
      }
    }
  }
}
</style> 