<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="教材制作"
    activePage="content-creation"
    activeSubPage="textbook"
  >
    <div class="space-y-6">
      <!-- 顶部操作区 -->
      <div class="flex flex-wrap gap-4 mb-6">
        <!-- 左侧：搜索和筛选 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100 flex-1">
          <div class="flex flex-wrap items-center gap-4">
            <!-- 搜索框 -->
            <div class="relative flex-1 min-w-[280px]">
              <input 
                type="text" 
                v-model="searchQuery"
                placeholder="搜索教材项目..." 
                class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</i>
            </div>
            
            <!-- 筛选项 -->
            <div class="flex flex-wrap items-center gap-3">
              <select 
                v-model="subjectFilter" 
                class="border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
              >
                <option value="all">所有学科</option>
                <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                  {{ subject.name }}
                </option>
              </select>
              
              <select 
                v-model="sortOption" 
                class="border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
              >
                <option value="recent">最近更新</option>
                <option value="oldest">最早创建</option>
                <option value="az">名称 A-Z</option>
                <option value="za">名称 Z-A</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- 右侧：操作按钮 -->
        <div class="flex items-center gap-3">
          <button
            class="bg-gray-50 hover:bg-blue-50 text-blue-700 border border-blue-200 px-4 py-2 rounded-md font-medium flex items-center gap-2"
            @click="showRuleModal = true"
          >
            <i class="material-icons text-base">rule</i>
            教材生成规则
          </button>
          
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center gap-2 whitespace-nowrap"
            @click="createNewTextbook"
          >
            <i class="material-icons text-sm">add</i>
            新建教材项目
          </button>
        </div>
      </div>
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
      <!-- 项目列表 -->
      <div v-else-if="paginatedProjects.length > 0" class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                项目名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学科
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                教材类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                更新时间
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="project in paginatedProjects" :key="project.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 w-1.5 h-8 rounded-sm" :class="getSubjectColorClass(project.subject?.name)"></div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ project.title }}</div>
                    <div class="text-xs text-gray-500 max-w-md truncate">{{ project.description || '无具体描述' }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span :class="`w-6 h-6 rounded-full flex items-center justify-center ${getSubjectBgClass(project.subject?.name)}`">
                    <i class="material-icons text-xs">{{ getSubjectIconClass(project.subject?.name) }}</i>
                  </span>
                  <span class="ml-2 text-sm text-gray-700">{{ project.subject?.name }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ project.textbook_type }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ formatDate(project.updated_at) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  class="text-blue-600 hover:text-blue-900 mr-3" 
                  @click="viewProject(project.id)"
                >
                  查看
                </button>
                <button 
                  class="text-green-600 hover:text-green-900 mr-3" 
                  @click="editProject(project.id)"
                >
                  编辑
                </button>
                <button 
                  class="text-red-600 hover:text-red-900" 
                  @click="deleteProject(project.id)"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 空状态 -->
      <div v-else class="bg-white rounded-lg p-12 shadow-sm border border-gray-100 text-center">
        <div class="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <i class="material-icons text-gray-400 text-2xl">search</i>
        </div>
        <h3 class="text-lg font-medium text-gray-800 mb-2">未找到教材项目</h3>
        <p class="text-gray-600 mb-4">尝试调整搜索条件或创建新的教材项目</p>
        <button 
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
          @click="createNewTextbook"
        >
          新建教材项目
        </button>
      </div>
      <!-- 分页控制 -->
      <div v-if="filteredProjects.length > 0" class="mt-5 flex justify-between items-center">
        <div class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ startItem }}-{{ endItem }}</span> 条，共 <span class="font-medium">{{ filteredProjects.length }}</span> 条
        </div>
        <div class="flex items-center space-x-2">
          <button 
            class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="currentPage === 1"
            @click="currentPage--"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            上一页
          </button>
          <div v-for="pageNumber in displayedPageNumbers" :key="pageNumber">
            <button 
              v-if="pageNumber !== '...'"
              @click="currentPage = pageNumber"
              class="px-3 py-1 rounded-md text-sm font-medium"
              :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'"
            >
              {{ pageNumber }}
            </button>
            <span v-else class="text-gray-500 px-2">...</span>
          </div>
          <button 
            class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            下一页
          </button>
          <div class="flex items-center ml-2">
            <span class="text-sm text-gray-700 mr-2">前往</span>
            <input 
              type="number" 
              v-model.number="goToPage" 
              min="1" 
              :max="totalPages"
              class="w-12 border border-gray-300 rounded-md px-2 py-1 text-sm"
            />
            <span class="text-sm text-gray-700 mx-2">页</span>
            <button 
              @click="jumpToPage"
              class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-blue-700 hover:bg-blue-50"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-96 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
        <p class="text-gray-700 mb-4">您确定要删除这个教材项目吗？此操作无法撤销。</p>
        <div class="flex justify-end gap-3">
          <button 
            @click="cancelDelete" 
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button 
            @click="confirmDelete" 
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            删除
          </button>
        </div>
      </div>
    </div>
    <!-- 教材生成规则弹窗 -->
    <div v-if="showRuleModal" class="fixed inset-0 bg-gray-600 bg-opacity-40 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-xl p-6 relative">
        <h3 class="text-lg font-bold text-gray-900 mb-4">教材生成规则</h3>
        <p class="text-gray-600 mb-4 text-sm">这些规则将作为生成教材的提示词，您可以添加、编辑或删除规则。</p>
        <div class="space-y-3 max-h-64 overflow-y-auto mb-4">
          <div v-for="(rule, idx) in rules" :key="idx" class="flex items-center gap-2">
            <input
              v-model="rules[idx]"
              class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入规则内容，如：教材需包含章节小结..."
            />
            <button @click="removeRule(idx)" class="text-red-500 hover:text-red-700 px-2 py-1">
              <i class="material-icons text-base">delete</i>
            </button>
          </div>
        </div>
        <div class="flex gap-2 mb-4">
          <input
            v-model="newRule"
            class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="添加新规则，如：每章需有思考题..."
            @keyup.enter="addRule"
          />
          <button @click="addRule" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">添加</button>
        </div>
        <div class="flex justify-end gap-2 mt-2">
          <button @click="showRuleModal = false" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">关闭</button>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { subjectApi } from '@/api/subject'

const router = useRouter()
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})
const searchQuery = ref('')
const subjectFilter = ref('all')
const sortOption = ref('recent')
const subjects = ref([])
const currentPage = ref(1)
const pageSize = ref(9)
const goToPage = ref(1)
const isLoading = ref(false)
const textbookProjects = ref([])

// 教材生成规则弹窗相关
const showRuleModal = ref(false)
const rules = ref([
  '教材需包含章节小结',
  '每章需有思考题',
])
const newRule = ref('')
const addRule = () => {
  if (newRule.value.trim()) {
    rules.value.push(newRule.value.trim())
    newRule.value = ''
  }
}
const removeRule = (idx) => {
  rules.value.splice(idx, 1)
}

// 假数据
const mockTextbookProjects = [
  {
    id: 1,
    title: 'Python程序设计基础',
    description: '适用于大一新生的Python入门教材',
    subject: { id: 'python', name: 'Python' },
    textbook_type: '电子教材',
    updated_at: '2024-06-01T10:00:00',
    created_at: '2024-05-01T09:00:00'
  },
  {
    id: 2,
    title: '高等数学A版',
    description: '理工科通用高数教材',
    subject: { id: 'math', name: '数学' },
    textbook_type: '纸质教材',
    updated_at: '2024-05-28T14:30:00',
    created_at: '2024-04-15T08:00:00'
  },
  {
    id: 3,
    title: '商务英语实用教程',
    description: '面向商务英语专业的教材',
    subject: { id: 'english', name: '英语' },
    textbook_type: '电子教材',
    updated_at: '2024-05-20T16:00:00',
    created_at: '2024-03-20T10:00:00'
  },
  {
    id: 4,
    title: '人工智能导论',
    description: 'AI基础理论与应用',
    subject: { id: 'ai', name: '人工智能' },
    textbook_type: '电子教材',
    updated_at: '2024-05-18T11:00:00',
    created_at: '2024-03-10T10:00:00'
  }
]

const loadSubjects = async () => {
  try {
    const response = await subjectApi.getSubjects()
    if (response.data && response.data.length > 0) {
      subjects.value = response.data
    } else {
      console.warn('未获取到学科数据')
    }
  } catch (error) {
    console.error('获取学科列表失败:', error)
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '未知日期'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return dateString
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const loadTextbookProjects = async () => {
  isLoading.value = true
  try {
    // 直接赋值假数据
    textbookProjects.value = mockTextbookProjects
  } finally {
    isLoading.value = false
  }
}

const filteredProjects = computed(() => {
  let result = textbookProjects.value
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(project => 
      project.title.toLowerCase().includes(query) || 
      (project.description && project.description.toLowerCase().includes(query)) ||
      (project.subject?.name && project.subject.name.toLowerCase().includes(query))
    )
  }
  if (sortOption.value === 'recent') {
    result = [...result].sort((a, b) => new Date(b.updated_at || 0) - new Date(a.updated_at || 0))
  } else if (sortOption.value === 'oldest') {
    result = [...result].sort((a, b) => new Date(a.created_at || 0) - new Date(b.created_at || 0))
  } else if (sortOption.value === 'az') {
    result = [...result].sort((a, b) => a.title.localeCompare(b.title))
  } else if (sortOption.value === 'za') {
    result = [...result].sort((a, b) => b.title.localeCompare(a.title))
  }
  return result
})

const paginatedProjects = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredProjects.value.slice(startIndex, startIndex + pageSize.value)
})

const totalPages = computed(() => {
  return Math.ceil(filteredProjects.value.length / pageSize.value) || 1
})

const startItem = computed(() => {
  return (currentPage.value - 1) * pageSize.value + 1
})
const endItem = computed(() => {
  return Math.min(currentPage.value * pageSize.value, filteredProjects.value.length)
})
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5
  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    result.push(1)
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    if (startPage > 2) {
      result.push('...')
    }
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    result.push(totalPages.value)
  }
  return result
})
const jumpToPage = () => {
  if (goToPage.value >= 1 && goToPage.value <= totalPages.value) {
    currentPage.value = goToPage.value
  } else {
    goToPage.value = currentPage.value
  }
}
const getSubjectColorClass = (subject) => {
  const subjectColorMap = {
    'Python': 'bg-blue-500',
    '数学': 'bg-green-500',
    '英语': 'bg-yellow-500',
    '电子商务': 'bg-purple-500',
    '人工智能': 'bg-red-500'
  }
  return subjectColorMap[subject] || 'bg-gray-500'
}
const getSubjectBgClass = (subject) => {
  const subjectBgMap = {
    'Python': 'bg-blue-100 text-blue-600',
    '数学': 'bg-green-100 text-green-600',
    '英语': 'bg-yellow-100 text-yellow-600',
    '电子商务': 'bg-purple-100 text-purple-600',
    '人工智能': 'bg-red-100 text-red-600'
  }
  return subjectBgMap[subject] || 'bg-gray-100 text-gray-600'
}
const getSubjectIconClass = (subject) => {
  const subjectIconMap = {
    'Python': 'code',
    '数学': 'calculate',
    '英语': 'translate',
    '电子商务': 'shopping_cart',
    '人工智能': 'smart_toy'
  }
  return subjectIconMap[subject] || 'book'
}
const showDeleteConfirm = ref(false)
const pendingDeleteId = ref(null)
const createNewTextbook = () => {
  router.push('/teacher/textbook/create')
}
const viewProject = (projectId) => {
  router.push(`/teacher/textbook/${projectId}/detail`)
}
const editProject = (projectId) => {
  router.push(`/teacher/textbook/${projectId}/edit`)
}
const deleteProject = (projectId) => {
  pendingDeleteId.value = projectId
  showDeleteConfirm.value = true
}
const cancelDelete = () => {
  pendingDeleteId.value = null
  showDeleteConfirm.value = false
}
const confirmDelete = async () => {
  if (!pendingDeleteId.value) return
  
  try {
    // 使用模拟删除操作，而不是调用API
    textbookProjects.value = textbookProjects.value.filter(project => project.id !== pendingDeleteId.value)
    showDeleteConfirm.value = false
    pendingDeleteId.value = null
  } catch (error) {
    console.error('删除教材项目失败:', error)
  }
}
watch(subjectFilter, () => {
  loadTextbookProjects()
})
onMounted(() => {
  loadSubjects()
  loadTextbookProjects()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 