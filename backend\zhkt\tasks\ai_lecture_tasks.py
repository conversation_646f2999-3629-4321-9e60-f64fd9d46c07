# -*- coding: utf-8 -*-
# @Time    : 2025/5/1
# <AUTHOR> ai-education
# @File    : ai_lecture_tasks.py
# @Description : AI讲课文档处理Celery任务 - 重构版本，使用编排器模式

import logging
import os
from typing import Dict, Any

from celery import shared_task, states
from celery.exceptions import Ignore
from tenacity import retry, stop_after_attempt, wait_fixed
from ..services.ai_lecture import AILectureOrchestrator, SubtitleAnalyzer
from ..utils.gemini_utils import generate_dialogue_and_audio
from ..utils.temp_file_utils import create_temp_subdir, clean_temp_dir
logger = logging.getLogger(__name__)

# 创建全局编排器实例
orchestrator = AILectureOrchestrator()






@shared_task(bind=True, max_retries=3)
def process_ai_outline_task(self, document_id: int) -> Dict[str, Any]:
    """
    Celery任务：生成大纲
    Args:
        document_id: AILectureDocument主键
    Returns:
        dict: 处理结果
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '开始处理AI讲课文档'})
        result = orchestrator.process_document_outline_generation(document_id)

        if result.get('status') == 'fail':
            return {'status': 'fail', 'error': result.get('error', '未知错误')}

        return result
    except Exception as e:
        return {'status': 'fail', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def process_ai_lecture_chapter_task(self, chapter_id: int) -> Dict[str, Any]:
    """
    Celery任务：只处理指定章节的要点、HTML、语音等内容
    Args:
        chapter_id: AILectureChapter主键
    Returns:
        dict: 处理结果
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '开始处理章节内容'})
        result = process_ai_lecture_chapter(chapter_id)

        if result.get('status') == 'fail':
            return {'status': 'fail', 'error': result.get('error', '未知错误')}

        return result
    except Exception as e:
        return {'status': 'fail', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def process_ai_outline_with_first_chapter_task(self, document_id: int) -> Dict[str, Any]:
    """
    Celery任务：生成大纲并处理第一章内容
    先生成文档大纲，然后处理第一章的要点、HTML和语音内容
    Args:
        document_id: AILectureDocument主键
    Returns:
        dict: 处理结果
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '开始生成文档大纲'})
        result = orchestrator.process_document_with_first_chapter(document_id)

        if result.get('status') == 'fail':
            return {'status': 'fail', 'error': result.get('error', '大纲生成失败')}

        return result
    except Exception as e:
        return {'status': 'fail', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def process_ai_lecture_chapter_with_style_task(self, chapter_id: int, style_code: str) -> Dict[str, Any]:
    """
    Celery任务：使用指定风格重新生成章节音频内容
    Args:
        chapter_id: AILectureChapter主键
        style_code: 风格代码
    Returns:
        dict: 处理结果
    """
    try:
        self.update_state(state='PROGRESS', meta={
            'status': f'开始使用风格 {style_code} 重新生成音频内容'
        })

        result = orchestrator.regenerate_chapter_audio_with_style(chapter_id, style_code)

        if result.get('status') == 'fail':
            return {'status': 'fail', 'error': result.get('error', '未知错误')}

        return result
    except Exception as e:
        return {'status': 'fail', 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def process_ai_outline_with_first_chapter_gemini_task(self, document_id: int) -> Dict[str, Any]:
    """
            Celery任务：生成大纲并处理第一章Gemini音频
        先生成文档大纲，然后处理第一章的Gemini对话音频
        Args:
            document_id: AILectureDocument主键
        Returns:
            dict: 处理结果
    """
    try:
        self.update_state(state='PROGRESS', meta={'status': '开始生成文档大纲'})
        
        # 1. 先生成大纲
        outline_result = orchestrator.process_document_outline_generation(document_id)
        if outline_result.get('status') == 'fail':
            return {'status': 'fail', 'error': outline_result.get('error', '大纲生成失败')}

        self.update_state(state='PROGRESS', meta={'status': '大纲生成完成，开始处理第一章音频'})

        # 2. 查找第一章并处理
        from ..entitys.ai_lecture import AILectureDocument, AILectureChapter
        
        try:
            document = AILectureDocument.objects.get(id=document_id)
            first_chapter = AILectureChapter.objects.filter(
                document=document,
                deleted_at__isnull=True
            ).order_by('chapter_order').first()

            if not first_chapter:
                error_msg = '文档下没有找到章节'
                return {'status': 'fail', 'error': error_msg}

            # 更新章节状态为生成中
            first_chapter.status = 'gen'
            first_chapter.save()
            logger.info(f"已更新章节状态为生成中: {first_chapter.chapter_title}")

        except AILectureDocument.DoesNotExist:
            error_msg = f'文档 ID {document_id} 不存在'
            return {'status': 'fail', 'error': error_msg}

        # 3. 处理第一章
        try:
            self.update_state(state='PROGRESS', meta={
                'status': f'正在处理第一章: {first_chapter.chapter_title}'
            })

            result = process_chapter_gemini_audio_sync(first_chapter.id)
            
            if result.get('status') == 'fail':
                # 处理失败，将状态重置
                try:
                    first_chapter.refresh_from_db()
                    first_chapter.status = 'not_gen'
                    first_chapter.save()
                    logger.warning(f"处理失败，已重置章节状态: {first_chapter.chapter_title}")
                except Exception as e:
                    logger.error(f"重置章节状态失败: {str(e)}")
                    
                return {'status': 'fail', 'error': result.get('error', '第一章处理失败')}

            # 处理成功，更新状态为已完成
            try:
                first_chapter.refresh_from_db()
                first_chapter.status = 'completed'
                first_chapter.save()
                logger.info(f"处理完成，已更新章节状态为已完成: {first_chapter.chapter_title}")
            except Exception as e:
                logger.error(f"更新章节状态失败: {str(e)}")

            return {
                'status': 'success',
                'message': f'文档 {document.title} 大纲生成完成，第一章音频处理完成',
                'document_id': document_id,
                'document_title': document.title,
                'first_chapter_result': result
            }

        except Exception as e:
            # 处理异常，将状态重置
            try:
                first_chapter.refresh_from_db()
                first_chapter.status = 'not_gen'
                first_chapter.save()
                logger.warning(f"处理异常，已重置章节状态: {first_chapter.chapter_title}")
            except Exception as ex:
                logger.error(f"重置章节状态失败: {str(ex)}")
                
            error_msg = f'处理第一章时出错: {str(e)}'
            logger.error(error_msg)
            return {'status': 'fail', 'error': error_msg}

    except Exception as e:
        error_msg = f"处理文档 {document_id} 时发生未知错误: {str(e)}"
        logger.error(error_msg)
        return {'status': 'fail', 'error': error_msg}


@shared_task(bind=True, max_retries=3)
def process_chapter_gemini_audio_task(self, chapter_id: int) -> Dict[str, Any]:
    """
    Celery任务：只处理指定章节的Gemini音频内容
    Args:
        chapter_id: AILectureChapter主键
    Returns:
        dict: 处理结果
    """
    try:
        from ..entitys.ai_lecture import AILectureChapter
        
        # 获取章节，更新状态为生成中
        try:
            chapter = AILectureChapter.objects.get(id=chapter_id)
            chapter.status = 'gen'
            chapter.save()
            logger.info(f"已更新章节状态为生成中: {chapter.chapter_title}")
        except AILectureChapter.DoesNotExist:
            return {'status': 'fail', 'error': f'章节 ID {chapter_id} 不存在'}
        except Exception as e:
            logger.error(f"更新章节状态失败: {str(e)}")
        
        self.update_state(state='PROGRESS', meta={'status': '开始处理章节Gemini音频'})
        result = process_chapter_gemini_audio_sync(chapter_id)

        if result.get('status') == 'fail':
            # 处理失败，将状态重置
            try:
                chapter.refresh_from_db()
                chapter.status = 'not_gen'
                chapter.save()
                logger.warning(f"处理失败，已重置章节状态: {chapter.chapter_title}")
            except Exception as e:
                logger.error(f"重置章节状态失败: {str(e)}")
                
            return {'status': 'fail', 'error': result.get('error', '未知错误')}

        # 处理成功，更新状态为已完成
        try:
            chapter.refresh_from_db()
            chapter.status = 'completed'
            chapter.save()
            logger.info(f"处理完成，已更新章节状态为已完成: {chapter.chapter_title}")
        except Exception as e:
            logger.error(f"更新章节状态失败: {str(e)}")
            
        return result
    except Exception as e:
        # 尝试将章节状态重置
        try:
            chapter = AILectureChapter.objects.get(id=chapter_id)
            chapter.status = 'not_gen'
            chapter.save()
            logger.warning(f"处理异常，已重置章节状态: {chapter.chapter_title}")
        except Exception:
            pass
            
        return {'status': 'fail', 'error': str(e)}


def process_ai_lecture_chapter(chapter_id: int) -> Dict[str, Any]:
    """
    只处理指定章节的要点、HTML、语音等内容 - 同步版本
    Args:
        chapter_id: AILectureChapter主键
    Returns:
        dict: 处理结果
    """
    try:
        return orchestrator.process_chapter_content_generation(chapter_id)
    except Exception as e:
        logger.error(f"AI讲课章节处理失败(ID: {chapter_id}): {str(e)}")
        return {'status': 'fail', 'error': str(e)}


@retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
def process_chapter_gemini_audio_sync(chapter_id: int) -> Dict[str, Any]:
    """
    只处理指定章节的Gemini音频内容 - 同步版本
    Args:
        chapter_id: AILectureChapter主键
    Returns:
        dict: 处理结果
    """
    temp_dir = None
    
    try:
        from ..entitys.ai_lecture import AILectureChapter
        
        # 定义不同语言的提示词模板
        LANGUAGE_PROMPT_TEMPLATES = {
            'zh': """
            {content_text}
            
            生成真实的人物对话播客音频内容
            主持人名字是Anya和Liam
            输出要求:
            - 直接输出人物对话的讲稿内容
            - 不要有"以下是讲稿"、"根据文档内容"等开头语
            - 不要提起对方名字
            输出格式：
            Anya: xxxx
            Liam: xxxx
            
            请直接生成中文播客内容
            """,
            
            'en': """
            {content_text}
            
            Generate realistic dialogue podcast audio content.
            The hosts are named Anya and Liam.
            Requirements:
            - Output the dialogue script directly
            - Do not include introductions like "Here's the script" or "Based on the document"
            - Do not mention each other by name
            Output format:
            Anya: xxxx
            Liam: xxxx
            
            Please generate the podcast content directly in English.
            """,
            
            'vi': """
            {content_text}
            
            Tạo nội dung âm thanh podcast đối thoại thực tế.
            Tên người dẫn chương trình là Anya và Liam.
            Yêu cầu:
            - Xuất trực tiếp nội dung kịch bản đối thoại
            - Không đưa vào phần giới thiệu như "Đây là kịch bản" hoặc "Dựa trên tài liệu"
            - Không nhắc đến tên của nhau
            Định dạng đầu ra:
            Anya: xxxx
            Liam: xxxx
            
            Vui lòng tạo nội dung podcast trực tiếp bằng tiếng Việt.
            """,
            
            'id': """
            {content_text}
            
            Hasilkan konten audio podcast dialog yang realistis.
            Pembawa acara bernama Anya dan Liam.
            Persyaratan:
            - Keluarkan skrip dialog secara langsung
            - Jangan sertakan pengantar seperti "Berikut adalah skrip" atau "Berdasarkan dokumen"
            - Jangan menyebut nama satu sama lain
            Format output:
            Anya: xxxx
            Liam: xxxx
            
            Hasilkan konten podcast langsung dalam bahasa Indonesia.
            """
        }
        
        # 1. 获取章节信息 (不可重试的错误)
        try:
            chapter = AILectureChapter.objects.get(id=chapter_id)
            
            # 获取章节的语言代码
            language_code = getattr(chapter, 'language_code', None)
            
            # 如果章节没有语言代码，尝试从文档获取
            if not language_code:
                language_code = getattr(chapter.document, 'language_code', 'zh')
            
            # 验证语言代码是否有效
            if language_code not in ['zh', 'en', 'vi', 'id']:
                language_code = 'zh'  # 默认使用中文
                
        except AILectureChapter.DoesNotExist:
            # 这是一个致命错误，不应重试
            logger.error(f'致命错误: 章节 ID {chapter_id} 不存在')
            # 通过Ignore异常告知Celery不要重试此任务
            raise Ignore()

        logger.info(f"开始处理章节Gemini音频: {chapter.chapter_title} (页数: {chapter.start_page}-{chapter.end_page}, 语言: {language_code})")

        # 2. 获取文档内容 (可重试)
        document_content_list = orchestrator.document_processor.get_or_create_document_content(chapter.document)

        # 3. 提取章节页面内容
        chapter_text = orchestrator._extract_text_by_page_range(
            document_content_list, chapter.start_page, chapter.end_page
        )

        if not chapter_text.strip():
            logger.warning(f"章节 {chapter.chapter_title} 没有文本内容，跳过处理")
            # 内容为空是确定的状态，不应重试
            return {'status': 'success', 'message': '章节内容为空，跳过处理'}

        # 4. 使用 Gemini TTS 生成音频并处理后续流程 (可重试)
        # 创建临时目录用于存储生成的音频和脚本文件
        temp_dir = create_temp_subdir(f"gemini_audio_doc{chapter.document.id}_ch{chapter.id}")
        logger.info(f"创建临时目录用于存储音频文件: {temp_dir}")

        # 使用预定义的提示词模板（如果配置中有）
        custom_prompt = None
        if hasattr(chapter, 'custom_prompt') and chapter.custom_prompt:
            # 如果章节有自定义提示词，优先使用
            custom_prompt = chapter.custom_prompt
            logger.info(f"使用章节自定义提示词")
        elif hasattr(chapter.document, 'custom_prompt') and chapter.document.custom_prompt:
            # 如果文档有自定义提示词，其次使用
            custom_prompt = chapter.document.custom_prompt
            logger.info(f"使用文档自定义提示词")
        else:
            # 使用预定义的语言模板
            prompt_template = LANGUAGE_PROMPT_TEMPLATES.get(language_code)
            if prompt_template:
                custom_prompt = prompt_template.format(content_text=chapter_text)
                logger.info(f"使用预定义{language_code}语言模板")

        # 传递语言参数和可能的自定义提示词给音频生成函数
        audio_result = generate_dialogue_and_audio(
            content_text=chapter_text,
            output_dir=temp_dir,
            language=language_code,  # 传递语言参数
            custom_prompt=custom_prompt  # 传递自定义提示词
        )
        script_text = audio_result.get('script_text')

        # 5. 使用编排器处理章节分析结果并生成HTML页面 (如果失败，只记录警告，不影响主流程)
        html_result = None
        try:
            html_result = orchestrator.process_chapter_with_gemini_analysis(
                chapter=chapter,
                chapter_text=chapter_text,
                audio_file_path=audio_result.get('audio_file'),
                script_text=script_text
            )
            logger.info(f"章节 {chapter.chapter_title} HTML页面生成完成")
        except Exception as e:
            logger.warning(f"章节HTML页面生成失败: {str(e)}")

        result = {
            'status': 'success',
            'message': f'章节 {chapter.chapter_title} Gemini音频生成完成',
            'chapter_id': chapter.id,
            'chapter_title': chapter.chapter_title,
            'page_range': f"{chapter.start_page}-{chapter.end_page}",
            'language': language_code,  # 添加语言信息到结果
            'used_custom_prompt': audio_result.get('used_custom_prompt', False),  # 是否使用了自定义提示词
            'audio_file': audio_result.get('audio_file'),
            'script_file': audio_result.get('script_file'),
            'html_pages': html_result.get('saved_pages') if html_result else None,
            'total_pages': html_result.get('total_pages') if html_result else 0,
            'subtitle_count': html_result.get('subtitle_count') if html_result else 0,
            'total_duration': html_result.get('total_duration') if html_result else 0
        }

        logger.info(f"章节 {chapter.chapter_title} Gemini音频生成成功 (语言: {language_code}): {audio_result.get('audio_file')}")
        return result

    except Ignore:
        # 重新抛出Ignore异常，以确保Celery正确处理
        raise
    except Exception as e:
        logger.error(f"处理章节 {chapter_id} 时发生可重试错误: {str(e)}")
        # 抛出异常以触发tenacity重试
        raise e
    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            try:
                clean_temp_dir(temp_dir)
                logger.info(f"已清理临时目录: {temp_dir}")
            except Exception as cleanup_error:
                logger.warning(f"清理临时目录失败: {cleanup_error}")







