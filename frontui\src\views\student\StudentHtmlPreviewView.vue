<template>
  <audio
    ref="audioPlayerRef"
    :src="audios[currentAudioIdx]?.audio_url"
    @ended="onAudioEnded"
    style="display:none"
  />
  <StudentLayout :userName="studentData.name" :userAvatar="studentData.avatar" :pageTitle="documentTitle || '文档预览'" activePage="bookshelf">
    <div class="flex h-full">
      <div class="flex-1 flex flex-col bg-white relative">
        <!-- 顶部按钮组 -->
        <div class="absolute top-4 right-4 z-10 flex items-center">
          <button @click="showChapterDialog = true" class="action-btn">{{ t('htmlPreview.actions.chapterList') }}</button>
          <button @click="openStyleDialog" class="action-btn">{{ t('htmlPreview.actions.switchStyle') }}</button>
          <button @click="goBack" class="action-btn">{{ t('htmlPreview.actions.back') }}</button>
        </div>

        <!-- 章节弹窗 -->
        <el-dialog v-model="showChapterDialog" :title="t('htmlPreview.chapterDialog.title')" width="500px" append-to-body>
          <div v-if="chapterInfo?.length">
            <div v-for="ch in chapterInfo" :key="ch.chapter_id"
                 class="chapter-item"
                 :class="{'active': currentChapter?.chapter_id === ch.chapter_id}"
                 @click="handleJumpToChapter(ch)">
              <div class="flex items-center">
                <span>{{ ch.chapter_title }}</span>
                <span :class="getStatusClass(ch.status)" class="status-badge">
                  {{ getStatusText(ch.status) }}
                </span>
              </div>
              <span class="text-gray-500 text-sm">{{ t('htmlPreview.chapterDialog.page', { start: ch.start_page, end: ch.end_page }) }}</span>
            </div>
          </div>
          <div v-else class="text-gray-400">{{ t('htmlPreview.chapterDialog.noChapters') }}</div>
        </el-dialog>

        <!-- 风格切换弹窗 -->
        <el-dialog v-model="showStyleDialog" :title="t('htmlPreview.styleDialog.title')" width="400px" append-to-body>
          <div v-if="!isLoadingStyles">
            <div class="mb-4 text-gray-600 text-sm">
              {{ t('htmlPreview.styleDialog.currentStyle') }} <span class="font-medium text-blue-600">{{ availableStyles[selectedStyle] || 'Unknown Style' }}</span>
            </div>
            <div class="space-y-2">
              <div v-for="(name, code) in availableStyles" :key="code"
                   class="style-option"
                   :class="{'selected': tempSelectedStyle === code}"
                   @click="tempSelectedStyle = code">
                <div class="flex-1">
                  <div class="font-medium">{{ name }}</div>
                  <div class="text-gray-500 text-sm">{{ getStyleDescription(code) }}</div>
                </div>
                <div v-if="tempSelectedStyle === code" class="text-blue-500">✓</div>
              </div>
            </div>
          </div>
          <div v-else class="loading-text">{{ t('htmlPreview.styleDialog.loading') }}</div>
          
          <template #footer>
            <div class="flex justify-end space-x-2">
              <button @click="showStyleDialog = false" class="btn-secondary">{{ t('htmlPreview.styleDialog.cancel') }}</button>
              <button @click="confirmStyleChange" 
                      :disabled="tempSelectedStyle === selectedStyle || isChangingStyle"
                      class="btn-primary">
                {{ isChangingStyle ? t('htmlPreview.styleDialog.switching') : t('htmlPreview.styleDialog.confirm') }}
              </button>
            </div>
          </template>
        </el-dialog>

        <!-- 内容区域 -->
        <div class="flex-1 flex flex-col relative">
          <!-- 各种状态展示 -->
          <div v-if="loading" class="overlay loading-overlay">
            <div class="text-gray-400 text-lg">{{ t('htmlPreview.status.loading') }}</div>
          </div>
          
          <div v-if="isGenerating && !canPreview" class="overlay generation-overlay">
            <div class="text-lg mb-4">{{ t('htmlPreview.status.generating') }}</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${generatingProgress}%` }"></div>
            </div>
            <div class="text-gray-500">{{ t('htmlPreview.status.pointsGenerated', { count: keyPointsGeneratedCount }) }}</div>
          </div>
          
          <div v-if="isGenerating && canPreview" class="generation-tip">
            <div class="flex flex-col">
              <div class="flex items-center mb-1">
                <span class="text-sm mr-2">{{ t('htmlPreview.status.generating') }}</span>
                <div class="mini-progress-bar">
                  <div class="progress-fill" :style="{ width: `${generatingProgress}%` }"></div>
                </div>
              </div>
              <div class="text-xs text-gray-600">{{ t('htmlPreview.status.pointsGenerated', { count: keyPointsGeneratedCount }) }}</div>
            </div>
          </div>
          
          <div v-if="needsGeneration && !isGenerating" class="overlay">
            <div class="text-lg mb-6">{{ t('htmlPreview.status.notGenerated') }}</div>
            <button @click="triggerGeneration" class="btn-primary-large">{{ t('htmlPreview.status.startGenerate') }}</button>
          </div>
          
          <!-- HTML 内容展示 -->
          <div class="flex-1 flex flex-col relative">
            <div v-if="htmlContent && currentKeyPoint" class="flex-1 relative">
              <iframe ref="previewFrame" class="preview-iframe" :srcdoc="htmlContent" frameborder="0" sandbox="allow-same-origin allow-scripts"></iframe>
              <!-- 字幕浮层 -->
              <!-- 
              <transition name="fade">
                <div v-if="currentSubtitleIdx >= 0 && currentSubtitles[currentSubtitleIdx]" class="subtitle-overlay">
                  <span>{{ currentSubtitles[currentSubtitleIdx].text }}</span>
                </div>
              </transition>
               -->
            </div>
            <div v-else-if="htmlLoading" class="flex-1 flex items-center justify-center">
              <el-icon class="is-loading text-blue-500" :size="40"><Loading /></el-icon>
            </div>
            
            <!-- 控制面板 -->
            <div v-if="totalPagesForPaging > 0" class="control-panel">
              <!-- 播放按钮 -->
              <div class="flex items-center gap-2">
                <button v-if="getPlayButtonState().disabled" :disabled="true" class="btn-disabled">
                  {{ getPlayButtonState().text }}
                </button>
                <button v-else-if="!playing && audios.length > 0" @click="playAudio" class="btn-play">{{ t('htmlPreview.controls.play') }}</button>
                <button v-else-if="playing" @click="pauseAudio" class="btn-pause">{{ t('htmlPreview.controls.pause') }}</button>
              </div>
              
              <!-- 分页控制 -->
              <button :disabled="currentKeyPointIdx <= 0" @click="prevPage" class="btn-nav">{{ t('htmlPreview.controls.prev') }}</button>
              <span class="page-info">{{ getPageInfo() }}</span>
              <button :disabled="currentKeyPointIdx >= totalPagesForPaging - 1" @click="nextPage" class="btn-nav">{{ t('htmlPreview.controls.next') }}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { bookshelfApi } from '@/api/bookshelf'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import studentAvatar from '@/assets/images/avatars/student1.png'
import { ElDialog, ElNotification, ElIcon } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const docId = route.params.docId || route.query.docId
const studentData = ref({ name: '张同学', avatar: studentAvatar })
const previewFrame = ref(null)
const audioPlayerRef = ref(null)
const documentTitle = ref('') // 用于存储文档标题

// ==================== 章节管理 ====================
const chapterInfo = ref([])
const currentChapter = ref(null)
const showChapterDialog = ref(false)

// 风格相关
const selectedStyle = ref('classroom')
const availableStyles = ref({})
const isChangingStyle = ref(false)
const showStyleDialog = ref(false)
const tempSelectedStyle = ref('classroom')
const isLoadingStyles = ref(false)

// 从后端获取风格和描述
const styleDescriptions = ref({})

// 获取风格描述
const getStyleDescription = (code) => {
  if (styleDescriptions.value[code]) {
    return styleDescriptions.value[code];
  }
  return '';
}

// 从API获取所有可用的风格和描述
const fetchAvailableStyles = async () => {
  try {
    isLoadingStyles.value = true;
    const res = await bookshelfApi.getAllSpeechStyles();
    
    // 更新风格列表和描述
    const styles = {};
    const descriptions = {};
    
    Object.keys(res.data).forEach(code => {
      styles[code] = res.data[code].name;
      descriptions[code] = res.data[code].description;
    });
    
    availableStyles.value = styles;
    styleDescriptions.value = descriptions;
    
    // 如果风格列表为空，至少提供一个默认风格
    if (Object.keys(availableStyles.value).length === 0) {
      availableStyles.value = {
        'classroom': '播客风格'
      };
      styleDescriptions.value = {
        'classroom': '标准教学模式，适合课堂讲授'
      };
    }
  } catch (error) {
    console.error('获取风格列表失败:', error);
    // 提供默认值
    availableStyles.value = {
      'classroom': '播客风格'
    };
    styleDescriptions.value = {
      'classroom': '标准教学模式，适合课堂讲授'
    };
  } finally {
    isLoadingStyles.value = false;
  }
};

// 更新大纲生成通知
const updateGeneratingItems = () => {
  const generating = new Set()
  bookshelfItems.value.forEach(item => {
    if (item.outline_status === 'generating') {
      generating.add(item.id)
    }
  })
  generatingOutlineItems.value = generating
}

// 用于更新生成完成的提醒
const showOutlineCompletedNotification = (completedDocIds) => {
  // 显示具体完成的文档名称
  const completedNames = completedDocIds.map(docId => {
    const item = bookshelfItems.value.find(i => i.id === docId)
    return item ? item.name : `文档${docId}`
  }).join('、')
  
  ElMessage({
    type: 'success',
    message: t('htmlPreview.notifications.outlineCompleted', { names: completedNames }),
    duration: 4000
  })
}

const fetchChapters = async () => {
  try {
    const res = await bookshelfApi.getChapters(docId)
    chapterInfo.value = res.data
    return res.data
  } catch (error) {
    ElNotification({
      title: t('htmlPreview.notifications.chapterFetchFailed'),
      message: error.message || t('htmlPreview.notifications.retryLater'),
      type: 'error',
    })
    return []
  }
}

const checkChapterStatus = async (chapterId) => {
  try {
    const res = await bookshelfApi.get(`/bookshelf/${docId}/chapter/${chapterId}/status/`)
    return res.data
  } catch (error) {
    console.error('检查章节状态失败', error)
    return { status: 'error', key_points_count: 0 }
  }
}

const triggerChapterGeneration = async (chapterId) => {
  try {
    const res = await bookshelfApi.post(`/bookshelf/${docId}/chapter/${chapterId}/generate/`)
    return res
  } catch (error) {
    console.error('触发章节生成失败', error)
    throw error
  }
}

// ==================== 音频播放 ====================
const audios = ref([])
const currentAudioIdx = ref(0)
const playing = ref(false)
const currentSubtitles = ref([])
const currentSubtitleIdx = ref(-1)

const audioCheckTimer = ref(null)
const lastAudioCheckKey = ref(null)
const audioCheckCount = ref(0)

const startAudioStatusChecking = (keyPointId) => {
  stopAudioStatusChecking()
  
  if (!keyPointId) return
  
  lastAudioCheckKey.value = keyPointId
  audioCheckCount.value = 0
  
  audioCheckTimer.value = setInterval(async () => {
    if (audioCheckCount.value >= 10) {
      stopAudioStatusChecking()
      return
    }
    
    audioCheckCount.value++
    console.log(`检查音频状态，第 ${audioCheckCount.value} 次: ${keyPointId}`)
    
    if (currentKeyPoint.value?.key_point_id === lastAudioCheckKey.value) {
      try {
        const res = await bookshelfApi.getKeyPointAudios(docId, keyPointId)
        const newAudios = res.data || []
        
        if (newAudios.length > 0 && 
           (audios.value.length === 0 || 
            JSON.stringify(newAudios) !== JSON.stringify(audios.value))) {
          
          console.log('检测到新音频数据，更新中...')
          audios.value = newAudios
          currentAudioIdx.value = 0
          ElNotification({
            title: t('htmlPreview.notifications.audioUpdated'),
            message: t('htmlPreview.notifications.audioUpdateDetail'),
            type: 'success',
            duration: 3000,
          })
          stopAudioStatusChecking()
        }
      } catch (error) {
        console.error('检查音频状态失败', error)
      }
    } else {
      stopAudioStatusChecking()
    }
  }, 5000)
}

const stopAudioStatusChecking = () => {
  if (audioCheckTimer.value) {
    clearInterval(audioCheckTimer.value)
    audioCheckTimer.value = null
  }
}

const fetchAudios = async (keyPointId, preservePlayingState = false) => {
  console.log('fetchAudios: 获取音频，keyPointId:', keyPointId, 'preservePlayingState:', preservePlayingState)
  
  const wasPlaying = playing.value
  const currentAudio = audioPlayerRef.value
  
  // Reset states if not preserving
  if (!preservePlayingState) {
    audios.value = []
    currentAudioIdx.value = 0
    currentSubtitles.value = []
    currentSubtitleIdx.value = -1
  }
  
  if (!keyPointId) {
    console.log('fetchAudios: keyPointId为空，跳过')
    return
  }
  
  try {
    const res = await bookshelfApi.getKeyPointAudios(docId, keyPointId)
    const newAudios = res.data || []
    console.log('fetchAudios: 获取音频成功，数量:', newAudios.length)
    
    // Smooth transition logic for when paging while playing
    if (preservePlayingState && wasPlaying && newAudios.length > 0) {
      audios.value = newAudios
      currentAudioIdx.value = 0
      
      await nextTick()
      if (currentAudio) {
        currentAudio.src = newAudios[0].audio_url
        try {
          await currentAudio.play()
          playing.value = true
          if (newAudios[0].speech_id) {
            await loadSubtitles(newAudios[0].speech_id)
          }
          console.log('fetchAudios: 平滑切换播放成功')
        } catch (e) {
          playing.value = false
          console.error('fetchAudios: 播放失败', e)
        }
      }
    } else {
      // Standard logic for initial load or paging while paused
      audios.value = newAudios
      currentAudioIdx.value = 0
      
      // Proactively load subtitles for the first audio track
      if (newAudios.length > 0 && newAudios[0].speech_id) {
        await loadSubtitles(newAudios[0].speech_id);
      } else {
        currentSubtitles.value = [];
        currentSubtitleIdx.value = -1;
      }
      
      // Stop playing if the new page has no audio
      if (preservePlayingState && wasPlaying && newAudios.length === 0) {
        playing.value = false
        console.log('fetchAudios: 新页面无音频，停止播放')
      }
    }
    
    // If no audios found, start polling for them
    if (newAudios.length === 0) {
      console.log('未找到音频，开始定期检查更新...')
      startAudioStatusChecking(keyPointId)
    }
  } catch (error) {
    console.error('获取音频失败', error)
    if (!preservePlayingState) {
      audios.value = []
      currentSubtitles.value = []
    }
  }
}

const loadSubtitles = async (speechId) => {
  if (!speechId) return
  
  try {
    const res = await bookshelfApi.getSpeechSubtitles(docId, speechId)
    currentSubtitles.value = res.data || []
    currentSubtitleIdx.value = -1
    console.log('加载字幕数据成功，数量:', currentSubtitles.value.length)
  } catch (error) {
    console.error('获取字幕失败', error)
    currentSubtitles.value = []
    currentSubtitleIdx.value = -1
  }
}

const playAudio = async () => {
  if (!audios.value.length || !audios.value[currentAudioIdx.value]?.audio_url) {
    console.log('No audio URL to play.');
    return;
  }
  await nextTick()
  const audio = audioPlayerRef.value
  if (audio) {
    const currentAudioInfo = audios.value[currentAudioIdx.value]
    const newAudioUrl = currentAudioInfo.audio_url
    
    // Check if we need to change the audio source.
    // audio.src is always absolute. We assume newAudioUrl from backend is also absolute.
    if (audio.src !== newAudioUrl) {
      audio.src = newAudioUrl
      // It's a new track, so load its subtitles
      if (currentAudioInfo.speech_id) {
        await loadSubtitles(currentAudioInfo.speech_id)
      } else {
        currentSubtitles.value = []
        currentSubtitleIdx.value = -1
      }
    }

    try {
      await audio.play()
      playing.value = true
      console.log('播放成功:', audio.src)
      
      // Add event listener for subtitle sync
      // Defensively remove to prevent duplicates, then add.
      audio.removeEventListener('timeupdate', updateSubtitle)
      audio.addEventListener('timeupdate', updateSubtitle)
    } catch (e) {
      playing.value = false
      console.error('播放失败', e)
    }
  } else {
    console.error('audio ref is null')
  }
}

const pauseAudio = () => {
  const audio = audioPlayerRef.value
  if (audio) {
    audio.pause()
    playing.value = false
    // 移除时间更新监听
    audio.removeEventListener('timeupdate', updateSubtitle)
  }
}

const updateSubtitle = () => {
  const audio = audioPlayerRef.value
  if (!audio || !currentSubtitles.value.length) return
  
  const currentTime = audio.currentTime * 1000 // 转换为毫秒
  
  // 查找当前应该显示的字幕
  let foundIdx = -1
  for (let i = 0; i < currentSubtitles.value.length; i++) {
    const subtitle = currentSubtitles.value[i]
    if (currentTime >= subtitle.start_time && currentTime <= subtitle.end_time) {
      foundIdx = i
      break
    }
  }
  
  // 如果找到了字幕，并且不是当前显示的字幕，则更新
  if (foundIdx !== -1 && foundIdx !== currentSubtitleIdx.value) {
    currentSubtitleIdx.value = foundIdx
  } else if (foundIdx === -1 && currentSubtitleIdx.value !== -1) {
    // 如果当前没有字幕应该显示，但有字幕在显示，则清除
    currentSubtitleIdx.value = -1
  }
}

const handleAudioEnded = async (onAutoPage) => {
  const audio = audioPlayerRef.value
  // 移除时间更新监听
  if (audio) {
    audio.removeEventListener('timeupdate', updateSubtitle)
  }
  
  currentSubtitles.value = []
  currentSubtitleIdx.value = -1
  
  if (currentAudioIdx.value < audios.value.length - 1) {
    // 当前页面还有更多音频
    currentAudioIdx.value++
    await playAudio()
  } else {
    // 当前页面音频播放完毕，尝试自动翻页
    if (onAutoPage) {
      onAutoPage()
    } else {
      playing.value = false
    }
  }
}

const onAudioEnded = () => {
  handleAudioEnded(() => {
    if (currentKeyPointIdx.value < totalPagesForPaging.value - 1) {
      ElNotification({
        title: t('htmlPreview.notifications.autoPaging'),
        message: t('htmlPreview.notifications.pagingMessage', { page: currentKeyPointIdx.value + 2 }),
        type: 'info',
        duration: 2000,
      })
      currentKeyPointIdx.value++
      
      // 添加延迟确保字幕加载完成后再播放
      setTimeout(async () => {
        if (audios.value.length > 0 && audios.value[0].speech_id) {
          // 确保字幕已加载
          await loadSubtitles(audios.value[0].speech_id)
          console.log('自动翻页后确保字幕已加载:', currentSubtitles.value.length)
          if (playing.value) {
            await playAudio()
          }
        }
      }, 500)
    } else {
      playing.value = false
      ElNotification({
        title: t('htmlPreview.notifications.playCompleted'),
        message: t('htmlPreview.notifications.playCompletedDetail'),
        type: 'success',
        duration: 3000,
      })
    }
  })
}

// ==================== 内容生成 ====================
const isGenerating = ref(false)
const needsGeneration = ref(false)
const canPreview = ref(false)
const generatingStatus = ref('正在生成章节内容...')
const generatingProgress = ref(0)
const keyPointsGeneratedCount = ref(0)
const totalPointsEstimate = ref(10)
const pollTimer = ref(null)

// 更新完成时的通知
const completeGeneration = async (chapterId) => {
  stopPolling()
  generatingProgress.value = 100
  generatingStatus.value = t('htmlPreview.status.completed')
  
  setTimeout(async () => {
    isGenerating.value = false
    if (currentChapter.value) {
      currentChapter.value.status = 'completed'
    }
    await fetchKeyPoints(chapterId, false)
    ElNotification({
      title: t('htmlPreview.notifications.generationCompleted'),
      message: t('htmlPreview.notifications.allContentGenerated'),
      type: 'success',
    })
  }, 1000)
}

// 更新canPreview通知
const startPolling = (chapterId) => {
  stopPolling()
  
  pollTimer.value = setInterval(async () => {
    const statusData = await checkChapterStatus(chapterId)
    const prevPointsCount = keyPointsGeneratedCount.value
    keyPointsGeneratedCount.value = statusData.key_points_count
    const hasNewPoints = keyPointsGeneratedCount.value > prevPointsCount
    
    // 检查是否可以开始预览
    if (!canPreview.value && keyPointsGeneratedCount.value > 0) {
      await fetchKeyPoints(chapterId, true)
      // 检查第一个要点是否可以预览
      if (keyPoints.value.length > 0) {
        const firstKp = keyPoints.value[0]
        try {
          const htmlRes = await bookshelfApi.getHtmlContent(docId, { keyPointId: firstKp.key_point_id })
          if (htmlRes.data.html && firstKp.audio_status !== 'not_gen') {
            canPreview.value = true
            currentKeyPointIdx.value = 0
            htmlContent.value = createHtmlDocument(htmlRes.data.html)
            await fetchAudios(firstKp.key_point_id)
            ElNotification({
              title: t('htmlPreview.notifications.firstPointGenerated'),
              message: t('htmlPreview.notifications.firstPointDetail'),
              type: 'success',
            })
          }
        } catch (e) {
          console.error("检查第一个要点内容失败:", e)
        }
      }
    } else if (canPreview.value && hasNewPoints) {
      await fetchKeyPoints(chapterId, false)
    }
    
    // 计算进度
    updateProgress()
    
    // 检查是否完成
    if (statusData.status === 'completed') {
      completeGeneration(chapterId)
    }
  }, 2000)
}

const stopPolling = () => {
  if (pollTimer.value) {
    clearInterval(pollTimer.value)
    pollTimer.value = null
  }
}

const updateProgress = () => {
  if (keyPointsGeneratedCount.value > 0) {
    generatingProgress.value = Math.min(
      Math.round((keyPointsGeneratedCount.value / totalPointsEstimate.value) * 100), 
      95
    )
    if (keyPointsGeneratedCount.value > totalPointsEstimate.value * 0.8) {
      totalPointsEstimate.value = Math.max(
        totalPointsEstimate.value, 
        Math.ceil(keyPointsGeneratedCount.value * 1.2)
      )
    }
  } else {
    generatingProgress.value = 5
  }
}

const resetGenerationState = () => {
  isGenerating.value = false
  needsGeneration.value = false
  canPreview.value = false
  generatingProgress.value = 0
  keyPointsGeneratedCount.value = 0
  generatingStatus.value = '正在生成章节内容...'
  stopPolling()
}

// ==================== 其他状态 ====================
const keyPoints = ref([])
const currentKeyPointIdx = ref(0)
const htmlContent = ref('')
const htmlLoading = ref(false)

// ==================== 计算属性 ====================
const currentKeyPoint = computed(() => keyPoints.value[currentKeyPointIdx.value] || null)

const totalPagesForPaging = computed(() => {
  if (isGenerating.value && canPreview.value) {
    return keyPointsGeneratedCount.value
  } else if (!isGenerating.value && keyPoints.value.length > 0) {
    return keyPoints.value.length
  }
  return 0
})

// ==================== 核心功能函数 ====================
const fetchKeyPoints = async (chapterId, resetIndex = true) => {
  const res = await bookshelfApi.getChapterPages(docId, chapterId)
  const newKeyPoints = res.data || []
  
  if (resetIndex || newKeyPoints.length === 0) {
    currentKeyPointIdx.value = 0
  } else {
    currentKeyPointIdx.value = Math.min(currentKeyPointIdx.value, Math.max(0, newKeyPoints.length - 1))
  }
  keyPoints.value = newKeyPoints
}

const fetchHtml = async () => {
  htmlLoading.value = true
  
  if (!currentKeyPoint.value) {
    htmlContent.value = ''
    htmlLoading.value = false
    return
  }
  
  try {
    const res = await bookshelfApi.getHtmlContent(docId, { keyPointId: currentKeyPoint.value.key_point_id })
    htmlContent.value = createHtmlDocument(res.data.html)
  } catch (e) {
    htmlContent.value = createHtmlDocument('<div style="color:red">加载失败</div>')
  } finally {
    htmlLoading.value = false
  }
}

const createHtmlDocument = (content) => `
  <!DOCTYPE html>
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body { margin: 0; padding: 20px; font-family: system-ui, -apple-system, sans-serif; }
        img { max-width: 100%; height: auto; }
      </style>
    </head>
    <body>${content}</body>
  </html>
`

// ==================== 风格管理 ====================
const openStyleDialog = () => {
  tempSelectedStyle.value = selectedStyle.value
  showStyleDialog.value = true
}

const confirmStyleChange = async () => {
  if (!currentChapter.value || tempSelectedStyle.value === selectedStyle.value) {
    showStyleDialog.value = false
    return
  }
  
  try {
    isChangingStyle.value = true
    await bookshelfApi.updateChapterStyle(docId, currentChapter.value.chapter_id, tempSelectedStyle.value)
    selectedStyle.value = tempSelectedStyle.value
    showStyleDialog.value = false
    
    ElNotification({
      title: t('htmlPreview.notifications.styleChanged'),
      message: t('htmlPreview.notifications.styleChangedDetail', { style: availableStyles.value[tempSelectedStyle.value] }),
      type: 'success',
      duration: 3000,
    })
    
    setTimeout(() => {
      window.location.reload()
    }, 1500)
    
  } catch (error) {
    ElNotification({
      title: t('htmlPreview.notifications.styleChangeFailed'),
      message: t('htmlPreview.notifications.retryLater'),
      type: 'error',
    })
  } finally {
    isChangingStyle.value = false
  }
}

// ==================== 工具函数 ====================
const getStatusClass = (status) => ({
  'bg-gray-100 text-gray-500': status === 'not_gen',
  'bg-blue-100 text-blue-500': status === 'gen',
  'bg-green-100 text-green-500': status === 'completed'
})

// 状态相关函数
const getStatusText = (status) => {
  const statusMap = {
    'not_gen': t('htmlPreview.status.notGenerated'),
    'gen': t('htmlPreview.status.generating'),
    'completed': t('htmlPreview.status.completed')
  };
  return statusMap[status] || status;
}

const getPlayButtonState = () => {
  if (!currentKeyPoint.value) return { disabled: true, text: t('htmlPreview.controls.noAudio') };
  if (currentKeyPoint.value.audio_status === 'gen') return { disabled: true, text: t('htmlPreview.controls.generating') };
  if (currentKeyPoint.value.audio_status === 'not_gen') return { disabled: true, text: t('htmlPreview.controls.noAudio') };
  return { disabled: false };
}

const getPageInfo = () => {
  if (currentKeyPoint.value && totalPagesForPaging.value > 0) {
    return t('htmlPreview.controls.page', { current: currentKeyPointIdx.value + 1, total: totalPagesForPaging.value });
  } else if (totalPagesForPaging.value === 0 && (isGenerating.value || needsGeneration.value)) {
    return t('htmlPreview.controls.generating');
  }
  return '-';
}

// ==================== 事件处理 ====================
const handleJumpToChapter = async (chapter) => {
  currentChapter.value = chapter
  showChapterDialog.value = false
  resetGenerationState()
  
  // 重置状态
  keyPoints.value = []
  htmlContent.value = ''
  audios.value = []
  currentKeyPointIdx.value = 0
  currentAudioIdx.value = 0
  playing.value = false
  
  // 检查章节状态
  const statusData = await checkChapterStatus(chapter.chapter_id)
  
  if (statusData.status === 'completed') {
    needsGeneration.value = false
    canPreview.value = true
    await fetchKeyPoints(chapter.chapter_id, true)
    if (keyPoints.value.length > 0) {
      await fetchHtml()
      await fetchAudios(currentKeyPoint.value?.key_point_id)
    }
  } else if (statusData.status === 'gen') {
    isGenerating.value = true
    keyPointsGeneratedCount.value = statusData.key_points_count
    startPolling(chapter.chapter_id)
  } else {
    needsGeneration.value = true
  }
}

const triggerGeneration = async () => {
  if (!currentChapter.value) return
  
  try {
    isGenerating.value = true
    needsGeneration.value = false
    await triggerChapterGeneration(currentChapter.value.chapter_id)
    keyPointsGeneratedCount.value = 0
    startPolling(currentChapter.value.chapter_id)
    
    ElNotification({
      title: t('htmlPreview.notifications.generationStarted'),
      message: t('htmlPreview.notifications.generationDetail'),
      type: 'success',
    })
  } catch (error) {
    isGenerating.value = false
    needsGeneration.value = true
    ElNotification({
      title: t('htmlPreview.notifications.generationFailed'),
      message: t('htmlPreview.notifications.triggerFailed'),
      type: 'error',
    })
  }
}

const prevPage = () => {
  if (currentKeyPointIdx.value > 0) {
    currentKeyPointIdx.value--
  }
}

const nextPage = () => {
  if (currentKeyPointIdx.value < totalPagesForPaging.value - 1) {
    currentKeyPointIdx.value++
  }
}

const goBack = () => {
  router.push({ name: 'student-bookshelf' })
}

// ==================== 监听器 ====================
watch(currentKeyPointIdx, async (newIdx, oldIdx) => {
  if ((canPreview.value || (!isGenerating.value && !needsGeneration.value)) && 
      currentKeyPoint.value && newIdx !== oldIdx) {
    await fetchHtml()
    
    if (playing.value) {
      await fetchAudios(currentKeyPoint.value.key_point_id, true)
    } else {
      await fetchAudios(currentKeyPoint.value.key_point_id, false)
      currentAudioIdx.value = 0
    }
  }
})

watch(currentAudioIdx, async () => {
  if (playing.value && audios.value.length) {
    await playAudio()
  }
})

// ==================== 生命周期 ====================
onMounted(async () => {
  // 获取文档信息
  await fetchDocumentInfo();
  
  // 获取可用的风格列表和描述
  await fetchAvailableStyles();
  
  const chapters = await fetchChapters()
  if (chapters.length > 0) {
    await handleJumpToChapter(chapters[0])
  }
})

onBeforeUnmount(() => {
  stopPolling()
  stopAudioStatusChecking()
})

// 获取文档信息
const fetchDocumentInfo = async () => {
  try {
    // 获取文档基本信息，通过获取章节列表时的响应获取文档标题
    const chapters = await bookshelfApi.getChapters(docId)
    if (chapters && chapters.data && chapters.data.length > 0) {
      // 从第一个章节中获取文档标题
      const firstChapter = chapters.data[0]
      if (firstChapter && firstChapter.document_title) {
        documentTitle.value = firstChapter.document_title
        console.log('获取到文档标题:', firstChapter.document_title)
      }
    }
  } catch (error) {
    console.error('获取文档信息失败:', error)
    documentTitle.value = '文档预览' // 设置默认标题
  }
}
</script>

<style scoped>
/* 基础样式 */
.action-btn {
  @apply mr-2 bg-white rounded-full p-2 shadow-md hover:shadow-lg text-blue-500 hover:text-blue-600;
}

.chapter-item {
  @apply flex justify-between items-center py-2 px-2 rounded cursor-pointer hover:bg-gray-100;
}

.chapter-item.active {
  @apply bg-blue-50 text-blue-600;
}

.status-badge {
  @apply ml-2 px-2 py-0.5 text-xs rounded-full;
}

.style-option {
  @apply flex items-center p-3 border rounded cursor-pointer transition-colors border-gray-200 hover:bg-gray-50;
}

.style-option.selected {
  @apply border-blue-500 bg-blue-50;
}

.loading-text {
  @apply text-center py-4 text-gray-400;
}

/* 按钮样式 */
.btn-secondary {
  @apply px-4 py-2 border rounded hover:bg-gray-50;
}

.btn-primary {
  @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary-large {
  @apply px-6 py-3 rounded-lg bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300;
}

.btn-disabled {
  @apply px-4 py-2 rounded border bg-gray-200 text-gray-500 cursor-not-allowed;
}

.btn-play {
  @apply px-4 py-2 rounded border bg-blue-500 text-white hover:bg-blue-600;
}

.btn-pause {
  @apply px-4 py-2 rounded border bg-gray-200 hover:bg-gray-300;
}

.btn-nav {
  @apply px-4 py-2 rounded border bg-gray-50 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 布局样式 */
.overlay {
  @apply absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-20;
}

.loading-overlay {
  @apply flex-col;
}

.generation-overlay {
  @apply flex-col;
}

.generation-tip {
  @apply absolute top-6 left-5 flex items-center bg-blue-50 px-4 py-2 rounded-lg shadow-md border border-blue-200 z-20 max-w-xs;
}

.progress-bar {
  @apply w-64 bg-gray-200 rounded-full h-2.5 mb-4 overflow-hidden;
}

.mini-progress-bar {
  @apply w-20 bg-gray-200 rounded-full h-1.5 overflow-hidden;
}

.progress-fill {
  @apply bg-blue-600 h-full rounded-full;
}

.preview-iframe {
  @apply w-full h-full absolute inset-0;
}

.control-panel {
  @apply flex justify-center items-center py-4 bg-white border-t gap-2;
}

.page-info {
  @apply mx-4;
}

.subtitle-overlay {
  @apply absolute left-0 right-0 bottom-0 z-30 flex justify-center items-end min-h-14 pb-10 pointer-events-none;
}

.subtitle-overlay span {
  @apply bg-black bg-opacity-35 text-white text-2xl px-5 py-1 rounded-2xl shadow-lg max-w-full break-words leading-relaxed pointer-events-none;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style> 