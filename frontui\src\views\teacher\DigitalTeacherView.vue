<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="数字分身"
    activePage="digital-teacher"
  >
    <div>
      <h1 class="text-2xl font-bold text-gray-800 mb-6">数字分身</h1>
      <p class="text-gray-600">此页面将显示教师的数字分身功能，正在开发中...</p>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref } from 'vue'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})
</script> 