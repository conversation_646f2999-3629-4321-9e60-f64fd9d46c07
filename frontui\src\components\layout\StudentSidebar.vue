<template>
  <div class="h-full pt-0 mt-0 flex flex-col bg-white border-r w-full transition-all duration-300" :class="{ 'collapsed': props.isCollapsed }">
    <!-- Logo and App Title -->
    <div class="flex items-center justify-center h-16 px-4 bg-blue-600">
      <router-link to="/student/dashboard" class="flex items-center gap-2">
        <div class="flex items-center justify-center h-8 w-8 rounded-full text-blue-600">
          <img src="/img/zhLogo.png" :alt="t('system.title')" class="h-8 w-8 object-contain rounded-full" />
        </div>
        <span class="font-bold text-xl text-white" :class="{ 'collapsed-hide': props.isCollapsed }">{{ t('system.title') }}</span>
      </router-link>
    </div>

    <!-- Navigation Links -->
    <div class="pt-0 mt-0 flex-1 overflow-y-auto">
      <nav class="px-2 py-4 space-y-1">
        <router-link 
          v-for="link in navLinks" 
          :key="link.id" 
          :to="link.path" 
          class="flex items-center px-2 py-2 text-sm font-medium rounded-md sidebar-link"
          :class="[currentPage === link.id ? 'text-white bg-blue-600' : 'text-gray-600 hover:bg-gray-100', props.isCollapsed ? 'justify-center' : '']"
        >
          <el-icon :class="`text-lg ${props.isCollapsed ? 'mx-auto' : 'mr-3'}`"><component :is="link.icon" /></el-icon>
          <span class="sidebar-link-text" :class="{ 'collapsed-hide': props.isCollapsed }">{{ link.text }}</span>
        </router-link>
      </nav>
    </div>

    <!-- Toggle Button -->
    <div class="p-2 border-t border-gray-200">
      <button class="w-full text-gray-600 hover:text-gray-800 text-sm py-1 flex items-center justify-center sidebar-toggle-btn" @click="toggleSidebar">
        <template v-if="!props.isCollapsed">
          <el-icon class="mr-2"><ArrowLeft /></el-icon>{{ t('sidebar.collapse') }}
        </template>
        <template v-else>
          <el-icon class="mx-auto"><ArrowRight /></el-icon>
        </template>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  House, 
  Reading, 
  Document, 
  DataAnalysis, 
  ChatDotRound, 
  ShoppingCart,
  School,
  ArrowLeft,
  ArrowRight,
  User,
  Search
} from '@element-plus/icons-vue'

const { t } = useI18n()

// Navigation configuration - using computed to make it reactive to language changes
const navLinks = computed(() => [
  { id: 'dashboard', path: '/student/dashboard', icon: 'House', text: t('sidebar.home') },
  { id: 'courses', path: '/student/courses', icon: 'Reading', text: t('sidebar.courses') },
  // { id: 'assignments', path: '/student/assignments', icon: 'Document', text: t('sidebar.assignments') }, // 隐藏
  // { id: 'knowledge-base', path: '/student/knowledge-base', icon: 'DataAnalysis', text: t('sidebar.knowledgeBase') }, // 隐藏
  { id: 'student-today-learn', path: '/student/today-learn', icon: 'Search', text: t('sidebar.aiLecture') },
  // { id: 'ai-assistant', path: '/student/ai-assistant', icon: 'ChatDotRound', text: t('sidebar.aiAssistant') }, // 隐藏
  // { id: 'points-mall', path: '/student/points-mall', icon: 'ShoppingCart', text: t('sidebar.pointsMall') }, // 隐藏
  // { id: 'personal-center', path: '/student/personal-center', icon: 'User', text: t('sidebar.personalCenter') }
])

const route = useRoute()
const props = defineProps({
  isCollapsed: Boolean,
  activePage: String
})
const emit = defineEmits(['toggleCollapse'])

// Get the current page ID from the route or activePage prop
const currentPage = computed(() => {
  // If activePage prop is set to 'meta-ai-search', prioritize it
  if (props.activePage === 'meta-ai-search') {
    return 'meta-ai-search';
  }
  // Otherwise, extract the page ID from the route path (e.g., /student/dashboard -> dashboard)
  const path = route.path
  const segments = path.split('/')
  return segments[segments.length - 1]
})

// Toggle sidebar collapsed state
const toggleSidebar = () => {
  emit('toggleCollapse')
}

onMounted(() => {
  console.log('Sidebar mounted, current page:', currentPage.value)
})
</script>

<style scoped>
.collapsed {
  width: 64px !important; /* 4rem = 64px */
}

/* 收缩时隐藏文字和多余间距 */
.collapsed-hide {
  display: none !important;
}
.sidebar-link {
  transition: padding 0.3s;
}
.collapsed .sidebar-link {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  justify-content: center !important;
}
.sidebar-link-text {
  transition: opacity 0.3s, width 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 