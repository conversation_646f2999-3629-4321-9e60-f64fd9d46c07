# -*- coding: utf-8 -*-
"""
文档处理服务
负责文档解析、格式转换、内容提取等功能
"""

import json
import logging
import os
import subprocess
import time
from typing import List, Tuple

from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from ...entitys.ai_lecture import AILectureDocument
from ...utils.deepseek_api import DeepSeekAPI
from ...utils.file_utils import FileUtils
from ...utils.knowledge_utils import KnowledgeBaseAPI
from ...utils.temp_file_utils import clean_temp_file, clean_temp_dir, get_temp_dir
from .exceptions import DocumentProcessingException

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """文档处理器 - 负责文档的解析、转换和内容提取"""
    
    def __init__(self):
        self.deepseek_api = DeepSeekAPI()
        self.knowledge_api = KnowledgeBaseAPI()
        self.logger = logger
    
    def extract_pdf_cover_image(self, pdf_path: str) -> str:
        """
        提取PDF文档的封面图片
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            str: 封面图片的存储路径
            
        Raises:
            DocumentProcessingException: 提取失败时抛出
        """
        try:
            import fitz  # PyMuPDF
            self.logger.info(f"开始提取PDF封面图片: {pdf_path}")
            temp_dir = get_temp_dir()

            pdf_document = fitz.open(pdf_path)
            if pdf_document.page_count == 0:
                raise DocumentProcessingException(f"PDF {pdf_path} 没有页面，无法提取封面")

            first_page = pdf_document[0]
            zoom_factor = 2.0
            matrix = fitz.Matrix(zoom_factor, zoom_factor)
            pixmap = first_page.get_pixmap(matrix=matrix)

            cover_filename = f"{os.path.splitext(os.path.basename(pdf_path))[0]}_cover.jpg"
            cover_temp_path = os.path.join(temp_dir, cover_filename)

            pixmap.save(cover_temp_path)
            pdf_document.close()

            cover_storage_path = FileUtils.save_image_file(cover_temp_path, "covers")
            clean_temp_file(cover_temp_path)

            self.logger.info(f"封面图片已保存: {cover_storage_path}")
            return cover_storage_path
        except Exception as e:
            error_msg = f"提取PDF封面图片失败: {e}"
            self.logger.error(error_msg)
            raise DocumentProcessingException(error_msg)
    
    def download_document_to_local(self, file_path: str, local_path: str = None) -> str:
        """
        下载文档文件到本地临时目录
        
        Args:
            file_path: 远程文件路径
            local_path: 本地保存路径（可选）
            
        Returns:
            str: 本地文件路径
            
        Raises:
            DocumentProcessingException: 下载失败时抛出
        """
        try:
            temp_dir = get_temp_dir()
            if not local_path:
                local_filename = os.path.basename(file_path)
                local_path = os.path.join(temp_dir, local_filename)
            FileUtils.download_from_oss(file_path, local_path)
            return local_path
        except Exception as e:
            error_msg = f"下载文件失败: {e}"
            self.logger.error(error_msg)
            raise DocumentProcessingException(error_msg)
    
    def get_or_create_document_content(self, document: AILectureDocument) -> List:
        """
        获取或创建文档的内容列表
        
        Args:
            document: AI讲课文档对象
            
        Returns:
            List: 文档内容列表
            
        Raises:
            DocumentProcessingException: 处理失败时抛出
        """
        if not document.content_list_file_path:
            self.logger.info(f"文档未解析content_list，将重新生成")
            content_list, pdf_file, cover_path = self._extract_document_content(document.file_path)
            content_list_storage_path = self._save_content_list_to_storage(content_list, pdf_file)
            
            # 更新文档信息
            document.content_list_file_path = content_list_storage_path
            document.cover_image_path = cover_path
            document.save()
            return content_list
        else:
            # 读取已有的content_list
            try:
                local_path = self.download_document_to_local(document.content_list_file_path)
                with open(local_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                error_msg = f"读取文档内容列表失败: {e}"
                self.logger.error(error_msg)
                raise DocumentProcessingException(error_msg)
    
    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2), retry=retry_if_exception_type(DocumentProcessingException))
    def _extract_document_content(self, file_path: str) -> Tuple[List, str, str]:
        """
        从文档提取内容列表 (带重试机制，最多尝试5次)
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            Tuple[List, str, str]: (内容列表, PDF文件路径, 封面路径)
        """
        pdf_file = None
        try:
            # 转换文档为PDF
            pdf_file = self._convert_document_to_pdf(file_path)
            
            # 上传PDF进行解析
            task_id = self._upload_pdf_for_parsing(pdf_file)
            content_list_json = self._wait_for_parsing_result(task_id)
            content_list = json.loads(content_list_json)
            
            # 提取封面图片
            cover_path = self.extract_pdf_cover_image(pdf_file)
            
            return content_list, pdf_file, cover_path
        except Exception as e:
            error_msg = f"通过接口解析PDF失败: {e}"
            self.logger.error(f"提取文档内容失败: {error_msg}")
            # 抛出DocumentProcessingException以触发重试
            raise DocumentProcessingException(error_msg)
        finally:
            # 清理临时PDF文件
            if pdf_file and os.path.exists(pdf_file):
                # 检查是否为临时文件，避免删除原始文件
                if pdf_file.startswith(get_temp_dir()):
                    try:
                        clean_temp_file(pdf_file)
                        self.logger.info(f"已清理临时PDF文件: {pdf_file}")
                    except Exception as cleanup_error:
                        self.logger.warning(f"清理临时PDF文件失败: {cleanup_error}")
    
    def _convert_document_to_pdf(self, file_path: str) -> str:
        """
        将文档转换为PDF格式
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            str: PDF文件路径
        """
        local_path = self.download_document_to_local(file_path)
        if self._is_word_document(local_path):
            pdf_file = self._convert_word_to_pdf(local_path)
        else:
            pdf_file = local_path
        return pdf_file
    
    def _is_word_document(self, file_path: str) -> bool:
        """检查是否为Word文档"""
        file_extension = file_path.lower().split('.')[-1] if '.' in file_path else ''
        return file_extension in ['doc', 'docx']
    
    def _convert_word_to_pdf(self, word_file_path: str, pdf_output_path: str = None) -> str:
        """
        将Word文档转换为PDF
        
        Args:
            word_file_path: Word文件路径
            pdf_output_path: PDF输出路径（可选）
            
        Returns:
            str: 转换后的PDF文件路径
            
        Raises:
            DocumentProcessingException: 转换失败时抛出
        """
        from ...config import LibreOffice_path
        
        if pdf_output_path is None:
            pdf_output_path = os.path.splitext(word_file_path)[0] + '.pdf'
            
        try:
            self.logger.info(f"开始将Word文档转换为PDF: {word_file_path}")

            libreoffice_executable = self._find_libreoffice_executable()
            if not libreoffice_executable:
                raise DocumentProcessingException("未找到LibreOffice安装，请确认LibreOffice已正确安装")

            self.logger.info(f"使用LibreOffice: {libreoffice_executable}")

            output_directory = os.path.dirname(pdf_output_path)
            if not output_directory:
                output_directory = os.getcwd()

            if not os.path.exists(output_directory):
                os.makedirs(output_directory)

            conversion_command = [
                libreoffice_executable,
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', output_directory,
                word_file_path
            ]

            self.logger.info(f"执行转换命令: {' '.join(conversion_command)}")

            process_result = subprocess.run(
                conversion_command, 
                capture_output=True, 
                text=True, 
                timeout=60
            )

            if process_result.returncode == 0:
                expected_pdf_path = os.path.join(
                    output_directory, 
                    os.path.splitext(os.path.basename(word_file_path))[0] + '.pdf'
                )
                
                if os.path.exists(expected_pdf_path) and expected_pdf_path != pdf_output_path:
                    os.rename(expected_pdf_path, pdf_output_path)

                if os.path.exists(pdf_output_path):
                    self.logger.info(f"Word文档转换完成: {pdf_output_path}")
                    return pdf_output_path
                else:
                    raise DocumentProcessingException(f"PDF文件未生成: {pdf_output_path}")
            else:
                error_message = process_result.stderr if process_result.stderr else process_result.stdout
                raise DocumentProcessingException(f"LibreOffice转换失败: {error_message}")

        except subprocess.TimeoutExpired:
            raise DocumentProcessingException("LibreOffice转换超时")
        except Exception as e:
            error_msg = f"转换Word文档时出错: {e}"
            self.logger.error(error_msg)
            raise DocumentProcessingException(error_msg)
    
    def _find_libreoffice_executable(self) -> str:
        """查找LibreOffice可执行文件"""
        from ...config import LibreOffice_path
        
        possible_paths = [
            LibreOffice_path, 
            'soffice', 
            'libreoffice',
        ]
        
        for path in possible_paths:
            try:
                if os.path.isfile(path):
                    return path
                elif path in ['soffice', 'libreoffice']:
                    result = subprocess.run(
                        [path, '--version'], 
                        capture_output=True, 
                        text=True, 
                        timeout=5
                    )
                    if result.returncode == 0:
                        return path
            except:
                continue
        return None
    
    def _upload_pdf_for_parsing(self, pdf_path: str) -> str:
        """
        上传PDF文件到解析接口，返回任务ID
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            str: 任务ID
        """
        import requests
        from ...config import AI_EDUCATION_TOOL_URL
        
        api_base_url = f"{AI_EDUCATION_TOOL_URL}/api"
        upload_url = f"{api_base_url}/pdf_to_json"
        
        with open(pdf_path, "rb") as pdf_file:
            files = {"file": (os.path.basename(pdf_path), pdf_file, "application/pdf")}
            response = requests.post(upload_url, files=files)
            
        response.raise_for_status()
        response_data = response.json()
        
        if "task_id" not in response_data:
            raise DocumentProcessingException(f"上传PDF失败: {response_data}")
            
        return response_data["task_id"]
    
    def _wait_for_parsing_result(self, task_id: str, timeout: int = 3600, check_interval: int = 2) -> str:
        """
        等待PDF解析结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            str: 解析结果JSON字符串
        """
        import requests
        from ...config import AI_EDUCATION_TOOL_URL
        
        api_base_url = f"{AI_EDUCATION_TOOL_URL}/api"
        status_url = f"{api_base_url}/status"
        
        start_time = time.time()
        while True:
            response = requests.get(status_url, params={"task_id": task_id})
            
            if response.status_code == 404:
                raise DocumentProcessingException("解析任务不存在")
                
            response_data = response.json()
            task_status = response_data.get("status")
            
            if task_status == "done":
                return response_data.get("result")
            elif task_status == "error":
                raise DocumentProcessingException(f"PDF解析失败: {response_data.get('error')}")
            elif time.time() - start_time > timeout:
                raise DocumentProcessingException("PDF解析超时")
                
            time.sleep(check_interval)
    
    def _save_content_list_to_storage(self, content_list: List, pdf_file: str) -> str:
        """
        保存内容列表到存储系统
        
        Args:
            content_list: 内容列表
            pdf_file: PDF文件路径
            
        Returns:
            str: 存储路径
        """
        temp_content_list_path = None
        try:
            temp_dir = get_temp_dir()
            temp_content_list_path = os.path.join(
                temp_dir,
                f"{os.path.splitext(os.path.basename(pdf_file))[0]}_content_list.json"
            )
            
            with open(temp_content_list_path, "w", encoding="utf-8") as f:
                json.dump(content_list, f, ensure_ascii=False, indent=2)
                
            content_list_storage_path = FileUtils.save_json_file(temp_content_list_path)
            
            if temp_content_list_path and os.path.exists(temp_content_list_path):
                clean_temp_file(temp_content_list_path)
                self.logger.info(f"已清理临时content_list文件: {temp_content_list_path}")
            
            return content_list_storage_path
            
        except Exception as e:
            if temp_content_list_path and os.path.exists(temp_content_list_path):
                try:
                    clean_temp_file(temp_content_list_path)
                except Exception as cleanup_error:
                    self.logger.warning(f"清理临时content_list文件失败: {cleanup_error}")
            
            error_msg = f"保存content_list文件失败: {e}"
            self.logger.error(error_msg)
            raise DocumentProcessingException(error_msg)
