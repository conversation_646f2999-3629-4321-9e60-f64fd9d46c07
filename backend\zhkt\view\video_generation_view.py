from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.forms.models import model_to_dict
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from ..services.video_generation_service import VideoGenerationService
from ..utils.file_utils import FileUtils


@method_decorator(csrf_exempt, name='dispatch')
class VideoGenerationViewSet(viewsets.ViewSet):
    """视频生成管理视图集"""
    
    def _format_generated_video(self, video):
        """格式化生成视频数据"""
        data = model_to_dict(video)
        data['video_url'] = FileUtils.get_file_url(video.video_url) if video.video_url else None
        data['cover_image'] = FileUtils.get_file_url(video.cover_image) if video.cover_image else None
        data['uploaded_audio_url'] = FileUtils.get_file_url(video.uploaded_audio_url) if video.uploaded_audio_url else None
        data['create_time'] = video.create_time.isoformat() if video.create_time else None
        data['update_time'] = video.update_time.isoformat() if video.update_time else None
        
        # 转换状态显示
        status_map = {
            'processing': '生成中',
            'success': '已完成',
            'failed': '失败'
        }
        data['status_display'] = status_map.get(video.status, video.status)
        
        # 解析文件夹ID
        data['folder_id'] = int(video.tags) if video.tags and video.tags.isdigit() else None
        
        return data
    
    @action(detail=False, methods=['post'], url_path='generate')
    def generate_video(self, request):
        """
        生成数字人视频
        
        Body Parameters:
        - name: 视频名称（必填）
        - digital_human_id: 数字人ID（必填）
        - input_type: 输入类型（必填，'text' 或 'audio'）
        - text_content: 文本内容（text类型时必填）
        - voice_id: 语音模型ID（text类型时必填）
        - speed: 语速（text类型时可选，默认1.0）
        - pitch: 音调（text类型时可选，默认0）
        - audio: 音频文件（audio类型时必填）
        - folder_id: 文件夹ID（可选）
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            name = request.data.get('name')
            digital_human_id = request.data.get('digital_human_id')
            input_type = request.data.get('input_type')
            text_content = request.data.get('text_content')
            voice_id = request.data.get('voice_id')
            speed = float(request.data.get('speed', 1.0))
            pitch = int(request.data.get('pitch', 0))
            audio_file = request.FILES.get('audio')
            folder_id = request.data.get('folder_id')
            
            # 基本参数验证
            if not name:
                return Response({
                    "code": 400,
                    "message": "视频名称不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not digital_human_id:
                return Response({
                    "code": 400,
                    "message": "数字人ID不能为空",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if input_type not in ['text', 'audio']:
                return Response({
                    "code": 400,
                    "message": "输入类型必须是 'text' 或 'audio'",
                    "data": None
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 文本输入验证
            if input_type == 'text':
                if not text_content:
                    return Response({
                        "code": 400,
                        "message": "文本输入时，文本内容不能为空",
                        "data": None
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if not voice_id:
                    return Response({
                        "code": 400,
                        "message": "文本输入时，语音模型ID不能为空",
                        "data": None
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # 音频上传验证
            if input_type == 'audio':
                if not audio_file:
                    return Response({
                        "code": 400,
                        "message": "音频上传时，音频文件不能为空",
                        "data": None
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # 验证音频文件格式
                if not audio_file.content_type.startswith('audio/'):
                    return Response({
                        "code": 400,
                        "message": "音频文件格式不正确，请上传音频文件",
                        "data": None
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # 调用服务生成视频
            generated_video = VideoGenerationService.generate_video(
                user_id=user_id,
                name=name,
                digital_human_id=int(digital_human_id),
                input_type=input_type,
                text_content=text_content,
                audio_file=audio_file,
                voice_id=int(voice_id) if voice_id else None,
                speed=speed,
                pitch=pitch,
                folder_id=int(folder_id) if folder_id else None
            )
            
            return Response({
                "code": 200,
                "message": "视频生成任务创建成功",
                "data": self._format_generated_video(generated_video)
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"生成视频失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'], url_path='list')
    def get_generated_videos(self, request):
        """
        获取用户生成的视频列表
        
        Query Parameters:
        - skip: 跳过的记录数，默认0
        - limit: 返回的记录数，默认10
        - search: 搜索关键词，可选
        - folder_id: 文件夹ID，可选
        - status: 状态筛选，可选
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            skip = int(request.query_params.get('skip', 0))
            limit = int(request.query_params.get('limit', 10))
            search = request.query_params.get('search', None)
            folder_id = request.query_params.get('folder_id', None)
            status_filter = request.query_params.get('status', None)
            
            # 转换folder_id
            if folder_id is not None and folder_id != '':
                try:
                    folder_id = int(folder_id)
                except ValueError:
                    folder_id = None
            else:
                folder_id = None
            
            total, videos = VideoGenerationService.get_user_generated_videos(
                user_id=user_id,
                skip=skip,
                limit=limit,
                search=search,
                folder_id=folder_id,
                status_filter=status_filter
            )
            
            return Response({
                "code": 200,
                "message": "获取生成视频列表成功",
                "data": {
                    "total": total,
                    "videos": [self._format_generated_video(video) for video in videos],
                    "page": (skip // limit) + 1 if limit > 0 else 1,
                    "size": limit,
                    "pages": (total + limit - 1) // limit if limit > 0 else 1
                }
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"获取生成视频列表失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['put'], url_path='update')
    def update_generated_video(self, request, pk=None):
        """
        更新生成的视频信息
        
        Body Parameters:
        - name: 视频名称，可选
        - folder_id: 文件夹ID，可选
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            name = request.data.get('name')
            folder_id = request.data.get('folder_id')
            
            # 转换folder_id
            if folder_id is not None and folder_id != '':
                try:
                    folder_id = int(folder_id)
                except ValueError:
                    folder_id = None
            
            updated_video = VideoGenerationService.update_generated_video(
                user_id=user_id,
                video_id=pk,
                name=name,
                folder_id=folder_id
            )
            
            if not updated_video:
                return Response({
                    "code": 404,
                    "message": "视频不存在",
                    "data": None
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                "code": 200,
                "message": "视频更新成功",
                "data": self._format_generated_video(updated_video)
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"更新视频失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['delete'], url_path='delete')
    def delete_generated_video(self, request, pk=None):
        """删除生成的视频"""
        try:
            user_id = getattr(request.user, 'id', 1)
            success = VideoGenerationService.delete_generated_video(
                user_id=user_id,
                video_id=pk
            )
            
            if not success:
                return Response({
                    "code": 404,
                    "message": "视频不存在或删除失败",
                    "data": None
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                "code": 200,
                "message": "视频删除成功",
                "data": None
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"删除视频失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'], url_path='statistics')
    def get_video_statistics(self, request):
        """
        获取用户视频统计数据
        """
        try:
            user_id = getattr(request.user, 'id', 1)
            statistics = VideoGenerationService.get_video_statistics(user_id)
            
            return Response({
                "code": 200,
                "message": "获取统计数据成功",
                "data": statistics
            })
            
        except Exception as e:
            return Response({
                "code": 500,
                "message": f"获取统计数据失败: {str(e)}",
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 