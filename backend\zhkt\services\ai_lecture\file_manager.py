# -*- coding: utf-8 -*-
"""
文件管理服务
负责文件的保存、删除、路径管理等功能
"""

import logging
import os
import re
from typing import Tuple

from ...entitys.ai_lecture import (AILectureHtmlContent, AILectureKeyPoint,
                                    AILectureSpeechContent)
from ...utils.file_utils import FileUtils
from ...utils.temp_file_utils import (clean_temp_dir, clean_temp_file,
                                        create_temp_subdir)
from .exceptions import FileOperationException

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器 - 负责AI讲课相关文件的管理"""
    
    def __init__(self):
        self.logger = logger

    def save_html_slide_file(self, html_content: str, chapter_title: str, slide_index: int) -> str:
        """
        保存HTML幻灯片文件并返回存储路径
        
        Args:
            html_content: HTML内容
            chapter_title: 章节标题
            slide_index: 幻灯片索引
            
        Returns:
            str: 文件存储路径
            
        Raises:
            FileOperationException: 保存失败时抛出
        """
        temp_output_dir = None
        temp_html_file_path = None
        
        try:
            # 创建临时输出目录
            temp_output_dir = create_temp_subdir('html_slides')
            
            # 生成安全的文件名
            safe_chapter_name = self._generate_safe_filename(chapter_title)
            temp_html_file_path = os.path.join(
                temp_output_dir, 
                f"{safe_chapter_name}_{str(slide_index)}.html"
            )
            
            # 写入HTML内容到临时文件
            with open(temp_html_file_path, "w", encoding="utf-8") as file:
                file.write(html_content)
            
            # 保存到存储系统并获取路径
            storage_path = FileUtils.save_html_file(temp_html_file_path)
            
            # 清理临时文件
            self._cleanup_temp_files(temp_html_file_path, temp_output_dir)
                
            return storage_path
            
        except Exception as e:
            # 异常情况下确保清理临时文件
            self._cleanup_temp_files(temp_html_file_path, temp_output_dir)
            error_msg = f"保存HTML幻灯片文件失败: {e}"
            self.logger.error(error_msg)
            raise FileOperationException(error_msg)

    def save_audio_speech_file(self, audio_content: bytes, chapter_title: str, 
                             point_index: int, sentence_order: int) -> str:
        """
        保存音频语音文件并返回存储路径
        
        Args:
            audio_content: 音频二进制内容
            chapter_title: 章节标题
            point_index: 要点索引
            sentence_order: 句子顺序
            
        Returns:
            str: 文件存储路径
            
        Raises:
            FileOperationException: 保存失败时抛出
        """
        temp_output_dir = None
        temp_audio_file_path = None
        
        try:
            # 创建临时输出目录
            temp_output_dir = create_temp_subdir('audio_speeches')
            
            # 生成安全的文件名
            safe_chapter_name = self._generate_safe_filename(chapter_title)
            temp_audio_file_path = os.path.join(
                temp_output_dir,
                f"{safe_chapter_name}_{str(point_index)}_speech_{sentence_order}.mp3"
            )
            
            # 写入音频内容到临时文件
            with open(temp_audio_file_path, "wb") as file:
                file.write(audio_content)
            
            # 保存到存储系统并获取路径
            storage_path = FileUtils.save_audio_file(temp_audio_file_path)
            
            # 清理临时文件
            self._cleanup_temp_files(temp_audio_file_path, temp_output_dir)
            
            return storage_path
            
        except Exception as e:
            # 异常情况下确保清理临时文件
            self._cleanup_temp_files(temp_audio_file_path, temp_output_dir)
            error_msg = f"保存音频语音文件失败: {e}"
            self.logger.error(error_msg)
            raise FileOperationException(error_msg)

    def delete_chapter_related_files(self, chapters) -> Tuple[int, int, int]:
        """
        删除章节相关的所有文件（HTML文件和语音文件）
        
        Args:
            chapters: 章节对象列表
            
        Returns:
            Tuple[int, int, int]: (总文件数, 成功删除数, 失败删除数)
        """
        files_to_delete = []

        # 收集所有需要删除的文件路径
        for chapter in chapters:
            key_points = AILectureKeyPoint.objects.filter(chapter=chapter)

            for key_point in key_points:
                # 收集HTML文件路径
                try:
                    html_content = AILectureHtmlContent.objects.get(key_point=key_point)
                    if html_content.html_file_path:
                        files_to_delete.append(html_content.html_file_path)
                except AILectureHtmlContent.DoesNotExist:
                    pass

                # 收集语音文件路径
                speech_contents = AILectureSpeechContent.objects.filter(key_point=key_point)
                for speech_content in speech_contents:
                    if speech_content.audio_file_path:
                        files_to_delete.append(speech_content.audio_file_path)

        # 执行文件删除并统计结果
        total_files = len(files_to_delete)
        success_count = 0
        failed_count = 0

        for file_path in files_to_delete:
            try:
                if self._delete_single_file(file_path):
                    success_count += 1
                    self.logger.info(f"已删除文件: {file_path}")
                else:
                    failed_count += 1
                    self.logger.warning(f"删除文件失败: {file_path}")
            except Exception as e:
                failed_count += 1
                self.logger.error(f"删除文件异常: {file_path}, 错误: {str(e)}")

        if total_files > 0:
            self.logger.info(f"文件删除完成 - 总计: {total_files}, 成功: {success_count}, 失败: {failed_count}")

        return total_files, success_count, failed_count

    def _generate_safe_filename(self, original_name: str) -> str:
        """
        生成安全的文件名，替换特殊字符
        
        Args:
            original_name: 原始文件名
            
        Returns:
            str: 安全的文件名
        """
        # 替换不安全的文件名字符为下划线
        safe_name = re.sub(r'[^\w\-_\. ]', '_', original_name)
        return safe_name

    def _cleanup_temp_files(self, temp_file_path: str = None, temp_dir_path: str = None):
        """
        清理临时文件和目录
        
        Args:
            temp_file_path: 临时文件路径
            temp_dir_path: 临时目录路径
        """
        try:
            if temp_file_path and os.path.exists(temp_file_path):
                clean_temp_file(temp_file_path)
                self.logger.debug(f"已清理临时文件: {temp_file_path}")
                
            if temp_dir_path and os.path.exists(temp_dir_path):
                clean_temp_dir(temp_dir_path)
                self.logger.debug(f"已清理临时目录: {temp_dir_path}")
                
        except Exception as cleanup_error:
            self.logger.warning(f"清理临时文件失败: {cleanup_error}")

    def _delete_single_file(self, file_path: str) -> bool:
        """
        删除单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否删除成功
        """
        try:
            return FileUtils.delete_file(file_path)
        except Exception as e:
            self.logger.error(f"删除文件异常: {file_path}, 错误: {str(e)}")
            return False

    def get_file_download_url(self, file_path: str) -> str:
        """
        获取文件的下载URL
        
        Args:
            file_path: 文件存储路径
            
        Returns:
            str: 下载URL
            
        Raises:
            FileOperationException: 获取失败时抛出
        """
        try:
            # 这里调用FileUtils获取下载URL的方法
            # 具体实现取决于FileUtils的接口设计
            download_url = FileUtils.get_download_url(file_path)
            return download_url
        except Exception as e:
            error_msg = f"获取文件下载URL失败: {e}"
            self.logger.error(error_msg)
            raise FileOperationException(error_msg)

    def validate_file_exists(self, file_path: str) -> bool:
        """
        验证文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否存在
        """
        try:
            # 这里调用FileUtils检查文件是否存在的方法
            return FileUtils.file_exists(file_path)
        except Exception as e:
            self.logger.warning(f"验证文件存在性失败: {file_path}, 错误: {e}")
            return False 