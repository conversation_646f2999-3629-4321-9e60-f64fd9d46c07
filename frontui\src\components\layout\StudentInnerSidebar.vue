<template>
  <div class="inner-sidebar w-56 bg-slate-50 p-4 flex flex-col border-r border-slate-200 flex-shrink-0">
    <nav class="space-y-2">
      <!-- <div>
        <router-link to="/student/ai-learning" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-blue-100 hover:text-blue-700 [&.router-link-exact-active]:bg-blue-600 [&.router-link-exact-active]:text-white [&.router-link-exact-active]:font-semibold">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          {{ t('sidebar.home') }}
        </router-link>
      </div> -->
      <div>
        <router-link to="/student/today-learn" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-blue-100 hover:text-blue-700 [&.router-link-exact-active]:bg-blue-600 [&.router-link-exact-active]:text-white [&.router-link-exact-active]:font-semibold">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 25">
            <path fill="currentColor" d="M22.959 8.055v4.062l.915 1.125-1.77 1.62-1.627-1.62.976-1.146V8.724c-5.227 2.248-6.651 2.961-7.81 3.502-1.16.54-1.993.54-3.152.086-1.14-.454-6.367-2.55-9.092-3.912-1.81-.908-1.933-1.491.04-2.27 2.563-1.015 6.549-2.657 8.787-3.543 1.322-.562 2.033-.865 3.254-.238 2.177.95 6.895 2.896 9.377 3.977 2.156.993.712 1.318.102 1.729m-9.052 5.856c1.261-.54 2.97-1.448 4.841-2.29v6.699S16.348 21 12.097 21c-4.556 0-7.018-2.68-7.018-2.68v-6.267c1.445.605 3.051 1.145 5.004 1.836 1.2.476 2.726.627 3.824.022"></path>
          </svg>
          {{ t('sidebar.aiLecture') }}
        </router-link>
      </div>
      <div>
        <router-link to="/student/bookshelf" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-blue-100 hover:text-blue-700 [&.router-link-exact-active]:bg-blue-600 [&.router-link-exact-active]:text-white [&.router-link-exact-active]:font-semibold">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          {{ t('sidebar.bookshelf') }}
        </router-link>
      </div>
    </nav>
    <div class="flex-grow"></div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
</script>

<style scoped>
.inner-sidebar {
  height: calc(100vh - 64px); /* Assuming header height is 64px */
}
</style> 