# -*- coding: utf-8 -*-
# @Time    : 2025/1/23
# <AUTHOR> lhq
# @File    : fish_speech.py
# @Description :

import os
import logging
from typing import Optional

import requests
from tenacity import retry, stop_after_attempt, wait_fixed

from ..config import AI_EDUCATION_TOOL_URL

logger = logging.getLogger(__name__)


class FishSpeechClient:
    """FishSpeech API客户端 - 通过ai-education-tool API代理"""
    
    def __init__(self):
        """初始化客户端"""
        self.api_base_url = AI_EDUCATION_TOOL_URL
    
    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
    def generate_speech(self, text: str, reference_id: str, speed: float = 1.0, model: str = 'speech-1.6'):
        """
        使用FishSpeech生成音频
        
        Args:
            text: 要转换的文本
            reference_id: 声音克隆参考ID
            speed: 语速倍率，默认1.0
            model: 语音模型，默认speech-1.6
            
        Returns:
            dict: 包含生成结果的字典
        """
        try:
            # 调用API
            response = requests.post(
                f"{self.api_base_url}/api/fish_speech/generate_audio",
                data={
                    "text": text,
                    "reference_id": reference_id,
                    "speed": speed,
                    "model": model
                }
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("status") == "success":
                return result
            else:
                raise Exception(result.get("message", "未知错误"))
                
        except Exception as e:
            logger.error(f"生成音频失败: {str(e)}")
            raise
    
    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
    def local_generate_speech(self, text: str, reference_id: str):
        """
        本地化部署生成音频
        
        Args:
            text: 要转换的文本
            reference_id: 声音克隆参考ID
            
        Returns:
            dict: 包含生成结果的字典
        """
        try:
            # 调用API
            response = requests.post(
                f"{self.api_base_url}/api/fish_speech/local_generate_audio",
                data={
                    "text": text,
                    "reference_id": reference_id
                }
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("status") == "success":
                return result
            else:
                raise Exception(result.get("message", "未知错误"))
                
        except Exception as e:
            logger.error(f"本地生成音频失败: {str(e)}")
            raise
    
    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
    def create_model(self, title: str, description: str, voice_file_path: str, cover_image_path: Optional[str] = None):
        """
        创建FishSpeech模型
        
        Args:
            title: 模型标题
            description: 模型描述
            voice_file_path: 声音文件路径
            cover_image_path: 封面图片文件路径(可选)
            
        Returns:
            dict: 创建的模型信息
        """
        try:
            # 准备文件
            files = {'voice_file': open(voice_file_path, 'rb')}
            if cover_image_path:
                files['cover_image'] = open(cover_image_path, 'rb')
            
            # 准备数据
            data = {
                'title': title,
                'description': description
            }
            
            # 调用API
            response = requests.post(
                f"{self.api_base_url}/api/fish_speech/create_model",
                data=data,
                files=files
            )
            response.raise_for_status()
            result = response.json()
            
            # 关闭文件句柄
            for file_obj in files.values():
                file_obj.close()
            
            if result.get("status") == "success":
                return result.get("model", {})
            else:
                raise Exception(result.get("message", "未知错误"))
                
        except Exception as e:
            logger.error(f"创建模型失败: {str(e)}")
            # 确保文件句柄被关闭
            try:
                for file_obj in files.values():
                    file_obj.close()
            except:
                pass
            raise
    
    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
    def delete_model(self, model_id: str):
        """
        删除FishSpeech模型
        
        Args:
            model_id: 模型ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 调用API
            response = requests.delete(
                f"{self.api_base_url}/api/fish_speech/delete_model/{model_id}"
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("status") == "success":
                return True
            else:
                raise Exception(result.get("message", "未知错误"))
                
        except Exception as e:
            logger.error(f"删除模型失败: {str(e)}")
            raise


# 便捷函数，保持与原来的函数签名兼容

def local_generate_speech(reference_id, speech_script):
    """
    本地化部署克隆音频 (API代理版)
    
    Args:
        reference_id: 参考ID
        speech_script: 语音脚本
        
    Returns:
        bytes: 音频数据
    """
    client = FishSpeechClient()
    result = client.local_generate_speech(speech_script, reference_id)
    
    # 如果需要二进制音频数据
    if result and "audio_base64" in result:
        import base64
        return base64.b64decode(result["audio_base64"])
    
    return None


def generate_fish_speech(text, file_path, reference_id=None, speed=1.0, model='speech-1.6'):
    """
    使用FishSpeech生成音频 (API代理版)
    
    Args:
        text: 要转换的文本
        file_path: 输出文件路径
        reference_id: 声音克隆参考ID
        speed: 语速倍率，默认1.0
        model: 语音模型，默认speech-1.6
    """
    if not reference_id:
        raise ValueError("reference_id is required")
    
    client = FishSpeechClient()
    result = client.generate_speech(text, reference_id, speed, model)
    
    # 保存音频文件
    if result and "audio_base64" in result:
        import base64
        audio_bytes = base64.b64decode(result["audio_base64"])
        
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        
        with open(file_path, "wb") as f:
            f.write(audio_bytes)
            
        return '音频文件生成成功'
    
    return '音频生成失败'


def create_fish_model(title, description, voice_file, cover_image=None):
    """
    创建FishSpeech模型 (API代理版)
    
    Args:
        title: 模型标题
        description: 模型描述
        voice_file: 声音文件路径
        cover_image: 封面图片文件路径(可选)
    
    Returns:
        创建的模型对象
    """
    client = FishSpeechClient()
    return client.create_model(title, description, voice_file, cover_image)


def delete_fish_model(model_id: str):
    """
    删除FishSpeech模型 (API代理版)
    
    Args:
        model_id: 模型ID
    
    Returns:
        bool: 删除是否成功
    """
    client = FishSpeechClient()
    return client.delete_model(model_id)


if __name__ == '__main__':
    # 测试API调用
    # print(generate_fish_speech("哈喽，各位车主朋友们！", "test.wav", "your_reference_id"))
    pass