from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

class AudioModel(models.Model):
    """语音模型实体类"""
    TYPE_CHOICES = (
        ('custom', '自定义'),
        ('system', '系统'),
    )
    
    STATUS_CHOICES = (
        ('processing', '处理中'),
        ('success', '成功'),
        ('failed', '失败'),
    )
    
    name = models.CharField(_('模型名称'), max_length=100)
    description = models.TextField(_('模型描述'), blank=True, null=True)
    type = models.CharField(_('模型类型'), max_length=20, choices=TYPE_CHOICES, default='custom')
    category = models.CharField(_('模型分类'), max_length=50, blank=True, null=True)
    reference_id = models.CharField(_('引用ID'), max_length=255, blank=True, null=True)
    avatar_url = models.Char<PERSON><PERSON>(_('头像URL'), max_length=255, blank=True, null=True)
    audio_url = models.CharField(_('音频URL'), max_length=255, blank=True, null=True)
    clone_audio_url = models.CharField(_('克隆音频URL'), max_length=255, blank=True, null=True)
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='processing')
    usage_count = models.IntegerField(_('使用次数'), default=0)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)
    user = models.ForeignKey(get_user_model(), on_delete=models.CASCADE, related_name='voice_models', 
                           verbose_name=_('所属用户'), null=True, blank=True)

    class Meta:
        verbose_name = _('语音模型')
        verbose_name_plural = _('语音模型')
        db_table = 'zhkt_audio_model'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class AudioGenerationHistory(models.Model):
    """语音生成历史实体类"""
    STATUS_CHOICES = (
        ('processing', '处理中'),
        ('success', '成功'),
        ('failed', '失败'),
    )
    
    name = models.CharField(_('生成名称'), max_length=100)
    text_content = models.TextField(_('文本内容'))
    audio_url = models.CharField(_('音频URL'), max_length=255, blank=True, null=True)
    duration = models.IntegerField(_('音频时长(秒)'), blank=True, null=True)
    voice_model = models.ForeignKey(AudioModel, on_delete=models.CASCADE, related_name='generation_histories',
                                    verbose_name=_('语音模型'))
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='processing')
    speed = models.FloatField(_('语速'), default=1.0)
    pitch = models.IntegerField(_('音调'), default=0)
    power_consumed = models.IntegerField(_('消耗算力'), default=0)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)
    user = models.ForeignKey(get_user_model(), on_delete=models.CASCADE, related_name='voice_generations',
                           verbose_name=_('所属用户'))

    class Meta:
        verbose_name = _('语音生成历史')
        verbose_name_plural = _('语音生成历史')
        db_table = 'zhkt_audio_generation_history'
        ordering = ['-created_at']

    def __str__(self):
        return self.name 