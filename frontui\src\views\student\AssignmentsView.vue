<template>
  <StudentLayout 
    pageTitle="我的作业" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    
    <!-- 作业统计卡片 -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-[200px] p-4 rounded-lg shadow bg-blue-50">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-blue-100 mr-4">
              <span class="material-icons text-blue-600">assignment</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">已提交</p>
              <p class="text-xl font-semibold text-gray-800">
                <el-skeleton :loading="statsLoading" animated>
                  <template #template>
                    <el-skeleton-item variant="text" style="width: 50px" />
                  </template>
                  <template #default>
                    {{ assignmentStats.submitted }}
                  </template>
                </el-skeleton>
              </p>
            </div>
          </div>
        </div>
        <div class="flex-1 min-w-[200px] p-4 rounded-lg shadow bg-green-50">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-green-100 mr-4">
              <span class="material-icons text-green-600">check_circle</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">已批改</p>
              <p class="text-xl font-semibold text-gray-800">
                <el-skeleton :loading="statsLoading" animated>
                  <template #template>
                    <el-skeleton-item variant="text" style="width: 50px" />
                  </template>
                  <template #default>
                    {{ assignmentStats.completed }}
                  </template>
                </el-skeleton>
              </p>
            </div>
          </div>
        </div>
        <div class="flex-1 min-w-[200px] bg-yellow-50 p-4 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-yellow-100 mr-4">
              <span class="material-icons text-yellow-600">hourglass_empty</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">待完成</p>
              <p class="text-xl font-semibold text-gray-800">
                <el-skeleton :loading="statsLoading" animated>
                  <template #template>
                    <el-skeleton-item variant="text" style="width: 50px" />
                  </template>
                  <template #default>
                    {{ assignmentStats.pending }}
                  </template>
                </el-skeleton>
              </p>
            </div>
          </div>
        </div>
        <div class="flex-1 min-w-[200px] bg-red-50 p-4 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-red-100 mr-4">
              <span class="material-icons text-red-600">star</span>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">已逾期</p>
              <p class="text-xl font-semibold text-gray-800">
                <el-skeleton :loading="statsLoading" animated>
                  <template #template>
                    <el-skeleton-item variant="text" style="width: 50px" />
                  </template>
                  <template #default>
                    {{ assignmentStats.overdue }}
                  </template>
                </el-skeleton>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
      <div class="flex items-center justify-between gap-4">
        <div class="flex gap-4">
          <div class="w-[180px]">
            <label for="course" class="block text-sm font-medium text-gray-700 mb-1">课程筛选</label>
            <el-select 
              v-model="selectedCourse" 
              placeholder="全部课程"
              class="w-full"
              :loading="coursesLoading"
            >
              <el-option label="全部课程" value="" />
              <el-option 
                v-for="course in courses" 
                :key="course.id" 
                :label="course.name" 
                :value="course.id" 
              />
            </el-select>
          </div>
          <div class="w-[140px]">
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <el-select 
              v-model="selectedStatus" 
              placeholder="全部状态"
              class="w-full"
              :loading="loading"
            >
              <el-option label="全部状态" value="" />
              <el-option label="待完成" value="pending" />
              <el-option label="已提交" value="submitted" />
              <el-option label="已批改" value="graded" />
              <el-option label="已过期" value="overdue" />
            </el-select>
          </div>
          <div class="w-[160px]">
            <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">排序方式</label>
            <el-select 
              v-model="sortBy" 
              placeholder="排序方式"
              class="w-full"
              :loading="loading"
            >
              <el-option label="截止时间（近到远）" value="dueDateDesc" />
              <el-option label="截止时间（远到近）" value="dueDateAsc" />
              <el-option label="发布时间" value="publish_date" />
              <el-option label="作业名称" value="title" />
            </el-select>
          </div>
        </div>
        <div class="w-[300px]">
          <div class="flex items-center">
            <div class="relative flex-1">
              <el-input
                v-model="searchQuery"
                placeholder="搜索作业"
                :loading="loading"
                @keyup.enter="searchAssignments"
              >
                <template #prefix>
                  <span class="material-icons text-gray-400">search</span>
                </template>
              </el-input>
            </div>
            <el-button 
              type="primary"
              class="ml-3"
              :loading="loading"
              @click="searchAssignments"
            >
              搜索
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签切换 -->
    <div class="border-b border-gray-200 mb-6">
      <nav class="-mb-px flex space-x-8">
        <a 
          v-for="tab in tabs" 
          :key="tab.value"
          href="#" 
          @click.prevent="selectedTab = tab.value"
          :class="[
            selectedTab === tab.value 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
          ]"
        >
          {{ tab.label }}
        </a>
      </nav>
    </div>

    <!-- 作业列表 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <el-table 
        :data="filteredAssignments" 
        style="width: 100%"
        border
        stripe
        v-loading="loading"
      >
        <el-table-column label="作业名称" min-width="200">
          <template #default="{ row }">
            <div class="text-sm font-medium text-gray-900">{{ row.title }}</div>
            <div class="text-xs text-gray-500">{{ row.description }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="所属课程" min-width="150">
          <template #default="{ row }">
            <div class="text-sm text-gray-900">{{ row.courseName }}</div>
            <div class="text-xs text-gray-500">{{ row.collegeName + ' ' +  row.teacherTitle + '[' + row.teacherName+']' }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="发布时间" min-width="120">
          <template #default="{ row }">
            <div class="text-sm text-gray-500">{{ formatDate(row.publish_date) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="截止时间" min-width="120">
          <template #default="{ row }">
            <div class="text-sm text-gray-500">{{ formatDate(row.end_time) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="完成状态" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="成绩" min-width="80">
          <template #default="{ row }">
            <span :class="getScoreClass(row.score)">
              {{ getScoreText(row.score) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" min-width="100" fixed="right">
          <template #default="{ row }">
            <router-link 
              :to="getAssignmentRoute(row)"
              :class="[
                'mr-3',
                row.status === 'overdue'
                  ? 'text-gray-500 hover:text-gray-700 cursor-not-allowed'
                  : 'text-blue-600 hover:text-blue-900'
              ]"
            >
              {{ getActionText(row.status) }}
            </router-link>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-700" v-if="totalItems > 0">
        显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
      </div>
      <div class="text-sm text-gray-700" v-else>
        暂无数据
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalItems"
        layout="total, sizes, prev, pager, next"
        @size-change="handlePageSizeChange"
        @current-change="changePage"
        :loading="loading"
      />
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { formatDate } from '@/utils/date'
import { getStudentAssignments, getStudentStats } from '@/api/homework'
import { ElMessage } from 'element-plus'

const studentStore = useStudentStore()

// 加载状态
const loading = ref(false)
const statsLoading = ref(false)
const coursesLoading = ref(false)

// 作业统计数据
const assignmentStats = ref({
  submitted: 0,
  completed: 0,
  pending: 0,
  overdue: 0
})

// 筛选和搜索
const selectedCourse = ref('')
const selectedStatus = ref('')
const sortBy = ref('dueDateDesc')
const searchQuery = ref('')
const selectedTab = ref('all')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)

// 标签页配置
const tabs = [
  { label: '全部作业', value: 'all' },
  { label: '待完成', value: 'pending' },
  { label: '已提交', value: 'submitted' },
  { label: '迟交', value: 'late' },
  { label: '已批改', value: 'graded' },
  { label: '已逾期', value: 'overdue' }
]

// 课程列表
const courses = computed(() => studentStore.courses)

// 加载课程列表
const loadCourses = async () => {
  try {
    coursesLoading.value = true
    await studentStore.fetchSelectedCourses()
  } catch (error) {
    ElMessage.error('加载课程列表失败')
    console.error('加载课程列表失败:', error)
  } finally {
    coursesLoading.value = false
  }
}

// 作业列表数据
const assignments = ref([])

// 加载作业统计数据
const loadStats = async () => {
  try {
    statsLoading.value = true
    const response = await getStudentStats()
    assignmentStats.value = {
      submitted: response.submitted,
      completed: response.graded,
      pending: response.pending,
      overdue: response.overdue
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败')
    console.error('加载统计数据失败:', error)
  } finally {
    statsLoading.value = false
  }
}

// 加载作业列表
const loadAssignments = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      course_id: selectedCourse.value,
      status: selectedStatus.value,
      sort: sortBy.value,
      search: searchQuery.value
    }
    
    const response = await getStudentAssignments(params)
    // 根据loadCourses()获取的课程列表，将response中的course_id替换为课程名称
    response.forEach(item => {
      item.courseName = courses.value.find(c => String(c.id) === String(item.course))?.name
      item.collegeName = courses.value.find(c => String(c.id) === String(item.course))?.teacher.college_name
      item.teacherTitle = courses.value.find(c => String(c.id) === String(item.course))?.teacher.title
      item.teacherName = courses.value.find(c => String(c.id) === String(item.course))?.teacher.user.alias
    })  
    assignments.value = response
    totalItems.value = response.length // 如果后端支持分页，这里应该使用后端返回的总数
  } catch (error) {
    ElMessage.error('加载作业列表失败')
    console.error('加载作业列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 过滤后的作业列表
const filteredAssignments = computed(() => {
  let result = [...assignments.value]

  // 按标签过滤
  if (selectedTab.value !== 'all') {
    result = result.filter(a => a.status === selectedTab.value)
  }

  // 按课程过滤
  if (selectedCourse.value) {
    result = result.filter(a => a.courseName === courses.value.find(c => String(c.id) === selectedCourse.value)?.title)
  }

  // 按状态过滤
  if (selectedStatus.value) {
    result = result.filter(a => a.status === selectedStatus.value)
  }

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(a => 
      a.title.toLowerCase().includes(query) ||
      a.courseName.toLowerCase().includes(query) ||
      a.teacherName.toLowerCase().includes(query)
    )
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'dueDateDesc':
        return new Date(b.end_time).getTime() - new Date(a.end_time).getTime()
      case 'dueDateAsc':
        return new Date(a.end_time).getTime() - new Date(b.end_time).getTime()
      case 'publish_date':
        return new Date(b.publish_date).getTime() - new Date(a.publish_date).getTime()
      case 'title':
        return a.title.localeCompare(b.title)
      default:
        return 0
    }
  })

  return result
})

// 状态样式和文本
const getStatusType = (status) => {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'submitted':
      return 'info'
    case 'late':
      return 'danger'
    case 'graded':
      return 'success'
    case 'overdue':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待完成'
    case 'submitted':
      return '已提交'
    case 'late':
      return '迟交'
    case 'graded':
      return '已批改'
    case 'overdue':
      return '已逾期'
    default:
      return status
  }
}

// 分数样式和文本
const getScoreClass = (score) => {
  if (score === null) return 'text-gray-500'
  if (score >= 90) return 'text-green-600 font-semibold'
  if (score >= 60) return 'text-blue-600'
  return 'text-red-600'
}

const getScoreText = (score) => {
  if (score === null) return '待批改'
  return score
}

// 操作按钮文本
const getActionText = (status) => {
  switch (status) {
    case 'pending':
      return '开始做题'
    case 'submitted':
    case 'late':
      return '查看提交'
    case 'graded':
      return '查看批改'
    case 'overdue':
      return '已截止'
    default:
      return '查看'
  }
}

// 搜索作业
const searchAssignments = () => {
  currentPage.value = 1
  loadAssignments()
}

// 切换页码
const changePage = (page) => {
  currentPage.value = page
  loadAssignments()
}

// 改变每页显示数量
const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadAssignments()
}

// 监听筛选条件变化
watch([selectedCourse, selectedStatus, sortBy], () => {
  currentPage.value = 1
  loadAssignments()
})

// 页面加载完成时执行
onMounted(async () => {
  if(!studentStore.studentData.id) {
    await studentStore.fetchCurrentStudentInfo()
  }
  await loadCourses()
  loadStats()
  loadAssignments()
})

const getAssignmentRoute = (assignment) => {
  switch (assignment.status) {
    case 'pending':
    case 'submitted':
    case 'late':
      return { name: 'student-assignment-detail', params: { id: assignment.id }}
    case 'graded':
      return { name: 'student-assignment-grading', params: { id: assignment.id }}
    default:
      return { name: 'student-assignment-detail', params: { id: assignment.id }}
  }
}
</script>

<style scoped>
/* 这里可以添加组件样式 */
</style> 