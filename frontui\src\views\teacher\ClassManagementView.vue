<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="班级管理"
    activePage="class-list"
  >
    <div class="space-y-6">
      <!-- 功能区 -->
      <div class="flex flex-wrap justify-between items-center gap-4 mb-4">
        <div class="flex gap-3">
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
            @click="showAddClassModal = true"
          >
            <span class="material-icons text-sm">group_add</span>
            新建班级
          </button>
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_upload</span>
            导入班级
          </button>
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_download</span>
            导出班级
          </button>
        </div>
        <div class="flex gap-3">
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索班级..." 
              class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
              <span class="material-icons text-gray-600">filter_list</span>
              筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <div class="space-y-3">
                <div class="font-medium">院系</div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="dept in ['计算机学院', '软件学院', '电子学院', '机械学院']" 
                    :key="dept"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer"
                    :class="selectedFilters.departments.includes(dept) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('departments', dept)"
                  >
                    {{ dept }}
                  </span>
                </div>
                <div class="font-medium mt-3">年级</div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="grade in ['2023级', '2022级', '2021级', '2020级']" 
                    :key="grade"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer"
                    :class="selectedFilters.grades.includes(grade) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('grades', grade)"
                  >
                    {{ grade }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="flex flex-wrap gap-4">
        <div class="bg-blue-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">总班级数</p>
              <p class="text-2xl font-bold text-gray-800">32</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <span class="material-icons text-blue-600 text-xl">groups</span>
            </div>
          </div>
        </div>
        <div class="bg-green-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">本学期活跃班级</p>
              <p class="text-2xl font-bold text-gray-800">24</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
              <span class="material-icons text-green-600 text-xl">assignment_turned_in</span>
            </div>
          </div>
        </div>
        <div class="bg-purple-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">今日课程数</p>
              <p class="text-2xl font-bold text-gray-800">8</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
              <span class="material-icons text-purple-600 text-xl">today</span>
            </div>
          </div>
        </div>
        <div class="bg-yellow-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">待批阅作业</p>
              <p class="text-2xl font-bold text-gray-800">47</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
              <span class="material-icons text-yellow-600 text-xl">assignment</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类筛选区 -->
      <div class="flex border-b border-gray-200">
        <button 
          v-for="tab in tabs" 
          :key="tab.value"
          @click="currentTab = tab.value"
          class="py-3 px-6 font-medium relative"
          :class="currentTab === tab.value ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'"
        >
          {{ tab.label }}
          <span 
            v-if="currentTab === tab.value" 
            class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"
          ></span>
        </button>
      </div>

      <!-- 班级列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        <div v-for="classItem in paginatedClasses" :key="classItem.id" 
             class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 overflow-hidden">
          <div class="relative">
            <!-- 班级封面图 -->
            <div class="h-32 bg-gradient-to-r"
                 :class="getRandomGradient(classItem.id)">
              <!-- 状态标签 -->
              <div class="absolute top-3 right-3">
                <span class="px-2 py-1 text-xs rounded-full font-medium"
                      :class="getStatusClass(classItem.status)">
                  {{ classItem.status }}
                </span>
              </div>
            </div>
            
            <!-- 班级头像 -->
            <div class="absolute bottom-0 transform translate-y-1/2 left-4">
              <div class="w-14 h-14 rounded-lg bg-white flex items-center justify-center shadow-sm border border-gray-100">
                <span class="text-2xl font-bold text-blue-600">{{ classItem.name.substring(0, 2) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 班级信息 -->
          <div class="pt-10 px-4 pb-4">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-lg font-medium text-gray-900">{{ classItem.name }}</h3>
                <p class="text-sm text-gray-500 mt-1">{{ classItem.department }} · {{ classItem.grade }}</p>
              </div>
              <el-dropdown trigger="click">
                <button class="text-gray-400 hover:text-gray-600">
                  <span class="material-icons">more_vert</span>
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="viewClassDetails(classItem)">查看详情</el-dropdown-item>
                    <el-dropdown-item @click="editClass(classItem)">编辑班级</el-dropdown-item>
                    <el-dropdown-item @click="manageStudents(classItem)">学生管理</el-dropdown-item>
                    <el-dropdown-item @click="assignTasks(classItem)">布置作业</el-dropdown-item>
                    <el-dropdown-item @click="downloadRoster(classItem)">下载花名册</el-dropdown-item>
                    <el-dropdown-item divided class="text-red-500" @click="archiveClass(classItem)">归档班级</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <!-- 班级统计信息 -->
            <div class="mt-4 grid grid-cols-3 gap-2">
              <div class="text-center p-2 rounded-md bg-gray-50">
                <p class="text-xs text-gray-500">学生</p>
                <p class="font-medium text-gray-800">{{ classItem.studentCount }}</p>
              </div>
              <div class="text-center p-2 rounded-md bg-gray-50">
                <p class="text-xs text-gray-500">课程</p>
                <p class="font-medium text-gray-800">{{ classItem.courseCount }}</p>
              </div>
              <div class="text-center p-2 rounded-md bg-gray-50">
                <p class="text-xs text-gray-500">作业</p>
                <p class="font-medium text-gray-800">{{ classItem.assignmentCount }}</p>
              </div>
            </div>
            
            <!-- 最近活动 -->
            <div class="mt-4 pt-3 border-t border-gray-100">
              <div class="flex justify-between items-center mb-2">
                <p class="text-xs text-gray-500">最近活动</p>
                <p class="text-xs text-gray-400">{{ classItem.lastActivity }}</p>
              </div>
              <p class="text-sm text-gray-600 truncate">{{ classItem.latestAction }}</p>
            </div>
            
            <!-- 操作按钮 -->
            <div class="mt-4 flex gap-2">
              <button @click="viewClassDetails(classItem)" 
                      class="flex-1 py-1.5 border border-gray-200 rounded-md text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                查看详情
              </button>
              <button @click="manageStudents(classItem)" 
                      class="flex-1 py-1.5 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-600 hover:bg-blue-100 transition-colors">
                管理学生
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页控件 -->
      <div class="flex justify-between items-center mt-6 pb-6">
        <div class="text-sm text-gray-500">
          显示 {{ (currentPage - 1) * pageSize + 1 }} 至 
          {{ Math.min(currentPage * pageSize, filteredClasses.length) }} 项，
          共 {{ filteredClasses.length }} 项
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="filteredClasses.length"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>

      <!-- 新建班级模态框 -->
      <el-dialog
        v-model="showAddClassModal"
        :title="newClass.isEditing ? '编辑班级' : '新建班级'"
        width="500px"
      >
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">班级名称</label>
            <el-input v-model="newClass.name" placeholder="请输入班级名称" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">所属院系</label>
            <el-select v-model="newClass.department" placeholder="请选择院系" class="w-full">
              <el-option v-for="dept in departments" :key="dept" :label="dept" :value="dept" />
            </el-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">年级</label>
            <el-select v-model="newClass.grade" placeholder="请选择年级" class="w-full">
              <el-option v-for="grade in grades" :key="grade" :label="grade" :value="grade" />
            </el-select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">班级简介</label>
            <el-input v-model="newClass.description" type="textarea" rows="3" placeholder="请输入班级简介" />
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end gap-2">
            <el-button @click="showAddClassModal = false">取消</el-button>
            <el-button type="primary" @click="createClass">{{ newClass.isEditing ? '保存修改' : '创建' }}</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 教师个人数据
const teacherData = ref({
  name: '张老师',
  avatar: '/img/avatars/teacher1.jpg',
})

// 搜索和筛选
const searchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  departments: [],
  grades: [],
})

// 分页
const currentPage = ref(1)
const pageSize = ref(9)

// 标签页
const tabs = [
  { label: '全部班级', value: 'all' },
  { label: '本学期班级', value: 'current' },
  { label: '已归档班级', value: 'archived' },
]
const currentTab = ref('all')

// 模态框
const showAddClassModal = ref(false)
const newClass = ref({
  name: '',
  department: '',
  grade: '',
  description: '',
  isEditing: false
})

// 下拉选项
const departments = ['计算机学院', '软件学院', '电子学院', '机械学院', '管理学院', '经济学院']
const grades = ['2023级', '2022级', '2021级', '2020级']

// 模拟班级数据
const classes = ref([
  {
    id: 1,
    name: '软件工程2021级1班',
    department: '软件学院',
    grade: '2021级',
    status: '进行中',
    studentCount: 42,
    courseCount: 3,
    assignmentCount: 8,
    lastActivity: '今天 14:30',
    latestAction: '布置了《软件工程》课程的第三次作业',
  },
  {
    id: 2,
    name: '计算机科学2022级2班',
    department: '计算机学院',
    grade: '2022级',
    status: '进行中',
    studentCount: 38,
    courseCount: 4,
    assignmentCount: 6,
    lastActivity: '昨天 09:15',
    latestAction: '更新了《数据结构》课程的教学资料',
  },
  {
    id: 3,
    name: '人工智能2022级1班',
    department: '计算机学院',
    grade: '2022级',
    status: '进行中',
    studentCount: 36,
    courseCount: 4,
    assignmentCount: 5,
    lastActivity: '前天 15:45',
    latestAction: '发布了《机器学习》课程的期中考试通知',
  },
  {
    id: 4,
    name: '电子工程2021级3班',
    department: '电子学院',
    grade: '2021级',
    status: '进行中',
    studentCount: 40,
    courseCount: 3,
    assignmentCount: 7,
    lastActivity: '3天前',
    latestAction: '批阅了《模拟电路》课程的实验报告',
  },
  {
    id: 5,
    name: '工业设计2023级1班',
    department: '机械学院',
    grade: '2023级',
    status: '进行中',
    studentCount: 32,
    courseCount: 5,
    assignmentCount: 3,
    lastActivity: '5天前',
    latestAction: '上传了《工业设计基础》课程的示例作品',
  },
  {
    id: 6,
    name: '软件工程2020级2班',
    department: '软件学院',
    grade: '2020级',
    status: '已归档',
    studentCount: 45,
    courseCount: 0,
    assignmentCount: 0,
    lastActivity: '3个月前',
    latestAction: '课程已全部结束，班级已归档',
  },
])

// 根据班级ID生成随机渐变色
const gradients = [
  'from-blue-400 to-blue-600',
  'from-green-400 to-green-600',
  'from-purple-400 to-purple-600',
  'from-orange-400 to-orange-600',
  'from-teal-400 to-teal-600',
  'from-indigo-400 to-indigo-600',
  'from-red-400 to-red-600',
  'from-yellow-400 to-yellow-600',
]

const getRandomGradient = (id) => {
  return gradients[id % gradients.length]
}

// 根据状态获取样式类
const getStatusClass = (status) => {
  switch (status) {
    case '进行中':
      return 'bg-green-100 text-green-800'
    case '已归档':
      return 'bg-gray-100 text-gray-800'
    case '即将开始':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 过滤班级列表
const filteredClasses = computed(() => {
  return classes.value.filter(classItem => {
    // 标签页筛选
    if (currentTab.value === 'current' && classItem.status !== '进行中') return false
    if (currentTab.value === 'archived' && classItem.status !== '已归档') return false
    
    // 搜索筛选
    if (searchQuery.value && !classItem.name.toLowerCase().includes(searchQuery.value.toLowerCase())) return false
    
    // 其他筛选条件
    if (selectedFilters.value.departments.length > 0 && !selectedFilters.value.departments.includes(classItem.department)) return false
    if (selectedFilters.value.grades.length > 0 && !selectedFilters.value.grades.includes(classItem.grade)) return false
    
    return true
  })
})

// 分页后的班级列表
const paginatedClasses = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return filteredClasses.value.slice(startIndex, endIndex)
})

// 切换筛选条件
const toggleFilter = (filterType, value) => {
  if (selectedFilters.value[filterType].includes(value)) {
    selectedFilters.value[filterType] = selectedFilters.value[filterType].filter(v => v !== value)
  } else {
    selectedFilters.value[filterType].push(value)
  }
}

// 页码变化处理
const handlePageChange = (page) => {
  currentPage.value = page
}

// 班级操作方法
const viewClassDetails = (classItem) => {
  router.push(`/teacher/class/${classItem.id}/detail`)
}

const editClass = (classItem) => {
  showAddClassModal.value = true
  newClass.value = {
    name: classItem.name,
    department: classItem.department,
    grade: classItem.grade,
    description: classItem.description || '',
    id: classItem.id,  // 保存ID用于更新
    isEditing: true    // 标记为编辑模式
  }
}

const manageStudents = (classItem) => {
  router.push(`/teacher/class/${classItem.id}/students`)
}

const assignTasks = (classItem) => {
  router.push(`/teacher/class/${classItem.id}/assignments/create`)
}

const downloadRoster = (classItem) => {
  ElMessage.success(`正在下载 ${classItem.name} 的花名册`)
  // 模拟下载文件
  setTimeout(() => {
    const link = document.createElement('a')
    link.href = '#'
    link.setAttribute('download', `${classItem.name}-学生花名册.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, 1000)
}

const archiveClass = (classItem) => {
  ElMessageBox.confirm(`确认要归档班级 ${classItem.name} 吗？归档后班级将不再显示在活跃班级列表中。`, '归档确认', {
    confirmButtonText: '确认归档',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 更新班级状态
    const classIndex = classes.value.findIndex(c => c.id === classItem.id)
    if (classIndex !== -1) {
      classes.value[classIndex].status = '已归档'
      ElMessage.success(`班级 ${classItem.name} 已成功归档`)
    }
  }).catch(() => {})
}

// 创建新班级
const createClass = () => {
  if (!newClass.value.name || !newClass.value.department || !newClass.value.grade) {
    ElMessage.warning('请填写完整的班级信息')
    return
  }
  
  if (newClass.value.isEditing) {
    // 编辑现有班级
    const classIndex = classes.value.findIndex(c => c.id === newClass.value.id)
    if (classIndex !== -1) {
      // 保持原有的其他属性不变，只更新表单中的字段
      const updatedClass = {
        ...classes.value[classIndex],
        name: newClass.value.name,
        department: newClass.value.department,
        grade: newClass.value.grade,
        description: newClass.value.description
      }
      
      classes.value[classIndex] = updatedClass
      ElMessage.success('班级信息更新成功')
    }
  } else {
    // 添加新班级
    const newId = classes.value.length + 1
    classes.value.push({
      id: newId,
      name: newClass.value.name,
      department: newClass.value.department,
      grade: newClass.value.grade,
      status: '进行中',
      studentCount: 0,
      courseCount: 0,
      assignmentCount: 0,
      lastActivity: '刚刚',
      latestAction: '新建了班级',
      description: newClass.value.description
    })
    ElMessage.success('班级创建成功')
  }
  
  showAddClassModal.value = false
  
  // 重置表单
  newClass.value = {
    name: '',
    department: '',
    grade: '',
    description: '',
    isEditing: false
  }
}

// 生命周期钩子
onMounted(() => {
  // 可以在这里添加获取班级数据的API调用
  console.log('班级管理页面已加载')
})
</script>

<style scoped>
.el-dropdown-menu {
  min-width: 120px;
}
</style> 