from .user import User, Role, Student, Teacher, Admin
from .organization import College, Major, ClassGroup
from .course import Course, Chapter, Lesson, Resource, Note, CourseRating, CourseOverview
from .homework import Homework, Submission, Feedback, HomeworkQuestion
from .points import PointsRecord, Product, Order
from .ai_assistant import AIChat, AIChatMessage, AIPrompt
from .system import Dept, Menu
from .knowledge import KnowledgeCategory, KnowledgeDataset, KnowledgeDocument
from .ppt import PPTProject, Subject
from .lesson_plan import LessonPlan
from .comment import Comment
from .ai_lecture import (
    AILectureDocument, AILectureChapter, AILectureKeyPoint,
    AILectureHtmlContent, AILectureSpeechContent, AILectureSpeechStyle
)
from .question import Question, Option
from .digital_human import DigitalHuman
from .generated_video import GeneratedVideo

__all__ = [
    'User', 'Role', 'Student', 'Teacher', 'Admin',
    'College', 'Major', 'ClassGroup',
    'Course', 'Chapter', 'Lesson', 'Resource',
    'Homework', 'Submission', 'Feedback',
    'PointsRecord', 'Product', 'Order',
    'AIChat', 'AIChatMessage', 'AIPrompt',
    'Dept', 'Menu',
    'KnowledgeCategory', 'KnowledgeDataset', 'KnowledgeDocument', # 知识库
    'PPTProject', 'Subject', # PPT项目
    'LessonPlan', # 教学设计
    'Note', 'CourseRating', 'Comment', # 学习笔记
    'CourseOverview', # 课程概述
    'AILectureDocument', 'AILectureChapter', 'AILectureKeyPoint',
    'AILectureHtmlContent', 'AILectureSpeechContent','AILectureSpeechStyle',
    'Question', 'Option', 'HomeworkQuestion',
    'DigitalHuman',  # 数字人
    'GeneratedVideo'  # 生成视频
]