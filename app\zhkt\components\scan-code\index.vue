<template>
  <view class="scan-code">
    <!-- 扫码区域 -->
    <camera 
      class="camera"
      mode="scanCode"
      :flash="flash"
      @scancode="handleScanCode"
      @error="handleError"
    >
      <!-- 扫码框 -->
      <view class="scan-box">
        <view class="corner top-left"></view>
        <view class="corner top-right"></view>
        <view class="corner bottom-left"></view>
        <view class="corner bottom-right"></view>
        <view class="scan-line"></view>
      </view>

      <!-- 提示文本 -->
      <view class="tip-text">将二维码/条码放入框内，即可自动扫描</view>

      <!-- 操作按钮 -->
      <view class="actions">
        <view class="action-item" @tap="toggleFlash">
          <uni-icons 
            :type="flash === 'on' ? 'eye-filled' : 'eye'"
            size="24"
            color="#fff"
          ></uni-icons>
          <text>{{ flash === 'on' ? '关闭' : '打开' }}闪光灯</text>
        </view>
        <view class="action-item" @tap="selectImage">
          <uni-icons type="image" size="24" color="#fff"></uni-icons>
          <text>从相册选择</text>
        </view>
      </view>
    </camera>
  </view>
</template>

<script>
export default {
  name: 'ScanCode',

  data() {
    return {
      flash: 'off', // 闪光灯状态
      isScanning: false // 是否正在扫码
    }
  },

  methods: {
    // 处理扫码结果
    handleScanCode(e) {
      if (this.isScanning) return
      this.isScanning = true

      const result = e.detail.result
      this.$emit('success', result)

      // 延迟重置状态，防止重复扫码
      setTimeout(() => {
        this.isScanning = false
      }, 1500)
    },

    // 处理扫码错误
    handleError(e) {
      this.$emit('error', e)
      this.$toast.error('扫码失败，请重试')
    },

    // 切换闪光灯
    toggleFlash() {
      this.flash = this.flash === 'on' ? 'off' : 'on'
    },

    // 从相册选择图片
    selectImage() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          // 识别图片中的二维码
          uni.scanCode({
            scanType: ['qrCode', 'barCode'],
            image: res.tempFilePaths[0],
            success: (result) => {
              this.$emit('success', result.result)
            },
            fail: () => {
              this.$toast.error('未识别到有效的码')
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.scan-code {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #000;

  .camera {
    width: 100%;
    height: 100%;
    position: relative;

    .scan-box {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 500rpx;
      height: 500rpx;
      background: transparent;

      .corner {
        position: absolute;
        width: 60rpx;
        height: 60rpx;
        border-color: #007AFF;
        border-style: solid;
        border-width: 8rpx;

        &.top-left {
          left: 0;
          top: 0;
          border-right: none;
          border-bottom: none;
        }

        &.top-right {
          right: 0;
          top: 0;
          border-left: none;
          border-bottom: none;
        }

        &.bottom-left {
          left: 0;
          bottom: 0;
          border-right: none;
          border-top: none;
        }

        &.bottom-right {
          right: 0;
          bottom: 0;
          border-left: none;
          border-top: none;
        }
      }

      .scan-line {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 8rpx;
        background: #007AFF;
        box-shadow: 0 0 10rpx rgba(0, 122, 255, 0.8);
        animation: scan 2s linear infinite;
      }
    }

    .tip-text {
      position: absolute;
      left: 50%;
      top: calc(50% + 300rpx);
      transform: translateX(-50%);
      color: #fff;
      font-size: 28rpx;
      text-align: center;
      width: 100%;
    }

    .actions {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 100rpx;
      display: flex;
      justify-content: space-around;
      padding: 0 100rpx;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        text {
          color: #fff;
          font-size: 24rpx;
          margin-top: 10rpx;
        }
      }
    }
  }
}

@keyframes scan {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(492rpx);
  }
}
</style> 