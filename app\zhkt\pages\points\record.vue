<template>
  <view class="record-container">
    <!-- 兑换记录列表 -->
    <scroll-view 
      class="record-list"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view 
        class="record-item"
        v-for="item in exchangeRecord"
        :key="item.id"
      >
        <view class="item-header">
          <text class="time">{{ formatDate(item.createTime) }}</text>
          <text class="status" :class="item.status">{{ getStatusText(item.status) }}</text>
        </view>
        <view class="item-content">
          <image class="goods-image" :src="item.goodsImage" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="name">{{ item.goodsName }}</text>
            <text class="points">{{ item.points }}积分</text>
          </view>
        </view>
        <view class="item-footer">
          <text class="exchange-code" v-if="item.exchangeCode">
            兑换码：{{ item.exchangeCode }}
          </text>
          <view class="actions">
            <button 
              class="action-btn copy" 
              v-if="item.exchangeCode"
              @tap="copyExchangeCode(item.exchangeCode)"
            >复制兑换码</button>
            <button 
              class="action-btn detail"
              @tap="viewDetail(item)"
            >查看详情</button>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <uni-load-more :status="loadMoreStatus" :content-text="loadMoreText"></uni-load-more>

      <!-- 空状态 -->
      <view class="empty-state" v-if="exchangeRecord.length === 0">
        <image src="/static/images/empty.png" mode="aspectFit"></image>
        <text>暂无兑换记录</text>
        <button class="go-mall" @tap="navigateToMall">去商城逛逛</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { formatDate } from '@/utils/date'

export default {
  data() {
    return {
      page: 1,
      isRefreshing: false,
      loadMoreStatus: 'more',
      loadMoreText: {
        contentdown: '上拉加载更多',
        contentrefresh: '加载中...',
        contentnomore: '没有更多了'
      }
    }
  },

  computed: {
    ...mapState('points', ['exchangeRecord'])
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    ...mapActions('points', ['getExchangeRecord']),

    // 初始化页面
    async initPage() {
      this.loadMoreStatus = 'loading'
      try {
        await this.loadExchangeRecord()
      } catch (error) {
        this.$toast.error('加载失败')
      }
      this.loadMoreStatus = 'more'
    },

    // 加载兑换记录
    async loadExchangeRecord() {
      try {
        await this.getExchangeRecord({
          page: this.page,
          limit: 10
        })
      } catch (error) {
        throw error
      }
    },

    // 下拉刷新
    async onRefresh() {
      this.isRefreshing = true
      this.page = 1
      try {
        await this.initPage()
        this.$toast.success('刷新成功')
      } catch (error) {
        this.$toast.error('刷新失败')
      }
      this.isRefreshing = false
    },

    // 加载更多
    async loadMore() {
      if (this.loadMoreStatus !== 'more') return
      this.loadMoreStatus = 'loading'
      this.page++
      try {
        await this.loadExchangeRecord()
      } catch (error) {
        this.$toast.error('加载失败')
        this.page--
      }
      this.loadMoreStatus = 'more'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: '待使用',
        used: '已使用',
        expired: '已过期'
      }
      return statusMap[status] || status
    },

    // 复制兑换码
    copyExchangeCode(code) {
      uni.setClipboardData({
        data: code,
        success: () => {
          this.$toast.success('复制成功')
        }
      })
    },

    // 查看详情
    viewDetail(item) {
      uni.navigateTo({
        url: `/pages/points/detail?id=${item.goodsId}`
      })
    },

    // 跳转到商城
    navigateToMall() {
      uni.navigateTo({
        url: '/pages/points/mall'
      })
    },
  }
}
</script>

<style lang="scss">
.record-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;

  .record-list {
    height: 100vh;

    .record-item {
      background: #fff;
      border-radius: 10rpx;
      margin-bottom: 20rpx;
      overflow: hidden;

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;
        border-bottom: 1rpx solid #eee;

        .time {
          font-size: 24rpx;
          color: #999;
        }

        .status {
          font-size: 24rpx;

          &.pending {
            color: #ff9900;
          }

          &.used {
            color: #999;
          }

          &.expired {
            color: #ff5a5f;
          }
        }
      }

      .item-content {
        display: flex;
        padding: 20rpx;

        .goods-image {
          width: 160rpx;
          height: 160rpx;
          border-radius: 10rpx;
          margin-right: 20rpx;
        }

        .goods-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .name {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 10rpx;
          }

          .points {
            font-size: 32rpx;
            color: #ff5a5f;
            font-weight: bold;
          }
        }
      }

      .item-footer {
        padding: 20rpx;
        background: #f9f9f9;

        .exchange-code {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 20rpx;
          display: block;
        }

        .actions {
          display: flex;
          justify-content: flex-end;

          .action-btn {
            margin-left: 20rpx;
            font-size: 24rpx;
            padding: 10rpx 30rpx;
            border-radius: 30rpx;
            background: none;

            &.copy {
              color: #007AFF;
              border: 1rpx solid #007AFF;
            }

            &.detail {
              color: #666;
              border: 1rpx solid #666;
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 100rpx 0;

      image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }

      text {
        font-size: 28rpx;
        color: #999;
        margin-bottom: 40rpx;
      }

      .go-mall {
        font-size: 28rpx;
        color: #fff;
        background: #007AFF;
        padding: 20rpx 60rpx;
        border-radius: 40rpx;
      }
    }
  }
}
</style> 