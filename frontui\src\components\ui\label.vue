<template>
  <el-form-item
    :label="label"
    :required="required"
    :error="error"
    :prop="prop"
    :size="size"
    v-bind="$attrs"
  >
    <slot />
  </el-form-item>
</template>

<script setup>
defineProps({
  label: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  prop: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'default',
    validator: (val) => ['large', 'default', 'small'].includes(val)
  }
});
</script> 