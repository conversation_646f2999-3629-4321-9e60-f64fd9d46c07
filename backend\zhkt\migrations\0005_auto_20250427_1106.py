# Generated by Django 3.2.20 on 2025-04-27 03:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0004_auto_20250427_1031'),
    ]

    operations = [
        migrations.RenameField(
            model_name='admin',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='aichat',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='aichatmessage',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='aiprompt',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='chapter',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='course',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.<PERSON>ameField(
            model_name='courseenrollment',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='dept',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='feedback',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='homework',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='menu',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='order',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='pointsrecord',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='product',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='resource',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='role',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='student',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='submission',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='teacher',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='user',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.AddField(
            model_name='classgroup',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='college',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='major',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
    ]
