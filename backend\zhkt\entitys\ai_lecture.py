from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from ..config import LANGUAGE_CHOICES, DEFAULT_LANGUAGE

class AILectureDocument(models.Model):
    """AI讲课文档模型"""
    title = models.CharField(_('文档标题'), max_length=255)
    file_path = models.Char<PERSON>ield(_('文件路径'), max_length=500)
    file_type = models.Char<PERSON>ield(_('文件类型'), max_length=20, blank=True, null=True)
    total_pages = models.IntegerField(_('总页数'), null=True, blank=True)
    content_list_file_path = models.Char<PERSON>ield(_('解析内容列表文件路径'), max_length=500, blank=True, null=True)
    cover_image_path = models.Char<PERSON>ield(_('封面图路径'), max_length=500, blank=True, null=True)
    speech_style = models.Char<PERSON>ield(_('语音风格'), max_length=50, default='classroom', 
                                  help_text='可选值: classroom, grumpy, storytelling, novel, memorial')
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='文档的语言')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)
    user = models.ForeignKey(get_user_model(), on_delete=models.CASCADE, related_name='lecture_documents', verbose_name='所属用户')

    class Meta:
        verbose_name = _('AI讲课文档')
        verbose_name_plural = _('AI讲课文档')
        db_table = 'zhkt_ai_lecture_document'

    def __str__(self):
        return self.title

class AILectureChapter(models.Model):
    """AI讲课章节模型"""
    STATUS_CHOICES = (
        ('not_gen', '未生成'),
        ('gen', '生成中'),
        ('completed', '已完成'),
    )
    
    document = models.ForeignKey(AILectureDocument, on_delete=models.CASCADE, related_name='chapters')
    chapter_title = models.CharField(_('章节标题'), max_length=255)
    start_page = models.IntegerField(_('起始页码'))
    end_page = models.IntegerField(_('结束页码'))
    chapter_order = models.IntegerField(_('章节顺序'))
    status = models.CharField(_('生成状态'), max_length=20, choices=STATUS_CHOICES, default='not_gen')
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='章节的语言')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('AI讲课章节')
        verbose_name_plural = _('AI讲课章节')
        ordering = ['chapter_order']
        db_table = 'zhkt_ai_lecture_chapter'

    def __str__(self):
        return f"{self.document.title} - {self.chapter_title}"

class AILectureKeyPoint(models.Model):
    """AI讲课要点模型"""
    AUDIO_STATUS_CHOICES = (
        ('not_gen', '音频未生成'),
        ('gen', '音频生成中'),
        ('completed', '音频已完成'),
    )
    
    chapter = models.ForeignKey(AILectureChapter, on_delete=models.CASCADE, related_name='key_points')
    original_point = models.TextField(_('原始要点'))
    enhanced_point = models.TextField(_('知识增强后的要点'))
    point_order = models.IntegerField(_('要点顺序'))
    audio_status = models.CharField(_('音频状态'), max_length=20, choices=AUDIO_STATUS_CHOICES, default='not_gen',
                                   help_text='该要点的音频生成状态')
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='要点的语言')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('AI讲课要点')
        verbose_name_plural = _('AI讲课要点')
        ordering = ['point_order']
        db_table = 'zhkt_ai_lecture_key_point'

    def __str__(self):
        return f"{self.chapter.chapter_title} - 要点{self.point_order}"

class AILectureHtmlContent(models.Model):
    """AI讲课HTML内容模型"""
    key_point = models.OneToOneField(AILectureKeyPoint, on_delete=models.CASCADE, related_name='html_content')
    html_file_path = models.CharField(_('HTML文件路径'), max_length=500)
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='HTML内容的语言')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('AI讲课HTML内容')
        verbose_name_plural = _('AI讲课HTML内容')
        db_table = 'zhkt_ai_lecture_html_content'

    def __str__(self):
        return f"{self.key_point.chapter.chapter_title} - HTML内容"

class AILectureSpeechContent(models.Model):
    """AI讲课语音内容模型"""
    key_point = models.ForeignKey(AILectureKeyPoint, on_delete=models.CASCADE, related_name='speech_contents')
    sentence_order = models.IntegerField(_('句子顺序'), default=1)
    speech_script = models.TextField(_('讲稿内容'))
    audio_file_path = models.CharField(_('音频文件路径'), max_length=500)
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='语音内容的语言')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('AI讲课语音内容')
        verbose_name_plural = _('AI讲课语音内容')
        db_table = 'zhkt_ai_lecture_speech_content'
        ordering = ['key_point', 'sentence_order']

    def __str__(self):
        return f"{self.key_point.chapter.chapter_title} - 语音内容 - 第{self.sentence_order}句"

class AILectureSpeechStyle(models.Model):
    """AI讲课语音风格模型"""
    style_code = models.CharField(_('风格代码'), max_length=50, unique=True, 
                                help_text='如classroom, grumpy, storytelling等')
    style_name = models.CharField(_('风格名称'), max_length=100)
    description = models.CharField(_('风格描述'), max_length=255)
    prompt_template = models.TextField(_('提示词模板'), 
                                     help_text='提示词模板，使用{chapter_title}, {points}, {chapter_text}作为占位符')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('AI讲课语音风格')
        verbose_name_plural = _('AI讲课语音风格')
        db_table = 'zhkt_ai_lecture_speech_style'
        
    def __str__(self):
        return self.style_name 

class AILectureSubtitle(models.Model):
    """AI讲课字幕内容"""
    speech_content = models.ForeignKey(AILectureSpeechContent, on_delete=models.CASCADE, related_name='subtitles', verbose_name='语音内容ID')
    start_time = models.IntegerField(verbose_name='开始时间(毫秒)', default=0)
    end_time = models.IntegerField(verbose_name='结束时间(毫秒)', default=0)
    subtitle_text = models.TextField(verbose_name='字幕文本')
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='字幕的语言')
    created_at = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name='更新时间', auto_now=True)
    deleted_at = models.DateTimeField(verbose_name='删除时间', null=True, blank=True)
    
    class Meta:
        verbose_name = 'AI讲课字幕内容'
        verbose_name_plural = verbose_name
        db_table = 'zhkt_ai_lecture_subtitle'
        ordering = ['start_time']
        