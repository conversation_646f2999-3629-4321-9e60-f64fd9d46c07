@import './mixins.scss';

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  background-color: #f8f8f8;
}

/* 清除按钮默认样式 */
button {
  padding: 0;
  margin: 0;
  background: none;
  border: none;
  
  &::after {
    border: none;
  }
}

/* 统一图标大小 */
.uni-icons {
  line-height: 1;
}

/* 统一卡片样式 */
.card {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  @include box-shadow(light);
}

/* 统一列表样式 */
.list-item {
  @include flex(row, space-between);
  padding: 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
}

/* 统一按钮样式 */
.btn {
  @include flex(row, center);
  height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  
  &.primary {
    @include gradient-bg;
    color: #fff;
  }
  
  &.outline {
    background: #fff;
    color: #007AFF;
    border: 1rpx solid #007AFF;
  }
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

/* 统一标签样式 */
.tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  
  &.primary {
    color: #007AFF;
    background: rgba(0, 122, 255, 0.1);
  }
  
  &.warning {
    color: #ff9900;
    background: rgba(255, 153, 0, 0.1);
  }
  
  &.danger {
    color: #ff3b30;
    background: rgba(255, 59, 48, 0.1);
  }
}

/* 统一加载更多样式 */
.uni-load-more {
  @include flex(row, center);
  padding: 30rpx 0;
  
  .uni-load-more__text {
    font-size: 28rpx;
    color: #999;
    margin: 0 20rpx;
  }
}

/* 统一空状态样式 */
.empty-state {
  @include flex(column, center, center);
  padding: 100rpx 0;
  
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .text {
    font-size: 28rpx;
    color: #999;
  }
} 