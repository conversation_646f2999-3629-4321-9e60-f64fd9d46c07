import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// Import our custom Element Plus theme after the default theme
import './assets/element-custom.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// Font Awesome 通过CDN引入，查看index.html

// Import Material Icons
import 'material-icons/iconfont/material-icons.css'

import App from './App.vue'
import router from './router'
import i18n from './i18n'
import '@/assets/base.css'
import '@/assets/element-custom.css'
import '@/assets/auth-styles.css'

// 初始化应用
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)
app.use(i18n) // 添加i18n支持

app.mount('#app')
