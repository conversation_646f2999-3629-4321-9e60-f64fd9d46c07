<template>
  <StudentLayout 
    :pageTitle="$t('dashboard.student.title')" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    <!-- 欢迎信息与个人数据概览 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1">
          <h2 class="text-xl font-bold text-gray-800">{{ $t('dashboard.student.welcome', { name: studentStore.userFullName }) }}</h2>
          <p class="text-base text-gray-600 mt-1">{{ $t('dashboard.student.today', { date: formattedDate }) }}</p>
          
          <!-- 学习状态摘要 -->
          <div class="mt-3">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full bg-green-100 text-green-800 mr-2 text-sm tracking-wide">
              <el-icon class="mr-1"><Lightning /></el-icon>
              {{ $t('dashboard.student.learningStatus.continuousLearning', { days: studentStore.studentData.continuousLearningDays }) }}
            </span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full bg-blue-100 text-blue-800 text-sm tracking-wide">
              <el-icon class="mr-1"><Calendar /></el-icon>
              {{ $t('dashboard.student.learningStatus.weeklyLearning', { hours: studentStore.studentData.weeklyLearningHours }) }}
            </span>
          </div>
        </div>
        
        <!-- 最近目标 -->
        <div class="mt-4 md:mt-0 md:ml-6 bg-blue-50 p-4 rounded-lg">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-2 rounded-md bg-blue-100">
              <el-icon class="text-blue-600"><Aim /></el-icon>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-gray-900">{{ $t('dashboard.student.learningGoal.title') }}</h3>
              <p class="text-sm text-gray-600">
                {{ $t('dashboard.student.learningGoal.remainingCourses', { count: studentStore.learningStats.totalCourses-studentStore.learningStats.completedCourses }) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习表现概览 -->
    <div class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 课程完成率 -->
        <div class="bg-white p-4 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-blue-100 mr-4">
              <el-icon class="text-blue-600"><Monitor /></el-icon>
            </div>
            <div class="flex-1">
              <p class="text-base font-medium text-gray-600">{{ $t('dashboard.student.stats.courseCompletion.title') }}</p>
              <div class="flex items-center">
                <p class="text-xl font-bold text-gray-800">{{ studentStore.learningStats.courseCompletionRate }}%</p>
              </div>
            </div>
          </div>
          <div class="mt-4 w-full h-2 bg-gray-200 rounded-full">
            <div class="h-2 bg-blue-600 rounded-full" :style="`width: ${studentStore.learningStats.courseCompletionRate}%`"></div>
          </div>
          <div class="mt-1 text-sm text-gray-500 flex justify-between">
            <span>
              {{ $t('dashboard.student.stats.courseCompletion.totalCourses', { 
                total: studentStore.learningStats.totalCourses,
                completed: studentStore.learningStats.completedCourses 
              }) }}
            </span>
            <span>{{ $t('dashboard.student.stats.courseCompletion.monthlyChange', { percent: studentStore.learningStats.courseCompletionChange }) }}</span>
          </div>
        </div>

        <!-- 作业完成率 -->
        <div class="bg-white p-4 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-green-100 mr-4">
              <el-icon class="text-green-600"><Check /></el-icon>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('dashboard.student.stats.homeworkCompletion.title') }}</p>
              <div class="flex items-center">
                <p class="text-xl font-semibold text-gray-800">{{ studentStore.learningStats.assignmentCompletionRate }}%</p>
              </div>
            </div>
          </div>
          <div class="mt-4 w-full h-2 bg-gray-200 rounded-full">
            <div class="h-2 bg-green-600 rounded-full" :style="`width: ${studentStore.learningStats.assignmentCompletionRate}%`"></div>
          </div>
          <div class="mt-1 text-xs text-gray-500 flex justify-between">
            <span>
              {{ $t('dashboard.student.stats.homeworkCompletion.totalHomeworks', { 
                total: studentStore.learningStats.totalHomeworks,
                completed: studentStore.learningStats.completedHomeworks 
              }) }}
            </span>
            <span>{{ $t('dashboard.student.stats.homeworkCompletion.averageScore', { score: studentStore.learningStats.averageScore }) }}</span>
          </div>
        </div>

        <!-- 学习时长 -->
        <div class="bg-white p-4 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-yellow-100 mr-4">
              <el-icon class="text-yellow-600"><Clock /></el-icon>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('dashboard.student.stats.learningHours.title') }}</p>
              <div class="flex items-center">
                <p class="text-xl font-semibold text-gray-800">
                  {{ $t('dashboard.student.stats.learningHours.hours', { hours: studentStore.learningStats.totalLearningHours }) }}
                </p>
                <span class="ml-2 text-xs text-green-600">
                  {{ $t('dashboard.student.stats.learningHours.increase', { hours: studentStore.learningStats.recentHoursChange }) }}
                </span>
              </div>
            </div>
          </div>
          <div class="mt-4 grid grid-cols-7 gap-1">
            <div 
              v-for="(hours, index) in studentStore.learningStats.weeklyLearningData" 
              :key="index"
              class="rounded-sm" 
              :class="[
                hours <= 2 ? 'bg-green-100' : 
                hours <= 4 ? 'bg-green-200' : 
                hours <= 6 ? 'bg-green-300' : 
                'bg-green-400'
              ]"
              :style="`height: ${Math.min(2 + hours * 1.25, 10)}rem`">
            </div>
          </div>
          <div class="mt-1 text-xs text-gray-500 flex justify-between">
            <span>{{ $t('dashboard.student.stats.learningHours.weekday.monday') }}</span>
            <span>{{ $t('dashboard.student.stats.learningHours.weekday.sunday') }}</span>
          </div>
        </div>

        <!-- 积分与成就 -->
        <div class="bg-white p-4 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-3 rounded-md bg-purple-100 mr-4">
              <el-icon class="text-purple-600"><Medal /></el-icon>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('dashboard.student.stats.points.title') }}</p>
              <div class="flex items-center">
                <p class="text-xl font-semibold text-gray-800">
                  {{ $t('dashboard.student.stats.points.points', { points: studentStore.formattedPoints }) }}
                </p>
                <span class="ml-2 text-xs text-green-600">
                  {{ $t('dashboard.student.stats.points.increase', { points: studentStore.learningStats.recentPointsChange }) }}
                </span>
              </div>
            </div>
          </div>
          <div class="mt-4 w-full h-2 bg-gray-200 rounded-full">
            <div class="h-2 bg-purple-600 rounded-full" style="width: 62.5%"></div>
          </div>
          <div class="mt-2 flex items-center">
            <span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded">
              {{ $t(`dashboard.student.stats.points.medalLevel.${studentStore.learningStats.medalLevel}`) }}
            </span>
            <span class="ml-2 text-xs text-gray-500">
              {{ $t('dashboard.student.stats.points.nextLevel', { 
                points: studentStore.learningStats.pointsToNextLevel,
                level: studentStore.learningStats.medalLevel === 'bronze' ? 
                  $t('dashboard.student.stats.points.medalLevel.silver') : 
                  $t('dashboard.student.stats.points.medalLevel.gold')
              }) }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 学习建议 -->
    <div class="mb-6">
      <div class="bg-white rounded-lg shadow p-4">
        <h3 class="text-xl font-bold text-gray-800 mb-3">
          <el-icon class="text-yellow-500 mr-2"><Star /></el-icon>{{ $t('dashboard.student.recommendations.title') }}
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div v-for="(category, index) in studentStore.learningRecommendations" :key="index"
            class="p-4 border rounded-lg"
            :class="{ 
              'border-blue-200 bg-blue-50': category.category === 'efficiency',
              'border-green-200 bg-green-50': category.category === 'reinforcement',
              'border-purple-200 bg-purple-50': category.category === 'habit'
            }">
            <h4 class="text-base font-bold mb-3"
              :class="{
                'text-blue-800': category.category === 'efficiency',
                'text-green-800': category.category === 'reinforcement',
                'text-purple-800': category.category === 'habit'
              }">{{ $t(`dashboard.student.recommendations.${category.category}`) }}</h4>
            <ul class="text-sm leading-relaxed text-gray-700 space-y-2">
              <li v-for="(recommendation, recIndex) in category.recommendations" :key="recIndex" class="flex items-start">
                <el-icon class="mt-1 mr-2"
                  :class="{
                    'text-blue-500': category.category === 'efficiency',
                    'text-green-500': category.category === 'reinforcement',
                    'text-purple-500': category.category === 'habit'
                  }"><CircleCheck /></el-icon>
                <span>{{ recommendation }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { 
  Aim,
  Calendar,
  Medal,
  Clock,
  Check,
  Lightning,
  CircleCheck,
  Star,
  Monitor,
} from '@element-plus/icons-vue'

const studentStore = useStudentStore()
const { t, locale } = useI18n()

// Format current date based on current language
const today = new Date()
const formattedDate = computed(() => {
  // 根据当前语言选择日期格式
  if (locale.value === 'zh') {
    const options = {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      weekday: 'long'
    }
    return today.toLocaleDateString('zh-CN', options)
  } else if (locale.value === 'en') {
    return today.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    })
  } else if (locale.value === 'vi') {
    return today.toLocaleDateString('vi-VN', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    })
  } else if (locale.value === 'id') {
    return today.toLocaleDateString('id-ID', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    })
  } else {
    // 默认英文
    return today.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    })
  }
})

// 在组件挂载时获取学生信息
onMounted(async () => {
  await studentStore.fetchCurrentStudentInfo()
})
</script> 