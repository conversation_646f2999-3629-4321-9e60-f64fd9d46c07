<template>
  <div class="comment-section">
    <!-- 评论输入框 -->
    <div class="comment-input-container">
      <CustomAvatar 
        :src="currentUserAvatar" 
        :name="currentUserName" 
        class="comment-input-avatar"
      />
      <div class="comment-input-wrapper">
        <el-input
          v-model="newComment"
          type="textarea"
          :rows="3"
          placeholder="分享你的想法..."
          resize="none"
        />
        <el-button type="primary" @click="addComment" :disabled="!newComment.trim()">
          发表评论
        </el-button>
      </div>
    </div>
    
    <!-- 评论列表 -->
    <div class="comments-list" v-if="comments.length > 0">
      <el-empty v-if="comments.length === 0" description="暂无评论" />
      
      <transition-group name="comment-fade">
        <div v-for="comment in comments" :key="comment.id" class="comment-item">
          <!-- 评论主体 -->
          <div class="comment-main">
            <CustomAvatar 
              :src="resolveAvatarPath(comment.user.avatar)" 
              :name="comment.user.name" 
              :type="comment.user.isInstructor ? 'instructor' : 'student'"
              :isInstructor="comment.user.isInstructor"
              class="comment-avatar"
            />
            <div class="comment-content">
              <div class="comment-header">
                <span class="comment-username">
                  {{ comment.user.name }}
                  <span v-if="comment.user.isInstructor" class="instructor-badge">讲师</span>
                </span>
                <span class="comment-time">{{ comment.time }}</span>
              </div>
              <div class="comment-text">{{ comment.content }}</div>
              <div class="comment-actions">
                <el-button 
                  class="action-btn" 
                  size="small" 
                  text 
                  @click="likeComment(comment)"
                  :type="comment.isLiked ? 'primary' : 'default'"
                >
                  <el-icon><Thumb /></el-icon> 
                  <span>{{ comment.likes > 0 ? comment.likes : '' }}</span>
                </el-button>
                <el-button 
                  class="action-btn" 
                  size="small" 
                  text 
                  @click="comment.showReplyInput = !comment.showReplyInput"
                >
                  <el-icon><ChatLineRound /></el-icon> 回复
                </el-button>
              </div>
              
              <!-- 回复输入框 -->
              <div v-if="comment.showReplyInput" class="reply-input-container">
                <CustomAvatar 
                  :src="currentUserAvatar" 
                  :name="currentUserName" 
                  class="reply-input-avatar"
                  size="small"
                />
                <div class="reply-input-wrapper">
                  <el-input
                    v-model="comment.replyText"
                    type="textarea"
                    :rows="2"
                    placeholder="回复..."
                    resize="none"
                  />
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="replyToComment(comment)"
                    :disabled="!comment.replyText?.trim()"
                  >
                    发表回复
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 回复列表 -->
          <div v-if="comment.replies && comment.replies.length > 0" class="replies-list">
            <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
              <CustomAvatar 
                :src="resolveAvatarPath(reply.user.avatar)" 
                :name="reply.user.name" 
                :type="reply.user.isInstructor ? 'instructor' : 'assistant'"
                :isInstructor="reply.user.isInstructor"
                class="reply-avatar"
                size="small"
              />
              <div class="reply-content">
                <div class="reply-header">
                  <span class="reply-username">
                    {{ reply.user.name }}
                    <span v-if="reply.user.isInstructor" class="instructor-badge">讲师</span>
                  </span>
                  <span class="reply-time">{{ reply.time }}</span>
                </div>
                <div class="reply-text">{{ reply.content }}</div>
                <div class="reply-actions">
                  <el-button 
                    class="action-btn" 
                    size="small" 
                    text 
                    @click="likeReply(reply)"
                    :type="reply.isLiked ? 'primary' : 'default'"
                  >
                    <el-icon><Thumb /></el-icon> 
                    <span>{{ reply.likes > 0 ? reply.likes : '' }}</span>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition-group>

      <div class="comments-pagination" v-if="comments.length > 5">
        <el-pagination
          layout="prev, pager, next"
          :total="totalComments"
          :page-size="pageSize"
          @current-change="changePage"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { Thumb, ChatLineRound } from '@element-plus/icons-vue'
import CustomAvatar from './CustomAvatar.vue'

export default {
  name: 'CommentSection',
  components: {
    CustomAvatar,
    Thumb,
    ChatLineRound
  },
  props: {
    // 从父组件接收的评论数据
    initialComments: {
      type: Array,
      default: () => []
    },
    // 当前用户信息
    currentUser: {
      type: Object,
      default: () => ({
        id: 1,
        name: '当前用户',
        avatar: '/src/assets/images/avatars/user_avatar.jpg'
      })
    }
  },
  emits: ['comment-added', 'reply-added'],
  setup(props, { emit }) {
    // 评论数据
    const comments = ref([...props.initialComments]);
    
    // 新评论内容
    const newComment = ref('');
    
    // 分页
    const pageSize = ref(10);
    const currentPage = ref(1);
    
    // 计算属性
    const totalComments = computed(() => comments.value.length);
    const currentUserName = computed(() => props.currentUser.name);
    const currentUserAvatar = computed(() => props.currentUser.avatar);
    
    // 方法
    const addComment = () => {
      if (!newComment.value.trim()) return;
      
      const comment = {
        id: Date.now(),
        user: { ...props.currentUser },
        content: newComment.value,
        time: formatDate(new Date()),
        likes: 0,
        isLiked: false,
        showReplyInput: false,
        replyText: '',
        replies: []
      };
      
      comments.value.unshift(comment);
      newComment.value = '';
      
      // 发送事件通知父组件
      emit('comment-added', comment);
    };
    
    const replyToComment = (comment) => {
      if (!comment.replyText?.trim()) return;
      
      const reply = {
        id: Date.now(),
        user: { ...props.currentUser },
        content: comment.replyText,
        time: formatDate(new Date()),
        likes: 0,
        isLiked: false
      };
      
      if (!comment.replies) {
        comment.replies = [];
      }
      
      comment.replies.push(reply);
      comment.replyText = '';
      comment.showReplyInput = false;
      
      // 发送事件通知父组件
      emit('reply-added', { commentId: comment.id, reply });
    };
    
    const likeComment = (comment) => {
      comment.isLiked = !comment.isLiked;
      if (comment.isLiked) {
        comment.likes++;
      } else {
        comment.likes = Math.max(0, comment.likes - 1);
      }
    };
    
    const likeReply = (reply) => {
      reply.isLiked = !reply.isLiked;
      if (reply.isLiked) {
        reply.likes++;
      } else {
        reply.likes = Math.max(0, reply.likes - 1);
      }
    };
    
    const changePage = (page) => {
      currentPage.value = page;
    };
    
    // 辅助函数
    const formatDate = (date) => {
      return new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date).replace(/\//g, '-');
    };
    
    const resolveAvatarPath = (path) => {
      // 如果是相对路径，需要处理一下
      if (path && path.startsWith('@/')) {
        // 这里简单处理，实际项目中建议使用require或import动态导入
        return path.replace('@/', '/src/');
      }
      return path;
    };
    
    return {
      comments,
      newComment,
      pageSize,
      currentPage,
      totalComments,
      currentUserName,
      currentUserAvatar,
      addComment,
      replyToComment,
      likeComment,
      likeReply,
      changePage,
      resolveAvatarPath
    };
  }
}
</script>

<style scoped>
.comment-section {
  margin: 24px 0;
}

.comment-input-container {
  display: flex;
  margin-bottom: 32px;
  gap: 16px;
  align-items: flex-start;
}

.comment-input-avatar {
  flex-shrink: 0;
}

.comment-input-wrapper {
  flex: 1;
  position: relative;
}

.comment-input-wrapper .el-button {
  position: absolute;
  right: 12px;
  bottom: 12px;
}

.comments-list {
  margin-top: 24px;
}

.comment-item {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-main {
  display: flex;
  gap: 16px;
}

.comment-avatar {
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  align-items: center;
}

.comment-username {
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.comment-text {
  margin-bottom: 12px;
  line-height: 1.6;
  word-break: break-word;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
}

.instructor-badge {
  display: inline-block;
  background-color: var(--el-color-success);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  font-weight: 500;
}

.reply-input-container {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.reply-input-wrapper {
  flex: 1;
  position: relative;
}

.reply-input-wrapper .el-button {
  position: absolute;
  right: 12px;
  bottom: 12px;
}

.replies-list {
  margin-top: 16px;
  margin-left: 56px;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.reply-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  align-items: center;
}

.reply-username {
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.reply-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.reply-text {
  margin-bottom: 8px;
  line-height: 1.6;
  word-break: break-word;
}

.reply-actions {
  display: flex;
  gap: 16px;
}

.comments-pagination {
  margin-top: 32px;
  display: flex;
  justify-content: center;
}

/* Animations */
.comment-fade-enter-active,
.comment-fade-leave-active {
  transition: all 0.5s ease;
}
.comment-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}
.comment-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style> 