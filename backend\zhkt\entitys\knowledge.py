# -*- coding: utf-8 -*-
# @Time    : 2025/4/24
# <AUTHOR> lhq
# @File    : knowledge.py
# @Description :

from django.db import models
from django.utils.translation import gettext_lazy as _

class KnowledgeCategory(models.Model):
    """知识库主分类表实体 - 包含"我的文档"和"知识库"两个固定大分类
    - 个人文档(personal): 只有创建者自己可以看到和管理自己的文档
    - 知识库(knowledge): 由老师创建和上传，所有师生都可以查看
    """
    id = models.AutoField(primary_key=True, verbose_name=_('大分类ID'))
    name = models.CharField(max_length=255, verbose_name=_('大分类名称'))
    icon = models.CharField(max_length=255, null=True, blank=True, verbose_name=_('分类图标'))
    user_id = models.IntegerField(null=True, blank=True, verbose_name=_('创建者ID'))
    color = models.CharField(max_length=20, null=True, blank=True, verbose_name=_('分类颜色代码'))
    description = models.TextField(null=True, blank=True, verbose_name=_('大分类描述信息'))
    category_type = models.CharField(max_length=20, choices=[('personal', '个人文档'), ('knowledge', '知识库')],
                                    default='knowledge', verbose_name=_('分类类型'))
    sort_order = models.IntegerField(default=0, verbose_name=_('排序顺序'))
    status = models.CharField(max_length=20, default='1', verbose_name=_('状态'))
    created_at = models.DateTimeField(null=True, blank=True, verbose_name=_('创建日期时间'))
    updated_at = models.DateTimeField(null=True, blank=True, verbose_name=_('更新日期时间'))
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name=_('逻辑删除时间'))

    class Meta:
        db_table = 'zhkt_kb_categories'
        verbose_name = _('知识库主分类')
        verbose_name_plural = _('知识库主分类')
        ordering = ['sort_order']

    def __str__(self):
        return self.name

class KnowledgeDataset(models.Model):
    """知识库子分类表实体 - 可以是个人文档或知识库的子分类
    - 个人文档子分类: 由用户(老师/学生)创建，只有创建者可见
    - 知识库子分类: 只有老师可以创建，所有师生可查看
    """
    id = models.CharField(max_length=64, primary_key=True, verbose_name=_('数据集ID'))
    category = models.ForeignKey(KnowledgeCategory, on_delete=models.SET_NULL, null=True, related_name='datasets', verbose_name=_('所属大分类'))
    user_id = models.IntegerField(null=True, blank=True, verbose_name=_('创建者ID'))
    icon = models.CharField(max_length=255, null=True, blank=True, verbose_name=_('数据集图标'))
    name = models.CharField(max_length=255, verbose_name=_('数据集名称'))
    avatar = models.TextField(null=True, blank=True, verbose_name=_('数据集头像'))
    description = models.TextField(null=True, blank=True, verbose_name=_('数据集描述信息'))
    embedding_model = models.CharField(max_length=255, null=True, blank=True, verbose_name=_('嵌入模型名称'))
    status = models.CharField(max_length=20, default='1', verbose_name=_('数据集状态'))
    created_at = models.DateTimeField(null=True, blank=True, verbose_name=_('创建日期时间'))
    updated_at = models.DateTimeField(null=True, blank=True, verbose_name=_('更新日期时间'))
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name=_('逻辑删除时间'))

    class Meta:
        db_table = 'zhkt_kb_datasets'
        verbose_name = _('知识库子分类')
        verbose_name_plural = _('知识库子分类')
        unique_together = ('user_id', 'name', 'category')

    def __str__(self):
        return self.name

class KnowledgeDocument(models.Model):
    """知识库文件存储表实体
    """
    id = models.CharField(max_length=64, primary_key=True, verbose_name=_('文档ID'))
    dataset = models.ForeignKey(KnowledgeDataset, on_delete=models.CASCADE, related_name='documents', verbose_name=_('所属数据集'))
    user_id = models.IntegerField(null=True, blank=True, verbose_name=_('文档所有者ID'))
    icon = models.CharField(max_length=255, null=True, blank=True, verbose_name=_('文档图标'))
    name = models.CharField(max_length=512, verbose_name=_('文档名称'))
    size = models.BigIntegerField(default=0, verbose_name=_('文档大小'))
    type = models.CharField(max_length=50, null=True, blank=True, verbose_name=_('文档类型'))
    file_path = models.CharField(max_length=512, null=True, blank=True, verbose_name=_('文件存储路径'))
    chunk_method = models.CharField(max_length=50, null=True, blank=True, verbose_name=_('文档分块方法'))
    chunk_count = models.IntegerField(default=0, verbose_name=_('文档分块数量'))
    token_count = models.IntegerField(default=0, verbose_name=_('文档token数量'))
    process_duration = models.IntegerField(default=0, verbose_name=_('文档处理时间'))
    progress = models.FloatField(default=0, verbose_name=_('处理进度'))
    run = models.CharField(max_length=20, null=True, blank=True, verbose_name=_('运行状态'))
    status = models.CharField(max_length=20, default='1', verbose_name=_('文档状态'))
    created_at = models.DateTimeField(null=True, blank=True, verbose_name=_('创建日期时间'))
    updated_at = models.DateTimeField(null=True, blank=True, verbose_name=_('更新日期时间'))
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name=_('逻辑删除时间'))

    class Meta:
        db_table = 'zhkt_kb_documents'
        verbose_name = _('知识库文件')
        verbose_name_plural = _('知识库文件')

    def __str__(self):
        return self.name
        
class KnowledgeFavorite(models.Model):
    """知识库收藏表实体 - 记录用户收藏的文档"""
    id = models.AutoField(primary_key=True, verbose_name=_('收藏ID'))
    user_id = models.IntegerField(verbose_name=_('用户ID'))
    document = models.ForeignKey(KnowledgeDocument, on_delete=models.CASCADE, related_name='favorites', verbose_name=_('收藏的文档'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('收藏时间'))
    
    class Meta:
        db_table = 'zhkt_kb_favorites'
        verbose_name = _('知识库收藏')
        verbose_name_plural = _('知识库收藏')
        unique_together = ('user_id', 'document')
        
    def __str__(self):
        return f"{self.user_id}收藏的{self.document.name}"