# Generated by Django 3.2.20 on 2025-06-05 15:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0051_alter_question_answer'),
    ]

    operations = [
        migrations.CreateModel(
            name='HomeworkQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.IntegerField(default=0, verbose_name='题目顺序')),
                ('score', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='分值')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('homework', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='homework_questions', to='zhkt.homework')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='homework_questions', to='zhkt.question')),
            ],
            options={
                'verbose_name': '作业题目',
                'verbose_name_plural': '作业题目',
                'ordering': ['order'],
            },
        ),
    ]
