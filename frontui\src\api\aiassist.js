import { get, post, del, put } from '@/utils/request'
import { useAuthStore } from '@/stores/auth'

// 获取对话列表
export function getChatList(page = 1, pageSize = 100) {
  return get('/ai-chats/', { page, page_size: pageSize })
}

// 创建新对话
export function createChat(data) {
  return post('/ai-chats/', data)
}

// 获取对话消息
export function getChatMessages(chatId) {
  return get(`/ai-chat-messages/`, { chat: chatId })
}

// 发送消息
export function sendMessage(data) {
  return post('/ai-chat-messages/', data)
}

// 获取AI回复（流式）
export function getAIResponse(data) {
  const authStore = useAuthStore()
  const url = `/api/ai-chat-messages/stream_response/`
  
  console.log('发送请求到:', url)
  console.log('请求数据:', data)
  
  return fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authStore.token}`,
      'Accept': '*/*'
    },
    body: JSON.stringify(data),
    cache: 'no-cache',
    credentials: 'same-origin',
    mode: 'cors'
  }).then(response => {
    console.log('收到响应:', response)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response
  }).catch(error => {
    console.error('请求失败:', error)
    throw error
  })
}

// 获取提示词列表
export function getPromptList(type) {
  return get('/ai-prompts/', { type })
}

// 删除对话
export function deleteChat(chatId) {
  return del(`/ai-chats/${chatId}/`)
}

// 更新对话标题
export function updateChatTitle(chatId, title) {
  return put(`/ai-chats/${chatId}/`, { title })
} 