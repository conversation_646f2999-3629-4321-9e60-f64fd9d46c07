# -*- coding: utf-8 -*-
# @Time    : 2025/4/28
# <AUTHOR> lhq
# @File    : lesson_plan_tasks.py
# @Description : 视频处理任务模块，生成课程视频相关数据

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import os
import shutil
import subprocess
import tempfile
import time
from urllib.parse import urlparse

import requests
# Celery相关导入
from celery import shared_task, states
from celery.exceptions import Ignore
# 视频处理相关导入
from faster_whisper import WhisperModel
# Django相关导入
from ..entitys.course import Lesson
# 添加 DeepSeekAPI 导入
from ..utils.deepseek_api import DeepSeekAPI

# 日志配置
logger = logging.getLogger(__name__)



# 创建临时文件目录
TEMP_DIR = os.path.join(tempfile.gettempdir(), 'lesson_processor')
os.makedirs(TEMP_DIR, exist_ok=True)

def download_video_from_url(video_url, output_path=None):
    """
    从URL下载视频文件
    
    Args:
        video_url (str): 视频URL
        output_path (str, optional): 输出文件路径
        
    Returns:
        str: 下载的视频文件路径
    """
    # 如果未指定输出路径，创建临时文件
    if output_path is None:
        # 从URL提取文件名
        parsed_url = urlparse(video_url)
        path = parsed_url.path
        filename = os.path.basename(path)
        
        # 如果没有有效文件名，使用时间戳创建一个
        if not filename or '.' not in filename:
            filename = f"video_{int(time.time())}.mp4"
            
        # 创建输出路径
        output_path = os.path.join(TEMP_DIR, filename)
    
    try:
        # 检查文件是否已存在
        if os.path.exists(output_path):
            logger.info(f"视频文件已存在，跳过下载: {output_path}")
            return output_path
            
        logger.info(f"开始从 {video_url} 下载视频...")
        
        # 检查URL类型和处理方式
        parsed_url = urlparse(video_url)
        
        # 对于HTTP和HTTPS链接，使用requests下载
        if parsed_url.scheme in ['http', 'https']:
            with requests.get(video_url, stream=True, timeout=60) as response:
                response.raise_for_status()
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                        
        # 对于本地文件路径，直接复制
        elif parsed_url.scheme in ['file', ''] and os.path.exists(parsed_url.path):
            shutil.copy2(parsed_url.path, output_path)
            
        else:
            raise ValueError(f"不支持的URL类型: {video_url}")
            
        logger.info(f"视频下载完成: {output_path}")
        return output_path
        
    except requests.RequestException as e:
        error_msg = f"下载视频失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"处理视频URL失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


class SpeechToText:
    """语音转文字类，用于将音频转换为文字并生成字幕文件"""
    
    def __init__(self, model_size="medium"):
        """
        初始化语音转文字类
        
        Args:
            model_size (str): 模型大小，可选项: tiny, base, small, medium, large-v2, large-v3
        """
        self.model_size = model_size
        self.model = None
        
        # 检测是否有GPU可用
        import torch
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        # 在Mac上，对于CPU使用float32提供更好的性能/兼容性
        self.compute_type = "float16" if self.device == "cuda" else "float32"
        
        # 创建模型目录
        models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "models")
        os.makedirs(models_dir, exist_ok=True)
        self.models_dir = models_dir
        
    def load_model(self):
        """加载Whisper模型"""
        if self.model is None:
            print(f"使用设备: {self.device}, 计算类型: {self.compute_type}, 模型大小: {self.model_size}")
            print("加载语音识别模型中...")
            
            self.model = WhisperModel(
                self.model_size,
                device=self.device,
                compute_type=self.compute_type,
                download_root=self.models_dir
            )
            print("模型加载完成")
    

    def create_subtitle_file(self, audio_path, beam_size=5):
        """
        创建SRT字幕文件
        
        Args:
            audio_path (str): 音频文件路径
            output_path (str, optional): 输出SRT文件路径
            beam_size (int): Beam search大小
            
        Returns:
            str: 生成的SRT文件路径
        """
        self.load_model()
        
        start_time = time.time()
        
        try:
            print(f"开始语音转写，使用设备: {self.device}, 模型: {self.model_size}...")
            # 执行语音转写
            segments, info = self.model.transcribe(
                audio_path,
                beam_size=beam_size,
                vad_filter=True,
                word_timestamps=True  # 获取单词级时间戳
            )

            subtitle_data = []

            for segment in segments:
                text = segment.text.strip().replace('\n', ' ')
                start_time = segment.start
                end_time = segment.end

                # 将时间转换为 ASS 格式：H:MM:SS.cs（cs 为百分之一秒）
                start_h, remainder = divmod(start_time, 3600)
                start_m, start_s = divmod(remainder, 60)
                end_h, remainder = divmod(end_time, 3600)
                end_m, end_s = divmod(remainder, 60)

                start_time_str = f"{int(start_h)}:{int(start_m):02}:{int(start_s):02}.{int((start_s % 1) * 100):02}"
                end_time_str = f"{int(end_h)}:{int(end_m):02}:{int(end_s):02}.{int((end_s % 1) * 100):02}"
                # 写入 Dialogue 行
                subtitle_data.append({
                    "start_time_str": start_time_str,
                    "end_time_str": end_time_str,
                    "text": text
                })

            return subtitle_data
            
        except Exception as e:
            error_msg = f"语音转写失败: {str(e)}"
            print(f"❌ {error_msg}")
            # 记录更详细的错误信息
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            
            # 返回一个空的转录结果而不是失败
            empty_transcription = {
                "segments": []
            }

            return  empty_transcription
    

class AINavigator:
    """AI导航器，用于生成视频内容导航点"""
    
    def __init__(self):
        """
        初始化AI导航器，使用DeepSeek API
        """
        # 创建DeepSeekAPI实例
        self.client = DeepSeekAPI()
        # 默认模型名称
        self.model = "deepseek-chat"
    
    def generate_navigation(self, subtitle_data):
        """
        根据视频信息和转录生成AI视频导航
        
        参数:
            transcription (dict): 转录结果
            
        返回:
            dict: AI生成的视频导航，JSON格式
        """
        try:
            # 检查转录是否有效

            # 创建系统提示，加强时间指导
            system_prompt = f"""
            你是一个视频内容分析专家，请根据提供的视频信息和语音转文字结果，生成一个视频导航JSON。
            
            你需要：
            1. 提供一个简短的视频内容总结（100字以内）
            2. 识别视频中的关键时间点和对应的主要内容
            3. 为每个关键时间点提供简短而有意义的内容描述
            4. 确保按时间顺序排列关键点
            5. 确保输出的JSON格式正确
            
            输出JSON格式示例：
            {{
                "summary": "视频内容总结，不超过100字",
                "key_points": [
                    {{
                        "time": "00:00",
                        "content": "关键点1的内容描述"
                    }},
                    {{
                        "time": "01:30",
                        "content": "关键点2的内容描述"
                    }},
                    ...
                ]
            }}
            
            关键点要求：
            1. 必须提供准确的6-10个关键时间点，不多不少
            2. 每个内容描述简洁明了，不超过20字
            3. 内容描述要反映该时间点的核心内容
            4. 使用MM:SS格式的时间戳（如05:30表示5分30秒）或HH:MM:SS格式（如01:05:30表示1小时5分30秒）
            5. 时间点必须均匀分布在整个视频中，确保覆盖视频的开始、中间和结尾部分
            6. 时间戳必须准确对应视频内容，基于实际转录文本中的时间点选择
            7. 所有时间戳必须小于视频总时长
            """
            
            # 创建用户消息，包含视频信息和转录
            user_message = f"""
            以下是视频信息和语音转文字结果，请据此生成视频导航JSON：
            
            带时间的转写文本:
            {str(subtitle_data)}
            
            请分析以上内容并生成一个完整的视频导航JSON。
            请确保你的输出是有效的JSON格式，可以直接解析。
            请确保生成6-10个关键时间点，并且时间戳要与视频内容准确对应。
            重要：所有时间戳必须严格小于视频总时长，请务必检查时间格式正确。
            """
            
            # 使用DeepSeekAPI进行API调用
            print(f"正在使用DeepSeek API生成内容导航...")
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]
            completion_text = self.client.chat(
                messages=messages,
                model=self.model,
                temperature=0.2
            )
            
            # 从完成文本中提取JSON
            json_start = completion_text.find('{')
            json_end = completion_text.rfind('}')
            
            json_data = None
            if json_start >= 0 and json_end >= 0:
                json_str = completion_text[json_start:json_end+1]
                json_data = json.loads(json_str)
            else:
                # 如果JSON提取失败，尝试解析整个响应
                try:
                    json_data = json.loads(completion_text)
                except:
                    raise ValueError("无法从DeepSeek API响应中提取有效的JSON")

            return json_data
            
        except Exception as e:
            raise Exception(f"生成导航失败: {str(e)}")
    
    def _format_time(self, seconds):
        """
        将秒格式化为HH:MM:SS或MM:SS格式
        
        参数:
            seconds (float): 秒数
            
        返回:
            str: 格式化的时间字符串
        """
        # 确保seconds是数字
        try:
            seconds = float(seconds)
        except (TypeError, ValueError):
            return "00:00"  # 默认值
            
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    

class DialogueGenerator:
    """对话生成器，用于根据字幕内容和关键点生成预设教育对话"""
    
    def __init__(self):
        """
        初始化对话生成器，使用DeepSeek API
        """
        # 创建DeepSeekAPI实例
        self.client = DeepSeekAPI()
        # 默认模型名称
        self.model = "deepseek-chat"
        

    def generate_preset_dialogues(self,  subtitle_data):
        """
        根据视频信息、转录和导航点生成3套预设对话
        
        参数:
            transcription (dict): 转录结果

        返回:
            dict: AI生成的3套预设对话，格式为{"dialogue_sets": [set1, set2, set3]}
        """
        try:
            # 存储3套对话集
            dialogue_sets = []
            
            # 创建系统提示，加强时间指导
            system_prompt = f"""
            你是一个专业的教育对话生成器，现在需要根据课程字幕内容，生成一组学习伙伴之间的互动对话。这些对话应该围绕字幕中的知识点展开，模拟真实课堂上不同类型学生的互动场景。
            
            你需要生成符合JSON格式的对话数据，包含以下5个角色：
            
            1. 助教王老师（teacher）：讲师角色，负责解释概念、回答问题，语气专业且耐心
            2. 小明（funnyKid）：活跃开朗的学生，经常开玩笑活跃课堂气氛，语言轻松幽默，偶尔带有emoji表情
            3. 李华（thinker）：提供深入讨论的学生，善于思考问题的本质和提出有深度的见解，语言表达严谨
            4. 张颖（curious）：积极提出问题的学生，不懂就问，问题简单直接，有助于引出重要概念的解释
            5. 赵阳（topStudent）：整理学习要点的学生，善于用符号标记（如⭐、📌）和序号梳理关键内容，语言简洁有力
            
            对话要求：
            1. 对话应围绕字幕内容中的关键知识点展开，确保准确反映课程内容
            2. 每个角色的语言风格和关注点应与其人设一致
            3. 对话应呈现渐进式学习过程，从基础理解到深度思考
            4. 对话应该有教育意义，帮助巩固和扩展字幕中的知识点
            5. 合理设置对话触发时间点，与字幕内容进度匹配
            6. 确保对话自然流畅，符合真实课堂讨论的节奏
            7. 各角色发言频率应平均分布，确保每个角色都有大致相等的发言机会
            8. 避免一个角色连续多次发言，尽量让不同角色轮流参与讨论
            
            输出要求：
            1. 输出必须是严格的JSON格式，确保可以被直接解析
            2. 每个对话组应关联一个关键时间点
            3. 每个对话组应包含2-5条消息，由不同角色发出
            4. 确保每个角色至少有一次发言机会
            5. 对话内容必须与关键时间点处的视频内容相关
            """
            
            # 准备用户提示
            user_prompt = f"""
            我需要你基于以下字幕内容和关键点生成一组预设对话：
            
            字幕内容:
            {str(subtitle_data)} 
            
            请确保对话符合JSON格式，并且以下是期望的输出格式：
            ```json
            {{
              "dialogues": [
                {{
                  "triggerTime": "MM:SS",
                  "messages": [
                    {{
                      "agentId": "角色ID",
                      "agentName": "角色名称",
                      "content": "对话内容"
                    }},
                    {{
                      "agentId": "角色ID",
                      "agentName": "角色名称",
                      "content": "对话内容" 
                    }}
                  ]
                }}
              ]
            }}
            ```
            
            请确保:
            1. 每个关键时间点都有一组对话
            2. 每组对话包含2-5条消息
            3. 所有角色在整个对话中的发言次数大致相等
            4. 对话内容与该时间点的视频内容密切相关
            5. 角色ID应使用以下映射:
               - teacher: 王老师
               - funnyKid: 小明
               - thinker: 李华
               - curious: 张颖
               - topStudent: 赵阳
            
            重要：请创建独特的对话内容，与其他可能已经生成的对话不同。请确保对话中包含不同的问题、观点和讨论角度。
            """
            
            # 使用不同的温度参数生成3套对话
            temperatures = [0.3, 0.5, 0.7]  # 不同的创意程度
            
            print("正在生成3套预设对话...")
            
            for i, temp in enumerate(temperatures):
                print(f"正在生成第{i+1}套对话(temperature={temp})...")
                
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                
                completion_text = self.client.chat(
                    messages=messages,
                    model=self.model,
                    temperature=temp
                )
                
                # 从完成文本中提取JSON
                json_start = completion_text.find('{')
                json_end = completion_text.rfind('}')
                
                preset_dialogues = None
                if json_start >= 0 and json_end >= 0:
                    json_str = completion_text[json_start:json_end+1]
                    preset_dialogues = json.loads(json_str)
                else:
                    # 如果JSON提取失败，尝试解析整个响应
                    try:
                        preset_dialogues = json.loads(completion_text)
                    except:
                        print(f"无法从API响应中提取有效的JSON，跳过第{i+1}套对话")
                        continue
                
                # 验证对话数据
                if preset_dialogues:
                    dialogues = preset_dialogues.get("dialogues", [])
                    print(f"成功生成了第{i+1}套对话，包含{len(dialogues)}组对话")
                    
                    # 检查每个对话组
                    for j, dialogue in enumerate(dialogues):
                        trigger_time = dialogue.get("triggerTime", "")
                        messages = dialogue.get("messages", [])
                        print(f"对话组 {j+1} (触发时间: {trigger_time}): {len(messages)} 条消息")
                    
                    # 将这套对话添加到结果中
                    dialogue_sets.append(dialogues)
            
            # 如果未能生成3套完整对话，补足空集合
            while len(dialogue_sets) < 3:
                print(f"警告：只生成了{len(dialogue_sets)}套对话，添加空对话集补足3套")
                dialogue_sets.append([])
            
            # 返回包含3套对话的数据结构
            result = {"dialogue_sets": dialogue_sets}
            return result
            
        except Exception as e:
            print(f"生成预设对话失败: {str(e)}")
            # 返回一个基本的空结构
            return {"dialogue_sets": [[], [], []]}


def extract_audio(video_path, output_audio_path=None):
    """
    从视频中提取音频
    
    Args:
        video_path (str): 视频文件路径
        output_audio_path (str, optional): 输出音频文件路径
        
    Returns:
        str: 提取的音频文件路径
    """
    if output_audio_path is None:
        # 创建输出目录
        video_basename = os.path.basename(video_path)
        video_name = os.path.splitext(video_basename)[0]
        output_audio_path = os.path.join(TEMP_DIR, f"{video_name}.wav")
    
    try:
        # 检查文件是否已存在
        if os.path.exists(output_audio_path):
            logger.info(f"音频文件已存在，跳过提取: {output_audio_path}")
            return output_audio_path
            
        # 使用FFmpeg提取音频
        cmd = [
            "ffmpeg", 
            "-i", video_path, 
            "-vn",                # 无视频
            "-acodec", "pcm_s16le",  # PCM 16位音频
            "-ar", "16000",          # 16kHz采样率
            "-ac", "1",              # 单声道
            "-y",                    # 覆盖输出文件
            output_audio_path
        ]
        
        logger.info(f"正在从视频提取音频...")
        process = subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        logger.info(f"音频提取成功: {output_audio_path}")
        return output_audio_path
    except subprocess.CalledProcessError as e:
        error_msg = e.stderr.decode('utf-8')
        logger.error(f"音频提取失败: {error_msg}")
        raise Exception(f"Error extracting audio from video: {error_msg}")
    except Exception as e:
        logger.error(f"音频提取失败: {str(e)}")
        raise

def get_video_info(video_path):
    """
    获取视频基本信息
    
    Args:
        video_path (str): 视频文件路径
        
    Returns:
        dict: 包含视频信息的字典
    """
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"视频文件不存在: {video_path}")
    
    try:
        # 使用ffprobe获取视频时长和分辨率
        cmd = [
            "ffprobe", 
            "-v", "error", 
            "-show_entries", "format=duration", 
            "-show_entries", "stream=width,height,r_frame_rate", 
            "-of", "json", 
            video_path
        ]
        
        process = subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output = process.stdout.decode('utf-8')
        
        data = json.loads(output)
        
        # 提取信息
        duration = float(data.get("format", {}).get("duration", 0))
        
        # 查找视频流
        width = height = fps = None
        for stream in data.get("streams", []):
            if stream.get("width") and stream.get("height"):
                width = stream.get("width")
                height = stream.get("height")
                # 解析fps可能的格式为"24/1"
                fps_str = stream.get("r_frame_rate", "24/1")
                if '/' in fps_str:
                    num, den = map(int, fps_str.split('/'))
                    fps = num / den if den else 0
                else:
                    fps = float(fps_str)
                break
        
        return {
            "filename": os.path.basename(video_path),
            "duration": duration,
            "width": width,
            "height": height,
            "fps": fps
        }
    except subprocess.CalledProcessError as e:
        error_msg = e.stderr.decode('utf-8')
        print(f"获取视频信息失败: {error_msg}")
        raise Exception(f"Error getting video info: {error_msg}")
    except Exception as e:
        print(f"获取视频信息失败: {str(e)}")
        raise

def check_dependencies():
    """
    检查系统依赖项是否正确安装
    
    Returns:
        bool: 所有依赖项都可用返回True，否则返回False
    """
    try:
        # 检查ffmpeg是否可用
        subprocess.run(["ffmpeg", "-version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        logger.info("✅ FFmpeg 可用")
        
        # 检查ffprobe是否可用
        subprocess.run(["ffprobe", "-version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        logger.info("✅ FFprobe 可用")
        
        # 检查GPU是否可用（如果使用GPU）
        try:
            import torch
            if torch.cuda.is_available():
                logger.info(f"✅ GPU 可用: {torch.cuda.get_device_name(0)}")
            else:
                logger.info("⚠️ GPU 不可用，将使用CPU进行处理")
        except ImportError:
            logger.warning("⚠️ PyTorch未安装，无法检查GPU可用性")
        
        return True
    except subprocess.CalledProcessError:
        logger.error("❌ FFmpeg或FFprobe未正确安装")
        return False
    except Exception as e:
        logger.error(f"❌ 检查依赖项时出错: {str(e)}")
        return False

def process_video(video_path, offset=0, model_size="large-v2", task=None):
    """
    完整处理视频：提取音频、生成字幕、创建AI导航和生成预设对话
    
    Args:
        video_path (str): 视频文件路径
        offset (float): 字幕时间偏移量（秒）
        model_size (str): 语音识别模型大小
        task (celery.Task, optional): Celery任务对象，用于状态更新
        
    Returns:
        dict: 处理结果，包含各输出信息
    """
    # 首先检查系统依赖项
    if not check_dependencies():
        error_msg = "系统依赖项检查失败，无法处理视频"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    # 记录开始时间
    start_time = time.time()
    temp_files = []  # 跟踪所有临时文件

    try:
        # 更新任务状态
        if task:
            task.update_state(state='PROGRESS', meta={'status': '正在获取视频信息'})
        
        # 检查视频文件是否存在
        if not os.path.exists(video_path):
            error_msg = f"视频文件不存在: {video_path}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)
        
        # 获取视频信息
        logger.info("获取视频信息...")
        video_info = get_video_info(video_path)
        logger.info(f"视频文件: {video_path}")
        logger.info(f"时长: {video_info['duration']:.2f}秒")
        logger.info(f"分辨率: {video_info['width']}x{video_info['height']}")
        
        temp_files.append(video_path)  # 添加视频文件到临时文件列表
        
        # 更新任务状态
        if task:
            task.update_state(state='PROGRESS', meta={'status': '正在提取音频', 'progress': 10})
        
        # 提取音频
        audio_path = extract_audio(video_path)
        temp_files.append(audio_path)  # 添加音频文件到临时文件列表
        logger.info(f"音频提取完成，耗时: {time.time() - start_time:.2f}秒")
        
        # 检查音频文件是否有效
        if not os.path.exists(audio_path) or os.path.getsize(audio_path) == 0:
            error_msg = f"提取的音频文件无效: {audio_path}"
            logger.error(error_msg)
            raise Exception(error_msg)
        
        # 更新任务状态
        if task:
            task.update_state(state='PROGRESS', meta={'status': '正在生成字幕', 'progress': 20})
        
        # 设置输出路径
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        srt_path = os.path.join(TEMP_DIR, f"{video_name}.srt")
        temp_files.append(srt_path)  # 添加字幕文件到临时文件列表
        
        # 创建语音转文字实例
        stt = SpeechToText(model_size=model_size)
        
        # 偏移量说明
        offset_explanation = "提前" if offset < 0 else "延迟"
        logger.info(f"正在生成字幕文件 (时间偏移量: {offset} 秒，字幕将{offset_explanation}显示)...")
        
        # 生成字幕并获取转录结果
        subtitle_data = stt.create_subtitle_file(audio_path)

        # 更新任务状态
        if task:
            task.update_state(state='PROGRESS', meta={'status': '正在生成AI视频导航', 'progress': 60})
        
        try:
            logger.info("正在生成AI视频导航...")
            navigator = AINavigator()
            navigation = navigator.generate_navigation(subtitle_data)

        except Exception as e:
            logger.error(f"生成AI导航失败: {str(e)}")
            navigation = None
        
        # 更新任务状态
        if task:
            task.update_state(state='PROGRESS', meta={'status': '正在生成预设对话', 'progress': 80})
        
        # 生成预设对话
        try:
            logger.info("\n正在生成预设对话...")
            dialogue_generator = DialogueGenerator()
            preset_dialogues = dialogue_generator.generate_preset_dialogues(subtitle_data)

        except Exception as e:
            logger.error(f"生成预设对话失败: {str(e)}")
            preset_dialogues = None
        
        # 计算总耗时
        total_time = time.time() - start_time
        logger.info(f"\n处理完成！总耗时: {total_time:.2f}秒")
        
        # 返回处理结果（不再包含文件路径，直接返回数据）
        return {
            "video_info": video_info,
            "subtitle_data": subtitle_data,
            "navigation": navigation,
            "preset_dialogues": preset_dialogues,
            "total_time": total_time,
        }
        
    except Exception as e:
        logger.error(f"视频处理失败: {str(e)}")
        raise
    finally:
        # 这里不自动清理临时文件，而是由调用者决定何时清理
        pass

# Celery任务
@shared_task(bind=True, max_retries=3)
def process_lesson_video(self, lesson_id):
    """
    处理课时视频的Celery任务，生成字幕、导航和对话数据
    Args:
        self: Celery任务对象
        lesson_id: 课时ID
    Returns:
        dict: 处理结果
    """
    try:
        # 更新任务状态
        self.update_state(state='PROGRESS', meta={'status': '开始处理课时视频'})
        # 获取Lesson对象
        try:
            lesson = Lesson.objects.get(id=lesson_id)
        except Lesson.DoesNotExist:
            error_msg = f"课时ID {lesson_id} 不存在"
            logger.error(error_msg)
            self.update_state(state=states.FAILURE, meta={'error': error_msg})
            raise Ignore()
        # 检查视频URL是否存在
        if not lesson.video_url:
            error_msg = f"课时ID {lesson_id} 没有视频URL"
            logger.error(error_msg)
            self.update_state(state=states.FAILURE, meta={'error': error_msg})
            raise Ignore()
        # 更新任务状态
        self.update_state(state='PROGRESS', meta={'status': '正在下载视频', 'progress': 5})
        # 下载视频
        try:
            video_path = download_video_from_url(lesson.video_url)
        except Exception as e:
            error_msg = f"下载视频失败: {str(e)}"
            logger.error(error_msg)
            self.update_state(state=states.FAILURE, meta={'error': error_msg})
            raise Ignore()
        # 处理视频
        try:
            # 默认使用
            result = process_video(
                video_path=video_path,
                offset=0,  # 可以通过配置文件或参数调整
                model_size="large-v2",  # 可以通过配置文件或参数调整
                task=self  # 传递任务对象用于状态更新
            )
            print("\n===================== 字幕内容 =====================")
            print(json.dumps(result["subtitle_data"], ensure_ascii=False, indent=2))
            print("\n===================== 关键点导航 =====================")
            print(json.dumps(result["navigation"], ensure_ascii=False, indent=2))
            print("\n===================== 预设对话 =====================")
            print(json.dumps(result["preset_dialogues"], ensure_ascii=False, indent=2))
        except Exception as e:
            error_msg = f"处理视频失败: {str(e)}"
            logger.error(error_msg)
            self.update_state(state=states.FAILURE, meta={'error': error_msg})
            raise Ignore()
        # 更新任务状态
        self.update_state(state='PROGRESS', meta={'status': '正在更新数据库', 'progress': 95})
        # 更新Lesson模型数据
        try:
            update_lesson_data(lesson, result)
        except Exception as e:
            error_msg = f"更新数据库失败: {str(e)}"
            logger.error(error_msg)
            self.update_state(state=states.FAILURE, meta={'error': error_msg})
            raise Ignore()
        # 更新任务状态为完成
        result_msg = f"课时 '{lesson.title}' 视频处理完成，耗时: {result['total_time']:.2f}秒"
        logger.info(result_msg)
        return {
            'status': 'success',
            'message': result_msg,
            'lesson_id': lesson_id,
            'total_time': result['total_time']
        }
    except Exception as e:
        logger.error(f"处理课时视频失败(ID: {lesson_id}): {str(e)}")
        # 修复错误处理部分
        try:
            # 直接使用具体的异常类型替代原始异常对象
            # 创建一个新的ValueError作为重试的异常
            retry_exc = ValueError(str(e))
            self.retry(exc=retry_exc, countdown=60 * 5)  # 5分钟后重试
        except Exception as retry_error:
            # 记录重试过程中的错误
            logger.error(f"重试失败: {str(retry_error)}")
            # 更新状态时不传递异常对象，而是传递错误信息
            self.update_state(
                state=states.FAILURE, 
                meta={'error': f"处理失败: {str(e)}", 'exc_type': type(e).__name__}
            )
            raise Ignore()

def update_lesson_data(lesson, result):
    """
    更新Lesson模型的数据
    
    Args:
        lesson: Lesson模型对象
        result: 视频处理结果
        
    Returns:
        Lesson: 更新后的Lesson对象
    """
    # 更新字幕数据
    if 'subtitle_data' in result and result['subtitle_data']:
        lesson.subtitle_data = result['subtitle_data']
        
    # 更新导航数据
    if 'navigation' in result and result['navigation']:
        lesson.mock_navigation_data = result['navigation']
        
    # 更新对话数据
    if 'preset_dialogues' in result and result['preset_dialogues']:
        # 检查新的数据结构
        lesson.mock_dialogue_data = result['preset_dialogues']

    # 保存更新
    lesson.save()
    logger.info(f"课时 '{lesson.title}' (ID: {lesson.id}) 数据已更新")
    
    return lesson
