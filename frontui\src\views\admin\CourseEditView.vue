<template>
  <div class="min-h-screen bg-gray-100 py-6 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button
          @click="goBack"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <svg class="mr-2 -ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          返回课程详情
        </button>
      </div>

      <!-- 编辑表单卡片 -->
      <div class="bg-white shadow rounded-lg">
        <form @submit.prevent="handleSubmit">
          <div class="px-6 pt-6 pb-4 sm:p-8 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h1 class="text-2xl leading-6 font-bold text-gray-900">编辑课程</h1>
                
                <div class="mt-8 grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-3">
                  <!-- 课程名称 -->
                  <div class="sm:col-span-3">
                    <label for="name" class="block text-sm font-medium text-gray-700">课程名称</label>
                    <div class="mt-2">
                      <input 
                        type="text" 
                        id="name" 
                        v-model="formData.name" 
                        required
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>
                  </div>

                  <!-- 所属学院 -->
                  <div>
                    <label for="college" class="block text-sm font-medium text-gray-700">所属学院</label>
                    <div class="mt-2">
                      <select 
                        id="college" 
                        v-model="formData.college" 
                        required
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option v-for="college in colleges" :key="college.id" :value="college.name">
                          {{ college.name }}
                        </option>
                      </select>
                    </div>
                  </div>

                  <!-- 课程类型 -->
                  <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700">课程类型</label>
                    <div class="mt-2">
                      <select 
                        id="subject" 
                        v-model="formData.subject" 
                        required
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option value="math">数学类</option>
                        <option value="programming">编程类</option>
                        <option value="physics">物理类</option>
                        <option value="chemistry">化学类</option>
                        <option value="database">数据库类</option>
                      </select>
                    </div>
                  </div>

                  <!-- 授课教师 -->
                  <div>
                    <label for="teacher" class="block text-sm font-medium text-gray-700">授课教师</label>
                    <div class="mt-2">
                      <input 
                        type="text" 
                        id="teacher" 
                        v-model="formData.teacher" 
                        required
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>
                  </div>

                  <!-- 开课时间 -->
                  <div>
                    <label for="startDate" class="block text-sm font-medium text-gray-700">开课时间</label>
                    <div class="mt-2">
                      <input 
                        type="date" 
                        id="startDate" 
                        v-model="formData.startDate" 
                        required
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>
                  </div>

                  <!-- 课程时长 -->
                  <div>
                    <label for="duration" class="block text-sm font-medium text-gray-700">课程时长（周）</label>
                    <div class="mt-2">
                      <input 
                        type="number" 
                        id="duration" 
                        v-model="formData.duration" 
                        required 
                        min="1"
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>
                  </div>

                  <!-- 班级数量 -->
                  <div>
                    <label for="classCount" class="block text-sm font-medium text-gray-700">班级数量</label>
                    <div class="mt-2">
                      <input 
                        type="number" 
                        id="classCount" 
                        v-model="formData.classCount" 
                        required 
                        min="1"
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>
                  </div>

                  <!-- 课程状态 -->
                  <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">课程状态</label>
                    <div class="mt-2">
                      <select 
                        id="status" 
                        v-model="formData.status" 
                        required
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option value="planned">计划中</option>
                        <option value="active">进行中</option>
                        <option value="ended">已结束</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="bg-gray-50 px-6 py-4 sm:px-8 sm:flex sm:flex-row-reverse">
            <button 
              type="submit"
              class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              保存修改
            </button>
            <button 
              type="button" 
              @click="goBack"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 模拟数据 - 实际应用中应该从API获取
const colleges = ref([
  { id: 1, name: '计算机学院' },
  { id: 2, name: '数学学院' },
  { id: 3, name: '物理学院' }
])

const formData = ref({
  id: '',
  name: '',
  college: '',
  subject: '',
  teacher: '',
  startDate: '',
  duration: 16,
  classCount: 1,
  status: 'planned'
})

// 在组件挂载时获取课程数据
onMounted(async () => {
  const courseId = route.params.id
  // TODO: 从API获取课程数据
  // const response = await fetchCourseById(courseId)
  // formData.value = { ...response.data }
})

const handleSubmit = async () => {
  try {
    // TODO: 调用API更新课程信息
    // await updateCourse(formData.value)
    router.push(`/admin/courses/${formData.value.id}`)
  } catch (error) {
    console.error('更新课程失败:', error)
  }
}

const goBack = () => {
  router.push(`/admin/courses/${route.params.id}`)
}
</script> 