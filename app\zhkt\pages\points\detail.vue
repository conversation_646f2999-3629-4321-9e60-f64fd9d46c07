<template>
  <view class="detail-container">
    <!-- 商品图片
    <swiper class="goods-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
      <swiper-item v-for="(image, index) in goodsDetail.images" :key="index">
        <image :src="getProductImage(image)" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
    -->
    <view class="goods-swiper">
      <image :src="getProductImage(goodsDetail.image)" mode="aspectFill"></image>
    </view>

    <!-- 商品信息 -->
    <view class="goods-info">
      <view class="name">{{ goodsDetail.name }}</view>
      <view class="price-info">
        <text class="points">{{ goodsDetail.points_price }}积分</text>
        <text class="stock" :class="{ low: goodsDetail.stock < 10 }">
          库存: {{ goodsDetail.stock }}
        </text>
      </view>
      <view class="desc">{{ goodsDetail.description }}</view>
    </view>

    <!-- 商品详情 -->
    <view class="goods-detail">
      <view class="section-title">商品详情</view>
      <rich-text :nodes="goodsDetail.description"></rich-text>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="points-info">
        <text class="label">我的积分</text>
        <text class="value">{{ myPoints }}</text>
      </view>
      <button 
        class="exchange-btn" 
        :class="{ disabled: !canExchange }"
        @tap="handleExchange"
      >立即兑换</button>
    </view>

    <!-- 兑换确认弹窗 -->
    <uni-popup ref="exchangePopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="兑换确认"
        :content="exchangeConfirmContent"
        :before-close="true"
        @confirm="confirmExchange"
        @close="cancelExchange"
      ></uni-popup-dialog>
    </uni-popup>

    <!-- 兑换成功弹窗 -->
    <uni-popup ref="successPopup" type="dialog">
      <uni-popup-dialog
        type="success"
        title="兑换成功"
        :content="exchangeSuccessContent"
        :before-close="true"
        @confirm="viewRecord"
        @close="closeSuccessPopup"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getGoodsDetail, exchangeGoods } from '@/api/modules/points'
import { useUserStore } from '@/store/modules/user'
import defaultProductImage from '@/static/images/product/coffee.jpg'

const userStore = useUserStore()

// 响应式数据
const goodsId = ref('')
const myPoints = ref(0)
const goodsDetail = ref({
  images: [],
  name: '',
  points_price: 0,
  stock: 0,
  description: '',
  content: '',
  exchangeMethod: '',
  validPeriod: '',
  useInstructions: ''
})

// 弹窗引用
const exchangePopup = ref(null)
const successPopup = ref(null)

// 计算属性
const canExchange = computed(() => {
  return goodsDetail.value.stock > 0 && myPoints.value >= goodsDetail.value.points_price
})

// 获取商品图片
const getProductImage = (image) => {
  return image || defaultProductImage
}

const exchangeConfirmContent = computed(() => {
  return `确定使用${goodsDetail.value.points_price}积分兑换该商品吗？`
})

const exchangeSuccessContent = computed(() => {
  return `恭喜您成功兑换${goodsDetail.value.name}！\n您可以在"兑换记录"中查看详情。`
})

// 初始化页面
onLoad((options) => {
  goodsId.value = options.id
  initPage()
})

// 方法
const initPage = async () => {
  try {
    myPoints.value = userStore.userInfo.role_info.points
    await loadGoodsDetail()
  } catch (error) {
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  }
}

const loadGoodsDetail = async () => {
  try {
    const detail = await getGoodsDetail(goodsId.value)
    detail.images = []
    detail.images.push(detail.image)
    goodsDetail.value = detail
  } catch (error) {
    throw error
  }
}

const handleExchange = () => {
  if (!canExchange.value) {
    uni.showToast({
      title: goodsDetail.value.stock <= 0 ? '商品库存不足' : '积分不足',
      icon: 'error'
    })
    return
  }
  exchangePopup.value.open()
}

const confirmExchange = async () => {
  try {
    await exchangeGoods({
      goodsId: goodsId.value,
      points: goodsDetail.value.points_price
    })
    exchangePopup.value.close()
    successPopup.value.open()
    // 刷新商品详情和积分
    initPage()
  } catch (error) {
    uni.showToast({
      title: '兑换失败',
      icon: 'error'
    })
  }
}

const cancelExchange = () => {
  exchangePopup.value.close()
}

const viewRecord = () => {
  uni.navigateTo({
    url: '/pages/points/record'
  })
}

const closeSuccessPopup = () => {
  successPopup.value.close()
}
</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;

  .goods-swiper {
    width: 100%;
    height: 500rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .goods-info {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .name {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .price-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .points {
        color: #ff5a5f;
        font-size: 36rpx;
        font-weight: bold;
      }

      .stock {
        font-size: 24rpx;
        color: #999;

        &.low {
          color: #ff5a5f;
        }
      }
    }

    .desc {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
    }
  }

  .goods-detail {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 20rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 24rpx;
        background: #007AFF;
        border-radius: 3rpx;
      }
    }
  }

  .exchange-info {
    background: #fff;
    padding: 30rpx;

    .section-title {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 20rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 24rpx;
        background: #007AFF;
        border-radius: 3rpx;
      }
    }

    .info-item {
      margin-bottom: 20rpx;

      .label {
        font-size: 26rpx;
        color: #666;
      }

      .value {
        font-size: 26rpx;
        color: #333;
      }
    }
  }

  .bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100rpx;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    .points-info {
      flex: 1;

      .label {
        font-size: 24rpx;
        color: #666;
      }

      .value {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        margin-left: 10rpx;
      }
    }

    .exchange-btn {
      width: 240rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: #007AFF;
      color: #fff;
      font-size: 28rpx;
      border-radius: 40rpx;

      &.disabled {
        background: #ccc;
      }
    }
  }
}
</style> 