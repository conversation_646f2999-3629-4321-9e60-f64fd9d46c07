<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input type="text" placeholder="搜索笔记" @confirm="onSearch" />
      </view>
    </view>
    
    <!-- 课程筛选 -->
    <scroll-view class="course-filter" scroll-x>
      <view 
        class="filter-item" 
        :class="{ active: currentCourse === item.id }"
        v-for="item in courses" 
        :key="item.id"
        @click="switchCourse(item.id)"
      >
        {{ item.name }}
      </view>
    </scroll-view>
    
    <!-- 笔记列表 -->
    <scroll-view class="notes-list" scroll-y @scrolltolower="loadMore">
      <view class="note-item" v-for="note in notesList" :key="note.id" @click="viewNote(note)">
        <view class="note-header">
          <text class="course-name">{{ note.courseName }}</text>
          <text class="time">{{ note.createTime }}</text>
        </view>
        <view class="note-content">
          <text class="title">{{ note.title }}</text>
          <text class="preview">{{ note.content }}</text>
        </view>
        <view class="note-footer">
          <view class="tags">
            <text class="tag" v-for="tag in note.tags" :key="tag">{{ tag }}</text>
          </view>
          <view class="actions">
            <button class="action-btn" @click.stop="editNote(note)">
              <uni-icons type="compose" size="16" color="#666"></uni-icons>
            </button>
            <button class="action-btn" @click.stop="deleteNote(note)">
              <uni-icons type="trash" size="16" color="#666"></uni-icons>
            </button>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <uni-load-more :status="loadMoreStatus"></uni-load-more>
    </scroll-view>
    
    <!-- 新建笔记按钮 -->
    <view class="fab-button" @click="createNote">
      <uni-icons type="plusempty" size="24" color="#fff"></uni-icons>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import request from '@/utils/request'

const userStore = useUserStore()

// 课程列表
const courses = ref([{ id: 0, name: '全部' }])
const currentCourse = ref(0)

// 笔记列表
const notesList = ref([])
const page = ref(1)
const loadMoreStatus = ref('more')

// 获取课程列表
const getCourses = async () => {
  try {
    const response = await request({
      url: `/students/${userStore.userInfo.role_info.id}/courses/`,
      method: 'GET'
    })
    courses.value = [{ id: 0, name: '全部' }, ...response.results]
  } catch (error) {
    uni.showToast({
      title: '获取课程列表失败',
      icon: 'none'
    })
  }
}

// 获取笔记列表
const getNotes = async (refresh = false) => {
  if (refresh) {
    page.value = 1
    notesList.value = []
  }
  
  try {
    const classes = userStore.userInfo.role_info.classes  
    const classesStr = classes.map(item => item.id).join(',')
    const url = currentCourse.value ? `/notes/?course=${currentCourse.value}&classes=${classesStr}` : `/notes/?classes=${classesStr}` 
    loadMoreStatus.value = 'loading'
    const response = await request({
      url: url,
      method: 'GET',
      data: {
        page: page.value
      }
    })
    
    const { results, has_next } = response
    notesList.value = [...notesList.value, ...results]
    loadMoreStatus.value = has_next ? 'more' : 'noMore'
  } catch (error) {
    loadMoreStatus.value = 'more'
    uni.showToast({
      title: '获取笔记列表失败',
      icon: 'none'
    })
  }
}

// 切换课程
const switchCourse = (courseId) => {
  currentCourse.value = courseId
  getNotes(true)
}

// 搜索笔记
const onSearch = (e) => {
  const keyword = e.detail.value
  uni.navigateTo({
    url: `/pages/notes/search?keyword=${keyword}`
  })
}

// 加载更多
const loadMore = () => {
  if (loadMoreStatus.value === 'loading' || loadMoreStatus.value === 'noMore') return
  page.value++
  getNotes()
}

// 查看笔记
const viewNote = (note) => {
  uni.navigateTo({
    url: `/pages/notes/detail?id=${note.id}`
  })
}

// 编辑笔记
const editNote = (note) => {
  uni.navigateTo({
    url: `/pages/notes/edit?id=${note.id}`
  })
}

// 删除笔记
const deleteNote = async (note) => {
  try {
    const res = await uni.showModal({
      title: '提示',
      content: '确定要删除这条笔记吗？'
    })
    
    if (!res.confirm) {
      return
    }
    
    await request({
      url: `/notes/${note.id}/`,
      method: 'DELETE'
    })
    
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
    
    getNotes(true)
  } catch (error) {
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
  }
}

// 创建笔记
const createNote = () => {
  uni.navigateTo({
    url: '/pages/notes/edit'
  })
}

onMounted(() => {
  getCourses()
  getNotes()
})
</script>

<style lang="scss">
@import '@/styles/mixins.scss';

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.search-bar {
  padding: 20rpx;
  background: #fff;
  
  .search-input {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    padding: 10rpx 20rpx;
    border-radius: 30rpx;
    
    input {
      flex: 1;
      margin-left: 10rpx;
      font-size: 28rpx;
    }
  }
}

.course-filter {
  white-space: nowrap;
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  
  .filter-item {
    display: inline-block;
    padding: 10rpx 30rpx;
    margin-right: 20rpx;
    font-size: 28rpx;
    color: #666;
    background: #f5f5f5;
    border-radius: 30rpx;
    
    &.active {
      color: #fff;
      background: #3cc51f;
    }
    
    &:last-child {
      margin-right: 0;
    }
  }
}

.notes-list {
  flex: 1;
  padding: 20rpx;
  
  .note-item {
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    
    .note-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10rpx;
      
      .course-name {
        font-size: 24rpx;
        color: #3cc51f;
      }
      
      .time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .note-content {
      margin-bottom: 20rpx;
      
      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      .preview {
        font-size: 28rpx;
        color: #666;
        @include text-ellipsis(2);
      }
    }
    
    .note-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .tags {
        display: flex;
        flex-wrap: wrap;
        
        .tag {
          font-size: 24rpx;
          color: #666;
          background: #f5f5f5;
          padding: 4rpx 16rpx;
          border-radius: 20rpx;
          margin-right: 10rpx;
          margin-bottom: 10rpx;
        }
      }
      
      .actions {
        display: flex;
        
        .action-btn {
          background: none;
          padding: 10rpx;
          margin-left: 20rpx;
          
          &::after {
            border: none;
          }
        }
      }
    }
  }
}

.fab-button {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: #3cc51f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
</style> 