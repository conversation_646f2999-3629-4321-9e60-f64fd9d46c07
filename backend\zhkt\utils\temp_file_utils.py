import os
import uuid
import datetime
import shutil

def get_temp_dir():
    """
    获取项目临时目录路径，如果不存在则创建
    
    Returns:
        str: 临时目录的绝对路径
    """
    # 使用当前工作目录
    project_root = os.getcwd()
    temp_dir = os.path.join(project_root, 'temp')
    
    # 确保temp目录存在
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
        
    return temp_dir

def generate_temp_filename(suffix=''):
    """
    生成唯一的临时文件名
    
    Args:
        suffix (str): 文件扩展名，例如 '.txt', '.docx' 等
    
    Returns:
        str: 生成的唯一文件名
    """
    unique_id = uuid.uuid4().hex
    timestamp = datetime.datetime.now().strftime("%Y%m%d")
    return f"temp_{timestamp}_{unique_id}{suffix}"

def get_temp_filepath(suffix=''):
    """
    获取临时文件的完整路径
    
    Args:
        suffix (str): 文件扩展名，例如 '.txt', '.docx' 等
    
    Returns:
        str: 临时文件的完整路径
    """
    temp_dir = get_temp_dir()
    filename = generate_temp_filename(suffix)
    return os.path.join(temp_dir, filename)

def create_temp_subdir(prefix='output'):
    """
    在临时目录中创建一个唯一的子目录
    
    Args:
        prefix (str): 子目录名称前缀
    
    Returns:
        str: 创建的子目录的完整路径
    """
    temp_dir = get_temp_dir()
    unique_id = uuid.uuid4().hex
    timestamp = datetime.datetime.now().strftime("%Y%m%d")
    subdir_name = f"{prefix}_{timestamp}_{unique_id}"
    subdir_path = os.path.join(temp_dir, subdir_name)
    
    # 创建子目录
    os.makedirs(subdir_path, exist_ok=True)
    
    return subdir_path

def clean_temp_file(file_path):
    """
    安全地删除临时文件
    
    Args:
        file_path (str): 要删除的文件路径
    """
    if file_path and os.path.exists(file_path):
        try:
            os.unlink(file_path)
        except Exception as e:
            print(f"警告: 无法删除临时文件 {file_path}: {str(e)}")

def clean_temp_dir(dir_path):
    """
    安全地删除临时目录及其内容
    
    Args:
        dir_path (str): 要删除的目录路径
    """
    if dir_path and os.path.exists(dir_path):
        try:
            shutil.rmtree(dir_path)
        except Exception as e:
            print(f"警告: 无法删除临时目录 {dir_path}: {str(e)}")
