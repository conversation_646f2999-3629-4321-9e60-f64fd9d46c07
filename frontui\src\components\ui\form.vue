<template>
  <el-form
    :model="model"
    :rules="rules"
    :label-position="labelPosition"
    :label-width="labelWidth"
    :inline="inline"
    :size="size"
    :disabled="disabled"
    :validate-on-rule-change="validateOnRuleChange"
    @submit.prevent="$emit('submit')"
    v-bind="$attrs"
  >
    <slot />
  </el-form>
</template>

<script setup>
const props = defineProps({
  model: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    default: () => ({})
  },
  labelPosition: {
    type: String,
    default: 'right',
    validator: (val) => ['left', 'right', 'top'].includes(val)
  },
  labelWidth: {
    type: [String, Number],
    default: ''
  },
  inline: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'default',
    validator: (val) => ['large', 'default', 'small'].includes(val)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  validateOnRuleChange: {
    type: <PERSON>olean,
    default: true
  }
});

defineEmits(['submit']);
</script> 