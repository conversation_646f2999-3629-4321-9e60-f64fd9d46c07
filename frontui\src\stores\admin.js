import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import defaultAdminAvatar from '@/assets/images/avatars/user_avatar.jpg'
import { adminApi } from '@/api/admin'

export const useAdminStore = defineStore('admin', () => {

  const adminData = ref({
    name: '系统管理员',
    avatar: defaultAdminAvatar,
    role: 'admin',
    lastLogin: '2024-01-20 08:30:00'
  })
  const notifications = ref([])

  const unreadNotifications = computed(() => {
    return notifications.value.filter(n => !n.read).length
  })  

  async function fetchAdminInfo() {
    try {
      const response = await adminApi.getAdminInfo()
      
      // 更新用户数据
      adminData.value = {
        name: response.user.alias,
        avatar: response.user.avatar || defaultAdminAvatar,
        role: response.user.roles[0].name,
        lastLogin: response.user.last_login
      }
      
      return response
    } catch (error) {
      console.error('获取管理员信息失败:', error)
      throw error
    }
  }
 
  async function fetchSystemStatus() {
    // TODO: Implement API call to fetch system status
  }

  async function markNotificationAsRead(notificationId) {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }
  
  return {
    // State
    adminData,
    notifications,
    
    // Getters
    unreadNotifications,
    
    // Actions
    fetchAdminInfo,
    fetchSystemStatus,
    markNotificationAsRead
  }

}) 