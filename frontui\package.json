{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@docmee/sdk-ui": "^1.1.19", "@element-plus/icons-vue": "^2.3.1", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "boxen": "^8.0.1", "chalk": "^4.1.2", "chart.js": "^4.4.9", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "element-plus": "^2.9.7", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "highlight.js": "^11.11.1", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "markdown-it": "^14.1.0", "material-icons": "^1.13.14", "openai": "^4.89.0", "ora": "^8.2.0", "pinia": "^3.0.1", "qrcode": "^1.5.4", "reka-ui": "^2.2.0", "tw-animate-css": "^1.2.5", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-i18n": "^9.14.5", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.4", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.488.0", "postcss": "^8.5.3", "radix-vue": "^1.9.17", "shadcn-vue": "^2.0.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}