import traceback
from typing import List, Dict, Any, Optional
from ..entitys.knowledge import KnowledgeCategory, KnowledgeDataset, KnowledgeDocument, KnowledgeFavorite
import uuid
from django.utils import timezone
import os
from django.conf import settings
from django.db.models import Q

from ..utils.knowledge_utils import KnowledgeBaseAPI
from ..utils.file_utils import FileUtils

class KnowledgeService:
    """知识库服务类，提供知识库相关的业务逻辑"""

    @staticmethod
    def get_all_categories(category_type=None) -> List[Dict[str, Any]]:
        """
        获取所有知识库大分类，可按类型过滤
        
        Args:
            category_type: 可选，分类类型filter ('personal' 或 'knowledge')
            
        Returns:
            List[Dict[str, Any]]: 大分类列表，每个大分类包含id、name、icon、color等信息
        """
        try:
            # 构建基本查询
            query = KnowledgeCategory.objects.filter(status='1', deleted_at__isnull=True)
            
            # 如果指定了类型，添加过滤条件
            if category_type in ['personal', 'knowledge']:
                query = query.filter(category_type=category_type)
                
            # 按排序执行查询
            categories = query.order_by('sort_order')
            
            result = []
            
            for category in categories:
                # 计算该分类下的数据集数量（可能需要按用户过滤）
                dataset_count = KnowledgeDataset.objects.filter(
                    category=category,
                    status='1',
                    deleted_at__isnull=True
                ).count()
                
                result.append({
                    'id': category.id,
                    'name': category.name,
                    'value': category.id,  # 前端需要的value字段
                    'icon': category.icon or 'Document',  # 默认图标
                    'iconColor': category.color or 'text-blue-500',  # 默认颜色
                    'description': category.description,
                    'category_type': category.category_type,  # 添加类型字段
                    'badgeColor': 'bg-blue-100 text-blue-800',  # 默认徽章颜色
                    'count': dataset_count
                })
            
            return result
        except Exception as e:
            traceback.print_exc()
            raise e

    @staticmethod
    def get_datasets_by_category(category_id: int) -> List[Dict[str, Any]]:
        """
        获取指定大分类下的所有知识库数据集
        
        Args:
            category_id: 大分类ID
            
        Returns:
            List[Dict[str, Any]]: 数据集列表
        """
        try:
            # 确保category_id是整数类型
            if not isinstance(category_id, int):
                try:
                    category_id = int(category_id)
                except (ValueError, TypeError):
                    traceback.print_exc()
                    return []
            
            category = KnowledgeCategory.objects.get(id=category_id, status='1', deleted_at__isnull=True)
            datasets = KnowledgeDataset.objects.filter(
                category=category,
                status='1',
                deleted_at__isnull=True
            ).order_by('-created_at')
            
            result = []
            for dataset in datasets:
                # 计算数据集中的文档数量
                doc_count = KnowledgeDocument.objects.filter(
                    dataset=dataset,
                    status='1',
                    deleted_at__isnull=True
                ).count()
                
                result.append({
                    'id': dataset.id,
                    'name': dataset.name,
                    'description': dataset.description,
                    'avatar': dataset.avatar,
                    'icon': dataset.icon or 'Notebook',  # 返回图标，如果为空则使用默认值
                    'embedding_model': dataset.embedding_model,
                    'created_at': dataset.created_at,
                    'count': doc_count
                })
            
            return result
        except KnowledgeCategory.DoesNotExist:
            traceback.print_exc()
            return []
        except Exception as e:
            traceback.print_exc()
            raise e

    @classmethod
    def create_dataset(cls, category_id: int, name: str, description: str = None, 
                      avatar: str = None, embedding_model: str = "text-embedding-v3", 
                       icon: str = None, user_id: str = None) -> Dict[str, Any]:
        """
        创建新的知识库数据集，通过API创建，然后将返回的信息保存到本地数据库
        
        Args:
            category_id: 所属大分类ID
            name: 数据集名称
            description: 数据集描述
            avatar: 数据集头像（Base64编码）
            embedding_model: 使用的嵌入模型名称
            icon: 数据集图标，如果未提供则使用默认图标
            user_id: 所属用户ID
            
        Returns:
            Dict[str, Any]: 新创建的数据集信息
        """
        try:
            # 验证大分类是否存在
            category = KnowledgeCategory.objects.get(id=category_id, status='1', deleted_at__isnull=True)
            
            # 验证权限 - 如果是知识库类型，需要检查是否为老师
            # 注意：此检查需要在视图层中实现，这里只是提供接口
            
            # 通过API创建数据集
            api_response = KnowledgeBaseAPI().create_dataset(
                name=uuid.uuid4().hex,
                avatar=avatar,
                description=description,
                embedding_model=embedding_model
            )
            
            # 检查API响应
            if "code" in api_response and api_response["code"] != 0:
                traceback.print_exc()
                raise ValueError(f"知识库API创建数据集失败: {api_response.get('message', '未知错误')}")
            
            # 从API响应中获取数据集ID和其他信息
            api_data = api_response.get("data", {})
            dataset_id = api_data.get("id")
            
            if not dataset_id:
                traceback.print_exc()
                raise ValueError("知识库API未返回有效的数据集ID")
            
            # 创建日期时间
            current_time = timezone.now()
            
            # 如果未提供图标，则使用默认图标
            if not icon:
                icon = 'Notebook'  # 默认使用Notebook图标表示知识库
            
            # 创建本地数据库记录
            dataset = KnowledgeDataset(
                id=dataset_id,  # 使用API返回的ID
                category=category,
                user_id=user_id,  # 设置所属用户ID
                name=name,
                description=description,
                avatar=avatar,
                icon=icon,  # 设置图标
                embedding_model=embedding_model,
                status='1',
                created_at=current_time,
                updated_at=current_time
            )
            dataset.save()
            
            # 返回创建的数据集信息
            return {
                'id': dataset.id,
                'category_id': category_id,
                'category_type': category.category_type,
                'user_id': user_id,
                'name': dataset.name,
                'description': dataset.description,
                'avatar': dataset.avatar,
                'icon': dataset.icon,  # 增加返回图标信息
                'embedding_model': dataset.embedding_model,
                'created_at': dataset.created_at,
                'count': 0  # 新创建的数据集没有文档
            }
        except KnowledgeCategory.DoesNotExist:
            traceback.print_exc()
            raise ValueError(f"指定的大分类ID '{category_id}' 不存在")
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"创建数据集失败: {str(e)}")

    @staticmethod
    def get_documents_by_dataset(dataset_id: str, user_id: str, page=1, page_size=10) -> Dict[str, Any]:
        """
        获取指定数据集下的所有文档，支持分页
        
        Args:
            dataset_id: 数据集ID
            user_id: 用户ID（必需），用于权限检查和收藏状态
            page: 页码，从1开始
            page_size: 每页记录数
            
        Returns:
            Dict[str, Any]: 包含分页信息和文档列表的字典
        """
        try:
            # 验证数据集是否存在
            dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
            
            # 权限检查
            if dataset.category and dataset.category.category_type == 'personal':
                # 个人文档，只有创建者可以查看
                if dataset.user_id != user_id:
                    raise ValueError("您无权查看此数据集的文档")
            
            # 查询该数据集下的所有文档
            all_documents = KnowledgeDocument.objects.filter(
                dataset=dataset,
                status='1',
                deleted_at__isnull=True
            ).order_by('-created_at')
            
            # 计算总记录数
            total = all_documents.count()
            
            # 计算分页信息
            start = (page - 1) * page_size
            end = start + page_size
            
            # 获取当前页的文档
            documents = all_documents[start:end]
            
            result = []
            for doc in documents:
                # 检查文档是否已被当前用户收藏
                is_favorite = False
                if user_id:
                    is_favorite = KnowledgeFavorite.objects.filter(
                        user_id=user_id,
                        document_id=doc.id
                    ).exists()
                
                result.append({
                    'id': doc.id,
                    'name': doc.name,
                    'size': doc.size,
                    'type': doc.type,
                    'icon': doc.icon or KnowledgeService.get_document_icon(doc.type),  # 返回图标，如果为空则根据类型计算
                    'file_path': doc.file_path,  # 添加文件路径
                    'chunk_method': doc.chunk_method,
                    'chunk_count': doc.chunk_count,
                    'token_count': doc.token_count,
                    'process_duration': doc.process_duration,
                    'progress': doc.progress,
                    'run': doc.run,
                    'created_at': doc.created_at,
                    'is_favorite': is_favorite  # 添加收藏状态
                })
            
            # 返回分页信息和数据
            return {
                'list': result,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size  # 向上取整计算总页数
            }
        except KnowledgeDataset.DoesNotExist:
            traceback.print_exc()
            return {
                'list': [],
                'total': 0,
                'page': page,
                'page_size': page_size,
                'total_pages': 0
            }
        except Exception as e:
            traceback.print_exc()
            raise e

    @staticmethod
    def get_all_documents(user_id: str, page=1, page_size=10) -> Dict[str, Any]:
        """
        获取所有知识库文档，支持分页
        
        Args:
            user_id: 用户ID（必需），用于权限检查和收藏状态
            page: 页码，从1开始
            page_size: 每页记录数
            
        Returns:
            Dict[str, Any]: 包含分页信息和文档列表的字典
        """
        try:
            # 构建查询条件：包含所有知识库类型文档或用户自己的个人文档
            personal_datasets = KnowledgeDataset.objects.filter(
                user_id=user_id,
                category__category_type='personal',
                status='1',
                deleted_at__isnull=True
            ).values_list('id', flat=True)
            
            knowledge_datasets = KnowledgeDataset.objects.filter(
                category__category_type='knowledge',
                status='1',
                deleted_at__isnull=True
            ).values_list('id', flat=True)
            
            # 合并查询条件
            all_documents = KnowledgeDocument.objects.filter(
                (Q(dataset_id__in=personal_datasets) | Q(dataset_id__in=knowledge_datasets)),
                status='1',
                deleted_at__isnull=True
            ).select_related('dataset').order_by('-created_at')
            
            # 计算总记录数
            total = all_documents.count()
            
            # 计算分页信息
            start = (page - 1) * page_size
            end = start + page_size
            
            # 获取当前页的文档
            documents = all_documents[start:end]
            
            result = []
            for doc in documents:
                # 获取文档所属数据集和分类信息
                dataset_name = doc.dataset.name if doc.dataset else "未知数据集"
                category_name = doc.dataset.category.name if doc.dataset and doc.dataset.category else "未知分类"
                
                # 如果文档没有图标，则根据类型生成一个
                icon = doc.icon or KnowledgeService.get_document_icon(doc.type)
                
                # 检查文档是否已被当前用户收藏
                is_favorite = False
                if user_id:
                    is_favorite = KnowledgeFavorite.objects.filter(
                        user_id=user_id,
                        document_id=doc.id
                    ).exists()
                
                result.append({
                    'id': doc.id,
                    'name': doc.name,
                    'size': doc.size,
                    'type': doc.type,
                    'icon': icon,
                    'file_path': doc.file_path,  # 添加文件路径
                    'chunk_method': doc.chunk_method,
                    'chunk_count': doc.chunk_count,
                    'token_count': doc.token_count,
                    'process_duration': doc.process_duration,
                    'progress': doc.progress,
                    'run': doc.run,
                    'created_at': doc.created_at,
                    'dataset_id': doc.dataset.id if doc.dataset else None,
                    'dataset_name': dataset_name,
                    'category_id': doc.dataset.category.id if doc.dataset and doc.dataset.category else None,
                    'category_name': category_name,
                    'category_type': doc.dataset.category.category_type if doc.dataset and doc.dataset.category else "unknown",
                    'is_favorite': is_favorite
                })
            
            # 返回分页信息和数据
            return {
                'list': result,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size,  # 向上取整计算总页数
                'user_id': user_id  # 返回用户ID，方便前端显示
            }
        except Exception as e:
            traceback.print_exc()
            raise e

    @classmethod
    def upload_documents(cls, dataset_id: str, files: List, icon: str = None, user_id: str = None) -> Dict[str, Any]:
        """
        上传文档到指定数据集
        
        Args:
            dataset_id: 数据集ID
            files: 文件列表
            icon: 自定义图标名称(可选)
            user_id: 上传者用户ID，应该从request.user.id获取，不从前端传递
            
        Returns:
            Dict[str, Any]: 上传结果，包含上传的文档ID列表
        """
        try:
            # 验证数据集是否存在
            dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
            
            # 获取数据集所属的大分类
            category = dataset.category
            
            # 判断是否为个人文档还是知识库
            is_personal_doc = category and category.category_type == 'personal'
            
            # 验证权限 - 个人文档只能由所有者上传
            if is_personal_doc and user_id and dataset.user_id != user_id:
                raise ValueError("您无权在该分类下上传文档")
            
            # 保存文件到临时目录
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            
            file_paths = []  # 临时文件路径列表
            original_filenames = []  # 原始文件名列表
            oss_file_paths = []  # OSS存储路径列表
            
            for file in files:
                # 保存原始文件名
                original_filenames.append(file.name)
                
                # 文件名可能包含中文或特殊字符，这里生成一个安全的文件名
                ext = os.path.splitext(file.name)[1]
                safe_filename = f"{uuid.uuid4().hex}{ext}"
                temp_path = os.path.join(temp_dir, safe_filename)
                
                # 保存上传的文件到临时目录
                with open(temp_path, 'wb+') as destination:
                    for chunk in file.chunks():
                        destination.write(chunk)
                
                # 将文件保存到OSS
                try:
                    oss_path = FileUtils.save_knowledge_file(temp_path)
                    oss_file_paths.append(oss_path)
                except Exception as e:
                    # 如果OSS保存失败，记录错误但继续处理
                    traceback.print_exc()
                    oss_file_paths.append(None)
                    print(f"保存文件到OSS失败，将继续上传到知识库API: {str(e)}")
                
                file_paths.append(temp_path)
            
            # 通过API上传文档
            api_client = KnowledgeBaseAPI()
            api_response = api_client.upload_documents(dataset_id, file_paths)
            
            # 检查API响应
            if "code" in api_response and api_response["code"] != 0:
                # 清理临时文件
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                
                traceback.print_exc()
                raise ValueError(f"知识库API上传文档失败: {api_response.get('message', '未知错误')}")
            
            # 从API响应中获取上传的文档信息 - 修正此部分以匹配API实际返回格式
            api_data = api_response.get("data", [])
            
            # API返回的是文档对象列表，每个对象包含id
            document_ids = []
            if isinstance(api_data, list):
                document_ids = [doc.get("id") for doc in api_data if doc.get("id")]
            
            # 清理临时文件
            for file_path in file_paths:
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            # 为每个上传的文档创建本地数据库记录
            created_documents = []
            current_time = timezone.now()
            
            for i, doc_id in enumerate(document_ids):
                if i < len(files):  # 确保有对应的文件
                    file = files[i]
                    file_type = os.path.splitext(file.name)[1][1:].lower()  # 去掉点号的扩展名
                    
                    # 获取OSS文件路径（如果存在）
                    file_path = oss_file_paths[i] if i < len(oss_file_paths) else None
                    
                    # 优先使用用户传入的图标，如果没有则根据文件类型确定图标
                    doc_icon = icon if icon else cls.get_document_icon(file_type)
                    
                    # 创建文档记录
                    document = KnowledgeDocument(
                        id=doc_id,
                        dataset=dataset,
                        user_id=user_id,  # 设置文档所有者
                        name=file.name,
                        size=file.size,
                        type=file_type,
                        file_path=file_path,  # 添加文件在OSS的存储路径
                        icon=doc_icon,  # 使用用户选择的或根据类型推断的图标
                        status='1',
                        progress=0,  # 初始进度为0
                        run="UNSTART",  # 初始状态为未解析，使用UNSTART字符串而不是布尔值
                        created_at=current_time,
                        updated_at=current_time
                    )
                    document.save()
                    
                    created_documents.append({
                        'id': doc_id,
                        'name': file.name,
                        'size': file.size,
                        'type': file_type,
                        'icon': doc_icon,
                        'file_path': file_path,  # 返回文件路径
                    })
            
            # 返回上传结果
            return {
                'document_ids': document_ids,
                'documents': created_documents,
                'count': len(document_ids)
            }
        except KnowledgeDataset.DoesNotExist:
            traceback.print_exc()
            raise ValueError(f"指定的数据集ID '{dataset_id}' 不存在")
        except Exception as e:
            # 确保清理临时文件
            for file_path in file_paths if 'file_paths' in locals() else []:
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            traceback.print_exc()
            raise Exception(f"上传文档失败: {str(e)}")
    
    @staticmethod
    def get_document_icon(file_type: str) -> str:
        """
        根据文件类型获取对应的图标名称
        
        Args:
            file_type: 文件类型扩展名，如pdf、docx等
            
        Returns:
            str: 图标名称
        """
        # 文件类型到图标的映射
        icon_mapping = {
            # 文档类型
            'pdf': 'Document',
            'doc': 'Document',
            'docx': 'Document',
            'txt': 'Document',
            'md': 'Document',
            
            # 表格类型
            'xls': 'Collection',
            'xlsx': 'Collection',
            'csv': 'Collection',
            
            # 演示文稿
            'ppt': 'Monitor',
            'pptx': 'Monitor',
            
            # 图像类型
            'jpg': 'Picture',
            'jpeg': 'Picture',
            'png': 'Picture',
            'gif': 'Picture',
            
            # 代码类型
            'py': 'Cpu',
            'js': 'Cpu',
            'java': 'Cpu',
            'c': 'Cpu',
            'cpp': 'Cpu',
            
            # 链接类型
            'url': 'Link',
            'html': 'Link',
            'htm': 'Link'
        }
        
        # 返回对应的图标，如果没有则返回默认值
        return icon_mapping.get(file_type.lower(), 'Document')

    @classmethod
    def parse_documents(cls, dataset_id: str, document_ids: List[str]) -> Dict[str, Any]:
        """
        解析指定数据集中的文档
        
        Args:
            dataset_id: 数据集ID
            document_ids: 要解析的文档ID列表
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            # 验证数据集是否存在
            dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
            
            # 验证文档ID是否存在
            documents = []
            for doc_id in document_ids:
                try:
                    doc = KnowledgeDocument.objects.get(id=doc_id, dataset=dataset, status='1', deleted_at__isnull=True)
                    documents.append(doc)
                except KnowledgeDocument.DoesNotExist:
                    raise ValueError(f"文档ID '{doc_id}' 不存在或不属于指定数据集")
            
            # 通过API解析文档
            api_client = KnowledgeBaseAPI()
            api_response = api_client.parse_documents(dataset_id, document_ids)
            
            # 检查API响应
            if "code" in api_response and api_response["code"] != 0:
                traceback.print_exc()
                raise ValueError(f"知识库API解析文档失败: {api_response.get('message', '未知错误')}")
            
            # 从API响应中获取任务ID
            api_data = api_response.get("data", {})
            task_id = api_data.get("task_id")
            
            # 更新文档状态为解析中
            current_time = timezone.now()
            for doc in documents:
                doc.run = "RUNNING"  # 修改为字符串类型，与前端匹配
                doc.progress = 0
                doc.updated_at = current_time
                doc.save()
            
            # 返回解析结果
            return {
                'task_id': task_id,
                'document_ids': document_ids,
                'dataset_id': dataset_id
            }
        except KnowledgeDataset.DoesNotExist:
            traceback.print_exc()
            raise ValueError(f"指定的数据集ID '{dataset_id}' 不存在")
        except KnowledgeDocument.DoesNotExist as e:
            traceback.print_exc()
            raise e
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"解析文档失败: {str(e)}")

    @classmethod
    def batch_update_document_status(cls, dataset_id: str, document_ids: List[str] = None) -> List[Dict[str, Any]]:
        """
        批量更新文档解析状态
        
        Args:
            dataset_id: 数据集ID
            document_ids: 文档ID列表，如果为None则更新数据集中所有文档
            
        Returns:
            List[Dict[str, Any]]: 更新后的文档状态列表
        """
        try:
            # 调用API获取文档状态
            api_client = KnowledgeBaseAPI()
            api_response = api_client.get_documents_status(dataset_id, document_ids)
            
            # 检查API响应
            if "code" in api_response and api_response["code"] != 0:
                raise ValueError(f"批量获取文档状态失败: {api_response.get('message', '未知错误')}")
            
            # 提取文档列表
            api_data = api_response.get("data", {})
            api_docs = api_data.get("docs", [])
            
            # 更新结果列表
            updated_docs = []
            current_time = timezone.now()
            
            # 遍历API返回的文档
            for api_doc in api_docs:
                doc_id = api_doc.get("id")
                if not doc_id:
                    continue
                
                try:
                    # 查询本地文档
                    document = KnowledgeDocument.objects.get(id=doc_id, dataset_id=dataset_id, status='1', deleted_at__isnull=True)
                    
                    # 更新文档状态
                    document.run = api_doc.get("run", document.run)
                    document.progress = float(api_doc.get("progress", document.progress))
                    document.chunk_count = int(api_doc.get("chunk_count", document.chunk_count))
                    document.token_count = int(api_doc.get("token_count", document.token_count))
                    document.process_duration = float(api_doc.get("process_duation", document.process_duration))
                    document.updated_at = current_time
                    document.save()
                    
                    # 添加到结果列表
                    updated_docs.append({
                        'id': document.id,
                        'name': document.name,
                        'dataset_id': dataset_id,
                        'run': document.run,
                        'progress': document.progress,
                        'chunk_count': document.chunk_count,
                        'token_count': document.token_count,
                        'process_duration': document.process_duration,
                        'updated_at': document.updated_at
                    })
                except KnowledgeDocument.DoesNotExist:
                    # 跳过不存在的文档
                    continue
            
            return updated_docs
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"批量更新文档状态失败: {str(e)}")

    @staticmethod
    def get_personal_datasets(user_id: str) -> List[Dict[str, Any]]:
        """
        获取指定用户的个人文档子分类
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict[str, Any]]: 个人文档子分类列表
        """
        try:
            # 先获取"个人文档"大分类
            personal_category = KnowledgeCategory.objects.filter(
                category_type='personal', 
                status='1', 
                deleted_at__isnull=True
            ).first()
            
            if not personal_category:
                return []
                
            # 查询该用户在个人文档大分类下的所有子分类
            datasets = KnowledgeDataset.objects.filter(
                category=personal_category,
                user_id=user_id,
                status='1',
                deleted_at__isnull=True
            ).order_by('-created_at')
            
            result = []
            for dataset in datasets:
                # 计算数据集中的文档数量
                doc_count = KnowledgeDocument.objects.filter(
                    dataset=dataset,
                    status='1',
                    deleted_at__isnull=True
                ).count()
                
                result.append({
                    'id': dataset.id,
                    'name': dataset.name,
                    'description': dataset.description,
                    'avatar': dataset.avatar,
                    'icon': dataset.icon or 'Notebook',  # 返回图标，如果为空则使用默认值
                    'embedding_model': dataset.embedding_model,
                    'created_at': dataset.created_at,
                    'category_id': personal_category.id,
                    'category_type': 'personal',
                    'count': doc_count
                })
            
            return result
        except Exception as e:
            traceback.print_exc()
            return []

    @classmethod
    def toggle_document_favorite(cls, user_id: str, document_id: str) -> Dict[str, Any]:
        """
        切换文档收藏状态（如果已收藏则取消，未收藏则添加）
        
        Args:
            user_id: 用户ID
            document_id: 文档ID
            
        Returns:
            Dict[str, Any]: 包含收藏状态的响应
        """
        try:
            # 验证文档是否存在
            try:
                document = KnowledgeDocument.objects.get(id=document_id, status='1', deleted_at__isnull=True)
            except KnowledgeDocument.DoesNotExist:
                raise ValueError(f"文档ID '{document_id}' 不存在")
                
            # 检查是否已收藏
            try:
                favorite = KnowledgeFavorite.objects.get(user_id=user_id, document=document)
                # 如果存在则删除（取消收藏）
                favorite.delete()
                is_favorite = False
                favorite_id = None
            except KnowledgeFavorite.DoesNotExist:
                # 如果不存在则创建（添加收藏）
                favorite = KnowledgeFavorite(
                    user_id=user_id,
                    document=document
                )
                favorite.save()
                is_favorite = True
                favorite_id = favorite.id
                
            return {
                'document_id': document_id,
                'is_favorite': is_favorite,
                'favorite_id': favorite_id,
                'message': "取消收藏成功" if not is_favorite else "收藏成功"
            }
        except ValueError as e:
            traceback.print_exc()
            raise e
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"操作收藏失败: {str(e)}")
            
    @staticmethod
    def is_document_favorite(user_id: str, document_id: str) -> bool:
        """
        检查文档是否被用户收藏
        
        Args:
            user_id: 用户ID
            document_id: 文档ID
            
        Returns:
            bool: 是否已收藏
        """
        try:
            return KnowledgeFavorite.objects.filter(
                user_id=user_id,
                document_id=document_id
            ).exists()
        except Exception:
            return False
            
    @staticmethod
    def get_user_favorites(user_id: str, page=1, page_size=10) -> Dict[str, Any]:
        """
        获取用户收藏的所有文档
        
        Args:
            user_id: 用户ID
            page: 页码
            page_size: 每页记录数
            
        Returns:
            Dict[str, Any]: 包含收藏文档列表和分页信息的字典
        """
        try:
            # 查询用户的所有收藏
            favorites = KnowledgeFavorite.objects.filter(
                user_id=user_id
            ).select_related('document', 'document__dataset', 'document__dataset__category').order_by('-created_at')
            
            # 计算总记录数
            total = favorites.count()
            
            # 分页
            start = (page - 1) * page_size
            end = start + page_size
            paged_favorites = favorites[start:end]
            
            result = []
            for favorite in paged_favorites:
                doc = favorite.document
                if not doc or doc.status != '1' or doc.deleted_at:
                    continue
                    
                # 获取文档关联的数据集和分类信息
                dataset = doc.dataset
                dataset_name = dataset.name if dataset else "未知数据集"
                category = dataset.category if dataset else None
                category_name = category.name if category else "未知分类"
                category_type = category.category_type if category else "unknown"
                
                # 如果文档没有图标，则根据类型生成一个
                icon = doc.icon or KnowledgeService.get_document_icon(doc.type)
                
                result.append({
                    'id': doc.id,
                    'name': doc.name,
                    'size': doc.size,
                    'type': doc.type,
                    'icon': icon,
                    'file_path': doc.file_path,  # 添加文件路径
                    'run': doc.run,
                    'created_at': doc.created_at,
                    'dataset_id': dataset.id if dataset else None,
                    'dataset_name': dataset_name,
                    'category_id': category.id if category else None,
                    'category_name': category_name,
                    'category_type': category_type,
                    'favorite_id': favorite.id,
                    'favorite_created_at': favorite.created_at
                })
                
            # 返回分页信息和数据
            return {
                'list': result,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size  # 向上取整计算总页数
            }
        except Exception as e:
            traceback.print_exc()
            raise e

    @classmethod
    def batch_delete_documents(cls, dataset_id: str, document_ids: List[str], user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        批量删除文档，也可用于删除单个文档(传入单元素列表)
        
        Args:
            dataset_id: 数据集ID
            document_ids: 要删除的文档ID列表，可以是单个文档ID的列表
            user_id: 当前用户ID，用于权限验证
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            if not document_ids:
                return {
                    'success': False,
                    'message': '未提供要删除的文档ID'
                }
                
            # 验证数据集是否存在
            try:
                dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
            except KnowledgeDataset.DoesNotExist:
                raise ValueError(f"指定的数据集ID '{dataset_id}' 不存在")
            
            # 获取这些文档
            documents = KnowledgeDocument.objects.filter(
                id__in=document_ids, 
                dataset=dataset, 
                status='1', 
                deleted_at__isnull=True
            )
            
            # 检查是否找到所有文档
            found_ids = [str(doc.id) for doc in documents]
            not_found_ids = [doc_id for doc_id in document_ids if doc_id not in found_ids]
            
            if not_found_ids:
                raise ValueError(f"以下文档ID不存在或不属于此数据集: {', '.join(not_found_ids)}")
            
            # 权限检查：如果是个人文档，验证当前用户是否是所有者
            if dataset.category and dataset.category.category_type == 'personal' and user_id:
                unauthorized_docs = documents.exclude(user_id=user_id)
                if unauthorized_docs.exists():
                    raise ValueError("您没有权限删除某些文档")
            
            # 调用API批量删除知识库中的文档
            api_client = KnowledgeBaseAPI()
            api_response = api_client.delete_documents(dataset_id, document_ids)
            
            # 检查API响应
            # if "code" in api_response and api_response["code"] != 0:
            #     raise ValueError(f"知识库API批量删除文档失败: {api_response.get('message', '未知错误')}")
            
            # 更新文档状态为已删除
            current_time = timezone.now()
            
            deleted_count = 0
            for document in documents:
                document.status = '0'  # 标记为已删除
                document.deleted_at = current_time
                document.updated_at = current_time
                document.save()
                deleted_count += 1
            
            # 清理关联的收藏记录
            KnowledgeFavorite.objects.filter(document_id__in=document_ids).delete()
            
            # 如果只删除了一个文档，返回单个删除的消息
            if deleted_count == 1:
                return {
                    'success': True,
                    'count': 1,
                    'deleted_ids': found_ids,
                    'message': '文档删除成功'
                }
            else:
                return {
                    'success': True,
                    'count': deleted_count,
                    'deleted_ids': found_ids,
                    'message': f'成功删除 {deleted_count} 个文档'
                }
            
        except ValueError as e:
            traceback.print_exc()
            raise e
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"批量删除文档失败: {str(e)}")

    @classmethod
    def delete_dataset(cls, dataset_id: str, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        删除数据集
        
        Args:
            dataset_id: 数据集ID
            user_id: 当前用户ID，用于权限验证
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            # 验证数据集是否存在
            try:
                dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
            except KnowledgeDataset.DoesNotExist:
                raise ValueError(f"指定的数据集ID '{dataset_id}' 不存在")
            
            # 权限检查：如果是个人文档，验证当前用户是否是所有者
            if dataset.category and dataset.category.category_type == 'personal' and user_id:
                if dataset.user_id != user_id:
                    raise ValueError("您没有权限删除此数据集")
            
            # 调用API删除远程知识库数据集
            api_client = KnowledgeBaseAPI()
            api_response = api_client.delete_datasets([dataset_id])
            
            # 检查API响应
            # if "code" in api_response and api_response["code"] != 0:
            #     raise ValueError(f"知识库API删除数据集失败: {api_response.get('message', '未知错误')}")
            
            # 更新数据集状态为已删除
            current_time = timezone.now()
            dataset.status = '0'  # 标记为已删除
            dataset.deleted_at = current_time
            dataset.updated_at = current_time
            dataset.save()
            
            # 更新数据集内所有文档的状态为已删除
            documents = KnowledgeDocument.objects.filter(
                dataset=dataset,
                status='1',
                deleted_at__isnull=True
            )
            
            deleted_doc_count = 0
            for document in documents:
                document.status = '0'  # 标记为已删除
                document.deleted_at = current_time
                document.updated_at = current_time
                document.save()
                deleted_doc_count += 1
            
            # 清理关联的收藏记录
            document_ids_list = [doc.id for doc in documents]
            KnowledgeFavorite.objects.filter(document_id__in=document_ids_list).delete()
            
            return {
                'success': True,
                'dataset_id': dataset_id,
                'dataset_name': dataset.name,
                'deleted_doc_count': deleted_doc_count,
                'message': f'数据集"{dataset.name}"删除成功，同时删除了{deleted_doc_count}个相关文档'
            }
            
        except ValueError as e:
            traceback.print_exc()
            raise e
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"删除数据集失败: {str(e)}")
            
    @classmethod
    def update_dataset(cls, dataset_id: str, data: Dict[str, Any], user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        更新数据集信息
        
        Args:
            dataset_id: 数据集ID
            data: 要更新的数据集字段，可包含name, description, icon等
            user_id: 当前用户ID，用于权限验证
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            # 验证数据集是否存在
            try:
                dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
            except KnowledgeDataset.DoesNotExist:
                raise ValueError(f"指定的数据集ID '{dataset_id}' 不存在")
            
            # 权限检查：如果是个人文档，验证当前用户是否是所有者
            if dataset.category and dataset.category.category_type == 'personal' and user_id:
                if dataset.user_id != user_id:
                    raise ValueError("您没有权限修改此数据集")
            
            # 验证传入的数据
            if not data:
                raise ValueError("未提供要更新的数据")
            
            # 记录原始值，用于返回变更信息
            original_name = dataset.name
            
            # 更新数据集字段
            updated_fields = []
            
            if 'name' in data and data['name'] and data['name'] != dataset.name:
                dataset.name = data['name']
                updated_fields.append('name')
            
            if 'description' in data and data['description'] != dataset.description:
                dataset.description = data['description']
                updated_fields.append('description')
            
            if 'icon' in data and data['icon'] and data['icon'] != dataset.icon:
                dataset.icon = data['icon']
                updated_fields.append('icon')
            
            if 'avatar' in data and data['avatar'] != dataset.avatar:
                dataset.avatar = data['avatar']
                updated_fields.append('avatar')
            
            # 如果没有字段需要更新，返回提示
            if not updated_fields:
                return {
                    'success': False,
                    'message': '没有字段需要更新',
                    'dataset': {
                        'id': dataset.id,
                        'name': dataset.name,
                        'description': dataset.description,
                        'icon': dataset.icon,
                        'category_id': dataset.category.id if dataset.category else None,
                        'category_type': dataset.category.category_type if dataset.category else None
                    }
                }
            
            # 更新时间
            dataset.updated_at = timezone.now()
            dataset.save()
            
            # 返回更新结果
            return {
                'success': True,
                'updated_fields': updated_fields,
                'dataset': {
                    'id': dataset.id,
                    'name': dataset.name,
                    'description': dataset.description,
                    'icon': dataset.icon,
                    'category_id': dataset.category.id if dataset.category else None,
                    'category_type': dataset.category.category_type if dataset.category else None
                },
                'message': f'数据集"{original_name}"更新成功'
            }
            
        except ValueError as e:
            traceback.print_exc()
            raise e
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"更新数据集失败: {str(e)}")

    @staticmethod
    def search_documents(search_query: str, user_id: str, page=1, page_size=10, dataset_id=None, document_type=None) -> Dict[str, Any]:
        """
        搜索知识库文档
        
        Args:
            search_query: 搜索关键词
            user_id: 用户ID（必需），用于权限检查和收藏状态
            page: 页码，从1开始
            page_size: 每页记录数
            dataset_id: 可选，限定搜索特定数据集
            document_type: 可选，限定文档类型
            
        Returns:
            Dict[str, Any]: 包含分页信息和文档列表的字典
        """
        try:
            # 构建权限查询条件：只能搜索知识库类型文档或用户自己的个人文档
            personal_datasets = KnowledgeDataset.objects.filter(
                user_id=user_id,
                category__category_type='personal',
                status='1',
                deleted_at__isnull=True
            ).values_list('id', flat=True)
            
            knowledge_datasets = KnowledgeDataset.objects.filter(
                category__category_type='knowledge',
                status='1',
                deleted_at__isnull=True
            ).values_list('id', flat=True)
            
            # 构建基础查询
            query = KnowledgeDocument.objects.filter(
                (Q(dataset_id__in=personal_datasets) | Q(dataset_id__in=knowledge_datasets)),
                status='1',
                deleted_at__isnull=True
            ).select_related('dataset', 'dataset__category')
            
            # 添加搜索条件
            if search_query:
                query = query.filter(
                    Q(name__icontains=search_query) |  # 文档名称包含搜索词
                    Q(dataset__name__icontains=search_query)  # 所属数据集名称包含搜索词
                )
            
            # 按数据集过滤
            if dataset_id:
                # 如果指定了数据集，需要检查用户是否有权限查看该数据集
                try:
                    dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
                    # 如果是个人文档类型，检查是否属于当前用户
                    if dataset.category and dataset.category.category_type == 'personal' and dataset.user_id != user_id:
                        raise ValueError("您无权查看此数据集的文档")
                    query = query.filter(dataset_id=dataset_id)
                except KnowledgeDataset.DoesNotExist:
                    raise ValueError(f"指定的数据集ID '{dataset_id}' 不存在")
            
            # 按文档类型过滤
            if document_type:
                query = query.filter(type=document_type)
                
            # 按修改时间排序
            query = query.order_by('-created_at')
            
            # 计算总记录数
            total = query.count()
            
            # 分页处理
            start = (page - 1) * page_size
            end = start + page_size
            documents = query[start:end]
            
            # 处理结果
            result = []
            for doc in documents:
                # 获取文档所属数据集和分类信息
                dataset_name = doc.dataset.name if doc.dataset else "未知数据集"
                category_name = doc.dataset.category.name if doc.dataset and doc.dataset.category else "未知分类"
                category_type = doc.dataset.category.category_type if doc.dataset and doc.dataset.category else "unknown"
                
                # 检查文档是否已被当前用户收藏
                is_favorite = False
                if user_id:
                    is_favorite = KnowledgeFavorite.objects.filter(
                        user_id=user_id,
                        document_id=doc.id
                    ).exists()
                
                # 如果文档没有图标，则根据类型生成一个
                icon = doc.icon or KnowledgeService.get_document_icon(doc.type)
                
                result.append({
                    'id': doc.id,
                    'name': doc.name,
                    'size': doc.size,
                    'type': doc.type,
                    'icon': icon,
                    'file_path': doc.file_path,  # 添加文件路径
                    'chunk_method': doc.chunk_method,
                    'chunk_count': doc.chunk_count,
                    'token_count': doc.token_count,
                    'process_duration': doc.process_duration,
                    'progress': doc.progress,
                    'run': doc.run,
                    'created_at': doc.created_at,
                    'dataset_id': doc.dataset.id if doc.dataset else None,
                    'dataset_name': dataset_name,
                    'category_id': doc.dataset.category.id if doc.dataset and doc.dataset.category else None,
                    'category_name': category_name,
                    'category_type': category_type,
                    'is_favorite': is_favorite
                })
            
            # 返回分页信息和数据
            return {
                'list': result,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size,  # 向上取整计算总页数
                'search_query': search_query  # 返回搜索关键词，方便前端显示
            }
        except Exception as e:
            traceback.print_exc()
            raise e

    @staticmethod
    def get_document_file_url(document_id: str, user_id: str = None) -> Dict[str, Any]:
        """
        获取知识库文档的文件URL
        
        Args:
            document_id: 文档ID
            user_id: 用户ID，用于权限验证
            
        Returns:
            Dict[str, Any]: 包含文件URL的响应
        """
        try:
            # 查询文档
            document = KnowledgeDocument.objects.get(id=document_id, status='1', deleted_at__isnull=True)
            
            # 如果是个人文档，验证权限
            if document.dataset and document.dataset.category.category_type == 'personal':
                if not user_id or str(document.user_id) != str(user_id):
                    raise ValueError("您无权访问此文档")
            
            # 检查文件路径是否存在
            if not document.file_path:
                raise ValueError("此文档没有关联的文件")
            
            # 获取文件URL
            file_url = FileUtils.get_file_url(document.file_path, full_url=True)
            
            # 返回文件信息
            return {
                'success': True,
                'document_id': document_id,
                'name': document.name,
                'type': document.type,
                'size': document.size,
                'file_url': file_url
            }
        except KnowledgeDocument.DoesNotExist:
            raise ValueError(f"文档ID '{document_id}' 不存在")
        except Exception as e:
            traceback.print_exc()
            raise ValueError(f"获取文件URL失败: {str(e)}")


    @staticmethod
    def get_datasets_by_documents(document_ids: List[str]) -> List[str]:
        """
        根据文档ID列表获取关联的数据集ID列表

        Args:
            document_ids: 文档ID列表

        Returns:
            List[str]: 数据集ID列表
        """
        try:
            # 通过Django ORM查询文档所属的数据集ID
            dataset_ids = KnowledgeDocument.objects.filter(
                id__in=document_ids,
                status='1',
                deleted_at__isnull=True
            ).values_list('dataset_id', flat=True).distinct()

            print(f"从文档获取的数据集IDs: {list(dataset_ids)}")

            # 转换为列表并返回
            return list(dataset_ids)
        except Exception as e:
            traceback.print_exc()
            print(f"获取文档关联数据集时出错: {str(e)}")
            # 出错时返回空列表
            return []
            return []