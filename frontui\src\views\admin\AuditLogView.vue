<template>
  <AdminLayout 
    pageTitle="日志审计" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <!-- 搜索和过滤 -->
        <el-card class="mb-4">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <el-input
              v-model="search.keyword"
              placeholder="搜索关键字"
            />
            <el-select
              v-model="search.type"
              placeholder="日志类型"
              clearable
            >
              <el-option
                v-for="item in logTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-select
              v-model="search.level"
              placeholder="日志级别"
              clearable
            >
              <el-option
                v-for="item in logLevels"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-date-picker
              v-model="search.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </div>
        </el-card>

        <!-- 日志列表 -->
        <el-card>
          <el-table :data="logs" style="width: 100%">
            <el-table-column prop="timestamp" label="时间" width="180" />
            <el-table-column prop="type" label="类型" width="120" />
            <el-table-column prop="level" label="级别" width="100">
              <template #default="{ row }">
                <el-tag :type="getLogLevelType(row.level)">
                  {{ row.level }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="user" label="操作用户" width="120" />
            <el-table-column prop="ip" label="IP地址" width="140" />
            <el-table-column prop="action" label="操作" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="详情" width="80">
              <template #default="{ row }">
                <el-button type="primary" link @click="showDetails(row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="flex justify-center mt-4">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
            />
          </div>
        </el-card>

        <!-- 日志详情对话框 -->
        <el-dialog
          v-model="detailsVisible"
          title="日志详情"
          width="60%"
        >
          <div v-if="selectedLog" class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-gray-600">时间</p>
                <p>{{ selectedLog.timestamp }}</p>
              </div>
              <div>
                <p class="text-gray-600">操作用户</p>
                <p>{{ selectedLog.user }}</p>
              </div>
              <div>
                <p class="text-gray-600">IP地址</p>
                <p>{{ selectedLog.ip }}</p>
              </div>
              <div>
                <p class="text-gray-600">操作类型</p>
                <p>{{ selectedLog.type }}</p>
              </div>
            </div>
            <div>
              <p class="text-gray-600">操作详情</p>
              <pre class="bg-gray-100 p-4 rounded mt-2">{{ selectedLog.details }}</pre>
            </div>
          </div>
        </el-dialog>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref } from 'vue'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import { useAdminStore } from '@/stores/admin'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

const router = useRouter()
const adminStore = useAdminStore()
const authStore = useAuthStore()

// 搜索条件
const search = ref({
  keyword: '',
  type: '',
  level: '',
  dateRange: []
})

// 日志类型选项
const logTypes = [
  { value: 'login', label: '登录' },
  { value: 'operation', label: '操作' },
  { value: 'system', label: '系统' },
  { value: 'security', label: '安全' }
]

// 日志级别选项
const logLevels = [
  { value: 'info', label: '信息' },
  { value: 'warning', label: '警告' },
  { value: 'error', label: '错误' }
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 模拟日志数据
const logs = ref([
  {
    timestamp: '2024-03-15 10:00:00',
    type: '登录',
    level: 'info',
    user: 'admin',
    ip: '*************',
    action: '用户登录系统',
    status: 'success',
    details: '管理员成功登录系统\nIP: *************\n浏览器: Chrome\n操作系统: Windows'
  },
  {
    timestamp: '2024-03-15 10:30:00',
    type: '操作',
    level: 'warning',
    user: 'admin',
    ip: '*************',
    action: '删除用户',
    status: 'success',
    details: '删除用户操作\n用户ID: 1001\n用户名: test_user\n操作原因: 违规行为'
  }
])

// 日志详情对话框
const detailsVisible = ref(false)
const selectedLog = ref(null)

// 根据日志级别返回对应的标签类型
const getLogLevelType = (level) => {
  const types = {
    info: 'info',
    warning: 'warning',
    error: 'danger'
  }
  return types[level] || 'info'
}

// 显示日志详情
const showDetails = (log) => {
  selectedLog.value = log
  detailsVisible.value = true
}
</script> 