from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from ..config import LANGUAGE_CHOICES, DEFAULT_LANGUAGE

class Course(models.Model):
    """课程模型"""
    COURSE_TYPE_CHOICES = (
        ('REQUIRED', '必修课'),
        ('ELECTIVE', '选修课'),
    )
    
    STATUS_CHOICES = (
        ('published', '已上架'),
        ('archived', '已下架'),
    )
    
    name = models.CharField(_('课程名称'), max_length=100)
    code = models.CharField(_('课程编码'), max_length=20, unique=True)
    course_type = models.CharField(_('课程类型'), max_length=20, choices=COURSE_TYPE_CHOICES)
    teacher = models.ForeignKey('Teacher', on_delete=models.CASCADE, related_name='courses', verbose_name=_('授课教师'))
    major = models.ManyToManyField('Major', related_name='courses', verbose_name=_('适用专业'))
    class_groups = models.ManyToManyField('ClassGroup', related_name='courses', verbose_name=_('开课班级'), blank=True)
    description = models.TextField(_('课程描述'), blank=True)
    pointsCost = models.PositiveIntegerField(_('积分'), default=0)
    total_hours = models.PositiveIntegerField(_('总学时'), default=0)
    cover_image = models.CharField(_('封面图片'), max_length=500, blank=True)
    plan_count = models.PositiveIntegerField(_('计划人数'), default=0)
    status = models.CharField(_('课程状态'), max_length=20, choices=STATUS_CHOICES, default='archived')
    average_rating = models.FloatField(_('平均评分'), default=0.0, validators=[MinValueValidator(0.0), MaxValueValidator(5.0)])
    rating_count = models.PositiveIntegerField(_('评分人数'), default=0)
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='课程的语言')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('课程')
        verbose_name_plural = _('课程')
        ordering = ['-created_at']

    def __str__(self):
        return self.name

class Chapter(models.Model):
    """课程章节模型"""
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='chapters', null=True, blank=True)
    title = models.CharField(_('章节标题'), max_length=200)
    description = models.TextField(_('章节描述'), blank=True)
    order = models.IntegerField(_('排序'))
    duration = models.IntegerField(_('视频时长(秒)'), default=0)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('章节')
        verbose_name_plural = _('章节')
        ordering = ['order']

    def __str__(self):
        return f"{self.course.name if self.course else '未关联课程'} - {self.title}"

class Lesson(models.Model):
    """课时模型"""
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, related_name='lessons', null=True, blank=True)
    title = models.CharField(_('课时标题'), max_length=200)
    description = models.TextField(_('课时描述'), blank=True)
    order = models.IntegerField(_('排序'))
    video_url = models.URLField(_('视频URL'), blank=True)
    duration = models.IntegerField(_('视频时长(秒)'), default=0)
    mock_dialogue_data = models.JSONField(_('对话数据'), null=True, blank=True, default=dict) # 非必要不要加载此数据
    mock_navigation_data = models.JSONField(_('导航数据'), null=True, blank=True, default=dict) # 非必要不要加载此数据
    subtitle_data = models.JSONField(_('字幕数据'), null=True, blank=True, default=dict) # 非必要不要加载此数据
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('课时')
        verbose_name_plural = _('课时')
        ordering = ['order']

    def __str__(self):
        return f"{self.chapter.title if self.chapter else '未关联章节'} - {self.title}"

class Resource(models.Model):
    """课程资源模型"""
    RESOURCE_TYPE_CHOICES = (
        ('DOCUMENT', '文档'),
        ('VIDEO', '视频'),
        ('AUDIO', '音频'),
        ('OTHER', '其他'),
    )
    
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='resources')
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, related_name='resources', null=True, blank=True)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name='resources', null=True, blank=True)
    title = models.CharField(_('资源标题'), max_length=200)
    resource_type = models.CharField(_('资源类型'), max_length=20, choices=RESOURCE_TYPE_CHOICES)
    file = models.CharField(_('资源文件'),max_length=500)
    description = models.TextField(_('资源描述'), blank=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('课程资源')
        verbose_name_plural = _('课程资源')

    def __str__(self):
        return self.title

class Note(models.Model):
    """学习笔记模型"""
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='notes', null=True, blank=True)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='notes', null=True, blank=True)
    title = models.CharField(_('笔记标题'), max_length=200)
    content = models.TextField(_('笔记内容'))
    timestamp = models.CharField(_('视频时间戳'), max_length=10, default='00:00', help_text='视频播放时间点，格式为MM:SS')
    is_public = models.BooleanField(_('是否公开'), default=False)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('学习笔记')
        verbose_name_plural = _('学习笔记')

    def __str__(self):
        return f"{self.student.user.username} - {self.title}"

class CourseRating(models.Model):
    """课程评分模型"""
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='ratings', verbose_name=_('课程'))
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='course_ratings', verbose_name=_('学生'))
    rating = models.FloatField(_('评分'), validators=[MinValueValidator(0.0), MaxValueValidator(5.0)])
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('课程评分')
        verbose_name_plural = _('课程评分')
        unique_together = ['course', 'student']  # 每个学生只能对同一门课程评分一次

    def __str__(self):
        return f"{self.student.user.username} - {self.course.name} - {self.rating}"

    def save(self, *args, **kwargs):
        # 保存评分时更新课程的平均分
        is_new = self.pk is None
        super().save(*args, **kwargs)

        course = self.course
        if is_new:
            # 只有新增评分时才更新课程平均分
            course.rating_count += 1
        else:
            # 更新评分时更新课程平均分
            pass
        total_rating = sum(rating.rating for rating in course.ratings.filter(deleted_at__isnull=True))
        course.average_rating = total_rating / course.rating_count
        course.save()

class CourseOverview(models.Model):
    """课程概述模型"""
    course = models.OneToOneField(
        'Course',
        on_delete=models.CASCADE,
        related_name='overview',
        verbose_name=_('课程')
    )
    key_points = models.TextField(_('课程要点'), blank=True)
    important_points = models.TextField(_('重点知识点'), blank=True)
    notes = models.TextField(_('注意事项'), blank=True)
    language_code = models.CharField(_('语言'), max_length=10, choices=LANGUAGE_CHOICES, default=DEFAULT_LANGUAGE,
                                  help_text='课程概述的语言')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('课程概述')
        verbose_name_plural = _('课程概述')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.course.name} - 课程概述" 