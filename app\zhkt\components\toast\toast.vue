<template>
  <view class="toast-container" v-if="visible">
    <view class="toast-content" :class="type">
      <uni-icons :type="getIcon" size="24" color="#fff"></uni-icons>
      <text class="toast-text">{{ text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Toast',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'default',
      validator: value => ['default', 'success', 'warning', 'error'].indexOf(value) !== -1
    },
    duration: {
      type: Number,
      default: 2000
    }
  },
  
  computed: {
    getIcon() {
      const iconMap = {
        default: 'info',
        success: 'checkmarkempty',
        warning: 'warning',
        error: 'closeempty'
      }
      return iconMap[this.type]
    }
  },
  
  watch: {
    visible(val) {
      if (val) {
        setTimeout(() => {
          this.$emit('update:visible', false)
        }, this.duration)
      }
    }
  }
}
</script>

<style lang="scss">
.toast-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  
  .toast-content {
    display: flex;
    align-items: center;
    padding: 20rpx 40rpx;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10rpx;
    animation: fadeIn 0.3s;
    
    &.success {
      background: rgba(60, 197, 31, 0.9);
    }
    
    &.warning {
      background: rgba(255, 153, 0, 0.9);
    }
    
    &.error {
      background: rgba(255, 59, 48, 0.9);
    }
    
    .toast-text {
      font-size: 28rpx;
      color: #fff;
      margin-left: 10rpx;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 