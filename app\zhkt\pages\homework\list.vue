<template>
  <view class="container">
    <!-- 状态筛选 -->
    <scroll-view class="filter-list" scroll-x>
      <view 
        class="filter-item" 
        :class="{ active: currentStatus === item.value }"
        v-for="item in statusFilters" 
        :key="item.value"
        @click="switchStatus(item.value)"
      >
        {{ item.label }}
      </view>
    </scroll-view>
    
    <!-- 作业列表 -->
    <scroll-view class="homework-list" scroll-y @scrolltolower="loadMore">
      <view class="homework-item" v-for="homework in homeworkList" :key="homework.id" @click="navigateToHomework(homework)">
        <view class="homework-info">
          <text class="title">{{ homework.title }}</text>
          <view class="meta">
            <text class="course">{{ homework.courseName }}</text>
            <text class="deadline">截止时间：{{ homework.deadline }}</text>
          </view>
          <view class="status-bar">
            <text class="status" :class="homework.status">{{ getStatusText(homework.status) }}</text>
            <text class="score" v-if="homework.score">得分：{{ homework.score }}分</text>
          </view>
        </view>
        <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
      </view>
      
      <!-- 加载更多 -->
      <uni-load-more :status="loadMoreStatus"></uni-load-more>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="homeworkList.length === 0">
        <image src="/static/images/empty.png" mode="aspectFit"></image>
        <text class="text">暂无作业</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'

// 状态筛选器
const statusFilters = ref([
  { label: '全部', value: '' },
  { label: '待完成', value: 'pending' },
  { label: '已提交', value: 'submitted' },
  { label: '已批改', value: 'graded' },
  { label: '已逾期', value: 'overdue' }
])
const currentStatus = ref('')

// 作业列表
const homeworkList = ref([])
const page = ref(1)
const loadMoreStatus = ref('more')

// 获取作业列表
const getHomeworkList = async (refresh = false) => {
  if (refresh) {
    page.value = 1
    homeworkList.value = []
  }
  
  try {
    loadMoreStatus.value = 'loading'
    const response = await request({
      url: '/homeworks/student_assignments/',
      method: 'GET'
    })
    
    const { results, next } = response
    homeworkList.value = [...homeworkList.value, ...results]
    loadMoreStatus.value = next ? 'more' : 'noMore'
  } catch (error) {
    loadMoreStatus.value = 'more'
    uni.showToast({
      title: '获取作业列表失败',
      icon: 'none'
    })
  }
}

// 切换状态筛选
const switchStatus = (status) => {
  currentStatus.value = status
  getHomeworkList(true)
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待完成',
    'submitted': '已提交',
    'graded': '已批改',
    'overdue': '已逾期'
  }
  return statusMap[status] || status
}

// 加载更多
const loadMore = () => {
  if (loadMoreStatus.value === 'loading' || loadMoreStatus.value === 'noMore') return
  page.value++
  getHomeworkList()
}

// 跳转到作业详情
const navigateToHomework = (homework) => {
  uni.navigateTo({
    url: `/pages/homework/detail?id=${homework.id}`
  })
}

onMounted(() => {
  getHomeworkList()
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.filter-list {
  white-space: nowrap;
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  
  .filter-item {
    display: inline-block;
    padding: 10rpx 30rpx;
    margin-right: 20rpx;
    font-size: 28rpx;
    color: #666;
    background: #f5f5f5;
    border-radius: 30rpx;
    
    &.active {
      color: #fff;
      background: #3cc51f;
    }
    
    &:last-child {
      margin-right: 0;
    }
  }
}

.homework-list {
  flex: 1;
  padding: 20rpx;
  
  .homework-item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .homework-info {
      flex: 1;
      margin-right: 20rpx;
      
      .title {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        margin-bottom: 20rpx;
      }
      
      .meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        
        .course {
          font-size: 28rpx;
          color: #666;
        }
        
        .deadline {
          font-size: 28rpx;
          color: #999;
        }
      }
      
      .status-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .status {
          font-size: 24rpx;
          padding: 6rpx 20rpx;
          border-radius: 20rpx;
          
          &.pending {
            color: #ff9900;
            background: rgba(255, 153, 0, 0.1);
          }
          
          &.submitted {
            color: #3cc51f;
            background: rgba(60, 197, 31, 0.1);
          }
          
          &.graded {
            color: #007aff;
            background: rgba(0, 122, 255, 0.1);
          }
          
          &.overdue {
            color: #ff3b30;
            background: rgba(255, 59, 48, 0.1);
          }
        }
        
        .score {
          font-size: 28rpx;
          color: #ff9900;
          font-weight: bold;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .text {
    font-size: 28rpx;
    color: #999;
  }
}
</style> 