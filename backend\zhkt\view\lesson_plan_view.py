from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser, JSONParser
from django.http import StreamingHttpResponse

from ..services.lesson_plan_service import LessonPlanService
from ..utils.response import ResponseResult


class LessonPlanViewSet(ViewSet):
    """教学设计相关接口视图集"""
    permission_classes = [IsAuthenticated]
    parser_classes = [J<PERSON><PERSON>ars<PERSON>, MultiPartParser, FormParser]

    @action(detail=False, methods=['post'], url_path='create')
    def create_lesson_plan(self, request):
        """
        上传教材并创建教学设计
        
        Request Body:
            file: 教材文件
            lessonTemplate: 教案模板，例如："标准教学模板"、"PBL问题导向模板"等
            teachingStyle: 教学风格，例如："平衡型（理论与实践并重）"等
            specificRequirements: 具体需求说明(可选)
        
        Returns:
            Response: 包含创建结果
        """
        try:
            # 获取请求参数
            lesson_template = request.data.get('lessonTemplate', '')
            teaching_style = request.data.get('teachingStyle', '')
            specific_requirements = request.data.get('specificRequirements', '')
            
            # 检查必填参数
            if not lesson_template:
                return ResponseResult.error(
                    code=400,
                    message='请选择教案模板'
                )
                
            if not teaching_style:
                return ResponseResult.error(
                    code=400,
                    message='请选择教学风格'
                )
                
            # 检查是否上传了文件
            if 'file' not in request.FILES:
                return ResponseResult.error(
                    code=400,
                    message='请上传教材文件'
                )
                
            file = request.FILES['file']
            
            # 获取当前用户ID
            user_id = request.user.id
            
            # 调用服务创建教学设计
            result = LessonPlanService.create_lesson_plan(
                file=file,
                lesson_template=lesson_template,
                teaching_style=teaching_style,
                specific_requirements=specific_requirements,
                user_id=user_id
            )

            return ResponseResult.success(
                data=result,
                message='教学设计创建成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'创建教学设计失败: {str(e)}'
            )

    @action(detail=False, methods=['post'], url_path='save')
    def save_lesson_plan(self, request):
        """
        保存生成的教学设计
        
        Request Body:
            taskId: 任务ID
            title: 教学设计标题
            subjectId: 学科ID
        
        Returns:
            Response: 包含保存结果
        """
        try:
            # 获取请求参数
            task_id = request.data.get('taskId', '')
            title = request.data.get('title', '')
            subject_id = request.data.get('subjectId')
            
            # 检查必填参数
            if not task_id:
                return ResponseResult.error(
                    code=400,
                    message='任务ID不能为空'
                )
                
            if not title:
                return ResponseResult.error(
                    code=400,
                    message='教学设计标题不能为空'
                )
                
            if not subject_id:
                return ResponseResult.error(
                    code=400,
                    message='请选择学科'
                )
            
            # 调用服务保存教学设计
            result = LessonPlanService.save_lesson_plan(
                task_id=task_id,
                title=title,
                subject_id=subject_id
            )
            
            if result.get('success'):
                return ResponseResult.success(
                    data=result.get('data'),
                    message=result.get('message')
                )
            else:
                return ResponseResult.error(
                    code=400,
                    message=result.get('message')
                )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'保存教学设计失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='stream/(?P<task_id>[^/.]+)')
    def stream_lesson_plan(self, request, task_id):
        """
        流式生成教学设计并返回
        
        URL参数:
            task_id: 任务ID
            
        Returns:
            StreamingHttpResponse: 流式响应，包含SSE格式的生成内容
        """
        try:
            return StreamingHttpResponse(
                LessonPlanService.generate_lesson_plan_stream(task_id),
                content_type='text/event-stream'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'启动流式生成失败: {str(e)}'
            )
    
    @action(detail=False, methods=['get'], url_path='status/(?P<task_id>[^/.]+)')
    def get_task_status(self, request, task_id):
        """
        获取教学设计任务状态
        
        URL参数:
            task_id: 任务ID
            
        Returns:
            Response: 包含任务状态信息
        """
        result = LessonPlanService.get_task_status(task_id)
        
        if result.get('status') == 'not_found':
            return ResponseResult.error(
                code=404,
                message='任务不存在'
            )
            
        return ResponseResult.success(
            data=result,
            message='获取任务状态成功'
        )
        
    @action(detail=False, methods=['get'], url_path='list')
    def get_lesson_plans(self, request):
        """
        获取教学设计列表
        
        Query Parameters:
            subject_id: 学科ID，可选
            search: 搜索关键词，可选
            page: 页码，默认1
            page_size: 每页大小，默认10
        
        Returns:
            Response: 包含教学设计列表
        """
        try:
            # 获取查询参数
            subject_id = request.query_params.get('subject_id')
            search = request.query_params.get('search')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            
            # 获取当前用户ID
            user_id = request.user.id
            
            # 调用服务获取教学设计列表
            result = LessonPlanService.get_lesson_plans(
                user_id=user_id,
                subject_id=subject_id,
                search=search,
                page=page,
                page_size=page_size
            )
            
            if result.get('success'):
                return ResponseResult.success(
                    data=result.get('data'),
                    message='获取教学设计列表成功'
                )
            else:
                return ResponseResult.error(
                    code=400,
                    message=result.get('message')
                )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取教学设计列表失败: {str(e)}'
            )
    
    @action(detail=True, methods=['get'], url_path='detail')
    def get_lesson_plan(self, request, pk=None):
        """
        获取教学设计详情
        
        URL Parameters:
            pk: 教学设计ID
        
        Returns:
            Response: 包含教学设计详情
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id
            
            # 调用服务获取教学设计详情
            result = LessonPlanService.get_lesson_plan(
                lesson_plan_id=pk,
                user_id=user_id
            )
            
            if result.get('success'):
                return ResponseResult.success(
                    data=result.get('data'),
                    message='获取教学设计详情成功'
                )
            else:
                return ResponseResult.error(
                    code=400,
                    message=result.get('message')
                )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取教学设计详情失败: {str(e)}'
            )
    
    @action(detail=True, methods=['put'], url_path='update')
    def update_lesson_plan(self, request, pk=None):
        """
        更新教学设计
        
        URL Parameters:
            pk: 教学设计ID
            
        Request Body:
            title: 教学设计标题，可选
            subject_id: 学科ID，可选
            content: 教学设计内容，可选
            lesson_template: 教案模板，可选
            teaching_style: 教学风格，可选
            specific_requirements: 具体要求，可选
        
        Returns:
            Response: 包含更新结果
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id
            
            # 获取请求数据
            data = request.data
            
            # 调用服务更新教学设计
            result = LessonPlanService.update_lesson_plan(
                lesson_plan_id=pk,
                user_id=user_id,
                data=data
            )
            
            if result.get('success'):
                return ResponseResult.success(
                    data=result.get('data'),
                    message=result.get('message')
                )
            else:
                return ResponseResult.error(
                    code=400,
                    message=result.get('message')
                )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'更新教学设计失败: {str(e)}'
            )
    
    @action(detail=True, methods=['delete'], url_path='delete')
    def delete_lesson_plan(self, request, pk=None):
        """
        删除教学设计
        
        URL Parameters:
            pk: 教学设计ID
        
        Returns:
            Response: 包含删除结果
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id
            
            # 调用服务删除教学设计
            result = LessonPlanService.delete_lesson_plan(
                lesson_plan_id=pk,
                user_id=user_id
            )
            
            if result.get('success'):
                return ResponseResult.success(
                    message=result.get('message')
                )
            else:
                return ResponseResult.error(
                    code=400,
                    message=result.get('message')
                )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'删除教学设计失败: {str(e)}'
            ) 