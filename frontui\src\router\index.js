import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import studentRoutes from './student'
import teacherRoutes from './teacher'
import adminRoutes from './admin'
import { courseRoutes } from './course'
import StudentProfileView from '@/views/student/StudentProfileView.vue'
import StudentSettingsView from '@/views/student/StudentSettingsView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 根路径直接重定向到登录页
    {
      path: '/',
      redirect: '/auth/login'
    },
    // 认证相关路由
    {
      path: '/auth/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/register',
      name: 'register',
      component: () => import('../views/auth/RegisterView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/auth/ForgotPasswordView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/reset-password',
      name: 'reset-password',
      component: () => import('../views/auth/ResetPasswordView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/mobile-login',
      name: 'mobile-login',
      component: () => import('../views/auth/MobileLoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/wechat-login',
      name: 'wechat-login',
      component: () => import('../views/auth/WechatLoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/qq-login',
      name: 'qq-login',
      component: () => import('../views/auth/QQLoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/terms',
      name: 'terms',
      component: () => import('../views/auth/TermsView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/privacy',
      name: 'privacy',
      component: () => import('../views/auth/PrivacyView.vue'),
      meta: { requiresAuth: false }
    },

    // 导入学生相关路由
    ...studentRoutes,
    
    // 导入教师相关路由
    ...teacherRoutes,

    // 导入管理员相关路由
    ...adminRoutes,

    // 导入课程相关路由
    courseRoutes,

    // Shared User Profile Route
    {
      path: '/profile',
      name: 'UserProfile',
      component: StudentProfileView, 
      meta: { requiresAuth: true }
    },
    // Shared User Settings Route
    {
      path: '/settings',
      name: 'UserSettings',
      component: StudentSettingsView,
      meta: { requiresAuth: true }
    },
  ],
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 检查路由路径，确保登录页始终可以显示
  if (to.path === '/auth/login') {
    // 始终允许访问登录页
    next()
    return
  }

  if(!to.meta.requiresAuth) {
    // 不需要认证检查
    next()
    return
  }else{
    // 需要认证检查
    if (!authStore.isLoggedIn) {
      // 未登录，重定向到登录页
      next({ name: 'login' })
      return
    }
    next()
    return
  }
})

export default router
