<template>
  <div class="flex h-screen overflow-hidden" :data-active-page="activePage" :data-active-sub-page="activeSubPage">
    <!-- Sidebar - fixed on desktop, toggleable on mobile -->
    <div class="md:block" :class="{ 'hidden': !showSidebar }">
      <TeacherSidebar :is-collapsed="isCollapsed" @toggleCollapse="toggleCollapse" />
    </div>

    <!-- Main Content Area -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <!-- Top Header with Breadcrumbs -->
      <UserHeader 
        @toggleSidebar="toggleSidebar"
        @toggleCollapse="toggleCollapse"
        :pageTitle="pageTitle"
        :userName="userName"
        :userAvatar="userAvatar"
        :userRole="'teacher'"
      />

      <!-- Main Content - Scrollable independently from header -->
      <div class="flex-1 p-3 overflow-y-auto bg-gray-50">
        <slot></slot>
      </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div 
      v-if="showSidebar && isMobile" 
      class="fixed inset-0 bg-black bg-opacity-30 z-10 md:hidden"
      @click="toggleSidebar"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import TeacherSidebar from './TeacherSidebar.vue'
import UserHeader from './UserHeader.vue'
import { useRoute } from 'vue-router'

// Props for the layout
const props = defineProps({
  pageTitle: {
    type: String,
    default: '教师首页'
  },
  userName: {
    type: String,
    required: true
  },
  userAvatar: {
    type: String,
    required: true
  },
  activePage: {
    type: String,
    default: ''
  },
  activeSubPage: {
    type: String,
    default: ''
  }
})

// Route
const route = useRoute()

// State
const showSidebar = ref(true)
const isMobile = ref(window.innerWidth < 768) // Initialize with current width
const isCollapsed = ref(false) // 新增

// Toggle sidebar visibility (primarily for mobile view)
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
  console.log('Sidebar toggled, now:', showSidebar.value)
}

const toggleCollapse = () => { // 新增
  isCollapsed.value = !isCollapsed.value
}

// Detect window resize to determine if we're on mobile
const handleResize = () => {
  isMobile.value = window.innerWidth < 768
  // If desktop width, always show sidebar
  if (!isMobile.value) {
    showSidebar.value = true
  }
}

// Initialize and setup event listeners
onMounted(() => {
  handleResize() // Set initial state based on screen size
  window.addEventListener('resize', handleResize)
  console.log('TeacherLayout mounted, sidebar:', showSidebar.value)
})

// Clean up
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* Add smooth transition to sidebar toggle */
.md\:block {
  transition: all 0.3s ease;
}
</style> 