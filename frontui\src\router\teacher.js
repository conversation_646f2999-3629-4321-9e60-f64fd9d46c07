// 导入教师相关视图
import TeacherDashboard from '@/views/teacher/TeacherDashboard.vue'

// 教师相关路由配置
const teacherRoutes = [
  {
    path: '/teacher',
    name: 'teacher',
    component: TeacherDashboard,
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/dashboard',
    name: 'teacher-dashboard',
    component: TeacherDashboard,
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/course-management',
    name: 'teacher-course-management',
    component: () => import('@/views/teacher/CourseManagementView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/class-list',
    name: 'teacher-class-list',
    component: () => import('@/views/teacher/ClassManagementView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/student-management',
    name: 'teacher-student-management',
    component: () => import('@/views/teacher/StudentManagementView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/question-bank',
    name: 'teacher-question-bank',
    component: () => import('@/views/teacher/QuestionBankView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/assignment-management',
    name: 'teacher-assignment-management',
    component: () => import('@/views/teacher/AssignmentManagementView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/grade-management',
    name: 'teacher-grade-management',
    component: () => import('@/views/teacher/GradeManagementView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/content-creation',
    name: 'teacher-content-creation',
    component: () => import('@/views/teacher/ContentCreationView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/lesson-plan-projects',
    name: 'teacher-lesson-plan-projects',
    component: () => import('@/views/teacher/LessonPlanProjectsView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/lesson-plan/create',
    name: 'teacher-lesson-plan-create',
    component: () => import('@/views/teacher/CreateLessonPlanView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/lesson-plan/:id/edit',
    name: 'teacher-lesson-plan-edit',
    component: () => import('@/views/teacher/CreateLessonPlanView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/lesson-plan/:id/detail',
    name: 'teacher-lesson-plan-detail',
    component: () => import('@/views/teacher/CreateLessonPlanView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/content-ppt-projects',
    name: 'teacher-content-ppt-projects',
    component: () => import('@/views/teacher/PPTProjectsView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/ppt/create',
    name: 'teacher-ppt-create',
    component: () => import('@/views/teacher/CreatePPTView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/content-script-projects',
    name: 'teacher-content-script-projects',
    component: () => import('@/views/teacher/ScriptProjectsView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/script/create',
    name: 'teacher-script-create',
    component: () => import('@/views/teacher/CreateScriptView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/content-video-projects',
    name: 'teacher-content-video-projects',
    component: () => import('@/views/teacher/VideoProjectsView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/video-course',
    name: 'teacher-video-course',
    component: () => import('@/views/teacher/VideoCourseView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/knowledge-base',
    name: 'teacher-knowledge-base',
    component: () => import('@/views/teacher/KnowledgeBaseView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/ai-assistant',
    name: 'teacher-ai-assistant',
    component: () => import('@/views/teacher/AiAssistantView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/digital-teacher',
    name: 'teacher-digital-teacher',
    component: () => import('@/views/teacher/DigitalTeacherView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/digital-teacher-management',
    name: 'teacher-digital-teacher-management',
    component: () => import('@/views/teacher/DigitalTeacherManagementView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/voice-management',
    name: 'teacher-voice-management',
    component: () => import('@/views/teacher/VoiceManagementView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/profile',
    name: 'teacher-profile',
    component: () => import('@/views/teacher/TeacherProfileView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/settings',
    name: 'teacher-settings',
    component: () => import('@/views/teacher/TeacherSettingsView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/ppt/editor',
    name: 'teacher-ppt-editor',
    component: () => import('@/views/teacher/DocmeePPTEditorView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/textbook-projects',
    name: 'TeacherTextbookProjects',
    component: () => import('@/views/teacher/TextbookProjectsView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/textbook/create',
    name: 'CreateTextbook',
    component: () => import('@/views/teacher/TextbookCreateView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/textbook/:id/edit',
    name: 'EditTextbook',
    component: () => import('@/views/teacher/TextbookEditView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
  {
    path: '/teacher/textbook/:id/detail',
    name: 'TextbookDetail',
    component: () => import('@/views/teacher/TextbookDetailView.vue'),
    meta: { requiresAuth: true, userType: 'teacher' }
  },
]

export default teacherRoutes 