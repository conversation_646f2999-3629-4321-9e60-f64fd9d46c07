from rest_framework import serializers
from django.contrib.auth import get_user_model
from .entitys import (
    User, Role, Student, Teacher, Admin,
    College, Major, ClassGroup,
    Course, Chapter, Resource,
    Homework, Submission, Feedback,
    PointsRecord, Product, Order,
    AIChat, AIChatMessage, AIPrompt,
    Dept, Menu,
    Lesson, Note,
    CourseRating,
    Comment,
    CourseOverview,
    Question, Option,
    HomeworkQuestion
)
from django.utils import timezone

# 系统管理相关序列化器
class MenuSerializer(serializers.ModelSerializer):
    class Meta:
        model = Menu
        fields = ['id', 'name', 'code', 'parent', 'path', 'component', 'icon', 
                 'order', 'type', 'permission', 'status', 'created_at', 'updated_at']

class DeptSerializer(serializers.ModelSerializer):
    class Meta:
        model = Dept
        fields = ['id', 'name', 'code', 'parent', 'order', 'leader', 'phone', 
                 'email', 'status', 'created_at', 'updated_at']

class RoleSerializer(serializers.ModelSerializer):
    menus = MenuSerializer(many=True, read_only=True)
    
    class Meta:
        model = Role
        fields = ['id', 'name', 'code', 'description', 'menus', 'created_at', 'updated_at']

class UserSerializer(serializers.ModelSerializer):
    dept = DeptSerializer(read_only=True)
    roles = RoleSerializer(many=True, read_only=True)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'phone', 
                 'gender', 'avatar', 'roles', 'dept', 'password', 'alias', 'date_joined']
        read_only_fields = ['id', 'date_joined']
        extra_kwargs = {
            'password': {
                'write_only': True,
                'required': False  # 更新时不要求密码
            }
        }

    def create(self, validated_data):
        password = validated_data.pop('password', None)
        user = super().create(validated_data)
        if password:
            user.set_password(password)
            user.save()
        return user

    def update(self, instance, validated_data):
        # 从验证数据中移除密码字段（如果存在）
        password = validated_data.pop('password', None)
        # 更新其他字段
        user = super().update(instance, validated_data)
        # 如果提供了密码，则更新密码
        if password:
            user.set_password(password)
            user.save()
        return user

class TeacherSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    college_name = serializers.CharField(source='college.name', read_only=True)
    
    class Meta:
        model = Teacher
        fields = ['id', 'user', 'teacher_id', 'title', 'college', 'college_name', 'majors', 'introduction']

class AdminSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = Admin
        fields = ['id', 'user', 'admin_id', 'department', 'role']

# 组织相关序列化器
class CollegeSerializer(serializers.ModelSerializer):
    class Meta:
        model = College
        fields = '__all__'

class MajorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Major
        fields = '__all__'

class ClassGroupSerializer(serializers.ModelSerializer):
    """班级序列化器"""
    head_teacher_name = serializers.CharField(source='head_teacher.name', read_only=True)
    student_count = serializers.SerializerMethodField()
    course_count = serializers.SerializerMethodField()

    class Meta:
        model = ClassGroup
        fields = ['id', 'name', 'code', 'description', 'head_teacher', 'head_teacher_name', 
                 'student_count', 'course_count', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def get_student_count(self, obj):
        """获取班级学生数量"""
        return obj.students.count() if hasattr(obj, 'students') else 0

    def get_course_count(self, obj):
        """获取班级课程数量"""
        return obj.courses.count() if hasattr(obj, 'courses') else 0

# 课程相关序列化器
class CourseRatingSerializer(serializers.ModelSerializer):
    student_name = serializers.SerializerMethodField()
    
    class Meta:
        model = CourseRating
        fields = ['id', 'course', 'student', 'rating', 'created_at', 'student_name']
        read_only_fields = ['student_name']
    
    def get_student_name(self, obj):
        return obj.student.user.username if obj.student and obj.student.user else None

class CourseOverviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseOverview
        fields = ['id', 'course', 'key_points', 'important_points', 'notes', 'language_code', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class CourseSerializer(serializers.ModelSerializer):
    teacher = TeacherSerializer(read_only=True)
    teacher_id = serializers.IntegerField(write_only=True)
    major = MajorSerializer(many=True, read_only=True)
    major_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    class_groups = ClassGroupSerializer(many=True, read_only=True)
    class_group_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    student_count = serializers.SerializerMethodField()
    college = serializers.SerializerMethodField()
    ratings = CourseRatingSerializer(many=True, read_only=True)
    user_rating = serializers.SerializerMethodField()
    overview = CourseOverviewSerializer(read_only=True)

    class Meta:
        model = Course
        fields = [
            'id', 'name', 'code', 'course_type', 'teacher', 'teacher_id',
            'major', 'major_ids', 'class_groups', 'class_group_ids', 'description', 'pointsCost', 'total_hours',
            'plan_count', 'cover_image', 'status', 'language_code', 'created_at', 'updated_at',
            'student_count', 'college', 'average_rating', 'rating_count',
            'ratings', 'user_rating', 'overview'
        ]

    def get_student_count(self, obj):
        # 获取课程关联的所有班级
        class_groups = obj.class_groups.filter(deleted_at__isnull=True)
        # 统计这些班级中的学生数量（去重）
        return Student.objects.filter(
            classes__in=class_groups,
            deleted_at__isnull=True
        ).distinct().count()

    def get_college(self, obj):
        return obj.teacher.college.name if obj.teacher.college else None

    def get_user_rating(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                student = request.user.student_profile
                rating = obj.ratings.filter(student=student, deleted_at__isnull=True).first()
                if rating:
                    return CourseRatingSerializer(rating).data
            except:
                pass
        return None

    def create(self, validated_data):
        major_ids = validated_data.pop('major_ids', [])
        class_group_ids = validated_data.pop('class_group_ids', [])
        course = Course.objects.create(**validated_data)
        course.major.set(major_ids)
        course.class_groups.set(class_group_ids)
        return course

    def update(self, instance, validated_data):
        major_ids = validated_data.pop('major_ids', None)
        class_group_ids = validated_data.pop('class_group_ids', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if major_ids is not None:
            instance.major.set(major_ids)
        if class_group_ids is not None:
            instance.class_groups.set(class_group_ids)
        return instance

class ChapterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Chapter
        fields = ['id', 'course', 'title', 'description', 'order', 'duration', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']
        extra_kwargs = {
            'course': {'required': False, 'allow_null': True}
        }

class ResourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Resource
        fields = '__all__'

# 积分相关序列化器
class PointsRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = PointsRecord
        fields = '__all__'

class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = '__all__'

class OrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = '__all__'

# AI助手相关序列化器
class AIChatSerializer(serializers.ModelSerializer):
    message_count = serializers.SerializerMethodField()

    class Meta:
        model = AIChat
        fields = ['id', 'title', 'created_at', 'updated_at', 'message_count']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_message_count(self, obj):
        return obj.messages.count()

class AIChatMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIChatMessage
        fields = ['id', 'chat', 'role', 'content', 'created_at']
        read_only_fields = ['id', 'created_at']

class AIPromptSerializer(serializers.ModelSerializer):
    class Meta:
        model = AIPrompt
        fields = ['id', 'title', 'content', 'prompt_type', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class LessonSerializer(serializers.ModelSerializer):
    class Meta:
        model = Lesson
        fields = ['id', 'chapter', 'title', 'description', 'order', 'video_url', 'duration', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']
        extra_kwargs = {
            'chapter': {'required': False, 'allow_null': True}
        }

    def create(self, validated_data):
        return Lesson.objects.create(**validated_data) 
    
class NoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Note
        fields = ['id', 'student', 'course', 'title', 'content', 'is_public', 'timestamp', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at'] 

class CommentSerializer(serializers.ModelSerializer):
    user_name = serializers.SerializerMethodField()
    user_avatar = serializers.SerializerMethodField()
    is_instructor = serializers.SerializerMethodField()
    replies = serializers.SerializerMethodField()
    has_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = Comment
        fields = [
            'id', 'content', 'created_at', 'updated_at', 'likes',
            'course', 'user', 'parent', 'user_name', 'user_avatar',
            'is_instructor', 'replies', 'has_liked'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'likes', 'user']
    
    def get_user_name(self, obj):
        return obj.user.alias if obj.user else None
    
    def get_user_avatar(self, obj):
        return obj.user.avatar if obj.user else None
    
    def get_is_instructor(self, obj):
        return hasattr(obj.user, 'teacher_profile') if obj.user else False
    
    def get_replies(self, obj):
        if obj.parent is None:  # 只处理父评论的回复
            replies = obj.replies.all()
            return CommentSerializer(replies, many=True, context=self.context).data
        return []
    
    def get_has_liked(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.liked_by.filter(id=request.user.id).exists()
        return False 
    
class StudentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    classes = ClassGroupSerializer(many=True, read_only=True)
    class_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Student
        fields = ['id', 'user', 'student_id', 'college', 'major', 'classes', 'class_ids',
                 'enrollment_date', 'expected_graduation_date', 'status', 'points']

    def create(self, validated_data):
        class_ids = validated_data.pop('class_ids', [])
        student = Student.objects.create(**validated_data)
        student.classes.set(class_ids)
        return student

    def update(self, instance, validated_data):
        class_ids = validated_data.pop('class_ids', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if class_ids is not None:
            instance.classes.set(class_ids)
        return instance

class OptionSerializer(serializers.ModelSerializer):
    """题目选项序列化器"""
    class Meta:
        model = Option
        fields = ['id', 'content', 'is_correct', 'order', 'created_at', 'updated_at', 'deleted_at']
        read_only_fields = ['id', 'created_at', 'updated_at', 'deleted_at']

class QuestionSerializer(serializers.ModelSerializer):
    """题目序列化器"""
    options = OptionSerializer(many=True, read_only=True)
    course_name = serializers.CharField(source='course.name', read_only=True)
    question_type_display = serializers.CharField(source='get_question_type_display', read_only=True)
    difficulty_display = serializers.CharField(source='get_difficulty_display', read_only=True)
    
    class Meta:
        model = Question
        fields = [
            'id', 'course', 'course_name', 'title', 'question_type', 'question_type_display',
            'difficulty', 'difficulty_display', 'content', 'answer', 'analysis',
            'score', 'tags', 'order', 'options', 'created_at', 'updated_at', 'deleted_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'deleted_at']

    def create(self, validated_data):
        options_data = self.context.get('options', [])
        question = Question.objects.create(**validated_data)
        
        for option_data in options_data:
            Option.objects.create(question=question, **option_data)
            
        return question

    def update(self, instance, validated_data):
        options_data = self.context.get('options', [])
        
        # 更新题目基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 如果提供了选项数据，则更新选项
        if options_data:
            # 删除现有选项
            instance.options.all().delete()
            # 创建新选项
            for option_data in options_data:
                Option.objects.create(question=instance, **option_data)
        
        return instance
    
# 作业相关序列化器
class HomeworkQuestionSerializer(serializers.ModelSerializer):
    """作业题目关联序列化器"""
    question_detail = QuestionSerializer(source='question', read_only=True)
    
    class Meta:
        model = HomeworkQuestion
        fields = ['id', 'question', 'question_detail', 'order', 'score']

class HomeworkSerializer(serializers.ModelSerializer):
    """作业序列化器"""
    questions = HomeworkQuestionSerializer(source='homework_questions', many=True, read_only=True)
    question_settings = serializers.ListField(write_only=True, required=False)
    status = serializers.SerializerMethodField()
    submit_count = serializers.SerializerMethodField()
    total_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Homework
        fields = ['id', 'course', 'title', 'description', 'start_time', 
                 'end_time', 'total_score', 'questions', 'question_settings',
                 'status', 'submit_count', 'total_count', 'created_at', 'updated_at',
                 'is_published', 'publish_date']
        read_only_fields = ['created_at', 'updated_at', 'total_score']
    
    def get_status(self, obj):
        now = timezone.now()
        if now < obj.start_time:
            return '未开始'
        elif now > obj.end_time:
            return 'overdue'
        else:
            return 'pending'
    
    def get_submit_count(self, obj):
        return obj.submissions.filter(deleted_at__isnull=True).count()
    
    def get_total_count(self, obj):
        # 获取课程关联的所有班级
        class_groups = obj.course.class_groups.filter(deleted_at__isnull=True)
        # 统计这些班级中的学生数量（去重）
        return Student.objects.filter(
            classes__in=class_groups,
            deleted_at__isnull=True
        ).distinct().count()
    
    def create(self, validated_data):
        question_settings = validated_data.pop('question_settings', [])
        # 计算总分
        total_score = sum(float(setting['score']) for setting in question_settings)
        validated_data['total_score'] = total_score
        homework = super().create(validated_data)
        
        # 创建作业题目关联
        for setting in question_settings:
            HomeworkQuestion.objects.create(
                homework=homework,
                question_id=setting['question_id'],
                order=setting['order'],
                score=setting['score']
            )
        
        return homework
    
    def update(self, instance, validated_data):
        question_settings = validated_data.pop('question_settings', None)
        
        if question_settings is not None:
            # 计算新的总分
            total_score = sum(float(setting['score']) for setting in question_settings)
            validated_data['total_score'] = total_score
            
            # 删除原有的题目关联
            instance.homework_questions.all().delete()
            # 创建新的题目关联
            for setting in question_settings:
                HomeworkQuestion.objects.create(
                    homework=instance,
                    question_id=setting['question_id'],
                    order=setting['order'],
                    score=setting['score']
                )
        
        return super().update(instance, validated_data)

class SubmissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Submission
        fields = '__all__'

class FeedbackSerializer(serializers.ModelSerializer):
    class Meta:
        model = Feedback
        fields = '__all__'