# Generated by Django 3.2.20 on 2025-06-18 18:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0066_avatar_digitalhuman'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='digitalhuman',
            name='user',
        ),
        migrations.AddField(
            model_name='digitalhuman',
            name='category',
            field=models.CharField(blank=True, help_text='仅系统预设使用', max_length=50, null=True, verbose_name='预设分类'),
        ),
        migrations.AddField(
            model_name='digitalhuman',
            name='duration',
            field=models.IntegerField(default=0, verbose_name='视频时长(秒)'),
        ),
        migrations.AddField(
            model_name='digitalhuman',
            name='type',
            field=models.CharField(choices=[('real', '真人'), ('system', '系统预设')], default='real', max_length=20, verbose_name='类型'),
        ),
        migrations.AddField(
            model_name='digitalhuman',
            name='usage_count',
            field=models.IntegerField(default=0, verbose_name='使用次数'),
        ),
        migrations.AddField(
            model_name='digitalhuman',
            name='user_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='用户ID'),
        ),
        migrations.AddField(
            model_name='digitalhuman',
            name='video_url',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='训练视频路径'),
        ),
        migrations.AlterField(
            model_name='digitalhuman',
            name='avatar_url',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='头像图像路径'),
        ),
        migrations.AlterField(
            model_name='digitalhuman',
            name='status',
            field=models.CharField(choices=[('active', '已激活'), ('inactive', '未激活'), ('processing', '处理中')], default='inactive', max_length=20, verbose_name='状态'),
        ),
        migrations.DeleteModel(
            name='Avatar',
        ),
    ]
