<template>
  <StudentLayout
    :userName="studentData.name"
    :userAvatar="studentData.avatar"
    :pageTitle="t('todayLearn.title')"
    activePage="student-today-learn" 
  >
    <div class="flex h-full">
      <!-- Inner Left Sidebar -->
      <StudentInnerSidebar />

      <!-- Main Content for TodayLearnView -->
      <div class="today-learn-content flex-1 flex flex-col items-center justify-start bg-gray-50 p-20 overflow-y-auto">
        <div class="title-container mb-8 text-center">
          <h1 class="text-7xl font-bold text-gray-800 flex items-center justify-center mb-10">
            <span class="inline-flex items-center justify-center w-[130px] h-[130px] bg-[#eef1f9] rounded-full shadow-[8px_8px_20px_#d0d6e8]">
              <span class="text-8xl text-[#4079db]">AI</span>
            </span>
            <span class="ml-3">{{ t('todayLearn.title') }}</span>
          </h1>
        </div>
        <div class="w-full max-w-2xl mx-auto">
          <div 
            class="upload-area bg-white rounded-lg flex flex-col items-center justify-center p-8 relative min-h-[200px] w-full"
            :class="{ 'drag-active': isDragging }"
            :style="{ border: `2px dashed ${isSearchHovered || isDragging ? '#175CD3' : '#e5e5e5'}` }"
            @mouseenter="isSearchHovered = true"
            @mouseleave="isSearchHovered = false"
            @dragenter.prevent="handleDragEnter"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleDrop"
          >
            <!-- Upload Icon -->
            <div class="mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3 3m0 0l-3-3m3 3V6" />
              </svg>
            </div>
            
            <!-- Upload Text -->
            <div class="text-center mb-4">
              <p class="text-sm font-medium text-gray-700">{{ t('todayLearn.upload.area') }}</p>
              <button 
                @click="triggerFileUpload"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2 mt-2"
              ><i class="material-icons text-sm">file_upload</i>
                {{ t('todayLearn.upload.button') }}
              </button>
            </div>
            
            <!-- File Type Info -->
            <p class="text-xs text-gray-500">{{ t('todayLearn.upload.supportTypes') }}</p>

            <input
              type="file"
              ref="fileInput"
              @change="handleFileUpload"
              accept=".doc,.docx,.txt,.pdf"
              class="hidden"
            />
            
            <!-- 拖拽提示遮罩 -->
            <div v-if="isDragging" class="drag-overlay">
              <div class="text-center text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3 3m0 0l-3-3m3 3V6" />
                </svg>
                <span class="text-sm font-medium">{{ t('todayLearn.upload.dropHint') }}</span>
              </div>
            </div>

            <!-- 上传状态提示 -->
            <div v-if="uploadStatus.show" 
                 class="upload-status-tooltip"
                 :class="uploadStatus.type">
              {{ uploadStatus.message }}
            </div>

            <!-- 文件上传进度指示器 -->
            <div v-if="isLoading" class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center">
              <div class="flex items-center space-x-2">
                <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600">{{ t('todayLearn.upload.uploading') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="showStyleDialog"
      :title="t('todayLearn.styleDialog.title')"
      width="450px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="mb-5">
        <p class="text-sm text-gray-500 mb-3">{{ t('todayLearn.styleDialog.description') }}</p>
        <div class="grid grid-cols-1 gap-3">
          <div
            v-for="(name, code) in availableStyles"
            :key="code"
            class="style-option"
            :class="{'selected': selectedStyle === code}"
            @click="selectedStyle = code"
          >
            <div class="flex-1">
              <div class="font-medium">{{ name }}</div>
              <div class="text-gray-500 text-sm">{{ getStyleDescription(code) }}</div>
            </div>
            <div v-if="selectedStyle === code" class="text-blue-500">✓</div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="cancelFileUpload">{{ t('todayLearn.styleDialog.cancel') }}</el-button>
          <el-button type="primary" @click="confirmStyleAndUpload">{{ t('todayLearn.styleDialog.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </StudentLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import StudentInnerSidebar from '@/components/layout/StudentInnerSidebar.vue'
import studentAvatar from '@/assets/images/avatars/student1.png'
import { bookshelfApi } from '@/api/bookshelf'

const router = useRouter()
const { t } = useI18n()

const studentData = ref({
  name: '张同学', 
  avatar: studentAvatar,
})

const isSearchHovered = ref(false)
const isDragging = ref(false)
const isLoading = ref(false)
const fileInput = ref(null)
const searchInput = ref('')
const uploadStatus = ref({
  show: false,
  message: '',
  type: 'success',
})

const showStyleDialog = ref(false)
const selectedStyle = ref('classroom')
const pendingFile = ref(null)

// 风格相关
const availableStyles = ref({})
const styleDescriptions = ref({})
const isLoadingStyles = ref(false)

// 获取风格描述
const getStyleDescription = (code) => {
  if (styleDescriptions.value[code]) {
    return styleDescriptions.value[code];
  }
  return '';
}

// 从API获取所有可用的风格和描述
const fetchAvailableStyles = async () => {
  try {
    isLoadingStyles.value = true;
    const res = await bookshelfApi.getAllSpeechStyles();
    
    // 更新风格列表和描述
    const styles = {};
    const descriptions = {};
    
    Object.keys(res.data).forEach(code => {
      styles[code] = res.data[code].name;
      descriptions[code] = res.data[code].description;
    });
    
    availableStyles.value = styles;
    styleDescriptions.value = descriptions;
    
    // 如果风格列表为空，至少提供一个默认风格
    if (Object.keys(availableStyles.value).length === 0) {
      availableStyles.value = {
        'classroom': '播客风格'
      };
      styleDescriptions.value = {
        'classroom': '标准教学模式，适合课堂讲授'
      };
    }

    // 确保selectedStyle是有效的选项
    if (!styles[selectedStyle.value]) {
      selectedStyle.value = Object.keys(styles)[0] || 'classroom';
    }
  } catch (error) {
    console.error('获取风格列表失败:', error);
    // 提供默认值
    availableStyles.value = {
      'classroom': '播客风格'
    };
    styleDescriptions.value = {
      'classroom': '标准教学模式，适合课堂讲授'
    };
  } finally {
    isLoadingStyles.value = false;
  }
};

const showUploadStatus = (message, type = 'success') => {
  uploadStatus.value = {
    show: true,
    message,
    type
  }
  setTimeout(() => {
    uploadStatus.value.show = false
  }, 3000)
}

const handleDragEnter = (e) => {
  isDragging.value = true
}

const handleDragOver = (e) => {
  isDragging.value = true
}

const handleDragLeave = (e) => {
  const rect = e.target.getBoundingClientRect()
  const x = e.clientX
  const y = e.clientY
  
  if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
    isDragging.value = false
  }
}

const handleDrop = async (e) => {
  isDragging.value = false
  const files = e.dataTransfer.files
  if (files.length > 0) {
    // 保存文件并显示风格选择对话框
    pendingFile.value = files[0]
    showStyleDialog.value = true
  }
}

const triggerFileUpload = () => {
  fileInput.value.click()
}

const handleFileUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    pendingFile.value = file
    showStyleDialog.value = true
  }
}

const cancelFileUpload = () => {
  showStyleDialog.value = false
  pendingFile.value = null
  // Reset file inputs
  if (fileInput.value) fileInput.value.value = ''
}

const confirmStyleAndUpload = async () => {
  if (!pendingFile.value) {
    showStyleDialog.value = false
    return
  }
  
  showStyleDialog.value = false
  await handleFileSubmission(pendingFile.value, selectedStyle.value)
  pendingFile.value = null
}

const handleFileSubmission = async (file, style) => {
  // 检查文件类型
  const allowedTypes = ['.doc', '.docx', '.txt', '.pdf']
  
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
  
  if (!allowedTypes.includes(fileExtension)) {
    showUploadStatus('不支持的文件类型', 'error')
    return
  }

  try {
    isLoading.value = true
    // 使用 bookshelfApi 上传文件，包含风格参数
    const formData = new FormData()
    formData.append('file', file)
    formData.append('style', style) // 添加风格参数
    
    const response = await bookshelfApi.uploadBookshelfFile(formData)
    
    if (response && response.data) {
      showUploadStatus('文件上传成功！')
      console.log('Uploaded file:', file.name, 'Response:', response.data)
      
      // 跳转到书架页面
      setTimeout(() => {
        router.push('/student/bookshelf')
      }, 1000)
    } else {
      throw new Error('上传响应异常')
    }
  } catch (error) {
    showUploadStatus('文件上传失败，请重试', 'error')
    console.error('Upload error:', error)
  } finally {
    isLoading.value = false
  }
}

const handleSearch = async () => {
  if (!searchInput.value.trim()) return
  
  try {
    isLoading.value = true
    // TODO: 实现搜索或URL处理逻辑
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟请求
    console.log('Searching:', searchInput.value)
  } catch (error) {
    console.error('Search error:', error)
  } finally {
    isLoading.value = false
  }
}

// Note: The activePage prop for StudentLayout is set to "ai-learning". 
// This is because "今天学点啥" is conceptually part of the "秘塔AI搜索" functionality (accessed via its inner sidebar).
// If "今天学点啥" should be a distinct top-level item in the main sidebar, 
// its route and activePage value would need to be adjusted accordingly.

onMounted(() => {
  fetchAvailableStyles()
})
</script>

<style scoped>
.today-learn-content {
   height: calc(100vh - 64px); /* Assuming header height is 64px */
}

/* Style options for lecture style selection */
.style-option {
  @apply flex items-center p-3 border rounded cursor-pointer transition-colors border-gray-200 hover:bg-gray-50;
}

.style-option.selected {
  @apply border-blue-500 bg-blue-50;
}

.drag-active {
  background-color: #f0f9ff;
  border-color: #175CD3 !important;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(240, 249, 255, 0.95);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.upload-status-tooltip {
  position: absolute;
  bottom: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  z-index: 30;
  animation: fadeInOut 3s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.upload-status-tooltip.success {
  background-color: #dcfce7;
  color: #166534;
}

.upload-status-tooltip.error {
  background-color: #fee2e2;
  color: #991b1b;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { opacity: 0; }
}

.upload-area {
  transition: all 0.2s ease;
}

.upload-area:hover {
  border-color: #175CD3 !important;
  background-color: #f8fafc;
}
</style> 