from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone
import json
from .base_model_view import BaseModelViewSet
from zhkt.serializers import (
    HomeworkSerializer,
    QuestionSerializer,
    SubmissionSerializer,
    FeedbackSerializer
)
from zhkt.entitys import (
    Homework, 
    Question,
    Student,
    Course,
    Submission,
    Feedback
)

# 作业相关视图集
class HomeworkViewSet(BaseModelViewSet):
    """作业视图集"""
    queryset = Homework.objects.all()
    serializer_class = HomeworkSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().filter(deleted_at__isnull=True)
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        return queryset.order_by('-created_at')

    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """发布作业"""
        homework = self.get_object()
        homework.is_published = True
        homework.publish_date = timezone.now()
        homework.save()
        return Response({'status': 'success', 'message': '作业发布成功'})

    @action(detail=True, methods=['post'])
    def unpublish(self, request, pk=None):
        """撤回作业"""
        homework = self.get_object()
        homework.is_published = False
        homework.publish_date = None
        homework.save()
        return Response({'status': 'success', 'message': '作业已撤回'})

    @action(detail=False, methods=['get'])
    def course_questions(self, request):
        """获取课程的题库题目"""
        course_id = request.query_params.get('course_id')
        if not course_id:
            return Response({'error': '请提供课程ID'}, status=400)
            
        questions = Question.objects.filter(
            course_id=course_id,
            deleted_at__isnull=True
        ).order_by('created_at')
        
        serializer = QuestionSerializer(questions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取作业统计信息"""
        homework = self.get_object()
        # 获取课程关联的所有班级
        class_groups = homework.course.class_groups.filter(deleted_at__isnull=True)
        # 统计这些班级中的学生数量（去重）
        total_students = Student.objects.filter(
            classes__in=class_groups,
            deleted_at__isnull=True
        ).distinct().count()
        submitted_count = homework.submissions.filter(deleted_at__isnull=True).count()
        on_time_count = homework.submissions.filter(
            deleted_at__isnull=True,
            status='submitted'
        ).count()
        late_count = homework.submissions.filter(
            deleted_at__isnull=True,
            status='late'
        ).count()
        
        return Response({
            'total_students': total_students,
            'submitted_count': submitted_count,
            'on_time_count': on_time_count,
            'late_count': late_count,
            'completion_rate': round(submitted_count / total_students * 100, 2) if total_students > 0 else 0
        })

    @action(detail=False, methods=['get'])
    def course_stats(self, request):
        """获取课程作业统计信息"""
        course_id = request.query_params.get('course_id')
        if not course_id:
            return Response({'error': '请提供课程ID'}, status=400)
            
        now = timezone.now()
        homeworks = self.get_queryset().filter(course_id=course_id)
        
        total = homeworks.count()
        ongoing = homeworks.filter(start_time__lte=now, end_time__gt=now).count()
        ended = homeworks.filter(end_time__lte=now).count()
        draft = homeworks.filter(start_time__gt=now).count()
        
        return Response({
            'total': total,
            'ongoing': ongoing,
            'ended': ended,
            'draft': draft
        })

    @action(detail=False, methods=['get'])
    def student_assignments(self, request):
        """获取学生的作业列表"""
        try:
            student = request.user.student_profile
            
            # 获取学生所在班级的所有课程
            courses = Course.objects.filter(
                class_groups__in=student.classes.all(),
                deleted_at__isnull=True
            ).distinct()
            
            # 获取这些课程的所有作业
            homeworks = self.get_queryset().filter(
                course__in=courses,
                is_published=True,
                deleted_at__isnull=True
            )
            
            # 获取作业提交记录
            submissions = {
                sub.homework_id: sub 
                for sub in Submission.objects.filter(
                    student=student,
                    homework__in=homeworks,
                    deleted_at__isnull=True
                )
            }
            
            # 获取作业反馈
            feedbacks = {
                fb.submission.homework_id: fb 
                for fb in Feedback.objects.filter(
                    submission__student=student,
                    submission__homework__in=homeworks,
                    deleted_at__isnull=True
                )
            }
            
            # 构建作业列表数据
            now = timezone.now()
            assignments_data = []
            for homework in homeworks:
                submission = submissions.get(homework.id)
                feedback = feedbacks.get(homework.id)
                
                # 确定作业状态
                if submission:
                    status = submission.status
                elif now > homework.end_time:
                    status = 'overdue'
                else:
                    status = 'pending'
                
                assignment_data = self.get_serializer(homework).data
                assignment_data.update({
                    'status': status,
                    'score': submission.score if submission else None,
                    'has_feedback': bool(feedback),
                    'submitted_at': submission.submitted_at if submission else None
                })
                assignments_data.append(assignment_data)
            
            return Response(assignments_data)
            
        except Student.DoesNotExist:
            return Response(
                {'error': '当前用户不是学生'},
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def student_stats(self, request):
        """获取学生作业统计数据"""
        try:
            student = request.user.student_profile
            
            # 获取学生所在班级的所有课程
            courses = Course.objects.filter(
                class_groups__in=student.classes.all(),
                deleted_at__isnull=True
            ).distinct()
            
            # 获取这些课程的所有作业
            homeworks = self.get_queryset().filter(
                course__in=courses,
                is_published=True,
                deleted_at__isnull=True
            )
            
            # 获取作业提交记录
            submissions = Submission.objects.filter(
                student=student,
                homework__in=homeworks,
                deleted_at__isnull=True
            )
            
            # 获取作业反馈
            feedbacks = Feedback.objects.filter(
                submission__student=student,
                submission__homework__in=homeworks,
                deleted_at__isnull=True
            )
            
            # 计算统计数据
            now = timezone.now()
            total = homeworks.count()
            submitted = submissions.count()
            graded = feedbacks.count()
            pending = homeworks.filter(end_time__gt=now).exclude(
                id__in=submissions.values_list('homework_id', flat=True)
            ).count()
            overdue = homeworks.filter(end_time__lte=now).exclude(
                id__in=submissions.values_list('homework_id', flat=True)
            ).count()
            
            # 计算平均分
            scores = [sub.score for sub in submissions if sub.score is not None]
            average_score = sum(scores) / len(scores) if scores else 0
            
            return Response({
                'total': total,
                'submitted': submitted,
                'graded': graded,
                'pending': pending,
                'overdue': overdue,
                'average_score': round(average_score, 1)
            })
            
        except Student.DoesNotExist:
            return Response(
                {'error': '当前用户不是学生'},
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    @action(detail=True, methods=['get'])
    def student_status(self, request, pk=None):
        """获取学生作业完成状态和答案"""
        try:
            homework = self.get_object()
            student = request.user.student_profile
            
            submission = Submission.objects.filter(
                homework=homework,
                student=student,
                deleted_at__isnull=True
            ).first()
            
            if submission:
                # 解析提交的答案
                try:
                    content = json.loads(submission.content)
                    answers = {
                        str(answer['question_id']): answer['answer']
                        for answer in content.get('answers', [])
                    }
                except:
                    answers = {}
            else:
                answers = {}
            
            return Response({
                'has_submitted': bool(submission),
                'status': submission.status if submission else None,
                'submitted_at': submission.submitted_at if submission else None,
                'score': submission.score if submission else None,
                'answers': answers
            })
            
        except Student.DoesNotExist:
            return Response(
                {'error': '当前用户不是学生'},
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def grading_info(self, request, pk=None):
        """获取作业批改信息"""
        try:
            homework = self.get_object()
            course_id = request.query_params.get('course_id')
            
            if not course_id:
                return Response(
                    {'error': '请提供课程ID'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 获取作业题目
            questions = homework.homework_questions.filter(
                deleted_at__isnull=True
            ).order_by('order')
            
            # 获取提交统计
            submissions = homework.submissions.filter(deleted_at__isnull=True)
            total_submissions = submissions.count()
            graded_count = submissions.filter(score__isnull=False).count()
            pending_count = total_submissions - graded_count
            
            # 计算平均分
            scores = [sub.score for sub in submissions if sub.score is not None]
            average_score = sum(scores) / len(scores) if scores else 0
            
            return Response({
                'homework': self.get_serializer(homework).data,
                'stats': {
                    'totalSubmissions': total_submissions,
                    'gradedCount': graded_count,
                    'pendingCount': pending_count,
                    'averageScore': round(average_score, 1)
                }
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def submissions(self, request, pk=None):
        """获取作业提交记录列表"""
        try:
            homework = self.get_object()
            course_id = request.query_params.get('course_id')
            
            if not course_id:
                return Response(
                    {'error': '请提供课程ID'},
                    status= status.HTTP_400_BAD_REQUEST
                )
            
            # 获取查询参数
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            search = request.query_params.get('search', '')
            submission_status = request.query_params.get('submission_status', '')
            
            # 构建查询
            submissions = homework.submissions.filter(deleted_at__isnull=True)
            
            # 应用搜索过滤
            if search:
                submissions = submissions.filter(
                    Q(student__user__username__icontains=search) |
                    Q(student__user__first_name__icontains=search) |
                    Q(student__user__last_name__icontains=search)
                )
            
            # 应用状态过滤
            if submission_status:
                submissions = submissions.filter(status=submission_status)
            
            # 计算分页
            total = submissions.count()
            start = (page - 1) * page_size
            end = start + page_size
            
            # 获取分页数据
            submissions_data = []
            for submission in submissions[start:end]:
                # 获取学生信息
                student = submission.student
                user = student.user
                
                # 获取反馈信息
                feedback = submission.feedbacks.filter(deleted_at__isnull=True).first()
                
                submissions_data.append({
                    'id': submission.id,
                    'student': {
                        'id': student.id,
                        'name': user.alias,
                        'avatar': user.avatar if user.avatar else None,
                        'email': user.email
                    },
                    'answers': submission.content,
                    'submitted_at': submission.submitted_at,
                    'status': submission.status,
                    'score': submission.score,
                    'feedback': feedback.content if feedback else None
                })
            
            return Response({
                'results': submissions_data,
                'total': total
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def grade(self, request, pk=None):
        """保存作业批改结果"""
        try:
            homework = self.get_object()
            submission_id = request.data.get('submission_id')
            course_id = request.data.get('course_id')
            
            if not submission_id or not course_id:
                return Response(
                    {'error': '缺少必要参数'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 获取提交记录
            try:
                submission = Submission.objects.get(
                    id=submission_id,
                    homework=homework,
                    deleted_at__isnull=True
                )
            except Submission.DoesNotExist:
                return Response(
                    {'error': '提交记录不存在'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 获取教师信息
            try:
                teacher = request.user.teacher_profile
            except:
                return Response(
                    {'error': '当前用户不是教师'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 解析批改数据
            # feedback = request.data.get('feedback', {})
            scores = request.data.get('scores', {})
            overall = request.data.get('overall', '')
            
            # 计算总分
            total_score = sum(float(score) for score in scores.values())
            
            # 更新提交记录
            submission.score = total_score
            submission.status = 'graded'
            submission.save()
            
            # 创建或更新反馈
            feedback_obj, created = Feedback.objects.update_or_create(
                submission=submission,
                teacher=teacher,
                defaults={
                    'content': json.dumps({
                        'scores': scores,
                        'overall': overall
                    })
                }
            )
            
            return Response({
                'message': '批改成功',
                'score': total_score
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def publish_grades(self, request, pk=None):
        """发布作业成绩"""
        try:
            homework = self.get_object()
            course_id = request.data.get('course_id')
            
            if not course_id:
                return Response(
                    {'error': '请提供课程ID'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 检查是否所有提交都已批改
            ungraded = homework.submissions.filter(
                deleted_at__isnull=True,
                score__isnull=True
            ).exists()
            
            if ungraded:
                return Response(
                    {'error': '还有未批改的提交'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 更新作业状态
            homework.is_graded = True
            homework.save()
            
            return Response({
                'message': '成绩发布成功'
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def student_feedback(self, request, pk=None):
        """获取学生作业反馈"""
        try:
            homework = self.get_object()
            
            # 验证学生身份
            try:
                student = request.user.student_profile
            except:
                return Response(
                    {'error': '当前用户不是学生'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 获取提交记录
            submission = Submission.objects.filter(
                homework=homework,
                student=student,
                deleted_at__isnull=True
            ).first()
            
            if not submission:
                return Response(
                    {'error': '未找到提交记录'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 获取反馈信息
            feedback = submission.feedbacks.filter(
                deleted_at__isnull=True
            ).first()
            
            if not feedback:
                return Response(
                    {'error': '未找到反馈信息'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            return Response({
                'feedback': feedback.content
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def homework_grades(self, request, pk=None):
        """获取作业的学生成绩列表"""
        homework = self.get_object()
        course = homework.course
        
        # 获取课程班级的所有学生
        students = Student.objects.filter(
            classes__in=course.class_groups.all()
        ).distinct()
        
        # 获取所有提交记录
        submissions = Submission.objects.filter(
            homework=homework,
            deleted_at__isnull=True
        ).select_related('student', 'student__user')
        
        # 构建提交记录字典
        submission_dict = {sub.student_id: sub for sub in submissions}
        
        # 构建成绩列表
        grades_list = []
        for student in students:
            submission = submission_dict.get(student.id)
            grades_list.append({
                'studentId': student.student_id,
                'name': student.user.alias,
                'score': submission.score if submission and submission.score is not None else None,
                'status': submission.status if submission else None,
                'submittedAt': submission.submitted_at if submission else None
            })
        
        return Response(grades_list)

class SubmissionViewSet(BaseModelViewSet):
    queryset = Submission.objects.all()
    serializer_class = SubmissionSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['post'])
    def submit(self, request):
        """提交作业"""
        try:
            # 获取参数
            homework_id = request.data.get('homework_id')
            content = request.data.get('content')
            
            if not homework_id or not content:
                return Response(
                    {'error': '缺少必要参数'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 获取作业信息
            try:
                homework = Homework.objects.get(
                    id=homework_id,
                    is_published=True,
                    deleted_at__isnull=True
                )
            except Homework.DoesNotExist:
                return Response(
                    {'error': '作业不存在或未发布'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 验证学生身份
            try:
                student = request.user.student_profile
            except:
                return Response(
                    {'error': '当前用户不是学生'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 检查是否已提交
            existing_submission = Submission.objects.filter(
                homework=homework,
                student=student,
                deleted_at__isnull=True
            ).first()
            
            if existing_submission and existing_submission.status == 'graded':
                return Response(
                    {'error': '该作业已批改，不能提交'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 检查提交时间
            now = timezone.now()
            if now < homework.start_time:
                return Response(
                    {'error': '作业还未开始'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 确定提交状态
            submission_status = 'submitted'
            if now > homework.end_time:
                submission_status = 'late'
            
            # 创建或更新提交记录
            if existing_submission:
                existing_submission.content = content
                existing_submission.status = submission_status
                existing_submission.submitted_at = now
                existing_submission.save()
                submission = existing_submission
            else:
                submission = Submission.objects.create(
                    homework=homework,
                    student=student,
                    content=content,
                    status=submission_status,
                    submitted_at=now
                )
            
            return Response({
                'id': submission.id,
                'status': submission.status,
                'submitted_at': submission.submitted_at
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
class FeedbackViewSet(BaseModelViewSet):
    queryset = Feedback.objects.all()
    serializer_class = FeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]