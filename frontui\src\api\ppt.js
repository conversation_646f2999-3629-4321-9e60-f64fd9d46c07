import request from '@/utils/request'
import { useAuthStore } from '@/stores/auth'

/**
 * PPT生成相关API
 */
export const pptApi = {
  /**
   * 获取文多多API Token
   * @returns {Promise} 包含token的Promise
   */
  getToken() {
    return request({
      url: '/ppt/token/',
      method: 'get'
    })
  },
  
  /**
   * 获取PPT项目列表
   * @param {Object} params 请求参数
   * @param {string} [params.search] 搜索关键词
   * @param {string|number} [params.subject_id] 学科ID
   * @param {string} [params.sort] 排序选项
   * @param {number} [params.page=1] 当前页码
   * @param {number} [params.page_size=10] 每页条数
   * @returns {Promise} 包含PPT项目列表的Promise
   */
  getProjects(params = {}) {
    return request({
      url: '/ppt/projects/',
      method: 'get',
      params
    })
  },
  
  /**
   * 获取学科列表
   * @returns {Promise} 包含学科列表的Promise
   */
  getSubjects() {
    return request({
      url: '/subjects/list/',
      method: 'get'
    })
  },
  
  /**
   * 生成PPT内容
   * @param {FormData} formData 请求参数
   * @param {File} [formData.file] 教案文件（二选一）
   * @param {string} [formData.content] 教案文本内容（二选一）
   * @param {string} [formData.teaching_duration] 教学时长(分钟)，默认45
   * @param {string} [formData.teaching_style] 教学风格，默认balanced
   * @param {string} [formData.specific_requirements] 具体需求说明
   * @param {Array<string>} [formData.dataset_ids] 知识库数据集ID列表(可选)
   * @param {Array<string>} [formData.document_ids] 知识库文档ID列表(可选)
   * @returns {Promise} 包含任务ID的Promise
   */
  generatePPT(formData) {
    return request({
      url: '/ppt/generate/',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 保存PPT草稿
   * @param {Object} draftData PPT草稿数据
   * @param {string} draftData.title PPT标题
   * @param {Array} draftData.slides PPT幻灯片内容
   * @param {Object} [draftData.settings] PPT设置信息
   * @returns {Promise} 包含保存结果的Promise
   */
  saveDraft(draftData) {
    return request({
      url: '/ppt/save-draft/',
      method: 'post',
      data: draftData
    })
  },
  
  /**
   * 获取PPT草稿列表
   * @param {number} page 当前页码，默认为1
   * @param {number} pageSize 每页记录数，默认为10
   * @returns {Promise} 包含PPT草稿列表的Promise
   */
  getDraftList(page = 1, pageSize = 10) {
    return request({
      url: '/ppt/drafts/',
      method: 'get',
      params: {
        page,
        page_size: pageSize
      }
    })
  },
  
  /**
   * 获取PPT草稿详情
   * @param {string} draftId 草稿ID
   * @returns {Promise} 包含PPT草稿详情的Promise
   */
  getDraftDetail(draftId) {
    return request({
      url: `/ppt/drafts/${draftId}/`,
      method: 'get'
    })
  },
  
  /**
   * 删除PPT草稿
   * @param {string} draftId 草稿ID
   * @returns {Promise} 包含删除结果的Promise
   */
  deleteDraft(draftId) {
    return request({
      url: `/ppt/drafts/${draftId}/`,
      method: 'delete'
    })
  },
  
  /**
   * 保存文多多生成的PPT ID
   * @param {Object} data 请求数据
   * @param {string} data.ppt_id 文多多PPT ID
   * @param {string} data.title PPT标题
   * @param {string} data.subject_id 学科ID
   * @param {string} [data.description] PPT描述
   * @returns {Promise} 包含保存结果的Promise
   */
  savePptId(data) {
    return request({
      url: '/ppt/save-ppt-id/',
      method: 'post',
      data
    })
  },
  
  /**
   * 获取流式生成的PPT URL
   * @param {string} taskId 任务ID
   * @returns {string} 流式接口URL
   */
  getPPTStreamUrl(taskId) {
    return `/api/ppt/stream/${taskId}/`
  },

  /**
   * 获取流式生成的PPT内容
   * @param {string} taskId 任务ID
   * @returns {Promise<Response>} fetch API响应
   */
  getPPTStream(taskId) {
    const authStore = useAuthStore()
    const url = `/api/ppt/stream/${taskId}/`
    
    console.log('请求PPT流:', url)
    
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Accept': '*/*'
      },
      cache: 'no-cache',
      credentials: 'same-origin',
      mode: 'cors'
    }).then(response => {
      console.log('收到PPT流响应:', response)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response
    }).catch(error => {
      console.error('PPT流请求失败:', error)
      throw error
    })
  }
  
} 