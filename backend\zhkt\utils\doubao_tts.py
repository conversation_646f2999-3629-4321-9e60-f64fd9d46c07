# coding=utf-8
"""
豆包语音合成工具类
基于火山引擎语音技术API
使用固定Token进行认证
"""

import uuid
import json
import logging
import requests
import base64
from typing import Optional, Dict, Any, Union
from pathlib import Path

# 导入配置
try:
    from ..config import DOUBAO_TTS_APPID, DOUBAO_TTS_TOKEN, DOUBAO_TTS_CLUSTER
except ImportError:
    # 如果无法导入配置，使用默认值
    DOUBAO_TTS_APPID = "6535178642"
    DOUBAO_TTS_TOKEN = "DMVTssnhj3eodNy7c64ME1raTUlqeZq7"
    DOUBAO_TTS_CLUSTER = "volcano_tts"

logger = logging.getLogger(__name__)


class DoubaoTTSError(Exception):
    """豆包TTS异常类"""
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(f"TTS Error {code}: {message}")


class DoubaoTTS:
    """豆包语音合成工具类"""
    
    def __init__(self, host: str = "openspeech.bytedance.com"):
        """
        初始化豆包TTS客户端
        
        Args:
            host: API主机地址，默认为openspeech.bytedance.com
        """
        # 使用配置文件中的固定参数
        self.appid = DOUBAO_TTS_APPID
        self.token = DOUBAO_TTS_TOKEN
        self.cluster = DOUBAO_TTS_CLUSTER
        self.host = host
        self.api_url = f"https://{host}/api/v1/tts"
        
        # 默认音频配置
        self.default_audio_config = {
            "encoding": "mp3",
            "speed_ratio": 1.0,
            "loudness_ratio": 1.0,
            "rate": 24000,
        }
        
        # 默认用户配置
        self.default_user_config = {
            "uid": "default_user"
        }
    

    def synthesize(self, text: str, voice_type: str = "BV001_streaming", 
                   output_path: Optional[Union[str, Path]] = None,
                   speed_ratio: float = 1.0, loudness_ratio: float = 1.0,
                   encoding: str = "mp3", rate: int = 24000) -> Union[bytes, str]:
        """
        语音合成
        
        Args:
            text: 要合成的文本
            voice_type: 音色类型，默认BV001_streaming
            output_path: 输出文件路径，如果不提供则返回音频字节数据
            speed_ratio: 语速比例，范围[0.8,2]，默认1.0
            loudness_ratio: 音量比例，范围[0.5,2]，默认1.0
            encoding: 音频编码格式，默认mp3 (wav/pcm/ogg_opus/mp3)
            rate: 音频采样率，默认24000 (8000/16000/24000)
            
        Returns:
            如果提供output_path则返回文件路径，否则返回音频字节数据
        """
        # 构建请求数据，严格按照文档格式
        request_data = {
            "app": {
                "appid": self.appid,
                "token": "access_token",
                "cluster": self.cluster
            },
            "user": {
                "uid": self.default_user_config["uid"]
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": encoding,
                "speed_ratio": speed_ratio,
                "loudness_ratio": loudness_ratio,
                "rate": rate
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "operation": "query"
            }
        }
        
        # 请求头，严格按照文档格式
        headers = {
            "Authorization": f"Bearer;{self.token}",
            "Content-Type": "application/json"
        }
        
        try:
            # 发送HTTP请求
            response = requests.post(
                self.api_url, 
                headers=headers, 
                json=request_data,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            
            # 检查响应状态，根据文档成功码是3000
            if result.get("code") != 3000:
                error_msg = result.get("message", "语音合成失败")
                error_code = result.get("code", -1)
                logger.error(f"豆包TTS API错误: {error_code} - {error_msg}")
                raise DoubaoTTSError(error_code, error_msg)
            
            # 获取音频数据
            audio_data_b64 = result.get("data", "")
            if not audio_data_b64:
                logger.error("豆包TTS API返回空音频数据")
                raise DoubaoTTSError(-1, "未获取到音频数据")
            
            # 解码音频数据
            audio_data = base64.b64decode(audio_data_b64)
            
            # 获取音频时长信息
            addition = result.get("addition", {})
            duration = addition.get("duration", "未知")
            logger.info(f"音频合成成功，时长: {duration}ms")
            
            # 保存到文件或返回数据
            if output_path:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                with open(output_path, 'wb') as f:
                    f.write(audio_data)
                logger.info(f"音频已保存到: {output_path}")
                return str(output_path)
            else:
                return audio_data
                
        except requests.Timeout as e:
            error_msg = f"请求超时: {e}"
            logger.error(error_msg)
            raise DoubaoTTSError(-1, error_msg)
        except requests.ConnectionError as e:
            error_msg = f"网络连接失败: {e}"
            logger.error(error_msg)
            raise DoubaoTTSError(-1, error_msg)
        except requests.HTTPError as e:
            error_msg = f"HTTP错误 {e.response.status_code}: {e}"
            logger.error(error_msg)
            raise DoubaoTTSError(e.response.status_code, error_msg)
        except requests.RequestException as e:
            error_msg = f"网络请求失败: {e}"
            logger.error(error_msg)
            raise DoubaoTTSError(-1, error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"JSON解析失败: {e}"
            logger.error(error_msg)
            raise DoubaoTTSError(-1, error_msg)
        except Exception as e:
            error_msg = f"语音合成失败: {e}"
            logger.error(error_msg)
            raise DoubaoTTSError(-1, error_msg)


# 便捷函数
def create_doubao_tts(host: str = "openspeech.bytedance.com") -> DoubaoTTS:
    """
    创建豆包TTS实例的便捷函数
    
    Args:
        host: API主机地址
        
    Returns:
        DoubaoTTS实例
    """
    return DoubaoTTS(host=host)


def create_doubao_tts_from_config() -> DoubaoTTS:
    """
    使用配置文件创建豆包TTS实例的便捷函数
    
    Returns:
        DoubaoTTS实例
    """
    return DoubaoTTS()


def main():
    """使用示例"""
    print("=== 豆包TTS语音合成示例 ===")
    
    # 创建TTS实例（使用配置文件中的参数）
    tts = DoubaoTTS()

    audio_content = tts.synthesize(
        text="电子商务数据分析概述与基础",
        voice_type="BV001_streaming",  # 使用默认音色
        encoding="mp3",
        speed_ratio=1.0,
        loudness_ratio=1.0
    )

    # 保存音频文件
    with open('1.mp3', "wb") as sf:
        sf.write(audio_content)


if __name__ == "__main__":
    main() 