import request from '@/utils/request'

/**
 * 知识库相关API
 */
export const knowledgeApi = {
  /**
   * 获取所有知识库大分类
   * @param {string} [categoryType] 可选，分类类型 ('personal' 或 'knowledge')
   * @returns {Promise} 包含大分类列表的Promise
   */
  getAllCategories(categoryType) {
    return request({
      url: '/knowledge/categories/',
      method: 'get',
      params: categoryType ? { category_type: categoryType } : {}
    })
  },

  /**
   * 搜索知识库文档
   * @param {string} query 搜索关键词
   * @param {number} page 当前页码，默认为1
   * @param {number} pageSize 每页记录数，默认为10
   * @param {string} [datasetId] 可选，限定特定数据集搜索
   * @param {string} [documentType] 可选，限定文档类型
   * @returns {Promise} 包含搜索结果的Promise
   */
  searchDocuments(query, page = 1, pageSize = 10, datasetId = null, documentType = null) {
    const params = {
      q: query,
      page,
      page_size: pageSize
    }
    
    // 添加可选参数
    if (datasetId) {
      params.dataset_id = datasetId
    }
    
    if (documentType) {
      params.document_type = documentType
    }
    
    return request({
      url: '/knowledge/search/',
      method: 'get',
      params
    })
  },

  /**
   * 获取指定大分类下的所有知识库数据集
   * @param {string} categoryId 大分类ID
   * @returns {Promise} 包含数据集列表的Promise
   */
  getDatasetsByCategory(categoryId) {
    return request({
      url: `/knowledge/${categoryId}/datasets/`,
      method: 'get'
    })
  },

  /**
   * 创建新的知识库数据集
   * @param {string} categoryId 所属大分类ID
   * @param {Object} datasetData 数据集数据
   * @param {string} datasetData.name 数据集名称
   * @param {string} [datasetData.description] 数据集描述
   * @param {string} [datasetData.avatar] 数据集头像Base64
   * @param {string} [datasetData.embedding_model] 嵌入模型名称
   * @returns {Promise} 包含新创建的数据集信息的Promise
   */
  createDataset(categoryId, datasetData) {
    return request({
      url: `/knowledge/${categoryId}/create-dataset/`,
      method: 'post',
      data: datasetData
    })
  },

  /**
   * 获取指定数据集下的所有文档
   * @param {string} datasetId 数据集ID
   * @param {number} page 当前页码，默认为1
   * @param {number} pageSize 每页记录数，默认为10
   * @returns {Promise} 包含文档列表的Promise
   */
  getDocumentsByDataset(datasetId, page = 1, pageSize = 10) {
    return request({
      url: `/knowledge/datasets/${datasetId}/documents/`,
      method: 'get',
      params: {
        page,
        page_size: pageSize
      }
    })
  },

  /**
   * 获取所有知识库文档，支持分页
   * @param {number} page 当前页码，默认为1
   * @param {number} pageSize 每页记录数，默认为10
   * @returns {Promise} 包含文档列表的Promise
   */
  getAllDocuments(page = 1, pageSize = 10) {
    return request({
      url: '/knowledge/all-documents/',
      method: 'get',
      params: {
        page,
        page_size: pageSize
      }
    })
  },

  /**
   * 上传文档到指定数据集
   * @param {string} datasetId 数据集ID
   * @param {FormData} formData 包含文件的表单数据
   * @returns {Promise} 包含上传结果的Promise
   */
  uploadDocuments(datasetId, formData) {
    return request({
      url: `/knowledge/datasets/${datasetId}/upload-documents/`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 解析指定数据集中的文档
   * @param {string} datasetId 数据集ID
   * @param {Array<string>} documentIds 要解析的文档ID列表
   * @returns {Promise} 包含解析结果的Promise
   */
  parseDocuments(datasetId, documentIds) {
    return request({
      url: `/knowledge/datasets/${datasetId}/parse-documents/`,
      method: 'post',
      data: {
        document_ids: documentIds
      }
    })
  },

  /**
   * 批量获取文档解析状态
   * @param {string} datasetId 数据集ID
   * @param {Array<string>} documentIds 文档ID列表，可选
   * @returns {Promise} 包含文档解析状态的Promise
   */
  getDocumentsStatus(datasetId, documentIds) {
    let params = {};
    if (documentIds && documentIds.length > 0) {
      params.document_ids = documentIds.join(',');
    }
    
    return request({
      url: `/knowledge/datasets/${datasetId}/documents-status/`,
      method: 'get',
      params
    })
  },

  /**
   * 获取当前用户的个人文档子分类
   * @returns {Promise} 包含个人文档子分类列表的Promise
   */
  getPersonalDatasets() {
    return request({
      url: '/knowledge/personal/datasets/',
      method: 'get'
    })
  },

  /**
   * 获取当前用户收藏的文档
   * @param {number} page 当前页码，默认为1
   * @param {number} pageSize 每页记录数，默认为10
   * @returns {Promise} 包含收藏文档列表的Promise
   */
  getFavorites(page = 1, pageSize = 10) {
    return request({
      url: '/knowledge/favorites/',
      method: 'get',
      params: {
        page,
        page_size: pageSize
      }
    })
  },

  /**
   * 切换文档收藏状态（收藏/取消收藏）
   * @param {string} documentId 文档ID
   * @returns {Promise} 包含操作结果的Promise
   */
  toggleFavorite(documentId) {
    return request({
      url: `/knowledge/1/documents/${documentId}/toggle-favorite/`,
      method: 'post'
    })
  },

  /**
   * 删除文档(支持批量和单个)
   * @param {string} datasetId 数据集ID
   * @param {Array<string>} documentIds 要删除的文档ID列表，当只有一个元素时为删除单个文档
   * @returns {Promise} 包含删除结果的Promise
   */
  batchDeleteDocuments(datasetId, documentIds) {
    return request({
      url: `/knowledge/del_datasets/${datasetId}/documents/`,
      method: 'delete',
      data: {
        document_ids: documentIds
      }
    })
  },
  
  /**
   * 删除数据集（子分类）
   * @param {string} datasetId 要删除的数据集ID
   * @returns {Promise} 包含删除结果的Promise
   */
  deleteDataset(datasetId) {
    return request({
      url: `/knowledge/del_datasets/${datasetId}/`,
      method: 'delete'
    })
  },
  
  /**
   * 更新数据集（子分类）
   * @param {string} datasetId 要更新的数据集ID
   * @param {Object} datasetData 更新的数据集字段
   * @param {string} [datasetData.name] 数据集名称
   * @param {string} [datasetData.description] 数据集描述
   * @param {string} [datasetData.icon] 数据集图标
   * @param {string} [datasetData.avatar] 数据集头像Base64
   * @returns {Promise} 包含更新结果的Promise
   */
  updateDataset(datasetId, datasetData) {
    return request({
      url: `/knowledge/del_datasets/${datasetId}/update/`,
      method: 'put',
      data: datasetData
    })
  },

  /**
   * 获取知识库文档的文件URL
   * @param {string} documentId 文档ID
   * @returns {Promise} 包含文件URL的Promise
   */
  getDocumentFileUrl(documentId) {
    return request({
      url: `/knowledge/documents/${documentId}/file-url/`,
      method: 'get'
    })
  }
} 