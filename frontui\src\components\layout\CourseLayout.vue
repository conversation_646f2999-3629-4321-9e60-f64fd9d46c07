<template>
  <div class="flex h-screen overflow-hidden" :data-active-page="activePage" :data-active-sub-page="activeSubPage">
    <!-- Sidebar - fixed on desktop, toggleable on mobile -->
    <div class="md:block" :class="{ 'hidden': !showSidebar }">
      <CourseSidebar 
        :is-collapsed="isCollapsed" 
        @toggle-collapse="toggleCollapse"
        :course-name="courseName"
        :course-code="courseCode"
        :course-id="courseId"
        :teacher-name="userName"
        :teacher-avatar="userAvatar"
      />
    </div>

    <!-- Main Content Area -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <!-- Top Header with Breadcrumbs -->
      <UserHeader 
        @toggleSidebar="toggleSidebar"
        @toggleCollapse="toggleCollapse"
        :pageTitle="currentPageTitle"
        :userName="userName"
        :userAvatar="userAvatar"
        :userRole="'teacher'"
      />

      <!-- Main Content - Scrollable independently from header -->
      <div class="flex-1 p-6 overflow-y-auto bg-gray-50">
        <router-view></router-view>
      </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div 
      v-if="showSidebar && isMobile" 
      class="fixed inset-0 bg-black bg-opacity-30 z-10 md:hidden"
      @click="toggleSidebar"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import UserHeader from './UserHeader.vue'
import CourseSidebar from './CourseSidebar.vue'
import { useRoute } from 'vue-router'

// Props for the layout
const props = defineProps({
  pageTitle: {
    type: String,
    default: '课程管理'
  },
  userName: {
    type: String,
    required: true
  },
  userAvatar: {
    type: String,
    required: true
  },
  activePage: {
    type: String,
    default: ''
  },
  activeSubPage: {
    type: String,
    default: ''
  },
  courseId: {
    type: [String, Number],
    required: true
  },
  courseName: {
    type: String,
    required: true
  },
  courseCode: {
    type: String,
    default: ''
  }
})

// Route
const route = useRoute()

// State
const showSidebar = ref(true)
const isMobile = ref(window.innerWidth < 768)
const isCollapsed = ref(false)

// 计算当前页面标题
const currentPageTitle = computed(() => {
  return route.meta.title ? `${props.courseName} - ${route.meta.title}` : props.courseName
})

// Toggle sidebar visibility (primarily for mobile view)
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// Detect window resize to determine if we're on mobile
const handleResize = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    showSidebar.value = true
  }
}

// Initialize and setup event listeners
onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
})

// Clean up
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* Add smooth transition to sidebar toggle */
.md\:block {
  transition: all 0.3s ease;
}

/* Active menu item styles */
.router-link-active {
  background-color: rgb(239 246 255);
  color: rgb(37 99 235);
}
</style> 