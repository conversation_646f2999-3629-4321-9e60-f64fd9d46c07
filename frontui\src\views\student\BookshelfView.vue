<template>
  <StudentLayout
    :userName="studentData.name"
    :userAvatar="studentData.avatar"
    :pageTitle="t('bookshelf.title')"
    activePage="bookshelf" 
  >
    <div class="flex h-full">
      <!-- Inner Left Sidebar -->
      <StudentInnerSidebar />

      <!-- Main Content for BookshelfView -->
      <div 
        class="bookshelf-content flex-1 flex flex-col bg-white p-6 overflow-y-auto relative"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
      >
        <!-- Drop zone overlay -->
        <div 
          v-if="isDragging" 
          class="absolute inset-0 bg-blue-50 bg-opacity-70 border-2 border-dashed border-blue-500 z-10 flex items-center justify-center"
        >
          <div class="bg-white p-6 rounded-lg shadow-lg text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
            <h3 class="text-xl font-medium text-gray-900 mb-1">{{ t('bookshelf.addDocument.dropHint') }}</h3>
            <p class="text-gray-500">PDF, DOCX</p>
          </div>
        </div>
        
        <!-- Upload progress overlay -->
        <div 
          v-if="isUploading" 
          class="absolute inset-0 bg-white bg-opacity-80 z-10 flex items-center justify-center"
        >
          <div class="bg-white p-6 rounded-lg shadow-lg text-center max-w-md w-full">
            <svg class="animate-spin h-10 w-10 text-blue-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h3 class="text-xl font-medium text-gray-900 mb-4">
              {{ currentOperation === 'delete' ? t('bookshelf.upload.deleting') : t('bookshelf.upload.uploading') }}
            </h3>
            <!-- 只有上传时才显示进度条 -->
            <template v-if="currentOperation === 'upload'">
              <div class="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                <div class="bg-blue-600 h-2.5 rounded-full" :style="{width: `${uploadProgress.total ? Math.round(uploadProgress.completed / uploadProgress.total * 100) : 0}%`}"></div>
              </div>
              <p class="text-sm text-gray-500">
                {{ t('bookshelf.upload.progress', { completed: uploadProgress.completed, total: uploadProgress.total }) }}
              </p>
            </template>
            <!-- 删除时只显示简单提示 -->
            <template v-else-if="currentOperation === 'delete'">
              <p class="text-sm text-gray-500">{{ t('bookshelf.upload.pleaseWait') }}</p>
            </template>
          </div>
        </div>

        <!-- Header Section -->
        <div class="mb-6">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <h1 class="text-2xl font-bold text-gray-800 mr-2">{{ t('bookshelf.title') }}</h1>
              <span class="bg-gray-200 text-gray-700 text-xs font-semibold px-2 py-0.5 rounded-full">{{ t('bookshelf.stats', { count: bookshelfItems.length }) }}</span>
              
              <!-- 大纲生成状态提示 -->
              <div v-if="generatingOutlineItems.size > 0" class="ml-3 flex items-center">
                <svg class="animate-spin h-4 w-4 text-orange-500 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-0.5 rounded-full">
                  {{ t('bookshelf.generating', { count: generatingOutlineItems.size }) }}
                </span>
              </div>
              
              <!-- 批量操作按钮 -->
              <div class="ml-4 flex items-center space-x-2" v-if="bookshelfItems.length > 0">
                <el-button 
                  size="small" 
                  @click="toggleBatchMode"
                  :type="isBatchMode ? 'primary' : 'default'"
                >
                  {{ isBatchMode ? t('bookshelf.exitBatch') : t('bookshelf.batchOperation') }}
                </el-button>
                
                <template v-if="isBatchMode">
                  <el-button size="small" @click="selectAll">
                    {{ selectedItems.length === filteredBookshelfItems.length ? t('bookshelf.cancelSelect') : t('bookshelf.selectAll') }}
                  </el-button>
                  
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="handleBatchDelete"
                    :disabled="selectedItems.length === 0"
                  >
                    {{ t('bookshelf.delete', { count: selectedItems.length }) }} ({{ selectedItems.length }})
                  </el-button>
                </template>
              </div>
            </div>
            <div class="relative w-64">
              <input 
                type="text" 
                :placeholder="t('bookshelf.search')"
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                v-model="searchTerm"
                @input="handleSearchChange"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
          <p class="text-sm text-gray-500 mb-5">{{ t('bookshelf.slogan') }}</p>
          
        </div>

        <!-- Bookshelf Grid -->
        <div 
          class="grid gap-6"
          style="grid-template-columns: repeat(auto-fill, 200px); justify-content: center;"
        >
          <!-- Add Document Button -->
          <div 
            class="add-document-card aspect-w-3 aspect-h-4 rounded-lg border-2 border-dashed border-blue-500 hover:border-blue-500 text-gray-400 hover:text-blue-500 flex flex-col items-center justify-center transition-colors duration-150 p-4"
            @click="handleAddDocument"
            :class="{'border-blue-600 bg-blue-50': isDragging}"
            :title="t('bookshelf.addDocument.title')"
          >
            <div class="flex flex-col items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                </svg>
                <span class="text-sm font-medium">{{ isDragging ? t('bookshelf.addDocument.dropHint') : t('bookshelf.addDocument.title') }}</span>
                <span v-if="!isDragging" class="text-xs text-gray-400 mt-1">{{ t('bookshelf.addDocument.supportDrag') }}</span>
            </div>
          </div>

          <!-- Bookshelf Items -->
          <div 
            v-for="item in filteredBookshelfItems" 
            :key="item.id" 
            class="document-card group relative aspect-w-3 aspect-h-4 rounded-lg border border-gray-200 bg-white shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-150"
            :class="{ 
              'ring-2 ring-blue-500': isBatchMode && selectedItems.includes(item.id),
              'ring-2 ring-orange-400 border-orange-400': item.outline_status === 'generating'
            }"
          >
            <!-- 大纲生成中的状态指示器 -->
            <div 
              v-if="item.outline_status === 'generating'" 
              class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-400 opacity-80"
            >
              <div class="h-full bg-gradient-to-r from-orange-500 to-yellow-500 animate-pulse"></div>
            </div>
            
            <!-- 批量选择复选框 -->
            <div 
              v-if="isBatchMode" 
              class="absolute top-2 left-2 z-30"
              @click.stop
            >
              <el-checkbox 
                :model-value="selectedItems.includes(item.id)"
                @change="toggleItemSelection(item.id)"
                size="large"
              />
            </div>
            
            <div 
              class="w-full h-3/4 bg-gray-100 flex items-center justify-center overflow-hidden relative" 
              @click="isBatchMode ? toggleItemSelection(item.id) : handlePreview(item)"
            >
              <!-- 正在生成大纲的覆盖层 -->
              <div 
                v-if="item.outline_status === 'generating'"
                class="absolute inset-0 bg-orange-50 bg-opacity-90 flex flex-col items-center justify-center z-20"
              >
                <svg class="animate-spin h-8 w-8 text-orange-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-xs text-orange-600 font-medium">{{ t('bookshelf.processingTime') }}</span>
              </div>
              
              <!-- Placeholder for document preview/icon -->
              <img v-if="item.previewUrl" :src="item.previewUrl" alt="Preview" class="object-cover w-full h-full">
              <div v-else class="text-gray-300">
                <svg v-if="item.type === 'text'" xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                <svg v-else-if="item.type === 'video'" xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
                <!-- Add more icons for other types -->
              </div>
              <div v-if="item.type === 'video'" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-opacity">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-white opacity-80" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            <div 
              class="p-3 h-1/6" 
              @click="isBatchMode ? toggleItemSelection(item.id) : handlePreview(item)"
            >
              <h3 class="text-sm font-medium text-gray-800 truncate flex items-center" :title="item.name">
                {{ item.name }}
                <!-- 生成状态小图标 -->
                <svg 
                  v-if="item.outline_status === 'generating'" 
                  class="animate-spin h-3 w-3 text-orange-500 ml-1 flex-shrink-0" 
                  xmlns="http://www.w3.org/2000/svg" 
                  fill="none" 
                  viewBox="0 0 24 24"
                >
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </h3>
              <!-- Można dodać więcej informacji, np. typ pliku, data dodania -->
            </div>
             <!-- Action buttons on hover -->
            <div 
              v-if="!isBatchMode"
              class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-150 flex flex-col items-end z-30"
              @click.stop
            >
              <el-popover
                placement="bottom-end"
                :width="140"
                trigger="click"
                popper-class="document-card-popover"
                :hide-after="0"
                :show-arrow="false"
                effect="light"
                transition="el-zoom-in-top"
              >
                <template #reference>
                  <button class="p-1 bg-white rounded-full shadow hover:bg-gray-100" :title="t('bookshelf.documentCard.moreActions')">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /></svg>
                  </button>
                </template>
                <div class="py-1">
                  <el-button 
                    text 
                    @click.stop="handleEditName(item)" 
                    class="w-full !justify-start !text-gray-700 !text-xs !px-3 !py-2 hover:!bg-gray-100"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    {{ t('bookshelf.documentCard.editName') }}
                  </el-button>
                  <div class="border-t border-gray-100 my-1"></div>
                  <el-button 
                    text 
                    @click.stop="handleDeleteItem(item)" 
                    class="w-full !justify-start !text-red-600 !text-xs !px-3 !py-2 hover:!bg-red-50"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    {{ t('bookshelf.documentCard.delete') }}
                  </el-button>
                </div>
              </el-popover>
            </div>
          </div>
        </div>
        
        <!-- Hidden file input for adding documents -->
        <input type="file" ref="fileInput" @change="handleFileSelected" class="hidden" multiple accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.mp4,.mov">

      </div>
    </div>
    
    <!-- 编辑名称对话框 -->
    <el-dialog
      v-model="showEditNameDialog"
      :title="t('bookshelf.editNameDialog.title')"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-input v-model="editingName" :placeholder="t('bookshelf.editNameDialog.placeholder')" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditNameDialog = false">{{ t('bookshelf.editNameDialog.cancel') }}</el-button>
          <el-button type="primary" @click="confirmEditName">{{ t('bookshelf.editNameDialog.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 选择讲课风格对话框 -->
    <el-dialog
      v-model="showStyleDialog"
      :title="t('bookshelf.styleDialog.title')"
      width="450px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="mb-5">
        <p class="text-sm text-gray-500 mb-3">{{ t('bookshelf.styleDialog.selectStyle') }}</p>
        <div class="grid grid-cols-1 gap-3">
          <div
            v-for="(name, code) in availableStyles"
            :key="code"
            class="style-option"
            :class="{'selected': selectedStyle === code}"
            @click="selectedStyle = code"
          >
            <div class="flex-1">
              <div class="font-medium">{{ name }}</div>
              <div class="text-gray-500 text-sm">{{ getStyleDescription(code) }}</div>
            </div>
            <div v-if="selectedStyle === code" class="text-blue-500">✓</div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="cancelStyleSelection">{{ t('bookshelf.styleDialog.cancelUpload') }}</el-button>
          <el-button type="primary" @click="confirmStyleAndUpload">{{ t('bookshelf.styleDialog.confirmAndUpload') }}</el-button>
        </div>
      </template>
    </el-dialog>
    
  </StudentLayout>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';
import { bookshelfApi } from '@/api/bookshelf';
import StudentLayout from '@/components/layout/StudentLayout.vue';
import StudentInnerSidebar from '@/components/layout/StudentInnerSidebar.vue';
import studentAvatar from '@/assets/images/avatars/student1.png'; // Assuming you have this
import { useRouter } from 'vue-router';
import { ElPopover, ElButton, ElMessageBox, ElMessage, ElDialog, ElInput, ElCheckbox } from 'element-plus';

const { t } = useI18n();

const studentData = ref({
  name: t('general.defaultStudentName'), 
  avatar: studentAvatar,
});

const searchTerm = ref('');
const activeTab = ref('default'); // Changed from '默认' to 'default'
const isUploading = ref(false);
const uploadProgress = ref({
  total: 0,
  completed: 0
});
const currentOperation = ref('upload'); // 'upload' 或 'delete'

// 移除tabs数组，因为已不再使用

const bookshelfItems = ref([]);
const fileInput = ref(null);
const router = useRouter();
// 编辑名称相关变量
const showEditNameDialog = ref(false);
const editingName = ref('');
const editingItemId = ref(null);

const isDragging = ref(false);

// 批量操作相关变量
const isBatchMode = ref(false);
const selectedItems = ref([]);

// 大纲生成监控相关变量
const generatingOutlineItems = ref(new Set()); // 正在生成大纲的文档ID集合
const monitoringInterval = ref(null); // 轮询定时器
const lastRefreshTime = ref(0); // 上次刷新时间，防止频繁刷新

// 风格选择相关变量
const showStyleDialog = ref(false);
const selectedStyle = ref('classroom');
const pendingFiles = ref([]);

// 风格相关
const availableStyles = ref({})
const styleDescriptions = ref({})
const isLoadingStyles = ref(false)

// 获取风格描述
const getStyleDescription = (code) => {
  if (styleDescriptions.value[code]) {
    return styleDescriptions.value[code];
  }
  return '';
}

// 从API获取所有可用的风格和描述
const fetchAvailableStyles = async () => {
  try {
    isLoadingStyles.value = true;
    const res = await bookshelfApi.getAllSpeechStyles();
    
    // 更新风格列表和描述
    const styles = {};
    const descriptions = {};
    
    Object.keys(res.data).forEach(code => {
      styles[code] = res.data[code].name;
      descriptions[code] = res.data[code].description;
    });
    
    availableStyles.value = styles;
    styleDescriptions.value = descriptions;
    
    // 如果风格列表为空，至少提供一个默认风格
    if (Object.keys(availableStyles.value).length === 0) {
      availableStyles.value = {
        'classroom': '播客风格'
      };
      styleDescriptions.value = {
        'classroom': '标准教学模式，适合课堂讲授'
      };
    }

    // 确保selectedStyle是有效的选项
    if (!styles[selectedStyle.value]) {
      selectedStyle.value = Object.keys(styles)[0] || 'classroom';
    }
  } catch (error) {
    console.error('获取风格列表失败:', error);
    // 提供默认值
    availableStyles.value = {
      'classroom': '播客风格'
    };
    styleDescriptions.value = {
      'classroom': '标准教学模式，适合课堂讲授'
    };
  } finally {
    isLoadingStyles.value = false;
  }
};

const fetchBookshelf = async () => {
  const res = await bookshelfApi.getBookshelfList();
  bookshelfItems.value = res.data.map(item => ({
    id: item.id,
    name: item.name || item.title,
    type: item.type,
    previewUrl: item.cover_image_path , // 使用封面图
    file_path: item.file_path,
    created_at: item.created_at,
    outline_status: item.outline_status, // 新增大纲状态
    has_chapters: item.has_chapters,
    chapters_count: item.chapters_count
  }));
  
  // 更新正在生成大纲的文档列表
  updateGeneratingItems();
  
  // 如果有正在生成的文档，启动监控
  if (generatingOutlineItems.value.size > 0) {
    startOutlineMonitoring();
  } else {
    stopOutlineMonitoring();
  }
};

// 更新正在生成大纲的文档集合
const updateGeneratingItems = () => {
  const generating = new Set();
  bookshelfItems.value.forEach(item => {
    if (item.outline_status === 'generating') {
      generating.add(item.id);
    }
  });
  generatingOutlineItems.value = generating;
};

// 开始监控大纲生成状态
const startOutlineMonitoring = () => {
  if (monitoringInterval.value) {
    clearInterval(monitoringInterval.value);
  }
  
  monitoringInterval.value = setInterval(async () => {
    if (generatingOutlineItems.value.size === 0) {
      stopOutlineMonitoring();
      return;
    }
    
    // 检查所有正在生成的文档状态
    const checkPromises = Array.from(generatingOutlineItems.value).map(async (docId) => {
      try {
        const res = await bookshelfApi.checkOutlineStatus(docId);
        return { docId, status: res.data };
      } catch (error) {
        console.error(`检查文档 ${docId} 状态失败:`, error);
        return { docId, status: null };
      }
    });
    
    const results = await Promise.all(checkPromises);
    let hasCompletedItems = false;
    const completedDocIds = [];
    
    results.forEach(({ docId, status }) => {
      if (status && status.status === 'completed') {
        // 大纲生成完成，从监控列表中移除
        generatingOutlineItems.value.delete(docId);
        hasCompletedItems = true;
        completedDocIds.push(docId);
      }
    });
    
    // 如果有完成的文档大纲生成，重新获取整个书架数据
    if (hasCompletedItems) {
      const now = Date.now();
      // 防止频繁刷新，至少间隔2秒
      if (now - lastRefreshTime.value > 2000) {
        lastRefreshTime.value = now;
        try {
          await fetchBookshelf(); // 重新获取书架数据，包括封面图等
          
          // 显示具体完成的文档名称
          const completedNames = completedDocIds.map(docId => {
            const item = bookshelfItems.value.find(i => i.id === docId);
            return item ? item.name : `${t('bookshelf.document')}${docId}`;
          }).join('、');
          
          ElMessage({
            type: 'success',
            message: `${completedNames} ${t('bookshelf.outlineCompleted')}`
          });
        } catch (error) {
          console.error('刷新书架数据失败:', error);
          // 如果刷新失败，仍然手动更新状态
          results.forEach(({ docId, status }) => {
            if (status && status.status === 'completed') {
              const item = bookshelfItems.value.find(i => i.id === docId);
              if (item) {
                item.outline_status = 'completed';
                item.has_chapters = status.has_chapters;
                item.chapters_count = status.chapters_count;
              }
            }
          });
          ElMessage({
            type: 'success',
            message: t('bookshelf.outlineCompleted')
          });
        }
      } else {
        // 如果间隔太短，只做状态更新不刷新整个列表
        results.forEach(({ docId, status }) => {
          if (status && status.status === 'completed') {
            const item = bookshelfItems.value.find(i => i.id === docId);
            if (item) {
              item.outline_status = 'completed';
              item.has_chapters = status.has_chapters;
              item.chapters_count = status.chapters_count;
            }
          }
        });
      }
    }
  }, 3000); // 每3秒检查一次
};

// 停止监控
const stopOutlineMonitoring = () => {
  if (monitoringInterval.value) {
    clearInterval(monitoringInterval.value);
    monitoringInterval.value = null;
  }
};

const handleEditName = (item) => {
  editingName.value = item.name;
  editingItemId.value = item.id;
  showEditNameDialog.value = true;
};

const confirmEditName = () => {
  if (editingName.value && editingName.value.trim() !== '') {
    const itemToUpdate = bookshelfItems.value.find(i => i.id === editingItemId.value);
    if (itemToUpdate) {
      itemToUpdate.name = editingName.value.trim();
      // 这里可以添加API调用来更新服务器上的名称
      ElMessage({
        type: 'success',
        message: t('bookshelf.nameUpdated')
      });
    }
  }
  showEditNameDialog.value = false;
};

const handleDeleteItem = async (item) => {
  try {
    await ElMessageBox.confirm(
      t('bookshelf.notifications.confirmDelete', { name: item.name }),
      t('bookshelf.notifications.deleteConfirmTitle'),
      {
        confirmButtonText: t('bookshelf.notifications.confirmButtonText'),
        cancelButtonText: t('bookshelf.notifications.cancelButtonText'),
        type: 'warning',
      }
    );
    
    await bookshelfApi.deleteBookshelfFile(item.id);
    await fetchBookshelf();
    ElMessage({
      type: 'success',
      message: t('bookshelf.notifications.deleteSuccess')
    });
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(t('bookshelf.notifications.deleteFailed'));
      console.error('删除失败:', e);
    }
  }
};

onMounted(async () => {
  fetchBookshelf();
  fetchAvailableStyles();
});

onBeforeUnmount(() => {
  // 清理监控定时器
  stopOutlineMonitoring();
  // 清理其他可能的定时器或监听器
  generatingOutlineItems.value.clear();
});

const filteredBookshelfItems = computed(() => {
  let itemsToFilter = bookshelfItems.value;
  if (!searchTerm.value.trim()) {
    return itemsToFilter;
  }
  const lowerSearchTerm = searchTerm.value.toLowerCase();
  return itemsToFilter.filter(item => 
    item.name.toLowerCase().includes(lowerSearchTerm)
  );
});

// 当搜索条件改变时，清理不在当前筛选结果中的选中项
const cleanupSelectedItems = () => {
  if (isBatchMode.value && selectedItems.value.length > 0) {
    const currentItemIds = filteredBookshelfItems.value.map(item => item.id);
    selectedItems.value = selectedItems.value.filter(id => currentItemIds.includes(id));
  }
};

// 监听搜索条件变化
const handleSearchChange = () => {
  cleanupSelectedItems();
};

const handleAddDocument = () => {
  fileInput.value?.click();
};

const handleFileSelected = async (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    // 过滤出有效文件
    const validFiles = Array.from(files).filter(file => {
      const ext = file.name.split('.').pop().toLowerCase();
      return ['pdf', 'docx'].includes(ext);
    });
    
    if (validFiles.length === 0) {
      ElMessage({
        type: 'warning',
        message: t('bookshelf.notifications.fileTypesHint')
      });
      return;
    }
    
    // 保存待上传文件并显示风格选择对话框
    pendingFiles.value = validFiles;
    showStyleDialog.value = true;
    
    // 清空文件输入框，以便下次上传
    if (fileInput.value) fileInput.value.value = '';
  }
};

const cancelStyleSelection = () => {
  showStyleDialog.value = false;
  pendingFiles.value = [];
  
  // 清空文件输入框
  if (fileInput.value) fileInput.value.value = '';
};

const confirmStyleAndUpload = async () => {
  if (pendingFiles.value.length === 0) {
    showStyleDialog.value = false;
    return;
  }
  
  showStyleDialog.value = false;
  
  // 开始上传
  currentOperation.value = 'upload';
  isUploading.value = true;
  uploadProgress.value = {
    total: pendingFiles.value.length,
    completed: 0
  };
  
  let successCount = 0;
  
  for (let i = 0; i < pendingFiles.value.length; i++) {
    const file = pendingFiles.value[i];
    try {
      // 使用FormData添加风格参数
      const formData = new FormData();
      formData.append('file', file);
      formData.append('style', selectedStyle.value);
      
      await bookshelfApi.uploadBookshelfFile(formData);
      successCount++;
      ElMessage({
        type: 'success',
        message: t('bookshelf.notifications.uploadSuccess', { name: file.name })
      });
    } catch (err) {
      ElMessage({
        type: 'error',
        message: err?.response?.data?.detail || t('general.uploadFailed')
      });
    } finally {
      uploadProgress.value.completed++;
    }
  }
  
  isUploading.value = false;
  pendingFiles.value = [];
  
  // 如果有文件上传成功，刷新书架并启动监控
  if (successCount > 0) {
    await fetchBookshelf();
  }
};

const handlePreview = async (item) => {
  // 如果大纲还在生成中，直接提示
  if (item.outline_status === 'generating') {
    ElMessage({
      message: t('bookshelf.preview.generating'),
      type: 'warning'
    });
    return;
  }
  
  try {
    // 先获取文档的章节信息
    const res = await bookshelfApi.getChapters(item.id);
    
    // 检查是否有章节（大纲）
    if (res.data.length === 0) {
      ElMessage({
        message: t('bookshelf.preview.generating'),
        type: 'warning'
      });
      return;
    }
    
    // 如果有章节（大纲已生成），就允许导航到预览页面
    router.push({ name: 'student-html-preview', params: { docId: item.id } });
  } catch (error) {
    console.error('获取章节信息失败:', error);
    ElMessage.error(t('bookshelf.preview.failed'));
  }
};

const handleDragOver = (event) => {
  isDragging.value = true;
  event.dataTransfer.dropEffect = 'copy';
};

const handleDragLeave = (event) => {
  // 确保只有当离开整个区域时才设置为false
  // 检查是否从父元素离开到子元素
  const rect = event.currentTarget.getBoundingClientRect();
  const x = event.clientX;
  const y = event.clientY;
  
  // 如果鼠标位置在元素外部，则认为已离开
  if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
    isDragging.value = false;
  }
};

const handleDrop = async (event) => {
  isDragging.value = false;
  const files = event.dataTransfer.files;
  
  if (files && files.length > 0) {
    const validFiles = Array.from(files).filter(file => {
      const ext = file.name.split('.').pop().toLowerCase();
      return ['pdf', 'docx'].includes(ext);
    });
    
    if (validFiles.length === 0) {
      ElMessage({
        type: 'warning',
        message: t('bookshelf.fileTypesHint')
      });
      return;
    }
    
    // 保存待上传文件并显示风格选择对话框
    pendingFiles.value = validFiles;
    showStyleDialog.value = true;
  }
};

const toggleBatchMode = () => {
  isBatchMode.value = !isBatchMode.value;
  if (!isBatchMode.value) {
    selectedItems.value = [];
    // 确保进度状态也被重置
    if (isUploading.value) {
      isUploading.value = false;
      uploadProgress.value = { total: 0, completed: 0 };
    }
  }
};

const toggleItemSelection = (itemId) => {
  const index = selectedItems.value.indexOf(itemId);
  if (index > -1) {
    selectedItems.value.splice(index, 1);
  } else {
    selectedItems.value.push(itemId);
  }
};

const selectAll = () => {
  if (selectedItems.value.length === filteredBookshelfItems.value.length) {
    selectedItems.value = [];
  } else {
    selectedItems.value = filteredBookshelfItems.value.map(item => item.id);
  }
};

const handleBatchDelete = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning(t('bookshelf.notifications.selectDocumentsToDelete'));
    return;
  }

  try {
    await ElMessageBox.confirm(
      t('bookshelf.notifications.confirmBatchDelete', { count: selectedItems.value.length }),
      t('bookshelf.notifications.batchDeleteConfirmTitle'),
      {
        confirmButtonText: t('bookshelf.notifications.confirmButtonText'),
        cancelButtonText: t('bookshelf.notifications.cancelButtonText'),
        type: 'warning',
      }
    );

    // 显示删除中状态
    currentOperation.value = 'delete';
    isUploading.value = true;

    try {
      // 使用批量删除API
      const response = await bookshelfApi.deleteDocumentsFromBookshelf(selectedItems.value);
      
      ElMessage({
        type: 'success',
        message: response.message || t('bookshelf.notifications.batchDeleteSuccess', { count: response.data?.deleted_count || selectedItems.value.length })
      });
      
      // 刷新书架
      await fetchBookshelf();
      
    } catch (error) {
      console.error('批量删除失败:', error);
      ElMessage({
        type: 'error',
        message: error?.response?.data?.message || t('bookshelf.notifications.batchDeleteFailed')
      });
    }

    isUploading.value = false;

    // 清空选择并退出批量模式
    selectedItems.value = [];
    isBatchMode.value = false;

  } catch (e) {
    isUploading.value = false;
    if (e !== 'cancel') {
      ElMessage.error(t('bookshelf.notifications.batchDeleteFailed'));
      console.error('批量删除失败:', e);
    }
  }
};

// Make sure the activePage for StudentLayout is correct if "书架" becomes a main navigation item.
// For now, it's under "ai-learning" conceptually like "TodayLearnView".
// If it's meant to be a distinct top-level item (visible in the main StudentLayout sidebar),
// this activePage prop and the router configuration would need adjustments.

</script>

<style scoped>
.bookshelf-content {
   height: calc(100vh - 64px); /* Assuming header height is 64px */
}

/* Style options for lecture style selection */
.style-option {
  @apply flex items-center p-3 border rounded cursor-pointer transition-colors border-gray-200 hover:bg-gray-50;
}

.style-option.selected {
  @apply border-blue-500 bg-blue-50;
}

/* Style for the cards to have a consistent aspect ratio */
.add-document-card,
.document-card {
  /* Tailwind's aspect-ratio utilities might need to be enabled in tailwind.config.js if not working by default */
  /* Or use padding-bottom trick for aspect ratio if JIT mode is not fully covering this. */
  /* For simplicity, ensure @tailwindcss/aspect-ratio plugin is installed or use explicit padding. */
  position: relative;
  height: 300px; /* Fixed height for cards, adjust as needed */
  width: 200px;
}

.add-document-card > *,
.document-card > .p-3 { /* Ensure content within card respects boundaries */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.document-card > .p-3 { /* Adjust for text content area */
    height: 20%;
    top: 80%;
}

.document-card .w-full.h-3\/4 { /* Preview area */
    position: absolute;
    top:0;
    left:0;
    width: 100%;
    height: 80%;
}
.add-document-card > svg, .add-document-card > span {
    position: static; /* Override absolute positioning for flex layout */
}


/* Ensure icons within cards are reasonably sized */
.document-card .h-16.w-16 {
  max-height: 80%;
  max-width: 80%;
}

/* 弹出菜单样式 */
:deep(.document-card-popover) {
  @apply rounded-lg shadow-lg;
  
  .el-popper__arrow {
    display: none;
  }
}
</style> 