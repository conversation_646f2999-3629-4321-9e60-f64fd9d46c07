<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="创建新讲稿"
    activePage="content-creation"
    activeSubPage="script"
  >
    <div class="space-y-6">
      <!-- 顶部导航 -->
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold flex items-center">
          <el-icon class="mr-2 text-blue-600"><Back /></el-icon>
          <span @click="goBack" class="cursor-pointer hover:text-blue-600">返回讲稿列表</span>
        </h2>
        
        <div class="text-gray-500 text-sm">
          <span>讲稿ID: </span>
          <span class="text-gray-700 font-medium">{{ draftId || '未保存' }}</span>
        </div>
      </div>

      <!-- 步骤导航 -->
      <el-steps :active="currentStep" finish-status="success" class="mb-8">
        <el-step title="上传幻灯片" description="选择或上传PPT" />
        <el-step title="编辑幻灯片" description="逐个完善讲稿" />
        <el-step title="配置参数" description="设置生成选项" />
        <el-step title="调整内容" description="预览和完善讲稿" />
      </el-steps>
      
      <!-- 第一步：上传幻灯片 -->
      <div v-if="currentStep === 0">
        <div class="bg-white rounded-lg p-4 shadow-sm max-w-4xl mx-auto">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <el-icon class="mr-2 text-blue-600"><Document /></el-icon>
            选择PPT幻灯片
          </h2>
          
          <!-- 已上传的幻灯片预览 -->
          <div v-if="slides.length > 0" class="mb-6">
            <div class="mb-4 flex justify-between items-center">
              <h3 class="text-base font-medium">已选择 {{ slides.length }} 张幻灯片</h3>
              <div class="flex space-x-2">
                <el-button size="small" @click="removeAllSlides">
                  <el-icon><Delete /></el-icon>
                  <span>清空</span>
                </el-button>
                <el-button type="primary" size="small" @click="refreshSlides">
                  <el-icon><Refresh /></el-icon>
                  <span>刷新</span>
                </el-button>
              </div>
            </div>
            
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div 
                v-for="(slide, index) in slides" 
                :key="index"
                class="border rounded-md cursor-pointer relative hover:border-blue-300"
                :class="{'border-blue-500 shadow-md': selectedSlideIndex === index}"
                @click="selectSlide(index)"
              >
                <div class="absolute top-2 right-2 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                  {{ index + 1 }}
                </div>
                <div class="absolute top-2 left-2">
                  <el-button 
                    type="danger" 
                    size="small" 
                    circle 
                    @click.stop="removeSlide(index)"
                    class="opacity-70 hover:opacity-100"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <div class="aspect-[16/9] bg-white rounded-t-md overflow-hidden">
                  <img 
                    :src="slide.thumbnail || 'https://via.placeholder.com/400x225?text=Slide+' + (index + 1)" 
                    :alt="`幻灯片 ${index + 1}`" 
                    class="w-full h-full object-cover"
                  />
                </div>
                <div class="p-2 border-t">
                  <p class="text-xs text-gray-700 font-medium truncate">{{ slide.title || `幻灯片 ${index + 1}` }}</p>
                  <p v-if="slide.content" class="text-xs text-gray-500 truncate mt-1">{{ truncateText(slide.content, 20) }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 上传PPT区域 -->
          <div v-else class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
            <div class="flex flex-col items-center justify-center">
              <el-icon class="text-4xl text-gray-400 mb-3"><UploadFilled /></el-icon>
              <h3 class="text-base font-medium text-gray-800 mb-2">上传PPT文件</h3>
              <p class="text-sm text-gray-500 mb-3">支持.ppt、.pptx格式，文件大小不超过20MB</p>
              
              <div class="upload-container">
                <el-upload
                  ref="uploadRef"
                  class="upload-demo"
                  drag
                  :http-request="handleUpload"
                  :auto-upload="true"
                  :limit="1"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :file-list="[]"
                  accept=".ppt,.pptx"
                >
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    拖拽文件到此处或 <em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持.ppt和.pptx格式文件，最大20MB
                    </div>
                  </template>
                </el-upload>
                
                <div v-if="uploadFile" class="mt-2 text-sm text-gray-600">
                  已选择文件: {{ uploadFile.name }} ({{ (uploadFile.size / 1024 / 1024).toFixed(2) }}MB)
                </div>
              </div>
            </div>
          </div>
          
          <!-- 简化的基本信息表单 -->
          <div class="mt-6 border-t pt-4">
            <h3 class="text-base font-medium mb-3">讲稿基本信息</h3>
            
            <el-form :model="scriptForm" label-position="top" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="讲稿标题" required>
                <el-input v-model="scriptForm.title" placeholder="请输入讲稿标题" />
              </el-form-item>
              <el-form-item label="学科">
                <el-select v-model="scriptForm.subject" placeholder="请选择学科" class="w-full">
                  <el-option label="通用" value="通用" />
                  <el-option label="Python编程" value="Python编程" />
                  <el-option label="人工智能" value="人工智能" />
                  <el-option label="英语" value="英语" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 第二步：编辑幻灯片讲稿 -->
      <div v-else-if="currentStep === 1">
        <div class="bg-white rounded-lg p-4 shadow-sm max-w-5xl mx-auto">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <el-icon class="mr-2 text-blue-600"><Edit /></el-icon>
            编辑所有幻灯片讲稿
          </h2>
          
          <!-- 讲稿生成设置 -->
          <div class="settings-panel mb-6">
            <h3 class="text-base font-medium mb-3 flex items-center">
              <el-icon class="mr-2 text-blue-600"><MagicStick /></el-icon>
              讲稿生成设置
            </h3>
            <div class="bg-blue-50 p-3 rounded-md mb-4 text-sm text-blue-700">
              <el-icon class="mr-1"><InfoFilled /></el-icon>
              <p>您可以为每个幻灯片单独生成讲稿，也可以一次性为全部幻灯片生成讲稿。</p>
              <p class="mt-1 font-medium">逐个生成可保证讲稿内容的连贯性和上下文关联。</p>
            </div>
            <el-form label-position="top" :model="scriptSettings" class="script-settings-form">
              <el-form-item label="讲稿风格">
                <el-select v-model="scriptSettings.style" placeholder="选择讲稿风格">
                  <el-option label="标准" value="standard"></el-option>
                  <el-option label="学术" value="academic"></el-option>
                  <el-option label="简易" value="simple"></el-option>
                  <el-option label="生动" value="lively"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="目标受众">
                <el-select v-model="scriptSettings.targetAudience" placeholder="选择目标受众">
                  <el-option label="小学" value="primary"></el-option>
                  <el-option label="初中" value="middle"></el-option>
                  <el-option label="高中" value="high"></el-option>
                  <el-option label="大学" value="college"></el-option>
                </el-select>
              </el-form-item>
             
            </el-form>
            
            <div class="flex justify-center mt-4">
              <el-button type="primary" @click="generateAllScripts" :loading="isGenerating">
                <el-icon class="mr-1"><MagicStick /></el-icon>
                一键生成所有讲稿
              </el-button>
            </div>
          </div>
          
          <!-- 完成进度 -->
          <div class="flex justify-between items-center mb-6 px-4 py-3 bg-gray-50 rounded-lg">
            <div class="text-base font-medium text-gray-700">
              幻灯片总数: {{ slides.length }}
            </div>
            <div class="text-base font-medium text-gray-700">
              已完成: {{ slides.filter(s => s.script).length }}/{{ slides.length }}
            </div>
          </div>
          
          <!-- 所有幻灯片编辑区域 -->
          <div v-if="slides.length > 0">
            <div v-for="(slide, index) in slides" :key="index" class="mb-8 pb-8 border-b border-gray-200 last:border-b-0">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium flex items-center">
                  <div class="bg-blue-500 text-white rounded-full w-7 h-7 mr-2 flex items-center justify-center text-sm font-medium">
                    {{ index + 1 }}
                  </div>
                  <span>{{ slide.title || `幻灯片 ${index + 1}` }}</span>
                  <div v-if="slide.script" class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-md">
                    已生成
                  </div>
                </h3>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <!-- 左侧：幻灯片预览 -->
                <div class="bg-gray-50 p-4 rounded-lg border">
                  <div class="mb-3 text-sm font-medium text-gray-700">幻灯片预览</div>
                  <div class="aspect-[16/9] bg-white rounded-md overflow-hidden shadow-sm mb-3">
                    <img 
                      :src="slide.thumbnail || `https://via.placeholder.com/400x225?text=Slide+${index + 1}`" 
                      :alt="`幻灯片 ${index + 1}`" 
                      class="w-full h-full object-contain"
                    />
                  </div>
                  <div v-if="slide.content" class="bg-white p-3 rounded-md border text-sm max-h-40 overflow-y-auto">
                    <div class="font-medium text-gray-700 mb-1">提取到的内容：</div>
                    <div class="whitespace-pre-line text-gray-600">{{ slide.content }}</div>
                  </div>
                  <div v-else class="bg-white p-3 rounded-md border text-sm text-gray-500 italic">
                    未提取到幻灯片文本内容
                  </div>
                </div>
                
                <!-- 右侧：编辑表单 -->
                <div>
                  <el-form :model="slide" label-position="top">
                    <el-form-item label="标题">
                      <el-input v-model="slide.title" placeholder="请输入幻灯片标题"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="讲稿内容">
                      <el-input
                        type="textarea"
                        v-model="slide.script"
                        :rows="6"
                        placeholder="请输入讲稿内容或点击下方按钮生成"
                      ></el-input>
                      <div class="mt-2 flex justify-between items-center">
                        <el-button 
                          type="primary" 
                          size="small" 
                          @click="generateSlideScript(index)"
                          :loading="generatingSlideIndex === index"
                          :disabled="isGenerating"
                        >
                          <el-icon class="mr-1"><MagicStick /></el-icon>
                          为此幻灯片生成讲稿
                        </el-button>
                        <span class="text-xs text-gray-500">
                          {{ slide.script ? getWordCount(slide.script) + ' 字' : '尚未生成' }}
                        </span>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 提示信息 -->
          <div v-else class="text-center py-8 border border-dashed border-gray-300 rounded-md">
            <el-icon class="text-4xl text-gray-400 mb-3"><InfoFilled /></el-icon>
            <p class="text-gray-600 mb-4">暂无幻灯片内容，请先上传PPT</p>
          </div>
        </div>
      </div>
      
      <!-- 第三步：配置参数 -->
      <div v-else-if="currentStep === 2">
        <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-sm border border-gray-100 p-8">
          <h2 class="text-2xl font-semibold mb-8 text-center flex items-center justify-center">
            <el-icon class="mr-2 text-blue-600 text-2xl"><SetUp /></el-icon>
            讲稿生成配置
          </h2>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 左侧：文本配置 -->
            <div>
              <div class="bg-blue-50 rounded-lg p-5 mb-6 border border-blue-100">
                <h3 class="text-lg font-medium mb-4 text-blue-800 flex items-center">
                  <el-icon class="mr-2"><Document /></el-icon>
                  内容风格配置
                </h3>
                
                <el-form :model="generationSettings" label-position="top">
                  <el-form-item label="内容风格">
                    <el-select v-model="generationSettings.style" placeholder="选择风格" class="w-full">
                      <el-option label="正式学术" value="academic" />
                      <el-option label="通俗易懂" value="simple" />
                      <el-option label="生动活泼" value="lively" />
                      <el-option label="深入详细" value="detailed" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="单页讲稿长度" class="mb-10">
                    <el-slider
                      v-model="generationSettings.length"
                      :step="25"
                      :marks="{100: '简短', 200: '标准', 300: '详细'}"
                      :min="100"
                      :max="300"
                    />
                  </el-form-item>
                  
                  <el-form-item label="自定义提示词" class="mt-10">
                    <el-input
                      v-model="generationSettings.customPrompt"
                      type="textarea"
                      :rows="3"
                      placeholder='输入额外的指导性提示，如"着重强调实际应用"、"包含生动的案例"等'
                    />
                  </el-form-item>
                </el-form>
              </div>
            </div>
            
            <!-- 右侧：音频配置 -->
            <div>
              <div class="bg-purple-50 rounded-lg p-5 mb-6 border border-purple-100">
                <h3 class="text-lg font-medium mb-4 text-purple-800 flex items-center">
                  <el-icon class="mr-2"><Microphone /></el-icon>
                  声音配置
                </h3>
                
                <el-form :model="voiceSettings" label-position="top">
                  <el-form-item label="选择声音">
                    <el-select v-model="voiceSettings.voice" placeholder="选择声音" class="w-full">
                      <el-option-group label="男声">
                        <el-option label="王老师 (标准男声)" value="male1" />
                        <el-option label="李教授 (儒雅男声)" value="male2" />
                        <el-option label="张老师 (活力男声)" value="male3" />
                      </el-option-group>
                      <el-option-group label="女声">
                        <el-option label="林老师 (标准女声)" value="female1" />
                        <el-option label="刘教授 (温柔女声)" value="female2" />
                        <el-option label="陈老师 (活力女声)" value="female3" />
                      </el-option-group>
                    </el-select>
                  </el-form-item>
                  
                  <div class="flex justify-center mb-4">
                    <el-tooltip content="点击试听当前选择的声音" placement="top" effect="light" class="voice-sample-tooltip">
                      <el-button 
                        @click="previewVoiceSample" 
                        type="success" 
                        class="voice-sample-btn"
                      >
                        <el-icon class="mr-1"><VideoPlay /></el-icon>
                        <span>试听示例</span>
                      </el-button>
                    </el-tooltip>
                  </div>
                  
                  <el-form-item label="语速">
                    <el-slider
                      v-model="voiceSettings.speed"
                      :step="0.1"
                      :min="0.5"
                      :max="2"
                      :format-tooltip="value => value + 'x'"
                    />
                  </el-form-item>
                  
                  <el-form-item label="声调">
                    <el-slider
                      v-model="voiceSettings.pitch"
                      :step="10"
                      :min="-100"
                      :max="100"
                    />
                  </el-form-item>
                </el-form>
              </div>
              
             
            </div>
          </div>
          
          <!-- 底部提示 -->
          <div class="mt-8 text-center">
            <p class="text-gray-500 text-sm">配置完成后，点击下一步将开始生成讲稿</p>
          </div>
        </div>
      </div>
      
      <!-- 第四步：预览和调整 -->
      <div v-else-if="currentStep === 3">
        <div class="space-y-6">
          <!-- 讲稿内容区域 -->
          <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-semibold flex items-center">
                <el-icon class="mr-2 text-blue-600"><Reading /></el-icon>
                讲稿内容
              </h2>
              
              <div class="flex space-x-3">
                <el-button 
                  size="small" 
                  type="primary"
                  @click="openVoiceSettings"
                  :disabled="!scriptContent"
                >
                  <el-icon class="mr-1"><Microphone /></el-icon>
                  更换音色
                </el-button>
                <el-button 
                  size="small" 
                  type="warning"
                  @click="resynthesizeAllAudio"
                  :loading="isResynthesizingAll"
                  :disabled="!scriptContent"
                >
                  <el-icon class="mr-1"><Refresh /></el-icon>
                  全部重新合成音频
                </el-button>
              </div>
            </div>
            
            <div v-if="!scriptContent" class="text-center py-8 border border-dashed border-gray-300 rounded-md mb-4">
              <el-icon class="text-4xl text-gray-400 mb-2"><DocumentAdd /></el-icon>
              <p class="text-gray-500 mb-4">尚未生成讲稿内容</p>
              <el-button type="primary" @click="generateScript" :loading="isGenerating">
                <el-icon><MagicStick /></el-icon>
                <span>{{ isGenerating ? '生成中...' : '生成讲稿' }}</span>
              </el-button>
            </div>
            
            <div v-else class="grid grid-cols-1 gap-6">
              <!-- 幻灯片缩略图区域 -->
              
              <!-- 幻灯片与讲稿内容匹配表格 -->
              <div class="overflow-x-auto">
                
                <el-table 
                  :data="slideScriptTableData" 
                  border 
                  style="width: 100%"
                  :highlight-current-row="true"
                  @current-change="handleCurrentRowChange"
                  :cell-style="{ 'white-space': 'pre-wrap', 'word-break': 'break-word' }"
                >
                  <el-table-column label="序号" width="70" align="center">
                    <template #default="scope">
                      <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mx-auto">
                        {{ scope.row.index + 1 }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="幻灯片" width="200">
                    <template #default="scope">
                      <div class="flex items-center">
                        <div class="w-16 h-9 bg-gray-100 mr-2 rounded overflow-hidden flex-shrink-0">
                          <img 
                            :src="scope.row.thumbnail" 
                            :alt="`幻灯片 ${scope.row.index + 1}`" 
                            class="w-full h-full object-cover"
                          />
                        </div>
                        <span class="text-sm truncate">{{ scope.row.title }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="讲稿内容" min-width="400">
                    <template #default="scope">
                      <div class="text-sm text-gray-700 whitespace-pre-line">
                        {{ scope.row.content || '暂无内容' }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80" align="center">
                    <template #default="scope">
                      <div class="flex flex-col space-y-2 justify-center">
                        <el-tooltip content="编辑" placement="top">
                          <el-button 
                            type="primary" 
                            size="small" 
                            circle
                            @click="editSlideScript(scope.row.index)"
                            :disabled="!scope.row.content"
                            class="mx-auto action-btn"
                          >
                            <el-icon><Edit /></el-icon>
                          </el-button>

                        </el-tooltip>

                        <el-tooltip content="播放" placement="top">
                          <el-button
                            type="success"
                            size="small"
                            circle
                            :loading="playingSlideIndex === scope.row.index"
                            @click="playSlideAudio(scope.row.index)"
                            :disabled="!scope.row.content"
                            class="mx-auto action-btn"
                          >
                            <el-icon><VideoPlay /></el-icon>
                          </el-button>
                        </el-tooltip>
                        <el-tooltip content="重新合成音频" placement="top">
                          <el-button
                            type="info"
                            size="small"
                            circle
                            @click="resynthesizeAudio(scope.row.index)"
                            :disabled="!scope.row.content"
                            class="mx-auto action-btn"
                          >
                            <el-icon><Refresh /></el-icon>
                          </el-button>
                        </el-tooltip>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              
              <!-- 完整讲稿预览 -->
              <div class="mt-4">
                <div class="flex justify-between items-center mb-3">
                  <h3 class="text-lg font-medium flex items-center">
                    <el-icon class="mr-2 text-blue-600"><DocumentAdd /></el-icon>
                    完整讲稿预览
                  </h3>
                  <el-tooltip content="试听完整讲稿" placement="top">
                    <el-button 
                      size="small" 
                      type="primary"
                      @click="previewVoice"
                      :loading="isPlayingAudio" 
                      :disabled="!scriptContent"
                    >
                      <el-icon class="mr-1"><VideoPlay /></el-icon>
                      试听完整讲稿
                    </el-button>
                  </el-tooltip>
                </div>
                <div class="border rounded-lg p-4 bg-gray-50 max-h-80 overflow-y-auto">
                  <pre class="whitespace-pre-wrap text-sm text-gray-700">{{ scriptContent }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部导航按钮 -->
      <div class="flex justify-between pt-6 border-t border-gray-200 mt-6">
        <el-button
          v-if="currentStep > 0"
          @click="prevStep"
        >
          <el-icon class="mr-1"><ArrowLeft /></el-icon>
          上一步
        </el-button>
        <div v-else></div>
        
        <div class="flex space-x-3">
          <el-button @click="goBack">
            取消
          </el-button>
          <el-button
            v-if="currentStep < 3"
            type="primary"
            @click="nextStep"
            :disabled="!canProceed"
          >
            下一步
            <el-icon class="ml-1"><ArrowRight /></el-icon>
          </el-button>
          <template v-else>
            <el-button @click="saveDraft">
              保存草稿
            </el-button>
            <el-button
              type="success"
              @click="createScript"
              :disabled="!isFormValid"
            >
              完成创建
              <el-icon class="ml-1"><Check /></el-icon>
            </el-button>
          </template>
        </div>
      </div>
    </div>
    
    <!-- 编辑讲稿对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑讲稿内容"
      width="700px"
    >
      <el-form :model="editingScriptData">
        <el-form-item v-if="selectedSlideIndex !== null" label="当前幻灯片">
          <div class="flex items-center">
            <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2">
              {{ selectedSlideIndex + 1 }}
            </div>
            <span>{{ slides[selectedSlideIndex]?.title || '无标题' }}</span>
          </div>
        </el-form-item>
        
        <el-form-item label="讲稿内容">
          <div class="mb-2">
            <div class="flex justify-between items-center">
              <div class="flex space-x-2">
                <el-button
                  type="primary"
                  size="small"
                  @click="aiExpandContent"
                  :loading="isAiExpanding"
                >
                  <el-icon class="mr-1"><MagicStick /></el-icon>
                  AI辅助拓写
                </el-button>
                <el-tooltip content="AI将帮助拓展和丰富当前内容" placement="top">
                  <el-icon class="text-gray-400"><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
              <div class="flex space-x-2">
                <el-select v-model="aiWriteStyle" size="small" placeholder="选择风格" class="w-36">
                  <el-option label="详细学术" value="academic" />
                  <el-option label="通俗易懂" value="simple" />
                  <el-option label="生动活泼" value="lively" />
                  <el-option label="简洁精炼" value="concise" />
                </el-select>
              </div>
            </div>
          </div>
          <el-input
            v-model="editingScriptData.content"
            type="textarea"
            :rows="10"
            placeholder="输入讲稿内容"
          />
          <div class="mt-2 text-sm text-gray-500 flex justify-between">
            <span>提示: 使用AI辅助拓写可以快速丰富内容</span>
            <span>{{ editingScriptData.content ? getWordCount(editingScriptData.content) : 0 }} 字</span>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveScriptEdit">
            保存修改
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 声音设置对话框 -->
    <el-dialog
      v-model="voiceSettingsDialogVisible"
      title="声音设置"
      width="500px"
    >
      <div class="bg-purple-50 rounded-lg p-5 mb-6 border border-purple-100">
        <h3 class="text-lg font-medium mb-4 text-purple-800 flex items-center">
          <el-icon class="mr-2"><Microphone /></el-icon>
          声音配置
        </h3>
        
        <el-form :model="voiceSettings" label-position="top">
          <el-form-item label="选择声音">
            <el-select v-model="voiceSettings.voice" placeholder="选择声音" class="w-full">
              <el-option-group label="男声">
                <el-option label="王老师 (标准男声)" value="male1" />
                <el-option label="李教授 (儒雅男声)" value="male2" />
                <el-option label="张老师 (活力男声)" value="male3" />
              </el-option-group>
              <el-option-group label="女声">
                <el-option label="林老师 (标准女声)" value="female1" />
                <el-option label="刘教授 (温柔女声)" value="female2" />
                <el-option label="陈老师 (活力女声)" value="female3" />
              </el-option-group>
            </el-select>
          </el-form-item>
          
          <div class="flex justify-center mb-4">
            <el-button @click="previewVoiceSample" type="primary" plain>
              <el-icon class="mr-1"><VideoPlay /></el-icon>
              <span>试听示例</span>
            </el-button>
          </div>
          
          <el-form-item label="语速">
            <el-slider
              v-model="voiceSettings.speed"
              :step="0.1"
              :min="0.5"
              :max="2"
              :format-tooltip="value => value + 'x'"
            />
          </el-form-item>
          
          <el-form-item label="声调">
            <el-slider
              v-model="voiceSettings.pitch"
              :step="10"
              :min="-100"
              :max="100"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="voiceSettingsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveVoiceSettings">
            确认应用
          </el-button>
        </span>
      </template>
    </el-dialog>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import speechDesign from '@/api/speechDesign'
import {
  Document,
  Reading,
  InfoFilled,
  SetUp,
  MagicStick,
  Microphone,
  Edit,
  CopyDocument,
  PictureRounded,
  Select,
  Refresh,
  Upload,
  Back,
  Check,
  VideoPlay,
  UploadFilled,
  DocumentAdd,
  Loading,
  Delete,
  ArrowLeft,
  ArrowRight
} from '@element-plus/icons-vue'

// Router
const router = useRouter()

// 步骤控制
const currentStep = ref(0)
const canProceed = computed(() => {
  if (currentStep.value === 0) {
    return slides.value.length > 0 && scriptForm.value.title.trim() !== ''
  } else if (currentStep.value === 1) {
    // 第二步需要至少有一张幻灯片有讲稿内容才能继续
    return slides.value.some(slide => slide.script)
  } else if (currentStep.value === 2) {
    return true // 第三步总是可以继续
  }
  return false
})

const nextStep = () => {
  if (currentStep.value < 3 && canProceed.value) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// Teacher Data
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 草稿ID
const draftId = ref('')

// PPT幻灯片数据
const slides = ref([])
const selectedSlideIndex = ref(null)
const uploadDialogVisible = ref(false)
const uploadFile = ref(null)

// 讲稿表单数据
const scriptForm = ref({
  title: '',
  description: '',
  subject: '通用',
  grade: '',
  tags: []
})

// 标签输入
const inputTagVisible = ref(false)
const inputTagValue = ref('')
const tagInputRef = ref(null)

// 讲稿内容
const scriptContent = ref('')
const scriptContentBySlide = ref({})
const isGenerating = ref(false)
const isPlayingAudio = ref(false)
const playingSlideIndex = ref(null) // 当前正在播放的幻灯片索引

// 讲稿编辑
const editDialogVisible = ref(false)
const editingScriptData = ref({
  slideIndex: null,
  content: ''
})

// AI辅助拓写
const isAiExpanding = ref(false)
const aiWriteStyle = ref('academic')

// 讲稿生成配置
const generationSettings = ref({
  style: 'academic',
  audience: 'intermediate',
  length: 200,
  keywords: '',
  customPrompt: ''
})

// 声音配置
const voiceSettings = ref({
  voice: 'female1',
  speed: 1.0,
  pitch: 0
})

// 声音设置对话框
const voiceSettingsDialogVisible = ref(false)
const isResynthesizingAll = ref(false)

// 表单校验
const isFormValid = computed(() => {
  return scriptForm.value.title.trim() !== '' &&
         scriptContent.value !== ''
})

// 幻灯片讲稿表格数据
const slideScriptTableData = computed(() => {
  return slides.value.map((slide, index) => ({
    index,
    title: slide.title || `幻灯片 ${index + 1}`,
    thumbnail: slide.thumbnail || `https://via.placeholder.com/400x225?text=Slide+${index + 1}`,
    content: scriptContentBySlide.value[index] || ''
  }))
})

// 处理表格当前行变化
const handleCurrentRowChange = (row) => {
  if (row) {
    selectSlide(row.index)
  }
}

// 编辑特定幻灯片的讲稿
const editSlideScript = (index) => {
  editingScriptData.value = {
    slideIndex: index,
    content: scriptContentBySlide.value[index] || ''
  }
  
  editDialogVisible.value = true
}

// 播放幻灯片音频
const playSlideAudio = async (index) => {
  // 如果已经有正在播放的音频，先停止它
  if (playingSlideIndex.value !== null) {
    // 模拟停止当前播放的音频
    playingSlideIndex.value = null
  }
  
  if (!scriptContentBySlide.value[index]) {
    ElMessage.warning('该幻灯片没有讲稿内容')
    return
  }
  
  // 设置当前播放幻灯片索引
  playingSlideIndex.value = index
  
  try {
    // 模拟获取或生成幻灯片的音频URL
    // 实际项目中，可能需要先检查是否已有缓存的音频，如果没有则调用TTS API
    const audioUrl = slides.value[index]?.audioUrl
    
    if (audioUrl) {
      // 如果已有音频URL，直接播放
      console.log('播放音频:', audioUrl)
      // 模拟播放时间
      await new Promise(resolve => setTimeout(resolve, 3000))
    } else {
      // 如果没有音频URL，调用resynthesizeAudio生成
      await resynthesizeAudio(index)
    }
    
    playingSlideIndex.value = null
    ElMessage.success(`幻灯片 ${index + 1} 音频播放完成`)
  } catch (error) {
    playingSlideIndex.value = null
    ElMessage.error('音频播放失败：' + (error.message || '未知错误'))
  }
}

// 重新合成音频
const resynthesizeAudio = async (index) => {
  if (!scriptContentBySlide.value[index]) {
    ElMessage.warning('该幻灯片没有讲稿内容')
    return
  }
  
  try {
    // 显示加载提示
    ElMessage.info({
      message: '正在生成音频...',
      duration: 0,
      showClose: true
    })
    
    // 模拟TTS API调用过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟生成的音频URL
    const audioUrl = `https://example.com/tts-audio-${Date.now()}.mp3`
    
    // 更新幻灯片的音频URL
    slides.value[index] = {
      ...slides.value[index],
      audioUrl
    }
    
    ElMessage.closeAll()
    ElMessage.success('音频合成成功')
    
    return audioUrl
  } catch (error) {
    ElMessage.closeAll()
    ElMessage.error('音频合成失败：' + (error.message || '未知错误'))
    throw error
  }
}

// 方法
const goBack = () => {
  // 如果已经保存过草稿，直接返回
  if (draftId.value) {
    router.push('/teacher/content-script-projects')
    return
  }

  // 未保存则询问用户
  ElMessageBox.confirm(
    '离开页面将丢失未保存的内容，是否确认离开？', 
    '确认离开', 
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      router.push('/teacher/content-script-projects')
    })
    .catch(() => {})
}

// 生成所有幻灯片讲稿
const generateAllScripts = async () => {
  if (slides.value.length === 0) {
    ElMessage.warning('请先上传幻灯片')
    return
  }

  try {
    isGenerating.value = true
    ElMessage.info({
      message: '正在为所有幻灯片生成讲稿...',
      duration: 0,
      showClose: true
    })

    // 按顺序为每张幻灯片生成讲稿
    for (let i = 0; i < slides.value.length; i++) {
      // 跳过已有讲稿的幻灯片，除非用户明确要求重新生成
      if (!slides.value[i].script) {
        selectSlide(i)
        await generateSlideScript(i)
        // 稍微延迟一下，避免API调用过快
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    ElMessage.closeAll()
    ElMessage.success('所有幻灯片讲稿生成完成')
  } catch (error) {
    ElMessage.closeAll()
    ElMessage.error('讲稿生成失败: ' + (error.message || '未知错误'))
  } finally {
    isGenerating.value = false
  }
}

// 格式化声音名称
const formatVoiceName = (voiceId) => {
  const voiceMap = {
    'male1': '王老师 (标准男声)',
    'male2': '李教授 (儒雅男声)',
    'male3': '张老师 (活力男声)',
    'female1': '林老师 (标准女声)',
    'female2': '刘教授 (温柔女声)',
    'female3': '陈老师 (活力女声)'
  }
  return voiceMap[voiceId] || voiceId
}

// 获取字数
const getWordCount = (text) => {
  if (!text) return 0
  // 中文字符计为1个字，英文单词计为1个字
  const cnCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length
  const enCount = (text.match(/[a-zA-Z]+/g) || []).length
  return cnCount + enCount
}

// 标签相关方法
const showTagInput = () => {
  inputTagVisible.value = true
  nextTick(() => {
    tagInputRef.value.input.focus()
  })
}

const addTag = () => {
  const value = inputTagValue.value.trim()
  if (value && !scriptForm.value.tags.includes(value)) {
    scriptForm.value.tags.push(value)
  }
  inputTagVisible.value = false
  inputTagValue.value = ''
}

const removeTag = (tag) => {
  scriptForm.value.tags = scriptForm.value.tags.filter(item => item !== tag)
}

// AI辅助拓写内容
const aiExpandContent = async () => {
  if (!editingScriptData.value.content.trim()) {
    ElMessage.warning('请先输入一些内容作为基础')
    return
  }
  
  isAiExpanding.value = true
  
  try {
    // 模拟AI拓写过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const originalContent = editingScriptData.value.content
    let expandedContent = ''
    
    // 根据选择的风格生成不同的拓展内容
    switch (aiWriteStyle.value) {
      case 'academic':
        expandedContent = generateAcademicContent(originalContent)
        break
      case 'simple':
        expandedContent = generateSimpleContent(originalContent)
        break
      case 'lively':
        expandedContent = generateLivelyContent(originalContent)
        break
      case 'concise':
        expandedContent = generateConciseContent(originalContent)
        break
      default:
        expandedContent = generateAcademicContent(originalContent)
    }
    
    // 更新编辑框中的内容
    editingScriptData.value.content = expandedContent
    
    isAiExpanding.value = false
    ElMessage.success('AI拓写完成')
  } catch (error) {
    isAiExpanding.value = false
    ElMessage.error('AI拓写失败：' + (error.message || '未知错误'))
  }
}

// 根据不同风格生成拓展内容的辅助函数
const generateAcademicContent = (originalContent) => {
  // 学术风格：添加专业术语、引用和详细解释
  return originalContent + '\n\n从学术角度来看，这个概念的重要性在于它能帮助我们系统地理解相关理论。'
}

const generateSimpleContent = (originalContent) => {
  // 通俗易懂：使用简单语言和生活例子
  return originalContent + '\n\n简单来说，这就像是我们日常生活中的一个例子。想象一下，当你在学习骑自行车时，刚开始可能会摔倒几次，但通过不断练习，你的身体逐渐掌握了平衡的感觉，最终可以轻松骑行。这个过程就类似于我们学习新知识的过程，需要经过尝试、失败、调整和再尝试，直到完全掌握。'
}

const generateLivelyContent = (originalContent) => {
  // 生动活泼：使用生动形象的比喻和活泼的语言
  return originalContent + '\n\n太有趣了！这就像是一场精彩的探险旅程，我们每个人都是勇敢的探险家，带着好奇心和求知欲出发，去探索未知的知识宝藏！在这个过程中，我们会遇到各种挑战和谜题，但当我们解开它们时，那种成就感和喜悦是无与伦比的。让我们一起踏上这段奇妙的学习之旅吧！'
}

const generateConciseContent = (originalContent) => {
  // 简洁精炼：保持简短明了，突出要点
  return originalContent + '\n\n简而言之，核心要点如下：\n1. 掌握基本概念是理解复杂知识的基础\n2. 实践是检验理论的唯一标准\n3. 反思和总结有助于深化理解\n4. 持续学习是保持知识更新的关键'
}

// PPT幻灯片相关方法
const handleFileChange = (file) => {
  console.log('handleFileChange接收到文件:', file);
  // 确保存储的是原始File对象而非el-upload包装对象
  uploadFile.value = file.raw || file;
}

const handleFileRemove = () => {
  uploadFile.value = null;
}

const handleUpload = async (options) => {
  const { file, onSuccess, onError } = options;
  console.log('开始处理上传文件:', file);
  console.log('文件类型检查:', file instanceof File, typeof file, Object.prototype.toString.call(file));
  console.log('文件详情:', {name: file.name, size: file.size, type: file.type});
  
  // 检查文件类型
  const fileExt = file.name.split('.').pop().toLowerCase();
  if (!['ppt', 'pptx'].includes(fileExt)) {
    ElMessage.error('仅支持.ppt和.pptx格式的文件');
    onError('文件格式错误');
    return;
  }
  
  // 检查文件大小
  if (file.size > 20 * 1024 * 1024) { // 20MB
    ElMessage.error('文件大小不能超过20MB');
    onError('文件过大');
    return;
  }
  
  // 确保使用原始File对象
  uploadFile.value = file.raw || file;
  console.log('文件已存储到uploadFile变量', uploadFile.value);
  console.log('存储的文件类型检查:', uploadFile.value instanceof File, typeof uploadFile.value);
  onSuccess(); // 这里先通知上传组件上传成功
  
  // 自动触发上传处理
  console.log('即将调用submitUpload进行实际处理');
  await submitUpload();
}

const submitUpload = async () => {
  console.log('submitUpload被调用，当前文件:', uploadFile.value);
  
  if (!uploadFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  // 确保文件是File对象
  if (!(uploadFile.value instanceof File)) {
    console.error('uploadFile不是一个File对象:', uploadFile.value);
    
    // 尝试从Element Plus可能的文件包装对象中获取原始File
    if (uploadFile.value.raw instanceof File) {
      console.log('从元素包装对象中提取原始File');
      uploadFile.value = uploadFile.value.raw;
    } else {
      ElMessage.error('文件对象类型错误，无法上传');
      return;
    }
  }
  
  // 显示加载提示
  const loadingInstance = ElMessage.info({
    message: '正在上传并解析PPT...',
    duration: 0
  })
  console.log('显示上传进度提示');
  
  try {
    // 输出文件信息
    console.log('准备上传的文件详情:', {
      name: uploadFile.value.name,
      size: uploadFile.value.size,
      type: uploadFile.value.type,
      lastModified: new Date(uploadFile.value.lastModified).toLocaleString(),
      isFile: uploadFile.value instanceof File,
      constructor: uploadFile.value.constructor?.name
    });
    
    // 调用API解析PPT
    console.log('准备调用parsePPT API...');
    const response = await speechDesign.parsePPT(uploadFile.value);
    console.log('收到API响应:', response);
    
    if (response && response.code === 200) {
      const pptData = response.data;
      console.log('pptData结构:', pptData);
      
      // 处理嵌套的data结构
      const slideData = pptData.data || pptData;
      console.log('解析成功，幻灯片数量:', slideData.slides?.length);
      
      // 检查slides是否存在
      if (!slideData.slides || !Array.isArray(slideData.slides)) {
        console.error('返回的slides数据无效:', slideData);
        loadingInstance.close();
        ElMessage.error('PPT解析失败: 未返回有效的幻灯片数据');
        return;
      }
      
      // 更新幻灯片数据，直接使用后端返回的图片
      slides.value = slideData.slides.map((slide, index) => ({
        id: index + 1,
        index: slide.index,
        title: slide.title,
        content: slide.content,
        notes: slide.notes,
        image: slide.image, // 保留原始 image 字段，便于后端获取图片数据
        // 如果slide中包含image，则使用其base64数据，否则使用占位图
        thumbnail: slide.image 
          ? `data:image/${slide.image.image_format};base64,${slide.image.image_data}` 
          : `https://via.placeholder.com/400x225?text=Slide+${index + 1}`
      }));
      
      // 设置标题
      if (!scriptForm.value.title && slideData.title && slideData.title !== "无标题") {
        scriptForm.value.title = slideData.title;
      }
      
      // 由于图片已经包含在解析响应中，不需要再单独调用extractPPTImages
      loadingInstance.close();
      ElMessage.success('PPT解析成功');
      selectedSlideIndex.value = 0;
    } else {
      console.error('API返回非200状态:', response);
      throw new Error((response && response.message) || '解析失败');
    }
  } catch (error) {
    console.error('解析PPT详细错误:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    loadingInstance.close();
    ElMessage.error('上传解析失败：' + (error.message || '未知错误'));
  }
}

const useExamplePPT = () => {
  ElMessage.info('正在加载示例PPT...');
  
  // 加载示例PPT数据
  setTimeout(() => {
    slides.value = generateMockSlides();
    selectedSlideIndex.value = 0;
    ElMessage.success('示例PPT已加载');
  }, 1000);
}

const generateMockSlides = () => {
  // 这里生成模拟的幻灯片数据
  const mockSlides = []
  const slideCount = Math.floor(Math.random() * 6) + 5 // 5-10张幻灯片
  
  for (let i = 0; i < slideCount; i++) {
    mockSlides.push({
      id: i + 1,
      title: getSlideTitle(i),
      thumbnail: `https://via.placeholder.com/400x225?text=Slide+${i + 1}`,
      content: ''
    })
  }
  
  return mockSlides
}

const getSlideTitle = (index) => {
  const titles = [
    '课程简介',
    '学习目标',
    '核心概念',
    '实际应用',
    '案例分析',
    '关键要点',
    '进阶内容',
    '实操演示',
    '常见问题',
    '总结回顾'
  ]
  return titles[index % titles.length]
}

const selectSlide = (index) => {
  selectedSlideIndex.value = index
}

const removeAllSlides = () => {
  ElMessageBox.confirm(
    '确定要清空所有幻灯片吗？此操作不可撤销。',
    '确认清空',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      slides.value = []
      selectedSlideIndex.value = null
      scriptContentBySlide.value = {}
      scriptContent.value = ''
      ElMessage.success('已清空所有幻灯片')
    })
    .catch(() => {})
}

const removeSlide = (index) => {
  ElMessageBox.confirm(
    `确定要删除第 ${index + 1} 张幻灯片吗？`,
    '确认删除',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(() => {
      // 删除幻灯片
      slides.value.splice(index, 1)
      
      // 删除对应的讲稿内容
      if (scriptContentBySlide.value[index]) {
        delete scriptContentBySlide.value[index]
      }
      
      // 调整索引
      const newScriptContentBySlide = {}
      Object.keys(scriptContentBySlide.value).forEach(slideIndex => {
        const oldIndex = parseInt(slideIndex)
        if (oldIndex > index) {
          newScriptContentBySlide[oldIndex - 1] = scriptContentBySlide.value[oldIndex]
        } else if (oldIndex < index) {
          newScriptContentBySlide[oldIndex] = scriptContentBySlide.value[oldIndex]
        }
      })
      scriptContentBySlide.value = newScriptContentBySlide
      
      // 更新完整讲稿
      if (scriptContent.value) {
        let fullScript = ''
        slides.value.forEach((slide, i) => {
          if (scriptContentBySlide.value[i]) {
            fullScript += `【幻灯片${i + 1}：${slide.title}】\n${scriptContentBySlide.value[i]}\n\n`
          }
        })
        scriptContent.value = fullScript
      }
      
      // 如果删除的是当前选中的幻灯片，调整选中索引
      if (selectedSlideIndex.value === index) {
        selectedSlideIndex.value = slides.value.length > 0 ? 0 : null
      } else if (selectedSlideIndex.value > index) {
        selectedSlideIndex.value--
      }
      
      ElMessage.success('已删除幻灯片')
    })
    .catch(() => {})
}

const refreshSlides = () => {
  // 刷新幻灯片
  ElMessage.success('已刷新幻灯片')
}

// 讲稿内容相关方法
const editCurrentSlideScript = () => {
  if (selectedSlideIndex.value === null) {
    ElMessage.warning('请先选择一张幻灯片')
    return
  }
  
  editingScriptData.value = {
    slideIndex: selectedSlideIndex.value,
    content: scriptContentBySlide.value[selectedSlideIndex.value] || ''
  }
  
  editDialogVisible.value = true
}

const saveScriptEdit = () => {
  const { slideIndex, content } = editingScriptData.value
  
  if (slideIndex !== null && content.trim()) {
    // 更新单张幻灯片的讲稿
    scriptContentBySlide.value[slideIndex] = content
    
    // 重新生成完整讲稿
    let fullScript = ''
    slides.value.forEach((slide, index) => {
      if (scriptContentBySlide.value[index]) {
        fullScript += `【幻灯片${index + 1}：${slide.title}】\n${scriptContentBySlide.value[index]}\n\n`
      }
    })
    
    scriptContent.value = fullScript
    editDialogVisible.value = false
    ElMessage.success('讲稿内容已更新')
  } else {
    ElMessage.warning('请输入讲稿内容')
  }
}

const copyScript = () => {
  // 复制讲稿到剪贴板
  navigator.clipboard.writeText(scriptContent.value)
    .then(() => {
      ElMessage.success('讲稿内容已复制到剪贴板')
    })
    .catch(err => {
      ElMessage.error('复制失败: ' + err)
    })
}

// 声音相关功能
const previewVoiceSample = () => {
  // 模拟试听示例声音
  ElMessage.success({
    message: '正在试听示例声音...',
    duration: 2000
  })
}

const previewVoice = () => {
  if (!scriptContent.value) {
    ElMessage.warning('请先生成讲稿内容')
    return
  }
  
  // 根据当前选择的幻灯片获取内容
  const contentToPlay = selectedSlideIndex.value !== null && scriptContentBySlide.value[selectedSlideIndex.value]
    ? scriptContentBySlide.value[selectedSlideIndex.value]
    : scriptContent.value.substring(0, 200) + '...' // 如果没有选择幻灯片，播放前200个字符
  
  // 模拟试听过程
  isPlayingAudio.value = true
  
  setTimeout(() => {
    isPlayingAudio.value = false
    ElMessage.success('试听结束')
  }, 3000)
}

// 保存和创建
const saveDraft = () => {
  // 保存草稿
  const randomId = 'DRAFT-' + Math.floor(Math.random() * 10000)
  draftId.value = randomId
  
  ElMessage.success(`草稿已保存，ID: ${randomId}`)
}

const createScript = () => {
  if (!isFormValid.value) {
    ElMessage.warning('请填写必要的信息并生成讲稿内容')
    return
  }
  
  // 创建讲稿
  const scriptData = {
    ...scriptForm.value,
    content: scriptContent.value,
    contentBySlide: scriptContentBySlide.value,
    slides: slides.value,
    generationSettings: generationSettings.value,
    voiceSettings: voiceSettings.value
  }
  
  console.log('创建讲稿：', scriptData)
  
  ElMessageBox.confirm(
    '确认创建讲稿？',
    '创建确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'success',
    }
  )
    .then(() => {
      ElMessage.success('讲稿创建成功')
      router.push('/teacher/content-script-projects')
    })
    .catch(() => {
      // 取消操作
    })
}

// 打开声音设置对话框
const openVoiceSettings = () => {
  voiceSettingsDialogVisible.value = true
}

// 重新合成所有音频
const resynthesizeAllAudio = async () => {
  if (!scriptContent.value) {
    ElMessage.warning('请先生成讲稿内容')
    return
  }
  
  try {
    isResynthesizingAll.value = true
    ElMessage.info({
      message: '正在为所有幻灯片重新合成音频...',
      duration: 0,
      showClose: true
    })
    
    // 遍历所有有内容的幻灯片，重新合成音频
    const promises = Object.keys(scriptContentBySlide.value).map(async (index) => {
      if (scriptContentBySlide.value[index]) {
        try {
          await resynthesizeAudio(parseInt(index))
        } catch (error) {
          console.error(`幻灯片 ${parseInt(index) + 1} 音频合成失败:`, error)
        }
      }
    })
    
    await Promise.all(promises)
    
    ElMessage.closeAll()
    ElMessage.success('所有音频重新合成成功')
  } catch (error) {
    ElMessage.closeAll()
    ElMessage.error('音频合成失败：' + (error.message || '未知错误'))
  } finally {
    isResynthesizingAll.value = false
  }
}

// 保存声音设置
const saveVoiceSettings = async () => {
  voiceSettingsDialogVisible.value = false
  ElMessage.success(`已更新音色为: ${formatVoiceName(voiceSettings.value.voice)}`)
  
  // 询问是否需要重新合成所有音频
  if (scriptContent.value) {
    ElMessageBox.confirm(
      '是否要使用新的音色重新合成所有幻灯片的音频？',
      '重新合成确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info',
      }
    )
    .then(() => {
      resynthesizeAllAudio()
    })
    .catch(() => {
      ElMessage.info('您可以稍后点击"全部重新合成音频"按钮进行重新合成')
    })
  }
}

// 页面初始化
onMounted(() => {
  // 可以在这里加载初始数据
})

// 在定义slides之后添加以下变量
// 讲稿生成设置
const scriptSettings = reactive({
  style: 'standard', // 讲稿风格：standard-标准, academic-学术, simple-简易, lively-生动
  targetAudience: 'college', // 目标受众：primary-小学, middle-初中, high-高中, college-大学
  includeQuestions: true, // 是否包含引导性问题
  useFigures: true // 是否引用图表内容
})

// 单个幻灯片讲稿生成状态
const generatingSlideIndex = ref(null)

// 生成的讲稿内容
const generatedScripts = ref({})
const fullScript = ref('')
const currentGeneratingSlide = ref(0)

// 截断文本工具方法
const truncateText = (text, maxLength) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// 生成讲稿相关方法
/**
 * 为单个幻灯片生成讲稿
 */
async function generateSlideScript(slideIndex) {
  if (slideIndex === null || slideIndex < 0 || slideIndex >= slides.value.length) {
    ElMessage.warning('未选择有效的幻灯片')
    return
  }
  
  generatingSlideIndex.value = slideIndex
  // Initialize script content for the slide to allow reactive updates
  slides.value[slideIndex].script = '' 
  scriptContentBySlide.value[slideIndex] = ''
  updateFullScript() // Update full script to reflect clearing

  try {
    const currentSlide = { ...slides.value[slideIndex] };
    if (currentSlide.thumbnail) delete currentSlide.thumbnail;
    
    const previousScripts = {}
    for (let i = 0; i < slideIndex; i++) {
      const slide = slides.value[i]
      if (slide.script) {
        previousScripts[i] = {
          index: i,
          title: slide.title || `幻灯片 ${i+1}`,
          script: slide.script,
          original_slide: { ...slide }
        }
      }
    }
    
    const futureSlides = [];
    const maxFutureSlides = 3;
    for (let i = 1; i <= maxFutureSlides; i++) {
      const futureIndex = slideIndex + i;
      if (futureIndex < slides.value.length) {
        const futureSlide = { ...slides.value[futureIndex] };
        if (futureSlide.thumbnail) delete futureSlide.thumbnail;
        futureSlides.push({
          index: futureIndex,
          title: futureSlide.title || `幻灯片 ${futureIndex+1}`,
          content: futureSlide.content || '',
          image: futureSlide.image || null
        });
      }
    }
    
    const requestData = {
      current_slide: currentSlide,
      previous_scripts: previousScripts,
      future_slides: futureSlides,
      settings: {
        ...scriptSettings,
        total_slides: slides.value.length,
        presentation_title: scriptForm.value.title || currentSlide.title || `讲稿${slideIndex+1}`,
        presentation_goal: scriptForm.value.description || '帮助学生理解知识点'
      }
    }
    
    // ElMessage.info({ message: `正在为幻灯片 ${slideIndex + 1} 生成讲稿...`, duration: 0, showClose: true });

    const response = await speechDesign.generateSlideScriptStream(requestData)
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    let buffer = ''

    function processText(text) {
      buffer += text
      let boundary = buffer.indexOf('\n\n')
      while (boundary !== -1) {
        const message = buffer.substring(0, boundary)
        buffer = buffer.substring(boundary + 2)
        if (message.startsWith('data: ')) {
          try {
            const jsonData = JSON.parse(message.substring(5))
            if (jsonData.script_chunk) {
              slides.value[slideIndex].script += jsonData.script_chunk
              scriptContentBySlide.value[slideIndex] = slides.value[slideIndex].script
              updateFullScript()
            } else if (jsonData.status === 'completed') {
              console.log(`幻灯片 ${jsonData.title} 讲稿流式生成完成`);
              ElMessage.success(`幻灯片 ${slideIndex + 1} (${jsonData.title}) 讲稿生成成功`);
              // ElMessage.closeAll(); // Close the initial info message if any
            } else if (jsonData.error) {
              console.error('Streaming error from server:', jsonData.error);
              ElMessage.error(`幻灯片 ${slideIndex + 1} 讲稿生成失败: ${jsonData.error}`);
              // ElMessage.closeAll();
              throw new Error(jsonData.error); // Propagate error to stop further processing for this slide
            }
          } catch (e) {
            console.error('Error parsing SSE JSON:', e, 'Message:', message);
          }
        }
        boundary = buffer.indexOf('\n\n')
      }
    }

    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        if (buffer.length > 0) processText(buffer); // Process any remaining buffer
        console.log(`Stream finished for slide ${slideIndex + 1}`)
        // Check if a success message was already shown via SSE, if not, show a generic one.
        // This might be redundant if jsonData.status === 'completed' is reliably sent.
        // if (!slides.value[slideIndex].script) {
        // ElMessage.warning(`幻灯片 ${slideIndex + 1} 未收到讲稿内容.`);
        // }
        break
      }
      processText(decoder.decode(value, { stream: true }))
    }

  } catch (error) {
    console.error(`生成幻灯片 ${slideIndex + 1} 讲稿出错:`, error)
    ElMessage.error(`生成幻灯片 ${slideIndex + 1} 讲稿失败: ${error.message || '未知流错误'}`)
    // ElMessage.closeAll();
  } finally {
    generatingSlideIndex.value = null
  }
}

// 更新完整讲稿内容的辅助方法
const updateFullScript = () => {
  let fullScriptText = ''
  
  // 按幻灯片顺序组合所有讲稿
  slides.value.forEach((slide, index) => {
    if (slide.script) {
      fullScriptText += `【幻灯片${index+1}：${slide.title || `幻灯片 ${index+1}`}】\n${slide.script}\n\n`
    }
  })
  
  scriptContent.value = fullScriptText
}
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 主要容器样式 */
.space-y-6 > div {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* 标题样式 */
h2.text-xl, h3.text-lg {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

h3.text-base {
  color: #3a5169;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

/* 步骤导航样式 */
:deep(.el-steps) {
  padding: 1rem 2rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem !important;
}

:deep(.el-step__title) {
  font-size: 1rem;
  font-weight: 500;
  color: #5e6d82;
}

:deep(.el-step__title.is-process) {
  color: #409eff;
  font-weight: 600;
}

:deep(.el-step__description) {
  font-size: 0.85rem;
  color: #8492a6;
}

/* 按钮样式增强 */
:deep(.el-button) {
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-button.is-disabled:hover) {
  transform: none;
  box-shadow: none;
}

/* 主按钮增强 */
:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

:deep(.el-button--success) {
  background-color: #67c23a;
  border-color: #67c23a;
}

:deep(.el-button--success:hover) {
  background-color: #85ce61;
  border-color: #85ce61;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__inner), :deep(.el-textarea__inner) {
  border-radius: 0.375rem;
  border-color: #dcdfe6;
}

:deep(.el-input__inner:focus), :deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 幻灯片缩略图样式 */
.grid-cols-2 > div, .grid-cols-3 > div, .grid-cols-4 > div, .grid-cols-6 > div {
  transition: all 0.25s ease;
  overflow: hidden;
}

.grid-cols-2 > div:hover, .grid-cols-3 > div:hover, .grid-cols-4 > div:hover, .grid-cols-6 > div:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #66b1ff;
}

/* 选中幻灯片效果增强 */
.border-blue-500 {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3) !important;
  transform: translateY(-3px);
}

/* 上传区域样式 */
.border-dashed {
  background-color: #f8fafc;
  transition: all 0.3s ease;
}

.border-dashed:hover {
  border-color: #409eff;
  background-color: #f0f7ff;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: auto;
  padding: 2rem;
  border-radius: 0.5rem;
  border: 2px dashed #d9d9d9;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background-color: #f0f7ff;
}

:deep(.el-upload__text) {
  color: #606266;
  margin-top: 1rem;
}

:deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
  font-weight: 500;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table__row) {
  height: auto !important;
}

:deep(.el-table__cell) {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

:deep(.el-table .cell) {
  word-break: break-word !important;
  white-space: pre-wrap !important;
}

:deep(.el-table__row:hover > td) {
  background-color: #f0f7ff !important;
}

/* 设置板块样式 */
.settings-panel {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 0.5rem;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);
}

.bg-blue-50 {
  background-color: #ecf5ff !important;
  border-color: #d9ecff !important;
}

.bg-purple-50 {
  background-color: #f5f0ff !important;
  border-color: #e5dbfa !important;
}

/* 圆形按钮样式 */
.action-btn {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 修复圆形按钮中的图标居中问题 */
.action-btn :deep(.el-icon) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  margin: 0;
}

/* 表单分栏布局 */
.script-settings-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 640px) {
  .script-settings-form {
    grid-template-columns: 1fr;
  }
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  background-color: #f5f7fa;
  padding: 15px 20px;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__title) {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.1rem;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

/* 全局动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 样式覆盖 */
:deep(.el-upload-list) {
  width: 100%;
}

/* 弹性布局水平居中 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自适应高度 */
.full-height {
  height: 100%;
}

/* 进度条样式 */
:deep(.el-slider__bar) {
  background-color: #409eff;
}

:deep(.el-slider__button) {
  border: 2px solid #409eff;
}

/* 页面内容间距 */
.p-6 {
  padding: 1.5rem !important;
}

.mb-6 {
  margin-bottom: 1.5rem !important;
}

/* 卡片和边框样式 */
.border, .rounded-lg {
  border-color: #ebeef5;
}

.bg-white {
  background-color: #ffffff;
}

/* 下拉选择器样式 */
:deep(.el-select-dropdown__item.selected) {
  color: #409eff;
  font-weight: 600;
}

:deep(.el-select-dropdown__item:hover) {
  background-color: #f0f7ff;
}

/* 工具提示样式 */
:deep(.el-tooltip__popper) {
  border-radius: 4px;
  font-size: 0.85rem;
}

/* 完整讲稿区域 */
.max-h-80 {
  max-height: 20rem;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
  line-height: 1.6;
}

.max-h-80 pre {
  white-space: pre-wrap;
  font-family: system-ui, -apple-system, sans-serif;
  color: #333;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .p-6 {
    padding: 1rem !important;
  }
  
  .grid-cols-6 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
  
  :deep(.el-steps) {
    padding: 0.5rem;
  }
}
</style> 