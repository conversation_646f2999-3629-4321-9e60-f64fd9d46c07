# Generated by Django 3.2.20 on 2025-04-27 02:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0003_alter_user_groups'),
    ]

    operations = [
        migrations.RenameField(
            model_name='knowledgecategory',
            old_name='create_at',
            new_name='created_at',
        ),
        migrations.RenameField(
            model_name='knowledgecategory',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='knowledgecategory',
            old_name='update_at',
            new_name='updated_at',
        ),
        migrations.RenameField(
            model_name='knowledgedataset',
            old_name='create_at',
            new_name='created_at',
        ),
        migrations.RenameField(
            model_name='knowledgedataset',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.<PERSON>ame<PERSON>ield(
            model_name='knowledgedataset',
            old_name='update_at',
            new_name='updated_at',
        ),
        migrations.RenameField(
            model_name='knowledgedocument',
            old_name='create_at',
            new_name='created_at',
        ),
        migrations.RenameField(
            model_name='knowledgedocument',
            old_name='delete_at',
            new_name='deleted_at',
        ),
        migrations.RenameField(
            model_name='knowledgedocument',
            old_name='update_at',
            new_name='updated_at',
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='学科名称')),
                ('code', models.CharField(blank=True, max_length=50, null=True, verbose_name='学科代码')),
                ('description', models.CharField(blank=True, max_length=500, null=True, verbose_name='学科描述')),
                ('level', models.PositiveSmallIntegerField(default=1, verbose_name='学科层级')),
                ('icon', models.CharField(blank=True, max_length=255, null=True, verbose_name='学科图标')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序顺序')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('create_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('delete_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('parent', models.ForeignKey(blank=True, db_column='parent_id', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='zhkt.subject')),
            ],
            options={
                'verbose_name': '学科',
                'verbose_name_plural': '学科',
                'db_table': 'zhkt_subject',
            },
        ),
        migrations.CreateModel(
            name='PPTProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ppt_id', models.CharField(max_length=64, verbose_name='文多多PPT ID')),
                ('title', models.CharField(max_length=255, verbose_name='PPT标题')),
                ('description', models.TextField(blank=True, null=True, verbose_name='PPT描述')),
                ('slides_count', models.IntegerField(default=0, verbose_name='幻灯片数量')),
                ('create_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('delete_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('subject', models.ForeignKey(db_column='subject_id', on_delete=django.db.models.deletion.RESTRICT, related_name='ppt_projects', to='zhkt.subject')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ppt_projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'PPT项目',
                'verbose_name_plural': 'PPT项目',
                'db_table': 'zhkt_ppt_projects',
            },
        ),
    ]
