import request from '@/utils/request'

/**
 * 学科相关API
 */
export const subjectApi = {
  /**
   * 获取学科列表
   * @returns {Promise} 包含学科列表的Promise
   */
  getSubjects() {
    return request({
      url: '/subjects/list/',
      method: 'get'
    })
  },
  
  /**
   * 获取学科详情
   * @param {string|number} subjectId 学科ID
   * @returns {Promise} 包含学科详情的Promise
   */
  getSubjectDetail(subjectId) {
    return request({
      url: `/subjects/${subjectId}/detail/`,
      method: 'get'
    })
  },
  
  /**
   * 获取学科树形结构
   * @returns {Promise} 包含学科树形结构的Promise
   */
  getSubjectTree() {
    return request({
      url: '/subjects/tree/',
      method: 'get'
    })
  }
} 