<template>
  <AdminLayout 
    pageTitle="控制台" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 flex-col-md-row">
          <!-- 用户统计 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                  <Icon name="group" size="xl" className="text-white" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.totalUsers }}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3">
              <router-link to="/admin/users" class="text-sm text-indigo-600 hover:text-indigo-900 font-medium">用户管理 &rarr;</router-link>
            </div>
          </div>

          <!-- 课程统计 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                  <Icon name="school" size="xl" className="text-white" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">课程总数</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.totalCourses }}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3">
              <router-link to="/admin/courses" class="text-sm text-green-600 hover:text-green-900 font-medium">课程管理 &rarr;</router-link>
            </div>
          </div>

          <!-- 作业完成率 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                  <Icon name="assignment" size="xl" className="text-white" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">作业完成率</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.homeworkCompletionRate }}%</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3">
              <router-link to="/admin/homework" class="text-sm text-yellow-600 hover:text-yellow-900 font-medium">查看详情 &rarr;</router-link>
            </div>
          </div>

          <!-- 积分统计 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-red-500 rounded-md p-3">
                  <Icon name="stars" size="xl" className="text-white" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">积分发放</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.totalPoints }}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3">
              <router-link to="/admin/points" class="text-sm text-red-600 hover:text-red-900 font-medium">积分管理 &rarr;</router-link>
            </div>
          </div>
        </div>

        <!-- 主视图区域 -->
        <div class="mt-6 grid grid-cols-1 gap-5 lg:grid-cols-2 flex-col-md-row">
          <!-- 最近活动 -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <div class="flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">最近活动</h3>
                <router-link to="/admin/audit" class="text-sm text-indigo-600 hover:text-indigo-900">更多</router-link>
              </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <ul class="divide-y divide-gray-200">
                <li v-for="activity in recentActivities" :key="activity.id" class="py-3">
                  <div class="flex space-x-3">
                    <img class="h-6 w-6 rounded-full" :src="activity.avatar" :alt="activity.name">
                    <div class="flex-1 space-y-1">
                      <div class="flex items-center justify-between">
                        <h3 class="text-sm font-medium">{{ activity.name }}</h3>
                        <p class="text-sm text-gray-500">{{ activity.time }}</p>
                      </div>
                      <p class="text-sm text-gray-500">{{ activity.description }}</p>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <!-- 系统通知 -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <div class="flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">系统通知</h3>
                <router-link to="/admin/notifications" class="text-sm text-indigo-600 hover:text-indigo-900">更多</router-link>
              </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <ul class="divide-y divide-gray-200">
                <li v-for="notice in systemNotices" :key="notice.id" class="py-3">
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <Icon 
                        :name="notice.status === 'warning' ? 'warning' : 'info'" 
                        :color="notice.status === 'warning' ? 'warning' : 'info'" 
                      />
                    </div>
                    <div class="ml-3 flex-1">
                      <p class="text-sm text-gray-900 font-medium">{{ notice.title }}</p>
                      <p class="text-sm text-gray-500">{{ notice.content }}</p>
                      <p class="text-xs text-gray-400 mt-1">{{ notice.time }}</p>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 图表数据 -->
        <div class="mt-6 grid grid-cols-1 gap-5 lg:grid-cols-2 flex-col-md-row">
          <!-- 用户增长趋势 -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900">用户增长趋势</h3>
            </div>
            <div class="px-4 py-5 sm:p-6 h-80">
              <canvas ref="userGrowthChart"></canvas>
            </div>
          </div>

          <!-- 系统使用情况 -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900">系统使用情况</h3>
            </div>
            <div class="px-4 py-5 sm:p-6 h-80">
              <canvas ref="systemUsageChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import Icon from '@/components/common/Icon.vue'
import { useAdminStore } from '@/stores/admin'
import Chart from 'chart.js/auto'

const adminStore = useAdminStore()

// 引用图表canvas元素
const userGrowthChart = ref(null)
const systemUsageChart = ref(null)

// 模拟数据
const stats = ref({
  totalUsers: 2856,
  totalCourses: 142,
  homeworkCompletionRate: 78.5,
  totalPoints: 254382
})

const recentActivities = ref([
  {
    id: 1,
    name: '李明',
    avatar: '/images/avatars/user1.jpg',
    time: '3分钟前',
    description: '新用户注册并完成了个人资料设置'
  },
  {
    id: 2,
    name: '王教授',
    avatar: '/images/avatars/teacher.png',
    time: '25分钟前',
    description: '创建了新课程：《高等数学 II》'
  },
  // ... 更多活动数据
])

const systemNotices = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于周日凌晨2:00-4:00进行例行维护，请提前做好准备。',
    time: '2023-06-10 10:30',
    iconClass: 'material-icons text-yellow-500'
  },
  {
    id: 2,
    title: '新功能上线',
    content: '智能批改功能已上线，请前往教师面板查看使用说明。',
    time: '2023-06-08 14:15',
    iconClass: 'material-icons text-blue-500'
  },
  // ... 更多通知数据
])

onMounted(async () => {
  // 初始化用户增长趋势图表
  const userGrowthCtx = userGrowthChart.value.getContext('2d')
  new Chart(userGrowthCtx, {
    type: 'line',
    data: {
      labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
      datasets: [{
        label: '学生用户',
        data: [156, 210, 385, 481, 590, 702],
        borderColor: 'rgb(79, 70, 229)',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.4,
        fill: true
      }, {
        label: '教师用户',
        data: [28, 35, 42, 56, 65, 74],
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  })

  // 初始化系统使用情况图表
  const systemUsageCtx = systemUsageChart.value.getContext('2d')
  new Chart(systemUsageCtx, {
    type: 'doughnut',
    data: {
      labels: ['课程学习', '作业提交', '论坛讨论', '资源下载', '积分商城'],
      datasets: [{
        data: [35, 25, 15, 15, 10],
        backgroundColor: [
          'rgba(79, 70, 229, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(59, 130, 246, 0.8)'
        ],
        hoverOffset: 4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return context.label + ': ' + context.parsed + '%'
            }
          }
        }
      }
    }
  })
  await adminStore.fetchAdminInfo()
})
</script>

<style scoped>
.flex-col-md-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  flex-wrap: wrap;
  gap: 1.25rem;
}

.flex-col-md-row > div {
  flex: 1;
  min-width: 250px;
  max-width: calc(25% - 1rem);
}

/* 主视图区域和图表区域的卡片样式 */
.mt-6.flex-col-md-row > div {
  max-width: calc(50% - 1rem);
  min-width: 300px;
}

@media (max-width: 1024px) {
  .flex-col-md-row > div {
    max-width: calc(50% - 1rem);
  }
  
  .mt-6.flex-col-md-row > div {
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .flex-col-md-row > div {
    max-width: 100%;
  }
}
</style>
