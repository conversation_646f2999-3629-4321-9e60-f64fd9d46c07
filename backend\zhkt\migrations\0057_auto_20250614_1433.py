# Generated by Django 3.2.20 on 2025-06-14 14:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0056_homework_is_graded'),
    ]

    operations = [
        migrations.CreateModel(
            name='AILectureSubtitle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.IntegerField(default=0, verbose_name='开始时间(毫秒)')),
                ('end_time', models.IntegerField(default=0, verbose_name='结束时间(毫秒)')),
                ('subtitle_text', models.TextField(verbose_name='字幕文本')),
                ('speech_content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='zhkt.ailecturespeechcontent', verbose_name='语音内容ID')),
            ],
            options={
                'verbose_name': 'AI讲课字幕内容',
                'verbose_name_plural': 'AI讲课字幕内容',
            },
        ),
        migrations.AddIndex(
            model_name='ailecturesubtitle',
            index=models.Index(fields=['speech_content'], name='zhkt_ailect_speech__c77fd7_idx'),
        ),
    ]
