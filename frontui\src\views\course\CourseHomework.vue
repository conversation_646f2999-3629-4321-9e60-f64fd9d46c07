<template>
  <div class="course-homework">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">作业管理</h1>
      <div class="flex gap-3">
        <button 
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
          @click="showCreateHomeworkDialog"
        >
          <span class="material-icons text-sm">add</span>
          创建作业
        </button>
        <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
          <span class="material-icons text-sm">file_upload</span>
          导入作业
        </button>
        <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
          <span class="material-icons text-sm">file_download</span>
          导出记录
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-blue-50 rounded-md p-4 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-500 text-sm">总作业数量</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.total }}</p>
          </div>
          <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
            <span class="material-icons text-blue-600 text-xl">description</span>
          </div>
        </div>
      </div>
      <div class="bg-green-50 rounded-md p-4 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-500 text-sm">进行中作业</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.ongoing }}</p>
          </div>
          <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
            <span class="material-icons text-green-600 text-xl">hourglass_top</span>
          </div>
        </div>
      </div>
      <div class="bg-red-50 rounded-md p-4 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-500 text-sm">已截止作业</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.ended }}</p>
          </div>
          <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
            <span class="material-icons text-red-600 text-xl">assignment</span>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 rounded-md p-4 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-500 text-sm">草稿</p>
            <p class="text-2xl font-bold text-gray-800">{{ stats.draft }}</p>
          </div>
          <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
            <span class="material-icons text-gray-600 text-xl">edit_note</span>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow">
      <!-- 搜索和筛选区域 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex gap-3 items-center">
          <div class="relative flex-1">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索作业..." 
              class="w-full border border-gray-300 rounded-md pl-9 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
              <span class="material-icons text-gray-600">filter_list</span>
              筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <div class="space-y-3">
                <div class="font-medium flex items-center gap-1.5">
                  <span class="material-icons text-base text-gray-600">label</span>
                  状态
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="status in homeworkStatuses" 
                    :key="status"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.statuses.includes(status) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('statuses', status)"
                  >
                    <span v-if="selectedFilters.statuses.includes(status)" class="material-icons text-xs">check_circle</span>
                    {{ status }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 作业列表 -->
      <div class="p-4">
        <el-table :data="filteredHomeworks" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="title" label="作业信息" min-width="300">
            <template #default="{ row }">
              <div class="flex items-start gap-2">
                <span 
                  class="px-2 py-0.5 text-xs font-medium rounded-md"
                  :class="{
                    'bg-green-100 text-green-800': row.status === '进行中',
                    'bg-red-100 text-red-800': row.status === '已截止',
                    'bg-gray-100 text-gray-800': row.status === '草稿'
                  }"
                >
                  {{ row.status }}
                </span>
                <div>
                  <div class="text-gray-900">{{ row.title }}</div>
                  <div class="text-gray-500 text-sm mt-1">{{ row.description }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="start_time" label="开始日期" width="180">
            <template #default="{ row }">
              <div class="flex items-center gap-1">
                <span class="material-icons text-gray-400 text-sm">event</span>
                <span>{{ formatDateTime(row.start_time) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="end_time" label="截止日期" width="180">
            <template #default="{ row }">
              <div class="flex items-center gap-1">
                <span class="material-icons text-gray-400 text-sm">event</span>
                <span>{{ formatDateTime(row.end_time) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="submitCount" label="提交情况" width="120">
            <template #default="{ row }">
              <div>{{ row.submit_count }}/{{ row.total_count }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="300" fixed="right">
            <template #default="{ row }">
              <div class="flex gap-2 whitespace-nowrap">
                <button 
                  v-if="!row.is_published"
                  @click="publishHomeworkHandler(row)"
                  class="text-blue-600 hover:text-blue-800"
                >
                  发布
                </button>
                <button 
                  v-if="row.is_published"
                  @click="unpublishHomeworkHandler(row)"
                  class="text-orange-600 hover:text-orange-800"
                >
                  撤回
                </button>
                <button 
                  @click="viewHomework(row)"
                  class="text-blue-600 hover:text-blue-800"
                >
                  查看
                </button>
                <button 
                  v-if="!row.is_published"
                  @click="editHomework(row)"
                  class="text-green-600 hover:text-green-800"
                >
                  编辑
                </button>
                <button 
                  v-if="!row.is_published"
                  @click="deleteHomeworkHandler(row)"
                  class="text-red-600 hover:text-red-800"
                >
                  删除
                </button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 创建/编辑作业对话框 -->
    <el-dialog
      v-model="homeworkDialogVisible"
      :title="editingHomework ? '编辑作业' : '创建作业'"
      width="800px"
    >
      <el-form :model="homeworkForm" label-width="80px">
        <el-form-item label="作业标题">
          <el-input v-model="homeworkForm.title" placeholder="请输入作业标题"></el-input>
        </el-form-item>
        <el-form-item label="作业描述">
          <el-input 
            v-model="homeworkForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入作业描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="homeworkForm.start_time"
            type="datetime"
            placeholder="选择开始时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="截止日期">
          <el-date-picker
            v-model="homeworkForm.end_time"
            type="datetime"
            placeholder="选择截止日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="题目">
          <div class="mb-4">
            <el-button @click="showQuestionSelector = true" type="primary">
              从题库选择题目
            </el-button>
          </div>
          <div v-if="selectedQuestions.length > 0" class="space-y-4">
            <div 
              v-for="(question, index) in selectedQuestions" 
              :key="question.id"
              class="border rounded-md p-2"
            >
              <div class="flex justify-between items-start mb-2">
                <div class="font-medium">题目 {{ index + 1 }}</div>
                <div class="flex items-center gap-4">
                  <button 
                    @click="selectedQuestions.splice(index, 1)"
                    class="text-red-600 hover:text-red-800"
                  >
                    删除
                  </button>
                </div>
              </div>
              <div class="text-green-500 text-sm">{{ question.question_type_display }}</div>
              <div class="text-gray-600">{{ question.title }}</div>
            </div>
          </div>
          <div v-else class="text-gray-500 text-center py-4">
            暂未选择题目
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="homeworkDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleHomeworkSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 题库选择对话框 -->
    <el-dialog
      v-model="showQuestionSelector"
      title="选择题目"
      width="800px"
    >
      <div class="mb-4">
        <el-input
          v-model="questionSearchQuery"
          placeholder="搜索题目..."
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <el-table
        :data="filteredQuestions"
        style="width: 100%"
        @selection-change="handleQuestionSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="title" label="题目" show-overflow-tooltip></el-table-column>
        <el-table-column prop="question_type_display" label="类型" width="120"></el-table-column>
        <el-table-column prop="difficulty_display" label="难度" width="100"></el-table-column>
        <el-table-column prop="score" label="分值" width="100"></el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showQuestionSelector = false">取消</el-button>
          <el-button type="primary" @click="confirmQuestionSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 作业详情对话框 -->
    <el-dialog
      v-model="homeworkDetailVisible"
      title="作业详情"
      width="800px"
    >
      <div class="mb-4">
        <h2 class="text-2xl font-bold mb-4">作业信息</h2>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <span class="font-medium">作业标题:</span>
            <span class="text-gray-600 ml-2">{{ currentHomework.title }}</span>
          </div>
          <div>
            <span class="font-medium">作业状态:</span>
            <span class="text-gray-600 ml-2">{{ currentHomework.status }}</span>
          </div>
          <div>
            <span class="font-medium">开始时间:</span>
            <span class="text-gray-600 ml-2">{{ formatDateTime(currentHomework.start_time) }}</span>
          </div>
          <div>
            <span class="font-medium">截止时间:</span>
            <span class="text-gray-600 ml-2">{{ formatDateTime(currentHomework.end_time) }}</span>
          </div>
          <div>
            <span class="font-medium">题目数量:</span>
            <span class="text-gray-600 ml-2">{{ currentHomework.questions.length }}</span>
          </div>
          <div>
            <span class="font-medium">总分值:</span>
            <span class="text-gray-600 ml-2">{{ currentHomework.total_score }}</span>
          </div>
          <div class="col-span-2">
            <span class="font-medium">作业描述:</span>
            <span class="text-gray-600 ml-2">{{ currentHomework.description }}</span>
          </div>
        </div>
      </div>
      <div class="mb-4">
        <h2 class="text-2xl font-bold mb-4">题目列表</h2>
        <el-table :data="currentHomework.questions" style="width: 100%">
          <el-table-column prop="question_detail.question_type_display" label="类型" width="100"></el-table-column>
          <el-table-column prop="question_detail.title" label="题目" min-width="300"></el-table-column>
          <el-table-column prop="score" label="分值" width="100"></el-table-column>
        </el-table>
      </div>
      <div class="mb-4">
        <h2 class="text-2xl font-bold mb-4">提交统计</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="text-sm text-gray-500">总学生数</div>
            <div class="text-xl font-bold text-gray-800">{{ homeworkStats?.total_students || 0 }}</div>
          </div>
          <div class="bg-green-50 rounded-lg p-4">
            <div class="text-sm text-gray-500">已提交</div>
            <div class="text-xl font-bold text-gray-800">{{ homeworkStats?.submitted_count || 0 }}</div>
          </div>
          <div class="bg-yellow-50 rounded-lg p-4">
            <div class="text-sm text-gray-500">按时提交</div>
            <div class="text-xl font-bold text-gray-800">{{ homeworkStats?.on_time_count || 0 }}</div>
          </div>
          <div class="bg-red-50 rounded-lg p-4">
            <div class="text-sm text-gray-500">迟交</div>
            <div class="text-xl font-bold text-gray-800">{{ homeworkStats?.late_count || 0 }}</div>
          </div>
        </div>
        <div class="mt-4">
          <div class="flex items-center">
            <div class="text-sm text-gray-500 mr-2">完成率</div>
            <el-progress 
              :percentage="homeworkStats?.completion_rate || 0"
              :format="(val) => val + '%'"
              :stroke-width="10"
              :color="getProgressColor"
            ></el-progress>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button style="color: green;" 
        @click="handleGoGraded(currentHomework)">批改作业</el-button>
        <el-button @click="homeworkDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { courseApi } from '@/api/course'
import {
  getHomeworkList,
  getCourseQuestions,
  createHomework,
  updateHomework,
  deleteHomework,
  getHomeworkStatistics,
  getCourseHomeworkStats,
  publishHomework,
  unpublishHomework
} from '@/api/homework'

const route = useRoute()
const router = useRouter()
const courseId = route.params.courseId

// 统计数据
const stats = ref({
  total: 0,
  ongoing: 0,
  ended: 0,
  draft: 0
})

// 搜索和筛选
const searchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  statuses: []
})

// 作业状态
const homeworkStatuses = ['进行中', '已截止', '草稿']

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 加载状态
const loading = ref(false)

// 模拟作业数据
const homeworks = ref([])

// 过滤后的作业列表
const filteredHomeworks = computed(() => {
  let result = [...homeworks.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(homework =>
      homework.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      homework.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
  
  // 状态过滤
  if (selectedFilters.value.statuses.length > 0) {
    result = result.filter(homework =>
      selectedFilters.value.statuses.includes(homework.status)
    )
  }
  
  return result
})

// 作业对话框
const homeworkDialogVisible = ref(false)
const editingHomework = ref(null)
const homeworkForm = ref({
  title: '',
  description: '',
  start_time: '',
  end_time: '',
  course: null,
  total_score: 0,
  question_settings: []
})

// 添加题库数据
const questionBank = ref([])
const selectedQuestions = ref([])

// 获取课程题库题目
const loadQuestionBank = async () => {
  try {
    const response = await getCourseQuestions(courseId)
    questionBank.value = response
  } catch (error) {
    ElMessage.error('获取题库失败')
  }
}

// 加载作业列表
const loadHomeworks = async () => {
  loading.value = true
  try {
    const response = await getHomeworkList(courseId)
    response.results.map(item=>{
      item.status=item.status.replace('pending','进行中')
      item.status=item.status.replace('overdue','已截止')
    })
    
    homeworks.value = response.results || []
    total.value = response.count
  } catch (error) {
    ElMessage.error('获取作业列表失败',error)
    homeworks.value = []
  } finally {
    loading.value = false
  }
}

// 显示创建作业对话框
const showCreateHomeworkDialog = () => {
  editingHomework.value = null
  homeworkForm.value = {
    title: '',
    description: '',
    start_time: '',
    end_time: '',
    course: courseId,
    total_score: 0,
    question_settings: []
  }
  selectedQuestions.value = []
  loadQuestionBank()
  homeworkDialogVisible.value = true
}

// 编辑作业
const editHomework = (homework) => {
  editingHomework.value = homework
  const currentHomeworkQuestions = homework.questions.map(q => ({
      question: q.question_detail,
      question_id: q.id,
      order: q.order,
      score: q.score,
      title: q.question_detail.title
  }))
  homeworkForm.value = {
    title: homework.title,
    description: homework.description,
    start_time: homework.start_time,
    end_time: homework.end_time,
    course: homework.course,
    total_score: homework.total_score,
    question_settings: currentHomeworkQuestions
  }
  selectedQuestions.value = currentHomeworkQuestions.map(q => q.question)
  homeworkDialogVisible.value = true
}

// 作业详情对话框
const homeworkDetailVisible = ref(false)
const currentHomework = ref(null)
const homeworkStats = ref(null)

// 查看作业
const viewHomework = async (homework) => {
  currentHomework.value = homework
  homeworkDetailVisible.value = true
  
  try {
    // 获取作业统计信息
    const stats = await getHomeworkStatistics(homework.id)
    homeworkStats.value = stats
  } catch (error) {
    ElMessage.error('获取作业统计信息失败')
  }
}

// 删除作业
const deleteHomeworkHandler = async (homework) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除作业 "${homework.title}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteHomework(homework.id)
    ElMessage.success('删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 切换筛选条件
const toggleFilter = (type, value) => {
  const index = selectedFilters.value[type].indexOf(value)
  if (index === -1) {
    selectedFilters.value[type].push(value)
  } else {
    selectedFilters.value[type].splice(index, 1)
  }
}

// 题库选择相关
const showQuestionSelector = ref(false)
const questionSearchQuery = ref('')
const selectedQuestionIds = ref([])

// 过滤题库题目
const filteredQuestions = computed(() => {
  if (!questionSearchQuery.value) {
    return questionBank.value
  }
  const query = questionSearchQuery.value.toLowerCase()
  return questionBank.value.filter(q => 
    q.title.toLowerCase().includes(query) ||
    q.content.toLowerCase().includes(query)
  )
})

// 处理题目选择变化
const handleQuestionSelectionChange = (selection) => {
  selectedQuestionIds.value = selection.map(q => q.id)
}

// 确认题目选择
const confirmQuestionSelection = () => {
  const newQuestions = questionBank.value.filter(q => 
    selectedQuestionIds.value.includes(q.id)
  )
  selectedQuestions.value = [...selectedQuestions.value, ...newQuestions]
  showQuestionSelector.value = false
}

// 处理作业提交
const handleHomeworkSubmit = async () => {
  if (!homeworkForm.value.title.trim()) {
    ElMessage.warning('请输入作业标题')
    return
  }
  if (!homeworkForm.value.start_time) {
    ElMessage.warning('请选择开始时间')
    return
  }
  if (!homeworkForm.value.end_time) {
    ElMessage.warning('请选择截止时间')
    return
  }
  if (selectedQuestions.value.length === 0) {
    ElMessage.warning('请至少选择一道题目')
    return
  }

  const homeworkData = {
    ...homeworkForm.value,
    question_settings: selectedQuestions.value.map((q, index) => ({
      question_id: q.id,
      order: index + 1,
      score: q.score || 0
    }))
  }

  try {
    if (editingHomework.value) {
      await updateHomework(editingHomework.value.id, homeworkData)
      ElMessage.success('作业更新成功')
    } else {
      await createHomework(homeworkData)
      ElMessage.success('作业创建成功')
    }
    homeworkDialogVisible.value = false
    refreshData()
  } catch (error) {
    ElMessage.error(editingHomework.value ? '更新作业失败' : '创建作业失败')
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  // 这里应该重新加载数据
  loadHomeworks() 
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  // 这里应该重新加载数据
  loadHomeworks()
}

// 在 mounted 时加载作业列表和统计数据
onMounted(() => {
  loadHomeworks()
  loadStats()
})

// 在创建、更新、删除作业后刷新统计数据
const refreshData = () => {
  loadHomeworks()
  loadStats()
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await getCourseHomeworkStats(courseId)
    stats.value = response
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

// 在 script 部分添加进度条颜色计算函数
const getProgressColor = (percentage) => {
  if (percentage < 30) return '#F56C6C'
  if (percentage < 70) return '#E6A23C'
  return '#67C23A'
}

// 发布作业
const publishHomeworkHandler = async (homework) => {
  try {
    await ElMessageBox.confirm(
      `确定要发布作业 "${homework.title}" 吗？发布后将无法修改或删除。`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await publishHomework(homework.id)
    ElMessage.success('作业发布成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('发布失败')
    }
  }
}

// 撤回作业
const unpublishHomeworkHandler = async (homework) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤回作业 "${homework.title}" 吗？撤回后学生将无法查看此作业。`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await unpublishHomework(homework.id)
    ElMessage.success('作业已撤回')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤回失败')
    }
  }
}

//跳转作业批改
const handleGoGraded = async (currentHomework) => {
  const course = await courseApi.getCourseById(currentHomework.course)
  console.log(course);
  
  homeworkDetailVisible.value = false
  router.push({ 
    name: 'course-dograde', 
    params: { homeworkId: currentHomework.id },
    query: {
      name: course.name,
      code: course.code,
      teacherName: course.teacher.user.alias,
      teacherAvatar: course.teacher.user.avatar?course.teacher.user.avatar:teacherAvatar
    }
  })
}
</script> 

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 