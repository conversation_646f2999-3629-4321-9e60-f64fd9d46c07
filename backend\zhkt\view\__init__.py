# -*- coding: utf-8 -*-
# @Time    : 2025/4/23
# <AUTHOR> lhq
# @File    : __init__.py.py
# @Description :
from .bookshelf_view import BookshelfViewSet
from .ppt_view import PPTViewSet
from .subject_view import SubjectViewSet
from .knowledge_view import KnowledgeViewSet
from .lesson_plan_view import LessonPlanViewSet
from .video_assistant_view import VideoAssistantViewSet
from .speech_design_view import SpeechDesignViewSet
from .audio_view import AudioViewSet
from .ai_assistant_view import AIChatViewSet, AIChatMessageViewSet, AIPromptViewSet
from .course_view import CourseViewSet, CourseOverviewViewSet, ChapterViewSet, \
    ResourceViewSet, LessonViewSet, NoteViewSet, CommentViewSet
from .custom_token_view import CustomTokenObtainPairView
from .db_pool_test_view import DBPoolTestView   
from .homework_view import HomeworkViewSet, SubmissionViewSet, FeedbackViewSet
from .orgznization_view import CollegeViewSet, MajorViewSet, ClassGroupViewSet
from .points_view import PointsRecordViewSet, ProductViewSet, OrderViewSet
from .question_view import QuestionViewSet
from .system_view import RoleViewSet, DeptViewSet, MenuViewSet
from .user_view import UserViewSet, StudentViewSet, TeacherViewSet, AdminViewSet
from .digital_human_view import DigitalHumanViewSet

__all__ = [
    'PPTViewSet',
    'SubjectViewSet',
    'KnowledgeViewSet',
    'LessonPlanViewSet',
    'VideoAssistantViewSet',
    'SpeechDesignViewSet',
    'BookshelfViewSet',
    'DBPoolTestView',
    'AudioViewSet',
    'CustomTokenObtainPairView',
    'UserViewSet', 'RoleViewSet', 'StudentViewSet', 'TeacherViewSet', 'AdminViewSet',
    'CollegeViewSet', 'MajorViewSet', 'ClassGroupViewSet',
    'CourseViewSet', 'ChapterViewSet', 'ResourceViewSet',
    'HomeworkViewSet', 'SubmissionViewSet', 'FeedbackViewSet',
    'PointsRecordViewSet', 'ProductViewSet', 'OrderViewSet',
    'AIChatViewSet', 'AIChatMessageViewSet', 'AIPromptViewSet',
    'DeptViewSet', 'MenuViewSet',
    'LessonViewSet', 'NoteViewSet',
    'CommentViewSet', 'CourseOverviewViewSet',
    'QuestionViewSet',
    'DigitalHumanViewSet'
]

