<template>
  <view class="page-container">
    <!-- 移除导航栏，添加会话列表按钮 -->
    <view class="toggle-btn" @tap="toggleConversationList">
      <uni-icons :type="showConversationList ? 'list' : 'bars'" size="24" color="#333"></uni-icons>
    </view>

    <!-- 主内容区域 -->
    <view class="chat-container">
      <!-- 左侧会话列表 -->
      <view class="conversation-list" :class="{ 'hidden': !showConversationList }">
        <view class="new-chat-btn" @tap="createNewChat">
          <uni-icons type="plus" size="20"></uni-icons>
          <text>开启新对话</text>
        </view>
        
        <scroll-view class="conversation-scroll" scroll-y>
          <view 
            class="conversation-item" 
            v-for="(item, index) in conversationList" 
            :key="item.id"
            :class="{ active: currentConversationId === item.id }"
            @tap="selectConversation(item)"
          >
            <view class="conversation-content">
              <text class="title">{{ item.title || '新对话' }}</text>
              <text class="time">{{ formatDateTime(item.time) }}</text>
            </view>
            <view class="conversation-actions">
              <uni-icons 
                type="trash" 
                size="18" 
                color="#999" 
                @tap.stop="deleteConversation(item)"
              ></uni-icons>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧聊天区域 -->
      <view class="chat-area">
        <!-- 聊天记录 -->
        <scroll-view 
          class="message-list" 
          scroll-y 
          :scroll-into-view="scrollToId" 
          :scroll-with-animation="true"
        >
          <view 
            class="message-item" 
            v-for="(item, index) in chatList" 
            :key="index" 
            :id="'msg-' + item.id"
          >
            <!-- 用户消息 -->
            <view class="message user" v-if="item.role === 'USER'">
              <view class="avatar">
                <image :src="userAvatar" mode="aspectFill"></image>
              </view>
              <view class="content">
                <text>{{ item.content }}</text>
              </view>
            </view>
            
            <!-- AI消息 -->
            <view class="message ai" v-else>
              <view class="avatar">
                <image :src="systemAvatar" mode="aspectFill"></image>
              </view>
              <view class="content">
                <rich-text :nodes="formatMessage(item.content)"></rich-text>
                <view class="actions">
                  <view class="action-item" @tap="copyMessage(item.content)">
                    <uni-icons type="copy" size="16"></uni-icons>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 底部输入区域 -->
        <view class="input-area">
          <view class="input-wrapper">
            <textarea
              class="input"
              v-model="messageText"
              :adjust-position="false"
              :cursor-spacing="20"
              :show-confirm-bar="false"
              :hold-keyboard="true"
              @confirm="handleSendMessage"
              placeholder="请输入您的问题"
            ></textarea>
            <view class="voice-btn" @tap="startVoiceInput">
              <uni-icons type="mic" size="24"></uni-icons>
            </view>
          </view>
          <view 
            class="send-btn" 
            :class="{ active: messageText.trim() }" 
            @tap="handleSendMessage"
          >
            发送
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import * as aiApi from '@/api/modules/ai'
import { useUserStore } from '@/store/modules/user'
import { getFriendlyDate } from '@/utils/date'
import { ref } from 'vue'
import systemAvatar from '@/static/images/icons/assistant.png'

export default {
  data() {
    return {
      messageText: '',
      chatList: [],
      conversationList: [],
      page: 1,
      isLoading: false,
      scrollToId: '',
      userAvatar: '',
      currentConversationId: null,
      showConversationList: false,
      isTyping: ref(false),
      systemAvatar: systemAvatar
    }
  },

  setup() {
    const userStore = useUserStore()

    return {
      userStore,
      getFriendlyDate
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    // 初始化页面
    async initPage() {
      this.isLoading = true
      try {
        const userInfo = await this.userStore.getUserInfo()
        this.userAvatar = userInfo.user.avatar
        await this.loadConversationList()
      } catch (error) {
        this.$toast.error('加载失败')
      }
      this.isLoading = false
    },

    // 加载会话列表
    async loadConversationList() {
      try {
        const response = await aiApi.getChatSession({
          page: 1,
          limit: 50
        })
        this.conversationList = response.results.map(item => ({
          id: item.id,
          title: item.title,
          time: item.created_at
        }))
      } catch (error) {
        this.$toast.error('获取会话列表失败')
      }
    },

    // 创建新会话
    createNewChat() {
      this.currentConversationId = null
      this.chatList = []
      this.messageText = ''
      this.showConversationList = false
    },

    // 选择会话
    async selectConversation(conversation) {
      this.currentConversationId = conversation.id
      this.page = 1
      await this.loadChatHistory()
      this.showConversationList = false  // 选择会话后自动隐藏会话列表
    },

    // 删除会话
    async deleteConversation(conversation) {
      try {
        await aiApi.deleteChat({ id: conversation.id })
        this.conversationList = this.conversationList.filter(item => item.id !== conversation.id)
        if (this.currentConversationId === conversation.id) {
          this.createNewChat()
        }
        this.$toast.success('删除成功')
      } catch (error) {
        this.$toast.error('删除失败')
      }
    },

    // 加载聊天历史
    async loadChatHistory() {
      try {
        const response = await aiApi.getChatMessage({
          page: this.page,
          limit: 20,
          chat: this.currentConversationId
        })
        if (this.page === 1) {
          this.chatList = response
        } else {
          this.chatList = [...response, ...this.chatList]
        }
        this.scrollToBottom()
      } catch (error) {
        this.$toast.error('获取聊天记录失败')
      }
    },

    // 发送消息
    async handleSendMessage() {
      if (!this.messageText.trim()) return
      const message = this.messageText
      this.messageText = ''
      
      // 添加用户消息
      const userMessage = {
        id: Date.now(),
        type: 'user',
        title: message,
        time: new Date().toISOString()
      }
      this.chatList.push(userMessage)
      this.scrollToBottom()

      try {
        // 发送到服务器并获取AI回复
        const response = await aiApi.sendMessage({ 
          content: message,
          conversation_id: this.currentConversationId
        })
        
        // 如果是新会话，添加到会话列表
        if (!this.currentConversationId) {
          this.currentConversationId = response.conversation_id
          this.conversationList.unshift({
            id: response.conversation_id,
            firstMessage: message,
            time: new Date().toISOString()
          })
        }

        const aiMessage = {
          id: response.id,
          type: 'ai',
          title: response.content,
          time: new Date().toISOString(),
          isCollected: false
        }
        this.chatList.push(aiMessage)
        this.scrollToBottom()
      } catch (error) {
        this.$toast.error('发送失败')
      }
    },

    // 语音输入
    async startVoiceInput() {
      try {
        const { tempFilePath } = await new Promise((resolve, reject) => {
          uni.startRecord({
            success: resolve,
            fail: reject
          })
        })

        const result = await aiApi.recognizeVoice({
          filePath: tempFilePath
        })

        this.messageText = result.text
      } catch (error) {
        this.$toast.error('语音识别失败')
      }
    },

    // 复制消息
    copyMessage(content) {
      uni.setClipboardData({
        data: content,
        success: () => {
          this.$toast.success('复制成功')
        }
      })
    },

    // 格式化消息内容（支持代码高亮等）
    formatMessage(content) {
      // 这里可以添加markdown解析、代码高亮等功能
      return content
    },

    // 滚动到底部
    scrollToBottom() {
      if (this.chatList.length > 0) {
        this.scrollToId = 'msg-' + this.chatList[this.chatList.length - 1].id
      }
    },

    // 格式化日期的辅助方法
    formatDateTime(date) {
      if (!date) return ''
      const dateStr = typeof date === 'string' ? date : date.toISOString()
      return getFriendlyDate(dateStr)
    },

    // 添加切换会话列表显示的方法
    toggleConversationList() {
      this.showConversationList = !this.showConversationList
    }
  }
}
</script>

<style lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
  position: relative;
}

// 重新设置会话列表按钮样式
.toggle-btn {
  position: fixed;
  bottom: 180rpx;
  left: 20rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.chat-container {
  flex: 1;
  position: relative;
  display: flex;

  // 左侧会话列表
  .conversation-list {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 300rpx;
    background: #fff;
    border-right: 1rpx solid #eee;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    z-index: 100;
    padding-top: 120rpx; // 调整顶部间距，为按钮留出空间

    &.hidden {
      transform: translateX(-100%);
    }

    .new-chat-btn {
      padding: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;
      margin: 20rpx;
      border-radius: 10rpx;
      
      text {
        margin-left: 10rpx;
        font-size: 28rpx;
      }
    }

    .conversation-scroll {
      flex: 1;
      overflow-y: auto;
    }

    .conversation-item {
      padding: 20rpx;
      border-bottom: 1rpx solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      &.active {
        background: #e6f7ff;
      }

      .conversation-content {
        flex: 1;
        overflow: hidden;

        .title {
          font-size: 28rpx;
          color: #333;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .time {
          font-size: 24rpx;
          color: #999;
          margin-top: 10rpx;
        }
      }

      .conversation-actions {
        margin-left: 20rpx;
        opacity: 0;
      }

      &:hover .conversation-actions {
        opacity: 1;
      }
    }
  }

  // 右侧聊天区域
  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    margin-left: 0;
    transition: margin-left 0.3s ease;
    padding-top: 20rpx; // 调整顶部间距

    .message-list {
      flex: 1;
      padding: 20rpx;
      overflow-y: auto;

      .message-item {
        margin-bottom: 30rpx;

        .message {
          display: flex;
          align-items: flex-start;
          margin-bottom: 20rpx;

          .avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            overflow: hidden;
            
            image {
              width: 100%;
              height: 100%;
            }
          }

          .content {
            max-width: 60%;
            padding: 20rpx;
            border-radius: 10rpx;
            font-size: 28rpx;
            line-height: 1.5;
            word-break: break-all;
          }

          &.user {
            .content {
              margin-left: 20rpx;
              background: #e6f7ff;
              color: #333;
              border: 1px solid #91d5ff;
            }
          }

          &.ai {
            .content {
              margin-left: 20rpx;
              background: #fff;
              color: #333;
              box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

              .actions {
                display: flex;
                justify-content: flex-end;
                margin-top: 10rpx;

                .action-item {
                  padding: 10rpx;
                  margin-left: 20rpx;
                }
              }
            }
          }
        }
      }
    }

    .input-area {
      padding: 20rpx;
      background: #fff;
      border-top: 1rpx solid #eee;
      display: flex;
      align-items: center;

      .input-wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        background: #f5f5f5;
        border-radius: 10rpx;
        padding: 20rpx;

        .input {
          flex: 1;
          height: 80rpx;
          font-size: 28rpx;
          line-height: 40rpx;
        }

        .voice-btn {
          margin-left: 20rpx;
          padding: 10rpx;
        }
      }

      .send-btn {
        width: 120rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        margin-left: 20rpx;
        border-radius: 10rpx;
        font-size: 28rpx;
        color: #999;
        background: #f5f5f5;

        &.active {
          background: #007AFF;
          color: #fff;
        }
      }
    }
  }
}
</style> 