<template>
  <StudentLayout 
    pageTitle="个人资料" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    
    <div class="space-y-4">
      <!-- 压缩版个人资料卡片 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-4">
          <div class="flex items-start">
            <!-- 头像 -->
            <el-avatar 
              :size="70" 
              :src="studentStore.studentData.avatar"
              class="border-2 border-white shadow-md mr-4" />
            
            <!-- 基本信息 -->
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h2 class="text-xl font-semibold text-gray-800">{{ studentStore.userFullName }}</h2>
              </div>
              
              <!-- 学生信息色块 -->
              <div class="flex flex-wrap gap-2 mb-2">
                <span class="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">学号：{{ studentStore.studentData.studentId }}</span>
                <span class="px-3 py-1.5 bg-green-100 text-green-800 rounded-md text-sm font-medium">{{ academicInfo.college }}</span>
                <span class="px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium">{{ academicInfo.major }}</span>
                <span class="px-3 py-1.5 bg-purple-100 text-purple-800 rounded-md text-sm font-medium">{{ academicInfo.className }}</span>
              </div>
            </div>
          </div>

          <!-- 数据分析指标 - 水平布局 -->
          <div class="flex mt-3 pt-3 border-t gap-3">
            <!-- 学习时长与排名 -->
            <div class="flex-1">
              <div class="bg-blue-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-blue-800 flex items-center mb-1">
                  <el-icon class="text-blue-600 mr-2"><Timer /></el-icon>
                  学习时长
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-blue-900 mb-1">
                    {{ studentAnalytics.totalLearningHours }}小时
                  </div>
                  <div class="text-sm text-blue-700 bg-blue-100 rounded-full px-3 py-0.5 inline-block">
                    排名: {{ studentAnalytics.classRank }}/{{ studentAnalytics.classTotal }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 课程完成情况 -->
            <div class="flex-1">
              <div class="bg-green-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-green-800 flex items-center mb-1">
                  <el-icon class="text-green-600 mr-2"><Notebook /></el-icon>
                  课程完成
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-green-900 mb-1">
                    {{ studentAnalytics.completedCourses }}/{{ studentAnalytics.totalCourses }}
                  </div>
                  <div class="text-sm text-green-700 bg-green-100 rounded-full px-3 py-0.5 inline-block">
                    证书: {{ studentAnalytics.certificatesEarned }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 积分与成就 -->
            <div class="flex-1">
              <div class="bg-purple-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-purple-800 flex items-center mb-1">
                  <el-icon class="text-purple-600 mr-2"><Medal /></el-icon>
                  积分与成就
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-purple-900 mb-1">
                    {{ studentStore.studentData.points.toLocaleString() }}
                  </div>
                  <div class="text-sm text-purple-700 bg-purple-100 rounded-full px-3 py-0.5 inline-block">
                    连续: {{ studentAnalytics.continuousLearningDays }}天
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人信息管理选项卡 -->
      <el-card shadow="hover">
        <el-tabs v-model="activeTab" tab-position="left" style="min-height: 500px">
          <!-- 基本信息管理 -->
          <el-tab-pane label="基本信息" name="basic">
            <h3 class="text-xl font-medium text-gray-800 mb-4">基本信息</h3>
            <el-form 
              ref="basicInfoForm" 
              :model="basicInfo" 
              label-width="120px" 
              :rules="basicInfoRules"
              status-icon>
              <el-form-item label="姓名" prop="name">
                <el-input v-model="basicInfo.name" disabled />
              </el-form-item>
              <el-form-item label="学号" prop="studentId">
                <el-input v-model="basicInfo.studentId" disabled />
              </el-form-item>
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="basicInfo.idCard" disabled />
              </el-form-item>
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="basicInfo.gender">
                  <el-radio label="male">男</el-radio>
                  <el-radio label="female">女</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="出生日期" prop="birthday">
                <el-date-picker 
                  v-model="basicInfo.birthday" 
                  type="date" 
                  placeholder="选择日期" 
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />
              </el-form-item>
              <el-form-item label="民族" prop="ethnicity">
                <el-select v-model="basicInfo.ethnicity" placeholder="请选择民族" style="width: 100%">
                  <el-option v-for="item in ethnicityOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <el-form-item label="政治面貌" prop="politicalStatus">
                <el-select v-model="basicInfo.politicalStatus" placeholder="请选择政治面貌" style="width: 100%">
                  <el-option v-for="item in politicalStatusOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="basicInfo.phone" placeholder="请输入手机号码" />
              </el-form-item>
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model="basicInfo.email" placeholder="请输入电子邮箱" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveBasicInfo">保存信息</el-button>
                <el-button @click="resetBasicInfo">重置</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 学籍信息 -->
          <el-tab-pane label="学籍信息" name="academic">
            <h3 class="text-xl font-medium text-gray-800 mb-4">学籍信息</h3>
            <el-descriptions border>
              <el-descriptions-item label="所属学院">{{ academicInfo.college }}</el-descriptions-item>
              <el-descriptions-item label="所属专业">{{ academicInfo.major }}</el-descriptions-item>
              <el-descriptions-item label="所属班级">{{ academicInfo.className }}</el-descriptions-item>
              <el-descriptions-item label="学制">{{ academicInfo.educationLength }}年制</el-descriptions-item>
              <el-descriptions-item label="学习形式">{{ academicInfo.studyType }}</el-descriptions-item>
              <el-descriptions-item label="入学日期">{{ academicInfo.enrollmentDate }}</el-descriptions-item>
              <el-descriptions-item label="预计毕业日期">{{ academicInfo.expectedGraduationDate }}</el-descriptions-item>
              <el-descriptions-item label="学籍状态">
                <el-tag type="success">{{ academicInfo.status }}</el-tag>
              </el-descriptions-item>
            </el-descriptions>
            <div class="text-gray-600 text-sm mt-4">
              <el-alert type="info" show-icon>
                学籍信息为只读信息，如有疑问请联系教务管理部门
              </el-alert>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { ElMessage } from 'element-plus'
import {
  Timer,
  Notebook,
  Medal
} from '@element-plus/icons-vue'

// 引入学生数据存储
const studentStore = useStudentStore()

// 当前活动选项卡
const activeTab = ref('basic')

// 基本信息表单引用与数据
const basicInfoForm = ref(null)
const basicInfo = reactive({
  name: studentStore.userFullName,
  studentId: studentStore.studentData.student_id,
  idCard: '330************1234',
  gender: 'male',
  birthday: '1998-01-01',
  ethnicity: '汉族',
  politicalStatus: '共青团员',
  phone: '13800138000',
  email: studentStore.studentData.email,
  avatar: studentStore.studentData.avatar
})

// 民族与政治面貌选项
const ethnicityOptions = [
  '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
  '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族', '黎族', '傈僳族',
  '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族', '景颇族', '柯尔克孜族', '土族',
  '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族', '毛南族', '仡佬族', '锡伯族', '阿昌族', '普米族',
  '塔吉克族', '怒族', '乌孜别克族', '俄罗斯族', '鄂温克族', '德昂族', '保安族', '裕固族', '京族', '塔塔尔族',
  '独龙族', '鄂伦春族', '赫哲族', '门巴族', '珞巴族', '基诺族'
]

const politicalStatusOptions = [
  '中共党员', '中共预备党员', '共青团员', '民革党员', '民盟盟员', '民建会员', 
  '民进会员', '农工党党员', '致公党党员', '九三学社社员', '台盟盟员', '无党派人士', '群众'
]

// 表单验证规则
const basicInfoRules = {
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 学籍信息数据
const academicInfo = reactive({
  college: '计算机科学与技术学院',
  major: '计算机科学与技术',
  className: '计算机2023-2班',
  educationLength: 4,
  studyType: '全日制',
  enrollmentDate: '2023-09-01',
  expectedGraduationDate: '2027-07-01',
  status: '在读'
})

// 学生数据分析
const studentAnalytics = reactive({
  totalLearningHours: 158.5,
  classRank: 3,
  classTotal: 42,
  completedCourses: 5,
  totalCourses: 8,
  certificatesEarned: 3,
  continuousLearningDays: 15,
})

// 方法
const saveBasicInfo = () => {
  basicInfoForm.value.validate((valid) => {
    if (valid) {
      ElMessage.success('基本信息保存成功')
      console.log('Saving basic info:', JSON.parse(JSON.stringify(basicInfo)))
    } else {
      ElMessage.error('请检查表单信息')
    }
  })
}

const resetBasicInfo = () => {
  basicInfo.gender = 'male'
  basicInfo.birthday = '1998-01-01'
  basicInfo.ethnicity = '汉族'
  basicInfo.politicalStatus = '共青团员'
  basicInfo.phone = '13800138000'
  basicInfo.email = studentStore.studentData.email
  basicInfoForm.value.clearValidate()
}

onMounted(() => {
  console.log('个人资料页面已加载')
})
</script>

<style scoped>
.el-descriptions :deep(.el-descriptions-item__label) {
  width: 140px;
}
</style> 