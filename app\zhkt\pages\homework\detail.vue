<template>
  <view class="container">
    <!-- 作业信息 -->
    <view class="homework-info card">
      <text class="title">{{ homeworkInfo.title }}</text>
      <view class="meta">
        <text class="course">{{ homeworkInfo.courseName }}</text>
        <text class="deadline">截止时间：{{ homeworkInfo.deadline }}</text>
      </view>
      <view class="description">{{ homeworkInfo.description }}</view>
      <view class="status-bar">
        <text class="status" :class="homeworkInfo.status">{{ getStatusText(homeworkInfo.status) }}</text>
        <text class="score" v-if="homeworkInfo.score">得分：{{ homeworkInfo.score }}分</text>
      </view>
    </view>
    
    <!-- 作业内容 -->
    <view class="homework-content card">
      <view class="question" v-for="(question, index) in questions" :key="question.id">
        <view class="question-header">
          <text class="question-index">{{ index + 1 }}</text>
          <text class="question-type">{{ question.type }}</text>
          <text class="question-score">({{ question.score }}分)</text>
        </view>
        <view class="question-content">{{ question.content }}</view>
        
        <!-- 选择题选项 -->
        <view class="options" v-if="question.type === '选择题'">
          <view 
            class="option" 
            v-for="option in question.options" 
            :key="option.key"
            :class="{ selected: answers[question.id] === option.key }"
            @click="selectOption(question.id, option.key)"
          >
            <text class="option-key">{{ option.key }}</text>
            <text class="option-content">{{ option.content }}</text>
          </view>
        </view>
        
        <!-- 填空题/简答题答题区域 -->
        <view class="answer-area" v-else>
          <textarea 
            v-model="answers[question.id]" 
            :placeholder="'请输入答案'"
            :disabled="homeworkInfo.status === 'graded'"
          ></textarea>
        </view>
        
        <!-- 批改结果 -->
        <view class="review-result" v-if="homeworkInfo.status === 'graded' && question.review">
          <view class="score-line">
            <text class="label">得分：</text>
            <text class="value">{{ question.review.score }}分</text>
          </view>
          <view class="comment-line" v-if="question.review.comment">
            <text class="label">评语：</text>
            <text class="value">{{ question.review.comment }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar" v-if="homeworkInfo.status === 'pending'">
      <button class="submit-btn" @click="submitHomework" :disabled="submitting">
        {{ submitting ? '提交中...' : '提交作业' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'

// 作业信息
const homeworkInfo = ref({
  id: '',
  title: '',
  courseName: '',
  deadline: '',
  description: '',
  status: 'pending',
  score: null
})

// 题目列表
const questions = ref([])

// 答案
const answers = ref({})

// 提交状态
const submitting = ref(false)

// 获取作业详情
const getHomeworkDetail = async () => {
  try {
    const homeworkId = getQueryParam('id')
    const response = await request({
      url: `/homework/${homeworkId}/`,
      method: 'GET'
    })
    homeworkInfo.value = response
    questions.value = response.questions
    
    // 如果已提交，加载已保存的答案
    if (response.answers) {
      answers.value = response.answers
    }
  } catch (error) {
    uni.showToast({
      title: '获取作业详情失败',
      icon: 'none'
    })
  }
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待完成',
    'submitted': '已提交',
    'graded': '已批改',
    'overdue': '已逾期'
  }
  return statusMap[status] || status
}

// 选择选项
const selectOption = (questionId, optionKey) => {
  if (homeworkInfo.value.status !== 'pending') return
  answers.value[questionId] = optionKey
}

// 提交作业
const submitHomework = async () => {
  try {
    submitting.value = true
    await request({
      url: `/homework/${homeworkInfo.value.id}/submit/`,
      method: 'POST',
      data: { answers: answers.value }
    })
    
    uni.showToast({
      title: '提交成功',
      icon: 'success'
    })
    
    // 刷新作业状态
    getHomeworkDetail()
  } catch (error) {
    uni.showToast({
      title: error.message || '提交失败',
      icon: 'none'
    })
  } finally {
    submitting.value = false
  }
}

// 获取URL参数
const getQueryParam = (name) => {
  const query = window.location.search.substring(1)
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (pair[0] === name) {
      return pair[1]
    }
  }
  return null
}

onMounted(() => {
  getHomeworkDetail()
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.homework-info {
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    
    .course {
      font-size: 28rpx;
      color: #666;
    }
    
    .deadline {
      font-size: 28rpx;
      color: #999;
    }
  }
  
  .description {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
  }
  
  .status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .status {
      font-size: 24rpx;
      padding: 6rpx 20rpx;
      border-radius: 20rpx;
      
      &.pending {
        color: #ff9900;
        background: rgba(255, 153, 0, 0.1);
      }
      
      &.submitted {
        color: #3cc51f;
        background: rgba(60, 197, 31, 0.1);
      }
      
      &.graded {
        color: #007aff;
        background: rgba(0, 122, 255, 0.1);
      }
      
      &.overdue {
        color: #ff3b30;
        background: rgba(255, 59, 48, 0.1);
      }
    }
    
    .score {
      font-size: 32rpx;
      color: #ff9900;
      font-weight: bold;
    }
  }
}

.homework-content {
  .question {
    margin-bottom: 40rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .question-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      .question-index {
        width: 40rpx;
        height: 40rpx;
        background: #3cc51f;
        color: #fff;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      
      .question-type {
        font-size: 28rpx;
        color: #666;
        margin-right: 20rpx;
      }
      
      .question-score {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .question-content {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .options {
      .option {
        display: flex;
        align-items: center;
        padding: 20rpx;
        border: 1rpx solid #eee;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        
        &.selected {
          background: rgba(60, 197, 31, 0.1);
          border-color: #3cc51f;
        }
        
        .option-key {
          width: 40rpx;
          height: 40rpx;
          background: #f5f5f5;
          color: #666;
          font-size: 24rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          margin-right: 20rpx;
        }
        
        .option-content {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
      }
    }
    
    .answer-area {
      textarea {
        width: 100%;
        height: 200rpx;
        background: #f5f5f5;
        border-radius: 8rpx;
        padding: 20rpx;
        font-size: 28rpx;
      }
    }
    
    .review-result {
      margin-top: 20rpx;
      padding-top: 20rpx;
      border-top: 1rpx solid #eee;
      
      .score-line, .comment-line {
        display: flex;
        margin-bottom: 10rpx;
        
        .label {
          font-size: 24rpx;
          color: #666;
          margin-right: 10rpx;
        }
        
        .value {
          font-size: 24rpx;
          color: #333;
        }
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .submit-btn {
    width: 100%;
    height: 80rpx;
    background: #3cc51f;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.5;
    }
  }
}
</style> 