<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="教学设计"
    activePage="content-creation"
    activeSubPage="lesson-plan"
  >
    <div class="space-y-6">
      <div class="flex flex-wrap justify-between items-center gap-4 mb-6">
        
        <!-- 搜索与筛选区域 -->
        <div class="flex flex-wrap md:flex-nowrap items-center gap-3 w-full">
          <!-- 搜索与筛选组件 -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-100 flex-grow">
            <div class="flex flex-col md:flex-row p-4 gap-4">
              <!-- 搜索框区域 -->
              <div class="flex-1 min-w-[300px]">
                <div class="relative flex items-center">
                  <div class="relative flex-1">
                    <input 
                      type="text" 
                      v-model="searchQuery"
                      @input="handleSearchInput"
                      @keyup.enter="loadLessonPlans"
                      placeholder="搜索教学设计..." 
                      class="border border-gray-200 rounded-lg pl-10 pr-10 py-2.5 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                    <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</i>
                    
                    <!-- 加载指示器 -->
                    <div v-if="searchLoading" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                    
                    <!-- 清空按钮 -->
                    <button 
                      v-if="searchQuery && !searchLoading" 
                      @click="clearSearch"
                      class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="清空搜索"
                    >
                      <i class="material-icons text-lg">close</i>
                    </button>
                  </div>
                  
                  <!-- 搜索按钮 -->
                  <button 
                    @click="loadLessonPlans"
                    class="ml-3 px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center shadow-sm transition-colors"
                    :disabled="searchLoading"
                  >
                    <span>搜索</span>
                  </button>
                </div>
              </div>
              
              <!-- 筛选选项 -->
              <div class="flex flex-wrap gap-3 items-center">
                <!-- 学科筛选 -->
                <div class="relative group">
                  <select 
                    v-model="subjectFilter" 
                    @change="handleFilterChange"
                    class="appearance-none bg-gray-50 border border-gray-200 rounded-lg px-4 py-2.5 pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all cursor-pointer"
                  >
                    <option value="all">所有学科</option>
                    <option v-for="subject in subjects" :key="subject.id" :value="subject.id">{{ subject.name }}</option>
                  </select>
                  <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">category</i>
                  <i class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">expand_more</i>
                </div>
                
                <!-- 排序选项 -->
                <div class="relative">
                  <select 
                    v-model="sortOption" 
                    @change="handleFilterChange"
                    class="appearance-none bg-gray-50 border border-gray-200 rounded-lg px-4 py-2.5 pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all cursor-pointer"
                  >
                    <option value="recent">最近更新</option>
                    <option value="oldest">最早创建</option>
                    <option value="az">名称 A-Z</option>
                    <option value="za">名称 Z-A</option>
                  </select>
                  <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">sort</i>
                  <i class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">expand_more</i>
                </div>
                
                <!-- 清空搜索按钮 -->
                <button 
                  v-if="searchQuery" 
                  @click="clearSearch"
                  class="bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg px-4 py-2.5 flex items-center transition-colors"
                >
                  <i class="material-icons mr-1 text-sm">backspace</i>
                  清空搜索
                </button>
              </div>
            </div>
          </div>
          
          <!-- 新建教学设计按钮 -->
          <button 
            class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-2.5 px-6 rounded-lg flex items-center gap-2 shadow-sm transition-all duration-200 transform hover:scale-105 whitespace-nowrap h-[46px]"
            @click="createNewLessonPlan"
          >
            <i class="material-icons text-sm">add</i>
            新建教学设计
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex justify-center items-center h-40 w-full">
        <div class="flex flex-col items-center">
          <svg class="animate-spin h-10 w-10 text-blue-500 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p class="text-gray-500">正在加载教学设计...</p>
        </div>
      </div>

      <!-- 项目列表 -->
      <div v-else-if="paginatedProjects.length > 0" class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                项目名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学科
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                教案模板
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                更新时间
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="project in paginatedProjects" :key="project.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 w-1.5 h-8 rounded-sm" :class="getSubjectColorClass(project.subject?.name)"></div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ project.title }}</div>
                    <div class="text-xs text-gray-500 max-w-md truncate">{{ project.specific_requirements || '无具体要求' }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span :class="`w-6 h-6 rounded-full flex items-center justify-center ${getSubjectBgClass(project.subject?.name)}`">
                    <i class="material-icons text-xs">{{ getSubjectIconClass(project.subject?.name) }}</i>
                  </span>
                  <span class="ml-2 text-sm text-gray-700">{{ project.subject?.name }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ project.lesson_template }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ formatDate(project.updated_at) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  class="text-blue-600 hover:text-blue-900 mr-3" 
                  @click="viewProject(project.id)"
                >
                  查看
                </button>
                <button 
                  class="text-green-600 hover:text-green-900 mr-3" 
                  @click="editProject(project.id)"
                >
                  编辑
                </button>
                <button 
                  class="text-red-600 hover:text-red-900" 
                  @click="deleteProject(project.id)"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="bg-white rounded-lg p-12 shadow-sm border border-gray-100 text-center">
        <div class="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <i class="material-icons text-gray-400 text-2xl">search</i>
        </div>
        <h3 class="text-lg font-medium text-gray-800 mb-2">未找到教学设计</h3>
        <p class="text-gray-600 mb-4">尝试调整搜索条件或创建新的教学设计</p>
        <button 
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
          @click="createNewLessonPlan"
        >
          新建教学设计
        </button>
      </div>

      <!-- 分页控制 -->
      <div v-if="filteredProjects.length > 0" class="mt-5 flex justify-between items-center bg-white rounded-lg p-4 shadow-sm">
        <div class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ startItem }}-{{ endItem }}</span> 条，共 <span class="font-medium">{{ filteredProjects.length }}</span> 条
        </div>
        <div class="flex items-center space-x-2">
          <button 
            class="border border-gray-200 rounded-lg px-3.5 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            :disabled="currentPage === 1"
            @click="currentPage--; loadLessonPlans();"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            <i class="material-icons text-sm">chevron_left</i>
          </button>
          
          <div v-for="pageNumber in displayedPageNumbers" :key="pageNumber" class="hidden md:block">
            <button 
              v-if="pageNumber !== '...'"
              @click="currentPage = pageNumber; loadLessonPlans();"
              class="px-3.5 py-2 rounded-lg text-sm font-medium transition-colors"
              :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'"
            >
              {{ pageNumber }}
            </button>
            <span v-else class="text-gray-500 px-2">...</span>
          </div>
          
          <button 
            class="border border-gray-200 rounded-lg px-3.5 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            :disabled="currentPage === totalPages"
            @click="currentPage++; loadLessonPlans();"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            <i class="material-icons text-sm">chevron_right</i>
          </button>
          
          <div class="flex items-center ml-2">
            <span class="text-sm text-gray-700 mr-2 hidden md:block">前往</span>
            <input 
              type="number" 
              v-model.number="goToPage" 
              min="1" 
              :max="totalPages"
              class="w-14 border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="text-sm text-gray-700 mx-2 hidden md:block">页</span>
            <button 
              @click="jumpToPage"
              class="border border-gray-200 rounded-lg px-3.5 py-2 bg-white text-sm font-medium text-blue-700 hover:bg-blue-50 transition-colors"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-96 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
        <p class="text-gray-700 mb-4">您确定要删除这个教学设计吗？此操作无法撤销。</p>
        <div class="flex justify-end gap-3">
          <button 
            @click="cancelDelete" 
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button 
            @click="confirmDelete" 
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { lessonPlanApi } from '@/api/lessonPlan'
import { subjectApi } from '@/api/subject'
import { debounce } from 'lodash-es'

// Router
const router = useRouter()

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 搜索和筛选
const searchQuery = ref('')
const subjectFilter = ref('all')
const sortOption = ref('recent')

// 学科列表
const subjects = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(9)
const goToPage = ref(1)

// 数据加载状态
const isLoading = ref(false)
const searchLoading = ref(false)
const lessonPlans = ref([])

// 获取学科列表
const loadSubjects = async () => {
  try {
    const response = await subjectApi.getSubjects()
    if (response.data && response.data.length > 0) {
      subjects.value = response.data
    } else {
      console.warn('未获取到学科数据')
    }
  } catch (error) {
    console.error('获取学科列表失败:', error)
  }
}

// 日期格式化
const formatDate = (dateString) => {
  if (!dateString) return '未知日期'
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return dateString
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 防抖的搜索处理函数
const debouncedSearch = debounce(() => {
  currentPage.value = 1
  searchLoading.value = true
  loadLessonPlans()
}, 500)

// 处理搜索框输入
const handleSearchInput = () => {
  searchLoading.value = true
  debouncedSearch()
}

// 清空搜索
const clearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
  loadLessonPlans()
}

// 监听筛选条件变化，重置页码并重新获取数据
const handleFilterChange = () => {
  currentPage.value = 1
  loadLessonPlans()
}

// 加载教学设计列表
const loadLessonPlans = async () => {
  isLoading.value = true
  try {
    // 准备查询参数
    const params = {}
    
    // 添加搜索参数
    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    
    if (subjectFilter.value !== 'all') {
      params.subject_id = subjectFilter.value
    }
    
    // 添加排序参数
    params.sort = sortOption.value
    
    // 添加分页参数
    params.page = currentPage.value
    params.page_size = pageSize.value
    
    // 调用API获取数据
    const response = await lessonPlanApi.getLessonPlans(params)
    
    // 修正数据解析
    if (response.data) {
      lessonPlans.value = response.data.list || []
    } else {
      lessonPlans.value = []
    }
  } catch (error) {
    console.error('获取教学设计列表失败:', error)
    lessonPlans.value = []
  } finally {
    isLoading.value = false
    searchLoading.value = false
  }
}

// 根据筛选条件过滤项目
const filteredProjects = computed(() => {
  let result = lessonPlans.value

  // 排序（客户端排序，API可能已经排序过）
  if (sortOption.value === 'recent') {
    result = [...result].sort((a, b) => new Date(b.updated_at || 0) - new Date(a.updated_at || 0))
  } else if (sortOption.value === 'oldest') {
    result = [...result].sort((a, b) => new Date(a.created_at || 0) - new Date(b.created_at || 0))
  } else if (sortOption.value === 'az') {
    result = [...result].sort((a, b) => a.title.localeCompare(b.title))
  } else if (sortOption.value === 'za') {
    result = [...result].sort((a, b) => b.title.localeCompare(a.title))
  }

  return result
})

// 分页后的项目列表
const paginatedProjects = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredProjects.value.slice(startIndex, startIndex + pageSize.value)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredProjects.value.length / pageSize.value) || 1
})

// 显示项目范围
const startItem = computed(() => {
  return (currentPage.value - 1) * pageSize.value + 1
})

const endItem = computed(() => {
  return Math.min(currentPage.value * pageSize.value, filteredProjects.value.length)
})

// 显示页码
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 跳转到指定页面
const jumpToPage = () => {
  if (goToPage.value >= 1 && goToPage.value <= totalPages.value) {
    currentPage.value = goToPage.value
    loadLessonPlans()
  } else {
    goToPage.value = currentPage.value
  }
}

// 获取学科相关样式
const getSubjectColorClass = (subject) => {
  const subjectColorMap = {
    'Python': 'bg-blue-500',
    '数学': 'bg-green-500',
    '英语': 'bg-yellow-500',
    '电子商务': 'bg-purple-500',
    '人工智能': 'bg-red-500'
  }
  return subjectColorMap[subject] || 'bg-gray-500'
}

const getSubjectBgClass = (subject) => {
  const subjectBgMap = {
    'Python': 'bg-blue-100 text-blue-600',
    '数学': 'bg-green-100 text-green-600',
    '英语': 'bg-yellow-100 text-yellow-600',
    '电子商务': 'bg-purple-100 text-purple-600',
    '人工智能': 'bg-red-100 text-red-600'
  }
  return subjectBgMap[subject] || 'bg-gray-100 text-gray-600'
}

const getSubjectIconClass = (subject) => {
  const subjectIconMap = {
    'Python': 'code',
    '数学': 'calculate',
    '英语': 'translate',
    '电子商务': 'shopping_cart',
    '人工智能': 'smart_toy'
  }
  return subjectIconMap[subject] || 'book'
}

// 删除相关
const showDeleteConfirm = ref(false)
const pendingDeleteId = ref(null)

// 项目操作方法
const createNewLessonPlan = () => {
  router.push('/teacher/lesson-plan/create')
}

const viewProject = (projectId) => {
  router.push(`/teacher/lesson-plan/${projectId}/detail`)
}

const editProject = (projectId) => {
  router.push(`/teacher/lesson-plan/${projectId}/edit`)
}

const deleteProject = (projectId) => {
  pendingDeleteId.value = projectId
  showDeleteConfirm.value = true
}

const cancelDelete = () => {
  pendingDeleteId.value = null
  showDeleteConfirm.value = false
}

const confirmDelete = async () => {
  if (!pendingDeleteId.value) return
  
  try {
    await lessonPlanApi.deleteLessonPlan(pendingDeleteId.value)
    // 删除成功后重新加载数据
    loadLessonPlans()
  } catch (error) {
    console.error('删除教学设计失败:', error)
  } finally {
    showDeleteConfirm.value = false
    pendingDeleteId.value = null
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSubjects()
  loadLessonPlans()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
