export default {
  system: {
    title: 'Kelas Pintar',
    slogan: 'Pengetahuan mengubah takdir, kebijaksanaan menerangi masa depan',
    copyright: '© 2025 Kelas Pintar. Semua hak dilindungi undang-undang.'
  },
  login: {
    title: '<PERSON><PERSON><PERSON>',
    username: '<PERSON><PERSON>an masukkan nama pengguna',
    password: '<PERSON>lakan masukkan kata sandi',
    remember: 'Ingat kata sandi',
    forgot: 'Lupa kata sandi?',
    button: 'Masuk',
    otherMethods: 'Metode login lainnya',
    noAccount: 'Belum punya akun?',
    register: 'Daftar sekarang'
  },
  role: {
    student: '<PERSON><PERSON><PERSON>',
    teacher: '<PERSON>',
    admin: 'Administrator'
  },
  errors: {
    loginFailed: '<PERSON>l masuk, silakan coba lagi!',
    wrongCredentials: '<PERSON>a pengguna atau kata sandi salah, silakan coba lagi!',
    roleError: '<PERSON><PERSON><PERSON> peran pengguna, silakan coba lagi!',
    unknown: '<PERSON><PERSON>ahan tidak diketahui, silakan coba lagi nanti!',
    loadCoursesListFailed: 'Gagal memuat daftar kursus',
    fetchCoursesListFailed: 'Gagal mengambil daftar kursus',
    fetchLearningStatsFailed: 'Gagal mengambil statistik pembelajaran',
    fetchStudentInfoFailed: 'Gagal mengambil informasi siswa',
    joinClassFailed: 'Gagal bergabung dengan kelas'
  },
  dashboard: {
    student: {
      title: 'Dasbor Siswa',
      welcome: 'Selamat datang kembali, {name}',
      today: 'Hari ini adalah {date}',
      learningStatus: {
        continuousLearning: 'Belajar berturut-turut selama {days} hari',
        weeklyLearning: '{hours} jam minggu ini'
      },
      learningGoal: {
        title: 'Target Belajar',
        remainingCourses: 'Perlu menyelesaikan {count} kursus lagi untuk mencapai level berikutnya'
      },
      stats: {
        courseCompletion: {
          title: 'Penyelesaian Kursus',
          totalCourses: 'Menyelesaikan {completed} dari {total} kursus',
          monthlyChange: '↑{percent}% dari bulan lalu'
        },
        homeworkCompletion: {
          title: 'Penyelesaian Tugas',
          totalHomeworks: 'Menyelesaikan {completed} dari {total} tugas',
          averageScore: 'Nilai rata-rata: {score}'
        },
        learningHours: {
          title: 'Jam Belajar',
          hours: '{hours} jam',
          increase: '↑ {hours} jam',
          weekday: {
            monday: 'Sen',
            sunday: 'Min'
          }
        },
        points: {
          title: 'Poin & Prestasi',
          points: '{points} poin',
          increase: '↑ {points}',
          medalLevel: {
            bronze: 'Siswa Perunggu',
            silver: 'Siswa Perak',
            gold: 'Siswa Emas'
          },
          nextLevel: '{points} poin untuk {level}'
        }
      },
      recommendations: {
        title: 'Rekomendasi Pembelajaran Pribadi',
        efficiency: 'Efisiensi Pembelajaran',
        reinforcement: 'Penguatan Pengetahuan',
        habit: 'Kebiasaan Belajar'
      }
    }
  },
  courses: {
    title: 'Kursus Saya',
    joinClass: 'Gabung Kelas',
    points: 'Poin',
    
    courseStats: {
      total: 'Semua Kursus',
      completed: 'Selesai',
      inProgress: 'Sedang Berlangsung',
      weeklyHours: 'Belajar Mingguan'
    },
    
    joinClassModal: {
      title: 'Gabung Kelas',
      enterCode: 'Silakan masukkan kode undangan kelas',
      codePlaceholder: 'Masukkan kode 6 digit',
      joining: 'Bergabung...',
      confirm: 'Konfirmasi Gabung',
      success: 'Berhasil bergabung dengan kelas',
      failed: 'Gagal bergabung dengan kelas',
      failedWithError: 'Gagal bergabung dengan kelas: {error}'
    },
    
    coursesList: {
      title: 'Semua Kursus',
      teacher: 'Pengajar',
      hours: 'Jam',
      students: '{count} siswa terdaftar',
      continueLearning: 'Lanjutkan Belajar →',
      review: 'Tinjau →'
    },
    
    pagination: {
      previous: 'Sebelumnya',
      next: 'Berikutnya'
    },
    
    notes: {
      button: 'Catatan',
      title: '{course} - Catatan',
      listTitle: 'Daftar Catatan',
      count: '{count} catatan',
      searchPlaceholder: 'Cari catatan...',
      loading: 'Memuat daftar catatan...',
      loadingContent: 'Memuat konten catatan...',
      empty: 'Tidak ada catatan tersedia',
      untitled: 'Catatan Tanpa Judul',
      createdAt: 'Dibuat pada',
      loadFailed: 'Gagal memuat daftar catatan',
      loadDetailFailed: 'Gagal memuat detail catatan'
    }
  },
  
  courseContent: {
    videoPlayerNotSupported: 'Browser Anda tidak mendukung pemutar video HTML5',
    
    tabs: {
      overview: 'Ringkasan Kursus',
      discussion: 'Diskusi'
    },
    
    controls: {
      notes: 'Catatan',
      navigation: 'Navigasi',
      rating: 'Penilaian'
    },
    
    stats: {
      totalHours: 'Total Jam',
      hour: 'jam',
      studentsCount: 'Siswa',
      rating: 'Penilaian',
      ratingCount: 'penilaian'
    },
    
    comments: {
      title: 'Diskusi',
      placeholder: 'Bagikan pendapat Anda...',
      submit: 'Kirim Komentar',
      reply: 'Balas',
      replyPlaceholder: 'Balas...',
      submitReply: 'Kirim Balasan',
      instructor: 'Pengajar'
    },
    
    sidebar: {
      progress: 'Kemajuan',
      lessons: 'Pelajaran',
      lessonsUnit: 'pelajaran'
    },
    
    aiCompanion: {
      title: 'Asisten Belajar AI',
      subtitle: 'Siap menjawab pertanyaan Anda',
      inputPlaceholder: 'Ketik pertanyaan Anda di sini...',
      selectCompanion: 'Pilih Pendamping',
      close: 'Tutup'
    },
    
    aiCompanionDialog: {
      title: 'Pilih Teman Belajar Anda',
      requiredRoles: 'Peran Wajib',
      requiredRolesNote: '(Guru Wang dipilih secara otomatis)',
      companionRoles: 'Peran Pendamping',
      optionalRolesNote: '(Bebas untuk dipilih)',
      confirm: 'Konfirmasi Pilihan',
      roles: {
        teacher: {
          name: 'Guru Wang',
          desc: 'Asisten pendukung pengajaran'
        },
        funnyKid: {
          name: 'Xiao Ming (Badut Kelas)',
          desc: 'Ceria dan humoris, suka bercanda'
        },
        thinker: {
          name: 'Li Hua (Pemikir Dalam)',
          desc: 'Penuh pemikiran dan analitis'
        },
        curious: {
          name: 'Zhang Ying (Pikiran Ingin Tahu)',
          desc: 'Suka bertanya, menyoroti konsep'
        },
        topStudent: {
          name: 'Zhao Yang (Siswa Terbaik)',
          desc: 'Mengorganisir poin-poin utama dengan singkat'
        }
      }
    },
    
    noteModal: {
      title: 'Catatan Kursus',
      search: 'Cari catatan...',
      new: 'Catatan Baru',
      titlePlaceholder: 'Judul Catatan',
      contentPlaceholder: 'Masukkan konten catatan di sini...',
      timePoint: 'Waktu',
      save: 'Simpan Catatan',
      cancel: 'Batal',
      delete: 'Hapus Catatan',
      markdownSupport: 'Markdown didukung',
      charCount: '{count} karakter',
      confirmDelete: 'Apakah Anda yakin ingin menghapus catatan ini?',
      // Pesan notifikasi
      titleRequired: 'Silakan masukkan judul catatan',
      updateSuccess: 'Catatan berhasil diperbarui',
      createSuccess: 'Catatan berhasil dibuat',
      saveFailed: 'Gagal menyimpan catatan',
      loadFailed: 'Gagal memuat catatan',
      deleteSuccess: 'Catatan berhasil dihapus',
      deleteFailed: 'Gagal menghapus catatan',
      courseInfoError: 'Tidak dapat memperoleh informasi kursus'
    },
    
    videoNavModal: {
      title: 'Navigasi Video AI',
      summary: 'Ringkasan Konten Video',
      noSummary: 'Tidak ada ringkasan video',
      keyPoints: 'Navigasi Poin Penting',
      noKeyPoints: 'Tidak ada data poin penting'
    },
    
    ratingDialog: {
      title: 'Penilaian Kursus',
      submit: 'Kirim Penilaian',
      cancel: 'Batal'
    }
  },
  
  courseOverview: {
    keyPoints: 'Poin Utama Kursus',
    importantPoints: 'Poin Penting',
    notes: 'Catatan',
    noContent: 'Tidak ada konten',
    edit: 'Edit Ringkasan Kursus',
    enterKeyPoints: 'Masukkan poin utama kursus',
    enterImportantPoints: 'Masukkan poin penting',
    enterNotes: 'Masukkan catatan',
    updateSuccess: 'Ringkasan kursus berhasil diperbarui',
    saveFailed: 'Gagal menyimpan'
  },
  general: {
    confirm: 'Konfirmasi',
    cancel: 'Batal',
    confirmation: 'Konfirmasi',
    error: 'Terjadi kesalahan',
    uploadFailed: 'Gagal mengunggah',
    pleaseWait: 'Mohon tunggu...',
    defaultStudentName: 'Siswa',
    home: 'Beranda',
    points: 'Poin',
    save: 'Simpan'
  },
  
  userHeader: {
    profile: 'Profil',
    settings: 'Pengaturan Akun',
    activate: 'Aktivasi Akun',
    logout: 'Keluar',
    logoutFailed: 'Gagal keluar',
    verification: {
      title: 'Aktivasi Akun',
      alertTitle: 'Silakan Selesaikan Verifikasi',
      successTitle: 'Verifikasi Selesai',
      teacherDescription: 'Sebagai guru, aktivasi memungkinkan lebih banyak fitur. Silakan masukkan informasi asli Anda untuk verifikasi.',
      studentDescription: 'Aktivasi akun memungkinkan lebih banyak fitur. Silakan masukkan informasi asli Anda untuk verifikasi.',
      teacherSuccess: 'Akun guru Anda telah diverifikasi dan diaktifkan',
      studentSuccess: 'Akun Anda telah diverifikasi dan diaktifkan',
      step1: 'Masukkan Informasi',
      step2: 'Verifikasi Telepon',
      step3: 'Selesai',
      name: 'Nama',
      idCard: 'KTP',
      phone: 'Telepon',
      code: 'Kode',
      namePlaceholder: 'Masukkan nama asli Anda',
      idCardPlaceholder: 'Masukkan nomor KTP Anda',
      phonePlaceholder: 'Masukkan nomor telepon Anda',
      codePlaceholder: 'Masukkan kode verifikasi',
      nameRequired: 'Silakan masukkan nama asli Anda',
      nameLength: 'Nama harus 2-20 karakter',
      idCardRequired: 'Silakan masukkan nomor KTP Anda',
      idCardFormat: 'Silakan masukkan nomor KTP yang valid',
      phoneRequired: 'Silakan masukkan nomor telepon Anda',
      phoneFormat: 'Silakan masukkan nomor telepon yang valid',
      codeRequired: 'Silakan masukkan kode verifikasi',
      codeFormat: 'Kode harus 6 digit',
      next: 'Selanjutnya',
      previous: 'Sebelumnya',
      modify: 'Edit',
      getCode: 'Dapatkan Kode',
      resend: 'Kirim ulang dalam {seconds}d',
      verify: 'Verifikasi',
      complete: 'Selesai',
      codeSentSuccess: 'Kode dikirim ke {phone}',
      codeSendFailed: 'Gagal mengirim kode, silakan coba lagi nanti',
      verificationFailed: 'Verifikasi gagal, silakan periksa informasi Anda',
      enterCorrectCode: 'Silakan masukkan kode yang benar',
      fillRequiredFields: 'Silakan isi semua bidang yang diperlukan dengan benar',
      verifying: 'Memverifikasi ',
      threeElements: ' informasi dan kode',
      sendCodeTo: 'Mengirim kode ke'
    }
  },

  breadcrumb: {
    adminDashboard: 'Dasbor',
    teacherDashboard: 'Dasbor Guru',
    studentDashboard: 'Dasbor Siswa',
    myCourses: 'Kursus Saya',
    courseContent: 'Konten Kursus',
    myAssignments: 'Tugas Saya',
    assignmentManagement: 'Manajemen Tugas',
    courseManagement: 'Manajemen Kursus',
    studentManagement: 'Manajemen Siswa',
    classManagement: 'Manajemen Kelas',
    gradeManagement: 'Manajemen Nilai',
    questionBank: 'Bank Soal',
    textbookProjects: 'Proyek Buku Teks',
    contentCreation: 'Alat',
    lessonPlanProjects: 'Rencana Pelajaran',
    contentPptProjects: 'Pembuatan PPT',
    contentScriptProjects: 'Konten Naskah',
    contentVideoProjects: 'Sintesis Video',
    videoManagement: 'Manajemen Video',
    digitalTeacher: 'Guru Digital',
    aiVoice: 'Suara AI',
    aiLecture: 'Kuliah AI',
    aiLearning: 'Pembelajaran AI',
    bookshelf: 'Rak Buku',
    systemManagement: 'Manajemen Sistem',
    userManagement: 'Manajemen Pengguna',
    storeManagement: 'Manajemen Toko',
    pointsManagement: 'Manajemen Poin',
    notificationManagement: 'Manajemen Notifikasi',
    auditLogs: 'Log Audit',
    knowledgeBase: 'Basis Pengetahuan',
    personalCenter: 'Pusat Pribadi',
    profile: 'Profil',
    aiAssistant: 'Asisten AI',
    pointsMall: 'Mal Poin',
    settings: 'Pengaturan',
    chapter: 'Bab',
    class: 'Kelas',
    homework: 'Tugas',
    grades: 'Nilai',
    dograde: 'Nilai Tugas',
    grading: 'Detail Penilaian'
  },
  
  sidebar: {
    home: 'Beranda',
    aiLecture: 'Kuliah AI',
    bookshelf: 'Rak Buku',
    courses: 'Kursus Saya',
    assignments: 'Tugas Saya',
    knowledgeBase: 'Basis Pengetahuan',
    aiAssistant: 'Asisten AI',
    pointsMall: 'Mal Poin',
    personalCenter: 'Pusat Pribadi',
    collapse: 'Ciutkan Menu'
  },
  
  todayLearn: {
    title: 'Kuliah AI',
    upload: {
      area: 'Seret berkas ke sini, atau',
      button: 'Klik untuk mengunggah',
      supportTypes: 'Jenis berkas yang didukung: .doc, .docx, .txt, .pdf',
      dropHint: 'Lepaskan untuk mengunggah berkas',
      uploading: 'Mengunggah...'
    },
    styleDialog: {
      title: 'Pilih Gaya Kuliah',
      description: 'Silakan pilih gaya kuliah untuk dokumen yang Anda unggah:',
      cancel: 'Batalkan Unggahan',
      confirm: 'Konfirmasi dan Unggah'
    }
  },
  
  bookshelf: {
    title: 'Rak Buku',
    stats: '{count} Dokumen',
    slogan: 'Ciptakan kembali pengalaman membaca Anda',
    search: 'Cari dokumen...',
    generating: '{count} dokumen sedang dibuat',
    batchOperation: 'Operasi Batch',
    exitBatch: 'Keluar Batch',
    selectAll: 'Pilih Semua',
    cancelSelect: 'Batalkan Pilihan',
    delete: 'Hapus',
    outlineCompleted: 'Pembuatan kerangka selesai',
    processingTime: 'Perkiraan 10-15 menit',
    document: 'Dokumen',
    
    addDocument: {
      title: 'Tambah Dokumen',
      dropHint: 'Letakkan berkas di sini',
      supportDrag: 'Mendukung tarik & letakkan'
    },
    
    upload: {
      uploading: 'Mengunggah...',
      deleting: 'Menghapus...',
      completed: 'Selesai',
      pleaseWait: 'Mohon tunggu...',
      progress: 'Selesai {completed} / {total}'
    },
    
    documentCard: {
      editName: 'Edit Nama',
      delete: 'Hapus',
      moreActions: 'Tindakan Lain'
    },
    
    editNameDialog: {
      title: 'Edit Nama Dokumen',
      placeholder: 'Masukkan nama dokumen',
      confirm: 'Konfirmasi',
      cancel: 'Batal'
    },
    
    styleDialog: {
      title: 'Pilih Gaya Kuliah',
      description: 'Silakan pilih gaya yang ingin Anda gunakan AI untuk dokumen ini',
      selectStyle: 'Pilih gaya kuliah:',
      confirm: 'Konfirmasi',
      cancel: 'Batal',
      cancelUpload: 'Batalkan Unggahan',
      confirmAndUpload: 'Konfirmasi & Unggah'
    },
    
    preview: {
      generating: 'Kerangka sedang dibuat, silakan coba lagi nanti',
      failed: 'Gagal memuat pratinjau'
    },
    
    notifications: {
      nameUpdated: 'Nama dokumen diperbarui',
      deleteConfirm: 'Apakah Anda yakin ingin menghapus {name}?',
      deleteFailed: 'Gagal menghapus',
      fileTypeError: 'Jenis berkas tidak didukung',
      uploadSuccess: '{name} berhasil diunggah',
      generatingOutline: 'Membuat kerangka, ini mungkin memerlukan beberapa menit',
      selectDocumentsToDelete: 'Silakan pilih dokumen untuk dihapus',
      batchDeleteConfirm: 'Apakah Anda yakin ingin menghapus {count} dokumen yang dipilih?',
      batchDeleteSuccess: 'Berhasil menghapus {count} dokumen',
      batchDeleteFailed: 'Penghapusan batch gagal',
      confirmDelete: 'Apakah Anda yakin ingin menghapus {name}?',
      deleteConfirmTitle: 'Konfirmasi Hapus',
      confirmButtonText: 'Konfirmasi',
      cancelButtonText: 'Batal',
      deleteSuccess: 'Penghapusan berhasil',
      fileTypesHint: 'Hanya berkas PDF dan DOCX yang didukung',
      confirmBatchDelete: 'Apakah Anda yakin ingin menghapus {count} dokumen yang dipilih?',
      batchDeleteConfirmTitle: 'Konfirmasi Hapus Batch'
    }
  },
  
  htmlPreview: {
    actions: {
      chapterList: 'Daftar Bab',
      switchStyle: 'Ganti Gaya',
      back: 'Kembali ke Rak Buku'
    },
    chapterDialog: {
      title: 'Daftar Bab',
      noChapters: 'Tidak ada bab yang tersedia',
      page: 'hal. {start}-{end}'
    },
    styleDialog: {
      title: 'Pilih Gaya Kuliah',
      currentStyle: 'Gaya Saat Ini:',
      loading: 'Memuat daftar gaya...',
      cancel: 'Batal',
      confirm: 'Konfirmasi Perubahan',
      switching: 'Mengganti...'
    },
    status: {
      loading: 'Memuat...',
      generating: 'Menghasilkan konten bab...',
      pointsGenerated: 'Telah menghasilkan {count} poin kunci',
      completed: 'Pembuatan selesai!',
      notGenerated: 'Konten bab belum dihasilkan',
      startGenerate: 'Mulai Menghasilkan',
      loadFailed: 'Gagal memuat'
    },
    controls: {
      play: 'Putar',
      pause: 'Jeda',
      noAudio: 'Tidak Ada Audio',
      generating: 'Menghasilkan audio...',
      prev: 'Sebelumnya',
      next: 'Berikutnya',
      page: 'Halaman {current} / {total}',
      generating: 'Konten sedang dihasilkan...'
    },
    notifications: {
      styleChanged: 'Gaya berhasil diubah',
      styleChangedDetail: 'Beralih ke {style}, halaman akan dimuat ulang, audio akan dihasilkan di latar belakang',
      styleChangeFailed: 'Perubahan gaya gagal',
      retryLater: 'Silakan coba lagi nanti',
      audioUpdated: 'Audio diperbarui',
      audioUpdateDetail: 'Audio untuk gaya baru telah dihasilkan',
      generationStarted: 'Pembuatan dimulai',
      generationDetail: 'Pembuatan konten bab telah dimulai, harap tunggu...',
      generationFailed: 'Pembuatan gagal',
      triggerFailed: 'Gagal memulai pembuatan bab',
      autoPaging: 'Halaman otomatis',
      pagingMessage: 'Beralih ke halaman {page} dan melanjutkan pemutaran...',
      playCompleted: 'Pemutaran selesai',
      playCompletedDetail: 'Semua konten telah diputar',
      firstPointGenerated: 'Poin kunci pertama dihasilkan',
      firstPointDetail: 'Pratinjau sekarang tersedia, konten lainnya akan dihasilkan di latar belakang',
      generationCompleted: 'Pembuatan selesai',
      allContentGenerated: 'Semua konten bab telah dihasilkan',
      outlineCompleted: 'Pembuatan kerangka {names} selesai!'
    },
    status: {
      notGenerated: 'Belum dihasilkan',
      generating: 'Sedang menghasilkan',
      completed: 'Selesai'
    }
  }
} 