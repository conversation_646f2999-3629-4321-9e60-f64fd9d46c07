<template>
  <AdminLayout 
    pageTitle="课程管理" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <!-- 操作按钮 -->
        <div class="mt-4 flex justify-between">
          <div class="flex space-x-3">
            <button type="button" @click="showAddCourseModal = true" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <span class="material-icons mr-2">add</span>
              添加课程
            </button>
            <button type="button" @click="handleBatchImport" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <span class="material-icons mr-2">upload_file</span>
              批量导入
            </button>
            <button type="button" @click="handleExportData" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <span class="material-icons mr-2">download</span>
              导出数据
            </button>
          </div>
          <div class="flex space-x-3">
            <div class="relative">
              <select v-model="selectedCollege" class="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <option value="">所有学院</option>
                <option v-for="college in colleges" :key="college.id" :value="college.id">
                  {{ college.name }}
                </option>
              </select>
            </div>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="material-icons text-gray-400">search</span>
              </div>
              <input type="text" v-model="searchQuery" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="搜索课程...">
            </div>
          </div>
        </div>

        <!-- 课程卡片网格 -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="course in filteredCourses" :key="course.id" class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0 rounded-md p-3" :class="getSubjectColor(course.subject)">
                  <span class="material-icons text-white text-xl">{{ getSubjectIcon(course.subject) }}</span>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <h3 class="text-lg font-medium text-gray-900 truncate">{{ course.name }}</h3>
                  <div class="mt-1 flex items-center text-sm text-gray-500">
                    <span class="truncate">{{ course.college }}</span>
                  </div>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getStatusClass(course.status)">
                    {{ getStatusText(course.status) }}
                  </span>
                </div>
              </div>
              <div class="mt-4">
                <div class="flex justify-between">
                  <div class="text-sm text-gray-500">
                    <span class="material-icons text-sm align-middle mr-1">person</span> {{ course.teacher }}
                  </div>
                  <div class="text-sm text-gray-500">
                    <span class="material-icons text-sm align-middle mr-1">group</span> {{ course.studentCount }} 名学生
                  </div>
                </div>
                <div class="mt-2 flex justify-between">
                  <div class="text-sm text-gray-500">
                    <span class="material-icons text-sm align-middle mr-1">calendar_today</span> {{ course.startDate }}
                  </div>
                  <div class="text-sm text-gray-500">
                    <span class="material-icons text-sm align-middle mr-1">schedule</span> {{ course.duration }}周
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-5 py-3 flex items-center justify-between">
              <div>
                <a href="#" @click.prevent="viewCourseDetails(course)" class="text-sm text-indigo-600 hover:text-indigo-900">查看详情</a>
                <span class="mx-1">·</span>
                <a href="#" @click.prevent="editCourse(course)" class="text-sm text-gray-500 hover:text-gray-900">
                  {{ course.status === 'ended' ? '归档' : '编辑' }}
                </a>
              </div>
              <div class="text-sm">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                  {{ course.classCount }}个班级
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页控件 -->
        <div class="mt-6 flex justify-between items-center">
          <div class="text-sm text-gray-700">
            显示 <span class="font-medium">{{ paginationStart }}</span> 至 
            <span class="font-medium">{{ paginationEnd }}</span> 条，共 
            <span class="font-medium">{{ totalCourses }}</span> 条结果
          </div>
          <div class="flex-1 flex justify-end">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <a href="#" @click.prevent="prevPage" :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">上一页</span>
                <span class="material-icons text-sm">chevron_left</span>
              </a>
              <template v-for="page in displayedPages" :key="page">
                <span v-if="page === '...'" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                  ...
                </span>
                <a v-else href="#" @click.prevent="goToPage(page)" :class="{ 'bg-indigo-50 text-indigo-600': currentPage === page }" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                  {{ page }}
                </a>
              </template>
              <a href="#" @click.prevent="nextPage" :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <span class="sr-only">下一页</span>
                <span class="material-icons text-sm">chevron_right</span>
              </a>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Course Modal -->
    <AddCourseModal 
      :show="showAddCourseModal"
      :colleges="colleges"
      @close="showAddCourseModal = false"
      @submit="handleAddCourse"
    />

    <!-- Course Detail Modal -->
    <CourseDetailModal
      :show="showDetailModal"
      :course="selectedCourse"
      @close="showDetailModal = false"
      @edit="handleEditFromDetail"
      @archive="handleArchiveCourse"
    />

    <!-- Course Edit Modal -->
    <CourseEditModal
      :show="showEditModal"
      :course="selectedCourse"
      :colleges="colleges"
      @close="showEditModal = false"
      @submit="handleUpdateCourse"
    />
  </AdminLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import AddCourseModal from '@/components/modals/AddCourseModal.vue'
import CourseDetailModal from '@/components/modals/CourseDetailModal.vue'
import CourseEditModal from '@/components/modals/CourseEditModal.vue'
import { useAdminStore } from '@/stores/admin'

const router = useRouter()
const adminStore = useAdminStore()

// 状态变量
const searchQuery = ref('')
const selectedCollege = ref('')
const currentPage = ref(1)
const itemsPerPage = 6
const showAddCourseModal = ref(false)
const showDetailModal = ref(false)
const showEditModal = ref(false)
const selectedCourse = ref(null)

// 模拟数据
const colleges = ref([
  { id: 'math', name: '数学学院' },
  { id: 'cs', name: '计算机学院' },
  { id: 'physics', name: '物理学院' },
  { id: 'chemistry', name: '化学学院' }
])

const courses = ref([
  {
    id: 1,
    name: '高等数学 I',
    college: '数学学院',
    subject: 'math',
    teacher: '王教授',
    studentCount: 210,
    startDate: '2023-02-20',
    duration: 16,
    status: 'active',
    classCount: 3
  },
  {
    id: 2,
    name: '程序设计基础',
    college: '计算机学院',
    subject: 'programming',
    teacher: '张教授',
    studentCount: 185,
    startDate: '2023-02-20',
    duration: 14,
    status: 'active',
    classCount: 2
  },
  {
    id: 3,
    name: '大学物理',
    college: '物理学院',
    subject: 'physics',
    teacher: '刘教授',
    studentCount: 158,
    startDate: '2023-02-20',
    duration: 16,
    status: 'active',
    classCount: 4
  },
  {
    id: 4,
    name: '无机化学',
    college: '化学学院',
    subject: 'chemistry',
    teacher: '陈教授',
    studentCount: 92,
    startDate: '2023-09-01',
    duration: 12,
    status: 'planned',
    classCount: 2
  },
  {
    id: 5,
    name: '高等数学 II',
    college: '数学学院',
    subject: 'math',
    teacher: '王教授',
    studentCount: 0,
    startDate: '2023-09-01',
    duration: 16,
    status: 'planned',
    classCount: 3
  },
  {
    id: 6,
    name: '数据库系统原理',
    college: '计算机学院',
    subject: 'database',
    teacher: '李教授',
    studentCount: 124,
    startDate: '2022-09-01',
    duration: 14,
    status: 'ended',
    classCount: 2
  }
])

// 计算属性
const filteredCourses = computed(() => {
  let filtered = [...courses.value]
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(course => 
      course.name.toLowerCase().includes(query) ||
      course.teacher.toLowerCase().includes(query)
    )
  }
  
  if (selectedCollege.value) {
    filtered = filtered.filter(course => 
      course.college === colleges.value.find(c => c.id === selectedCollege.value)?.name
    )
  }
  
  const start = (currentPage.value - 1) * itemsPerPage
  return filtered.slice(start, start + itemsPerPage)
})

const totalCourses = computed(() => courses.value.length)
const totalPages = computed(() => Math.ceil(totalCourses.value / itemsPerPage))
const paginationStart = computed(() => (currentPage.value - 1) * itemsPerPage + 1)
const paginationEnd = computed(() => Math.min(currentPage.value * itemsPerPage, totalCourses.value))

const displayedPages = computed(() => {
  const pages = []
  const maxDisplayed = 5
  
  if (totalPages.value <= maxDisplayed) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1)
    if (currentPage.value > 3) pages.push('...')
    
    for (let i = Math.max(2, currentPage.value - 1); i <= Math.min(totalPages.value - 1, currentPage.value + 1); i++) {
      pages.push(i)
    }
    
    if (currentPage.value < totalPages.value - 2) pages.push('...')
    pages.push(totalPages.value)
  }
  
  return pages
})

// 方法
const getSubjectColor = (subject) => {
  const colors = {
    math: 'bg-indigo-600',
    programming: 'bg-purple-600',
    physics: 'bg-green-600',
    chemistry: 'bg-red-600',
    database: 'bg-gray-600'
  }
  return colors[subject] || 'bg-blue-600'
}

const getSubjectIcon = (subject) => {
  const icons = {
    math: 'calculate',
    programming: 'code',
    physics: 'science',
    chemistry: 'science',
    database: 'storage'
  }
  return icons[subject] || 'book'
}

const getStatusClass = (status) => {
  const classes = {
    active: 'bg-green-100 text-green-800',
    planned: 'bg-yellow-100 text-yellow-800',
    ended: 'bg-gray-100 text-gray-800'
  }
  return classes[status]
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    planned: '计划中',
    ended: '已结束'
  }
  return texts[status]
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const goToPage = (page) => {
  currentPage.value = page
}

const viewCourseDetails = (course) => {
  selectedCourse.value = course
  showDetailModal.value = true
}

const editCourse = (course) => {
  if (course.status === 'ended') {
    handleArchiveCourse(course)
  } else {
    selectedCourse.value = course
    showEditModal.value = true
  }
}

const handleBatchImport = () => {
  // 实现批量导入逻辑
  console.log('Batch import')
}

const handleExportData = () => {
  // 实现导出数据逻辑
  console.log('Export data')
}

const handleAddCourse = (courseData) => {
  // Generate a new ID (in a real app this would come from the backend)
  const newId = Math.max(...courses.value.map(c => c.id)) + 1
  
  // Add the new course
  courses.value.push({
    id: newId,
    name: courseData.name,
    college: colleges.value.find(c => c.id === courseData.college)?.name || '',
    subject: courseData.subject,
    teacher: courseData.teacher,
    studentCount: 0,
    startDate: courseData.startDate,
    duration: courseData.duration,
    status: courseData.status,
    classCount: courseData.classCount
  })
  
  // Close the modal
  showAddCourseModal.value = false
}

const handleEditFromDetail = () => {
  showDetailModal.value = false
  showEditModal.value = true
}

const handleArchiveCourse = () => {
  if (selectedCourse.value) {
    const index = courses.value.findIndex(c => c.id === selectedCourse.value.id)
    if (index !== -1) {
      courses.value[index] = {
        ...selectedCourse.value,
        status: 'ended'
      }
    }
  }
  showDetailModal.value = false
}

const handleUpdateCourse = (updatedCourse) => {
  const index = courses.value.findIndex(c => c.id === updatedCourse.id)
  if (index !== -1) {
    courses.value[index] = updatedCourse
  }
  showEditModal.value = false
}
</script>

<style scoped>
/* 添加任何必要的样式 */
</style> 