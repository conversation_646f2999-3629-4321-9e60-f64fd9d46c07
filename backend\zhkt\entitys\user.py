from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _

class Role(models.Model):
    """角色模型"""
    name = models.CharField(max_length=50, verbose_name='角色名称')
    code = models.CharField(max_length=50, unique=True, verbose_name='角色代码')
    description = models.TextField(blank=True, null=True, verbose_name='角色描述')
    menus = models.ManyToManyField('Menu', blank=True, verbose_name='菜单权限')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    class Meta:
        verbose_name = '角色'
        verbose_name_plural = verbose_name
        ordering = ['id']

    def __str__(self):
        return self.name

class User(AbstractUser):
    """用户模型"""
    alias = models.CharField(max_length=50, null=True, verbose_name='别名')
    phone = models.CharField(max_length=11, blank=True, null=True, verbose_name='手机号')
    gender = models.CharField(max_length=10, blank=True, null=True, verbose_name='性别')
    avatar = models.CharField(_('头像'), max_length=500, null=True, blank=True)
    roles = models.ManyToManyField(Role, blank=True, verbose_name='角色')
    dept = models.ForeignKey('Dept', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='所属机构')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = verbose_name
        ordering = ['id']

    def __str__(self):
        return self.username

class Student(models.Model):
    """学生模型"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='student_profile')
    student_id = models.CharField(_('学号'), max_length=20, unique=True)
    college = models.ForeignKey('College', on_delete=models.SET_NULL, null=True, related_name='students')
    major = models.ForeignKey('Major', on_delete=models.SET_NULL, null=True, related_name='students')
    classes = models.ManyToManyField('ClassGroup', related_name='students', verbose_name=_('班级'))
    enrollment_date = models.DateField(_('入学日期'), null=True, blank=True)
    expected_graduation_date = models.DateField(_('预计毕业日期'), null=True, blank=True)
    status = models.CharField(_('学籍状态'), max_length=20, default='在读')
    points = models.IntegerField(_('积分'), default=0)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    
    class Meta:
        verbose_name = _('学生')
        verbose_name_plural = _('学生')

class Teacher(models.Model):
    """教师模型"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='teacher_profile')
    teacher_id = models.CharField(_('教师编号'), max_length=20, unique=True)
    title = models.CharField(_('职称'), max_length=50)
    college = models.ForeignKey('College', on_delete=models.SET_NULL, null=True, related_name='teachers')
    majors = models.ManyToManyField('Major', related_name='teachers')
    introduction = models.TextField(_('个人简介'), blank=True)
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    
    class Meta:
        verbose_name = _('教师')
        verbose_name_plural = _('教师')

class Admin(models.Model):
    """管理员模型"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='admin_profile')
    admin_id = models.CharField(_('管理员编号'), max_length=20, unique=True)
    department = models.CharField(_('所属部门'), max_length=50)
    role = models.CharField(_('角色'), max_length=50)
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    
    class Meta:
        verbose_name = _('管理员')
        verbose_name_plural = _('管理员') 