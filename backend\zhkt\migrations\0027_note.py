# Generated by Django 3.2.20 on 2025-05-08 17:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0026_auto_20250508_1547'),
    ]

    operations = [
        migrations.CreateModel(
            name='Note',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='笔记标题')),
                ('content', models.TextField(verbose_name='笔记内容')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='zhkt.student')),
            ],
            options={
                'verbose_name': '学习笔记',
                'verbose_name_plural': '学习笔记',
            },
        ),
    ]
