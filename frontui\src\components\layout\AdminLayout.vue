<template>
  <div class="flex h-screen overflow-hidden">
    <!-- Sidebar - fixed on desktop, toggleable on mobile -->
    <div class="md:block" :class="{ 'hidden': !showSidebar }">
      <AdminSidebar />
    </div>

    <!-- Main Content Area -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <!-- Top Header with Breadcrumbs -->
      <UserHeader 
        @toggleSidebar="toggleSidebar"
        :pageTitle="pageTitle"
        :userName="userName"
        :userAvatar="userAvatar"
      />

      <!-- Main Content - Scrollable independently from header -->
      <div class="flex-1 p-6 overflow-y-auto bg-gray-50">
        <slot></slot>
      </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div 
      v-if="showSidebar && isMobile" 
      class="fixed inset-0 bg-black bg-opacity-30 z-10 md:hidden"
      @click="toggleSidebar"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import AdminSidebar from './AdminSidebar.vue'
import UserHeader from './UserHeader.vue'

// Props for the layout
const props = defineProps({
  pageTitle: {
    type: String,
    default: '管理控制台'
  },
  userName: {
    type: String,
    required: true
  },
  userAvatar: {
    type: String,
    required: true
  }
})

// State
const showSidebar = ref(true)
const isMobile = ref(window.innerWidth < 768) // Initialize with current width

// Toggle sidebar visibility (primarily for mobile view)
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

// Handle window resize
const handleResize = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    showSidebar.value = true
  }
}

// Lifecycle hooks
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* Add smooth transition to sidebar toggle */
.md\:block {
  transition: all 0.3s ease;
}
</style> 