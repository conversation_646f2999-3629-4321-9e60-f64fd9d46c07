export default {
  system: {
    title: '<PERSON><PERSON><PERSON> học Thông minh',
    slogan: '<PERSON><PERSON><PERSON> thức thay đổi số phận, trí tuệ thắp sáng tương lai',
    copyright: '© 2025 Lớp học Thông minh. Đ<PERSON> đăng ký bản quyền.'
  },
  login: {
    title: '<PERSON><PERSON><PERSON> nhập',
    username: '<PERSON><PERSON> lòng nhập tên người dùng',
    password: '<PERSON><PERSON> lòng nhập mật khẩu',
    remember: '<PERSON>hi nhớ mật khẩu',
    forgot: 'Quên mật khẩu?',
    button: 'Đăng nhập',
    otherMethods: 'Ph<PERSON>ơng thức đăng nhập khác',
    noAccount: 'Ch<PERSON>a có tài khoản?',
    register: 'Đăng ký ngay'
  },
  role: {
    student: '<PERSON><PERSON><PERSON> sinh',
    teacher: '<PERSON><PERSON><PERSON><PERSON> viên',
    admin: 'Quản trị viên'
  },
  errors: {
    loginFailed: '<PERSON><PERSON><PERSON> nhậ<PERSON> thất bại, vui lòng thử lại!',
    wrongCredentials: 'Tên người dùng hoặc mật khẩu không chính xác, vui lòng thử lại!',
    roleError: 'Lỗi vai trò người dùng, vui lòng thử lại!',
    unknown: 'Lỗi không xác định, vui lòng thử lại sau!',
    loadCoursesListFailed: 'Không thể tải danh sách khóa học',
    fetchCoursesListFailed: 'Lấy danh sách khóa học thất bại',
    fetchLearningStatsFailed: 'Lấy dữ liệu thống kê học tập thất bại',
    fetchStudentInfoFailed: 'Lấy thông tin học sinh thất bại',
    joinClassFailed: 'Tham gia lớp học thất bại'
  },
  dashboard: {
    student: {
      title: 'Trang Chính Học Sinh',
      welcome: 'Chào mừng trở lại, {name}',
      today: 'Hôm nay là {date}',
      learningStatus: {
        continuousLearning: 'Học liên tục {days} ngày',
        weeklyLearning: '{hours} giờ trong tuần này'
      },
      learningGoal: {
        title: 'Mục Tiêu Học Tập',
        remainingCourses: 'Cần hoàn thành thêm {count} khóa học để đạt cấp độ tiếp theo'
      },
      stats: {
        courseCompletion: {
          title: 'Hoàn Thành Khóa Học',
          totalCourses: 'Đã hoàn thành {completed} trong số {total} khóa học',
          monthlyChange: '↑{percent}% so với tháng trước'
        },
        homeworkCompletion: {
          title: 'Hoàn Thành Bài Tập',
          totalHomeworks: 'Đã hoàn thành {completed} trong số {total} bài tập',
          averageScore: 'Điểm trung bình: {score}'
        },
        learningHours: {
          title: 'Thời Gian Học',
          hours: '{hours} giờ',
          increase: '↑ {hours} giờ',
          weekday: {
            monday: 'T2',
            sunday: 'CN'
          }
        },
        points: {
          title: 'Điểm & Thành Tích',
          points: '{points} điểm',
          increase: '↑ {points}',
          medalLevel: {
            bronze: 'Học sinh Huy chương Đồng',
            silver: 'Học sinh Huy chương Bạc',
            gold: 'Học sinh Huy chương Vàng'
          },
          nextLevel: '{points} điểm để đạt {level}'
        }
      },
      recommendations: {
        title: 'Đề Xuất Học Tập Cá Nhân',
        efficiency: 'Hiệu Quả Học Tập',
        reinforcement: 'Củng Cố Kiến Thức',
        habit: 'Thói Quen Học Tập'
      }
    }
  },
  courses: {
    title: 'Khóa Học Của Tôi',
    joinClass: 'Tham gia lớp học',
    points: 'điểm',
    courseStats: {
      total: 'Tất cả khóa học',
      completed: 'Đã hoàn thành',
      inProgress: 'Đang học',
      weeklyHours: 'Học tuần này'
    },
    joinClassModal: {
      title: 'Tham gia lớp học',
      enterCode: 'Vui lòng nhập mã mời lớp học',
      codePlaceholder: 'Nhập mã 6 chữ số',
      joining: 'Đang tham gia...',
      confirm: 'Xác nhận tham gia',
      success: 'Tham gia lớp học thành công',
      failed: 'Tham gia lớp học thất bại',
      failedWithError: 'Tham gia lớp học thất bại: {error}'
    },
    coursesList: {
      title: 'Tất cả khóa học',
      teacher: 'Giảng viên',
      hours: 'Giờ học',
      students: 'Có {count} người đang học',
      continueLearning: 'Tiếp tục học →',
      review: 'Ôn tập →'
    },
    pagination: {
      previous: 'Trang trước',
      next: 'Trang sau'
    },
    notes: {
      button: 'Ghi chú',
      title: '{course} - Ghi chú',
      listTitle: 'Danh sách ghi chú',
      count: '{count} ghi chú',
      searchPlaceholder: 'Tìm kiếm ghi chú...',
      loading: 'Đang tải danh sách ghi chú...',
      loadingContent: 'Đang tải nội dung ghi chú...',
      empty: 'Không có ghi chú',
      untitled: 'Ghi chú chưa đặt tên',
      createdAt: 'Tạo lúc',
      loadFailed: 'Tải danh sách ghi chú thất bại',
      loadDetailFailed: 'Tải chi tiết ghi chú thất bại'
    }
  },
  courseContent: {
    videoPlayerNotSupported: 'Trình duyệt của bạn không hỗ trợ trình phát video HTML5',
    
    tabs: {
      overview: 'Tổng quan khóa học',
      discussion: 'Thảo luận'
    },
    
    controls: {
      notes: 'Ghi chú',
      navigation: 'Điều hướng',
      rating: 'Đánh giá'
    },
    
    stats: {
      totalHours: 'Tổng số giờ',
      hour: 'giờ',
      studentsCount: 'Học viên',
      rating: 'Đánh giá',
      ratingCount: 'đánh giá'
    },
    
    comments: {
      title: 'Thảo luận',
      placeholder: 'Chia sẻ ý kiến của bạn...',
      submit: 'Đăng bình luận',
      reply: 'Trả lời',
      replyPlaceholder: 'Trả lời...',
      submitReply: 'Đăng trả lời',
      instructor: 'Giảng viên'
    },
    
    sidebar: {
      progress: 'Tiến độ',
      lessons: 'Bài học',
      lessonsUnit: 'bài học'
    },
    
    aiCompanion: {
      title: 'Trợ lý học tập AI',
      subtitle: 'Sẵn sàng giải đáp thắc mắc của bạn',
      inputPlaceholder: 'Nhập câu hỏi của bạn tại đây...',
      selectCompanion: 'Chọn người đồng hành',
      close: 'Đóng'
    },
    
    aiCompanionDialog: {
      title: 'Chọn đối tác học tập của bạn',
      requiredRoles: 'Vai trò bắt buộc',
      requiredRolesNote: '(Giáo viên Wang được chọn tự động)',
      companionRoles: 'Vai trò người đồng hành',
      optionalRolesNote: '(Tự do lựa chọn)',
      confirm: 'Xác nhận lựa chọn',
      roles: {
        teacher: {
          name: 'Giáo viên Wang',
          desc: 'Hỗ trợ giảng dạy'
        },
        funnyKid: {
          name: 'Xiao Ming (Học sinh hài hước)',
          desc: 'Sôi nổi và vui vẻ, thích nói đùa'
        },
        thinker: {
          name: 'Li Hua (Người tư duy)',
          desc: 'Tư duy sâu sắc và phân tích'
        },
        curious: {
          name: 'Zhang Ying (Người tò mò)',
          desc: 'Thích đặt câu hỏi, làm nổi bật khái niệm'
        },
        topStudent: {
          name: 'Zhao Yang (Học sinh giỏi)',
          desc: 'Tổ chức các điểm chính một cách ngắn gọn'
        }
      }
    },
    
    noteModal: {
      title: 'Ghi chú khóa học',
      search: 'Tìm kiếm ghi chú...',
      new: 'Ghi chú mới',
      titlePlaceholder: 'Tiêu đề ghi chú',
      contentPlaceholder: 'Nhập nội dung ghi chú tại đây...',
      timePoint: 'Thời điểm',
      save: 'Lưu ghi chú',
      cancel: 'Hủy',
      delete: 'Xóa ghi chú',
      markdownSupport: 'Hỗ trợ Markdown',
      charCount: '{count} ký tự',
      confirmDelete: 'Bạn có chắc chắn muốn xóa ghi chú này?',
      // Thông báo
      titleRequired: 'Vui lòng nhập tiêu đề ghi chú',
      updateSuccess: 'Cập nhật ghi chú thành công',
      createSuccess: 'Tạo ghi chú thành công',
      saveFailed: 'Lưu ghi chú thất bại',
      loadFailed: 'Tải ghi chú thất bại',
      deleteSuccess: 'Xóa ghi chú thành công',
      deleteFailed: 'Xóa ghi chú thất bại',
      courseInfoError: 'Không thể lấy thông tin khóa học'
    },
    
    videoNavModal: {
      title: 'Điều hướng video AI',
      summary: 'Tóm tắt nội dung video',
      noSummary: 'Không có tóm tắt video',
      keyPoints: 'Điểm chính',
      noKeyPoints: 'Không có dữ liệu điểm chính'
    },
    
    ratingDialog: {
      title: 'Đánh giá khóa học',
      submit: 'Gửi đánh giá',
      cancel: 'Hủy'
    }
  },
  
  courseOverview: {
    keyPoints: 'Điểm chính khóa học',
    importantPoints: 'Kiến thức quan trọng',
    notes: 'Lưu ý',
    noContent: 'Không có nội dung',
    edit: 'Chỉnh sửa tổng quan khóa học',
    enterKeyPoints: 'Nhập điểm chính khóa học',
    enterImportantPoints: 'Nhập kiến thức quan trọng',
    enterNotes: 'Nhập lưu ý',
    updateSuccess: 'Tổng quan khóa học đã được cập nhật',
    saveFailed: 'Lưu không thành công'
  },
  general: {
    confirm: 'Xác nhận',
    cancel: 'Hủy',
    confirmation: 'Xác nhận',
    error: 'Đã xảy ra lỗi',
    uploadFailed: 'Tải lên thất bại',
    pleaseWait: 'Vui lòng đợi...',
    defaultStudentName: 'Học sinh',
    home: 'Trang chủ',
    points: 'Điểm',
    save: 'Lưu'
  },
  
  userHeader: {
    profile: 'Hồ sơ cá nhân',
    settings: 'Cài đặt tài khoản',
    activate: 'Kích hoạt tài khoản',
    logout: 'Đăng xuất',
    logoutFailed: 'Đăng xuất thất bại',
    verification: {
      title: 'Kích hoạt tài khoản',
      alertTitle: 'Vui lòng hoàn thành xác thực',
      successTitle: 'Xác thực hoàn tất',
      teacherDescription: 'Là giáo viên, kích hoạt cho phép sử dụng nhiều tính năng hơn. Vui lòng nhập thông tin thật để xác thực.',
      studentDescription: 'Kích hoạt tài khoản cho phép sử dụng nhiều tính năng hơn. Vui lòng nhập thông tin thật để xác thực.',
      teacherSuccess: 'Tài khoản giáo viên của bạn đã được xác thực và kích hoạt',
      studentSuccess: 'Tài khoản của bạn đã được xác thực và kích hoạt',
      step1: 'Nhập thông tin',
      step2: 'Xác thực điện thoại',
      step3: 'Hoàn thành',
      name: 'Họ tên',
      idCard: 'CMND/CCCD',
      phone: 'Điện thoại',
      code: 'Mã xác thực',
      namePlaceholder: 'Nhập họ tên thật',
      idCardPlaceholder: 'Nhập số CMND/CCCD',
      phonePlaceholder: 'Nhập số điện thoại',
      codePlaceholder: 'Nhập mã xác thực',
      nameRequired: 'Vui lòng nhập họ tên thật',
      nameLength: 'Họ tên phải từ 2-20 ký tự',
      idCardRequired: 'Vui lòng nhập số CMND/CCCD',
      idCardFormat: 'Vui lòng nhập số CMND/CCCD hợp lệ',
      phoneRequired: 'Vui lòng nhập số điện thoại',
      phoneFormat: 'Vui lòng nhập số điện thoại hợp lệ',
      codeRequired: 'Vui lòng nhập mã xác thực',
      codeFormat: 'Mã xác thực phải có 6 chữ số',
      next: 'Tiếp theo',
      previous: 'Trước',
      modify: 'Chỉnh sửa',
      getCode: 'Lấy mã',
      resend: 'Gửi lại sau {seconds}s',
      verify: 'Xác thực',
      complete: 'Hoàn thành',
      codeSentSuccess: 'Đã gửi mã đến {phone}',
      codeSendFailed: 'Không thể gửi mã, vui lòng thử lại sau',
      verificationFailed: 'Xác thực thất bại, vui lòng kiểm tra thông tin',
      enterCorrectCode: 'Vui lòng nhập đúng mã xác thực',
      fillRequiredFields: 'Vui lòng điền đầy đủ tất cả các trường bắt buộc',
      verifying: 'Đang xác thực ',
      threeElements: ' thông tin và mã',
      sendCodeTo: 'Gửi mã đến'
    }
  },

  breadcrumb: {
    adminDashboard: 'Bảng điều khiển',
    teacherDashboard: 'Trang chủ giáo viên',
    studentDashboard: 'Trang chủ học sinh',
    myCourses: 'Khóa học của tôi',
    courseContent: 'Nội dung khóa học',
    myAssignments: 'Bài tập của tôi',
    assignmentManagement: 'Quản lý bài tập',
    courseManagement: 'Quản lý khóa học',
    studentManagement: 'Quản lý học sinh',
    classManagement: 'Quản lý lớp học',
    gradeManagement: 'Quản lý điểm số',
    questionBank: 'Ngân hàng câu hỏi',
    textbookProjects: 'Dự án giáo trình',
    contentCreation: 'Công cụ',
    lessonPlanProjects: 'Giáo án',
    contentPptProjects: 'Tạo PPT',
    contentScriptProjects: 'Kịch bản giảng dạy',
    contentVideoProjects: 'Tổng hợp video',
    videoManagement: 'Quản lý video',
    digitalTeacher: 'Giáo viên số',
    aiVoice: 'Giọng nói AI',
    aiLecture: 'Bài giảng AI',
    aiLearning: 'Học tập AI',
    bookshelf: 'Kệ sách',
    systemManagement: 'Quản lý hệ thống',
    userManagement: 'Quản lý người dùng',
    storeManagement: 'Quản lý cửa hàng',
    pointsManagement: 'Quản lý điểm',
    notificationManagement: 'Quản lý thông báo',
    auditLogs: 'Nhật ký kiểm toán',
    knowledgeBase: 'Cơ sở kiến thức',
    personalCenter: 'Trung tâm cá nhân',
    profile: 'Hồ sơ',
    aiAssistant: 'Trợ lý AI',
    pointsMall: 'Cửa hàng điểm',
    settings: 'Cài đặt',
    chapter: 'Chương',
    class: 'Lớp học',
    homework: 'Bài tập',
    grades: 'Điểm số',
    dograde: 'Chấm điểm',
    grading: 'Chi tiết chấm điểm'
  },
  
  sidebar: {
    home: 'Trang chủ',
    aiLecture: 'Bài giảng AI',
    bookshelf: 'Kệ sách',
    courses: 'Khóa học của tôi',
    assignments: 'Bài tập của tôi',
    knowledgeBase: 'Cơ sở kiến thức',
    aiAssistant: 'Trợ lý AI',
    pointsMall: 'Cửa hàng điểm',
    personalCenter: 'Trung tâm cá nhân',
    collapse: 'Thu gọn menu'
  },
  
  todayLearn: {
    title: 'Bài Giảng AI',
    upload: {
      area: 'Kéo tệp vào đây, hoặc',
      button: 'Nhấn để tải lên',
      supportTypes: 'Định dạng tệp hỗ trợ: .doc, .docx, .txt, .pdf',
      dropHint: 'Thả để tải tệp lên',
      uploading: 'Đang tải lên...'
    },
    styleDialog: {
      title: 'Chọn Phong Cách Bài Giảng',
      description: 'Vui lòng chọn phong cách giảng dạy cho tài liệu của bạn:',
      cancel: 'Hủy Tải Lên',
      confirm: 'Xác Nhận và Tải Lên'
    }
  },
  
  bookshelf: {
    title: 'Kệ sách',
    stats: '{count} Tài liệu',
    slogan: 'Cải tiến trải nghiệm đọc của bạn',
    search: 'Tìm kiếm tài liệu...',
    generating: '{count} tài liệu đang tạo',
    batchOperation: 'Thao tác hàng loạt',
    exitBatch: 'Thoát chế độ hàng loạt',
    selectAll: 'Chọn tất cả',
    cancelSelect: 'Hủy chọn',
    delete: 'Xóa',
    outlineCompleted: 'Đã hoàn thành tạo dàn bài',
    processingTime: 'Ước tính 10-15 phút',
    document: 'Tài liệu',
    
    addDocument: {
      title: 'Thêm tài liệu',
      dropHint: 'Thả tệp vào đây',
      supportDrag: 'Hỗ trợ kéo & thả'
    },
    
    upload: {
      uploading: 'Đang tải lên...',
      deleting: 'Đang xóa...',
      completed: 'Đã hoàn thành',
      pleaseWait: 'Vui lòng đợi...',
      progress: 'Đã hoàn thành {completed} / {total}'
    },
    
    documentCard: {
      editName: 'Sửa tên',
      delete: 'Xóa',
      moreActions: 'Thao tác khác'
    },
    
    editNameDialog: {
      title: 'Sửa tên tài liệu',
      placeholder: 'Nhập tên tài liệu',
      confirm: 'Xác nhận',
      cancel: 'Hủy'
    },
    
    styleDialog: {
      title: 'Chọn phong cách giảng dạy',
      description: 'Vui lòng chọn phong cách bạn muốn AI sử dụng cho tài liệu này',
      selectStyle: 'Chọn phong cách giảng dạy:',
      confirm: 'Xác nhận',
      cancel: 'Hủy',
      cancelUpload: 'Hủy tải lên',
      confirmAndUpload: 'Xác nhận & Tải lên'
    },
    
    preview: {
      generating: 'Dàn bài đang được tạo, vui lòng thử lại sau',
      failed: 'Không thể tải xem trước'
    },
    
    notifications: {
      nameUpdated: 'Đã cập nhật tên tài liệu',
      deleteConfirm: 'Bạn có chắc chắn muốn xóa {name}?',
      deleteFailed: 'Xóa thất bại',
      fileTypeError: 'Loại tệp không được hỗ trợ',
      uploadSuccess: '{name} đã tải lên thành công',
      generatingOutline: 'Đang tạo dàn bài, quá trình này có thể mất vài phút',
      selectDocumentsToDelete: 'Vui lòng chọn tài liệu để xóa',
      batchDeleteConfirm: 'Bạn có chắc chắn muốn xóa {count} tài liệu đã chọn?',
      batchDeleteSuccess: 'Đã xóa thành công {count} tài liệu',
      batchDeleteFailed: 'Xóa hàng loạt thất bại',
      confirmDelete: 'Bạn có chắc chắn muốn xóa {name}?',
      deleteConfirmTitle: 'Xác nhận xóa',
      confirmButtonText: 'Xác nhận',
      cancelButtonText: 'Hủy',
      deleteSuccess: 'Xóa thành công',
      fileTypesHint: 'Chỉ hỗ trợ tệp PDF và DOCX',
      confirmBatchDelete: 'Bạn có chắc chắn muốn xóa {count} tài liệu đã chọn?',
      batchDeleteConfirmTitle: 'Xác nhận xóa hàng loạt'
    }
  },
  
  htmlPreview: {
    actions: {
      chapterList: 'Danh sách chương',
      switchStyle: 'Chuyển phong cách',
      back: 'Quay lại kệ sách'
    },
    chapterDialog: {
      title: 'Danh sách chương',
      noChapters: 'Không có chương nào',
      page: 'tr. {start}-{end}'
    },
    styleDialog: {
      title: 'Chọn phong cách giảng dạy',
      currentStyle: 'Phong cách hiện tại:',
      loading: 'Đang tải danh sách phong cách...',
      cancel: 'Hủy',
      confirm: 'Xác nhận thay đổi',
      switching: 'Đang chuyển...'
    },
    status: {
      loading: 'Đang tải...',
      generating: 'Đang tạo nội dung chương...',
      pointsGenerated: 'Đã tạo {count} điểm chính',
      completed: 'Tạo hoàn thành!',
      notGenerated: 'Nội dung chương chưa được tạo',
      startGenerate: 'Bắt đầu tạo',
      loadFailed: 'Không thể tải'
    },
    controls: {
      play: 'Phát',
      pause: 'Tạm dừng',
      noAudio: 'Không có âm thanh',
      generating: 'Đang tạo âm thanh...',
      prev: 'Trước',
      next: 'Sau',
      page: 'Trang {current} / {total}',
      generating: 'Đang tạo nội dung...'
    },
    notifications: {
      styleChanged: 'Thay đổi phong cách thành công',
      styleChangedDetail: 'Đã chuyển sang {style}, trang sẽ tải lại, âm thanh sẽ được tạo trong nền',
      styleChangeFailed: 'Thay đổi phong cách thất bại',
      retryLater: 'Vui lòng thử lại sau',
      audioUpdated: 'Âm thanh đã cập nhật',
      audioUpdateDetail: 'Âm thanh cho phong cách mới đã được tạo',
      generationStarted: 'Bắt đầu tạo',
      generationDetail: 'Quá trình tạo nội dung chương đã bắt đầu, vui lòng đợi...',
      generationFailed: 'Tạo thất bại',
      triggerFailed: 'Không thể kích hoạt tạo chương',
      autoPaging: 'Tự động lật trang',
      pagingMessage: 'Đang chuyển đến trang {page} và tiếp tục phát...',
      playCompleted: 'Phát hoàn tất',
      playCompletedDetail: 'Đã phát hết tất cả nội dung',
      firstPointGenerated: 'Đã tạo điểm chính đầu tiên',
      firstPointDetail: 'Bây giờ có thể xem trước, nội dung khác sẽ được tạo trong nền',
      generationCompleted: 'Tạo hoàn tất',
      allContentGenerated: 'Tất cả nội dung chương đã được tạo hoàn tất',
      outlineCompleted: 'Đã hoàn thành tạo dàn bài {names}!'
    },
    status: {
      notGenerated: 'Chưa tạo',
      generating: 'Đang tạo',
      completed: 'Hoàn thành'
    }
  }
} 