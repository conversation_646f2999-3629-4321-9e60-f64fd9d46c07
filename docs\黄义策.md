plan(计划)
PRD(产品需求)11121

https://www.taskmaster.one/
TaskMaster是一个AI驱动的任务管理系统，可以集成到多种代码编辑器中，包括Cursor、Lovable、Windsurf和Roo Code等。根据GitHub仓库信息，TaskMaster提供了以下主要功能：
任务分解与管理 - 可以解析产品需求文档(PRD)并自动生成任务列表
任务优先级排序 - 帮助开发者确定应该先处理哪些任务
任务实现辅助 - 在开发者实现特定任务时提供帮助
任务扩展 - 可以帮助详细展开特定任务的实现细节

我项目建立了一个mcp(taskmaster)
准备了基本的项目结构，包括：
scripts/prd.txt - 包含项目需求文档
tasks/ - 任务存储目录
askMaster已经成功安装并配置在你的系统中。由于文件操作遇到一些问题，让我为你总结在Cursor中使用TaskMaster的方法：

1、在Cursor中，可以直接向AI助手询问与TaskMaster相关的问题，例如：
Can you help me initialize taskmaster-ai into my project?
2、系统会自动使用已配置的TaskMaster来分析你的项目，并提供帮


系统会自动使用已配置的TaskMaster来分析你的项目，并提供帮助
常见TaskMaster命令
解析需求文档：Can you parse my PRD at scripts/prd.txt?
查看下一任务：What's the next task I should work on?
实现特定任务：Can you help me implement task 1?
扩展任务：Can you help me expand task 2?





Roo Code（类似CURSOR）





画布、自适应、裁剪
ts文件也要注意改
1、预设图片的加载到后，move-target没有边框正确包围内容 
2、本地上传图片加载到后正常
3、你结合本地上传解决move-target没有边框正确包围内容的问题

CSS：样式/图标

滑块的更新机制的原因
这个问题的主要原因是属性更新事件在组件层级间传递不完整，具体表现在两方面：
事件捕获不全：FormItem.vue 中只为 Slider 类型控件添加了完整的 @change 和 @input 事件监听，而其他控件类型（如 Color、Boolean、Input 等）缺少这些事件监听，导致它们的值变化无法正确传递。
属性实时更新限制：AttributeContainer.vue 中只对特定属性进行了实时更新处理，而不是对所有变更一视同仁，这导致某些属性变更不会立即生效。
解决方案是通过为所有控件类型添加一致的事件监听处理，并改进 AttributeContainer.vue 中的更新逻辑，使所有属性变更都能被实时应用，同时添加防抖机制避免频繁更新带来的性能问题。


明确任务：如增加、删除、检查、任务、要求、必须、异常、针对、就、路由指定、根据这个线索去处理问题
少用：没有、我想要、请、如果、帮我

就@AudioPanel进行功能调整
1、 参考@ImagePanel 的风格和布局来进行调，包过涉及到的ts文件也要修改
2、音频有三个标签：生成音频库、系统素材、本地上传，这三个标签要创建三个文件目录，注意路由指定
3、生成音频库、系统素材，这两个都是从文件里取音频
4、本地上传除了取文件里取音频，同时可以上传

对@VideoPanel 视频进行修改
1、 参考@AudioPanel 音频和@ImagePanel 图片的风格和布局来进行调，包过涉及到的ts文件也要修改
2、就视频菜单改为2个标签：系统素材、本地上传，这2个标签要创建2个文件目录，注意路由指定
3、系统素材、本地上传这两个都是从文件里取音频
4、本地上传除了取文件里取音频，同时可以上传其中刚才创建1个文件里


npm install


# Git提交、推送和拉取操作详解

## 提交操作 (Commit)

```bash
# 1. 查看当前更改状态
git status

# 2. 添加文件到暂存区
git add 文件名       # 添加单个文件
git add .           # 添加所有更改文件
git add *.js        # 添加所有JS文件

# 3. 提交已暂存的更改
git commit -m "提交说明"  # 带简短说明的提交

# 4. 跳过钩子提交(解决提交卡顿问题)
git commit -m "提交说明" --no-verify

# 5. 一步完成添加和提交(仅适用于已跟踪的文件)
git commit -am "提交说明"

# 6. 修改最近一次提交
git commit --amend -m "新的提交说明"  # 修改提交信息
git commit --amend --no-edit         # 不修改提交信息，只添加新的更改
```

## 推送操作 (Push)

```bash
# 1. 查看远程仓库
git remote -v

# 2. 推送到默认分支
git push

# 3. 推送到指定分支
git push origin main      # 推送到main分支
git push origin feature1  # 推送到feature1分支

# 4. 首次推送并设置上游分支
git push -u origin main  # 推送并设置上游分支为main
                         # (-u 等同于 --set-upstream)

# 5. 强制推送(谨慎使用)
git push --force        # 强制推送，可能覆盖其他人的提交
git push --force-with-lease  # 较安全的强制推送，如果远程有其他人的新提交则会拒绝

# 6. 推送标签
git push --tags        # 推送所有标签
git push origin v1.0.0  # 推送特定标签
```

## 拉取操作 (Pull/Fetch)

```bash
# 1. 拉取并合并远程更改(pull = fetch + merge)
git pull               # 拉取当前分支的远程更新
git pull origin main   # 从origin仓库拉取main分支的更新

# 2. 使用rebase方式拉取(保持提交历史线性)
git pull --rebase      # 拉取并使用rebase而不是merge

# 3. 仅获取远程更新但不合并
git fetch              # 获取所有分支的更新
git fetch origin       # 获取指定远程仓库的更新
git fetch origin main  # 获取指定远程仓库特定分支的更新

# 4. 查看拉取后的变化
git fetch
git diff origin/main   # 查看本地与远程main分支的差异

# 5. 拉取后检查日志
git pull
git log --oneline -10  # 查看最近10条提交记录
```

## 常见场景和问题解决

```bash
# 场景1: 推送前先拉取最新代码
git pull               # 或 git pull --rebase
git push

# 场景2: 解决推送被拒绝问题
git pull               # 先拉取并合并远程更改
# 解决冲突后
git add .
git commit -m "解决冲突"
git push

# 场景3: 查看远程分支
git branch -r          # 查看远程分支
git branch -a          # 查看所有分支(本地+远程)

# 场景4: 撤销本地提交(尚未推送)
git reset HEAD~1       # 撤销最近一次提交，保留更改
git reset --hard HEAD~1  # 撤销最近一次提交，丢弃更改

# 场景5: 创建分支并推送
git checkout -b feature1  # 创建并切换到新分支
# 做一些更改...
git add .
git commit -m "新功能开发"
git push -u origin feature1  # 推送新分支
```

## 专业提示

1. **提交前检查**：始终使用`git status`和`git diff`检查更改

2. **频繁小提交**：进行小而有意义的提交，而不是大型杂乱的提交

3. **推送前拉取**：在推送前先`git pull`同步远程更改，避免冲突

4. **提交信息规范**：养成写清晰、有意义的提交信息的习惯
   - 例如: "修复: 用户登录表单验证错误"
   - 或使用约定式提交格式: "fix: 修复用户登录表单验证"

5. **解决卡顿问题**：
   ```bash
   # 暂时禁用钩子
   git config core.hooksPath /dev/null
   # 完成操作后恢复钩子
   git config --unset core.hooksPath
   ```

以上命令涵盖了Git日常提交、推送和拉取的主要场景，您可以根据需要选择使用。


启动项目的代码

dev
要启动飞剪编辑器（Fly-Cut）项目，你需要按照以下步骤操作：
项目启动命令
项目使用Vite作为开发服务器，有以下启动选项：
普通开发模式:  npm run dev

HTTPS开发模式: npm run dev-ssl

这会启动带有SSL证书的开发服务器，适用于需要安全上下文的WebCodecs API测试
环境要求
Node.js >= 16.x
支持WebCodecs API的现代浏览器（Chrome 94+、Edge 94+等）
依赖项安装完成（通过 npm install、yarn 或 pnpm install）

其他可用命令
npm run preview: 预览构建后的项目
npm run lint: 运行ESLint检查代码
npm run lint-fix: 自动修复可修复的lint问题
npm run type-check: 运行TypeScript类型检查
开发服务器配置
Vite开发服务器配置详情（在vite.config.ts中）：
端口：4008
支持HTTPS（需要使用dev-ssl命令）
自动导入Vue组件和Element Plus组件
包含Vue DevTools集成
启动后，可以在开发服务器的控制台中按下h键查看更多帮助选项

件数据存储：使用 OPFS (Origin Private File System) 存储，路径前缀为 user_videos/
OPFS 是浏览器的原始私有文件系统，这些文件存储在浏览器的私有空间中，不是电脑上的常规文件夹