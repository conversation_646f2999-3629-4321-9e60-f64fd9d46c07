<template>
  <AdminLayout 
    pageTitle="通知管理" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    
    <div class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div class="space-y-8">
          <!-- 操作栏 -->
          <div class="flex flex-col sm:flex-row justify-between items-end gap-4">
            <div class="flex flex-wrap items-center gap-4">
              <el-button
                type="primary"
                @click="showAddNotificationModal = true"
                class="!flex items-center"
              >
                <el-icon class="mr-1"><Plus /></el-icon>
                发布通知
              </el-button>
              <el-select 
                v-model="typeFilter" 
                placeholder="通知类型" 
                clearable 
                class="!min-w-[120px]"
              >
                <el-option label="系统维护" value="maintenance">
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Tools /></el-icon>
                    系统维护
                  </div>
                </el-option>
                <el-option label="功能更新" value="update">
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Refresh /></el-icon>
                    功能更新
                  </div>
                </el-option>
                <el-option label="重要提醒" value="alert">
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Warning /></el-icon>
                    重要提醒
                  </div>
                </el-option>
              </el-select>
            </div>
            <el-input
                v-model="searchQuery"
                placeholder="搜索通知..."
              class="!w-full sm:!w-64"
              >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 通知列表 -->
          <el-card class="!border-0 shadow-md rounded-lg">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th scope="col" class="px-6 py-4 text-left text-sm font-semibold text-gray-600">
                      标题
                    </th>
                    <th scope="col" class="px-6 py-4 text-left text-sm font-semibold text-gray-600">
                      类型
                    </th>
                    <th scope="col" class="px-6 py-4 text-left text-sm font-semibold text-gray-600">
                      发布时间
                    </th>
                    <th scope="col" class="px-6 py-4 text-left text-sm font-semibold text-gray-600">
                      状态
                    </th>
                    <th scope="col" class="px-6 py-4 text-left text-sm font-semibold text-gray-600">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr v-for="notification in filteredNotifications" :key="notification.id" class="hover:bg-gray-50 transition-colors">
                    <td class="px-6 py-4">
                      <div class="flex items-center">
                        <el-icon :class="getIconColorClass(notification.type)" class="mr-3 text-lg">
                          <component :is="getIconComponent(notification.type)" />
                        </el-icon>
                        <span class="text-sm font-medium text-gray-900">{{ notification.title }}</span>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <el-tag
                        :type="getTagType(notification.type)"
                        size="small"
                        :effect="getTagEffect(notification.type)"
                      >
                        {{ getTypeName(notification.type) }}
                      </el-tag>
                    </td>
                    <td class="px-6 py-4">
                      <span class="text-sm text-gray-500">{{ notification.time }}</span>
                    </td>
                    <td class="px-6 py-4">
                      <el-tag
                        :type="getStatusTagType(notification.status)"
                        size="small"
                        :effect="getStatusTagEffect(notification.status)"
                      >
                        {{ getStatusName(notification.status) }}
                      </el-tag>
                    </td>
                    <td class="px-6 py-4">
                      <div class="flex items-center space-x-3">
                        <el-button
                          type="primary"
                          link
                          @click="editNotification(notification)"
                          class="!flex items-center"
                        >
                          <el-icon class="mr-1"><Edit /></el-icon>
                          编辑
                        </el-button>
                        <el-button
                          type="danger"
                          link
                          @click="deleteNotification(notification)"
                          class="!flex items-center"
                        >
                          <el-icon class="mr-1"><Delete /></el-icon>
                          删除
                        </el-button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
          </div>

          <!-- 分页 -->
            <div class="flex flex-col sm:flex-row justify-between items-center gap-4 px-6 py-4 border-t border-gray-200">
                <p class="text-sm text-gray-700">
                  显示第
                  <span class="font-medium">{{ startIndex + 1 }}</span>
                  至
                  <span class="font-medium">{{ Math.min(endIndex, filteredNotifications.length) }}</span>
                  条，共
                  <span class="font-medium">{{ filteredNotifications.length }}</span>
                  条
                </p>
              <el-pagination
                v-model:current-page="currentPage"
                :page-size="pageSize"
                :total="filteredNotifications.length"
                :pager-count="5"
                layout="prev, pager, next"
                background
                class="!m-0"
              />
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 添加/编辑通知模态框 -->
    <el-dialog
      v-model="showAddNotificationModal"
      :title="editingNotification ? '编辑通知' : '发布新通知'"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form 
        :model="notificationForm" 
        label-position="top"
        class="space-y-6"
      >
        <el-form-item label="标题">
          <el-input 
            v-model="notificationForm.title" 
            placeholder="请输入通知标题"
            :prefix-icon="Edit"
          />
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select 
            v-model="notificationForm.type" 
            placeholder="请选择通知类型"
            class="!w-full"
          >
            <el-option label="系统维护" value="maintenance">
              <div class="flex items-center">
                <el-icon class="mr-2"><Tools /></el-icon>
                系统维护
              </div>
            </el-option>
            <el-option label="功能更新" value="update">
              <div class="flex items-center">
                <el-icon class="mr-2"><Refresh /></el-icon>
                功能更新
              </div>
            </el-option>
            <el-option label="重要提醒" value="alert">
            <div class="flex items-center">
                <el-icon class="mr-2"><Warning /></el-icon>
                重要提醒
            </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="内容">
          <el-input
            v-model="notificationForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入通知内容"
            resize="vertical"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-select 
            v-model="notificationForm.status" 
            placeholder="请选择通知状态"
            class="!w-full"
          >
            <el-option label="已发布" value="published">
              <div class="flex items-center">
                <el-icon class="mr-2"><Check /></el-icon>
                已发布
              </div>
            </el-option>
            <el-option label="草稿" value="draft">
              <div class="flex items-center">
                <el-icon class="mr-2"><Files /></el-icon>
                草稿
              </div>
            </el-option>
            <el-option label="已过期" value="expired">
            <div class="flex items-center">
                <el-icon class="mr-2"><Timer /></el-icon>
                已过期
            </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="showAddNotificationModal = false">
            取消
          </el-button>
          <el-button type="primary" @click="saveNotification">
            {{ editingNotification ? '更新' : '发布' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </AdminLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import { useAdminStore } from '@/stores/admin'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Edit,
  Delete,
  Search,
  Tools,
  Refresh,
  Warning,
  Check,
  Files,
  Timer
} from '@element-plus/icons-vue'

const adminStore = useAdminStore()

// 状态变量
const searchQuery = ref('')
const typeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showAddNotificationModal = ref(false)
const editingNotification = ref(null)

// 通知表单
const notificationForm = ref({
  title: '',
  type: '',
  content: '',
  status: 'published'
})

// 模拟数据
const notifications = ref([
  {
    id: 1,
    title: '系统维护通知',
    type: 'maintenance',
    content: '系统将于周日凌晨2:00-4:00进行例行维护，请提前做好准备。',
    time: '2024-01-20 10:30',
    status: 'published'
  },
  {
    id: 2,
    title: '新功能上线',
    type: 'update',
    content: '智能批改功能已上线，请前往教师面板查看使用说明。',
    time: '2024-01-18 14:15',
    status: 'published'
  },
  {
    id: 3,
    title: '重要安全更新',
    type: 'alert',
    content: '请所有用户及时修改密码，确保账号安全。',
    time: '2024-01-15 09:00',
    status: 'published'
  }
])

// 计算属性
const filteredNotifications = computed(() => {
  return notifications.value.filter(notification => {
    const matchesSearch = !searchQuery.value ||
      notification.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      notification.content.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesType = !typeFilter.value || notification.type === typeFilter.value
    return matchesSearch && matchesType
  })
})

const totalPages = computed(() => {
  return Math.ceil(filteredNotifications.value.length / pageSize.value)
})

const startIndex = computed(() => {
  return (currentPage.value - 1) * pageSize.value
})

const endIndex = computed(() => {
  return Math.min(startIndex.value + pageSize.value, filteredNotifications.value.length)
})

const displayedPages = computed(() => {
  const pages = []
  let start = Math.max(1, currentPage.value - 2)
  let end = Math.min(totalPages.value, start + 4)
  
  if (end - start < 4) {
    start = Math.max(1, end - 4)
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const getTypeName = (type) => {
  const types = {
    maintenance: '系统维护',
    update: '功能更新',
    alert: '重要提醒'
  }
  return types[type] || type
}

const getTagType = (type) => {
  const types = {
    maintenance: 'info',
    update: 'success',
    alert: 'warning'
  }
  return types[type] || 'info'
}

const getTagEffect = (type) => {
  return type === 'alert' ? 'dark' : 'light'
}

const getStatusName = (status) => {
  const statuses = {
    published: '已发布',
    draft: '草稿',
    expired: '已过期'
  }
  return statuses[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    published: 'success',
    draft: '',
    expired: 'danger'
  }
  return types[status] || ''
}

const getStatusTagEffect = (status) => {
  return status === 'published' ? 'dark' : 'plain'
}

const getIconComponent = (type) => {
  switch (type) {
    case 'maintenance':
      return Tools
    case 'update':
      return Refresh
    case 'alert':
      return Warning
    default:
      return Warning
  }
}

const getIconColorClass = (type) => {
  const classes = {
    maintenance: 'text-blue-500',
    update: 'text-green-500',
    alert: 'text-yellow-500'
  }
  return classes[type] || 'text-gray-500'
}

const editNotification = (notification) => {
  editingNotification.value = notification
  notificationForm.value = { ...notification }
  showAddNotificationModal.value = true
}

const deleteNotification = (notification) => {
  ElMessageBox.confirm(
    '确定要删除这条通知吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = notifications.value.findIndex(n => n.id === notification.id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 取消删除
  })
}

const saveNotification = () => {
  if (!notificationForm.value.title.trim()) {
    ElMessage.error('请输入通知标题')
    return
  }
  
  if (!notificationForm.value.type) {
    ElMessage.error('请选择通知类型')
    return
  }
  
  if (!notificationForm.value.content.trim()) {
    ElMessage.error('请输入通知内容')
    return
  }

  if (editingNotification.value) {
    // 更新现有通知
    const index = notifications.value.findIndex(n => n.id === editingNotification.value.id)
    if (index !== -1) {
      notifications.value[index] = {
        ...editingNotification.value,
        ...notificationForm.value,
        time: new Date().toLocaleString('zh-CN')
      }
    }
  } else {
    // 添加新通知
    notifications.value.unshift({
      id: Date.now(),
      ...notificationForm.value,
      time: new Date().toLocaleString('zh-CN')
    })
  }

  showAddNotificationModal.value = false
  editingNotification.value = null
  notificationForm.value = {
    title: '',
    type: '',
    content: '',
    status: 'published'
  }
  
  ElMessage.success(editingNotification.value ? '更新成功' : '发布成功')
}
</script>

<style>
.el-dialog {
  --el-dialog-padding-primary: 0;
}

.el-dialog__header {
  @apply px-6 py-4 border-b border-gray-200 mb-0;
}

.el-dialog__body {
  @apply p-6;
}

.el-dialog__footer {
  @apply px-6 py-4 border-t border-gray-200;
}

.el-form-item__label {
  @apply !mb-2 !text-gray-700 !font-medium;
}

.el-input__wrapper,
.el-textarea__wrapper {
  @apply !shadow-none !border !border-gray-300 !rounded-md;
}

.el-input__wrapper:hover,
.el-textarea__wrapper:hover {
  @apply !border-indigo-400;
}

.el-input__wrapper.is-focus,
.el-textarea__wrapper.is-focus {
  @apply !border-indigo-500 !ring-1 !ring-indigo-500;
}

.el-select {
  @apply !w-full;
}

.el-tag {
  @apply !border-0;
}

.el-pagination {
  @apply !justify-center sm:!justify-end;
}

.el-button--primary {
  @apply !bg-indigo-600 !border-indigo-600 hover:!bg-indigo-700 hover:!border-indigo-700;
}

.el-button--primary.is-link {
  @apply !bg-transparent !text-indigo-600 hover:!text-indigo-700;
}

.el-button--danger.is-link {
  @apply !bg-transparent !text-red-600 hover:!text-red-700;
}
</style> 