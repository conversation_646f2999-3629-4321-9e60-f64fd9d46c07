<template>
  <el-divider
    :direction="direction"
    :content-position="contentPosition"
    :border-style="borderStyle"
  >
    <slot />
  </el-divider>
</template>

<script setup>
const props = defineProps({
  direction: {
    type: String,
    default: 'horizontal',
    validator: (value) => ['horizontal', 'vertical'].includes(value),
  },
  contentPosition: {
    type: String,
    default: 'center',
    validator: (value) => ['left', 'right', 'center'].includes(value)
  },
  borderStyle: {
    type: String,
    default: 'solid'
  }
});
</script> 