<template>
  <TeacherLayout 
    pageTitle="个人资料" 
    :userName="teacherStore.teacherData.name"
    :userAvatar="teacherStore.teacherData.avatar"
    activePage="profile">
    
    <div class="space-y-4">
      <!-- 压缩版个人资料卡片 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-4">
          <div class="flex items-start">
            <!-- 头像 -->
            <el-avatar 
              :size="70" 
              :src="teacherStore.teacherData.avatar"
              class="border-2 border-white shadow-md mr-4" />
            
            <!-- 基本信息 -->
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h2 class="text-xl font-semibold text-gray-800">{{ teacherStore.teacherData.name }}</h2>
              </div>
              
              <!-- 教师信息色块 -->
              <div class="flex flex-wrap gap-2 mb-2">
                <span class="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">工号：{{ teacherStore.teacherData.teacherId }}</span>
                <span class="px-3 py-1.5 bg-green-100 text-green-800 rounded-md text-sm font-medium">{{ academicInfo.department }}</span>
                <span class="px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium">{{ academicInfo.title }}</span>
              </div>
            </div>
          </div>

          <!-- 数据分析指标 - 水平布局 -->
          <div class="flex mt-3 pt-3 border-t gap-3">
            <!-- 教学时长 -->
            <div class="flex-1">
              <div class="bg-blue-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-blue-800 flex items-center mb-1">
                  <el-icon class="text-blue-600 mr-2"><Timer /></el-icon>
                  教学时长
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-blue-900 mb-1">
                    {{ teacherAnalytics.totalTeachingHours }}小时
                  </div>
                  <div class="text-sm text-blue-700 bg-blue-100 rounded-full px-3 py-0.5 inline-block">
                    近30天: {{ teacherAnalytics.last30DaysHours }}小时
                  </div>
                </div>
              </div>
            </div>

            <!-- 课程情况 -->
            <div class="flex-1">
              <div class="bg-green-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-green-800 flex items-center mb-1">
                  <el-icon class="text-green-600 mr-2"><Notebook /></el-icon>
                  课程情况
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-green-900 mb-1">
                    {{ teacherAnalytics.activeCourses }}门
                  </div>
                  <div class="text-sm text-green-700 bg-green-100 rounded-full px-3 py-0.5 inline-block">
                    累计: {{ teacherAnalytics.totalCourses }}门
                  </div>
                </div>
              </div>
            </div>

            <!-- 学生情况 -->
            <div class="flex-1">
              <div class="bg-purple-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-purple-800 flex items-center mb-1">
                  <el-icon class="text-purple-600 mr-2"><User /></el-icon>
                  学生情况
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-purple-900 mb-1">
                    {{ teacherAnalytics.currentStudents }}
                  </div>
                  <div class="text-sm text-purple-700 bg-purple-100 rounded-full px-3 py-0.5 inline-block">
                    累计: {{ teacherAnalytics.totalStudents }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人信息管理选项卡 -->
      <el-card shadow="hover">
        <el-tabs v-model="activeTab" tab-position="left" style="min-height: 500px">
          <!-- 基本信息管理 -->
          <el-tab-pane label="基本信息" name="basic">
            <h3 class="text-xl font-medium text-gray-800 mb-4">基本信息</h3>
            <el-form 
              ref="basicInfoForm" 
              :model="basicInfo" 
              label-width="120px" 
              :rules="basicInfoRules"
              status-icon>
              <el-form-item label="姓名" prop="name">
                <el-input v-model="basicInfo.name" disabled />
              </el-form-item>
              <el-form-item label="工号" prop="teacherId">
                <el-input v-model="basicInfo.teacherId" disabled />
              </el-form-item>
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="basicInfo.idCard" disabled />
              </el-form-item>
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="basicInfo.gender">
                  <el-radio label="male">男</el-radio>
                  <el-radio label="female">女</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="出生日期" prop="birthday">
                <el-date-picker 
                  v-model="basicInfo.birthday" 
                  type="date" 
                  placeholder="选择日期" 
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />
              </el-form-item>
              <el-form-item label="民族" prop="ethnicity">
                <el-select v-model="basicInfo.ethnicity" placeholder="请选择民族" style="width: 100%">
                  <el-option v-for="item in ethnicityOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <el-form-item label="政治面貌" prop="politicalStatus">
                <el-select v-model="basicInfo.politicalStatus" placeholder="请选择政治面貌" style="width: 100%">
                  <el-option v-for="item in politicalStatusOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="basicInfo.phone" placeholder="请输入手机号码" />
              </el-form-item>
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model="basicInfo.email" placeholder="请输入电子邮箱" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveBasicInfo">保存信息</el-button>
                <el-button @click="resetBasicInfo">重置</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 职称信息 -->
          <el-tab-pane label="教师信息" name="academic">
            <h3 class="text-xl font-medium text-gray-800 mb-4">教师信息</h3>
            <el-descriptions border>
              <el-descriptions-item label="所属院系">{{ academicInfo.department }}</el-descriptions-item>
              <el-descriptions-item label="职称">{{ academicInfo.title }}</el-descriptions-item>
              <el-descriptions-item label="专业方向">{{ academicInfo.specialization }}</el-descriptions-item>
              <el-descriptions-item label="入职日期">{{ formattedHireDate }}</el-descriptions-item>
              <el-descriptions-item label="教师类型">{{ academicInfo.teacherType }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag type="success">{{ academicInfo.status }}</el-tag>
              </el-descriptions-item>
            </el-descriptions>
            <div class="text-gray-600 text-sm mt-4">
              <el-alert type="info" show-icon>
                教师信息为只读信息，如有疑问请联系人事管理部门
              </el-alert>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useTeacherStore } from '@/stores/teacher'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import { ElMessage } from 'element-plus'
import {
  Timer,
  Notebook,
  User,
  Medal
} from '@element-plus/icons-vue'

// 引入教师数据存储
const teacherStore = useTeacherStore()

// 当前活动选项卡
const activeTab = ref('basic')

// 基本信息表单引用与数据
const basicInfoForm = ref(null)
const basicInfo = reactive({
  name: teacherStore.teacherData.name,
  teacherId: teacherStore.teacherData.teacherId,
  idCard: '3****************X', // 模拟数据
  gender: 'male',
  birthday: '1980-01-01',
  ethnicity: '汉族',
  politicalStatus: '党员',
  phone: teacherStore.teacherData.phone,
  email: teacherStore.teacherData.email
})

// 基本信息验证规则
const basicInfoRules = {
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的电子邮箱格式', trigger: 'blur' }
  ]
}

// 教师信息
const academicInfo = reactive({
  department: teacherStore.teacherData.department,
  title: teacherStore.teacherData.title,
  specialization: '人工智能',
  hireDate: teacherStore.teacherData.joinDate,
  teacherType: '专任教师',
  status: '在职'
})

// 格式化入职日期
const formattedHireDate = computed(() => {
  if (!academicInfo.hireDate) return ''
  const date = new Date(academicInfo.hireDate)
  return date.getFullYear() + '年' + 
         String(date.getMonth() + 1).padStart(2, '0') + '月' + 
         String(date.getDate()).padStart(2, '0') + '日'
})

// 教师统计数据
const teacherAnalytics = reactive({
  totalTeachingHours: 1420,
  last30DaysHours: 48,
  activeCourses: 3,
  totalCourses: 15,
  currentStudents: 126,
  totalStudents: 568
})

// 下拉选项
const ethnicityOptions = [
  '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
  '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族', '黎族', '傈僳族',
  '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族', '景颇族', '柯尔克孜族', '土族',
  '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族', '毛南族', '仡佬族', '锡伯族', '阿昌族', '普米族',
  '塔吉克族', '怒族', '乌孜别克族', '俄罗斯族', '鄂温克族', '德昂族', '保安族', '裕固族', '京族', '塔塔尔族',
  '独龙族', '鄂伦春族', '赫哲族', '门巴族', '珞巴族', '基诺族'
]

const politicalStatusOptions = [
  '党员', '预备党员', '群众', '民主党派', '无党派人士'
]

// 保存基本信息
const saveBasicInfo = () => {
  basicInfoForm.value.validate((valid) => {
    if (valid) {
      // 模拟API调用保存数据
      setTimeout(() => {
        ElMessage.success('基本信息更新成功')
      }, 1000)
    } else {
      return false
    }
  })
}

// 重置基本信息表单
const resetBasicInfo = () => {
  basicInfoForm.value.resetFields()
}

// 组件挂载时
onMounted(() => {
  // 可以在这里从API获取教师详细信息
  console.log('TeacherProfile component mounted')
})
</script>

<style scoped>
/* 自定义样式 */
</style> 