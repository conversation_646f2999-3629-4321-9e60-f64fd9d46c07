<template>
  <view class="container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
        <view class="info">
          <text class="name">{{ userInfo.alias }}</text>
          <br/>
          <text class="role">{{ userInfo.role }}[{{ userInfo.name }}]</text>
        </view>
      </view>
      <view class="points">
        <text class="points-num">{{ userInfo.points }}</text>
        <text class="points-text">积分</text>
      </view>
    </view>
    
    <!-- 统计数据 -->
    <view class="stats">
      <view class="stat-item" v-for="(stat, index) in stats" :key="index" @click="navigateTo(stat.path)">
        <text class="num">{{ stat.num }}</text>
        <text class="text">{{ stat.text }}</text>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-list">
      <view class="menu-group" v-for="(group, index) in menuGroups" :key="index">
        <text class="group-title" v-if="group.title">{{ group.title }}</text>
        <view class="menu-items">
          <view class="menu-item" v-for="item in group.items" :key="item.id" @click="handleMenuItem(item)">
            <view class="item-left">
              <uni-icons :type="item.icon" size="24" color="#666"></uni-icons>
              <text class="item-text">{{ item.text }}</text>
            </view>
            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import request from '@/utils/request'

const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => {
  if (!userStore.userInfo) {
    return {
      name: '',
      alias: '',
      avatar: '',
      role: '',
      points: 0
    }
  }
  return {
    name: userStore.userInfo.user.username,
    alias: userStore.userInfo.user.alias,
    avatar: userStore.userInfo.user.avatar,
    role: userStore.userInfo.user.roles[0].name,
    points: userStore.userInfo.role_info.points
  }
})

// 统计数据
const stats = ref([
  { num: 0, text: '课程', path: '/pages/course/list' },
  { num: 0, text: '班级', path: '/pages/class/list' },
  { num: 0, text: '作业', path: '/pages/homework/list' },
  { num: 0, text: '笔记', path: '/pages/notes/notes' }
])

// 菜单配置
const menuGroups = ref([
  {
    items: [
      { id: 'points', icon: 'gift', text: '我的积分', path: '/pages/points/my' }
    ]
  },
  {
    //title: '设置',
    items: [
      { id: 'account', icon: 'person', text: '账号管理', path: '/pages/settings/account' },
      { id: 'general', icon: 'gear', text: '通用设置', path: '/pages/settings/general' },
      { id: 'help', icon: 'help', text: '帮助中心', path: '/pages/settings/help' },
      { id: 'about', icon: 'info', text: '关于', path: '/pages/settings/about' }
    ]
  },
  {
    items: [
      { id: 'logout', icon: 'close', text: '退出登录', action: 'logout' }
    ]
  }
])

// 页面跳转
const navigateTo = (path) => {
  // tabBar页面列表
  const tabBarPages = ['/pages/notes/notes']
  
  if (tabBarPages.includes(path)) {
    uni.switchTab({ url: path })
  } else {
    uni.navigateTo({ url: path })
  }
}

// 处理菜单项点击
const handleMenuItem = (item) => {
  if (item.action) {
    switch (item.action) {
      case 'logout':
        handleLogout()
        break
    }
  } else if (item.path) {
    navigateTo(item.path)
  }
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await userStore.logout()
        // 退出后跳转到登录页
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

const setSingleStat = (tag,val) => {
	const filterStats = stats.value.filter(item=>item.text===tag)
	filterStats[0].num = val
}

// 获取统计数据
const getLearnningStats = async (studentId) => {
  if (!userStore.userInfo) {
    return
  }
  try {
    const response1 = await request({
      url: '/students/learning_stats/',
      method: 'GET'
    })
    setSingleStat('课程', response1.totalCourses)
    setSingleStat('作业', response1.totalHomeworks)
    setSingleStat('笔记', response1.totalNotes)
    
    const response2 = await request({
      url: `/students/${studentId}/classes/`,
      method: 'GET'
    })
    setSingleStat('班级', response2.length)
     
  } catch (error) {
    console.log('获取统计数据失败', error)
  }
}

onMounted(() => {
  if (userStore.userInfo && userStore.userInfo.role_info) {
    getLearnningStats(userStore.userInfo.role_info.id)
  }
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

.user-card {
  background: #fff;
  padding: 40rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .info {
      .name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      .role {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
  
  .points {
    text-align: center;
    
    .points-num {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff9900;
      margin-bottom: 6rpx;
    }
    
    .points-text {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.stats {
  background: #fff;
  padding: 30rpx 0;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  margin-bottom: 20rpx;
  
  .stat-item {
    text-align: center;
    
    .num {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 6rpx;
    }
    
    .text {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.menu-list {
  .menu-group {
    margin-bottom: 20rpx;
    
    .group-title {
      font-size: 28rpx;
      color: #999;
      padding: 20rpx;
    }
    
    .menu-items {
      background: #fff;
      
      .menu-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 20rpx;
        border-bottom: 1rpx solid #eee;
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-left {
          display: flex;
          align-items: center;
          
          .item-text {
            font-size: 28rpx;
            color: #333;
            margin-left: 20rpx;
          }
        }
      }
    }
  }
}
</style> 