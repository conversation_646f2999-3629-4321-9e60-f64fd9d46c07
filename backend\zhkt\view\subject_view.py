from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.parsers import J<PERSON>NParser

from ..services.subject_service import SubjectService
from ..utils.response import ResponseResult


class SubjectViewSet(ViewSet):
    """学科相关接口视图集"""
    permission_classes = [IsAuthenticated]
    parser_classes = [JSONParser]

    @action(detail=False, methods=['get'], url_path='list')
    def get_subjects(self, request):
        """
        获取学科列表
            
        Returns:
            Response: 包含学科列表
        """
        try:
            # 调用服务获取学科列表
            subjects = SubjectService.get_subject_list()
            
            return ResponseResult.success(
                data=subjects,
                message='获取学科列表成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取学科列表失败: {str(e)}'
            )
    
    @action(detail=True, methods=['get'], url_path='detail')
    def get_subject_detail(self, request, pk=None):
        """
        获取学科详情
        
        URL参数:
            pk: 学科ID
            
        Returns:
            Response: 包含学科详情
        """
        try:
            # 获取学科ID
            subject_id = pk
            
            # 校验参数
            if not subject_id:
                return ResponseResult.error(
                    code=400,
                    message='缺少必要参数: 学科ID'
                )
            
            # 调用服务获取学科详情
            subject = SubjectService.get_subject_by_id(subject_id)
            
            # 转换为字典
            subject_data = {
                'id': subject.id,
                'name': subject.name,
                'code': subject.code,
                'description': subject.description,
                'level': subject.level,
                'icon': subject.icon,
                'sort_order': subject.sort_order,
                'status': subject.status,
                'parent_id': subject.parent_id
            }
            
            return ResponseResult.success(
                data=subject_data,
                message='获取学科详情成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取学科详情失败: {str(e)}'
            )
    
    @action(detail=False, methods=['get'], url_path='tree')
    def get_subject_tree(self, request):
        """
        获取学科树形结构
            
        Returns:
            Response: 包含学科树形结构
        """
        try:
            # 调用服务获取学科树形结构
            subjects = SubjectService.get_subject_tree()
            
            return ResponseResult.success(
                data=subjects,
                message='获取学科树形结构成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取学科树形结构失败: {str(e)}'
            ) 