<template>
  <view class="scan-page">
    <scan-code
      @success="handleScanSuccess"
      @error="handleScanError"
    />
  </view>
</template>

<script>
import ScanCode from '@/components/scan-code/index.vue'

export default {
  components: {
    ScanCode
  },

  methods: {
    // 处理扫码成功
    handleScanSuccess(result) {
      try {
        const data = JSON.parse(result)
        switch (data.type) {
          case 'class':
            // 跳转到加入班级
            uni.navigateTo({
              url: `/pages/scan/joinClass?code=${data.code}`
            })
            break
          case 'sign':
            // 处理签到
            this.handleSign(data)
            break
          default:
            this.$toast.info('无法识别的二维码类型')
            break
        }
      } catch (error) {
        this.$toast.error('无效的二维码')
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },

    // 处理扫码错误
    handleScanError(error) {
      this.$toast.error('扫码失败，请重试')
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    },

    // 处理签到
    async handleSign(data) {
      try {
        this.$toast.success('签到成功')
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        this.$toast.error('签到失败')
      }
    }
  }
}
</script>

<style lang="scss">
.scan-page {
  width: 100vw;
  height: 100vh;
  background: #000;
}
</style> 