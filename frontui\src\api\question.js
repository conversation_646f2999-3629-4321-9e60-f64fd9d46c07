import request from '@/utils/request'

// 获取题目列表
export function getQuestions(params) {
  return request({
    url: '/questions/',
    method: 'get',
    params
  })
}

// 创建题目
export function createQuestion(data) {
  return request({
    url: '/questions/',
    method: 'post',
    data
  })
}

// 更新题目
export function updateQuestion(id, data) {
  return request({
    url: `/questions/${id}/`,
    method: 'put',
    data
  })
}

// 删除题目
export function deleteQuestion(id) {
  return request({
    url: `/questions/${id}/`,
    method: 'delete'
  })
}

// 批量创建题目
export function batchCreateQuestions(data) {
  return request({
    url: '/questions/batch_create/',
    method: 'post',
    data
  })
}

// 批量删除题目
export function batchDeleteQuestions(data) {
  return request({
    url: '/questions/batch_delete/',
    method: 'post',
    data
  })
}

// 导出题目
export function exportQuestions(params) {
  return request({
    url: '/questions/export/',
    method: 'get',
    params
  })
} 