import logging
import traceback

from django.http import StreamingHttpResponse
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser, JSONParser
from rest_framework.response import Response

from ..services.speech_design_service import SpeechDesignService
from ..utils.response import ResponseResult

# 配置日志
logger = logging.getLogger(__name__)

class SpeechDesignViewSet(viewsets.ViewSet):
    """PPT解析器视图集"""

    parser_classes = [MultiPartParser, FormParser, JSONParser]
    
    @action(detail=False, methods=['post'], url_path='parse')
    def parse_ppt(self, request):
        """
        解析上传的PPT文件内容
        
        Request:
            file: 上传的PPT文件 (.ppt或.pptx格式)
            
        Returns:
            Response: 包含解析结果的响应
                - slides: 幻灯片列表
                - total_slides: 幻灯片总数
                - title: PPT标题
                - slide_images: 幻灯片图片数据
        """
        logger.info("PPT解析接口被调用")
        logger.info(f"请求数据: {request.data}")
        logger.info(f"FILES: {request.FILES}")
        logger.info(f"请求头: {request.headers}")
        
        try:
            # 检查是否上传了文件
            if 'file' not in request.FILES:
                logger.error("未找到上传的文件")
                logger.error(f"请求文件: {request.FILES}")
                logger.error(f"请求数据: {request.data}")
                return ResponseResult.error(
                    code=400,
                    message="未上传PPT文件"
                )
            
            file = request.FILES['file']
            logger.info(f"收到文件: {file.name}, 大小: {file.size} 字节")
            
            # 调用服务层解析PPT
            result = SpeechDesignService.parse_ppt(file)

            return ResponseResult.success(
                data=result,
                message="PPT解析成功"
            )
                
        except ValueError as e:
            # 文件格式错误
            logger.error(f"文件格式错误: {str(e)}")
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except FileNotFoundError as e:
            # 未找到图片文件
            logger.error(f"未找到图片文件: {str(e)}")
            return ResponseResult.error(
                code=500,
                message=str(e)
            )
        except Exception as e:
            # 其他错误
            logger.error(f"PPT解析失败，详细错误: {str(e)}")
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"PPT解析失败: {str(e)}"
            )
    
    @action(detail=False, methods=['post'], url_path='extract-images')
    def extract_ppt_images(self, request):
        """
        提取PPT中的图片
        
        Request:
            file: 上传的PPT文件 (.ppt或.pptx格式)
            slide_index: 可选，指定提取特定幻灯片的图片，不指定则提取所有幻灯片的图片
            
        Returns:
            Response: 包含图片信息的响应
                - images: 图片列表，每个元素包含图片数据的base64编码和所属幻灯片索引
        """
        logger.info("PPT图片提取接口被调用")
        logger.info(f"请求数据: {request.data}")
        logger.info(f"FILES: {request.FILES}")
        
        try:
            # 检查是否上传了文件
            if 'file' not in request.FILES:
                logger.error("未找到上传的文件")
                return ResponseResult.error(
                    code=400,
                    message="未上传PPT文件"
                )
            
            file = request.FILES['file']
            logger.info(f"收到文件: {file.name}, 大小: {file.size} 字节")
            
            # 获取slide_index参数
            slide_index = request.data.get('slide_index')
            if slide_index is not None:
                try:
                    slide_index = int(slide_index)
                except ValueError:
                    return ResponseResult.error(
                        code=400,
                        message="slide_index必须是整数"
                    )
            
            # 调用服务层提取图片
            result = SpeechDesignService.extract_ppt_images(file, slide_index)

            return ResponseResult.success(
                data=result,
                message="PPT图片提取成功"
            )
                
        except ValueError as e:
            # 参数错误
            logger.error(f"参数错误: {str(e)}")
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            # 其他错误
            logger.error(f"PPT图片提取失败，详细错误: {str(e)}")
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"PPT图片提取失败: {str(e)}"
            )
    
    @action(detail=False, methods=['post'], url_path='generate-slide-script')
    def generate_slide_script(self, request):
        """
        生成单个幻灯片的讲稿内容
        
        请求体:
        - current_slide: 当前需要生成讲稿的幻灯片数据
        - previous_scripts: 之前已生成的讲稿内容（可选）
        - settings: 讲稿生成设置
        
        响应:
        - script: 生成的讲稿内容
        """
        try:
            data = request.data
            current_slide = data.get('current_slide', {})
            previous_scripts = data.get('previous_scripts', {})
            settings = data.get('settings', {})
            future_slides = data.get('future_slides', [])

            logger.info(f"收到单个幻灯片讲稿流式生成请求，幻灯片索引: {current_slide.get('index')}")
            logger.info(f"之前已生成讲稿数量: {len(previous_scripts)}")
            logger.info(f"讲稿生成设置: {settings}")
            
            if not current_slide:
                return ResponseResult.error(
                    code=400,
                    message='未提供幻灯片数据'
                )
                
            # 调用服务层流式生成讲稿
            stream_generator = SpeechDesignService.generate_slide_script_stream(
                current_slide,
                previous_scripts,
                settings,
                future_slides
            )

            return StreamingHttpResponse(stream_generator, content_type='text/event-stream')
            
        except Exception as e:
            logger.exception(f"幻灯片讲稿流式生成请求处理错误: {str(e)}")
            return ResponseResult.error(
                code=500,
                message=f'幻灯片讲稿生成失败: {str(e)}'
            ) 