// 导入管理员相关视图
import AdminLayout from '@/components/layout/AdminLayout.vue'
import AdminDashboard from '@/views/admin/DashboardView.vue'
import UserManagementView from '@/views/admin/UserManagementView.vue'
import CourseManagementView from '@/views/admin/CourseManagementView.vue'
import SystemSettingsView from '@/views/admin/SystemSettingsView.vue'
import DataAnalyticsView from '@/views/admin/DataAnalyticsView.vue'
import NotificationsView from '@/views/admin/NotificationsView.vue'
import StoreManagementView from '@/views/admin/StoreManagementView.vue'
import PointsManagementView from '@/views/admin/PointsManagementView.vue'
import AuditLogView from '@/views/admin/AuditLogView.vue'
import CourseDetailView from '@/views/admin/CourseDetailView.vue'
import CourseEditView from '@/views/admin/CourseEditView.vue'

// 管理员相关路由配置
const adminRoutes = [
  {
    path: '/admin',
    redirect: '/admin/dashboard'
  },
  {
    path: '/admin/dashboard',
    name: 'admin-dashboard',
    component: AdminDashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/users',
    name: 'admin-users',
    component: UserManagementView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/courses',
    name: 'admin-courses',
    component: CourseManagementView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/courses/:id',
    name: 'admin-course-detail',
    component: CourseDetailView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/courses/:id/edit',
    name: 'admin-course-edit',
    component: CourseEditView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/store',
    name: 'admin-store',
    component: StoreManagementView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/points',
    name: 'admin-points',
    component: PointsManagementView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/settings',
    name: 'admin-settings',
    component: SystemSettingsView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/analytics',
    name: 'admin-analytics',
    component: DataAnalyticsView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/notifications',
    name: 'admin-notifications',
    component: NotificationsView,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/audit',
    name: 'admin-audit',
    component: AuditLogView,
    meta: { requiresAuth: true }
  }
]

export default adminRoutes 