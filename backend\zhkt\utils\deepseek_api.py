import requests
import json
import sseclient
from typing import List, Dict, Any, Optional, Union, Generator, Callable

from .. import config


class DeepSeekAPI:
    """DeepSeek API简单封装类"""

    def __init__(self, base_url: str = "https://api.deepseek.com"):
        """
        初始化DeepSeek API客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL，默认为 https://api.deepseek.com
        """
        api_key = config.DEEPSEEK_API_KEY
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

    def chat(
        self,
        messages: List[Dict[str, Any]],
        model: str = "deepseek-chat",
        temperature: float = 1.0,
        max_tokens: int = 8192
    ) -> str:
        """
        创建聊天完成请求（一次性返回完整响应）
        
        Args:
            messages: 对话的消息列表
            model: 使用的模型，默认为 deepseek-chat
            temperature: 采样温度，介于0和2之间，默认为1.0
            max_tokens: 生成的最大token数，默认为4096
        
        Returns:
            API响应
        """
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        # 发送请求
        response = requests.post(url, headers=self.headers, json=data)
        
        # 处理响应
        return response.json().get("choices", [{}])[0].get("message", {}).get("content", "")
    
    def chat_stream(
        self,
        messages: List[Dict[str, Any]],
        stream_callback: Optional[Callable[[str], None]] = None,
        model: str = "deepseek-chat",
        temperature: float = 1.0,
        max_tokens: int = 8192
    ) -> Generator[Dict[str, str], None, None]:
        """
        创建流式聊天完成请求，返回生成器以产生每个内容块
        
        Args:
            messages: 对话的消息列表
            stream_callback: 流式内容回调函数，用于实时处理每个生成的内容块
            model: 使用的模型，默认为 deepseek-chat
            temperature: 采样温度，介于0和2之间，默认为1.0
            max_tokens: 生成的最大token数，默认为4096
        
        Returns:
            Generator: 生成器，每次产生一个包含content和reasoning的字典
        """
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True
        }
        
        # 发送请求
        response = requests.post(url, headers=self.headers, json=data, stream=True)
        client = sseclient.SSEClient(response)
        
        for event in client.events():
            if event.data == "[DONE]":
                break
            
            try:
                chunk = json.loads(event.data)
                if chunk.get("choices") and chunk["choices"][0].get("delta"):
                    delta = chunk["choices"][0]["delta"]
                    content = delta.get("content", "")
                    reasoning = delta.get("reasoning_content", "")

                    # 如果有回调函数，则调用
                    # if stream_callback:
                    #     stream_callback(content)

                    # 返回包含content和reasoning的字典
                    yield {
                        "content": content,
                        "reasoning": reasoning
                    }
            except json.JSONDecodeError:
                pass
    
    @staticmethod
    def create_system_message(content: str) -> Dict[str, str]:
        """创建system角色的消息"""
        return {"role": "system", "content": content}
    
    @staticmethod
    def create_user_message(content: str) -> Dict[str, str]:
        """创建user角色的消息"""
        return {"role": "user", "content": content}
    
    @staticmethod
    def create_assistant_message(content: str) -> Dict[str, str]:
        """创建assistant角色的消息"""
        return {"role": "assistant", "content": content} 