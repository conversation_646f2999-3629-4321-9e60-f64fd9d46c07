import os
import time
import json
import logging
import requests
from typing import Dict, List, Optional, Union, Any
from urllib.parse import urlparse

from .. import config
from .file_utils import FileUtils
from .temp_file_utils import get_temp_filepath, create_temp_subdir, clean_temp_dir, clean_temp_file

logger = logging.getLogger(__name__)

class MineruPDFUtils:
    """Mineru PDF解析工具类"""
    
    # Mineru API 端点
    MINERU_API_BASE = "https://mineru.net/api/v4"
    TASK_CREATE_ENDPOINT = "/extract/task"
    TASK_QUERY_ENDPOINT = "/extract/task/{task_id}"
    
    def __init__(self, token: str = None):
        """
        初始化Mineru PDF工具类
        
        Args:
            token: Mineru API Token，如果不提供则从配置文件读取
        """
        self.token = token or getattr(config, 'MINERU_API_TOKEN', '')
        if not self.token:
            logger.warning("Mineru API Token未配置，请在config.py中设置MINERU_API_TOKEN或在初始化时提供")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取API请求头"""
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.token}',
            'Accept': '*/*'
        }
    
    def create_extraction_task(self, 
                              file_path: str = None, 
                              file_url: str = None,
                              is_ocr: bool = True,
                              enable_formula: bool = True,
                              enable_table: bool = True,
                              language: str = "ch",
                              page_ranges: str = None,
                              model_version: str = "v2") -> Dict[str, Any]:
        """
        创建PDF解析任务
        
        Args:
            file_path: 本地文件路径，与file_url二选一
            file_url: 文件URL地址，与file_path二选一
            is_ocr: 是否启用OCR，默认True
            enable_formula: 是否启用公式识别，默认True
            enable_table: 是否启用表格识别，默认True
            language: 文档语言，默认"ch"，可选"auto"等
            page_ranges: 指定页码范围，如"1-10,15,20-30"
            model_version: 模型版本，默认"v2"，可选"v1"
            
        Returns:
            Dict: 包含task_id的响应数据
        """
        # 验证参数
        if not file_path and not file_url:
            raise ValueError("必须提供file_path或file_url参数")
            
        if file_path and file_url:
            raise ValueError("file_path和file_url不能同时提供")
        
        # 如果提供本地文件，先上传
        if file_path:
            if not os.path.exists(file_path):
                raise ValueError(f"文件不存在: {file_path}")
            
            # 上传文件，获取URL
            try:
                # 判断文件类型
                ext = os.path.splitext(file_path)[1].lower()
                if ext in ['.pdf', '.doc', '.docx', '.ppt', '.pptx']:
                    # 文档类型
                    saved_path = FileUtils.save_upload_file(
                        file_path=file_path,
                        directory=config.KNOWLEDGE_UPLOAD_DIR,
                        allowed_extensions=['pdf', 'doc', 'docx', 'ppt', 'pptx'],
                        max_size_mb=200  # Mineru限制单文件200MB
                    )
                elif ext in ['.png', '.jpg', '.jpeg']:
                    # 图片类型
                    saved_path = FileUtils.save_image_file(file_path)
                else:
                    raise ValueError(f"不支持的文件类型: {ext}，支持的类型: .pdf, .doc, .docx, .ppt, .pptx, .png, .jpg, .jpeg")
                
                # 获取完整URL
                file_url = FileUtils.get_file_url(saved_path, full_url=True)
                logger.info(f"文件已上传，URL: {file_url}")
            except Exception as e:
                raise ValueError(f"文件上传失败: {str(e)}")
        
        # 构造请求数据
        request_data = {
            "url": file_url,
            "is_ocr": is_ocr,
            "enable_formula": enable_formula,
            "enable_table": enable_table,
            "language": language,
            "model_version": model_version
        }
        
        # 添加可选参数
        if page_ranges:
            request_data["page_ranges"] = page_ranges
        
        # 发送请求创建任务
        try:
            response = requests.post(
                url=f"{self.MINERU_API_BASE}{self.TASK_CREATE_ENDPOINT}",
                headers=self._get_headers(),
                json=request_data
            )
            
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") != 0:
                raise ValueError(f"创建任务失败: {result.get('msg', '未知错误')}")
                
            task_id = result.get("data", {}).get("task_id")
            if not task_id:
                raise ValueError("响应中未包含task_id")
                
            logger.info(f"PDF解析任务创建成功，任务ID: {task_id}")
            return result["data"]
            
        except requests.RequestException as e:
            raise ValueError(f"API请求异常: {str(e)}")
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        if not task_id:
            raise ValueError("必须提供task_id参数")
            
        try:
            response = requests.get(
                url=f"{self.MINERU_API_BASE}{self.TASK_QUERY_ENDPOINT}".format(task_id=task_id),
                headers=self._get_headers()
            )
            
            response.raise_for_status()
            result = response.json()
            
            if result.get("code") != 0:
                raise ValueError(f"获取任务状态失败: {result.get('msg', '未知错误')}")
                
            logger.info(f"任务 {task_id} 状态: {result.get('data', {}).get('state')}")
            return result["data"]
            
        except requests.RequestException as e:
            raise ValueError(f"API请求异常: {str(e)}")
    
    def wait_for_task_completion(self, task_id: str, timeout: int = 600, interval: int = 5) -> Dict[str, Any]:
        """
        等待任务完成，带超时机制
        
        Args:
            task_id: 任务ID
            timeout: 超时时间(秒)，默认600秒
            interval: 查询间隔(秒)，默认5秒
            
        Returns:
            Dict: 任务完成后的状态信息
        """
        start_time = time.time()
        last_progress = None
        
        while True:
            # 检查超时
            if time.time() - start_time > timeout:
                raise TimeoutError(f"等待任务完成超时({timeout}秒)")
            
            # 获取任务状态
            task_status = self.get_task_status(task_id)
            state = task_status.get("state")
            
            # 显示进度
            if state == "running":
                progress = task_status.get("extract_progress", {})
                extracted_pages = progress.get("extracted_pages", 0)
                total_pages = progress.get("total_pages", 0)
                
                if progress != last_progress:
                    logger.info(f"解析进度: {extracted_pages}/{total_pages} 页")
                    last_progress = progress
            
            # 检查任务状态
            if state == "done":
                logger.info("任务解析完成")
                return task_status
            elif state == "failed":
                error_msg = task_status.get("err_msg", "未知错误")
                raise ValueError(f"任务解析失败: {error_msg}")
            elif state in ["pending", "running", "converting"]:
                # 继续等待
                time.sleep(interval)
            else:
                raise ValueError(f"未知的任务状态: {state}")
    
    def download_result_zip(self, zip_url: str, output_path: str = None) -> str:
        """
        下载解析结果压缩包
        
        Args:
            zip_url: 结果压缩包URL
            output_path: 输出路径，如果不提供则使用临时文件
            
        Returns:
            str: 下载文件的本地路径
        """
        if not zip_url:
            raise ValueError("必须提供zip_url参数")
        
        # 如果未提供输出路径，生成临时文件路径
        if not output_path:
            output_path = get_temp_filepath(".zip")
        
        try:
            logger.info(f"开始下载解析结果: {zip_url}")
            response = requests.get(zip_url, stream=True)
            response.raise_for_status()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 写入文件
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"解析结果下载完成: {output_path}")
            return output_path
            
        except requests.RequestException as e:
            raise ValueError(f"下载文件失败: {str(e)}")
    
    def extract_text_from_zip(self, zip_path: str) -> str:
        """
        从解析结果ZIP包中提取文本内容
        
        Args:
            zip_path: ZIP文件路径
            
        Returns:
            str: 提取的文本内容
        """
        import zipfile
        import shutil
        
        # 创建临时目录用于解压文件
        temp_dir = create_temp_subdir("mineru_extract")
        
        try:
            # 解压ZIP文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 首先查找markdown文件(优先级最高)
            markdown_files = []
            for root, _, files in os.walk(temp_dir):
                for file in files:
                    if file.endswith('.md'):
                        markdown_files.append(os.path.join(root, file))
            
            # 如果找到markdown文件，读取内容并合并
            if markdown_files:
                # 按文件名排序，确保页面顺序正确
                markdown_files.sort()
                
                # 合并所有markdown文件内容
                all_text = []
                for md_file in markdown_files:
                    with open(md_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 去除markdown标记(简单处理)
                        content = self._clean_markdown(content)
                        all_text.append(content)
                
                return "\n\n".join(all_text)
            
            # 如果没有找到markdown文件，查找txt文件
            txt_files = []
            for root, _, files in os.walk(temp_dir):
                for file in files:
                    if file.endswith('.txt'):
                        txt_files.append(os.path.join(root, file))
            
            if txt_files:
                txt_files.sort()
                all_text = []
                for txt_file in txt_files:
                    with open(txt_file, 'r', encoding='utf-8') as f:
                        all_text.append(f.read())
                
                return "\n\n".join(all_text)
            
            # 如果还没找到，尝试读取json文件
            json_files = []
            for root, _, files in os.walk(temp_dir):
                for file in files:
                    if file.endswith('.json'):
                        json_files.append(os.path.join(root, file))
            
            if json_files:
                import json
                all_text = []
                for json_file in json_files:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        try:
                            data = json.load(f)
                            # 尝试提取JSON中的文本内容
                            if isinstance(data, list):
                                for item in data:
                                    if isinstance(item, dict) and 'text' in item:
                                        all_text.append(item['text'])
                            elif isinstance(data, dict):
                                if 'text' in data:
                                    all_text.append(data['text'])
                                elif 'content' in data:
                                    all_text.append(data['content'])
                        except:
                            continue
                
                if all_text:
                    return "\n\n".join(all_text)
            
            # 没有找到任何可读取的文本文件
            return "未能从解析结果中提取文本内容"
            
        except Exception as e:
            logger.error(f"提取文本内容失败: {str(e)}")
            return f"解析文本失败: {str(e)}"
        finally:
            # 清理临时目录
            clean_temp_dir(temp_dir)
    
    def _clean_markdown(self, markdown_text: str) -> str:
        """简单清理Markdown标记"""
        import re
        
        # 移除标题标记 (# 标题)
        text = re.sub(r'^#+\s+', '', markdown_text, flags=re.MULTILINE)
        
        # 移除粗体和斜体
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        text = re.sub(r'\*(.*?)\*', r'\1', text)
        text = re.sub(r'__(.*?)__', r'\1', text)
        text = re.sub(r'_(.*?)_', r'\1', text)
        
        # 移除链接 [文本](链接)
        text = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', text)
        
        # 移除图片标记 ![alt](url)
        text = re.sub(r'!\[(.*?)\]\(.*?\)', '', text)
        
        # 移除代码块
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
        text = re.sub(r'`(.*?)`', r'\1', text)
        
        # 移除水平线
        text = re.sub(r'^\s*[\*\-_]{3,}\s*$', '', text, flags=re.MULTILINE)
        
        # 移除列表标记
        text = re.sub(r'^\s*[\*\-+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)
        
        # 移除表格
        text = re.sub(r'\|.*\|', '', text)
        text = re.sub(r'^\s*[-:]+\s*$', '', text, flags=re.MULTILINE)
        
        # 移除多余空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        return text.strip()
    
    def get_text_from_pdf(self, 
                       file_path: str = None, 
                       file_url: str = None,
                       **kwargs) -> str:
        """
        处理PDF并直接返回提取的文本内容（简化版接口）
        
        Args:
            file_path: 本地文件路径
            file_url: 文件URL
            **kwargs: 其他传递给process_pdf的参数
            
        Returns:
            str: 提取的文本内容
        """
        try:
            # 处理PDF
            result = self.process_pdf(
                file_path=file_path,
                file_url=file_url,
                wait_for_result=True,
                download_result=True,
                **kwargs
            )
            
            # 检查处理状态
            if result.get('status') != 'done':
                error_msg = result.get('error', '处理失败，未知错误')
                logger.error(f"PDF处理失败: {error_msg}")
                return f"文本提取失败: {error_msg}"
            
            # 提取文本
            zip_path = result.get('local_path')
            if not zip_path or not os.path.exists(zip_path):
                return "处理成功但未找到结果文件"
            
            # 从ZIP包中提取文本
            text_content = self.extract_text_from_zip(zip_path)
            
            # 清理临时ZIP文件
            try:
                clean_temp_file(zip_path)
            except:
                pass
                
            return text_content
            
        except Exception as e:
            logger.error(f"获取文本失败: {str(e)}")
            return f"文本提取过程出错: {str(e)}"
    
    def process_pdf(self, 
                   file_path: str = None, 
                   file_url: str = None,
                   wait_for_result: bool = True,
                   download_result: bool = True,
                   output_path: str = None,
                   **kwargs) -> Dict[str, Any]:
        """
        处理PDF文件的完整流程：上传、解析、等待结果、下载
        
        Args:
            file_path: 本地文件路径，与file_url二选一
            file_url: 文件URL地址，与file_path二选一
            wait_for_result: 是否等待解析完成，默认True
            download_result: 是否下载结果，默认True
            output_path: 结果文件输出路径，仅当download_result=True时有效
            **kwargs: 其他传递给create_extraction_task的参数
            
        Returns:
            Dict: 处理结果
        """
        # 1. 创建解析任务
        task_data = self.create_extraction_task(file_path=file_path, file_url=file_url, **kwargs)
        task_id = task_data.get("task_id")
        
        result = {
            "task_id": task_id,
            "status": "created"
        }
        
        # 2. 等待解析完成
        if wait_for_result:
            try:
                task_status = self.wait_for_task_completion(task_id)
                result.update({
                    "status": task_status.get("state"),
                    "zip_url": task_status.get("full_zip_url")
                })
                
                # 3. 下载结果
                if download_result and task_status.get("state") == "done":
                    zip_url = task_status.get("full_zip_url")
                    if zip_url:
                        local_path = self.download_result_zip(zip_url, output_path)
                        result["local_path"] = local_path
            except Exception as e:
                result["status"] = "error"
                result["error"] = str(e)
        
        return result

# 使用示例
"""
# 示例1：处理本地PDF文件
from zhkt.utils.mineru_utils import MineruPDFUtils

# 初始化工具类
mineru = MineruPDFUtils()

# 完整处理流程（上传、解析、等待结果、下载）
result = mineru.process_pdf(
    file_path="/path/to/your/document.pdf",  # 本地PDF文件路径
    is_ocr=True,                            # 启用OCR
    language="ch"                           # 中文文档
)

# 查看解析结果
print(f"任务ID: {result['task_id']}")
print(f"状态: {result['status']}")
if result['status'] == 'done':
    print(f"结果ZIP包: {result['zip_url']}")
    print(f"本地保存路径: {result['local_path']}")


# 示例2：异步方式处理，不等待结果
task_data = mineru.create_extraction_task(
    file_path="/path/to/your/document.pdf"
)
task_id = task_data['task_id']
print(f"任务已创建，ID: {task_id}")

# 之后可以通过任务ID查询状态
# task_status = mineru.get_task_status(task_id)


# 示例3：处理已有URL的文档
result = mineru.process_pdf(
    file_url="https://example.com/path/to/document.pdf",
    wait_for_result=True,
    download_result=True
)

# 示例4：直接获取文本内容(推荐用法)
text_content = mineru.get_text_from_pdf(
    file_path="/path/to/your/document.pdf",
    is_ocr=True
)
print(text_content)  # 直接输出提取的文本内容
"""
