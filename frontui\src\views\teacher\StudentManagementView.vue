<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="学生管理"
    activePage="student-management"
  >
    <div class="flex h-full">
      <!-- 左侧班级列表 -->
      <div class="w-64 bg-gray-50 border-r border-gray-200 p-4 flex-shrink-0 overflow-y-auto">
        <div class="mb-4 flex items-center justify-between">
          <span class="font-bold text-lg text-gray-700">班级列表</span>
        </div>
        <div class="mb-4 flex gap-2">
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded flex items-center gap-1 text-sm" @click="onCreateClass">
            <span class="material-icons" style="font-size: 14px;">add</span> 新建班级
          </button>
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded flex items-center gap-1 text-sm" @click="onManageClass">
            <span class="material-icons" style="font-size: 14px;">settings</span> 班级管理
          </button>
        </div>
        <div class="mb-4 flex items-center justify-between">
          <input type="text" v-model="searchQuery" placeholder="搜索班级" class="border border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
        </div>
        <ul>
          <li
            v-for="cls in filteredClassList"
            :key="cls.id"
            @click="selectClass(cls)"
            :class="[
              'px-4 py-2 rounded cursor-pointer mb-1',
              selectedClass && selectedClass.id === cls.id ? 'bg-blue-100 text-blue-700 font-semibold' : 'hover:bg-blue-50 text-gray-700'
            ]"
          >
            {{ cls.name }}
            <span class="text-xs text-gray-400 ml-2">({{ getStudentCountByClass(cls.name) }})</span>
          </li>
        </ul>
      </div>
      <!-- 右侧学生管理区 -->
      <div class="flex-1 p-8 overflow-y-auto">
        <div v-if="selectedClass" class="mb-6">
          <div class="flex items-center gap-4 mb-2">
            <h2 class="text-2xl font-bold text-gray-800">{{ selectedClass.name }}</h2>
            <span class="text-gray-500 mt-1">共 {{ filteredStudents.length }} 人</span>
            <button class="ml-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded flex items-center gap-1 text-sm mt-1" @click="onClassSettings">
              <span class="material-icons" style="font-size: 14px;">settings</span> 设置
            </button>
          </div>
          <div class="text-gray-500 text-sm mb-4">班级简介：{{ selectedClass.desc || '暂无简介' }}</div>
        </div>
        <!-- 右侧原有功能区和表格 -->
        <div class="space-y-6">
          <!-- 功能区 -->
          <div class="flex flex-wrap justify-between items-center gap-4 mb-4">
            <div class="flex gap-3">
              <button 
                class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
                @click="showAddStudentModal = true"
              >
                <span class="material-icons text-sm">person_add</span>
                添加学生
              </button>
              <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2" @click="showImportModal = true">
                <span class="material-icons text-sm">file_upload</span>
                批量导入
              </button>
              <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2" @click="exportStudentList">
                <span class="material-icons text-sm">file_download</span>
                导出名单
              </button>
            </div>
            <div class="flex gap-3">
              <div class="relative">
                <input 
                  type="text" 
                  v-model="searchQuery"
                  placeholder="搜索学生..." 
                  class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
              </div>
              <div class="relative">
                <button 
                  @click="showFilters = !showFilters"
                  class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
                >
                  <span class="material-icons text-gray-600">filter_list</span>
                  筛选
                </button>
                <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
                  <div class="space-y-3">
                    <div class="font-medium">班级</div>
                    <div class="flex flex-wrap gap-2">
                      <span 
                        v-for="cls in classList" 
                        :key="cls.id"
                        class="px-3 py-1 rounded-full text-sm cursor-pointer"
                        :class="selectedFilters.classes.includes(cls.name) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                        @click="toggleFilter('classes', cls.name)"
                      >
                        {{ cls.name }}
                      </span>
                    </div>
                    <div class="font-medium mt-3">状态</div>
                    <div class="flex flex-wrap gap-2">
                      <span 
                        v-for="status in ['活跃', '较少活动', '未激活']" 
                        :key="status"
                        class="px-3 py-1 rounded-full text-sm cursor-pointer"
                        :class="selectedFilters.statuses.includes(status) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                        @click="toggleFilter('statuses', status)"
                      >
                        {{ status }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计卡片 -->
          <div class="flex flex-wrap gap-4">
            <div class="bg-blue-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-gray-500 text-sm">总学生数量</p>
                  <p class="text-2xl font-bold text-gray-800">482</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <span class="material-icons text-blue-600 text-xl">groups</span>
                </div>
              </div>
            </div>
            <div class="bg-green-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-gray-500 text-sm">活跃学生数</p>
                  <p class="text-2xl font-bold text-gray-800">423</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <span class="material-icons text-green-600 text-xl">how_to_reg</span>
                </div>
              </div>
            </div>
            <div class="bg-yellow-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-gray-500 text-sm">较少活动</p>
                  <p class="text-2xl font-bold text-gray-800">178</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                  <span class="material-icons text-yellow-600 text-xl">login</span>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-gray-500 text-sm">未激活账号数</p>
                  <p class="text-2xl font-bold text-gray-800">59</p>
                </div>
                <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                  <span class="material-icons text-gray-600 text-xl">person_off</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分类筛选区 -->
          <div class="flex border-b border-gray-200">
            <button 
              v-for="tab in tabs" 
              :key="tab.value"
              @click="currentTab = tab.value"
              class="py-3 px-6 font-medium relative"
              :class="currentTab === tab.value ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'"
            >
              {{ tab.label }}
              <span 
                v-if="currentTab === tab.value" 
                class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"
              ></span>
            </button>
          </div>

          <!-- 表格控制区 -->
          <div class="flex justify-between items-center mb-4 mt-6">
            <div class="flex items-center">
              <input 
                type="checkbox" 
                id="select-all"
                class="rounded text-blue-600 focus:ring-blue-500"
                @change="toggleSelectAll"
                :checked="selectedStudents.length === filteredStudents.length && filteredStudents.length > 0"
              />
              <label for="select-all" class="ml-2 text-sm text-gray-600">全选</label>
              
              <div class="ml-4 flex items-center" v-if="selectedStudents.length > 0">
                <span class="text-sm text-gray-600">已选择 {{ selectedStudents.length }} 名学生</span>
                <button class="ml-3 text-sm text-blue-600 hover:text-blue-800">批量操作</button>
              </div>
            </div>
            
            <div class="flex items-center gap-4">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 mr-2">每页显示：</span>
                <select 
                  v-model="pageSize" 
                  class="border border-gray-300 rounded-md px-2 py-1 text-sm"
                >
                  <option :value="20">20条</option>
                  <option :value="50">50条</option>
                  <option :value="100">100条</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 学生表格 -->
          <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="w-12 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    选择
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学生信息
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学号
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    班级/专业
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学习状态
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    平均成绩
                  </th>
                  <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="student in paginatedStudents" :key="student.id" class="hover:bg-gray-50 cursor-pointer" @click="viewStudentDetails(student)">
                  <td class="px-2 py-4 whitespace-nowrap" @click.stop>
                    <input 
                      type="checkbox" 
                      :checked="selectedStudents.includes(student.id)"
                      @change="toggleStudentSelection(student.id)"
                      class="rounded text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <img class="h-8 w-8 rounded-full object-cover" :src="student.avatarUrl" :alt="student.name" />
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">{{ student.name }}</div>
                        <div class="text-sm text-gray-500">{{ student.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ student.studentId }}</div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ student.className }}</div>
                    <div class="text-sm text-gray-500">{{ student.major }}</div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" 
                      :class="{
                        'bg-green-100 text-green-800': student.status === 'active',
                        'bg-yellow-100 text-yellow-800': student.status === 'less-active',
                        'bg-gray-100 text-gray-800': student.status === 'inactive'
                      }">
                      {{ getStatusLabel(student.status) }}
                    </span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div v-if="student.averageGrade" class="text-sm text-gray-900 flex items-center">
                      <span class="mr-1">{{ student.averageGrade }}</span>
                      <span class="material-icons text-yellow-400 text-xs" v-if="student.averageGrade >= 90">star</span>
                    </div>
                    <div v-else class="text-sm text-gray-400">暂无评分</div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium" @click.stop>
                    <button class="text-blue-600 hover:text-blue-900 mr-2" @click="viewStudentDetails(student)">详情</button>
                    <button class="text-green-600 hover:text-green-900 mr-2" @click="editStudent(student)">编辑</button>
                    <button class="text-red-600 hover:text-red-900" @click="showRemoveStudentModal(student)">迁出</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页控制 -->
          <div class="mt-5 flex justify-between items-center">
            <div class="text-sm text-gray-700">
              共 <span class="font-medium">{{ students.length }}</span> 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button 
                class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                :disabled="currentPage === 1"
                @click="currentPage--"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
              >
                上一页
              </button>
              
              <div v-for="pageNumber in displayedPageNumbers" :key="pageNumber">
                <button 
                  v-if="pageNumber !== '...'"
                  @click="currentPage = pageNumber"
                  class="px-3 py-1 rounded-md text-sm font-medium"
                  :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'"
                >
                  {{ pageNumber }}
                </button>
                <span v-else class="text-gray-500 px-2">...</span>
              </div>
              
              <button 
                class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
              >
                下一页
              </button>
              
              <div class="flex items-center ml-2">
                <span class="text-sm text-gray-700 mr-2">前往</span>
                <input 
                  type="number" 
                  v-model.number="goToPage" 
                  min="1" 
                  :max="totalPages"
                  class="w-12 border border-gray-300 rounded-md px-2 py-1 text-sm"
                />
                <span class="text-sm text-gray-700 mx-2">页</span>
                <button 
                  @click="jumpToPage"
                  class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-blue-700 hover:bg-blue-50"
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加学生模态框 -->
    <div v-if="showAddStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-xl" @click.stop>
        <!-- 模态框头部 -->
        <div class="bg-blue-50 p-6 border-b border-blue-100 flex justify-between items-center">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="material-icons text-blue-600">person_add</span>
            </div>
            <h2 class="text-xl font-bold text-gray-800">添加学生</h2>
          </div>
          <button @click="showAddStudentModal = false" class="text-gray-500 hover:text-gray-700 transition-colors">
            <span class="material-icons text-xl">close</span>
          </button>
        </div>
        
        <form @submit.prevent="addStudent" class="p-6">
          <div class="space-y-5">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <input 
                    type="text" 
                    v-model="newStudent.name" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入学生姓名"
                    required
                  />
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">person</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">学号 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <input 
                    type="text" 
                    v-model="newStudent.studentId" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入学号"
                    required
                  />
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">badge</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">身份证号 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <input 
                    type="text" 
                    v-model="newStudent.idCard" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入身份证号"
                    required
                  />
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">assignment_ind</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">性别 <span class="text-red-500">*</span></label>
                <div class="flex gap-4 mt-2">
                  <label class="inline-flex items-center">
                    <input type="radio" v-model="newStudent.gender" value="male" class="form-radio text-blue-600" />
                    <span class="ml-2">男</span>
                  </label>
                  <label class="inline-flex items-center">
                    <input type="radio" v-model="newStudent.gender" value="female" class="form-radio text-blue-600" />
                    <span class="ml-2">女</span>
                  </label>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">出生日期</label>
                <div class="relative">
                  <input 
                    type="date" 
                    v-model="newStudent.birthday" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">cake</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">民族</label>
                <div class="relative">
                  <select 
                    v-model="newStudent.ethnicity" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option v-for="ethnicity in ethnicityOptions" :key="ethnicity" :value="ethnicity">{{ ethnicity }}</option>
                  </select>
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">people</span>
                  <span class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">arrow_drop_down</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">联系电话 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <input 
                    type="tel" 
                    v-model="newStudent.phone" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入手机号码"
                    required
                  />
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">phone</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">邮箱 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <input 
                    type="email" 
                    v-model="newStudent.email" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入邮箱"
                    required
                  />
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">email</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">班级 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <select 
                    v-model="newStudent.className" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">请选择班级</option>
                    <option value="计算机1班">计算机1班</option>
                    <option value="计算机2班">计算机2班</option>
                    <option value="软件工程1班">软件工程1班</option>
                    <option value="软件工程2班">软件工程2班</option>
                  </select>
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">groups</span>
                  <span class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">arrow_drop_down</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">专业 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <select 
                    v-model="newStudent.major" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">请选择专业</option>
                    <option value="计算机科学与技术">计算机科学与技术</option>
                    <option value="软件工程">软件工程</option>
                    <option value="人工智能">人工智能</option>
                    <option value="网络工程">网络工程</option>
                  </select>
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">school</span>
                  <span class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">arrow_drop_down</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">入学日期 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <input 
                    type="date" 
                    v-model="newStudent.enrollmentDate" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">event</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <div class="relative">
                  <select 
                    v-model="newStudent.status" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="active">活跃</option>
                    <option value="less-active">较少活动</option>
                    <option value="inactive">未激活</option>
                  </select>
                  <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">schedule</span>
                  <span class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">arrow_drop_down</span>
                </div>
              </div>
            </div>
            
            <!-- 上传头像 -->
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">学生头像</label>
              <div 
                class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md cursor-pointer hover:bg-gray-50"
                @click="$refs.avatarInput.click()"
              >
                <div class="space-y-1 text-center">
                  <div v-if="!newStudent.avatarPreview">
                    <span class="material-icons text-5xl text-gray-400">account_circle</span>
                    <div class="flex text-sm text-gray-600 mt-2">
                      <p class="pl-1">点击上传头像</p>
                    </div>
                    <p class="text-xs text-gray-500">PNG, JPG 格式，不超过 1MB</p>
                  </div>
                  <div v-else class="relative">
                    <img :src="newStudent.avatarPreview" alt="头像预览" class="h-32 w-32 rounded-full object-cover mx-auto" />
                    <button 
                      type="button"
                      @click.stop="removeAvatar"
                      class="absolute top-0 right-0 bg-red-100 text-red-600 rounded-full p-1"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
                <input 
                  ref="avatarInput"
                  type="file" 
                  class="hidden" 
                  accept="image/*"
                  @change="handleAvatarUpload"
                />
              </div>
            </div>
            
            <!-- 发送邀请选项 -->
            <div class="mt-4">
              <label class="flex items-center gap-2">
                <input 
                  type="checkbox" 
                  v-model="newStudent.sendInvitation" 
                  class="rounded text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm text-gray-700">向学生发送邀请邮件</span>
              </label>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="mt-8 flex justify-end gap-3 pt-5 border-t border-gray-200">
            <button 
              type="button" 
              @click="showAddStudentModal = false"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button 
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              添加学生
            </button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- 学生详情模态框 -->
    <div v-if="selectedStudent" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-xl" @click.stop>
        <!-- 模态框头部 -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-700 p-6 border-b flex justify-between items-center">
          <div class="flex items-center gap-4">
            <el-avatar :size="64" :src="selectedStudent.avatarUrl" class="border-2 border-white shadow-md" />
            <div class="text-white">
              <h2 class="text-xl font-bold">{{ selectedStudent.name }}</h2>
              <div class="flex gap-2 items-center mt-1">
                <el-tag size="small" type="info" effect="plain" class="border-white text-white bg-white/20">
                  {{ selectedStudent.studentId }}
                </el-tag>
                <el-tag size="small" type="info" effect="plain" class="border-white text-white bg-white/20">
                  {{ selectedStudent.className }}
                </el-tag>
              </div>
            </div>
          </div>
          <el-button 
            @click="closeStudentDetails" 
            circle 
            type="danger"
            :icon="Close"
            class="flex items-center justify-center"
          />
        </div>
        
        <!-- 学生信息 -->
        <div class="p-6">
          <el-tabs type="border-card">
            <!-- 基本信息选项卡 -->
            <el-tab-pane>
              <template #label>
                <div class="flex items-center gap-2">
                  <el-icon><User /></el-icon>
                  <span>基本信息</span>
                </div>
              </template>
              
              <div class="p-4">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="姓名">
                    <el-tag>{{ selectedStudent.name }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="学号">{{ selectedStudent.studentId }}</el-descriptions-item>
                  <el-descriptions-item label="身份证号">{{ selectedStudent.idCard || '330************1234' }}</el-descriptions-item>
                  <el-descriptions-item label="性别">{{ selectedStudent.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
                  <el-descriptions-item label="出生日期">{{ selectedStudent.birthday || '1998-01-01' }}</el-descriptions-item>
                  <el-descriptions-item label="民族">{{ selectedStudent.ethnicity || '汉族' }}</el-descriptions-item>
                  <el-descriptions-item label="联系电话">{{ selectedStudent.phone || '13800138000' }}</el-descriptions-item>
                  <el-descriptions-item label="邮箱">{{ selectedStudent.email }}</el-descriptions-item>
                  <el-descriptions-item label="入学日期">{{ selectedStudent.enrollmentDate || '2023-09-01' }}</el-descriptions-item>
                  <el-descriptions-item label="班级/专业">{{ selectedStudent.className }} / {{ selectedStudent.major }}</el-descriptions-item>
                  <el-descriptions-item label="学习状态">
                    <el-tag :type="getStatusType(selectedStudent.status)">
                      {{ getStatusLabel(selectedStudent.status) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="平均成绩">
                    <template v-if="selectedStudent.averageGrade">
                      <div class="flex items-center">
                        {{ selectedStudent.averageGrade }}
                        <el-icon v-if="selectedStudent.averageGrade >= 90" class="text-yellow-400 ml-1"><Star /></el-icon>
                      </div>
                    </template>
                    <template v-else>暂无</template>
                  </el-descriptions-item>
                  <el-descriptions-item label="课程报名">{{ selectedStudent.enrolledCourses }} 门课程</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-tab-pane>
            
            <!-- 课程选项卡 -->
            <el-tab-pane>
              <template #label>
                <div class="flex items-center gap-2">
                  <el-icon><Reading /></el-icon>
                  <span>课程学习</span>
                </div>
              </template>
              
              <div class="p-4">
                <div class="text-gray-700 mb-4 flex justify-between items-center">
                  <h3 class="text-lg font-medium">已报名 {{ selectedStudent.enrolledCourses }} 门课程</h3>
                  <el-button type="primary" size="small" @click="handleViewAllCourses">查看全部</el-button>
                </div>
                
                <el-card v-for="(course, index) in sampleCourses" :key="index" class="mb-4" shadow="hover">
                  <div class="flex justify-between items-start">
                    <div>
                      <div class="text-lg font-medium mb-1">{{ course.name }}</div>
                      <div class="text-sm text-gray-500">{{ course.term }}</div>
                    </div>
                    <el-tag :type="course.status === '进行中' ? 'success' : 'info'">
                      {{ course.status }}
                    </el-tag>
                  </div>
                  <div class="mt-4 flex justify-between items-center">
                    <div class="w-48">
                      <div class="text-sm text-gray-600 mb-1 flex justify-between">
                        <span>学习进度</span>
                        <span>{{ course.progress }}%</span>
                      </div>
                      <el-progress :percentage="course.progress" :stroke-width="8" />
                    </div>
                    <div class="text-sm">
                      <span class="text-gray-500">成绩：</span>
                      <span class="font-medium">{{ course.grade }}分</span>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-tab-pane>
            
            <!-- 学习活动选项卡 -->
            <el-tab-pane>
              <template #label>
                <div class="flex items-center gap-2">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>学习活动</span>
                </div>
              </template>
              
              <div class="p-4">
                <el-timeline>
                  <el-timeline-item
                    v-for="(activity, index) in studentActivities"
                    :key="index"
                    :type="activity.type"
                    :color="activity.color"
                    :timestamp="activity.timestamp"
                  >
                    <el-card>
                      <h4>{{ activity.title }}</h4>
                      <p class="text-sm text-gray-600">{{ activity.description }}</p>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-tab-pane>
          </el-tabs>
          
          <!-- 操作按钮 -->
          <div class="mt-8 flex justify-end gap-3 pt-5 border-t border-gray-200">
            <el-button type="primary" @click="editStudent(selectedStudent)">
              <el-icon class="mr-1"><Edit /></el-icon> 编辑信息
            </el-button>
            <el-button type="danger" @click="showRemoveStudentModal(selectedStudent)">
              <el-icon class="mr-1"><Delete /></el-icon> 迁出学生
            </el-button>
            <el-button @click="closeStudentDetails">关闭</el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 新建班级模态框 -->
    <div v-if="showAddClassModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-md w-full shadow-xl" @click.stop>
        <div class="bg-blue-50 p-4 border-b border-blue-100 flex justify-between items-center">
          <div class="flex items-center gap-2">
            <span class="material-icons text-blue-600" style="font-size: 20px;">group_add</span>
            <h2 class="text-lg font-bold text-gray-800">新建班级</h2>
          </div>
          <button @click="showAddClassModal = false" class="text-gray-500 hover:text-gray-700">
            <span class="material-icons">close</span>
          </button>
        </div>
        <form @submit.prevent="addClass" class="p-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">班级名称 <span class="text-red-500">*</span></label>
              <input type="text" v-model="newClass.name" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入班级名称" required />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">班级简介</label>
              <textarea v-model="newClass.desc" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="可选：填写班级简介"></textarea>
            </div>
          </div>
          <div class="mt-6 flex justify-end gap-2">
            <button type="button" @click="showAddClassModal = false" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">确定</button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- 批量导入模态框 -->
    <div v-if="showImportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-md w-full shadow-xl" @click.stop>
        <div class="bg-blue-50 p-4 border-b border-blue-100 flex justify-between items-center">
          <div class="flex items-center gap-2">
            <span class="material-icons text-blue-600" style="font-size: 20px;">file_upload</span>
            <h2 class="text-lg font-bold text-gray-800">批量导入学生</h2>
          </div>
          <button @click="showImportModal = false" class="text-gray-500 hover:text-gray-700">
            <span class="material-icons">close</span>
          </button>
        </div>
        <form class="p-6">
          <div class="space-y-4">
            <div>
              <input type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" @change="handleImportFile" />
            </div>
            <div v-if="importPreview.length > 0" class="bg-gray-50 border rounded p-2 text-xs text-gray-700">
              <div>预览（前5行）：</div>
              <div v-for="(row, idx) in importPreview.slice(0,5)" :key="idx">{{ row.join(', ') }}</div>
            </div>
          </div>
          <div class="mt-6 flex justify-end gap-2">
            <button type="button" @click="showImportModal = false" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
            <button type="button" @click="confirmImport" :disabled="importData.length === 0" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">导入</button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- 编辑学生信息模态框 -->
    <div v-if="showEditStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-xl" @click.stop>
        <div class="bg-green-50 p-6 border-b border-green-100 flex justify-between items-center">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <span class="material-icons text-green-600">edit</span>
            </div>
            <h2 class="text-xl font-bold text-gray-800">编辑学生信息</h2>
          </div>
          <button @click="closeEditStudentModal" class="text-gray-500 hover:text-gray-700 transition-colors">
            <span class="material-icons text-xl">close</span>
          </button>
        </div>
        <form @submit.prevent="saveEditStudent" class="p-6">
          <div class="space-y-5">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                <input type="text" v-model="editStudentForm.name" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">学号 <span class="text-red-500">*</span></label>
                <input type="text" v-model="editStudentForm.studentId" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">邮箱 <span class="text-red-500">*</span></label>
                <input type="email" v-model="editStudentForm.email" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">班级 <span class="text-red-500">*</span></label>
                <input type="text" v-model="editStudentForm.className" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">专业 <span class="text-red-500">*</span></label>
                <input type="text" v-model="editStudentForm.major" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select v-model="editStudentForm.status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500">
                  <option value="active">活跃</option>
                  <option value="less-active">较少活动</option>
                  <option value="inactive">未激活</option>
                </select>
              </div>
            </div>
          </div>
          <div class="mt-8 flex justify-end gap-3 pt-5 border-t border-gray-200">
            <button type="button" @click="closeEditStudentModal" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
            <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">保存</button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- 迁出学生确认模态框 -->
    <div v-if="showRemoveModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-sm w-full shadow-xl" @click.stop>
        <div class="bg-red-50 p-4 border-b border-red-100 flex items-center gap-2">
          <span class="material-icons text-red-600">warning</span>
          <h2 class="text-lg font-bold text-gray-800">确认迁出学生</h2>
        </div>
        <div class="p-6 text-gray-700">
          确认要将学生 <span class="font-bold text-red-600">{{ studentToRemove?.name }}</span>（学号：{{ studentToRemove?.studentId }}）从班级迁出吗？
        </div>
        <div class="flex justify-end gap-2 px-6 pb-6">
          <button @click="closeRemoveModal" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
          <button @click="removeStudent" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">确认迁出</button>
        </div>
      </div>
    </div>
    
    <!-- 班级设置模态框 -->
    <div v-if="showClassSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-2xl w-full shadow-xl" style="height:600px;display:flex;flex-direction:column;" @click.stop>
        <div class="flex justify-between items-center border-b p-4 bg-gray-50">
          <div class="flex items-center gap-2">
            <span class="material-icons text-blue-600">settings</span>
            <span class="text-lg font-bold text-gray-800">班级设置</span>
          </div>
          <button @click="closeClassSettingsModal" class="text-gray-400 hover:text-gray-700">
            <span class="material-icons">close</span>
          </button>
        </div>
        <div class="p-8 space-y-8 flex-1 overflow-y-auto">
          <!-- 通用设置 -->
          <div>
            <h3 class="text-base font-semibold mb-4">通用设置</h3>
            <div class="space-y-6">
              <div class="flex items-center justify-between">
                <label class="font-medium text-gray-700">允许学生加入</label>
                <el-switch v-model="classSettings.allowJoin" active-color="#409EFF" inactive-color="#dcdfe6" />
              </div>
              <transition name="collapse-fade-smooth">
                <div v-if="classSettings.allowJoin" class="bg-gray-50 rounded px-6 py-4 flex items-center gap-4 justify-start">
                  <div class="flex items-center gap-2">
                    <label class="text-gray-700">加入班级需要教师验证</label>
                    <el-tooltip content="开启后，学生通过邀请码/二维码加班，需要教师同意后才可进入" placement="top">
                      <span class="material-icons text-gray-400 text-base cursor-pointer">info</span>
                    </el-tooltip>
                  </div>
                  <el-switch v-model="classSettings.needTeacherVerify" active-color="#409EFF" inactive-color="#dcdfe6" />
                </div>
              </transition>
              <div class="flex items-center gap-4 justify-start">
                <label class="font-medium text-gray-700">允许学生退课</label>
                <el-switch v-model="classSettings.allowQuit" active-color="#409EFF" inactive-color="#dcdfe6" />
              </div>
              <div class="flex items-center gap-4 justify-start">
                <label class="font-medium text-gray-700">开启结课模式</label>
                <el-switch v-model="classSettings.finishMode" active-color="#409EFF" inactive-color="#dcdfe6" />
              </div>
              <p class="text-xs text-gray-500 mt-2">学生进入结课模式，学习行为不会产生统计数据的增加，班级将显示在列表最后</p>
            </div>
          </div>
          <!-- 高级设置（可折叠，简化） -->
          <div>
            <div class="flex items-center gap-4 justify-start">
              <h3 class="text-base font-semibold mb-4">高级设置</h3>
              <span @click="toggleAdvancedSettings" class="material-icons text-base cursor-pointer mb-2">{{ showAdvancedSettings ? 'expand_less' : 'expand_more' }}</span>
            </div>
            <transition name="collapse-fade-smooth">
              <div v-if="showAdvancedSettings" class="mt-4 space-y-6 border-t pt-4">
                <!-- 开放班级报名 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4">开放班级报名</label>
                  <el-switch v-model="classSettings.openApply" active-color="#409EFF" inactive-color="#dcdfe6" />
                  <span class="ml-3 text-xs text-gray-500">开启后，课程内容将公开。若要保护课程版权，可开启知识共享保护协议。</span>
                </div>
                <!-- 班级开课时间 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4">班级开课时间</label>
                  <el-date-picker
                    v-model="classSettings.startDate"
                    type="date"
                    placeholder="请选择开始时间"
                    :disabled="!classSettings.openApply"
                    class="mr-2"
                    size="small"
                    style="width: 150px;"
                  />
                  <span class="mx-2 text-gray-400">—</span>
                  <el-date-picker
                    v-model="classSettings.endDate"
                    type="date"
                    placeholder="请选择结束时间"
                    :disabled="!classSettings.openApply"
                    class="mr-2"
                    size="small"
                    style="width: 150px;"
                  />
                </div>
                <span class="text-xs text-gray-400">非开放时间段内班级将进入结课模式，学生无法完成任务点、作业、章节测验等。若延长开课结束时间，将自动关闭结课模式</span>
                <!-- 班级所属学期 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4" style="width: 110px;">班级所属学期</label>
                  <el-select v-model="classSettings.term" placeholder="请选择学期" style="width: 180px !important;">
                    <el-option label="2024秋季学期" value="2024秋季学期" />
                    <el-option label="2025春季学期" value="2025春季学期" />
                    <el-option label="2025秋季学期" value="2025秋季学期" />
                  </el-select>
                </div>
                <!-- 设置班级上限人数 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4">设置班级上限人数</label>
                  <el-input v-model.number="classSettings.maxStudents" style="width: 120px;" size="small" />
                </div>
                <!-- 3个开关项 -->
                <div class="flex flex-wrap gap-8 mt-4">
                  <div class="flex items-center gap-2">
                    <span class="text-gray-700 text-sm">忽略视频拖拽及窗口切换</span>
                    <el-switch v-model="classSettings.ignoreVideoDrag" size="small" />
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-gray-700 text-sm">显示第三方答疑</span>
                    <el-switch v-model="classSettings.showThirdPartyQA" size="small" />
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-gray-700 text-sm">对学生隐藏班级</span>
                    <el-switch v-model="classSettings.hideClassForStudents" size="small" />
                  </div>
                </div>
              </div>
            </transition>
          </div>
          <div style="background-color: #f5f7fa;padding: 10px;border-radius: 5px;">
            <h3 class="text-base font-semibold mb-1">生效班级</h3>
            <span class="text-xs text-gray-400">开放班级报名不支持保存至其他班级</span>
            <div class="mt-2 mb-2 flex items-center gap-2">
              <input type="checkbox" id="select-all-classes" :checked="allClassesChecked" @change="toggleAllClasses" class="form-checkbox text-blue-600" />
              <label for="select-all-classes" class="text-gray-700">全部班级</label>
            </div>
            <div class="grid grid-cols-3 gap-x-6 gap-y-2">
              <div v-for="cls in classList" :key="cls.id" class="flex items-center gap-2">
                <input type="checkbox" :id="'effective-class-' + cls.id" :value="cls.id" v-model="selectedEffectiveClasses" class="form-checkbox text-blue-600" />
                <label :for="'effective-class-' + cls.id" class="text-gray-700">{{ cls.name }}</label>
              </div>
            </div>
          </div>
          <div class="flex justify-end gap-2 pt-4 border-t">
            <button @click="closeClassSettingsModal" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
            <button @click="saveClassSettings" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">保存</button>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { 
  User, 
  Edit, 
  Delete, 
  Close, 
  Star, 
  Reading, 
  DataAnalysis
} from '@element-plus/icons-vue'

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 学生状态选项卡
const tabs = [
  { label: '全部学生', value: 'all' },
  { label: '活跃', value: 'active' },
  { label: '较少活动', value: 'less-active' },
  { label: '未激活', value: 'inactive' }
]
const currentTab = ref('all')

// 搜索和筛选
const searchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  classes: [],
  statuses: []
})

// 民族选项
const ethnicityOptions = [
  '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
  '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族', '黎族', '傈僳族',
  '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族', '景颇族', '柯尔克孜族', '土族',
  '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族', '毛南族', '仡佬族', '锡伯族', '阿昌族', '普米族',
  '塔吉克族', '怒族', '乌孜别克族', '俄罗斯族', '鄂温克族', '德昂族', '保安族', '裕固族', '京族', '塔塔尔族',
  '独龙族', '鄂伦春族', '赫哲族', '门巴族', '珞巴族', '基诺族'
]

// 政治面貌选项
const politicalStatusOptions = [
  '中共党员', '中共预备党员', '共青团员', '民革党员', '民盟盟员', '民建会员', 
  '民进会员', '农工党党员', '致公党党员', '九三学社社员', '台盟盟员', '无党派人士', '群众'
]

// 模态框状态
const showAddStudentModal = ref(false)
const showAddClassModal = ref(false)
const showImportModal = ref(false)
const showEditStudentModal = ref(false)

// 切换筛选器
const toggleFilter = (category, value) => {
  if (selectedFilters.value[category].includes(value)) {
    selectedFilters.value[category] = selectedFilters.value[category].filter(item => item !== value)
  } else {
    selectedFilters.value[category].push(value)
  }
}

// 学生课程示例数据
const sampleCourses = [
  {
    name: '人工智能基础',
    term: '2023春季学期',
    status: '进行中',
    progress: 65,
    grade: 92
  },
  {
    name: '高级数据结构',
    term: '2023春季学期',
    status: '进行中',
    progress: 45,
    grade: 88
  }
]

// 学生活动示例数据
const studentActivities = [
  {
    title: '完成了《人工智能基础》第8章测验',
    description: '得分：95/100',
    timestamp: '2023-06-15 14:30',
    type: 'primary',
    color: '#409EFF'
  },
  {
    title: '提交了《高级数据结构》第5章作业',
    description: '得分：88/100',
    timestamp: '2023-06-10 09:45',
    type: 'success',
    color: '#67C23A'
  },
  {
    title: '观看了《人工智能基础》第9章视频',
    description: '进度：100%',
    timestamp: '2023-06-05 16:20',
    type: 'warning',
    color: '#E6A23C'
  }
]

// 查看所有课程
const handleViewAllCourses = () => {
  console.log('查看所有课程:', selectedStudent.value.name)
  // 在实际应用中，这里应该跳转到学生的所有课程页面
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'active': '活跃',
    'less-active': '较少活动',
    'inactive': '未激活'
  }
  return statusMap[status] || status
}

// 获取Element Plus状态类型
const getStatusType = (status) => {
  const typeMap = {
    'active': 'success',
    'less-active': 'warning',
    'inactive': 'info'
  }
  return typeMap[status] || 'info'
}

// 模拟学生数据
const students = ref([
  // 24电商4班
  {
    id: 1,
    name: '林琪',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/women/11.jpg',
    studentId: '202401001',
    className: '24电商4班',
    major: '电子商务',
    status: 'active',
    averageGrade: 89
  },
  {
    id: 2,
    name: '刘欣婷',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/women/12.jpg',
    studentId: '202401002',
    className: '24电商4班',
    major: '电子商务',
    status: 'less-active',
    averageGrade: 75
  },
  // 24电商5班
  {
    id: 3,
    name: '潘持婷',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/women/13.jpg',
    studentId: '202405001',
    className: '24电商5班',
    major: '电子商务',
    status: 'active',
    averageGrade: 92
  },
  {
    id: 4,
    name: '李雨桐',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/women/14.jpg',
    studentId: '202405002',
    className: '24电商5班',
    major: '电子商务',
    status: 'inactive',
    averageGrade: null
  },
  // 23机服入一班
  {
    id: 5,
    name: '黄福全',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/men/15.jpg',
    studentId: '202301001',
    className: '23机服入一班',
    major: '机电一体化',
    status: 'active',
    averageGrade: 85
  },
  // 23物流二
  {
    id: 6,
    name: '覃思焱',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/men/16.jpg',
    studentId: '202302001',
    className: '23物流二',
    major: '物流管理',
    status: 'less-active',
    averageGrade: 78
  },
  // 23广告二班
  {
    id: 7,
    name: '覃镇龙',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/men/17.jpg',
    studentId: '202303001',
    className: '23广告二班',
    major: '广告设计',
    status: 'inactive',
    averageGrade: null
  },
  // 24会计
  {
    id: 8,
    name: '吕艳红',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/women/18.jpg',
    studentId: '202410001',
    className: '24会计',
    major: '会计',
    status: 'active',
    averageGrade: 90
  },
  // 24电商1
  {
    id: 9,
    name: '廖紫蝶',
    email: '<EMAIL>',
    avatarUrl: 'https://randomuser.me/api/portraits/women/19.jpg',
    studentId: '202411001',
    className: '24电商1',
    major: '电子商务',
    status: 'active',
    averageGrade: 88
  }
])

// 表格选择和分页
const selectedStudents = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const goToPage = ref(1)

// 切换学生选择
const toggleStudentSelection = (studentId) => {
  if (selectedStudents.value.includes(studentId)) {
    selectedStudents.value = selectedStudents.value.filter(id => id !== studentId)
  } else {
    selectedStudents.value.push(studentId)
  }
}

// 全选/取消全选
const toggleSelectAll = (e) => {
  if (e.target.checked) {
    selectedStudents.value = filteredStudents.value.map(student => student.id)
  } else {
    selectedStudents.value = []
  }
}

// 班级搜索过滤
const filteredClassList = computed(() => {
  if (!searchQuery.value) return classList.value
  const query = searchQuery.value.toLowerCase()
  return classList.value.filter(cls => cls.name.toLowerCase().includes(query))
})

// 根据过滤条件筛选学生
const filteredStudents = computed(() => {
  let result = students.value.filter(s => selectedClass.value && s.className === selectedClass.value.name)

  // 按标签筛选
  if (currentTab.value !== 'all') {
    result = result.filter(student => student.status === currentTab.value)
  }

  // 按选中的筛选器筛选
  if (selectedFilters.value.classes.length > 0) {
    result = result.filter(student => selectedFilters.value.classes.includes(student.className))
  }
  if (selectedFilters.value.statuses.length > 0) {
    result = result.filter(student => {
      const statusLabel = getStatusLabel(student.status)
      return selectedFilters.value.statuses.includes(statusLabel)
    })
  }
  // 按学生搜索关键词筛选（右侧搜索框，后续如需可单独变量控制）
  // if (studentSearchQuery.value) { ... }
  return result
})

// 分页后的学生列表
const paginatedStudents = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredStudents.value.slice(startIndex, startIndex + pageSize.value)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredStudents.value.length / pageSize.value) || 1
})

// 显示页码
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 跳转到指定页面
const jumpToPage = () => {
  if (goToPage.value >= 1 && goToPage.value <= totalPages.value) {
    currentPage.value = goToPage.value
  } else {
    goToPage.value = currentPage.value
  }
}

// 新学生表单
const newStudent = ref({
  name: '',
  studentId: '',
  email: '',
  className: '',
  major: '',
  status: 'inactive',
  avatarPreview: null,
  avatar: null,
  sendInvitation: true,
  idCard: '',
  gender: 'male',
  birthday: '',
  ethnicity: '汉族',
  phone: '',
  enrollmentDate: new Date().toISOString().slice(0, 10)
})

// 处理头像上传
const handleAvatarUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    newStudent.value.avatar = file
    const reader = new FileReader()
    reader.onload = (e) => {
      newStudent.value.avatarPreview = e.target.result
    }
    reader.readAsDataURL(file)
  }
}

// 移除头像
const removeAvatar = () => {
  newStudent.value.avatar = null
  newStudent.value.avatarPreview = null
  if (this.$refs && this.$refs.avatarInput) {
    this.$refs.avatarInput.value = ''
  }
}

// 添加学生
const addStudent = () => {
  // 这里应该发送API请求添加学生
  console.log('添加学生:', newStudent.value)
  
  // 模拟添加成功
  const newId = Math.max(...students.value.map(s => s.id)) + 1
  
  students.value.push({
    id: newId,
    name: newStudent.value.name,
    email: newStudent.value.email,
    avatarUrl: newStudent.value.avatarPreview || 'https://randomuser.me/api/portraits/men/5.jpg',
    studentId: newStudent.value.studentId,
    className: newStudent.value.className,
    major: newStudent.value.major,
    enrolledCourses: 0,
    status: newStudent.value.status,
    averageGrade: null,
    idCard: newStudent.value.idCard,
    gender: newStudent.value.gender,
    birthday: newStudent.value.birthday,
    ethnicity: newStudent.value.ethnicity,
    phone: newStudent.value.phone,
    enrollmentDate: newStudent.value.enrollmentDate
  })
  
  // 关闭模态框并重置表单
  showAddStudentModal.value = false
  resetNewStudentForm()
}

// 重置新学生表单
const resetNewStudentForm = () => {
  newStudent.value = {
    name: '',
    studentId: '',
    email: '',
    className: '',
    major: '',
    status: 'inactive',
    avatarPreview: null,
    avatar: null,
    sendInvitation: true,
    idCard: '',
    gender: 'male',
    birthday: '',
    ethnicity: '汉族',
    phone: '',
    enrollmentDate: new Date().toISOString().slice(0, 10)
  }
}

// 选中的学生详情
const selectedStudent = ref(null)

// 查看学生详情
const viewStudentDetails = (student) => {
  selectedStudent.value = { ...student }
}

// 关闭学生详情
const closeStudentDetails = () => {
  selectedStudent.value = null
}

// 编辑学生信息
const editStudentForm = ref({})

const editStudent = (student) => {
  editStudentForm.value = { ...student }
  showEditStudentModal.value = true
}

const closeEditStudentModal = () => {
  showEditStudentModal.value = false
  editStudentForm.value = {}
}

const saveEditStudent = () => {
  const idx = students.value.findIndex(s => s.id === editStudentForm.value.id)
  if (idx !== -1) {
    students.value[idx] = { ...students.value[idx], ...editStudentForm.value }
  }
  showEditStudentModal.value = false
}

// 显示学生迁出模态框
const showRemoveStudentModal = (student) => {
  studentToRemove.value = student
  showRemoveModal.value = true
}

// 班级列表静态数据
const classList = ref([
  { id: 1, name: '24电商4班', studentCount: 18, desc: '' },
  { id: 2, name: '24电商5班', studentCount: 16, desc: '' },
  { id: 3, name: '23机服入一班', studentCount: 20, desc: '' },
  { id: 4, name: '23物流二', studentCount: 22, desc: '' },
  { id: 5, name: '23广告二班', studentCount: 19, desc: '' },
  { id: 6, name: '23物流三班', studentCount: 21, desc: '' },
  { id: 7, name: '25', studentCount: 15, desc: '' },
  { id: 8, name: '22网络2班', studentCount: 17, desc: '' },
  { id: 9, name: '24酒店一班', studentCount: 18, desc: '' },
  { id: 10, name: '24会计', studentCount: 20, desc: '' },
  { id: 11, name: '24电商1', studentCount: 14, desc: '' },
])

const selectedClass = ref(classList.value[0])

const selectClass = (cls) => {
  selectedClass.value = cls
  currentPage.value = 1 // 切换班级时重置分页
}

// 新建班级按钮点击事件
const onCreateClass = () => {
  showAddClassModal.value = true
}

// 班级管理按钮点击事件
const router = useRouter()
const onManageClass = () => {
  // 跳转到班级管理页面
  router.push('/teacher/class-list')
}

// 新班级表单
const newClass = ref({
  name: '',
  desc: ''
})

// 添加班级
const addClass = () => {
  // 这里应该发送API请求添加班级
  console.log('添加班级:', newClass.value)
  
  // 模拟添加成功
  const newId = Math.max(...classList.value.map(c => c.id)) + 1
  
  classList.value.push({
    id: newId,
    name: newClass.value.name,
    studentCount: 0,
    desc: newClass.value.desc
  })
  
  // 自动选中新建班级并切换到第一页
  selectedClass.value = classList.value[classList.value.length - 1]
  currentPage.value = 1
  
  // 关闭模态框并重置表单
  showAddClassModal.value = false
  resetNewClassForm()
}

// 重置新班级表单
const resetNewClassForm = () => {
  newClass.value = {
    name: '',
    desc: ''
  }
}

// 导入数据
const importData = ref([])
const importPreview = ref([])

// 处理导入文件
const handleImportFile = (e) => {
  const file = e.target.files[0]
  if (!file) return
  const reader = new FileReader()
  reader.onload = (evt) => {
    const text = evt.target.result
    // 简单CSV解析（仅逗号分隔，真实项目可用PapaParse等库）
    const rows = text.split(/\r?\n/).filter(Boolean).map(row => row.split(','))
    importData.value = rows.slice(1) // 跳过表头
    importPreview.value = rows.slice(0, 6) // 表头+前5行
  }
  reader.readAsText(file)
}

// 确认导入
const confirmImport = () => {
  // 假设CSV格式：姓名,学号,邮箱,班级,专业
  importData.value.forEach(row => {
    if (row.length < 5) return
    const newId = Math.max(...students.value.map(s => s.id)) + 1
    students.value.push({
      id: newId,
      name: row[0],
      studentId: row[1],
      email: row[2],
      className: row[3],
      major: row[4],
      status: 'inactive',
      avatarUrl: '',
      averageGrade: null
    })
  })
  showImportModal.value = false
  importData.value = []
  importPreview.value = []
}

// 导出名单为CSV
const exportStudentList = () => {
  if (!selectedClass.value) return
  const header = ['姓名','学号','邮箱','班级','专业','状态']
  const rows = students.value.filter(s => s.className === selectedClass.value.name)
    .map(s => [s.name, s.studentId, s.email, s.className, s.major, getStatusLabel(s.status)])
  const csvContent = [header, ...rows].map(e => e.join(',')).join('\r\n')
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.setAttribute('download', `${selectedClass.value.name}-学生名单.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 获取班级学生数量
const getStudentCountByClass = (className) => {
  return students.value.filter(s => s.className === className).length
}

// 脚本部分
const showRemoveModal = ref(false)
const studentToRemove = ref(null)

const closeRemoveModal = () => {
  showRemoveModal.value = false
  studentToRemove.value = null
}

const removeStudent = () => {
  if (studentToRemove.value) {
    students.value = students.value.filter(s => s.id !== studentToRemove.value.id)
  }
  showRemoveModal.value = false
  studentToRemove.value = null
}

const onClassSettings = () => {
  showClassSettingsModal.value = true
}

// 脚本部分
const showClassSettingsModal = ref(false)
const showAdvancedSettings = ref(false)
const classSettings = ref({
  allowJoin: true,
  needTeacherVerify: false,
  allowQuit: true,
  finishMode: false,
  openApply: false,
  maxStudents: 200,
  startDate: new Date().toISOString().slice(0, 10),
  endDate: new Date().toISOString().slice(0, 10),
  term: '2024秋季学期',
  ignoreVideoDrag: false,
  showThirdPartyQA: false,
  hideClassForStudents: false
})

const closeClassSettingsModal = () => {
  showClassSettingsModal.value = false
}
const toggleAdvancedSettings = () => {
  showAdvancedSettings.value = !showAdvancedSettings.value
}
const saveClassSettings = () => {
  // TODO: 可对接API保存
  console.log('保存班级设置', classSettings.value)
  showClassSettingsModal.value = false
}

// 在<script setup>中添加:
// const selectedEffectiveClasses = ref(classList.value.map(cls => cls.id)); // 默认全选或根据业务调整
const selectedEffectiveClasses = ref([]);

const allClassesChecked = computed(() => selectedEffectiveClasses.value.length === classList.value.length && classList.value.length > 0)
const toggleAllClasses = () => {
  if (allClassesChecked.value) {
    selectedEffectiveClasses.value = []
  } else {
    selectedEffectiveClasses.value = classList.value.map(cls => cls.id)
  }
}
</script> 

<style scoped>
.collapse-fade-smooth-enter-active,
.collapse-fade-smooth-leave-active {
  transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1), padding 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.collapse-fade-smooth-enter-from,
.collapse-fade-smooth-leave-to {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.collapse-fade-smooth-enter-to,
.collapse-fade-smooth-leave-from {
  max-height: 500px;
  opacity: 1;
  padding-top: 1rem;
  padding-bottom: 1rem;
}
</style>