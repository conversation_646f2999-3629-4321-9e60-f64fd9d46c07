// 缓存工具类
class Cache {
  constructor(prefix = 'zhkt_') {
    this.prefix = prefix
    this.storage = uni.getStorageSync
  }
  
  // 获取完整的key
  getKey(key) {
    return `${this.prefix}${key}`
  }
  
  // 设置缓存
  set(key, value, expire = 0) {
    const data = {
      value,
      expire: expire ? Date.now() + expire * 1000 : 0
    }
    uni.setStorageSync(this.getKey(key), JSON.stringify(data))
  }
  
  // 获取缓存
  get(key) {
    try {
      const data = JSON.parse(uni.getStorageSync(this.getKey(key)))
      if (data) {
        if (data.expire && data.expire < Date.now()) {
          this.remove(key)
          return null
        }
        return data.value
      }
      return null
    } catch (e) {
      return null
    }
  }
  
  // 移除缓存
  remove(key) {
    uni.removeStorageSync(this.getKey(key))
  }
  
  // 清空缓存
  clear() {
    uni.clearStorageSync()
  }
  
  // 获取缓存信息
  info() {
    return uni.getStorageInfoSync()
  }
}

export default new Cache() 