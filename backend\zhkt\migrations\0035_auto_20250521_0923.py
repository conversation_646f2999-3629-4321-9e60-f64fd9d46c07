# Generated by Django 3.2.20 on 2025-05-21 09:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0034_courseoverview'),
    ]

    operations = [
        migrations.CreateModel(
            name='AILectureChapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('chapter_title', models.CharField(max_length=255, verbose_name='章节标题')),
                ('start_page', models.IntegerField(verbose_name='起始页码')),
                ('end_page', models.IntegerField(verbose_name='结束页码')),
                ('chapter_order', models.IntegerField(verbose_name='章节顺序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
            ],
            options={
                'verbose_name': 'AI讲课章节',
                'verbose_name_plural': 'AI讲课章节',
                'ordering': ['chapter_order'],
            },
        ),
        migrations.CreateModel(
            name='AILectureDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='文档标题')),
                ('file_path', models.CharField(max_length=500, verbose_name='文件路径')),
                ('file_type', models.CharField(choices=[('doc', 'Word文档'), ('docx', 'Word文档'), ('pdf', 'PDF文档')], max_length=10, verbose_name='文件类型')),
                ('total_pages', models.IntegerField(verbose_name='总页数')),
                ('content_list_file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='解析内容列表文件路径')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
            ],
            options={
                'verbose_name': 'AI讲课文档',
                'verbose_name_plural': 'AI讲课文档',
            },
        ),
        migrations.CreateModel(
            name='AILectureKeyPoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_point', models.TextField(verbose_name='原始要点')),
                ('enhanced_point', models.TextField(verbose_name='知识增强后的要点')),
                ('point_order', models.IntegerField(verbose_name='要点顺序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('chapter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='key_points', to='zhkt.ailecturechapter')),
            ],
            options={
                'verbose_name': 'AI讲课要点',
                'verbose_name_plural': 'AI讲课要点',
                'ordering': ['point_order'],
            },
        ),
        migrations.CreateModel(
            name='AILectureSpeechContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('speech_script', models.TextField(verbose_name='讲稿内容')),
                ('audio_file_path', models.CharField(max_length=500, verbose_name='音频文件路径')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('key_point', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='speech_content', to='zhkt.ailecturekeypoint')),
            ],
            options={
                'verbose_name': 'AI讲课语音内容',
                'verbose_name_plural': 'AI讲课语音内容',
            },
        ),
        migrations.CreateModel(
            name='AILectureHtmlContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('html_file_path', models.CharField(max_length=500, verbose_name='HTML文件路径')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('key_point', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='html_content', to='zhkt.ailecturekeypoint')),
            ],
            options={
                'verbose_name': 'AI讲课HTML内容',
                'verbose_name_plural': 'AI讲课HTML内容',
            },
        ),
        migrations.AddField(
            model_name='ailecturechapter',
            name='document',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chapters', to='zhkt.ailecturedocument'),
        ),
    ]
