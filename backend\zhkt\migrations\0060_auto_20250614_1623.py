# Generated by Django 3.2.20 on 2025-06-14 16:23

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0059_auto_20250614_1609'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='ailecturesubtitle',
            options={'ordering': ['start_time'], 'verbose_name': 'AI讲课字幕内容', 'verbose_name_plural': 'AI讲课字幕内容'},
        ),
        # migrations.RemoveIndex(
        #     model_name='ailecturesubtitle',
        #     name='zhkt_ailect_speech__c77fd7_idx',
        # ),
        migrations.AddField(
            model_name='ailecturesubtitle',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='创建时间'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='ailecturesubtitle',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='ailecturesubtitle',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
    ]
