<template>
  <AdminLayout 
    pageTitle="商城管理" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <el-card class="mb-4">
          <template #header>
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <el-input
                  v-model="searchQuery"
                  placeholder="搜索商品"
                  class="w-60"
                  clearable
                  @input="debouncedSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-select v-model="currentCategory" class="ml-4" @change="handleSearch">
                  <el-option label="全部" value="" />
                  <el-option label="实物商品" value="physical" />
                  <el-option label="虚拟商品" value="virtual" />
                  <el-option label="学习资源" value="learning" />
                </el-select>
              </div>
              <el-button type="primary" @click="handleAddItem">添加商品</el-button>
            </div>
          </template>
          
          <el-table :data="products" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column label="商品图片" width="100">
              <template #default="{ row }">
                <el-image 
                  :src="row.image || defaultProductImage" 
                  fit="cover"
                  :preview-src-list="[row.image || defaultProductImage]"
                  preview-teleported
                  class="product-thumbnail"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" min-width="120" />
            <el-table-column prop="points_price" label="价格(积分)" width="120" />
            <el-table-column prop="stock" label="库存" width="100" />
            <el-table-column prop="category" label="类别" width="120">
              <template #default="{ row }">
                {{ getCategoryLabel(row.category) }}
              </template>
            </el-table-column>
            <el-table-column prop="is_active" label="状态" width="120">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'info'">
                  {{ row.is_active ? '已上架' : '已下架' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
                  <el-button 
                    size="small" 
                    :type="row.is_active ? 'warning' : 'success'"
                    @click="handleToggleStatus(row)"
                  >
                    {{ row.is_active ? '下架' : '上架' }}
                  </el-button>
                  <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="flex justify-end mt-4">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[5, 10, 20, 50]"
              :total="total"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 商品表单弹窗 -->
    <ProductFormModal
      v-model:show="showProductModal"
      :product="selectedProduct"
      @submit="handleProductSubmit"
    />
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search, Picture } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import ProductFormModal from '@/components/modals/ProductFormModal.vue'
import { useAdminStore } from '@/stores/admin'
import { debounce } from '@/utils/debounce'
import { 
  getProducts, 
  createProduct, 
  updateProduct, 
  deleteProduct,
  updateProductStatus
} from '@/api/points'
import defaultProductImage from '@/assets/images/products/coffee.jpg'

const adminStore = useAdminStore()

// 状态变量
const loading = ref(false)
const searchQuery = ref('')
const currentCategory = ref('')
const currentPage = ref(1)
const pageSize = ref(5)
const total = ref(0)
const products = ref([])
const showProductModal = ref(false)
const selectedProduct = ref(null)

// 创建防抖的搜索函数
const debouncedSearch = debounce(() => {
  handleSearch()
}, 500)

// 获取商品列表
const loadProducts = async () => {
  loading.value = true
  try {
    const res = await getProducts({
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value,
      category: currentCategory.value
    })
    products.value = res.results
    total.value = res.count
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadProducts()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadProducts()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadProducts()
}

// 添加商品
const handleAddItem = () => {
  selectedProduct.value = null
  showProductModal.value = true
}

// 编辑商品
const handleEdit = (row) => {
  selectedProduct.value = row
  showProductModal.value = true
}

// 切换商品状态
const handleToggleStatus = async (row) => {
  try {
    await updateProductStatus(row.id, !row.is_active)
    ElMessage.success(`商品${row.is_active ? '下架' : '上架'}成功`)
    loadProducts()
  } catch (error) {
    console.error('更新商品状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 删除商品
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除该商品吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteProduct(row.id)
      ElMessage.success('删除成功')
      loadProducts()
    } catch (error) {
      console.error('删除商品失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 提交商品表单
const handleProductSubmit = async (formData, callback) => {
  try {
    let result
    if (selectedProduct.value) {
      result = await updateProduct(selectedProduct.value.id, formData)
      ElMessage.success('更新成功')
    } else {
      result = await createProduct(formData)
      ElMessage.success('创建成功')
    }
    loadProducts()
    if (callback) callback(result)
  } catch (error) {
    console.error('保存商品失败:', error)
    ElMessage.error('保存失败')
    if (callback) callback(null)
  }
}

// 获取类别标签
const getCategoryLabel = (category) => {
  const categoryMap = {
    physical: '实物商品',
    virtual: '虚拟商品',
    learning: '学习资源'
  }
  return categoryMap[category] || category
}

// 初始化
onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.product-form {
  padding: 20px;
}

.product-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  cursor: pointer;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--el-fill-color-lighter);
  color: var(--el-text-color-secondary);
}

.image-error .el-icon {
  font-size: 20px;
}
</style> 