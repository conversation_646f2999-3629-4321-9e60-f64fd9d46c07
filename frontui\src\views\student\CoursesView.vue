<template>
  <StudentLayout 
    :pageTitle="t('courses.title')" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    <!-- 选课按钮 -->
    <div class="mb-6 flex space-x-4">
      <button 
        @click="showJoinClassModal"
        class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md flex items-center"
      >
        <i class="material-icons mr-2">group_add</i>
        {{ t('courses.joinClass') }}
      </button>
    </div>

    <!-- 课程统计 -->
    <div class="mb-6">
      <div class="bg-white p-6 rounded-lg shadow-md mb-3 mt-0">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="bg-blue-50 p-6 rounded-lg transition-transform hover:transform hover:scale-105">
            <div class="flex items-center">
              <div class="p-4 rounded-md bg-blue-100 mr-4">
                <el-icon class="text-blue-600 text-2xl"><Collection /></el-icon>
              </div>
              <div>
                <p class="text-sm font-semibold text-gray-600 mb-1">{{ t('courses.courseStats.total') }}</p>
                <p class="text-2xl font-bold text-gray-800">{{ courseStats.total }}</p>
              </div>
            </div>
          </div>
          <div class="bg-green-50 p-6 rounded-lg transition-transform hover:transform hover:scale-105">
            <div class="flex items-center">
              <div class="p-4 rounded-md bg-green-100 mr-4">
                <el-icon class="text-green-600 text-2xl"><CircleCheck /></el-icon>
              </div>
              <div>
                <p class="text-sm font-semibold text-gray-600 mb-1">{{ t('courses.courseStats.completed') }}</p>
                <p class="text-2xl font-bold text-gray-800">{{ courseStats.completed }}</p>
              </div>
            </div>
          </div>
          <div class="bg-yellow-50 p-6 rounded-lg transition-transform hover:transform hover:scale-105">
            <div class="flex items-center">
              <div class="p-4 rounded-md bg-yellow-100 mr-4">
                <el-icon class="text-yellow-600 text-2xl"><Loading /></el-icon>
              </div>
              <div>
                <p class="text-sm font-semibold text-gray-600 mb-1">{{ t('courses.courseStats.inProgress') }}</p>
                <p class="text-2xl font-bold text-gray-800">{{ courseStats.inProgress }}</p>
              </div>
            </div>
          </div>
          <div class="bg-purple-50 p-6 rounded-lg transition-transform hover:transform hover:scale-105">
            <div class="flex items-center">
              <div class="p-4 rounded-md bg-purple-100 mr-4">
                <el-icon class="text-purple-600 text-2xl"><Timer /></el-icon>
              </div>
              <div>
                <p class="text-sm font-semibold text-gray-600 mb-1">{{ t('courses.courseStats.weeklyHours') }}</p>
                <p class="text-2xl font-bold text-gray-800">{{ courseStats.weeklyHours }}h</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加入班级弹窗 -->
    <div v-if="showJoinClassDialog" class="modal-overlay">
      <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-gray-800">{{ t('courses.joinClassModal.title') }}</h3>
          <button @click="closeJoinClassModal" class="text-gray-500 hover:text-gray-700">
            <i class="material-icons">close</i>
          </button>
        </div>
        
        <div class="mb-6">
          <label for="inviteCode" class="block text-sm font-medium text-gray-700 mb-2">
            {{ t('courses.joinClassModal.enterCode') }}
          </label>
          <input
            v-model="inviteCode"
            type="text"
            id="inviteCode"
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :placeholder="t('courses.joinClassModal.codePlaceholder')"
            maxlength="6"
          />
        </div>
        
        <div class="flex justify-end space-x-3">
          <button
            @click="closeJoinClassModal"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
          >
            {{ t('general.cancel') }}
          </button>
          <button
            @click="handleJoinClass"
            class="px-4 py-2 text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition-colors duration-200"
            :disabled="!inviteCode || isJoining"
          >
            {{ isJoining ? t('courses.joinClassModal.joining') : t('courses.joinClassModal.confirm') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="bg-white rounded-lg shadow-lg">
      <!-- 全部课程标题 -->
      <div class="p-6 border-b border-gray-100">
        <h3 class="text-xl font-semibold text-gray-800 tracking-tight">{{ t('courses.coursesList.title') }}</h3>
      </div>
      
      <!-- 课程列表 -->
      <div class="divide-y divide-gray-100">
        <div 
          v-for="course in courses" 
          :key="course.id" 
          class="p-6 hover:bg-gray-50 transition-colors duration-200"
        >
          <div class="flex items-start">
            <!-- 课程图片 -->
            <div class="w-52 h-32 flex-shrink-0 mr-8 overflow-hidden rounded-lg">
              <img :src="course.cover_image" :alt="course.name" class="w-full h-full object-cover transform hover:scale-110 transition-transform duration-300">
            </div>
            
            <!-- 课程信息和操作 -->
            <div class="flex-grow flex flex-col">
              <!-- 课程标题和学院 -->
              <div class="flex justify-between items-start mb-3">
                <div>
                  <h4 class="text-lg font-semibold text-gray-800 mb-1 tracking-tight">{{ course.name }}</h4>
                  <p class="text-sm text-gray-600">{{ course.college }}</p>
                </div>
                <div class="text-right text-sm">
                  <div class="flex items-center">
                    <div class="w-32 h-2.5 bg-gray-100 rounded-full mr-2">
                      <div class="h-2.5 rounded-full transition-all duration-300" 
                           :class="{'bg-blue-500': course.progress < 100, 'bg-green-500': course.progress === 100}"
                           :style="`width: ${course.progress}%`">
                      </div>
                    </div>
                    <span class="text-xs text-gray-500 font-medium">{{ course.progress }}/100</span>
                  </div>
                </div>
              </div>

              <!-- 课程详细信息和按钮 -->
              <div class="flex justify-between items-center mt-4">
                <div class="flex text-sm text-gray-600">
                  <div class="flex items-center mr-8">
                    <i class="material-icons mr-2 text-gray-400">person</i>
                    <span>{{ t('courses.coursesList.teacher') }}: {{ course.teacher.title }}</span>
                  </div>
                  <div class="flex items-center mr-8">
                    <i class="material-icons mr-2 text-gray-400">schedule</i>
                    <span>{{ t('courses.coursesList.hours') }}: {{ course.total_hours }}</span>
                  </div>
                  <div class="flex items-center">
                    <i class="material-icons mr-2 text-gray-400">group</i>
                    <span>{{ t('courses.coursesList.students', { count: course.student_count }) }}</span>
                  </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex items-center">
                  <div class="flex items-center text-red-500 mr-6 font-medium">
                    <i class="ph ph-coins mr-2"></i>
                    <span>{{ course.pointsCost }}{{ t('courses.points') }}</span>
                  </div>
                  <button @click="showNotes(course.id)" 
                          class="text-blue-600 hover:text-blue-800 font-medium mr-6 transition-colors duration-200">
                    <i class="ph ph-note mr-1"></i> {{ t('courses.notes.button') }}
                  </button>
                  <router-link 
                    :to="`/student/course/${course.id}`" 
                    class="px-5 py-2.5 text-sm font-medium text-white rounded-lg transition-all duration-200 hover:shadow-md"
                    :class="{'bg-blue-500 hover:bg-blue-600': course.progress < 100, 'bg-green-500 hover:bg-green-600': course.progress === 100}"
                  >
                    {{ course.progress < 100 ? t('courses.coursesList.continueLearning') : t('courses.coursesList.review') }}
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="mt-8 flex justify-center">
      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="分页">
        <!-- 上一页按钮 -->
        <button 
          @click="changePage(currentPage - 1)" 
          :disabled="currentPage === 1"
          :class="[
            currentPage === 1 ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-50', 
            'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500'
          ]"
        >
          <span class="sr-only">{{ t('courses.pagination.previous') }}</span>
          <i class="material-icons h-5 w-5">chevron_left</i>
        </button>
        
        <!-- 页码按钮 -->
        <template v-for="(page, index) in displayedPages" :key="index">
          <button
            v-if="page !== '...'"
            @click="changePage(page)"
            :class="[
              page === currentPage 
                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' 
                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
              'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
            ]"
          >
            {{ page }}
          </button>
          <span 
            v-else
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
          >
            ...
          </span>
        </template>
        
        <!-- 下一页按钮 -->
        <button 
          @click="changePage(currentPage + 1)" 
          :disabled="currentPage === totalPages"
          :class="[
            currentPage === totalPages ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-50',
            'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500'
          ]"
        >
          <span class="sr-only">{{ t('courses.pagination.next') }}</span>
          <i class="material-icons h-5 w-5">chevron_right</i>
        </button>
      </nav>
    </div>

    <!-- 笔记弹窗 -->
    <div v-if="showNotesModal" class="modal-overlay">
      <div id="notesContainer" class="note-modal" ref="notesContainer">
        <!-- 弹窗标题 (可拖动区域) -->
        <div class="modal-header" ref="notesDragHandle">
          <div class="flex items-center">
            <i class="material-icons text-blue-600 text-lg mr-2">edit_note</i>
            <h3 class="text-xl font-medium text-gray-800">{{ t('courses.notes.title', { course: currentCourseTitle }) }}</h3>
          </div>
          <button @click="closeNotes" class="modal-close">
            <el-icon :size="20"><Close /></el-icon>
          </button>
        </div>
        
        <div class="note-container">
          <!-- 左侧笔记列表 -->
          <div class="notes-sidebar" ref="leftPanel">
            <div class="notes-search">
              <div class="search-input-container">
                <input 
                  v-model="noteSearchQuery" 
                  type="text" 
                  :placeholder="t('courses.notes.searchPlaceholder')" 
                  class="w-full px-4 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <i class="material-icons text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2">search</i>
              </div>
            </div>
            
            <div class="notes-list">
              <div class="notes-list-header">
                <h4 class="font-medium text-gray-700">{{ t('courses.notes.listTitle') }}</h4>
                  <span class="notes-count text-xs text-gray-500">{{ t('courses.notes.count', { count: filteredNotes.length }) }}</span>
              </div>
              
              <!-- 笔记列表 -->
              <div class="notes-list-container">
                <div 
                  v-if="isLoadingNotes" 
                  class="flex items-center justify-center py-8"
                >
                  <el-icon class="text-blue-500 text-xl animate-spin mr-2"><Loading /></el-icon>
                  <span class="text-gray-600">{{ t('courses.notes.loading') }}</span>
                </div>
                <div 
                  v-else-if="filteredNotes.length === 0" 
                  class="text-gray-500 text-center py-4"
                >
                  {{ t('courses.notes.empty') }}
                </div>
                <div 
                  v-else
                  v-for="note in filteredNotes" 
                  :key="note.id" 
                  @click="selectNote(note.id)"
                  class="note-list-item"
                  :class="{'active': note.id === currentNoteId}"
                >
                  <div class="note-list-title">{{ note.title || t('courses.notes.untitled') }}</div>
                  <div class="note-list-content text-sm text-gray-500 truncate">{{ note.content.substring(0, 60) }}{{ note.content.length > 60 ? '...' : '' }}</div>
                  <div class="note-list-meta">
                    <span class="note-chapter">{{ note.chapter }}</span> 
                    <span class="note-timestamp">{{ formatDate(note.created_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 调整列宽拖动条 -->
          <div 
            ref="columnResizer"
            class="cursor-col-resize w-1 bg-gray-200 hover:bg-blue-400 active:bg-blue-600 transition-colors duration-150" 
          ></div>
          
          <!-- 右侧笔记内容展示区 -->
          <div class="note-content-view" ref="rightPanel">
            <div 
              v-if="isLoadingNoteDetail" 
              class="flex items-center justify-center h-full"
            >
              <el-icon class="text-blue-500 text-xl animate-spin mr-2"><Loading /></el-icon>
              <span class="text-gray-600">{{ t('courses.notes.loadingContent') }}</span>
            </div>
            <template v-else>
              <div class="note-view-header">
                <h2 class="text-xl font-medium text-gray-800">{{ currentNote.title || t('courses.notes.untitled') }}</h2>
                <div class="note-meta-info">
                  <div class="note-meta-item">
                    <!--<span class="text-sm text-gray-500">章节: {{ currentNote.chapter }}</span>-->
                    <span class="text-sm text-gray-500">{{ t('courses.notes.createdAt') }}: {{ currentNote.date }}</span>
                </div>
              </div>
            </div>
            
              <!-- 笔记内容 -->
              <div class="note-content-display markdown-body">
                {{ currentNote.content }}
              </div>
            </template>
          </div>
          
          <!-- 调整大小的手柄 -->
          <div class="absolute bottom-0 right-0 w-6 h-6 cursor-nwse-resize" ref="resizeHandle">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-6 h-6 text-gray-400 fill-current">
              <path d="M22 22H20V20H18V18H16V16H14V14H12V12H10V10H8V8H6V6H4V4H2V2H0V0H24V24H22Z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, computed, onMounted, reactive, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n' // 添加 i18n 支持
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { useStudentStore } from '@/stores/student'
import { Close, Collection, CircleCheck, Loading, Timer } from '@element-plus/icons-vue'
import 'material-icons/iconfont/material-icons.css'
import { ElMessage } from 'element-plus'
import { getNotes, getNoteDetail } from '@/api/note'
import { formatDate } from '@/utils/date'

const { t, locale } = useI18n() // 初始化 i18n

// 存储和状态
const studentStore = useStudentStore()

// 课程统计数据
const courseStats = computed(() => studentStore.courseStats)

// 课程数据
const courses = computed(() => studentStore.courses)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(8)
const totalItems = ref(0)

// 计算总页数
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value) || 1)

// 显示的页码范围
const displayedPages = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 切换页面
const changePage = async (page) => {
  if (page < 1 || page > totalPages.value || page === '...') return
  currentPage.value = page
  await loadCourses()
}

// 加载课程数据
const loadCourses = async () => {
  try {
    // 调用API获取分页数据
    const response = await studentStore.fetchSelectedCourses({
      page: currentPage.value,
      page_size: pageSize.value
    })
    
    // 更新总条目数
    if (response && response.count) {
      totalItems.value = response.count
    }
  } catch (error) {
    ElMessage.error(t('errors.loadCoursesListFailed'))
    console.error(t('errors.loadCoursesListFailed'), error)
  }
}

// 笔记功能相关
const showNotesModal = ref(false)
const currentCourseId = ref(null)
const currentNoteId = ref(null)
const currentCourseTitle = ref('')
const noteSearchQuery = ref('')
const notesContainer = ref(null)
const notesDragHandle = ref(null)
const leftPanel = ref(null)
const rightPanel = ref(null)
const columnResizer = ref(null)
const resizeHandle = ref(null)
const isLoadingNotes = ref(false)
const isLoadingNoteDetail = ref(false)
const notes = ref([])

// 当前显示的笔记
const currentNote = reactive({
  id: 0,
  title: '',
  content: '',
  date: '',
  chapter: ''
})

// 根据搜索过滤笔记
const filteredNotes = computed(() => {
  if (!notes.value) return []
  
  if (!noteSearchQuery.value) return notes.value
  
  const query = noteSearchQuery.value.toLowerCase()
  return notes.value.filter(note => 
    note.title.toLowerCase().includes(query) || 
    note.content.toLowerCase().includes(query) ||
    (note.chapter && note.chapter.toLowerCase().includes(query))
  )
})

// 显示笔记弹窗
const showNotes = async (courseId) => {
  currentCourseId.value = courseId
  
  // 获取课程名称
  const course = studentStore.getCourseById(courseId)
  if (course) {
    currentCourseTitle.value = course.title
  }
  
  // 显示弹窗
  showNotesModal.value = true
  
  // 加载笔记列表
  try {
    isLoadingNotes.value = true
    const response = await getNotes(courseId)
    notes.value = response.results || []
  
  // 默认选择第一个笔记（如果有）
    if (notes.value.length > 0) {
      await selectNote(notes.value[0].id)
  } else {
      // 如果没有笔记，显示空状态
      Object.assign(currentNote, {
        id: 0,
        title: '',
        content: '',
        date: '',
        chapter: ''
      })
    }
  } catch (error) {
    ElMessage.error(t('courses.notes.loadFailed'))
    console.error(t('courses.notes.loadFailed'), error)
  } finally {
    isLoadingNotes.value = false
  }
  
  // 设置初始大小和初始化拖拽功能（在nextTick中）
  nextTick(() => {
    initNotesModal()
  })
}

// 关闭笔记弹窗
const closeNotes = () => {
  showNotesModal.value = false
  notes.value = []
  Object.assign(currentNote, {
    id: 0,
    title: '',
    content: '',
    date: '',
    chapter: ''
  })
}

// 选择笔记
const selectNote = async (noteId) => {
  if (currentNoteId.value === noteId) return
  
  currentNoteId.value = noteId
  
  try {
    isLoadingNoteDetail.value = true
    const note = await getNoteDetail(noteId)
    Object.assign(currentNote, {
      id: note.id,
      title: note.title,
      content: note.content,
      date: formatDate(note.created_at),
      chapter: note.chapter || ''
    })
  } catch (error) {
    ElMessage.error(t('courses.notes.loadDetailFailed'))
    console.error(t('courses.notes.loadDetailFailed'), error)
  } finally {
    isLoadingNoteDetail.value = false
  }
}

// 设置初始弹窗大小
const setInitialModalSize = () => {
  const container = notesContainer.value
  if (container) {
    // 设置初始宽高 (根据屏幕大小动态设置)
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    
    // 计算合适的尺寸 (响应式设计)
    let width, height
    if (windowWidth >= 1200) {
      width = Math.min(1000, windowWidth * 0.8)
      height = Math.min(700, windowHeight * 0.8)
    } else if (windowWidth >= 768) {
      width = windowWidth * 0.85
      height = windowHeight * 0.75
    } else {
      width = windowWidth * 0.95
      height = windowHeight * 0.7
    }
    
    // 应用尺寸
    container.style.width = width + 'px'
    container.style.height = height + 'px'
    container.style.maxWidth = 'none' // 覆盖默认的max-width
    
    // 居中显示
    centerModal()
  }
}

// 居中显示弹窗
const centerModal = () => {
  const modal = notesContainer.value
  if (modal) {
    // 重置位置样式
    modal.style.left = ''
    modal.style.top = ''
    modal.style.transform = ''
    modal.parentElement.style.alignItems = 'center'
    modal.parentElement.style.justifyContent = 'center'
  }
}

// 初始化笔记弹窗功能
const initNotesModal = () => {
  setInitialModalSize()
  initDraggable()
  initResizable()
  initColumnResizer()
}

// 初始化弹窗拖拽功能
const initDraggable = () => {
  const modal = notesContainer.value
  const dragHandle = notesDragHandle.value
  
  if (!modal || !dragHandle) return
  
  let isDragging = false
  let offsetX, offsetY
  
  // 开始拖动
  dragHandle.addEventListener('mousedown', (e) => {
    // 只有当鼠标按下时才开始拖动
    if (e.button === 0) { // 左键
      isDragging = true
      modal.classList.add('dragging')
      
      // 计算鼠标与弹窗的相对位置
      const rect = modal.getBoundingClientRect()
      offsetX = e.clientX - rect.left
      offsetY = e.clientY - rect.top
      
      // 防止选中文本
      e.preventDefault()
    }
  })
  
  // 拖动中
  document.addEventListener('mousemove', (e) => {
    if (!isDragging) return
    
    // 计算新位置
    const x = e.clientX - offsetX
    const y = e.clientY - offsetY
    
    // 限制在可视区域内
    const maxX = window.innerWidth - modal.offsetWidth
    const maxY = window.innerHeight - modal.offsetHeight
    
    // 设置新位置
    modal.style.left = Math.max(0, Math.min(maxX, x)) + 'px'
    modal.style.top = Math.max(0, Math.min(maxY, y)) + 'px'
    
    // 取消居中布局
    modal.style.transform = 'none'
    modal.parentElement.style.alignItems = 'flex-start'
    modal.parentElement.style.justifyContent = 'flex-start'
  })
  
  // 结束拖动
  document.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false
      modal.classList.remove('dragging')
    }
  })
}

// 初始化调整大小功能
const initResizable = () => {
  const modal = notesContainer.value
  const handle = resizeHandle.value
  
  if (!modal || !handle) return
  
  let isResizing = false
  let startWidth, startHeight, startX, startY
  
  // 开始调整大小
  handle.addEventListener('mousedown', (e) => {
    if (e.button === 0) { // 左键
      isResizing = true
      document.body.classList.add('resizing')
      
      // 记录起始值
      startWidth = modal.offsetWidth
      startHeight = modal.offsetHeight
      startX = e.clientX
      startY = e.clientY
      
      // 防止选中文本
      e.preventDefault()
    }
  })
  
  // 调整大小中
  document.addEventListener('mousemove', (e) => {
    if (!isResizing) return
    
    // 计算宽高变化量
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY
    
    // 计算新的宽高
    let newWidth = Math.max(300, startWidth + deltaX)
    let newHeight = Math.max(400, startHeight + deltaY)
    
    // 限制最大宽高
    newWidth = Math.min(newWidth, window.innerWidth * 0.95)
    newHeight = Math.min(newHeight, window.innerHeight * 0.95)
    
    // 应用新的宽高
    modal.style.width = newWidth + 'px'
    modal.style.height = newHeight + 'px'
  })
  
  // 结束调整大小
  document.addEventListener('mouseup', () => {
    if (isResizing) {
      isResizing = false
      document.body.classList.remove('resizing')
    }
  })
}

// 初始化列宽调整器
const initColumnResizer = () => {
  const colResizer = columnResizer.value
  const leftPanelEl = leftPanel.value
  const rightPanelEl = rightPanel.value
  
  if (!colResizer || !leftPanelEl || !rightPanelEl) return
  
  let isResizing = false
  let startX, startLeftWidth
  
  // 开始调整列宽
  colResizer.addEventListener('mousedown', (e) => {
    if (e.button === 0) { // 左键
      isResizing = true
      document.body.classList.add('col-resizing')
      
      // 记录起始值
      startX = e.clientX
      startLeftWidth = leftPanelEl.offsetWidth
      
      // 防止选中文本
      e.preventDefault()
    }
  })
  
  // 调整列宽中
  document.addEventListener('mousemove', (e) => {
    if (!isResizing) return
    
    // 计算宽度变化量
    const deltaX = e.clientX - startX
    
    // 获取容器总宽度
    const containerWidth = leftPanelEl.parentElement.offsetWidth - colResizer.offsetWidth
    
    // 计算新的宽度比例
    const newLeftWidth = Math.max(100, Math.min(startLeftWidth + deltaX, containerWidth - 200))
    
    // 应用新的宽度
    const leftPercentage = (newLeftWidth / containerWidth) * 100
    const rightPercentage = 100 - leftPercentage
    
    leftPanelEl.style.width = leftPercentage + '%'
    rightPanelEl.style.width = rightPercentage + '%'
  })
  
  // 结束调整列宽
  document.addEventListener('mouseup', () => {
    if (isResizing) {
      isResizing = false
      document.body.classList.remove('col-resizing')
    }
  })
}

// 加入班级相关
const showJoinClassDialog = ref(false)
const inviteCode = ref('')
const isJoining = ref(false)

const showJoinClassModal = () => {
  showJoinClassDialog.value = true
  inviteCode.value = ''
}

const closeJoinClassModal = () => {
  showJoinClassDialog.value = false
  inviteCode.value = ''
}

const handleJoinClass = async () => {
  if (!inviteCode.value) return
  
  isJoining.value = true
  try {
    const success = await studentStore.joinClass(inviteCode.value)
    if (success) {
      ElMessage.success(t('courses.joinClassModal.success'))
      closeJoinClassModal()
    } else {
      ElMessage.error(t('courses.joinClassModal.failed'))
    }
  } catch (error) {
    ElMessage.error(t('courses.joinClassModal.failedWithError', { error: error.message }))
  } finally {
    isJoining.value = false
  }
}

// 监听语言变化，重新加载课程数据
watch(locale, async (newLang) => {
  console.log('语言切换到:', newLang)
  // 重置分页到第一页
  currentPage.value = 1
  // 重新加载课程数据
  await loadCourses()
})

// 页面加载完成时执行
onMounted(async () => {
  if(!studentStore.studentData.id) {
    await studentStore.fetchCurrentStudentInfo()
  }
  await loadCourses()
})
</script>

<style scoped>
/* 全局变量 */
:root {
  --primary-color: #3b82f6;
  --primary-light: #60a5fa;
  --primary-dark: #2563eb;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;
  --bg-light: #f9fafb;
  --bg-card: #ffffff;
  --border-color: #e5e7eb;
}

/* 基础样式系统 */
.h1 { font-size: 2rem; line-height: 2.5rem; font-weight: 600; }
.h2 { font-size: 1.5rem; line-height: 2rem; font-weight: 600; }
.h3 { font-size: 1.25rem; line-height: 1.75rem; font-weight: 600; }

.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }

.resize-container {
  min-width: 300px;
  min-height: 400px;
}

#notesContainer {
  transition: none;
  resize: both;
  overflow: auto;
}

/* 拖动时应用的样式 */
.dragging {
  cursor: move;
  user-select: none;
}

.resizing {
  cursor: nwse-resize !important;
  user-select: none;
}

.col-resizing {
  cursor: col-resize !important;
  user-select: none;
}

/* 调整大小手柄 */
#resizeHandle {
  opacity: 0.5;
  transition: opacity 0.2s;
}

#resizeHandle:hover {
  opacity: 1;
}

/* 优化移动端显示 */
@media (max-width: 768px) {
  .resize-container {
    width: 95vw !important;
    height: 90vh !important;
    max-height: 90vh !important;
  }
  
  #notesContainer {
    resize: none; /* 禁用移动端的resize功能 */
  }
}

/* 分页的hover和active状态 */
.pagination a:hover {
  z-index: 2;
}

/* 笔记列表项 */
.space-y-3 > div {
  transition: all 0.2s;
}

/* 工具栏按钮 */
.bg-gray-50 button {
  transition: all 0.15s ease;
}

.bg-gray-50 button:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.bg-gray-50 button:active {
  transform: translateY(0);
  box-shadow: none;
}

/* 课程卡片悬停效果 */
.divide-y > div {
  transition: background-color 0.3s;
}

/* 文本框自适应高度 */
textarea {
  height: 100%;
}

/* 确保列调整器有足够的点击区域 */
#columnResizer {
  position: relative;
}

#columnResizer::after {
  content: '';
  position: absolute;
  top: 0;
  left: -5px;
  right: -5px;
  bottom: 0;
  z-index: 5;
}

/* 模态框通用样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background-color: #f0f9ff;
  cursor: move;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: #f56c6c;
  color: white;
  padding: 8px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-close:hover {
  background: #e64242;
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

/* 笔记模态框样式 */
.note-modal {
  background: white;
  width: 90%;
  max-width: 1200px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 85vh;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  position: relative;
}

.note-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  background-color: var(--bg-light);
}

.notes-sidebar {
  width: 300px;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  background-color: var(--bg-card);
}

.notes-search {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.search-input-container {
  position: relative;
}

.search-input-container i {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.notes-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.notes-list-header {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.note-list-item {
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
  background-color: var(--bg-card);
}

.note-list-item:hover {
  background: #f5f7fe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.note-list-item.active {
  background: #eef2fd;
  border-left: 3px solid var(--primary-color);
}

.note-list-title {
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
}

.note-list-content {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.note-list-meta {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #888;
}

.note-content-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-card);
  overflow: hidden;
}

.note-view-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: #f8fafc;
}

.note-content-display {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  font-size: 15px;
  line-height: 1.8;
  color: var(--text-primary);
  background-color: var(--bg-card);
  white-space: pre-wrap;
}

.markdown-body {
  font-family: system-ui, -apple-system, sans-serif;
}
</style> 