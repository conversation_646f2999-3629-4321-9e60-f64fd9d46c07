from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class GeneratedVideo(models.Model):
    """生成视频模型"""
    
    STATUS_CHOICES = [
        ('processing', '处理中'),
        ('success', '成功'),
        ('failed', '失败'),
    ]
    
    INPUT_TYPE_CHOICES = [
        ('text', '文本输入'),
        ('audio', '音频上传'),
    ]
    
    # 基本信息
    id = models.BigAutoField(primary_key=True, verbose_name=_('生成视频ID'))
    user_id = models.BigIntegerField(_('用户ID'), db_index=True)
    name = models.CharField(_('视频名称'), max_length=100)
    digital_human_id = models.BigIntegerField(_('使用的数字人ID'), null=True, blank=True)
    
    # 输入类型和音频处理
    input_type = models.Char<PERSON>ield(_('输入类型'), max_length=10, choices=INPUT_TYPE_CHOICES, default='text')
    audio_id = models.BigIntegerField(_('生成的音频ID'), null=True, blank=True, help_text=_('文本输入时生成的音频ID'))
    uploaded_audio_url = models.CharField(_('上传的音频URL'), max_length=255, null=True, blank=True, help_text=_('用户上传的音频文件路径'))
    text_content = models.TextField(_('文本内容'), null=True, blank=True, help_text=_('文本输入时的原始内容'))
    
    # 视频信息
    duration = models.CharField(_('视频时长'), max_length=10, null=True, blank=True)
    video_url = models.CharField(_('视频URL'), max_length=255, null=True, blank=True)
    cover_image = models.CharField(_('封面图片路径'), max_length=255, null=True, blank=True)
    status = models.CharField(_('处理状态'), max_length=20, choices=STATUS_CHOICES, default='processing')
    power_consumed = models.IntegerField(_('消耗的算力'), default=0)
    tags = models.TextField(_('视频标签'), null=True, blank=True, help_text=_('以逗号分隔的标签ID'))
    
    # 时间字段
    create_time = models.DateTimeField(_('创建时间'), auto_now_add=True)
    update_time = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)
    
    class Meta:
        db_table = 'zhkt_generated_videos'
        verbose_name = _('生成视频')
        verbose_name_plural = _('生成视频')
        ordering = ['-create_time']
        indexes = [
            models.Index(fields=['user_id']),
            models.Index(fields=['status']),
            models.Index(fields=['input_type']),
            models.Index(fields=['create_time']),
            models.Index(fields=['digital_human_id']),
        ]
        
    def __str__(self):
        return self.name 