<template>
  <div class="min-h-screen bg-gray-100 py-6 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button
          @click="goBack"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <svg class="mr-2 -ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          返回课程列表
        </button>
      </div>

      <!-- 课程详情卡片 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 pt-6 pb-4 sm:p-8 sm:pb-4">
          <!-- 课程基本信息 -->
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <div class="flex justify-between items-center">
                <div>
                  <h1 class="text-2xl leading-6 font-bold text-gray-900">{{ course.name }}</h1>
                  <p class="mt-2 max-w-2xl text-base text-gray-500">{{ course.college }}</p>
                </div>
                <span class="inline-flex items-center px-4 py-1.5 rounded-full text-base font-medium" :class="getStatusClass(course.status)">
                  {{ getStatusText(course.status) }}
                </span>
              </div>

              <!-- 课程详细信息 -->
              <div class="mt-8">
                <dl class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-3">
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">授课教师</dt>
                    <dd class="mt-2 text-base text-gray-900">{{ course.teacher }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">学生人数</dt>
                    <dd class="mt-2 text-base text-gray-900">{{ course.studentCount }} 人</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">开课时间</dt>
                    <dd class="mt-2 text-base text-gray-900">{{ course.startDate }}</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">课程时长</dt>
                    <dd class="mt-2 text-base text-gray-900">{{ course.duration }} 周</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">班级数量</dt>
                    <dd class="mt-2 text-base text-gray-900">{{ course.classCount }} 个班级</dd>
                  </div>
                  <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">课程类型</dt>
                    <dd class="mt-2 text-base text-gray-900">{{ getSubjectText(course.subject) }}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="bg-gray-50 px-6 py-4 sm:px-8 sm:flex sm:flex-row-reverse">
          <button 
            v-if="course.status !== 'ended'"
            type="button"
            @click="handleEdit"
            class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:text-sm">
            编辑课程
          </button>
          <button 
            v-if="course.status === 'ended'"
            type="button"
            @click="handleArchive"
            class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:text-sm">
            归档课程
          </button>
        </div>
      </div>

      <!-- 编辑课程模态框 -->
      <CourseEditModal
        :show="showEditModal"
        :course="course"
        :colleges="colleges"
        @close="closeEditModal"
        @submit="handleEditSubmit"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import CourseEditModal from '@/components/modals/CourseEditModal.vue'

const route = useRoute()
const router = useRouter()
const showEditModal = ref(false)

// 模拟数据 - 实际应用中应该从API获取
const course = ref({
  id: '',
  name: '',
  college: '',
  subject: '',
  teacher: '',
  startDate: '',
  duration: 16,
  classCount: 1,
  studentCount: 0,
  status: 'planned'
})

const colleges = ref([
  { id: 1, name: '计算机学院' },
  { id: 2, name: '数学学院' },
  { id: 3, name: '物理学院' }
])

// 在组件挂载时获取课程数据
onMounted(async () => {
  const courseId = route.params.id
  // TODO: 从API获取课程数据
  // const response = await fetchCourseById(courseId)
  // course.value = response.data
})

const getStatusClass = (status) => {
  const classes = {
    active: 'bg-green-100 text-green-800',
    planned: 'bg-yellow-100 text-yellow-800',
    ended: 'bg-gray-100 text-gray-800'
  }
  return classes[status]
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    planned: '计划中',
    ended: '已结束'
  }
  return texts[status]
}

const getSubjectText = (subject) => {
  const texts = {
    math: '数学类',
    programming: '编程类',
    physics: '物理类',
    chemistry: '化学类',
    database: '数据库类'
  }
  return texts[subject]
}

const handleEdit = () => {
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
}

const handleEditSubmit = async (updatedCourse) => {
  // TODO: 调用API更新课程信息
  // await updateCourse(updatedCourse)
  course.value = { ...updatedCourse }
  closeEditModal()
}

const handleArchive = async () => {
  // TODO: 调用API归档课程
  // await archiveCourse(course.value.id)
  course.value.status = 'archived'
}

const goBack = () => {
  router.push('/admin/courses')
}
</script> 