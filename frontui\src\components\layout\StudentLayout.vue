<template>
  <div class="flex h-screen overflow-hidden">
    <!-- Sidebar - fixed on desktop, toggleable on mobile -->
    <div :class="[
      'transition-all duration-300',
      isCollapsed ? 'w-16' : 'w-56',
      { 'hidden': !showSidebar }
    ]">
      <StudentSidebar :is-collapsed="isCollapsed" @toggleCollapse="toggleCollapse" :active-page="props.activePage" />
    </div>

    <!-- Main Content Area -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <!-- Top Header with Breadcrumbs -->
      <UserHeader 
        @toggleSidebar="toggleSidebar"
        @toggleCollapse="toggleCollapse"
        :pageTitle="props.pageTitle"
        :userName="userName"
        :userAvatar="userAvatar"
        :userPoints="userPoints"
      />

      <!-- Main Content - Scrollable independently from header -->
      <div class="flex-1 p-6 overflow-y-auto bg-gray-50">
        <slot></slot>
      </div>
    </div>

    <!-- Mobile Sidebar Backdrop -->
    <div 
      v-if="showSidebar && isMobile" 
      class="fixed inset-0 bg-black bg-opacity-30 z-10 md:hidden"
      @click="toggleSidebar"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import StudentSidebar from './StudentSidebar.vue'
import UserHeader from './UserHeader.vue'

// Props for the layout
const props = defineProps({
  pageTitle: {
    type: String,
    default: '学生首页'
  },
  activePage: {
    type: String,
    default: ''
  },
  userName: {
    type: String,
    required: true
  },
  userAvatar: {
    type: String,
    required: true
  },
  userPoints: {
    type: [Number, String],
    required: true
  }
})

// State
const showSidebar = ref(true)
const isMobile = ref(window.innerWidth < 768) // Initialize with current width
const isCollapsed = ref(false)

// Toggle sidebar visibility (primarily for mobile view)
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
  console.log('Sidebar toggled, now:', showSidebar.value)
}

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// Detect window resize to determine if we're on mobile
const handleResize = () => {
  isMobile.value = window.innerWidth < 768
  // If desktop width, always show sidebar
  if (!isMobile.value) {
    showSidebar.value = true
  }
}

// Initialize and setup event listeners
onMounted(() => {
  handleResize() // Set initial state based on screen size
  window.addEventListener('resize', handleResize)
  console.log('StudentLayout mounted, sidebar:', showSidebar.value)
})

// Clean up
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
:root {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --bg-main: #f8fafc;
  --bg-sidebar: #fff;
  --bg-header: #fff;
  --border-color: #e5e7eb;
}

/* 侧边栏样式（宽度由外层div控制） */
/* 保留背景、阴影、边框等可加到 StudentSidebar.vue */

/* 主内容区样式 */
.flex-1.p-6.overflow-y-hidden.bg-gray-50 {
  background: var(--bg-main) !important;
  padding: 2.5rem !important; /* p-10 */
  min-height: 0;
}

/* 顶部栏样式（UserHeader） */
.flex.flex-col.flex-1.overflow-hidden > :first-child {
  background: var(--bg-header);
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  border-bottom: 1px solid var(--border-color);
  z-index: 20;
}

/* 移动端遮罩层动画 */
.fixed.inset-0.bg-black.bg-opacity-30.z-10.md\:hidden {
  animation: fadeIn 0.3s;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 字体系统 */
.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.5px;
}
.user-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}
.user-points {
  font-size: 1rem;
  color: var(--primary-color);
  font-weight: 600;
}

/* 头像样式 */
.user-avatar {
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(59,130,246,0.08);
  border: 2px solid var(--primary-color);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .flex-1.p-6.overflow-y-auto.bg-gray-50 {
    padding: 1rem !important;
  }
  /* 侧边栏宽度由外层div控制，无需 min-width */
}
</style> 