需要下载 https://zh-cn.libreoffice.org/ 并安装设置config的LibreOffice_path路径
ai_lecture_tasks.py 需要使用LibreOffice word转pdf
```markdown
# 1. 更新软件包列表
sudo apt update

# 2. 安装完整版 LibreOffice（包括 Writer、Calc、Impress 等）
sudo apt install libreoffice

# 3. 安装中文语言包（可选）
sudo apt install libreoffice-l10n-zh-cn

# 4. 验证安装
libreoffice --version
```

这个是在 ai-education-tool  这个项目配置的
需要使用开源项目 https://github.com/opendatalab/MinerU  MinerU用于将PDF转换成JSON，用于知道每页是哪些内容
安装步骤 

1.  C:\Users\<USER>\magic-pdf.json (WINDOWS)
内容:
```json
{
    "device-mode": "cpu",
    "models-dir": "E:/project/ai_model/mineru/mineru_models/PDF-Extract-Kit-1.0/models",
    "layout-config": {
        "model": "doclayout_yolo"
    },
    "formula-config": {
        "mfd_model": "yolo_v8_mfd",
        "mfr_model": "unimernet_small",
        "enable": false
    },
    "table-config": {
        "model": "rapid_table",
        "sub_model": "slanet_plus",
        "enable": false,
        "max_time": 400
    }
}
```
models-dir 是模型 需要下载
模型下载分为初始下载和模型目录更新。有关如何继续作的说明，请参阅相应的文档。

模型文件的初始下载
从 Hugging Face 下载模型
使用 Python 脚本从 Hugging Face 下载模型文件

```markdown
pip install huggingface_hub
wget https://github.com/opendatalab/MinerU/raw/master/scripts/download_models_hf.py -O download_models_hf.py
python download_models_hf.py
```
Python 脚本会自动下载模型文件，并在配置文件中配置 model 目录。

配置文件可以在 user 目录中找到，文件名为 magic-pdf.json。

具体请看github文档

处理后的结果
```json
[
  {
    "type": "text",
    "text": "模块一电子商务数据分析概述",
    "text_level": 1,
    "page_idx": 0
  },
  {
    "type": "text",
    "text": "学习目标",
    "text_level": 1,
    "page_idx": 0
  },
  {
    "type": "text",
    "text": "【知识目标】",
    "text_level": 1,
    "page_idx": 0
  }
]
```

