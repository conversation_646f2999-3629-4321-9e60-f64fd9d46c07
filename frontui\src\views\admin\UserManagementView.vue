<template>
  <AdminLayout 
    pageTitle="用户管理" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <!-- 操作按钮 -->
        <div class="mt-4 flex justify-between">
          <div class="flex space-x-3">
            <button
              @click="handleAddUser"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="person_add" size="sm" color="light" className="mr-2" />
              添加用户
            </button>
            <button
              @click="handleBatchImport"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="upload_file" size="sm" color="default" className="mr-2" />
              批量导入
            </button>
            <button
              @click="handleExportData"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="download" size="sm" color="default" className="mr-2" />
              导出数据
            </button>
          </div>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="search" color="light" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="搜索用户..."
            >
          </div>
        </div>

        <!-- 用户列表 -->
        <div class="mt-8 flex flex-col">
          <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
              <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">加入时间</th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                      <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-if="isLoading" class="text-center">
                      <td colspan="6" class="px-6 py-4">
                        <div class="flex justify-center items-center">
                          <Icon name="sync" size="sm" className="animate-spin mr-2" />
                          加载中...
                        </div>
                      </td>
                    </tr>
                    <tr v-else-if="filteredUsers.length === 0" class="text-center">
                      <td colspan="6" class="px-6 py-4 text-gray-500">
                        暂无用户数据
                      </td>
                    </tr>
                    <tr v-else v-for="user in filteredUsers" :key="user.id">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 h-10 w-10">
                            <img
                              v-if="user.avatar"
                              class="h-10 w-10 rounded-full"
                              :src="user.avatar"
                              :alt="user.name"
                            >
                            <div
                              v-else
                              class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center"
                            >
                              <Icon name="person" color="light" />
                            </div>
                          </div>
                          <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                            <div class="text-sm text-gray-500">
                              <span v-if="user.alias">({{ user.alias }})</span>
                              <span>{{ user.email }}</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ getRoleName(user.role) }}</div>
                        <div class="text-sm text-gray-500">{{ user.department }}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ formatDate(user.registerTime) }}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span
                          class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                          :class="{
                            'bg-green-100 text-green-800': user.status === 'active',
                            'bg-yellow-100 text-yellow-800': user.status === 'inactive',
                            'bg-red-100 text-red-800': user.status === 'blocked'
                          }"
                        >
                          {{ getStatusName(user.status) }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a
                          href="#"
                          @click.prevent="editUser(user)"
                          class="text-indigo-600 hover:text-indigo-900 mr-4"
                        >
                          <Icon name="edit" size="sm" color="primary" className="align-middle" /> 编辑
                        </a>
                        <a
                          href="#"
                          @click.prevent="handleDeleteUser(user)"
                          class="text-red-600 hover:text-red-900"
                        >
                          <Icon name="delete" size="sm" color="danger" className="align-middle" /> 删除
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="mt-6 flex justify-between items-center">
          <div class="text-sm text-gray-700">
            显示 <span class="font-medium">{{ startIndex + 1 }}</span> 至
            <span class="font-medium">{{ endIndex }}</span> 条，共
            <span class="font-medium">{{ total }}</span> 条结果
          </div>
          <div class="flex-1 flex justify-end">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <a
                href="#"
                @click.prevent="previousPage"
                :class="[
                  currentPage === 1 ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-50',
                  'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500'
                ]"
              >
                <span class="sr-only">上一页</span>
                <Icon name="chevron_left" size="sm" />
              </a>
              <template v-for="page in displayedPages" :key="page.type === 'page' ? page.value : `ellipsis-${page.value}`">
                <span
                  v-if="page.type === 'ellipsis'"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                >
                  ...
                </span>
                <a
                  v-else
                  href="#"
                  @click.prevent="goToPage(page.value)"
                  :class="[
                    page.value === currentPage
                      ? 'bg-indigo-50 border-indigo-500 text-indigo-600'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50',
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                  ]"
                >
                  {{ page.value }}
                </a>
              </template>
              <a
                href="#"
                @click.prevent="nextPage"
                :class="[
                  currentPage === totalPages ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-50',
                  'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500'
                ]"
              >
                <span class="sr-only">下一页</span>
                <Icon name="chevron_right" size="sm" />
              </a>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑用户模态框 -->
    <div v-if="showAddUserModal" class="fixed z-10 inset-0 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full relative"
             style="max-height: 90vh; margin-top: auto; margin-bottom: auto;">
          <div class="bg-white px-6 pt-5 pb-4 sm:p-6 sm:pb-4 overflow-y-auto">
            <div class="flex items-center justify-between mb-8">
              <div class="flex items-center">
                <el-icon class="text-indigo-600 mr-3" :size="28">
                  <component :is="editingUser ? 'Edit' : 'CirclePlus'" />
                </el-icon>
                <h3 class="text-xl leading-6 font-medium text-gray-900">
                  {{ editingUser ? '编辑用户' : '添加用户' }}
                </h3>
              </div>
              <el-icon 
                class="text-gray-400 hover:text-gray-500 cursor-pointer transition-colors duration-200" 
                :size="22"
                @click="showAddUserModal = false">
                <Close />
              </el-icon>
            </div>
            
            <form @submit.prevent="saveUser" class="space-y-5">
              <div class="relative rounded-md shadow-sm">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" :size="18"><User /></el-icon>
                    用户名
                  </div>
                </label>
                <div class="relative rounded-md shadow-sm">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <el-icon class="text-gray-400" :size="16"><Avatar /></el-icon>
                  </div>
                  <input
                    v-model="userForm.name"
                    type="text"
                    required
                    class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md text-sm shadow-sm placeholder-gray-400
                           focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                           transition duration-150 ease-in-out"
                    placeholder="请输入用户名"
                  >
                </div>
              </div>
              
              <div class="relative rounded-md shadow-sm">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" :size="18"><Message /></el-icon>
                    邮箱
                  </div>
                </label>
                <div class="relative rounded-md shadow-sm">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <el-icon class="text-gray-400" :size="16"><EmailIcon /></el-icon>
                  </div>
                  <input
                    v-model="userForm.email"
                    type="email"
                    required
                    class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md text-sm shadow-sm placeholder-gray-400
                           focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                           transition duration-150 ease-in-out"
                    placeholder="请输入邮箱地址"
                  >
                </div>
              </div>

              <div class="relative rounded-md shadow-sm">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" :size="18"><UserFilled /></el-icon>
                    角色
                  </div>
                </label>
                <el-select
                  v-model="userForm.role"
                  class="w-full"
                  required
                  placeholder="请选择用户角色"
                  clearable
                  size="large"
                  style="height: 42px;"
                >
                  <el-option 
                    v-for="role in roles" 
                    :key="role.code" 
                    :value="role.code" 
                    :label="role.name"
                    style="height: 42px; line-height: 42px;"
                  >
                    <div class="flex items-center">
                      <el-icon class="mr-2" :size="16">
                        <component :is="getRoleIcon(role.code)" />
                      </el-icon>
                      {{ role.name }}
                    </div>
                  </el-option>
                </el-select>
              </div>

              <div class="relative rounded-md shadow-sm">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" :size="18"><DepartmentIcon /></el-icon>
                    部门
                  </div>
                </label>
                <el-select
                  v-model="userForm.department"
                  class="w-full"
                  required
                  placeholder="请选择所属部门"
                  clearable
                  size="large"
                  style="height: 42px;"
                >
                  <el-option 
                    v-for="dept in depts" 
                    :key="dept.id" 
                    :value="dept.id" 
                    :label="dept.name"
                    style="height: 42px; line-height: 42px;"
                  >
                    <div class="flex items-center">
                      <el-icon class="mr-2" :size="16"><DepartmentIcon /></el-icon>
                      {{ dept.name }}
                    </div>
                  </el-option>
                </el-select>
              </div>

              <div v-if="!editingUser" class="relative rounded-md shadow-sm">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" :size="18"><Lock /></el-icon>
                    密码
                  </div>
                </label>
                <div class="relative rounded-md shadow-sm">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <el-icon class="text-gray-400" :size="16"><Lock /></el-icon>
                  </div>
                  <input
                    v-model="userForm.password"
                    type="password"
                    required
                    class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md text-sm shadow-sm placeholder-gray-400
                           focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500
                           transition duration-150 ease-in-out"
                    placeholder="请输入密码"
                  >
                </div>
              </div>
            </form>
          </div>
          <div class="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse sm:px-6">
            <el-button
              type="primary"
              @click="saveUser"
              class="w-full sm:w-32 sm:ml-3"
              :icon="editingUser ? 'Check' : 'Plus'"
              size="large"
            >
              {{ editingUser ? '保存' : '添加' }}
            </el-button>
            <el-button
              @click="showAddUserModal = false"
              class="mt-3 w-full sm:w-32 sm:mt-0"
              icon="Close"
              size="large"
            >
              取消
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { format } from 'date-fns'
import { ElMessage, ElMessageBox } from 'element-plus'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import { useAdminStore } from '@/stores/admin'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'
import {
  User,
  Avatar,
  Message,
  Message as EmailIcon,
  Tickets as CardIcon,
  Document as IdCardIcon,
  UserFilled,
  Reading,
  School,
  Setting,
  Location as DepartmentIcon,
  Edit,
  CirclePlus,
  Close,
  Check,
  Plus,
  Lock
} from '@element-plus/icons-vue'
import { usersApi } from '@/api/users'

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

export default {
  name: 'UserManagementView',
  components: {
    AdminLayout,
    Icon,
    User,
    Avatar,
    Message,
    EmailIcon,
    CardIcon,
    IdCardIcon,
    UserFilled,
    Reading,
    School,
    Setting,
    DepartmentIcon,
    Edit,
    CirclePlus,
    Close,
    Check,
    Plus,
    Lock
  },
  setup() {
    const router = useRouter()
    const adminStore = useAdminStore()
    const authStore = useAuthStore()

    // 用户数据
    const users = ref([])
    const isLoading = ref(false)
    const roles = ref([])
    const depts = ref([])
    const total = ref(0)

    // 获取用户数据的函数
    async function fetchUsers(page = 1, query = '') {
      try {
        isLoading.value = true
        const params = {
          page,
          page_size: 10,
          search_fields: ['username', 'alias'],  // 指定搜索字段
          search: query
        }
        const response = await usersApi.getUsers(params)
        users.value = response.results.map(user => ({
          id: user.id,
          name: user.username,
          alias: user.alias,  // 添加昵称字段
          email: user.email,
          role: user.roles[0]?.code || '',
          department: user.dept?.name || '',
          registerTime: user.date_joined,
          status: user.is_active ? 'active' : 'inactive',
          avatar: user.avatar
        }))
        total.value = response.count
      } catch (error) {
        console.error('获取用户数据失败:', error)
        ElMessage.error('获取用户数据失败')
      } finally {
        isLoading.value = false
      }
    }

    // 获取角色和部门数据
    async function fetchRolesAndDepts() {
      try {
        const [rolesRes, deptsRes] = await Promise.all([
          usersApi.getRoles(),
          usersApi.getDepts()
        ])
        roles.value = rolesRes.results
        depts.value = deptsRes.results
      } catch (error) {
        console.error('获取角色和部门数据失败:', error)
      }
    }

    // 权限检查
    onMounted(() => {
      fetchUsers()
      fetchRolesAndDepts()
    })

    const searchQuery = ref('')
    const currentPage = ref(1)
    const showAddUserModal = ref(false)
    const editingUser = ref(null)
    const userForm = ref({
      name: '',
      email: '',
      role: '',
      department: '',
      password: ''  // 添加密码字段
    })

    // 创建防抖版本的fetchUsers函数
    const debouncedFetchUsers = debounce((query) => {
      currentPage.value = 1 // 重置到第一页
      fetchUsers(1, query)
    }, 500) // 500ms 的防抖延迟

    // 监听搜索和分页变化
    watch(searchQuery, (newQuery) => {
      debouncedFetchUsers(newQuery)
    })

    watch(currentPage, (newPage) => {
      fetchUsers(newPage, searchQuery.value)
    })

    // 保存用户
    async function saveUser() {
      try {

        const roleId = roles.value.filter(item=>item.code===userForm.value.role)[0].id

        const userData = {
          username: userForm.value.name,
          email: userForm.value.email,
          roles: [roleId],
          dept: userForm.value.department
        }

        // 只在创建新用户时添加密码字段
        if (!editingUser.value) {
          userData.password = userForm.value.password
        }

        if (editingUser.value) {
          await usersApi.updateUser(editingUser.value.id, userData)
          ElMessage.success('用户更新成功')
        } else {
          await usersApi.createUser(userData)
          ElMessage.success('用户创建成功')
        }

        showAddUserModal.value = false
        editingUser.value = null
        userForm.value = {
          name: '',
          email: '',
          role: '',
          department: '',
          password: ''
        }
        fetchUsers(currentPage.value, searchQuery.value)
      } catch (error) {
        console.error('保存用户失败:', error)
        if (error.response?.data) {
          const errors = Object.entries(error.response.data)
            .map(([key, value]) => `${key}: ${value.join(', ')}`)
            .join('\n')
          ElMessage.error(`保存用户失败:\n${errors}`)
        } else {
          ElMessage.error('保存用户失败')
        }
      }
    }

    // 删除用户
    async function handleDeleteUser(user) {
      try {
        await ElMessageBox.confirm(
          '确定要删除该用户吗？此操作不可逆',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'custom-message-box'
          }
        )
        await usersApi.deleteUser(user.id)
        ElMessage.success('用户删除成功')
        // 如果当前页只有一条数据，且不是第一页，则删除后跳转到上一页
        if (users.value.length === 1 && currentPage.value > 1) {
          currentPage.value--
        }
        fetchUsers(currentPage.value, searchQuery.value)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除用户失败:', error)
          ElMessage.error('删除用户失败')
        }
      }
    }

    const filteredUsers = computed(() => users.value)

    const totalPages = computed(() => Math.ceil(total.value / 10))
    const startIndex = computed(() => (currentPage.value - 1) * 10)
    const endIndex = computed(() => Math.min(startIndex.value + 10, total.value))

    const displayedPages = computed(() => {
      const pages = []
      const maxVisiblePages = 5 // 最多显示5个页码
      const halfVisible = Math.floor(maxVisiblePages / 2)
      
      // 总页数小于等于最大显示页数，显示所有页码
      if (totalPages.value <= maxVisiblePages) {
        for (let i = 1; i <= totalPages.value; i++) {
          pages.push({ type: 'page', value: i })
        }
        return pages
      }
      
      // 添加第一页
      pages.push({ type: 'page', value: 1 })
      
      // 当前页靠近开始
      if (currentPage.value <= halfVisible + 1) {
        for (let i = 2; i <= maxVisiblePages - 1; i++) {
          pages.push({ type: 'page', value: i })
        }
        pages.push({ type: 'ellipsis' })
      }
      // 当前页靠近结束
      else if (currentPage.value >= totalPages.value - halfVisible) {
        pages.push({ type: 'ellipsis' })
        for (let i = totalPages.value - maxVisiblePages + 2; i < totalPages.value; i++) {
          pages.push({ type: 'page', value: i })
        }
      }
      // 当前页在中间
      else {
        pages.push({ type: 'ellipsis' })
        for (let i = currentPage.value - 1; i <= currentPage.value + 1; i++) {
          pages.push({ type: 'page', value: i })
        }
        pages.push({ type: 'ellipsis' })
      }
      
      // 添加最后一页
      pages.push({ type: 'page', value: totalPages.value })
      
      return pages
    })

    function getRoleName(role) {
      const roleMap = {
        admin: '管理员',
        teacher: '教师',
        student: '学生'
      }
      return roleMap[role] || role
    }

    function getStatusName(status) {
      const statusMap = {
        active: '活跃',
        inactive: '未激活',
        blocked: '已封禁'
      }
      return statusMap[status] || status
    }

    function formatDate(date) {
      if (!date) return '暂无数据'
      try {
        return format(new Date(date), 'yyyy-MM-dd')
      } catch (error) {
        console.error('日期格式化错误:', error)
        return '日期无效'
      }
    }

    function previousPage() {
      if (currentPage.value > 1) {
        currentPage.value--
      }
    }

    function nextPage() {
      if (currentPage.value < totalPages.value) {
        currentPage.value++
      }
    }

    function goToPage(page) {
      currentPage.value = page
    }

    function editUser(user) {
      editingUser.value = user
      userForm.value = {
        name: user.name,
        email: user.email,
        role: user.role,
        department: user.department,
      }
      showAddUserModal.value = true
    }

    function handleBatchImport() {
      // 实现批量导入用户的逻辑
      console.log('Batch import')
    }

    function handleExportData() {
      // 实现导出用户数据的逻辑
      console.log('Export data')
    }

    function handleAddUser() {
      editingUser.value = null
      userForm.value = {
        name: '',
        email: '',
        role: '',
        department: '',
      }
      showAddUserModal.value = true
    }

    function getRoleIcon(roleCode) {
      const iconMap = {
        admin: 'Setting',
        teacher: 'School',
        student: 'Reading'
      }
      return iconMap[roleCode] || 'User'
    }

    return {
      adminStore,
      users,
      searchQuery,
      currentPage,
      showAddUserModal,
      editingUser,
      userForm,
      roles,
      depts,
      total,
      isLoading,
      handleDeleteUser,
      filteredUsers,
      totalPages,
      startIndex,
      endIndex,
      displayedPages,
      getRoleName,
      getStatusName,
      formatDate,
      previousPage,
      nextPage,
      goToPage,
      editUser,
      saveUser,
      handleBatchImport,
      handleExportData,
      handleAddUser,
      getRoleIcon
    }
  }
}
</script>

<style scoped>
.role-select {
  --el-component-size: 42px;
}

.role-select :deep(.el-input) {
  --el-select-input-height: 42px;
  height: var(--el-select-input-height);
}

.role-select :deep(.el-input__wrapper) {
  min-height: var(--el-select-input-height);
  height: var(--el-select-input-height);
  padding: 1px 11px;
}

.role-select :deep(.el-input__inner) {
  height: var(--el-select-input-height);
  line-height: var(--el-select-input-height);
}

.role-select :deep(.el-select-dropdown__item) {
  height: var(--el-select-input-height);
  line-height: var(--el-select-input-height);
  padding: 0 15px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  height: 42px !important;
}

:deep(.el-input__inner) {
  height: 42px !important;
  line-height: 42px !important;
}

:deep(.el-select-dropdown__item) {
  height: 42px !important;
  line-height: 42px !important;
}
</style> 