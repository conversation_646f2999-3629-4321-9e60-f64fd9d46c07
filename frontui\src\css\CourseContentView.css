/* 全局样式 */
.course-content-view {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  color: #333;
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
  background-color: #f9f9f9;
  min-height: 100vh;
}

h1, h2, h3, h4 {
  margin-top: 0;
  color: #2c3e50;
}

button {
  cursor: pointer;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
}

button:hover {
  opacity: 0.85;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 顶部导航 */
.top-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
}

.nav-left {
  display: flex;
  align-items: center;
}

.back-link {
  display: flex;
  align-items: center;
  padding: 6px 8px 6px 6px;
  margin-right: 20px;
  color: #3b82f6;
  font-weight: 500;
  text-decoration: none;
  border-radius: 30px;
  background-color: #f0f7ff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.back-link:hover {
  background-color: #dbeafe;
  color: #2563eb;
  transform: translateX(-4px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.back-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 8px;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.back-link:hover .back-icon-container {
  transform: translateX(-2px) scale(1.05);
  background-color: #2563eb;
  color: white;
}

.back-link i {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.back-link span {
  padding-right: 6px;
  font-size: 14px;
}

.course-title {
  font-size: 20px;
  margin: 0;
}

.nav-right {
  display: flex;
  align-items: center;
}

.progress-container {
  margin-right: 25px;
}

.progress-text {
  font-size: 14px;
  margin-bottom: 5px;
}

.progress-bar {
  height: 6px;
  width: 150px;
  background: #e1e1e1;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a6cf7, #66b6ff);
  border-radius: 3px;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  margin-right: 10px;
  font-weight: 500;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #4a6cf7;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 主内容布局 */
.main-content {
  display: flex;
  padding: 20px;
  gap: 20px;
  align-items: flex-start;
}

.content-left {
  flex: 1;
  min-width: 0;
}

.content-right {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 300px;
  height: calc(100vh - 180px);
  position: sticky;
  top: 20px;
  border: 1px solid #e4e7ed;
}

/* 视频播放器 */
.video-player-container {
  position: relative;
  background-color: black;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.video-player {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 宽高比 */
  height: 0;
  overflow: hidden;
}

.video-player iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 标签导航栏 */
.tab-navigation {
  display: flex;
  align-items: center;
  margin-top: 20px;
  border-bottom: 1px solid #eaeaea;
  background-color: white;
  position: relative;
}

.tab {
  padding: 16px 20px;
  cursor: pointer;
  font-weight: 500;
  color: #4b5563;
  transition: all 0.3s ease;
  position: relative;
}

.tab.active {
  color: #3b82f6;
}

.tab.active::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background-color: #3b82f6;
}

.tab:hover {
  color: #3b82f6;
}

/* 视频控制按钮 */
.video-controls {
  display: flex;
  align-items: center;
  margin-left: auto;
  padding-right: 16px;
}

.video-control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  margin-left: 8px;
  border: none;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.video-control-btn i {
  font-size: 20px;
  margin-bottom: 4px;
}

.video-control-btn span {
  font-size: 12px;
  font-weight: 500;
}

.video-control-btn:hover {
  color: #3b82f6;
  background-color: #f0f7ff;
}

.video-control-btn.active {
  color: #3b82f6;
}

/* 课程概述内容样式 */
.overview-content {
  background: #fff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 24px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.section-description {
  line-height: 1.6;
  color: #5a5a5a;
  margin-bottom: 25px;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f7fe;
  padding: 15px;
  border-radius: 8px;
  margin: 0 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-card:first-child {
  margin-left: 0;
}

.stat-card:last-child {
  margin-right: 0;
}

.stat-card i, .stat-card .el-icon {
  font-size: 24px;
  color: #4a6cf7;
  margin-right: 15px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-card .stat-icon {
  font-size: 26px;
  color: #4a6cf7;
  width: 30px;
  height: 30px;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.learning-points {
  margin-bottom: 30px;
}

.learning-points h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.learning-points ul {
  padding-left: 20px;
}

.learning-points li {
  margin-bottom: 10px;
  line-height: 1.5;
}

.key-concept {
  background: #e5efff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.key-concept h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.key-concept p {
  line-height: 1.6;
}

.code-example {
  margin-bottom: 30px;
}

.code-example h3 {
  font-size: 18px;
  margin-bottom: 15px;
}

.code-example pre {
  background: #2c3e50;
  color: #f8f8f2;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  line-height: 1.5;
}

.note-warning {
  background: #fff9e6;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #ffcc00;
}

.note-warning h3 {
  font-size: 18px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.note-warning i, .note-warning .el-icon {
  color: #ffcc00;
  margin-right: 10px;
}

.note-warning .warning-icon {
  font-size: 22px;
  color: #E6A23C;
  margin-right: 10px;
}

/* 评论讨论内容样式 */
.discussion-content {
  background: #fff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.comment-input-container {
  margin-bottom: 30px;
}

.comment-input {
  width: 100%;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  margin-bottom: 10px;
}

.comment-submit {
  background: #4a6cf7;
  color: white;
  padding: 6px 20px;
  float: right;
}

.comments-list {
  margin-top: 40px;
}

.comment-item {
  margin-bottom: 25px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.comment-main {
  display: flex;
}

.comment-avatar {
  margin-right: 15px;
}

.comment-avatar img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comment-username {
  font-weight: 600;
  color: #2c3e50;
}

.instructor-badge {
  background: #4a6cf7;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-text {
  line-height: 1.5;
  margin-bottom: 10px;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

.action-btn {
  background: transparent;
  color: #666;
  padding: 5px 10px;
  font-size: 13px;
}

.action-btn:hover {
  color: #4a6cf7;
  background: transparent;
  box-shadow: none;
}

.replies-list {
  margin-left: 65px;
  margin-top: 15px;
}

.reply-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.reply-avatar {
  margin-right: 15px;
}

.reply-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.reply-username {
  font-weight: 600;
  color: #2c3e50;
}

.reply-time {
  font-size: 12px;
  color: #999;
}

.reply-text {
  line-height: 1.5;
  margin-bottom: 8px;
}

.reply-actions {
  display: flex;
}

.reply-input-container {
  margin-top: 15px;
}

.reply-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
}

.reply-submit {
  background: #4a6cf7;
  color: white;
  padding: 8px 15px;
  font-size: 13px;
}

/* 右侧课程目录样式 */
.course-sidebar-header {
  padding: 20px;
  background: #ecf5ff;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-title {
  font-size: 16px;
  margin: 0 0 12px 0;
  color: #303133;
  font-weight: 600;
}

.course-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
  font-size: 13px;
  color: #606266;
}

.instructor, .lessons-count {
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-icon {
  color: #409eff;
}

.sidebar-progress {
  margin-top: 12px;
}

.progress-text {
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}

.progress-bar {
  height: 6px;
  background: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #67c23a;
  border-radius: 3px;
  transition: width 0.5s ease;
}

.chapter-toggle .rotate-icon {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.chapter-list {
  max-height: calc(100vh - 280px);
  overflow-y: auto;
}

.chapter-list::-webkit-scrollbar {
  width: 4px;
}

.chapter-list::-webkit-scrollbar-track {
  background: transparent;
}

.chapter-list::-webkit-scrollbar-thumb {
  background: #d1d1d1;
  border-radius: 10px;
}

.chapter-item {
  border-bottom: 1px dashed #e0e0e0;
}

.chapter-header {
  padding: 14px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.chapter-header:hover {
  background: #f5f5f5;
}

.chapter-expanded {
  background: #f8f9ff;
  border-left: 3px solid #3f51b5;
}

.chapter-title {
  font-weight: 500;
  font-size: 14px;
  color: #424242;
}

.chapter-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
}

.chapter-expanded .chapter-toggle {
  transform: rotate(0);
  color: #3f51b5;
  background: rgba(63, 81, 181, 0.1);
}

.chapter-sections {
  background: #fcfcfc;
  border-top: 1px solid #f0f0f0;
}

.section-item {
  display: flex;
  align-items: center;
  padding: 10px 20px 10px 35px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.section-item:hover {
  background: #f0f4ff;
}

.section-item::before {
  content: "";
  position: absolute;
  left: 22px;
  top: 50%;
  height: 100%;
  width: 1px;
  background: #e0e0e0;
  transform: translateY(-50%);
}

.section-item:last-child::before {
  height: 50%;
  top: 0;
  transform: none;
}

.section-item:first-child::before {
  height: 50%;
  top: 50%;
  transform: none;
}

.section-status {
  margin-right: 10px;
  z-index: 1;
}

.section-status i {
  font-size: 14px;
  background: #fcfcfc;
  border-radius: 50%;
  padding: 2px;
}

.section-completed i {
  color: #4caf50;
}

.section-current i {
  color: #ff9800;
}

.section-pending i {
  color: #bdbdbd;
}

.section-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  flex: 1;
  font-weight: 400;
  color: #505050;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

.section-current .section-title {
  font-weight: 500;
  color: #3f51b5;
}

.section-duration {
  font-size: 12px;
  color: #9e9e9e;
  white-space: nowrap;
  margin-left: 8px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* 模态框通用样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.modal-close {
  background: transparent;
  color: #666;
  padding: 5px;
  font-size: 18px;
}

.modal-close:hover {
  color: #f44336;
  background: transparent;
  box-shadow: none;
}

/* 笔记模态框样式 */
.note-modal {
  background: white;
  width: 90%;
  max-width: 1000px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}

.note-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.notes-sidebar {
  width: 250px;
  border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
}

.notes-search {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.notes-search input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.notes-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.note-list-item {
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  transition: background 0.2s;
}

.note-list-item:hover {
  background: #f5f7fe;
}

.note-list-item.active {
  background: #eef2fd;
  border-left: 3px solid #4a6cf7;
}

.note-list-title {
  font-weight: 500;
  margin-bottom: 5px;
}

.note-list-meta {
  font-size: 12px;
  color: #666;
}

.new-note-btn {
  width: 100%;
  margin-top: 15px;
  background: #4a6cf7;
  color: white;
}

.note-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
}

.note-editor-header {
  margin-bottom: 15px;
}

.note-title-input {
  width: 100%;
  padding: 10px;
  font-size: 18px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.note-meta-info {
  font-size: 13px;
  color: #666;
}

.editor-toolbar {
  display: flex;
  padding: 8px 0;
  border: 1px solid #eee;
  border-radius: 4px 4px 0 0;
  background: #f9f9f9;
}

.toolbar-btn {
  background: transparent;
  color: #555;
  padding: 5px 12px;
  border-radius: 0;
}

.toolbar-btn:hover {
  background: #eee;
  box-shadow: none;
}

.note-content-editor {
  flex: 1;
  width: 100%;
  padding: 15px;
  border: 1px solid #eee;
  border-top: none;
  border-radius: 0 0 4px 4px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.6;
}

.editor-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.save-note-btn {
  background: #4a6cf7;
  color: white;
}

/* AI视频导航模态框样式 */
.video-nav-modal {
  background: white;
  width: 90%;
  max-width: 600px;
  border-radius: 8px;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.video-nav-content {
  padding: 20px;
  overflow-y: auto;
}

.video-summary {
  margin-bottom: 25px;
}

.video-summary h4 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.video-summary p {
  line-height: 1.6;
  color: #555;
}

/* AI学伴相关样式 */
.ai-companion-floating {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 900;
  transition: all 0.3s;
}

.ai-companion-floating:hover {
  transform: scale(1.1);
}

.ai-companion-floating img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 修改AI面板的宽度，确保不会因为内容变化而拉伸 */
.ai-companion-panel {
  position: fixed;
  bottom: 10px;
  right: 10px;
  width: 350px; /* 固定宽度为350px，不再增加 */
  height: 650px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 950;
  transition: all 0.3s ease-in-out;
}

.ai-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px; /* 减少内边距 */
  background: linear-gradient(90deg, #409eff, #a0cfff);
  color: white;
}

.ai-panel-title {
  display: flex;
  align-items: center;
  gap: 14px;
}

.ai-avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  overflow: hidden;
  background: white;
  border: 2px solid rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.ai-avatar img {
  width: 90%;
  height: 90%;
  object-fit: cover;
}

.ai-info {
  display: flex;
  flex-direction: column;
}

.ai-info h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.ai-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 2px;
}

.ai-panel-actions {
  display: flex;
  /* gap: 10px; */
}

.ai-action-btn {
  --el-button-bg-color: transparent;
  --el-button-text-color: white;
  --el-button-border-color: transparent;
  --el-button-hover-text-color: white;
  --el-button-hover-bg-color: rgba(255, 255, 255, 0.25);
  --el-button-hover-border-color: transparent;
  font-size: 14px;
  transition: transform 0.2s ease;
}

.ai-action-btn:hover {
  transform: translateY(-2px);
}

.ai-action-btn.close-btn {
  /* 加入放大按钮样式 */
  width: 36px;
  height: 36px;
  font-size: 18px;
}

.ai-action-btn.close-btn .el-icon {
  font-size: 30px; /* 增加图标大小 */
  width: 30x;
  height: 30px;
}

.ai-action-btn.close-btn:hover {
  --el-button-hover-bg-color: rgba(255, 0, 0, 0.25);
  transform: translateY(-2px);
}

.ai-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 22px; /* 增加消息之间的间距 */
  background: #f9fafb;
  width: 100%;
  min-width: 0;
}

.ai-messages-container::-webkit-scrollbar {
  width: 5px;
}

.ai-messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.ai-messages-container::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 10px;
}

.ai-message {
  max-width: 90%; /* 增加消息宽度 */
  display: flex;
  position: relative;
  font-size: 14px;
  animation: messageAppear 0.3s ease-out;
  width: auto;
}

@keyframes messageAppear {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.ai-message.ai {
  align-self: flex-start;
}

.ai-message.ai .message-content {
  background: #ecf5ff;
  border-bottom-left-radius: 4px;
  color: #303133;
}

.ai-message.ai .message-content::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: -6px;
  width: 12px;
  height: 12px;
  background: #ecf5ff;
  clip-path: polygon(100% 0, 100% 100%, 0 100%);
}

.ai-message.user {
  align-self: flex-end;
  flex-direction: row;
  text-align: left;
}

.ai-message.user .message-content {
  background: #409eff;
  color: white;
  border-bottom-right-radius: 4px;
  text-align: left; /* 保持文本左对齐，即使消息是右对齐的 */
}

.ai-message.user .message-content::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: -6px;
  width: 12px;
  height: 12px;
  background: #409eff;
  clip-path: polygon(0 0, 100% 100%, 0 100%);
}

/* 优化代码块样式 */
.ai-message pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  max-width: 100%;
  white-space: pre-wrap;
  word-break: break-word;
}

.ai-message code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  max-width: 100%;
}

/* 对不同角色的头像添加特殊边框色 */
.ai-message .message-avatar {
  border-color: rgba(64, 158, 255, 0.5);
}

/* 我的学伴按钮特殊样式 */
.ai-action-btn.companion-btn {
  background: linear-gradient(90deg, #5A8BFF 0%, #7F6BFF 50%, #A66CFF 100%);
  border: none;
  box-shadow: 0 0 10px 2px rgba(122, 139, 255, 0.15), 0 1px 4px rgba(166, 108, 255, 0.08);
  width: auto;
  max-width: 140px; /* 添加最大宽度限制 */
  min-width: 40px; /* 减少最小宽度 */
  height: 36px;
  margin-right: 8px; /* 增加右边距 */
  position: relative;
  overflow: hidden;
  z-index: 1;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px; /* 减小字体大小 */
  color: #fff !important;
  letter-spacing: 0.5px;
  transition: transform 0.18s cubic-bezier(.4,2,.6,1), box-shadow 0.3s, background 0.3s;
  animation: companion-pulse 1.6s infinite;
  cursor: pointer;
  padding: 0 8px; /* 减少内边距 */
}

.ai-action-btn.companion-btn span {
  font-weight: bold;
  font-size: 12px; /* 减小字体大小 */
  color: #fff !important;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 4px rgba(122, 139, 255, 0.12);
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出的文本 */
  text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
  max-width: 90px; /* 限制文本宽度 */
}

.ai-action-btn.companion-btn:hover {
  transform: scale(1.06) translateY(-1px) rotate(-1deg);
  box-shadow: 0 0 18px 4px rgba(122, 139, 255, 0.18), 0 2px 8px rgba(166, 108, 255, 0.10);
  background: linear-gradient(90deg, #A66CFF 0%, #7F6BFF 60%, #5A8BFF 100%);
  animation: companion-bounce 0.4s;
}

@keyframes companion-pulse {
  0% { box-shadow: 0 0 10px 2px rgba(122, 139, 255, 0.15); }
  50% { box-shadow: 0 0 18px 6px rgba(122, 139, 255, 0.22); }
  100% { box-shadow: 0 0 10px 2px rgba(122, 139, 255, 0.15); }
}

@keyframes companion-sparkle {
  0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
  50% { opacity: 0.7; transform: scale(1.18) rotate(15deg); }
}

@keyframes companion-bounce {
  0% { transform: scale(1) translateY(0) rotate(0deg); }
  30% { transform: scale(1.12, 0.92) translateY(-4px) rotate(-2deg); }
  50% { transform: scale(0.96, 1.08) translateY(2px) rotate(2deg); }
  70% { transform: scale(1.04, 0.98) translateY(-1px) rotate(-1deg); }
  100% { transform: scale(1.09) translateY(-2px) rotate(-2deg); }
}

/* 老师角色 */
.ai-message[class*="teacher"] .message-avatar {
  border-color: rgba(64, 158, 255, 0.8);
}

/* 助教角色 */
.ai-message[class*="assistant"] .message-avatar {
  border-color: rgba(255, 152, 0, 0.8);
}

/* 思考者角色 */
.ai-message[class*="thinker"] .message-avatar {
  border-color: rgba(102, 177, 255, 0.8);
}

/* 好奇宝宝角色 */
.ai-message[class*="questioner"] .message-avatar {
  border-color: rgba(230, 162, 60, 0.8);
}

/* 笔记员角色 */
.ai-message[class*="notetaker"] .message-avatar {
  border-color: rgba(103, 194, 58, 0.8);
}

/* 黑眼包角色 */
.ai-message[class*="challenger"] .message-avatar {
  border-color: rgba(236, 99, 99, 0.8);
}

.ai-input-container {
  padding: 16px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: white;
  position: relative; /* Add position relative to container */
  width: 100%; /* Ensure container takes full width */
}

.ai-input {
  flex: 1;
  width: 100%; /* Ensure input takes full width */
}

.ai-input :deep(.el-textarea__inner) {
  border-radius: 10px;
  padding: 12px;
  padding-right: 50px; /* Add padding to the right for the button */
  resize: none;
  transition: all 0.3s;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  width: 100%; /* Ensure textarea takes full width */
  min-height: 100px; /* Increase minimum height */
}

.ai-input :deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 2px #409eff30 inset;
}

.ai-send-btn {
  --el-button-bg-color: #ece9e9;
  --el-button-text-color: #409eff;
  --el-button-border-color: #409eff;
  --el-button-hover-text-color: #2a7ad4;
  --el-button-hover-bg-color: #d5cfcf;
  --el-button-hover-border-color: #c4d8ff;
  font-size: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: absolute; /* Position absolutely */
  bottom: 25px; /* Position at bottom */
  right: 25px; /* Position at right */
  z-index: 10; /* Ensure button is above textarea */
}

.ai-send-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 通知系统样式 */
.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  z-index: 1100;
  animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.notification.success {
  background: #4caf50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196f3;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(20px); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .content-right {
    width: 100%;
    margin-top: 20px;
  }
  
  .chapter-list {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .top-navigation {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .nav-right {
    width: 100%;
    margin-top: 15px;
    justify-content: space-between;
  }
  
  .course-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-card {
    margin: 0;
  }
  
  .note-container {
    flex-direction: column;
  }
  
  .notes-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #eee;
  }
  
  .ai-companion-panel {
    width: 95%;
    height: 70%;
    left: 2.5%;
    right: 2.5%;
    bottom: 15%;
  }
  
  .note-modal, .video-nav-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .ai-panel-actions {
    gap: 6px;
  }
  
  .ai-message {
    max-width: 90%;
  }
  
  .ai-action-toolbar {
    padding: 8px 10px;
    gap: 8px;
    overflow-x: auto;
    justify-content: space-between;
  }
  
  .ai-toolbar-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .ai-toolbar-btn span {
    font-size: 11px;
  }
}

.ai-action-toolbar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 15px;
  gap: 0px;
  background: #f0f6ff;
  border-top: 1px solid #e8f1ff;
}

.ai-toolbar-btn {
  --el-button-bg-color: #ffffff;
  --el-button-text-color: #409eff;
  --el-button-border-color: #e0e7ff;
  --el-button-hover-text-color: #2a7ad4;
  --el-button-hover-bg-color: #ecf5ff;
  --el-button-hover-border-color: #c4d8ff;
  font-size: 13px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 3px 8px;
}

.ai-toolbar-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ai-toolbar-btn .el-icon {
  margin-right: 2px;
}

.ai-toolbar-btn span {
  font-size: 12px;
  font-weight: 500;
}

.companion-dialog-content {
  padding: 15px 20px !important;
}

.companion-section-title {
  font-size: 14px;
  margin: 10px 0 8px 0;
  color: #333;
  font-weight: 500;
}

.companion-options-row {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
  justify-content: flex-start;
  width: 100%;
}

.companion-option {
  position: relative;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  flex: 1;
  max-width: 100%;
  width: 100%;
}

.companion-option.option-selected {
  border-color: #409EFF !important;
  background-color: #f0f7ff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
}

.required-roles-note {
  font-size: 12px;
  font-weight: normal;
  color: #409EFF;
  margin-left: 8px;
}

.optional-roles-note {
  font-size: 12px;
  font-weight: normal;
  color: #909399;
  margin-left: 8px;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 10px;
  border: 2px solid #ecf5ff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.message-avatar.user-avatar {
  margin-right: 0;
  margin-left: 10px;
  border-color: #d6e9ff;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  max-width: calc(100% - 50px);
  flex: 1;
  min-width: 0; /* 重要：保证弹性项目可以收缩到比内容更小 */
  width: 100%;
}

.message-sender {
  font-size: 12px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.message-content {
  padding: 14px 16px;
  border-radius: 12px;
  line-height: 1.5;
  position: relative;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
}

.message-content code {
  background-color: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 90%;
  color: #e83e8c;
}

.message-content .code-block {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 10px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 确保用户消息中的代码也有正确样式 */
.ai-message.user .message-content .code-block {
  background-color: #1a365d;
  color: #e2e8f0;
}

.companion-options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 12px;
  width: 100%;
}

.companion-option-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.companion-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
  border: 2px solid rgba(64, 158, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.companion-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.companion-info {
  flex: 1;
  position: relative;
  min-width: 0; /* 确保可以收缩 */
  overflow: hidden; /* 确保内容溢出时被隐藏 */
}

.companion-info:hover .companion-desc::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -30px;
  left: 0;
  background: rgba(0, 0, 0, 0.75);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: normal;
  width: max-content;
  max-width: 200px;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.companion-info:hover .companion-desc::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 15px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent rgba(0, 0, 0, 0.75) transparent;
  z-index: 10;
}

/* 角色名称和描述样式 */
.companion-name {
  font-weight: 500;
  font-size: 13px;
  color: #333;
  margin-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.companion-desc {
  font-size: 11px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 130px;
  line-height: 1.3;
}

.companion-checkbox, .companion-radio {
  width: 100%;
  display: flex;
}

.companion-checkbox :deep(.el-checkbox__input),
.companion-radio :deep(.el-radio__input) {
  align-self: flex-start;
  margin-top: 12px;
}

.companion-checkbox :deep(.el-checkbox__label),
.companion-radio :deep(.el-radio__label) {
  padding-left: 10px;
  display: flex;
  flex: 1;
  min-width: 0; /* 确保可以收缩 */
  width: 100%;
}

.companion-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e9ecef;
}

.companion-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.companion-dialog-header .close-btn {
  padding: 4px;
  font-size: 18px;
  color: #606266;
}

.companion-dialog-header .close-btn:hover {
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

.companion-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 550px !important; /* 增加宽度，更好地显示完整内容 */
}

.companion-dialog-content {
  padding: 0 !important;
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
  gap: 8px;
}

.companion-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e9ecef;
}

.companion-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.companion-dialog :deep(.el-dialog__headerbtn) {
  top: 12px;
  right: 16px;
}

.companion-dialog :deep(.el-dialog__body) {
  padding: 12px 16px;
}

.companion-dialog :deep(.el-dialog__footer) {
  padding: 8px 16px 16px;
  border-top: none;
}