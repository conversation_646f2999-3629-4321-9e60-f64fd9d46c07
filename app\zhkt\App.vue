<script>
import { useUserStore } from '@/store/modules/user'
import TabBar from './components/tabbar/index.vue'
import tabBarConfig from './config/tabbar'

export default {
	components: {
		TabBar
	},

	data() {
		return {
			currentTab: 0
		}
	},

	onLaunch() {
		console.log('App Launch')
		// 初始化
		this.initApp()
	},

	onShow() {
		console.log('App Show')
	},

	onHide() {
		console.log('App Hide')
	},

	methods: {
		// 初始化应用
		async initApp() {
			// 检查更新
			this.checkUpdate()
			// 获取用户信息
			await this.getUserInfo()
		},

		// 检查更新
		checkUpdate() {
			if (uni.canIUse('getUpdateManager')) {
				const updateManager = uni.getUpdateManager()
				updateManager.onCheckForUpdate((res) => {
					if (res.hasUpdate) {
						updateManager.onUpdateReady(() => {
							uni.showModal({
								title: '更新提示',
								content: '新版本已经准备好，是否重启应用？',
								success: (res) => {
									if (res.confirm) {
										updateManager.applyUpdate()
									}
								}
							})
						})
					}
				})
			}
		},

		// 获取用户信息
		async getUserInfo() {
			const userStore = useUserStore()
			// 如果已经有token则获取用户信息
			if (userStore.token) {
				try {
					await userStore.getUserInfo()
				} catch (error) {
					console.error('获取用户信息失败', error)
					// 可以添加更友好的错误提示
					uni.showToast({
						title: '获取用户信息失败',
						icon: 'none'
					})
				}
			} else {
				// 如果没有登录则跳转到登录页
				uni.reLaunch({
					url: './pages/login/login'
				})
			}
		},

		// 处理 TabBar 切换
		handleTabChange(index) {
			this.currentTab = index
		}
	}
}
</script>

<style lang="scss">
@import './styles/index.scss';

.container {
	min-height: 100vh;
	padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
}
</style>

<template>
	<view class="container">
		<!-- 页面内容 -->
		<slot></slot>

		<!-- 自定义 TabBar -->
		<tab-bar
			v-model="currentTab"
			:items="tabBarConfig.list"
			:active-color="tabBarConfig.selectedColor"
			@change="handleTabChange"
		/>
	</view>
</template>
