<template>
  <!-- 
    This component is just a wrapper around the Element Plus message system.
    The actual rendering is handled by invoking the ElMessage API programmatically.
  -->
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { defineEmits } from 'vue';

// Expose an API to show messages
const showMessage = (options) => {
  return ElMessage({
    message: options.message,
    type: options.type || 'info',
    duration: options.duration || 3000,
    showClose: options.showClose || false,
    grouping: options.grouping || false,
    offset: options.offset || 20,
    onClose: options.onClose
  });
};

const showSuccess = (message, options = {}) => {
  return showMessage({ ...options, message, type: 'success' });
};

const showError = (message, options = {}) => {
  return showMessage({ ...options, message, type: 'error' });
};

const showWarning = (message, options = {}) => {
  return showMessage({ ...options, message, type: 'warning' });
};

const showInfo = (message, options = {}) => {
  return showMessage({ ...options, message, type: 'info' });
};

// Expose confirm dialog
const showConfirm = (options) => {
  return ElMessageBox.confirm(
    options.message || 'Are you sure?',
    options.title || 'Confirm',
    {
      confirmButtonText: options.confirmText || 'OK',
      cancelButtonText: options.cancelText || 'Cancel',
      type: options.type || 'warning',
      ...options
    }
  );
};

// Expose the API to the component's users
defineExpose({
  showMessage,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  showConfirm
});
</script>

<script>
// Export the API for direct import
export const useToast = () => {
  const showMessage = (options) => {
    return ElMessage({
      message: options.message,
      type: options.type || 'info',
      duration: options.duration || 3000,
      showClose: options.showClose || false,
      grouping: options.grouping || false,
      offset: options.offset || 20
    });
  };

  return {
    showMessage,
    showSuccess: (message, options = {}) => showMessage({ ...options, message, type: 'success' }),
    showError: (message, options = {}) => showMessage({ ...options, message, type: 'error' }),
    showWarning: (message, options = {}) => showMessage({ ...options, message, type: 'warning' }),
    showInfo: (message, options = {}) => showMessage({ ...options, message, type: 'info' }),
    showConfirm: (options) => {
      return ElMessageBox.confirm(
        options.message || 'Are you sure?',
        options.title || 'Confirm',
        {
          confirmButtonText: options.confirmText || 'OK',
          cancelButtonText: options.cancelText || 'Cancel',
          type: options.type || 'warning',
          ...options
        }
      );
    }
  };
};
</script> 