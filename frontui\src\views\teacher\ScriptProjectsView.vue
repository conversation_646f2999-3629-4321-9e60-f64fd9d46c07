<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="讲稿内容"
    activePage="content-creation"
    activeSubPage="script"
  >
    <div class="space-y-6">
      <div class="flex flex-wrap justify-between items-center gap-4 mb-6">

        
        <!-- 搜索与筛选 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
          <div class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-[300px]">
              <div class="relative">
                <input 
                  type="text" 
                  v-model="searchQuery"
                  placeholder="搜索讲稿..." 
                  class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <i class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</i>
              </div>
            </div>
            
            <div class="flex flex-wrap gap-3">
              <div>
                <select 
                  v-model="subjectFilter" 
                  class="border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                >
                  <option value="all">所有学科</option>
                  <option value="python">Python</option>
                  <option value="math">数学</option>
                  <option value="english">英语</option>
                  <option value="ecommerce">电子商务</option>
                  <option value="ai">人工智能</option>
                </select>
              </div>
              
              <div>
                <select 
                  v-model="statusFilter" 
                  class="border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                >
                  <option value="all">所有状态</option>
                  <option value="draft">草稿</option>
                  <option value="in-progress">进行中</option>
                  <option value="review">审核中</option>
                  <option value="completed">已完成</option>
                </select>
              </div>
              
              <div>
                <select 
                  v-model="sortOption" 
                  class="border border-gray-300 rounded-md px-3 py-2 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                >
                  <option value="recent">最近更新</option>
                  <option value="oldest">最早创建</option>
                  <option value="az">名称 A-Z</option>
                  <option value="za">名称 Z-A</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        <button 
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
          @click="createNewScript"
        >
          <i class="material-icons text-sm">add</i>
          新建讲稿
        </button>
      </div>

      <!-- 统计卡片 -->
      <div class="flex flex-wrap gap-4">
        <div class="bg-purple-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">审核中</p>
              <p class="text-2xl font-bold text-gray-800">22</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
              <i class="material-icons text-purple-600 text-xl">description</i>
            </div>
          </div>
        </div>
        <div class="bg-green-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">已完成</p>
              <p class="text-2xl font-bold text-gray-800">14</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
              <i class="material-icons text-green-600 text-xl">check_circle</i>
            </div>
          </div>
        </div>
        <div class="bg-blue-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">进行中</p>
              <p class="text-2xl font-bold text-gray-800">6</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <i class="material-icons text-blue-600 text-xl">hourglass_empty</i>
            </div>
          </div>
        </div>
        <div class="bg-red-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">草稿</p>
              <p class="text-2xl font-bold text-gray-800">2</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
              <i class="material-icons text-red-600 text-xl">edit</i>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目列表 -->
      <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                讲稿名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学科
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                字数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                更新时间
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="project in paginatedProjects" :key="project.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 w-1.5 h-8 rounded-sm" :class="getSubjectColorClass(project.subject)"></div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ project.title }}</div>
                    <div class="text-xs text-gray-500 max-w-md truncate">{{ project.description }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span :class="`w-6 h-6 rounded-full flex items-center justify-center ${getSubjectBgClass(project.subject)}`">
                    <i class="material-icons text-xs">{{ getSubjectIconClass(project.subject) }}</i>
                  </span>
                  <span class="ml-2 text-sm text-gray-700">{{ project.subject }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ project.wordCount }} 字</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  :class="`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClasses(project.status)}`"
                >
                  {{ getStatusLabel(project.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">{{ project.updatedAt }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  class="text-blue-600 hover:text-blue-900 mr-3" 
                  @click="viewProject(project.id)"
                >
                  查看
                </button>
                <button 
                  class="text-green-600 hover:text-green-900" 
                  @click="editProject(project.id)"
                >
                  编辑
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-if="paginatedProjects.length === 0" class="bg-white rounded-lg p-12 shadow-sm border border-gray-100 text-center">
        <div class="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <i class="material-icons text-gray-400 text-2xl">search</i>
        </div>
        <h3 class="text-lg font-medium text-gray-800 mb-2">未找到讲稿</h3>
        <p class="text-gray-600 mb-4">尝试调整搜索条件或创建新的讲稿</p>
        <button 
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
          @click="createNewScript"
        >
          新建讲稿
        </button>
      </div>

      <!-- 分页控制 -->
      <div v-if="filteredProjects.length > 0" class="mt-5 flex justify-between items-center">
        <div class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ startItem }}-{{ endItem }}</span> 条，共 <span class="font-medium">{{ filteredProjects.length }}</span> 条
        </div>
        <div class="flex items-center space-x-2">
          <button 
            class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="currentPage === 1"
            @click="currentPage--"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            上一页
          </button>
          
          <div v-for="pageNumber in displayedPageNumbers" :key="pageNumber">
            <button 
              v-if="pageNumber !== '...'"
              @click="currentPage = pageNumber"
              class="px-3 py-1 rounded-md text-sm font-medium"
              :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'"
            >
              {{ pageNumber }}
            </button>
            <span v-else class="text-gray-500 px-2">...</span>
          </div>
          
          <button 
            class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            下一页
          </button>
          
          <div class="flex items-center ml-2">
            <span class="text-sm text-gray-700 mr-2">前往</span>
            <input 
              type="number" 
              v-model.number="goToPage" 
              min="1" 
              :max="totalPages"
              class="w-12 border border-gray-300 rounded-md px-2 py-1 text-sm"
            />
            <span class="text-sm text-gray-700 mx-2">页</span>
            <button 
              @click="jumpToPage"
              class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-blue-700 hover:bg-blue-50"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

// Router
const router = useRouter()

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 搜索和筛选
const searchQuery = ref('')
const subjectFilter = ref('all')
const statusFilter = ref('all')
const sortOption = ref('recent')

// 分页
const currentPage = ref(1)
const pageSize = ref(9)
const goToPage = ref(1)

// 示例讲稿项目数据
const scriptProjects = ref([
  {
    id: 1,
    title: 'Python编程基础入门课',
    description: '针对零基础学生的Python编程导论，涵盖基本概念、语法和简单应用示例。',
    subject: 'Python',
    wordCount: 3500,
    status: 'completed',
    updatedAt: '2023-06-15'
  },
  {
    id: 2,
    title: '人工智能伦理与社会影响',
    description: '探讨AI技术发展对社会的影响，以及相关的伦理道德问题和未来趋势。',
    subject: '人工智能',
    wordCount: 4200,
    status: 'in-progress',
    updatedAt: '2023-06-20'
  },
  {
    id: 3,
    title: '高等数学微积分应用',
    description: '微积分在自然科学和工程领域的实际应用案例分析与讲解。',
    subject: '数学',
    wordCount: 5100,
    status: 'completed',
    updatedAt: '2023-06-12'
  },
  {
    id: 4,
    title: '商务英语沟通技巧',
    description: '商务环境中的英语口语表达、邮件写作和谈判技巧讲解。',
    subject: '英语',
    wordCount: 2800,
    status: 'review',
    updatedAt: '2023-06-18'
  },
  {
    id: 5,
    title: '电商平台运营策略',
    description: '电子商务平台的用户增长、留存和转化率优化策略详解。',
    subject: '电子商务',
    wordCount: 3800,
    status: 'completed',
    updatedAt: '2023-06-10'
  },
  {
    id: 6,
    title: 'Python数据分析实战',
    description: '使用pandas和numpy进行数据清洗、分析和可视化的实操指南。',
    subject: 'Python',
    wordCount: 4500,
    status: 'in-progress',
    updatedAt: '2023-06-22'
  },
  {
    id: 7,
    title: '学术英语论文写作',
    description: '学术论文的结构、语言特点和常见表达方式详解。',
    subject: '英语',
    wordCount: 3200,
    status: 'draft',
    updatedAt: '2023-06-21'
  },
  {
    id: 8,
    title: '概率论与统计学基础',
    description: '概率分布、假设检验和回归分析等统计学核心概念讲解。',
    subject: '数学',
    wordCount: 4800,
    status: 'completed',
    updatedAt: '2023-06-11'
  },
  {
    id: 9,
    title: '深度学习原理与应用',
    description: '神经网络结构、训练方法和常见深度学习框架介绍。',
    subject: '人工智能',
    wordCount: 5500,
    status: 'completed',
    updatedAt: '2023-06-14'
  },
  {
    id: 10,
    title: '跨境电商市场分析',
    description: '全球主要跨境电商市场特点、准入条件和消费者行为分析。',
    subject: '电子商务',
    wordCount: 3600,
    status: 'in-progress',
    updatedAt: '2023-06-19'
  },
  {
    id: 11,
    title: 'Python Web开发入门',
    description: '使用Flask框架进行基础Web应用开发的入门指南。',
    subject: 'Python',
    wordCount: 4200,
    status: 'review',
    updatedAt: '2023-06-17'
  },
  {
    id: 12,
    title: '离散数学与算法设计',
    description: '离散数学在算法设计中的应用，包括图论、组合数学等内容。',
    subject: '数学',
    wordCount: 5200,
    status: 'draft',
    updatedAt: '2023-06-23'
  }
])

// 根据筛选条件过滤项目
const filteredProjects = computed(() => {
  let result = scriptProjects.value

  // 关键词搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(project => 
      project.title.toLowerCase().includes(query) || 
      project.description.toLowerCase().includes(query) ||
      project.subject.toLowerCase().includes(query)
    )
  }

  // 学科筛选
  if (subjectFilter.value !== 'all') {
    result = result.filter(project => 
      project.subject.toLowerCase() === subjectFilter.value.toLowerCase()
    )
  }

  // 状态筛选
  if (statusFilter.value !== 'all') {
    result = result.filter(project => project.status === statusFilter.value)
  }

  // 排序
  if (sortOption.value === 'recent') {
    result = [...result].sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
  } else if (sortOption.value === 'oldest') {
    result = [...result].sort((a, b) => new Date(a.updatedAt) - new Date(b.updatedAt))
  } else if (sortOption.value === 'az') {
    result = [...result].sort((a, b) => a.title.localeCompare(b.title))
  } else if (sortOption.value === 'za') {
    result = [...result].sort((a, b) => b.title.localeCompare(a.title))
  }

  return result
})

// 分页后的项目列表
const paginatedProjects = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredProjects.value.slice(startIndex, startIndex + pageSize.value)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredProjects.value.length / pageSize.value) || 1
})

// 显示项目范围
const startItem = computed(() => {
  return (currentPage.value - 1) * pageSize.value + 1
})

const endItem = computed(() => {
  return Math.min(currentPage.value * pageSize.value, filteredProjects.value.length)
})

// 显示页码
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 跳转到指定页面
const jumpToPage = () => {
  if (goToPage.value >= 1 && goToPage.value <= totalPages.value) {
    currentPage.value = goToPage.value
  } else {
    goToPage.value = currentPage.value
  }
}

// 获取状态标签和样式
const getStatusLabel = (status) => {
  const statusMap = {
    'completed': '已完成',
    'in-progress': '进行中',
    'review': '审核中',
    'draft': '草稿'
  }
  return statusMap[status] || status
}

const getStatusClasses = (status) => {
  const statusClassMap = {
    'completed': 'bg-green-100 text-green-800',
    'in-progress': 'bg-blue-100 text-blue-800',
    'review': 'bg-purple-100 text-purple-800',
    'draft': 'bg-red-100 text-red-800'
  }
  return statusClassMap[status] || 'bg-gray-100 text-gray-800'
}

// 获取学科相关样式
const getSubjectColorClass = (subject) => {
  const subjectColorMap = {
    'Python': 'bg-blue-500',
    '数学': 'bg-green-500',
    '英语': 'bg-yellow-500',
    '电子商务': 'bg-purple-500',
    '人工智能': 'bg-red-500'
  }
  return subjectColorMap[subject] || 'bg-gray-500'
}

const getSubjectBgClass = (subject) => {
  const subjectBgMap = {
    'Python': 'bg-blue-100 text-blue-600',
    '数学': 'bg-green-100 text-green-600',
    '英语': 'bg-yellow-100 text-yellow-600',
    '电子商务': 'bg-purple-100 text-purple-600',
    '人工智能': 'bg-red-100 text-red-600'
  }
  return subjectBgMap[subject] || 'bg-gray-100 text-gray-600'
}

const getSubjectIconClass = (subject) => {
  const subjectIconMap = {
    'Python': 'code',
    '数学': 'calculate',
    '英语': 'translate',
    '电子商务': 'shopping_cart',
    '人工智能': 'smart_toy'
  }
  return subjectIconMap[subject] || 'book'
}

// 项目操作方法
const createNewScript = () => {
  console.log('创建新讲稿')
  // 跳转到创建页面
  router.push('/teacher/script/create')
}

const viewProject = (projectId) => {
  console.log('查看讲稿:', projectId)
  // 跳转到查看页面
  // router.push(`/teacher/script/${projectId}`)
}

const editProject = (projectId) => {
  console.log('编辑讲稿:', projectId)
  // 跳转到编辑页面
  // router.push(`/teacher/script/${projectId}/edit`)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
