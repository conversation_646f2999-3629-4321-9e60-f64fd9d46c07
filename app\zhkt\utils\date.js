/**
 * 格式化日期时间
 * @param {string|number|Date} date 日期时间
 * @param {string} [format='YYYY-MM-DD HH:mm'] 格式化模板
 * @returns {string} 格式化后的日期时间字符串
 * 
 * @example
 * formatDate('2024-03-15 14:30:00') // 2024-03-15 14:30
 * formatDate('2024-03-15T14:30:00.081030') // 2024-03-15 14:30
 * formatDate(1710489000000) // 2024-03-15 14:30
 * formatDate(new Date(), 'MM-DD HH:mm') // 03-15 14:30
 * formatDate(new Date(), 'YYYY年MM月DD日 HH:mm') // 2024年03月15日 14:30
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm') {
  if (!date) return ''
  
  // 转换为Date对象
  let dateObj
  if (date instanceof Date) {
    dateObj = date
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else {
    // 处理ISO格式的日期字符串（包含T和毫秒的情况）
    const dateStr = date.replace('T', ' ').split('.')[0]
    dateObj = new Date(dateStr.replace(/-/g, '/')) // 兼容Safari
  }
  
  // 如果日期无效，返回空字符串
  if (isNaN(dateObj.getTime())) return ''
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  
  // 替换格式化模板中的占位符
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
}

/**
 * 获取友好的时间显示
 * @param {string|number|Date} date 日期时间
 * @returns {string} 友好的时间显示
 * 
 * @example
 * getFriendlyDate('2024-03-15 14:30:00') // 刚刚/x分钟前/x小时前/x天前/MM-DD/YYYY-MM-DD
 * getFriendlyDate('2024-03-15T14:30:00.081030') // 同上
 */
export function getFriendlyDate(date) {
  if (!date) return ''
  
  // 处理ISO格式的日期字符串（包含T和毫秒的情况）
  const dateStr = date.replace('T', ' ').split('.')[0]
  
  // 转换为时间戳
  const timestamp = new Date(dateStr.replace(/-/g, '/')).getTime()
  const now = Date.now()
  const diff = now - timestamp
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }
  
  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }
  
  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }
  
  // 是否是今年
  const dateObj = new Date(timestamp)
  const nowObj = new Date()
  if (dateObj.getFullYear() === nowObj.getFullYear()) {
    return formatDate(date, 'MM-DD HH:mm')
  }
  
  // 不是今年
  return formatDate(date, 'YYYY-MM-DD HH:mm')
} 