<template>
  <AdminLayout 
    pageTitle="数据分析" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <!-- 数据概览卡片 -->
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 flex-col-md-row">
          <!-- 学习时长统计 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                  <span class="material-icons text-white text-xl">timer</span>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">平均学习时长</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.avgStudyTime }} 小时/周</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 课程完成率 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                  <span class="material-icons text-white text-xl">done_all</span>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">课程完成率</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.courseCompletionRate }}%</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 活跃用户数 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                  <span class="material-icons text-white text-xl">people_alt</span>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">月活跃用户</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.monthlyActiveUsers }}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 互动指数 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                  <span class="material-icons text-white text-xl">forum</span>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">互动指数</dt>
                    <dd>
                      <div class="text-lg font-medium text-gray-900">{{ stats.interactionIndex }}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主数据分析区域 -->
        <div class="mt-6 grid grid-cols-1 gap-5 lg:grid-cols-2 flex-col-md-row">
          <!-- 学习时长分布 -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900">学习时长分布</h3>
            </div>
            <div class="px-4 py-5 sm:p-6 h-80">
              <canvas ref="studyTimeChart"></canvas>
            </div>
          </div>

          <!-- 课程参与度分析 -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900">课程参与度分析</h3>
            </div>
            <div class="px-4 py-5 sm:p-6 h-80">
              <canvas ref="courseEngagementChart"></canvas>
            </div>
          </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="mt-6">
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6 flex justify-between items-center">
              <h3 class="text-lg leading-6 font-medium text-gray-900">课程数据详情</h3>
              <div class="flex space-x-3">
                <select v-model="timeRange" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                  <option value="week">本周</option>
                  <option value="month">本月</option>
                  <option value="quarter">本季度</option>
                  <option value="year">本年度</option>
                </select>
                <button @click="exportData" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                  导出数据
                </button>
              </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">课程名称</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与人数</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成率</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均学习时长</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">互动次数</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="course in courseData" :key="course.id">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ course.name }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ course.participants }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ course.completionRate }}%</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ course.avgStudyTime }}小时</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ course.interactions }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import { useAdminStore } from '@/stores/admin'
import { useAuthStore } from '@/stores/auth'
import Chart from 'chart.js/auto'

const router = useRouter()
const adminStore = useAdminStore()
const authStore = useAuthStore()

// 引用图表canvas元素
const studyTimeChart = ref(null)
const courseEngagementChart = ref(null)

// 数据筛选
const timeRange = ref('month')

// 模拟数据
const stats = ref({
  avgStudyTime: 12.5,
  courseCompletionRate: 85.2,
  monthlyActiveUsers: 1234,
  interactionIndex: 8.7
})

const courseData = ref([
  {
    id: 1,
    name: '高等数学 I',
    participants: 156,
    completionRate: 87,
    avgStudyTime: 15.2,
    interactions: 423
  },
  {
    id: 2,
    name: '线性代数',
    participants: 142,
    completionRate: 82,
    avgStudyTime: 12.8,
    interactions: 356
  },
  {
    id: 3,
    name: '概率论',
    participants: 128,
    completionRate: 79,
    avgStudyTime: 11.5,
    interactions: 298
  }
])

// 导出数据方法
const exportData = () => {
  // 实现导出逻辑
  console.log('Exporting data...')
}

onMounted(() => {
  // 初始化图表
  initCharts()
})

function initCharts() {
  // 初始化学习时长分布图表
  const studyTimeCtx = studyTimeChart.value.getContext('2d')
  new Chart(studyTimeCtx, {
    type: 'bar',
    data: {
      labels: ['0-2h', '2-4h', '4-6h', '6-8h', '8h+'],
      datasets: [{
        label: '学生人数',
        data: [120, 250, 380, 220, 95],
        backgroundColor: 'rgba(79, 70, 229, 0.8)'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: '人数'
          }
        },
        x: {
          title: {
            display: true,
            text: '每周学习时长'
          }
        }
      }
    }
  })

  // 初始化课程参与度分析图表
  const courseEngagementCtx = courseEngagementChart.value.getContext('2d')
  new Chart(courseEngagementCtx, {
    type: 'radar',
    data: {
      labels: ['观看视频', '完成作业', '参与讨论', '资料下载', '测验成绩'],
      datasets: [{
        label: '平均参与度',
        data: [85, 75, 65, 80, 70],
        fill: true,
        backgroundColor: 'rgba(79, 70, 229, 0.2)',
        borderColor: 'rgb(79, 70, 229)',
        pointBackgroundColor: 'rgb(79, 70, 229)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(79, 70, 229)'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        }
      },
      scales: {
        r: {
          angleLines: {
            display: true
          },
          suggestedMin: 0,
          suggestedMax: 100
        }
      }
    }
  })
}
</script>

<style scoped>
.flex-col-md-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  flex-wrap: wrap;
  gap: 1.25rem;
}

.flex-col-md-row > div {
  flex: 1;
  min-width: 250px;
  max-width: calc(25% - 1rem);
}

/* 主视图区域和图表区域的卡片样式 */
.mt-6.flex-col-md-row > div {
  max-width: calc(50% - 1rem);
  min-width: 300px;
}

@media (max-width: 1024px) {
  .flex-col-md-row > div {
    max-width: calc(50% - 1rem);
  }
  
  .mt-6.flex-col-md-row > div {
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .flex-col-md-row > div {
    max-width: 100%;
  }
}
</style> 