<template>
  <div class="bg-gray-50 flex items-center justify-center min-h-screen">
    <div class="max-w-md w-full mx-auto">
      <el-card class="box-card">
        <!-- 头部 -->
        <div class="p-6 header-bg text-center">
          <h1 class="text-2xl font-bold text-white">智慧课堂</h1>
          <p class="text-white mt-2">知识改变命运，智慧点亮未来</p>
        </div>
        
        <!-- 手机登录区域 -->
        <div class="p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-6 text-center">手机验证码登录</h2>
          
          <el-form @submit.native.prevent="handleMobileLogin" :model="formData">
            <!-- 手机号 -->
            <el-form-item>
              <el-input 
                v-model="formData.mobile" 
                placeholder="请输入手机号"
                prefix-icon="Iphone"
                clearable
              />
            </el-form-item>
            
            <!-- 验证码 -->
            <el-form-item>
              <div class="flex space-x-2">
                <el-input 
                  v-model="formData.code" 
                  placeholder="请输入验证码"
                  prefix-icon="Key"
                  class="flex-1"
                />
                <el-button 
                  type="primary" 
                  @click="sendCode" 
                  :disabled="countdown > 0"
                >
                  {{ countdown > 0 ? `重新发送(${countdown}s)` : '获取验证码' }}
                </el-button>
              </div>
            </el-form-item>
            
            <!-- 提示文字 -->
            <div class="mb-6 text-xs text-gray-500">
              <p>未注册的手机号将自动完成注册</p>
            </div>
            
            <!-- 登录按钮 -->
            <el-button type="primary" @click="handleMobileLogin" class="w-full">
              <el-icon class="mr-2"><Right /></el-icon> 登录 / 注册
            </el-button>
          </el-form>
          
          <!-- 分割线 -->
          <el-divider content-position="center">其他登录方式</el-divider>
          
          <!-- 其他登录方式 -->
          <div class="mt-4 flex space-x-4 justify-center">
            <router-link to="/auth/wechat-login">
              <el-button class="w-full">
                <i class="fa-brands fa-weixin text-green-600 text-2xl"></i>
              </el-button>
            </router-link>
            <router-link to="/auth/qq-login">
              <el-button class="w-full">
                <i class="fa-brands fa-qq text-blue-500 text-2xl"></i>
              </el-button>
            </router-link>
            <router-link to="/auth/login">
              <el-button class="w-full">
                <el-icon><User /></el-icon>
              </el-button>
            </router-link>
          </div>
        </div>
      </el-card>
      
      <!-- 页脚 -->
      <div class="mt-4 text-center">
        <p class="text-xs text-gray-500">
          © {{ currentYear }} 智慧课堂. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Iphone, Key, Right, User } from '@element-plus/icons-vue'

const router = useRouter()
const countdown = ref(0)
const timer = ref(null)
const currentYear = computed(() => new Date().getFullYear())

// 表单数据
const formData = reactive({
  mobile: '',
  code: ''
})

// 发送验证码
const sendCode = () => {
  if (!formData.mobile) {
    ElMessage.warning('请输入手机号')
    return
  }
  
  // 简单的手机号格式验证
  if (!/^1[3-9]\d{9}$/.test(formData.mobile)) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  
  if (countdown.value > 0) return
  
  // 启动倒计时
  countdown.value = 60
  
  // 显示倒计时
  timer.value = setInterval(() => {
    countdown.value--
    
    if (countdown.value <= 0) {
      clearInterval(timer.value)
    }
  }, 1000)
  
  // 假装发送验证码
  ElMessage.success(`验证码已发送到手机号：${formData.mobile}`)
}

// 处理手机登录
const handleMobileLogin = () => {
  if (!formData.mobile || !formData.code) {
    ElMessage.warning('请填写完整登录信息')
    return
  }
  
  // 简单验证
  if (!/^1[3-9]\d{9}$/.test(formData.mobile)) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  
  // 简单验证码校验（演示用）
  if (formData.code.length !== 6 || !/^\d+$/.test(formData.code)) {
    ElMessage.error('验证码格式错误')
    return
  }
  
  // 成功登录
  ElMessage.success('登录成功！')
  ElMessage.info('目前仅有登录页面可用，请稍后尝试')
  
  // 不再跳转
  // router.push('/student-dashboard')
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<style scoped>
.header-bg {
  background-color: #2680EB;
}

:deep(.el-button--primary) {
  background-color: #2680EB;
  border-color: #2680EB;
}

:deep(.el-button--primary:hover), :deep(.el-button--primary:focus) {
  background-color: #1a6bca;
  border-color: #1a6bca;
}
</style> 