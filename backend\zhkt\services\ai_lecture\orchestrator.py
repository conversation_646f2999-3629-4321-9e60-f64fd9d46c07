# -*- coding: utf-8 -*-
"""
AI讲课业务编排器
负责协调各个服务组件，完成完整的AI讲课业务流程
"""

import concurrent.futures
import json
import logging
import os
import re
import subprocess
import tempfile
import traceback
from typing import Any, Dict, List

from tenacity import retry, stop_after_attempt, wait_fixed

from ...entitys.ai_lecture import (AILectureChapter, AILectureDocument,
                                    AILectureHtmlContent, AILectureKeyPoint,
                                    AILectureSpeechContent, AILectureSubtitle)
from ...prompt.ai_lecture_prompts import AILecturePromptManager
from ...utils.deepseek_api import DeepSeekAPI
from ...utils.temp_file_utils import clean_temp_file, clean_temp_dir
from .content_creator import ContentCreator
from .document_processor import DocumentProcessor
from .exceptions import *
from .file_manager import FileManager
from .outline_generator import OutlineGenerator
from .speech_synthesizer import SpeechSynthesizer

logger = logging.getLogger(__name__)

# AI模型配置
AI_MODEL = "deepseek-chat"


class AILectureOrchestrator:
    """AI讲课业务编排器 - 协调各个服务组件完成完整的业务流程"""
    
    def __init__(self):
        self.document_processor = DocumentProcessor()
        self.outline_generator = OutlineGenerator()
        self.content_creator = ContentCreator()
        self.speech_synthesizer = SpeechSynthesizer()
        self.file_manager = FileManager()
        self.prompt_manager = AILecturePromptManager()
        self.deepseek_api = DeepSeekAPI()
        self.logger = logger

    def process_document_outline_generation(self, document_id: int) -> Dict[str, Any]:
        """
        处理文档大纲生成的完整流程
        
        Args:
            document_id: AI讲课文档主键
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        pdf_file = None
        
        try:
            # 获取文档对象
            try:
                document = AILectureDocument.objects.get(id=document_id)
            except AILectureDocument.DoesNotExist:
                error_msg = f"文档ID {document_id} 不存在"
                self.logger.error(error_msg)
                return {'status': 'fail', 'error': error_msg}

            # 验证文档文件路径
            if not document.file_path:
                error_msg = f"文档文件路径为空: {document.file_path}"
                self.logger.error(error_msg)
                return {'status': 'fail', 'error': error_msg}

            # 获取或创建文档内容列表
            try:
                document_content_list = self.document_processor.get_or_create_document_content(document)
                
                # 如果没有封面图，尝试提取
                if not document.cover_image_path:
                    self.logger.info(f"文档 {document.id} 没有封面图，尝试提取...")
                    try:
                        from ...tasks.ai_lecture_tasks import DocumentProcessor as TaskProcessor
                        task_processor = TaskProcessor(self.logger)
                        pdf_file = task_processor.process_document_to_pdf(document.file_path)
                        cover_path = self.document_processor.extract_pdf_cover_image(pdf_file)
                        if cover_path:
                            document.cover_image_path = cover_path
                            document.save()
                            self.logger.info(f"已将封面图片路径 {cover_path} 保存到文档 {document.id}")
                    except Exception as e:
                        self.logger.error(f"提取文档 {document.id} 封面图片时出错: {e}")
                    finally:
                        # 清理临时PDF文件
                        if pdf_file and os.path.exists(pdf_file):
                            try:
                                clean_temp_file(pdf_file)
                                self.logger.info(f"已清理临时PDF文件: {pdf_file}")
                            except Exception as cleanup_error:
                                self.logger.warning(f"清理临时PDF文件失败: {cleanup_error}")
                        
            except (DocumentProcessingException, ValueError) as e:
                return {'status': 'fail', 'error': str(e)}

            # 使用大纲生成服务生成章节大纲
            outline_result = self.outline_generator.generate_document_outline_with_ai(document, document_content_list)
            
            if outline_result.get('status') == 'fail':
                return outline_result

            # 保存章节信息到数据库
            existing_chapters = AILectureChapter.objects.filter(document=document)
            self.file_manager.delete_chapter_related_files(existing_chapters)
            AILectureChapter.objects.filter(document=document).delete()

            chapter_outline = outline_result.get('chapters', [])
            chapter_objects = []
            for idx, chapter_info in enumerate(chapter_outline):
                chapter_obj = AILectureChapter.objects.create(
                    document=document,
                    chapter_title=chapter_info.get("chapter", "章节"),
                    start_page=chapter_info.get("start", 0),
                    end_page=chapter_info.get("stop", 0),
                    chapter_order=idx + 1,
                    status='not_gen',
                    language_code=document.language_code  # 添加此行，从文档继承语言设置
                )
                chapter_objects.append(chapter_obj)

            return {'status': 'success', 'message': f'文档 {document.title} 处理完成'}
            
        except Exception as e:
            self.logger.error(f"AI讲课文档处理失败(ID: {document_id}): {str(e)}")
            return {'status': 'fail', 'error': str(e)}

    def process_chapter_content_generation(self, chapter_id: int) -> Dict[str, Any]:
        """
        处理单个章节内容生成的完整流程（要点、HTML、语音）
        
        Args:
            chapter_id: AI讲课章节主键
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 获取章节对象
            try:
                chapter = AILectureChapter.objects.get(id=chapter_id)
            except AILectureChapter.DoesNotExist:
                error_msg = f"章节ID {chapter_id} 不存在"
                self.logger.error(error_msg)
                return {'status': 'fail', 'error': error_msg}

            # 更新章节状态为"生成中"
            chapter.status = 'gen'
            chapter.save()

            document = chapter.document

            # 获取或创建文档内容列表
            try:
                document_content_list = self.document_processor.get_or_create_document_content(document)
            except (DocumentProcessingException, ValueError) as e:
                chapter.status = 'not_gen'
                chapter.save()
                return {'status': 'fail', 'error': str(e)}

            # 处理该章节内容
            try:
                speech_style_code = document.speech_style
                self._process_single_chapter_content(chapter, document_content_list, speech_style_code)
                
                # 更新章节状态为"已完成"
                chapter.status = 'completed'
                chapter.save()
                return {'status': 'success', 'message': f'章节 {chapter.chapter_title} 处理完成'}
                
            except Exception as e:
                # 处理失败，更新状态为"未生成"
                chapter.status = 'not_gen'
                chapter.save()
                raise e
                
        except Exception as e:
            self.logger.error(f"AI讲课章节处理失败(ID: {chapter_id}): {str(e)}")
            return {'status': 'fail', 'error': str(e)}

    def process_document_with_first_chapter(self, document_id: int) -> Dict[str, Any]:
        """
        生成大纲并处理第一章内容的完整流程
        
        Args:
            document_id: AI讲课文档主键
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 第一步：生成文档大纲
            outline_result = self.process_document_outline_generation(document_id)
            if outline_result.get('status') == 'fail':
                return outline_result

            # 查找第一章并处理
            try:
                document = AILectureDocument.objects.get(id=document_id)
                first_chapter = AILectureChapter.objects.filter(
                    document=document
                ).order_by('chapter_order').first()

                if not first_chapter:
                    return {'status': 'fail', 'error': '未找到章节'}

                # 检查章节状态，避免重复处理
                if first_chapter.status == 'completed':
                    return {
                        'status': 'success',
                        'message': f'文档大纲生成完成，第一章 {first_chapter.chapter_title} 已处理完成',
                        'chapter_id': first_chapter.id,
                        'already_completed': True
                    }

                # 第二步：处理第一章内容
                chapter_result = self.process_chapter_content_generation(first_chapter.id)
                if chapter_result.get('status') == 'fail':
                    return chapter_result

                return {
                    'status': 'success',
                    'message': f'文档大纲生成完成，第一章 {first_chapter.chapter_title} 处理完成',
                    'chapter_id': first_chapter.id
                }

            except AILectureDocument.DoesNotExist:
                return {'status': 'fail', 'error': f'文档 ID {document_id} 不存在'}

        except Exception as e:
            self.logger.error(f"处理文档及第一章失败(ID: {document_id}): {str(e)}")
            return {'status': 'fail', 'error': str(e)}

    def regenerate_chapter_audio_with_style(self, chapter_id: int, style_code: str) -> Dict[str, Any]:
        """
        使用指定风格重新生成章节的音频内容
        
        Args:
            chapter_id: 章节ID
            style_code: 语音风格代码
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 获取章节对象
            try:
                chapter = AILectureChapter.objects.get(id=chapter_id)
            except AILectureChapter.DoesNotExist:
                error_msg = f"章节ID {chapter_id} 不存在"
                self.logger.error(error_msg)
                return {'status': 'fail', 'error': error_msg}

            self.logger.info(f"开始使用风格 {style_code} 重新生成章节 {chapter.chapter_title} 的音频内容")

            # 更新章节状态为生成中
            chapter.status = 'gen'
            chapter.save()

            # 获取章节下的所有要点
            key_points = AILectureKeyPoint.objects.filter(
                chapter=chapter,
                deleted_at__isnull=True
            ).order_by('point_order')

            if not key_points.exists():
                chapter.status = 'not_gen'
                chapter.save()
                return {'status': 'fail', 'error': '章节下没有找到要点数据'}

            # 获取章节文本内容
            document = chapter.document
            document_content_list = self.document_processor.get_or_create_document_content(document)
            chapter_text = self._extract_text_by_page_range(
                document_content_list, chapter.start_page, chapter.end_page
            )

            # 删除原有的音频内容
            self._delete_chapter_audio_contents_safely(key_points)

            # 为每个要点重新生成音频内容
            for key_point in key_points:
                try:
                    self.logger.info(f"开始重新生成要点 {key_point.point_order} 的音频内容")
                    
                    # 获取该要点对应的HTML内容
                    html_code = self._get_key_point_html_content(key_point)
                    
                    if html_code:
                        # 根据HTML内容生成音频
                        self._generate_key_point_audio_with_html_content(
                            key_point, chapter.chapter_title, chapter_text, style_code, html_code
                        )
                    else:
                        # 如果没有HTML内容，使用原始方法生成（向后兼容）
                        self.logger.warning(f"要点 {key_point.point_order} 没有找到HTML内容，使用原始方法生成音频")
                        self._generate_key_point_audio_with_standard_method(
                            key_point, chapter.chapter_title, chapter_text, style_code
                        )
                    
                    self.logger.info(f"要点 {key_point.point_order} 音频重新生成完成")
                except Exception as e:
                    self.logger.error(f"处理要点 {key_point.point_order} 时出错: {e}")
                    key_point.audio_status = 'not_gen'
                    key_point.save()
                    continue

            # 更新章节状态为已完成
            chapter.status = 'completed'
            chapter.save()

            return {
                'status': 'success',
                'message': f'章节 {chapter.chapter_title} 使用 {style_code} 风格的音频重新生成完成'
            }

        except Exception as e:
            self.logger.error(f"重新生成章节 {chapter_id} 音频失败: {str(e)}")
            try:
                chapter = AILectureChapter.objects.get(id=chapter_id)
                chapter.status = 'completed'
                chapter.save()
            except:
                pass
            return {'status': 'fail', 'error': str(e)}

    def process_chapter_with_gemini_analysis(self, chapter: 'AILectureChapter', 
                                    chapter_text: str, audio_file_path: str, script_text: str):
        """
        使用Gemini分析章节内容，生成HTML页面和音频
        
        Args:
            chapter: 章节对象
            chapter_text: 章节文本内容
            audio_file_path: 音频文件路径
            script_text: 脚本文本
            
        Returns:
            Dict: 处理结果
        """
        try:
            # 获取章节的语言代码
            language_code = getattr(chapter, 'language_code', None)
            
            # 如果章节没有语言代码，尝试从文档获取
            if not language_code:
                language_code = getattr(chapter.document, 'language_code', 'zh')
            
            # 验证语言代码是否有效
            if language_code not in ['zh', 'en', 'vi', 'id']:
                language_code = 'zh'  # 默认使用中文
                
            self.logger.info(f"开始使用Gemini分析章节 {chapter.chapter_title} (语言: {language_code})")
            
            # 1. 使用SubtitleAnalyzer分析章节内容
            from .subtitle_analyzer import SubtitleAnalyzer
            subtitle_analyzer = SubtitleAnalyzer()
            analysis_result = subtitle_analyzer.analyze_chapter_content(
                audio_file_path, 
                script_text,
                chapter_text,
                language=language_code  # 传递语言参数
            )
            
            # 2. 获取字幕数据
            all_subtitles = analysis_result.get('subtitle_data', [])
            
            # 3. 解析页面分割建议
            page_suggestions_json = analysis_result.get('page_suggestions', '')
            
            # 4. 生成HTML页面
            html_pages = subtitle_analyzer.generate_html_pages_for_chapter(
                chapter.chapter_title, 
                chapter_text, 
                page_suggestions_json,
                language=language_code  # 传递语言参数
            )
            
            saved_pages = []
            
            # 清除章节现有的要点和语音内容
            AILectureKeyPoint.objects.filter(chapter=chapter).delete()
            
            for page_data in html_pages:
                try:
                    # 创建要点对象
                    key_point = AILectureKeyPoint.objects.create(
                        chapter=chapter,
                        original_point=f"页面 {page_data['page_number']}: {page_data['page_title']}",
                        enhanced_point=', '.join(page_data['key_points']),
                        point_order=page_data['page_number'],
                        audio_status='not_gen',
                        language_code=language_code  # 设置语言代码
                    )
                    
                    # 转换时间戳为秒，并筛选字幕
                    start_sec = self._timestamp_to_seconds(page_data.get('start_timestamp'))
                    end_sec = self._timestamp_to_seconds(page_data.get('end_timestamp'))
                    
                    page_subtitles = [
                        sub for sub in all_subtitles 
                        if start_sec <= sub.get('start_time_seconds', 0) < end_sec
                    ]
                    
                    # 更新page_data中的时间戳为秒和字幕数据，以便后续处理
                    page_data['start_timestamp'] = start_sec
                    page_data['end_timestamp'] = end_sec
                    page_data['subtitles'] = page_subtitles
                    
                    # 保存HTML文件
                    html_storage_path = self.file_manager.save_html_slide_file(
                        page_data['html_content'], 
                        f"{chapter.chapter_title}_{page_data['page_title']}", 
                        page_data['page_number']
                    )
                    
                    # 创建HTML内容对象
                    html_content = AILectureHtmlContent.objects.create(
                        key_point=key_point,
                        html_file_path=html_storage_path,
                        language_code=language_code  # 设置语言代码
                    )

                    # 音频分割与保存
                    self.speech_synthesizer.create_speech_from_split(
                        key_point=key_point,
                        page_data=page_data,
                        source_audio_path=audio_file_path,
                        chapter=chapter,
                        subtitle_data=page_subtitles,
                        language=language_code  # 传递语言参数
                    )

                    saved_pages.append({
                        'id': html_content.id,
                        'key_point_id': key_point.id,
                        'title': page_data['page_title'],
                        'page_number': page_data['page_number'],
                        'html_path': html_content.html_file_path,
                        'start_timestamp': start_sec,
                        'end_timestamp': end_sec,
                        'language': language_code  # 添加语言信息
                    })
                    
                    self.logger.info(f"已保存章节 {chapter.chapter_title} 的第 {page_data['page_number']} 页HTML内容")
                except Exception as e:
                    self.logger.error(f"保存章节 {chapter.chapter_title} 的第 {page_data['page_number']} 页HTML失败: {str(e)}")
            
            # 5. 更新章节状态
            chapter.status = 'completed'
            chapter.save()
            
            result = {
                'status': 'success',
                'message': f'章节 {chapter.chapter_title} 的HTML页面生成完成',
                'total_pages': len(html_pages),
                'saved_pages': saved_pages,
                'subtitle_count': analysis_result.get('total_subtitles', 0),
                'total_duration': analysis_result.get('total_duration', 0),
                'language': language_code  # 添加语言信息
            }
            
            self.logger.info(f"章节 {chapter.chapter_title} 的Gemini分析结果处理完成 (语言: {language_code})")
            return result
            
        except Exception as e:
            traceback.print_exc()
            self.logger.error(f"处理章节 {chapter.chapter_title} 的Gemini分析结果失败: {str(e)}")
            return {'status': 'error', 'message': f'处理失败: {str(e)}'}

    def _timestamp_to_seconds(self, timestamp_str: str) -> float:
        """
        将 H:MM:SS.cs 格式的时间戳字符串转换为秒数
        """
        try:
            if not timestamp_str or not isinstance(timestamp_str, str):
                return 0.0
                
            parts = timestamp_str.split(':')
            if len(parts) == 3:
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds_parts = parts[2].split('.')
                seconds = int(seconds_parts[0])
                centiseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
                
                total_seconds = float(hours * 3600 + minutes * 60 + seconds + centiseconds / 100)
                return total_seconds
            else:
                self.logger.warning(f"无法解析时间戳格式: {timestamp_str}")
                return 0.0
        except Exception as e:
            self.logger.warning(f"解析时间戳 '{timestamp_str}' 失败: {e}")
            return 0.0

    def _process_single_chapter_content(self, chapter: AILectureChapter,
                                      document_content_list: List, speech_style_code: str):
        """处理单个章节的内容生成"""
        # 清空原有要点和相关文件
        self.file_manager.delete_chapter_related_files([chapter])
        AILectureKeyPoint.objects.filter(chapter=chapter).delete()

        # 获取章节文本内容
        chapter_text = self._extract_text_by_page_range(
            document_content_list, chapter.start_page, chapter.end_page
        )

        # 提取并增强要点
        try:
            points_and_enhanced = self.content_creator.extract_and_enhance_points_batch(chapter_text)

            for index, (original_point, enhanced_point) in enumerate(points_and_enhanced):
                try:
                    # 保存要点对象
                    key_point_obj = self._save_key_point_to_database(
                        chapter, original_point, enhanced_point, index
                    )

                    # 串行处理：先生成HTML，再根据HTML生成音频
                    try:
                        # 第一步：生成HTML内容
                        html_code = self._generate_and_save_html_content(
                            key_point_obj, enhanced_point, chapter.chapter_title, index
                        )
                        
                        # 第二步：根据HTML内容生成音频
                        self._generate_key_point_audio_with_html_content(
                            key_point_obj, chapter.chapter_title, chapter_text, 
                            speech_style_code, html_code
                        )
                        
                        self.logger.info(f"章节 {chapter.chapter_title} 要点{index + 1}处理完成")
                        
                    except Exception as e:
                        self.logger.error(
                            f"处理章节 {chapter.chapter_title} 要点{index + 1}时发生异常: {e}"
                        )
                except Exception as e:
                    self.logger.error(f"处理章节 {chapter.chapter_title} 要点{index + 1}时出错: {e}")
                    
        except Exception as e:
            self.logger.error(f"处理章节 {chapter.chapter_title} 时出错: {e}")
            raise e

    def _save_key_point_to_database(self, chapter: AILectureChapter, original_point: str, 
                                   enhanced_point: str, index: int) -> AILectureKeyPoint:
        """保存要点对象到数据库"""
        return AILectureKeyPoint.objects.create(
            chapter=chapter,
            original_point=original_point,
            enhanced_point=enhanced_point,
            point_order=index + 1,
            audio_status='not_gen',
            language_code=chapter.language_code  # 添加此行，继承章节的语言设置
        )

    def _generate_and_save_html_content(self, key_point: AILectureKeyPoint, 
                                      enhanced_point: str, chapter_title: str, index: int) -> str:
        """生成并保存HTML内容，返回HTML代码"""
        try:
            # 生成HTML内容
            html_code = self.content_creator.generate_html_slide_content(enhanced_point, chapter_title)
            
            # 保存HTML文件
            html_storage_path = self.file_manager.save_html_slide_file(
                html_code, chapter_title, index
            )
            
            # 保存HTML内容对象
            AILectureHtmlContent.objects.create(
                key_point=key_point,
                html_file_path=html_storage_path,
                language_code=key_point.language_code  # 添加此行，继承要点的语言设置
            )
            
            self.logger.info(f"HTML内容生成完成: {chapter_title} 要点 {index}")
            return html_code
        except Exception as e:
            self.logger.error(f"生成和保存章节 {chapter_title} 要点 {index + 1} 的HTML失败: {e}")
            raise e

    def _generate_key_point_audio_with_html_content(self, key_point: AILectureKeyPoint, 
                                                  chapter_title: str, chapter_text: str, 
                                                  style_code: str, html_code: str):
        """根据HTML内容生成要点的音频内容"""
        # 更新要点音频状态为生成中
        key_point.audio_status = 'gen'
        key_point.save()
        
        try:
            # 获取前面的语音内容上下文
            previous_context = self.speech_synthesizer.get_previous_speech_context(
                key_point, context_count=2
            )
            
            # 生成语音脚本（基于HTML内容）
            speech_script = self.speech_synthesizer.generate_speech_script_with_html(
                key_point.enhanced_point, 
                chapter_title, 
                chapter_text, 
                html_code,
                style_code, 
                previous_context
            )
            
            sentences = self.speech_synthesizer.split_text_into_sentences(speech_script)
            self.logger.info(f"生成语音内容: {chapter_title} 要点 {key_point.point_order} 的句子数: {len(sentences)}")
            
            # 有效句子计数器
            valid_sentence_order = 0
            
            for i, sentence in enumerate(sentences):
                try:
                    # 检查句子是否只包含标点符号
                    if self.speech_synthesizer.is_only_punctuation_and_whitespace(sentence):
                        self.logger.info(f"句子 {i + 1} 只包含标点符号，跳过音频生成: {sentence}")
                        continue
                    
                    valid_sentence_order += 1
                    
                    # 生成音频
                    audio_content = self._synthesize_audio_with_retry_mechanism(sentence)
                    
                    # 保存音频文件
                    audio_storage_path = self.file_manager.save_audio_speech_file(
                        audio_content, chapter_title, key_point.point_order - 1, valid_sentence_order
                    )
                    
                    # 保存语音内容记录
                    AILectureSpeechContent.objects.create(
                        key_point=key_point,
                        sentence_order=valid_sentence_order,
                        speech_script=sentence,
                        audio_file_path=audio_storage_path,
                        language_code=key_point.language_code  # 添加此行，继承要点的语言设置
                    )
                    
                    self.logger.info(f"有效句子 {valid_sentence_order} 音频生成完成: {sentence[:30]}...")
                    
                except Exception as e:
                    self.logger.error(f"生成句子 {valid_sentence_order} 的语音失败: {e}")
                    key_point.audio_status = 'not_gen'
                    key_point.save()
                    raise e
            
            # 所有音频生成成功
            key_point.audio_status = 'completed'
            key_point.save()
            self.logger.info(f"要点 {key_point.point_order} 音频生成完成")
            
        except Exception as e:
            key_point.audio_status = 'not_gen'
            key_point.save()
            self.logger.error(f"生成要点 {key_point.point_order} 的音频失败: {e}")
            raise e

    def _generate_key_point_audio_with_standard_method(self, key_point: AILectureKeyPoint, 
                                                     chapter_title: str, chapter_text: str, 
                                                     style_code: str):
        """生成要点的音频内容（标准方法，用于兼容）"""
        # 更新要点音频状态为生成中
        key_point.audio_status = 'gen'
        key_point.save()
        
        try:
            # 获取前面的语音内容上下文
            previous_context = self.speech_synthesizer.get_previous_speech_context(
                key_point, context_count=2
            )
            
            # 生成语音脚本（使用标准方法）
            speech_script = self.speech_synthesizer.generate_speech_script_standard(
                key_point.enhanced_point, 
                chapter_title, 
                chapter_text, 
                style_code, 
                previous_context
            )
            
            sentences = self.speech_synthesizer.split_text_into_sentences(speech_script)
            self.logger.info(f"生成语音内容: {chapter_title} 要点 {key_point.point_order} 的句子数: {len(sentences)}")
            
            # 有效句子计数器
            valid_sentence_order = 0
            
            for i, sentence in enumerate(sentences):
                try:
                    # 检查句子是否只包含标点符号
                    if self.speech_synthesizer.is_only_punctuation_and_whitespace(sentence):
                        self.logger.info(f"句子 {i + 1} 只包含标点符号，跳过音频生成: {sentence}")
                        continue
                    
                    valid_sentence_order += 1
                    
                    # 生成音频
                    audio_content = self._synthesize_audio_with_retry_mechanism(sentence)
                    
                    # 保存音频文件
                    audio_storage_path = self.file_manager.save_audio_speech_file(
                        audio_content, chapter_title, key_point.point_order - 1, valid_sentence_order
                    )
                    
                    # 保存语音内容记录
                    AILectureSpeechContent.objects.create(
                        key_point=key_point,
                        sentence_order=valid_sentence_order,
                        speech_script=sentence,
                        audio_file_path=audio_storage_path,
                        language_code=key_point.language_code  # 添加此行，继承要点的语言设置
                    )
                    
                    self.logger.info(f"有效句子 {valid_sentence_order} 音频生成完成: {sentence[:30]}...")
                    
                except Exception as e:
                    self.logger.error(f"生成句子 {valid_sentence_order} 的语音失败: {e}")
                    key_point.audio_status = 'not_gen'
                    key_point.save()
                    raise e
            
            # 所有音频生成成功
            key_point.audio_status = 'completed'
            key_point.save()
            self.logger.info(f"要点 {key_point.point_order} 音频生成完成")
            
        except Exception as e:
            key_point.audio_status = 'not_gen'
            key_point.save()
            self.logger.error(f"生成要点 {key_point.point_order} 的音频失败: {e}")
            raise e

    def _synthesize_audio_with_retry_mechanism(self, sentence: str) -> bytes:
        """带重试机制的TTS合成"""
        @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
        def _synthesize():
            return self.speech_synthesizer.synthesize_audio_content(
                text=sentence,
                voice_type="BV001_streaming",
                encoding="mp3",
                speed_ratio=1.0,
                loudness_ratio=1.0
            )
        
        return _synthesize()

    def _get_key_point_html_content(self, key_point: AILectureKeyPoint) -> str:
        """获取要点对应的HTML内容"""
        try:
            html_content_obj = AILectureHtmlContent.objects.filter(
                key_point=key_point,
                deleted_at__isnull=True
            ).first()
            
            if not html_content_obj or not html_content_obj.html_file_path:
                return None
            
            # 下载并读取HTML文件内容
            local_path = self.document_processor.download_document_to_local(
                html_content_obj.html_file_path
            )
            with open(local_path, 'r', encoding='utf-8') as f:
                html_code = f.read()
            
            return html_code
            
        except Exception as e:
            self.logger.error(f"获取要点 {key_point.point_order} 的HTML内容失败: {e}")
            return None

    def _extract_text_by_page_range(self, document_content_list: List, 
                                  start_page: int, end_page: int) -> str:
        """根据页码范围提取文本内容"""
        return "\n".join(
            item["text"] for item in document_content_list
            if (item.get("type") == "text" and 
                item.get("text") is not None and
                item.get("page_idx") is not None and
                start_page <= item["page_idx"] + 1 <= end_page)
        )

    def _delete_chapter_audio_contents_safely(self, key_points):
        """安全删除章节的音频内容"""
        try:

            # 重置所有要点的音频状态为未生成
            updated_count = key_points.update(audio_status='not_gen')
            self.logger.info(f"已重置 {updated_count} 个要点的音频状态为未生成")

            speech_contents = AILectureSpeechContent.objects.filter(
                key_point__in=key_points,
                deleted_at__isnull=True
            )

            # 收集需要删除的音频文件路径
            audio_files_to_delete = []
            for speech_content in speech_contents:
                if speech_content.audio_file_path:
                    audio_files_to_delete.append(speech_content.audio_file_path)

            self.logger.info(f"开始删除 {len(audio_files_to_delete)} 个音频文件")

            # 删除文件
            deleted_files_count = 0
            failed_files_count = 0
            
            for file_path in audio_files_to_delete:
                try:
                    from ...utils.file_utils import FileUtils
                    if FileUtils.delete_file(file_path):
                        deleted_files_count += 1
                    else:
                        failed_files_count += 1
                except Exception as e:
                    failed_files_count += 1
                    self.logger.error(f"删除音频文件异常: {file_path}, 错误: {str(e)}")

            self.logger.info(f"音频文件删除完成: 成功 {deleted_files_count} 个，失败 {failed_files_count} 个")

            # 删除数据库记录
            deleted_count = speech_contents.count()
            speech_contents.delete()
            self.logger.info(f"已删除 {deleted_count} 条音频内容数据库记录")
            

        except Exception as e:
            self.logger.error(f"删除章节音频内容时发生异常: {str(e)}")
            raise e 