from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework_simplejwt.tokens import RefreshToken
from django.db.models import Q, Count, Sum, F
from django.utils import timezone
from datetime import timedelta
from .base_model_view import BaseModelViewSet
from zhkt.config import DEFAULT_LANGUAGE
from zhkt.serializers import (
    AdminSerializer,
    UserSerializer,
    StudentSerializer,
    TeacherSerializer,
    CourseSerializer,
    ClassGroupSerializer,
)
from zhkt.entitys import (
    Admin, 
    User,
    Student,
    Teacher,
    Course,
    ClassGroup,
    Submission,
    Homework,
    PointsRecord,
    Note
)

# 用户相关视图集
class AdminViewSet(BaseModelViewSet):
    queryset = Admin.objects.all()
    serializer_class = AdminSerializer
    permission_classes = [permissions.IsAuthenticated]

class UserViewSet(BaseModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search', '')
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(alias__icontains=search)
            )
        return queryset.filter(deleted_at__isnull=True)

    def get_permissions(self):
        if self.action in ['create', 'login']:
            return [permissions.AllowAny()]
        return super().get_permissions()
    
    @action(detail=False, methods=['get'])
    def info(self, request):
        """获取当前登录用户信息"""
        try:
            user = request.user
            # 确保用户存在且未被删除
            if not user or not user.is_authenticated:
                return Response({'error': '用户未登录'}, status=status.HTTP_401_UNAUTHORIZED)
            
            # 获取用户详细信息
            user_data = self.get_serializer(user).data
            
            # 获取用户角色信息
            if hasattr(user, 'student_profile'):
                role_type = 'student'
                role_info = StudentSerializer(user.student_profile).data
            elif hasattr(user, 'teacher_profile'):
                role_type = 'teacher'
                role_info = TeacherSerializer(user.teacher_profile).data
            elif hasattr(user, 'admin_profile'):
                role_type = 'admin'
                role_info = AdminSerializer(user.admin_profile).data
            else:
                role_type = 'user'
                role_info = {}
            
            # 构建响应数据
            response_data = {
                'user': user_data,
                'role_type': role_type,
                'role_info': role_info
            }
            
            return Response(response_data)
            
        except Exception as e:
            return Response(
                {'error': f'获取用户信息失败: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def create(self, request, *args, **kwargs):
        # 检查邮箱是否已存在
        email = request.data.get('email')
        if email and User.objects.filter(email=email).exists():
            return Response(
                {'email': ['该邮箱已被注册']},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        
        # 处理用户角色关系
        roles = request.data.get('roles', [])
        if roles:
            user.roles.set(roles)
        
        # 返回用户信息和token
        refresh = RefreshToken.for_user(user)
        response_data = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': UserSerializer(user).data
        }
        
        headers = self.get_success_headers(serializer.data)
        return Response(response_data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        
        # 创建请求数据的副本，移除密码字段（如果存在）
        data = request.data.copy()
        if 'password' not in data or not data['password']:
            data.pop('password', None)
            
        serializer = self.get_serializer(instance, data=data, partial=partial)
        serializer.is_valid(raise_exception=True)
        user = self.perform_update(serializer)

        # 处理用户角色关系
        roles = request.data.get('roles', None)
        if roles is not None:  # 只有当明确提供了roles时才更新
            user.roles.set(roles)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def perform_update(self, serializer):
        return serializer.save()

    @action(detail=False, methods=['post'])
    def login(self, request):
        username = request.data.get('username')
        password = request.data.get('password')
        
        if not username or not password:
            return Response({'error': '请提供用户名和密码'}, status=status.HTTP_400_BAD_REQUEST)
        
        user = User.objects.filter(username=username).first()
        
        if not user or not user.check_password(password):
            return Response({'error': '用户名或密码错误'}, status=status.HTTP_401_UNAUTHORIZED)
        
        refresh = RefreshToken.for_user(user)
        
        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': UserSerializer(user).data
        })

    @action(detail=False, methods=['get'])
    def search(self, request):
        """通过用户名或手机号搜索用户"""
        query = request.query_params.get('query', '')
        if not query:
            return Response({'error': '请提供搜索关键词'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 查找用户
        users = User.objects.filter(
            Q(username__icontains=query) | Q(phone__icontains=query),
            deleted_at__isnull=True
        )
        
        serializer = self.get_serializer(users, many=True)
        return Response(serializer.data)

class CoursesPagination(PageNumberPagination):
    page_size = 8
    page_size_query_param = 'page_size'
    max_page_size = 100

class StudentViewSet(BaseModelViewSet):
    queryset = Student.objects.all()
    serializer_class = StudentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(deleted_at__isnull=True)

    @action(detail=False, methods=['get'])
    def current(self, request):
        """获取当前登录学生信息"""
        try:
            student = Student.objects.get(user=request.user, deleted_at__isnull=True)
            serializer = self.get_serializer(student)
            return Response(serializer.data)
        except Student.DoesNotExist:
            return Response({'error': '当前用户不是学生'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def courses(self, request, pk=None):
        """获取学生所在班级的所有课程，支持分页、课程类型过滤和关键词搜索"""
        student = self.get_object()

        # 获取过滤参数
        course_type = request.query_params.get('course_type', '0')
        keyword = request.query_params.get('keyword', '').strip()

        # 获取语言参数
        language = request.query_params.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language

        # 获取学生所在的所有班级
        classes = student.classes.filter(deleted_at__isnull=True)

        # 获取这些班级关联的所有课程
        courses = Course.objects.filter(
            class_groups__in=classes,
            deleted_at__isnull=True
        ).distinct()

        # 按语言过滤
        courses = courses.filter(language_code=language)
        
        # 应用课程类型过滤
        if course_type == '1':  # 必修课
            courses = courses.filter(course_type='REQUIRED')
        elif course_type == '2':  # 选修课
            courses = courses.filter(course_type='ELECTIVE')
            
        # 应用关键词搜索
        if keyword:
            courses = courses.filter(name__icontains=keyword)
        
        # 构建课程数据，包含学习进度
        courses_data = []
        for course in courses:
            course_data = CourseSerializer(course).data
            # Todo 获取学生对该课程的学习进度
            course_data.update({
                'progress': 50,
                'status': 'IN_PROGRESS',
                'last_accessed': '2025-05-30'
            })
            courses_data.append(course_data)
        
        # 使用分页器
        paginator = CoursesPagination()
        page = paginator.paginate_queryset(courses_data, request)
        
        if page is not None:
            return paginator.get_paginated_response(page)
            
        return Response(courses_data)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """通过用户名或手机号搜索学生"""
        query = request.query_params.get('query', '')
        if not query:
            return Response({'error': '请提供搜索关键词'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 查找用户
        students = Student.objects.filter(
            Q(user__username__icontains=query) | Q(user__phone__icontains=query),
            deleted_at__isnull=True
        ).select_related('user')
        
        serializer = self.get_serializer(students, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def leave_classes(self, request, pk=None):
        """学生退出班级"""
        student = self.get_object()
        class_ids = request.data.get('class_ids', [])
        
        if not class_ids:
            return Response({'error': '请提供班级ID列表'}, status=status.HTTP_400_BAD_REQUEST)
        
        classes = ClassGroup.objects.filter(id__in=class_ids)
        student.classes.remove(*classes)
        
        return Response({'message': '成功退出班级'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['get'])
    def classes(self, request, pk=None):
        """获取学生所在的所有班级"""
        student = self.get_object()
        classes = student.classes.filter(deleted_at__isnull=True)
        serializer = ClassGroupSerializer(classes, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def learning_stats(self, request):
        """获取学生学习统计数据"""
        try:
            student = Student.objects.get(user=request.user, deleted_at__isnull=True)
            now = timezone.now()
            
            # 获取学生所在的所有班级
            classes = student.classes.filter(deleted_at__isnull=True)

            # 获取这些班级关联的所有课程
            courses = Course.objects.filter(
                class_groups__in=classes,
                deleted_at__isnull=True
            ).distinct()

             # 获取学生所有笔记
            notes = Note.objects.filter(
                student=student,
                course__in=courses, 
                deleted_at__isnull=True
            )
            
            # 获取所有作业
            homeworks = Homework.objects.filter(
                course__in=courses,
                deleted_at__isnull=True,
                is_published=True
            ).distinct()
            
            # 获取学生的作业提交记录
            submissions = Submission.objects.filter(
                student=student,
                homework__in=homeworks,
                deleted_at__isnull=True
            )
            
            # 计算课程完成率
            total_courses = courses.count()
            # 获取每个课程的作业完成情况
            completed_courses = 0
            for course in courses:
                course_homeworks = homeworks.filter(course=course)
                if course_homeworks.exists():
                    completed_homeworks = submissions.filter(
                        homework__in=course_homeworks,
                        status__in=['submitted', 'late']
                    ).count()
                    # 如果课程的作业完成率超过80%，认为该课程已完成
                    if completed_homeworks / course_homeworks.count() >= 0.8:
                        completed_courses += 1
                
            course_completion_rate = round((completed_courses / total_courses * 100) if total_courses > 0 else 0, 1)
            
            # 计算上月课程完成率
            last_month = now - timedelta(days=30)
            last_month_completed = 0
            for course in courses:
                course_homeworks = homeworks.filter(
                    course=course,
                    created_at__lte=last_month
                )
                if course_homeworks.exists():
                    completed_homeworks = submissions.filter(
                        homework__in=course_homeworks,
                        status__in=['submitted', 'late'],
                        submitted_at__lte=last_month
                    ).count()
                    if completed_homeworks / course_homeworks.count() >= 0.8:
                        last_month_completed += 1
            
            last_month_rate = round((last_month_completed / total_courses * 100) if total_courses > 0 else 0, 1)
            course_completion_change = round(course_completion_rate - last_month_rate, 1)
            
            # 计算作业完成率
            total_homeworks = homeworks.count()
            completed_homeworks = submissions.filter(status__in=['submitted', 'late']).count()
            assignment_completion_rate = round((completed_homeworks / total_homeworks * 100) if total_homeworks > 0 else 0, 1)
            
            # 计算作业平均分
            graded_submissions = submissions.filter(
                status__in=['submitted', 'late'],
                score__isnull=False
            )
            total_score = graded_submissions.aggregate(total=Sum('score'))['total'] or 0
            average_score = round(total_score / graded_submissions.count() if graded_submissions.count() > 0 else 0, 1)
            
            # 计算本周学习时长
            week_start = now - timedelta(days=now.weekday())
            week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
            weekly_learning_data = [0] * 7  # 存储周一到周日的学习时长
            
            # 这里我们通过作业提交记录来统计学习时长
            # 每次提交作业计2小时学习时长
            study_records = submissions.filter(
                submitted_at__gte=week_start,
                status__in=['submitted', 'late']
            ).values('submitted_at__date').annotate(
                hours=Count('id') * 2  # 每次提交作业计2小时
            )
            
            for record in study_records:
                day_index = record['submitted_at__date'].weekday()
                weekly_learning_data[day_index] = record['hours']
            
            total_learning_hours = sum(weekly_learning_data)
            
            # 获取最近的积分变化
            recent_points_record = PointsRecord.objects.filter(
                student=student,
                deleted_at__isnull=True
            ).order_by('-created_at').first()
            
            recent_points_change = recent_points_record.points if recent_points_record else 0
            
            # 计算勋章等级
            points = student.points
            medal_level = 'bronze'
            if points >= 2000:
                medal_level = 'gold'
            elif points >= 1000:
                medal_level = 'silver'
            
            # 计算距离下一个等级需要的积分
            points_to_next_level = 1000 - (points % 1000)
            
            # 计算连续学习天数
            # 通过查找连续的作业提交记录来计算
            study_dates = submissions.filter(
                status__in=['submitted', 'late']
            ).values_list('submitted_at__date', flat=True).distinct().order_by('-submitted_at__date')
            
            continuous_learning_days = 0
            if study_dates:
                last_date = study_dates[0]
                continuous_learning_days = 1
                
                for date in study_dates[1:]:
                    if (last_date - date).days == 1:
                        continuous_learning_days += 1
                        last_date = date
                    else:
                        break
            
            # 计算本周学习小时数
            weekly_learning_hours = sum(weekly_learning_data)

            # 所有笔记数量
            total_notes = notes.count()
            
            response_data = {
                'courseCompletionRate': course_completion_rate,
                'courseCompletionChange': course_completion_change,
                'totalCourses': total_courses,
                'completedCourses': completed_courses,
                'assignmentCompletionRate': assignment_completion_rate,
                'totalHomeworks': total_homeworks,
                'completedHomeworks': completed_homeworks,
                'averageScore': average_score,
                'totalLearningHours': total_learning_hours,
                'recentHoursChange': weekly_learning_hours - sum(weekly_learning_data[:6]),  # 与前一周比较
                'weeklyLearningData': weekly_learning_data,
                'recentPointsChange': recent_points_change,
                'medalLevel': medal_level,
                'pointsToNextLevel': points_to_next_level,
                'continuousLearningDays': continuous_learning_days,
                'weeklyLearningHours': weekly_learning_hours,
                'totalNotes': total_notes
            }
            
            return Response(response_data)
            
        except Student.DoesNotExist:
            return Response({'error': '当前用户不是学生'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TeacherViewSet(BaseModelViewSet):
    queryset = Teacher.objects.all()
    serializer_class = TeacherSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def current(self, request):
        """获取当前登录教师信息"""
        try:
            teacher = Teacher.objects.get(user=request.user, deleted_at__isnull=True)
            serializer = self.get_serializer(teacher)
            return Response(serializer.data)
        except Teacher.DoesNotExist:
            return Response({'error': '当前用户不是教师'}, status=status.HTTP_404_NOT_FOUND)
            
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取教师统计数据"""
        try:
            teacher = Teacher.objects.get(user=request.user, deleted_at__isnull=True)
            
            # 获取当前时间
            now = timezone.now()
            
            # 计算本周开始时间（周一）
            week_start = now - timezone.timedelta(days=now.weekday())
            week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 计算本月开始时间
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            # 获取课程总数
            total_courses = Course.objects.filter(
                teacher=teacher,
                deleted_at__isnull=True
            ).count()
            
            # 获取本月新增课程数
            new_courses_this_month = Course.objects.filter(
                teacher=teacher,
                created_at__gte=month_start,
                deleted_at__isnull=True
            ).count()
            
            # 获取学生总数（去重）
            total_students = Student.objects.filter(
                classes__courses__teacher=teacher,
                deleted_at__isnull=True
            ).distinct().count()
            
            # 获取本周新增学生数
            new_students_this_week = Student.objects.filter(
                classes__courses__teacher=teacher,
                created_at__gte=week_start,
                deleted_at__isnull=True
            ).distinct().count()
            
            # 获取待批改作业数
            pending_assignments = Submission.objects.filter(
                homework__course__teacher=teacher,
                score__isnull=True,
                deleted_at__isnull=True
            ).count()
            
            return Response({
                'total_courses': total_courses,
                'total_students': total_students,
                'pending_assignments': pending_assignments,
                'new_students_this_week': new_students_this_week,
                'new_courses_this_month': new_courses_this_month
            })
            
        except Teacher.DoesNotExist:
            return Response({'error': '当前用户不是教师'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)