import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { joinClassByCode } from '@/api/class'
import { getCurrentStudentInfo, getStudentCourses, getStudentLearningStats } from '@/api/student'
import defaultStudentAvatar from '@/assets/images/avatars/student1.png'

export const useStudentStore = defineStore('student', () => {
  // State
  const studentData = ref({
    id: null,
    user: {
      alias: '',
      username: ''
    },
    student_id: '',
    avatar: defaultStudentAvatar,
    points: 0,
    continuousLearningDays: 0,
    weeklyLearningHours: 0,
  })
  
  // 课程统计数据
  const courseStats = ref({
    total: 0,
    completed: 0,
    inProgress: 0,
    weeklyHours: 0
  })

  // 课程列表数据
  const courses = ref([])
  
  const learningStats = ref({
    courseCompletionRate: 0,
    assignmentCompletionRate: 0,
    totalLearningHours: 0,
    recentHoursChange: 0,
    weeklyLearningData: [0, 0, 0, 0, 0, 0, 0], // 周一到周日的学习时长
    recentPointsChange: 0,
    medalLevel: 'bronze', // bronze, silver, gold
    pointsToNextLevel: 0
  })
  
  const learningRecommendations = ref([
    {
      category: 'efficiency',
      title: '提高学习效率',
      recommendations: [
        '建议在19:00-21:00时段学习，专注度更高',
        '增加视频学习方式，效率提升12%'
      ]
    },
    {
      category: 'reinforcement',
      title: '知识点巩固',
      recommendations: [
        '针对微分中值定理进行专项练习',
        '不定积分计算薄弱，建议复习基础公式'
      ]
    },
    {
      category: 'habit',
      title: '学习习惯优化',
      recommendations: [
        '建立更稳定的学习连续性，避免间断',
        '增加练习题解答频率，提高应用能力'
      ]
    }
  ])
  
  // Getters
  const userFullName = computed(() => studentData.value?.user?.alias || '')
  const userPoints = computed(() => studentData.value?.points || 0)
  const formattedPoints = computed(() => (studentData.value?.points || 0).toLocaleString())
  
  // Actions
  function updatePoints(amount) {
    studentData.value.points += amount
  }
  
  function updateStudentData(newData) {
    studentData.value = { ...studentData.value, ...newData }
    if(newData.user) {
      studentData.value.avatar = newData.user.avatar || defaultStudentAvatar
    }
  }
  
  function updateLearningStats(newStats) {
    learningStats.value = { ...learningStats.value, ...newStats }
  }

  // 课程相关操作
  const getCourseById = (id) => {
    return courses.value.find(course => course.id === id)
  }

  function updateCourseStats() {
    const completed = courses.value.filter(course => course.progress === 100).length
    const inProgress = courses.value.length - completed
    
    courseStats.value = {
      ...courseStats.value,
      total: courses.value.length,
      completed,
      inProgress
    }
  }

  // 获取已加入班级的课程列表
  async function fetchSelectedCourses(params = {}) {
    try {
      const response = await getStudentCourses(studentData.value.id, params)
      courses.value = response.results || response // 兼容不同的返回格式
      updateCourseStats()
      return response
    } catch (error) {
      console.error('获取课程列表失败:', error)
      throw error
    }
  }

  // 获取学习统计数据
  async function fetchLearningStats() {
    try {
      const response = await getStudentLearningStats()
      updateLearningStats(response)
      // 更新studentData中的相关字段
      updateStudentData({
        continuousLearningDays: response.continuousLearningDays,
        weeklyLearningHours: response.weeklyLearningHours
      })
      return response
    } catch (error) {
      console.error('获取学习统计数据失败:', error)
      return null
    }
  }

  // 获取当前学生信息
  async function fetchCurrentStudentInfo() {
    try {
      const response = await getCurrentStudentInfo()
      updateStudentData(response)
      // 获取学习统计数据
      await fetchLearningStats()
      return response
    } catch (error) {
      console.error('获取学生信息失败:', error)
      return null
    }
  }

  // 加入班级
  async function joinClass(code) {
    try {
      await joinClassByCode(code)
      // 加入成功后刷新用户数据
      await fetchCurrentStudentInfo()
      return true
    } catch (error) {
      console.error('加入班级失败:', error)
      return false
    }
  }

  return {
    // State
    studentData,
    learningStats,
    learningRecommendations,
    courseStats,
    courses,
    
    // Getters
    userFullName,
    userPoints,
    formattedPoints,
    
    // Actions
    updatePoints,
    updateStudentData,
    updateLearningStats,
    getCourseById,
    updateCourseStats,
    fetchSelectedCourses,
    joinClass,
    fetchCurrentStudentInfo,
    fetchLearningStats
  }
}) 