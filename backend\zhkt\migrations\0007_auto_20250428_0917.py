# Generated by Django 3.2.20 on 2025-04-28 01:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0006_auto_20250427_1742'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='course',
            options={'ordering': ['-created_at'], 'verbose_name': '课程', 'verbose_name_plural': '课程'},
        ),
        migrations.AlterField(
            model_name='course',
            name='code',
            field=models.CharField(max_length=20, unique=True, verbose_name='课程编码'),
        ),
        migrations.AlterField(
            model_name='course',
            name='credits',
            field=models.PositiveIntegerField(default=0, verbose_name='学分'),
        ),
        migrations.AlterField(
            model_name='course',
            name='description',
            field=models.TextField(blank=True, verbose_name='课程描述'),
        ),
        migrations.RemoveField(
            model_name='course',
            name='major',
        ),
        migrations.AddField(
            model_name='course',
            name='major',
            field=models.ManyToManyField(related_name='courses', to='zhkt.Major', verbose_name='适用专业'),
        ),
        migrations.AlterField(
            model_name='course',
            name='name',
            field=models.CharField(max_length=100, verbose_name='课程名称'),
        ),
        migrations.AlterField(
            model_name='course',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='zhkt.teacher', verbose_name='授课教师'),
        ),
        migrations.AlterField(
            model_name='course',
            name='total_hours',
            field=models.PositiveIntegerField(default=0, verbose_name='总学时'),
        ),
    ]
