import request from '@/utils/request'

export const lessonApi = {
    /**
     * 获取章节的课时列表
     * @param {number} chapterId - 章节ID
     * @returns {Promise} 包含课时列表的Promise
     */
    getLessons(chapterId) {
      return request({
        url: `/lessons/?chapter_id=${chapterId}`,
        method: 'get'
      })
    },
  
    /**
     * 创建课时
     * @param {Object} data - 课时数据
     * @returns {Promise} 包含创建结果的Promise
     */
    createLesson(data) {
      return request({
        url: '/lessons/',
        method: 'post',
        data
      })
    },
  
    /**
     * 更新课时
     * @param {number} id - 课时ID
     * @param {Object} data - 课时数据
     * @returns {Promise} 包含更新结果的Promise
     */
    updateLesson(id, data) {
      return request({
        url: `/lessons/${id}/`,
        method: 'patch',
        data
      })
    },
  
    /**
     * 删除课时
     * @param {number} id - 课时ID
     * @returns {Promise} 包含删除结果的Promise
     */
    deleteLesson(id) {
      return request({
        url: `/lessons/${id}/`,
        method: 'delete'
      })
    },
  
    /**
     * 上传课时视频
     * @param {number} id - 课时ID
     * @param {File} file - 视频文件
     * @returns {Promise} 包含上传结果的Promise
     */
    uploadLessonVideo(id, file) {
      const formData = new FormData()
      formData.append('file', file)
      return request({
        url: `/lessons/${id}/upload_video/`,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    }
  } 