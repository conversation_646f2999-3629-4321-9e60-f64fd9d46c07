<template>
  <StudentLayout
    :userName="studentData.name"
    :userAvatar="studentData.avatar"
    pageTitle="AI学习"
    activePage="ai-learning"
  >
    <div class="flex h-full">
      <!-- Inner Left Sidebar -->
      <div class="inner-sidebar w-56 bg-slate-50 p-4 flex flex-col border-r border-slate-200 flex-shrink-0">
        
        <!-- Navigation (from original screenshot) -->
        <nav class="space-y-2">
          <div>
            <router-link to="/student/ai-learning" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-blue-100 hover:text-blue-700 [&.router-link-exact-active]:bg-blue-600 [&.router-link-exact-active]:text-white [&.router-link-exact-active]:font-semibold">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              主页
            </router-link>
          </div>
          <div>
            <router-link to="/student/today-learn" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-blue-100 hover:text-blue-700 [&.router-link-exact-active]:bg-blue-600 [&.router-link-exact-active]:text-white [&.router-link-exact-active]:font-semibold">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 25">
                <path fill="#175CD3" d="M22.959 8.055v4.062l.915 1.125-1.77 1.62-1.627-1.62.976-1.146V8.724c-5.227 2.248-6.651 2.961-7.81 3.502-1.16.54-1.993.54-3.152.086-1.14-.454-6.367-2.55-9.092-3.912-1.81-.908-1.933-1.491.04-2.27 2.563-1.015 6.549-2.657 8.787-3.543 1.322-.562 2.033-.865 3.254-.238 2.177.95 6.895 2.896 9.377 3.977 2.156.993.712 1.318.102 1.729m-9.052 5.856c1.261-.54 2.97-1.448 4.841-2.29v6.699S16.348 21 12.097 21c-4.556 0-7.018-2.68-7.018-2.68v-6.267c1.445.605 3.051 1.145 5.004 1.836 1.2.476 2.726.627 3.824.022"></path>
              </svg>
              今天学点啥
            </router-link>
          </div>
          <div>
            <router-link to="/student/bookshelf" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-blue-100 hover:text-blue-700 [&.router-link-exact-active]:bg-blue-600 [&.router-link-exact-active]:text-white [&.router-link-exact-active]:font-semibold">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              书架
            </router-link>
          </div>
        </nav>
        <div class="flex-grow"></div>
      </div>

      <!-- Main Content (Search Interface) -->
      <div class="main-search-content flex-1 p-8 overflow-y-auto">
        <div class="w-[700px] mx-auto text-center">
          <div class="mt-16 mb-8">
            <div class="flex items-center justify-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="49" height="30" fill="none" viewBox="0 0 49 30"><path fill="url(#paint0_linear_1565_2625)" d="M15.846 13.91a1.74 1.74 0 0 1 3.008 0l8.233 14.203c1.153 1.99 3.867 2.534 5.722.995 1.448-1.198 1.651-3.347.724-4.975L20.595 1.898"></path><path fill="#175CD3" d="m48.122 24.314-7.94-13.73c-1.04-1.81-3.37-2.42-5.179-1.38s-2.42 3.37-1.38 5.18l7.94 13.73c1.04 1.809 3.37 2.42 5.179 1.379s2.42-3.37 1.38-5.18M35.116 1.899c-1.04-1.81-3.37-2.42-5.18-1.38s-2.42 3.37-1.38 5.18 3.37 2.42 5.18 1.38c1.81-1.064 2.443-3.37 1.38-5.18M19.216.52a3 3 0 0 0-.362-.182 3.75 3.75 0 0 0-4.614 1.244l-.023.023c-.023.045-.045.068-.068.113s-.068.113-.09.158L1.166 24.11a4 4 0 0 0-.497 1.402v.091a2.8 2.8 0 0 0-.068.588c-.023 1.086.407 2.171 1.289 2.895.724.61 1.56.882 2.398.905h.135c1.29 0 2.556-.679 3.257-1.878l8.301-14.407 4.637-8.03A3.78 3.78 0 0 0 19.216.52"></path><defs><linearGradient id="paint0_linear_1565_2625" x1="15.845" x2="34.095" y1="15.945" y2="15.945" gradientUnits="userSpaceOnUse"><stop offset="0.144" stop-color="#194185"></stop><stop offset="0.218" stop-color="#1849A9"></stop><stop offset="0.375" stop-color="#175CD3"></stop><stop offset="0.579" stop-color="#175CD3"></stop><stop offset="1" stop-color="#175CD3"></stop></linearGradient></defs></svg>
              <h1 class="text-3xl font-bold mb-2" style="color: #175cd3; margin-left: 6px;">AI学习</h1>
            </div>
            <p class="text-gray-400 font-bold" style="font-size: 20px;">没有广告，直达结果</p>
          </div>
          <div class="bg-white p-1 rounded-lg border-2 border-[#1570ef] shadow-sm text-left">
            <textarea
              class="w-full h-20 p-3 rounded-md resize-none focus:outline-none"
              placeholder="请输入，Enter键发送，Shift+Enter键换行"
              v-model="searchQuery"
              @keyup.enter="performSearch"
              @paste="handleImagePaste"
            ></textarea>
            <div :class="[
              'mt-4 flex justify-between items-center ml-2 mr-2',
              uploadedImage ? 'border-b border-gray-200 pb-2' : ''
            ]">
              <div v-if="!uploadedImage" class="flex items-center">
                <div class="relative inline-block text-left">
                  <div>
                    <button @click="toggleScopeDropdown" type="button" class="flex items-center text-sm text-gray-800 hover:text-blue-600 font-bold" id="options-menu" aria-haspopup="true" aria-expanded="true">
                      <div :class="['flex items-center justify-center w-3 h-3 rounded-full mr-1.5', `bg-${scopeColors[selectedScope].split('-')[1]}-100`]">
                        <span :class="['inline-block w-1.5 h-1.5 rounded-full', scopeColors[selectedScope]]"></span>
                      </div>
                      {{ selectedScope }}
                      <svg class="-mr-1 ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                    </button>
                  </div>
                  <div v-if="isScopeDropdownOpen" class="origin-top-left absolute left-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                    <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                      <div class="px-4 py-2 text-xs text-gray-500">搜索范围</div>
                      <a v-for="scope in scopes" :key="scope.name" @click="selectScope(scope.name)" href="#" :class="['block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 flex items-center justify-between', { 'font-semibold text-blue-600': selectedScope === scope.name }]" role="menuitem">
                        <div class="flex items-center">
                          <div :class="['flex items-center justify-center w-3 h-3 rounded-full mr-2', `bg-${scope.color.split('-')[1]}-100`]">
                            <span :class="['inline-block w-1.5 h-1.5 rounded-full', scope.color]"></span>
                          </div>
                          {{ scope.name }}
                        </div>
                        <svg v-if="selectedScope === scope.name" class="h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </a>
                      <div class="border-t border-gray-200 my-1"></div>
                      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 flex items-center" role="menuitem">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7.286M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M7.657 7.343A8 8 0 0018.97 18.97M10 14c.994 2.523 2.582 4 5.014 4.714M14 10c-.994-2.523-2.582-4-5.014-4.714" />
                        </svg>
                        工作流
                      </a>
                      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 flex items-center" role="menuitem">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        偏好设置
                      </a>
                    </div>
                  </div>
                </div>
                <div class="flex items-center ml-3">
                  <label class="inline-flex items-center cursor-pointer">
                    <input type="checkbox" v-model="isLongThinking" class="sr-only peer">
                    <div class="relative w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                  <span class="text-sm text-gray-500 font-bold ml-2">长思考·R1</span>
                </div>
              </div>
              <div v-if="uploadedImage" class="flex items-center space-x-2" style="margin-right: 10px; font-size: 14px;">
                <button class="flex items-center px-3 py-1.5 text-sm font-bold text-[#175CD3] bg-white rounded-md hover:bg-blue-50" style="border: 1px solid #175CD3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                  给我讲讲
                </button>
                <button class="flex items-center px-3 py-1.5 text-sm font-bold text-gray-700 bg-white border border-gray-200 rounded-md hover:bg-gray-50" style="border: 1px solid #475467">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  以图搜图
                </button>
              </div>
              <div class="flex items-center space-x-2" style="margin-right: 10px;">
                <div class="relative group">
                  <input
                    ref="fileInput"
                    type="file"
                    accept="image/*"
                    class="hidden"
                    @change="handleFileChange"
                  />
                  <button
                    class="p-1.5 text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none"
                    title="上传图片"
                    @click="triggerFileInput"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </button>
                  <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    Ctrl+V粘贴图片
                    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-gray-900"></div>
                  </div>
                </div>
                
                <div class="relative group">
                  <button 
                    @click="isInputEmpty ? null : performSearch" 
                    :class="[
                      'p-1.5 text-white rounded-full focus:outline-none', 
                      isInputEmpty ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
                    ]" 
                    :title="isInputEmpty ? '请输入搜索内容' : '发送'"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div v-if="uploadedImage" class="mt-2 flex justify-start border-gray-200 pt-2 relative ml-2 mr-2" style="width: 100px;">
              <img :src="uploadedImage" alt="预览" class="max-h-40 mb-2 ml-2" style="width: 65px;" />
              <button @click="removeImage" class="absolute top-0 right-0 mt-1 mr-1 bg-white bg-opacity-80 hover:bg-red-500 hover:text-white text-gray-500 rounded-full p-0.5 shadow transition-colors" style="line-height: 1;">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          <div class="mt-6 flex justify-center">
            <div class="flex space-x-4 border border-gray-200 rounded-lg bg-white">
              <button
                v-for="mode in modes"
                :key="mode.value"
                @click="selectMode(mode.value)"
                :class="[
                  'min-w-[120px] px-6 py-2 text-sm font-bold rounded-md focus:outline-none flex items-center justify-center',
                  selectedMode === mode.value
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 text-gray-700 hover:bg-gray-50',
                  mode.value === 'deep' ? 'min-w-[150px]' : ''
                ]"
              >
                <svg
                  v-if="mode.icon"
                  width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5"
                >
                  <path 
                    d="M14.667 17.168H14.5c-1.4 0-2.1 0-2.635-.273a2.5 2.5 0 0 1-1.092-1.092c-.273-.535-.273-1.235-.273-2.635V7.835c0-1.4 0-2.1.273-2.635a2.5 2.5 0 0 1 1.092-1.093c.535-.272 1.235-.272 2.635-.272h.167m0 13.333a1.667 1.667 0 1 0 3.333 0 1.667 1.667 0 0 0-3.333 0m0-13.333a1.667 1.667 0 1 0 3.333 0 1.667 1.667 0 0 0-3.333 0M6.333 10.5h8.334m-8.334 0a1.667 1.667 0 1 1-3.333 0 1.667 1.667 0 0 1 3.333 0m8.334 0a1.667 1.667 0 1 0 3.333 0 1.667 1.667 0 0 0-3.333 0"
                    :stroke="selectedMode === 'research' ? '#fff' : 'url(#research-gradient)'"
                    stroke-width="1.7" stroke-linecap="round" stroke-linejoin="round"
                  />
                </svg>
                {{ mode.label }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </StudentLayout>
  <svg width="0" height="0" style="position:absolute">
    <defs>
      <linearGradient id="research-gradient" x1="6" y1="3" x2="17" y2="18" gradientUnits="userSpaceOnUse">
        <stop stop-color="#0017E4"/>
        <stop offset="1" stop-color="#3793FF"/>
      </linearGradient>
    </defs>
  </svg>
</template>

<script setup>
import { ref, computed } from 'vue'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import studentAvatar from '@/assets/images/avatars/student1.png'

const studentData = ref({
  name: '张同学', 
  avatar: studentAvatar,
})

const searchQuery = ref('')
const isScopeDropdownOpen = ref(false)
const selectedScope = ref('全网')
const scopes = ref([
  { name: '全网', color: 'bg-blue-500' },
  { name: '文库', color: 'bg-red-500' },
  { name: '学术', color: 'bg-purple-500' },
  { name: '图片', color: 'bg-cyan-500' },
  { name: '视频', color: 'bg-pink-500' },
  { name: '播客', color: 'bg-orange-500' },
])

const scopeColors = computed(() => {
  return scopes.value.reduce((acc, scope) => {
    acc[scope.name] = scope.color;
    return acc;
  }, {});
});

const isInputEmpty = computed(() => {
  return !searchQuery.value || searchQuery.value.trim() === '';
});

const toggleScopeDropdown = () => {
  isScopeDropdownOpen.value = !isScopeDropdownOpen.value
}

const selectScope = (scopeName) => {
  selectedScope.value = scopeName
  isScopeDropdownOpen.value = false
}

const performSearch = () => {
  if (!isInputEmpty.value) {
    // Implement search logic here, potentially using selectedScope.value
    console.log(`Searching for "${searchQuery.value}" in scope "${selectedScope.value}"`);
    // Reset input or navigate to results page etc.
  }
}

const isLongThinking = ref(false)

const fileInput = ref(null)
const uploadedImage = ref(null) // 用于图片预览

const triggerFileInput = () => {
  if (fileInput.value) fileInput.value.click()
}

const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (file && file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      uploadedImage.value = e.target.result // base64
      // 这里可以上传到后端，或仅预览
      // uploadImage(file)
    }
    reader.readAsDataURL(file)
  }
}

// 粘贴图片时也赋值 uploadedImage
const handleImagePaste = (event) => {
  const items = (event.clipboardData || event.originalEvent.clipboardData).items;
  for (const item of items) {
    if (item.type.indexOf('image') === 0) {
      const blob = item.getAsFile();
      const reader = new FileReader();
      reader.onload = (e) => {
        uploadedImage.value = e.target.result;
        // uploadImage(blob)
      }
      reader.readAsDataURL(blob);
      event.preventDefault();
      break;
    }
  }
}

const modes = [
  { label: '简洁', value: 'simple' },
  { label: '深入', value: 'deep' },
  { label: '研究', value: 'research', icon: true }
]
const selectedMode = ref('simple')
const selectMode = (mode) => {
  selectedMode.value = mode
}

const removeImage = () => {
  uploadedImage.value = null
}
</script>

<style scoped>
/* Tailwind CSS is primarily used for styling. */
.inner-sidebar {
  height: calc(100vh - 64px); /* Assuming header height is 64px */
}

.main-search-content {
   height: calc(100vh - 64px); /* Assuming header height is 64px */
}
</style> 