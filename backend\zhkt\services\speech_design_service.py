import base64
import glob
import logging
import os
import re
import time
import subprocess
from typing import List, Dict, Any, Generator
import json

# 移除office模块导入，使用LibreOffice替代
# import office

from .. import config
from ..utils.temp_file_utils import create_temp_subdir, clean_temp_file, clean_temp_dir, get_temp_filepath
# 导入DeepSeek API
from ..utils.deepseek_api import DeepSeekAPI

# 设置日志
logger = logging.getLogger(__name__)

class SpeechDesignService:
    """讲稿设计服务类，提供PPT解析、图片提取、讲稿生成等功能"""
    
    @staticmethod
    def parse_ppt(file) -> Dict[str, Any]:
        """
        解析PPT文件并提取内容和图片
        
        Args:
            file: 上传的PPT文件对象
        
        Returns:
            Dict: 包含PPT解析结果的字典
        """
        if not file:
            raise ValueError("未找到上传的文件")
            
        # 检查文件类型
        filename = file.name
        if not (filename.endswith('.ppt') or filename.endswith('.pptx')):
            raise ValueError("文件必须是.ppt或.pptx格式")
        
        # 使用通用方法获取临时文件路径
        file_suffix = os.path.splitext(filename)[1]
        temp_file_path = get_temp_filepath(file_suffix)
        
        # 创建临时输出目录
        output_dir = create_temp_subdir('ppt_output')
        
        try:
            # 将上传的文件保存到临时文件
            with open(temp_file_path, 'wb') as temp_file:
                for chunk in file.chunks():
                    temp_file.write(chunk)
            
            logger.info(f"文件已保存到临时路径: {temp_file_path}")
            logger.info(f"输出目录: {output_dir}")
            
            # 使用python-pptx提取PPT文本内容
            ppt_content = []
            try:
                logger.info("使用python-pptx提取PPT文本内容")
                from pptx import Presentation
                ppt = Presentation(temp_file_path)
                
                for i, slide in enumerate(ppt.slides):
                    slide_content = ""
                    slide_title = ""
                    
                    # 提取标题和内容
                    for shape in slide.shapes:
                        if hasattr(shape, "has_text_frame") and shape.has_text_frame:
                            if shape.text.strip():
                                if not slide_title:
                                    slide_title = shape.text
                                else:
                                    slide_content += shape.text + "\n"
                    
                    ppt_content.append({
                        "index": i,
                        "title": slide_title or f"幻灯片 {i+1}",
                        "content": slide_content.strip()
                    })
                
                logger.info(f"成功提取 {len(ppt_content)} 张幻灯片的文本内容")
                
            except Exception as e:
                logger.error(f"使用python-pptx提取文本失败: {str(e)}")
                logger.info("继续尝试使用图片转换方式处理")
            
            # 使用LibreOffice将PPT转换为图片
            conversion_success = SpeechDesignService.convert_ppt_to_images_with_libreoffice(
                temp_file_path, output_dir
            )
            
            if not conversion_success:
                logger.error("LibreOffice PPT转图片失败")
                raise RuntimeError("PPT转图片失败")
            
            # 等待文件写入完成
            time.sleep(1)
            
            # 检查转换结果
            logger.info(f"PPT转图片完成，检查输出目录...")
            logger.info(f"输出目录内容: {os.listdir(output_dir)}")

            # 提取PPT标题
            ppt_title = os.path.splitext(os.path.basename(filename))[0]
            
            # 查找所有图片文件
            image_files = SpeechDesignService.find_image_files(output_dir)
            logger.info(f"找到 {len(image_files)} 个图片文件")
            
            slides = []
            
            # 将图片和文本内容结合
            for i, img_path in enumerate(image_files):
                # 读取图片并转换为base64
                with open(img_path, 'rb') as img_file:
                    img_data = img_file.read()
                    img_base64 = base64.b64encode(img_data).decode('utf-8')
                
                # 识别图片格式
                image_format = "png"  # 默认格式
                if img_path.lower().endswith('.jpg') or img_path.lower().endswith('.jpeg'):
                    image_format = "jpeg"
                
                # 获取文本内容
                slide_title = f"幻灯片 {i+1}"
                slide_content = ""
                slide_notes = ""
                
                # 如果有提取到的文本内容，则使用
                if ppt_content and i < len(ppt_content):
                    if isinstance(ppt_content[i], dict):
                        slide_title = ppt_content[i].get("title", slide_title)
                        slide_content = ppt_content[i].get("content", "")
                        slide_notes = ppt_content[i].get("notes", "")
                    elif isinstance(ppt_content[i], str):
                        # 如果直接是文本字符串
                        slide_content = ppt_content[i]
                
                # 创建幻灯片对象
                slide = {
                    "index": i,
                    "title": slide_title,
                    "content": slide_content,
                    "notes": slide_notes,
                    "image": {
                        "image_data": img_base64,
                        "image_format": image_format
                    }
                }
                slides.append(slide)
            
            logger.info(f"成功解析 {len(slides)} 张幻灯片，包含文本内容: {bool(ppt_content)}")
            
            return {
                "code": 200,
                "message": "PPT解析成功",
                "data": {
                    "title": ppt_title,
                    "total_slides": len(slides),
                    "slides": slides
                }
            }
            
        except Exception as e:
            logger.exception(f"PPT解析错误: {str(e)}")
            raise RuntimeError(f"PPT解析失败: {str(e)}")
        finally:
            # 清理临时文件和目录，使用通用方法
            clean_temp_file(temp_file_path)
            clean_temp_dir(output_dir)
    
    @staticmethod
    def find_image_files(directory) -> List[str]:
        """
        查找目录中的所有图片文件并按幻灯片顺序排序
        
        Args:
            directory: 要搜索的目录
        
        Returns:
            List[str]: 排序后的图片文件路径列表
        """
        image_files = []
        image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.gif']
        
        # 首先查找LibreOffice生成的slide_xxx.png格式文件
        slide_pattern = os.path.join(directory, 'slide_*.png')
        slide_files = glob.glob(slide_pattern)
        
        if slide_files:
            logger.info(f"找到LibreOffice生成的幻灯片图片: {len(slide_files)} 个")
            # 按文件名中的数字排序
            def extract_slide_number(filename):
                try:
                    basename = os.path.basename(filename)
                    match = re.search(r'slide_(\d+)', basename)
                    return int(match.group(1)) if match else 0
                except:
                    return 0
            
            slide_files.sort(key=extract_slide_number)
            image_files = slide_files
        else:
            # 如果没有找到slide_xxx.png格式，则查找其他图片文件
            logger.info("未找到LibreOffice格式图片，查找其他图片文件")
            
            # 获取子目录列表
            subdirs = [d for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))]
            logger.info(f"发现子目录: {subdirs}")
            
            search_dir = os.path.join(directory, subdirs[0]) if subdirs else directory
            logger.info(f"搜索目录: {search_dir}")
            
            # 查找所有图片文件
            for ext in image_extensions:
                files = glob.glob(os.path.join(search_dir, ext))
                image_files.extend(files)
            
            # 按文件名中的数字排序
            def extract_number(filename):
                try:
                    numbers = re.findall(r'\d+', os.path.basename(filename))
                    return int(numbers[-1]) if numbers else 0
                except:
                    return 0
            
            try:
                image_files.sort(key=extract_number)
            except Exception as e:
                logger.warning(f"按数字排序失败: {str(e)}，使用默认排序")
                image_files.sort()
        
        logger.info(f"最终找到 {len(image_files)} 个图片文件")
        if image_files:
            logger.info(f"排序后的前5个文件: {[os.path.basename(f) for f in image_files[:5]]}")
        
        return image_files
    
    @staticmethod
    def generate_slide_script_stream(current_slide: Dict, previous_scripts: Dict, settings: Dict, future_slides: List[Dict] = None) -> Generator[str, None, None]:
        """
        生成单个幻灯片的讲稿内容（流式版），考虑之前幻灯片的上下文内容，以及后续幻灯片内容
        
        Args:
            current_slide: 当前需要生成讲稿的幻灯片数据
            previous_scripts: 之前已生成的讲稿内容（按幻灯片索引为键）
            settings: 讲稿生成设置
            future_slides: 后续幻灯片内容，用于让AI为下文做铺垫
        
        Yields:
            str: SSE formatted string chunks of the generated script
        """
        slide_index = current_slide.get('index', 0)
        slide_title = current_slide.get('title', f"幻灯片 {slide_index+1}")
        slide_content = current_slide.get('content', '')
        logger.info(f"开始为幻灯片 {slide_index} ({slide_title}) 流式生成讲稿")
        
        try:
            # 初始化DeepSeek API 客户端
            deepseek_client = DeepSeekAPI()
            
            # 讲稿风格映射
            style_descriptions = {
                'standard': "使用标准教学语言，清晰、专业但不过于学术化，适合一般教学场景",
                'academic': "使用学术性语言，包含专业术语和理论框架，适合高等教育",
                'simple': "使用简单易懂的语言，多用比喻和例子，避免复杂术语，适合低年级学生",
                'lively': "使用活泼生动的语言，加入幽默和互动元素，保持听众兴趣"
            }
            
            # 受众映射
            audience_descriptions = {
                'primary': "小学生，需要简单直观的解释和具体例子",
                'middle': "初中生，可以理解一些抽象概念，但需要与生活联系",
                'high': "高中生，能够理解较复杂的概念和理论，但仍需要清晰解释",
                'college': "大学生，能够理解专业概念和理论框架"
            }
            
            # 获取风格和受众设置
            style = settings.get('style', 'standard')
            audience = settings.get('targetAudience', 'college')
            
            # 添加后续幻灯片的内容信息（如果有）
            future_slides_info = ""
            if future_slides and len(future_slides) > 0:
                logger.info(f"检测到 {len(future_slides)} 张后续幻灯片信息")
                future_slides_info = "后续幻灯片预览：\n"
                
                # 最多包含3张后续幻灯片的内容
                max_future_slides = min(3, len(future_slides))
                for i in range(max_future_slides):
                    future_slide = future_slides[i]
                    future_index = future_slide.get('index', slide_index + i + 1)
                    future_title = future_slide.get('title', f"幻灯片 {future_index+1}")
                    future_content = future_slide.get('content', '')
                    
                    future_slides_info += f"【幻灯片{future_index+1}：{future_title}】\n"
                    if future_content:
                        future_slides_info += f"内容：{future_content}\n\n"
            
            # ===== 新版提示词设计（排版优化） =====

            # 1. 构建系统提示词
            system_prompt = (
                f"你是一位经验丰富的教育专家和演讲者，擅长制作简洁有力的讲稿。\n"
                f"当前正在准备一个关于\"{settings.get('presentation_title', slide_title)}\"的演讲，目标是：{settings.get('presentation_goal', '帮助学生理解知识点')}\n\n"
                "你的角色和任务：\n"
                "1. 作为一位专业讲师，你需要将复杂的内容转化为易于理解的形式\n"
                "2. 确保讲稿内容紧扣当前幻灯片主题，简洁直接\n"
                f"3. 使用适合{audience_descriptions.get(audience, '大学生')}理解水平的语言\n"
                "4. 基于幻灯片内容进行生动讲解，不要过多回顾和展望\n"
                "5. 保持讲稿语言的简洁性，避免废话和重复\n\n"
                "演讲风格要求：\n"
                f"1. 语言风格：{style_descriptions.get(style, '标准教学语言')}\n"
                "2. 表达方式：简洁明了、直接切题、易于理解\n"
                "3. 互动设计：适当设置互动环节和思考问题\n"
                "4. 内容组织：层次分明、重点突出、逻辑清晰\n\n"
                "请记住：\n"
                "- 讲稿长度控制在50-150字之间\n"
                "- 使用'我们'、'大家'等字眼增加亲切感\n"
                "- 直接讲解当前幻灯片内容，尽量避免'刚才我们讨论了'、'接下来我们将'等过渡语\n"
                "- 基于当前幻灯片提供的文本内容进行讲解\n"
                "- 避免啰嗦，直接切入主题"
            )

            # 2. 构建用户提示词
            def format_context_slides(previous_scripts):
                """格式化历史上下文信息，仅取最近3张"""
                if not previous_scripts:
                    return "这是演讲的开始部分。"
                context_text = "前文内容回顾：\n"
                sorted_indices = sorted([
                    int(idx) for idx in previous_scripts.keys() if str(idx).isdigit()
                ])
                for idx in sorted_indices[-3:]:  # 只取最近3张
                    prev_slide = previous_scripts.get(str(idx), {}) or previous_scripts.get(idx, {})
                    if (
                        isinstance(prev_slide, dict)
                        and 'title' in prev_slide
                        and 'script' in prev_slide
                    ):
                        prev_title = prev_slide.get('title', f"幻灯片 {idx+1}")
                        prev_script = prev_slide.get('script', '')
                        context_text += (
                            f"【幻灯片{idx+1}：{prev_title}】\n"
                            f"讲稿内容：{prev_script}\n\n"
                        )
                return context_text

            user_prompt = (
                f"请为当前幻灯片生成讲稿内容。\n\n"
                f"【演讲基本信息】\n"
                f"- 主题：{settings.get('presentation_title', slide_title)}\n"
                f"- 目标：{settings.get('presentation_goal', '帮助学生理解知识点')}\n"
                f"- 当前进度：第{slide_index+1}/{settings.get('total_slides', '?')}张幻灯片\n\n"
                f"【当前幻灯片信息】\n"
                f"标题：{slide_title}\n"
                f"幻灯片内容：{slide_content}\n"
                f"【历史上下文】\n{format_context_slides(previous_scripts)}\n\n"
            )
            
            # 添加后续幻灯片信息（如果有）
            if future_slides_info:
                user_prompt += f"【后续幻灯片预览】\n{future_slides_info}\n\n"
                user_prompt += "请考虑后续幻灯片的标题和内容，为接下来的内容做好铺垫。\n\n"
            
            user_prompt += (
                "【讲稿具体要求】\n"
                "1. 内容要求：\n"
                "   - 直接切入当前幻灯片的核心内容\n"
                "   - 基于当前幻灯片的文本内容进行讲解\n"
                "   - 使用生动的语言和具体的例子\n"
                "   - 适当设置互动环节和思考问题\n"
                "2. 结构要求：\n"
                "   - 开场：直接切入主题，避免啰嗦的引入\n"
                "   - 主体：清晰讲解当前幻灯片内容\n"
                "   - 结尾：简洁总结，可以简短提示下一主题\n"
                f"3. 表达要求：\n   - 使用{style_descriptions.get(style, '标准教学语言')}的语言风格\n   - 适合{audience_descriptions.get(audience, '大学生')}的理解水平\n   - 保持讲解的简洁性和直接性\n"
                "请直接给出讲稿内容，不需要额外的解释。\n"
                "请保持内容简洁，避免使用'刚才我们讲了'、'前面提到'等过渡语。\n"
                "请直接讲解当前幻灯片内容，不要过多回顾过去或展望未来。\n"
                "请不要乱窜PPT，只生成当前PPT的讲稿内容。\n"
                "除了第一张幻灯片，其他幻灯片开头避免出现：大家好、好的、现在来到了第X张幻灯片\n"
                "讲稿力求简洁有力，直击重点，避免内容重复和啰嗦\n"
            )

            # 设置对话历史
            messages = [
                deepseek_client.create_system_message(system_prompt),
                deepseek_client.create_user_message(user_prompt)
            ]
            
            complete_script = ""
            try:
                # 使用DeepSeek API流式创建聊天完成
                for api_response_chunk in deepseek_client.chat_stream(
                    messages=messages,
                    model="deepseek-chat",
                    temperature=0.7,
                    max_tokens=1000  # Max tokens for the whole response
                ):
                    if api_response_chunk:
                        script_piece = api_response_chunk.get('content', '') # Assuming chat_stream yields dicts with 'content'
                        if script_piece:
                            complete_script += script_piece
                            yield f"data: {json.dumps({'script_chunk': script_piece})}\n\n"

                logger.info(f"Streamed讲稿 for slide {slide_index+1} ({slide_title}), total length: {len(complete_script)}")
                yield f"data: {json.dumps({'status': 'completed', 'title': slide_title})}\n\n"

            except Exception as api_error:
                logger.error(f"DeepSeek API streaming call failed for slide {slide_index+1} ({slide_title}): {str(api_error)}")
                yield f"data: {json.dumps({'error': f'DeepSeek API call failed: {str(api_error)}', 'status': 'failed'})}\n\n"
            
        except Exception as e:
            logger.exception(f"幻灯片讲稿流式生成错误 for slide {slide_index+1} ({slide_title}): {str(e)}")
            yield f"data: {json.dumps({'error': f'讲稿生成内部错误: {str(e)}', 'status': 'failed'})}\n\n"

    @staticmethod
    def extract_ppt_images(ppt_file, slide_index=None):
        """
        从PPT中提取图片
        
        Args:
            ppt_file: 上传的PPT文件对象
            slide_index (int, optional): 指定要提取图片的幻灯片索引。默认为None，提取所有幻灯片的图片。
        
        Returns:
            dict: 包含提取到的图片信息
                - images: 图片列表，每个元素包含图片数据的base64编码和所属幻灯片索引
                - total_images: 图片总数
        """
        # 调用parse_ppt，然后从结果中提取图片信息
        parse_result = SpeechDesignService.parse_ppt(ppt_file)
        
        if parse_result['code'] != 200:
            return {"images": [], "total_images": 0}
        
        slides = parse_result['data'].get('slides', [])
        images = []
        
        for slide in slides:
            if 'image' in slide:
                # 如果指定了slide_index，只返回指定幻灯片的图片
                if slide_index is not None and slide['index'] != slide_index:
                    continue
                    
                images.append({
                    "slide_index": slide['index'],
                    "image_data": slide['image']['image_data'],
                    "image_format": slide['image']['image_format']
                })
        
        return {
            "images": images,
            "total_images": len(images)
        }

    @staticmethod
    def convert_ppt_to_images_with_libreoffice(ppt_file_path: str, output_dir: str) -> bool:
        """
        使用LibreOffice将PPT转换为图片（所有幻灯片）

        Args:
            ppt_file_path: PPT文件路径
            output_dir: 输出目录

        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info(f"使用LibreOffice将PPT所有幻灯片转换为图片: {ppt_file_path}")

            # 从配置文件中读取LibreOffice路径
            libreoffice_exe = config.LibreOffice_path
            logger.info(f"使用 LibreOffice: {libreoffice_exe}")

            # 确保输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 第一步：PPT转PDF
            logger.info("将PPT转换为PDF中间格式")
            cmd_pdf = [
                libreoffice_exe,
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', output_dir,
                ppt_file_path
            ]

            logger.info(f"执行LibreOffice PPT转PDF命令: {' '.join(cmd_pdf)}")
            result_pdf = subprocess.run(cmd_pdf, capture_output=True, text=True, timeout=120)

            if result_pdf.returncode != 0:
                error_msg = result_pdf.stderr if result_pdf.stderr else result_pdf.stdout
                logger.error(f"LibreOffice PPT转PDF失败: {error_msg}")
                return False

            # 查找生成的PDF文件
            pdf_files = glob.glob(os.path.join(output_dir, '*.pdf'))
            if not pdf_files:
                logger.error("PPT转PDF完成但未找到PDF文件")
                return False

            actual_pdf_path = pdf_files[0]
            logger.info(f"PDF转换成功: {actual_pdf_path}")

            # 第二步：使用PyMuPDF将PDF转换为图片
            try:
                logger.info("使用PyMuPDF将PDF转换为图片")
                import fitz  # PyMuPDF

                # 打开PDF文件
                pdf_document = fitz.open(actual_pdf_path)
                page_count = len(pdf_document)
                logger.info(f"PDF包含 {page_count} 页")

                if page_count > 0:
                    # 转换每一页为图片
                    for page_num in range(page_count):
                        page = pdf_document.load_page(page_num)
                        # 设置缩放比例以获得更好的图片质量
                        mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
                        pix = page.get_pixmap(matrix=mat)

                        # 保存为PNG
                        image_filename = f"slide_{page_num+1:03d}.png"
                        image_path = os.path.join(output_dir, image_filename)
                        pix.save(image_path)
                        logger.info(f"保存图片: {image_filename}")

                    pdf_document.close()
                    logger.info(f"PyMuPDF成功转换 {page_count} 个页面")

                    # 清理PDF文件
                    try:
                        os.remove(actual_pdf_path)
                        logger.info("清理临时PDF文件")
                    except:
                        pass

                    return True
                else:
                    logger.error("PDF文件没有页面")
                    pdf_document.close()
                    return False

            except ImportError:
                logger.error("PyMuPDF库不可用，请安装: pip install PyMuPDF")
                return False
            except Exception as e:
                logger.error(f"PyMuPDF转换失败: {str(e)}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("LibreOffice转换超时")
            return False
        except Exception as e:
            logger.error(f"转换PPT时出错: {str(e)}")
            return False