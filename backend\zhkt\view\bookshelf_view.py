from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action

from ..services.bookshelf_service import BookshelfService
from ..utils.response import ResponseResult
from ..config import DEFAULT_LANGUAGE


class BookshelfViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def list(self, request):
        user_id = request.user.id
        # 从请求参数或请求头获取当前语言
        language = request.query_params.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language
        data = BookshelfService.get_user_bookshelf_with_status(user_id, language)
        return ResponseResult.success(data=data)

    def create(self, request):
        user_id = request.user.id
        upload_file = request.FILES.get('file')
        if not upload_file:
            return ResponseResult.error(code=400, message='未上传文件')
        
        # 从请求中获取选择的讲课风格（如果没有提供则使用默认的'classroom'风格）
        style_code = request.POST.get('style', 'classroom')
        
        # 获取当前语言
        language = request.POST.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language
        
        # 检查所选风格是否有效
        available_styles = BookshelfService.get_available_styles()
        if style_code not in available_styles:
            # 如果风格无效，使用默认的课堂风格
            style_code = 'classroom'
        
        # 将文件、风格和语言参数传递给服务，创建文档并启动AI处理
        doc = BookshelfService.add_document_to_bookshelf(user_id, upload_file, style_code, language)
        return ResponseResult.success(data={"id": doc.id}, message="上传成功")

    def destroy(self, request, pk=None):
        user_id = request.user.id
        doc, deletion_results = BookshelfService.delete_document_from_bookshelf(user_id, pk)
        # 可以记录文件删除结果，但不向前端返回
        failed_deletes = [path for path, result in deletion_results if not result]
        if failed_deletes:
            print(f"警告: 有{len(failed_deletes)}个文件物理删除失败")
        return ResponseResult.success(message="删除成功")

    @action(detail=False, methods=['post'], url_path='batch_delete')
    def batch_delete(self, request):
        """
        批量删除书架文档
        """
        user_id = request.user.id
        document_ids = request.data.get('document_ids', [])
        
        if not document_ids:
            return ResponseResult.error(code=400, message='未提供要删除的文档ID列表')
        
        try:
            result = BookshelfService.delete_documents_from_bookshelf(user_id, document_ids)
            
            message = f"成功删除 {result['deleted_count']} 个文档"
            if result['failed_files_count'] > 0:
                message += f"，{result['failed_files_count']} 个文件物理删除失败"
            
            return ResponseResult.success(data=result, message=message)
        except Exception as e:
            return ResponseResult.error(code=500, message=f"批量删除失败: {str(e)}")

    @action(detail=True, methods=['get'], url_path='chapters')
    def chapters(self, request, pk=None):
        """
        获取文档的章节分页信息
        """
        doc_id = pk
        # 从请求参数或请求头获取当前语言
        language = request.query_params.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language
        data = BookshelfService.get_document_chapters(doc_id, language)
        return ResponseResult.success(data=data)

    @action(detail=True, methods=['get'], url_path='chapter/(?P<chapter_id>[^/.]+)/pages')
    def chapter_pages(self, request, pk=None, chapter_id=None):
        """
        获取指定章节下的所有要点（分页用）
        """
        # 从请求参数或请求头获取当前语言
        language = request.query_params.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language
        data = BookshelfService.get_chapter_key_points(chapter_id, language)
        return ResponseResult.success(data=data)

    @action(detail=True, methods=['get'], url_path='html')
    def html(self, request, pk=None):
        """
        获取指定文档第page页或指定要点的HTML内容
        """
        doc_id = pk
        page = request.query_params.get('page')
        key_point_id = request.query_params.get('key_point_id')
        # 从请求参数或请求头获取当前语言
        language = request.query_params.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language
        page = int(page) if page is not None else None
        html_str, kp_id = BookshelfService.get_html_content_by_page(doc_id, page, key_point_id, language)
        if html_str is None:
            return ResponseResult.error(code=404, message='未找到HTML内容')
        return ResponseResult.success(data={
            'html': html_str,
            'key_point_id': kp_id,
            'page': page
        })

    @action(detail=True, methods=['get'], url_path='keypoint/(?P<key_point_id>[^/.]+)/audios')
    def keypoint_audios(self, request, pk=None, key_point_id=None):
        """
        获取指定要点下所有音频及字幕
        """
        # 从请求参数或请求头获取当前语言
        language = request.query_params.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language
        data = BookshelfService.get_key_point_audios(key_point_id, language)
        return ResponseResult.success(data=data)

    @action(detail=True, methods=['get'], url_path='speech/(?P<speech_content_id>[^/.]+)/subtitles')
    def speech_subtitles(self, request, pk=None, speech_content_id=None):
        """
        获取指定音频对应的字幕列表
        """
        # 从请求参数或请求头获取当前语言
        language = request.query_params.get('language') or request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language
        subtitle_data = BookshelfService.get_speech_subtitles(speech_content_id, language)
        return ResponseResult.success(data=subtitle_data)

    @action(detail=True, methods=['get'], url_path='chapter/(?P<chapter_id>[^/.]+)/status')
    def chapter_status(self, request, pk=None, chapter_id=None):
        """
        获取章节生成状态及进度
        """
        status_data = BookshelfService.check_chapter_status(chapter_id)
        return ResponseResult.success(data=status_data)

    @action(detail=True, methods=['post'], url_path='chapter/(?P<chapter_id>[^/.]+)/generate')
    def trigger_chapter_generation(self, request, pk=None, chapter_id=None):
        """
        触发章节内容生成
        """
        result = BookshelfService.trigger_chapter_generation(chapter_id)
        if not result['success']:
            return ResponseResult.error(code=400, message=result['message'])
        return ResponseResult.success(data={'task_id': result['task_id']}, message=result['message'])

    @action(detail=True, methods=['get'], url_path='style')
    def get_document_style(self, request, pk=None):
        """
        获取文档的语音风格设置
        """
        doc_id = pk
        style_code = BookshelfService.get_document_style(doc_id)
        available_styles = BookshelfService.get_available_styles()
        
        return ResponseResult.success(data={
            'style': style_code,
            'available_styles': available_styles
        })

    @action(detail=False, methods=['get'], url_path='styles')
    def get_available_styles_with_descriptions(self, request):
        """
        获取所有可用的语音风格及其描述
        """
        styles_with_descriptions = BookshelfService.get_available_styles_with_descriptions()
        return ResponseResult.success(data=styles_with_descriptions)

    @action(detail=True, methods=['post'], url_path='chapter/(?P<chapter_id>[^/.]+)/style')
    def update_chapter_style(self, request, pk=None, chapter_id=None):
        """
        更新章节的风格并重新生成内容
        """
        doc_id = pk
        style_code = request.data.get('style', 'classroom')
        
        # 验证风格代码
        available_styles = BookshelfService.get_available_styles()
        if style_code not in available_styles:
            return ResponseResult.error(code=400, message='无效的风格代码')
        
        # 使用新风格重新生成章节内容
        result = BookshelfService.trigger_chapter_generation_with_style(chapter_id, style_code)
        
        if not result['success']:
            return ResponseResult.error(code=400, message=result['message'])
        
        return ResponseResult.success(
            data={'task_id': result['task_id']}, 
            message=f"已切换到{available_styles[style_code]}，正在重新生成内容"
        )

    @action(detail=True, methods=['get'], url_path='outline-status')
    def outline_status(self, request, pk=None):
        """
        检查文档大纲生成状态
        """
        doc_id = pk
        status_data = BookshelfService.check_document_outline_status(doc_id)
        return ResponseResult.success(data=status_data) 