-- MySQL dump 10.13  Distrib 8.0.38, for Win64 (x86_64)
--
-- Host: localhost    Database: drf-zhkt
-- ------------------------------------------------------
-- Server version	5.7.19-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `auth_group`
--

DROP TABLE IF EXISTS `auth_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_group`
--

LOCK TABLES `auth_group` WRITE;
/*!40000 ALTER TABLE `auth_group` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_group_permissions`
--

DROP TABLE IF EXISTS `auth_group_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`),
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_group_permissions`
--

LOCK TABLES `auth_group_permissions` WRITE;
/*!40000 ALTER TABLE `auth_group_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_group_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_permission`
--

DROP TABLE IF EXISTS `auth_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `content_type_id` int(11) NOT NULL,
  `codename` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`),
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_permission`
--

LOCK TABLES `auth_permission` WRITE;
/*!40000 ALTER TABLE `auth_permission` DISABLE KEYS */;
INSERT INTO `auth_permission` VALUES (1,'Can add log entry',1,'add_logentry'),(2,'Can change log entry',1,'change_logentry'),(3,'Can delete log entry',1,'delete_logentry'),(4,'Can view log entry',1,'view_logentry'),(5,'Can add permission',2,'add_permission'),(6,'Can change permission',2,'change_permission'),(7,'Can delete permission',2,'delete_permission'),(8,'Can view permission',2,'view_permission'),(9,'Can add group',3,'add_group'),(10,'Can change group',3,'change_group'),(11,'Can delete group',3,'delete_group'),(12,'Can view group',3,'view_group'),(13,'Can add user',4,'add_user'),(14,'Can change user',4,'change_user'),(15,'Can delete user',4,'delete_user'),(16,'Can view user',4,'view_user'),(17,'Can add content type',5,'add_contenttype'),(18,'Can change content type',5,'change_contenttype'),(19,'Can delete content type',5,'delete_contenttype'),(20,'Can view content type',5,'view_contenttype'),(21,'Can add session',6,'add_session'),(22,'Can change session',6,'change_session'),(23,'Can delete session',6,'delete_session'),(24,'Can view session',6,'view_session'),(25,'Can add AI提示词',7,'add_aiprompt'),(26,'Can change AI提示词',7,'change_aiprompt'),(27,'Can delete AI提示词',7,'delete_aiprompt'),(28,'Can view AI提示词',7,'view_aiprompt'),(29,'Can add 教师',8,'add_teacher'),(30,'Can change 教师',8,'change_teacher'),(31,'Can delete 教师',8,'delete_teacher'),(32,'Can view 教师',8,'view_teacher'),(33,'Can add 作业',9,'add_homework'),(34,'Can change 作业',9,'change_homework'),(35,'Can delete 作业',9,'delete_homework'),(36,'Can view 作业',9,'view_homework'),(37,'Can add 订单',10,'add_order'),(38,'Can change 订单',10,'change_order'),(39,'Can delete 订单',10,'delete_order'),(40,'Can view 订单',10,'view_order'),(41,'Can add 商品',11,'add_product'),(42,'Can change 商品',11,'change_product'),(43,'Can delete 商品',11,'delete_product'),(44,'Can view 商品',11,'view_product'),(45,'Can add 知识点',12,'add_knowledgepoint'),(46,'Can change 知识点',12,'change_knowledgepoint'),(47,'Can delete 知识点',12,'delete_knowledgepoint'),(48,'Can view 知识点',12,'view_knowledgepoint'),(49,'Can add 学生',13,'add_student'),(50,'Can change 学生',13,'change_student'),(51,'Can delete 学生',13,'delete_student'),(52,'Can view 学生',13,'view_student'),(53,'Can add 作业反馈',14,'add_feedback'),(54,'Can change 作业反馈',14,'change_feedback'),(55,'Can delete 作业反馈',14,'delete_feedback'),(56,'Can view 作业反馈',14,'view_feedback'),(57,'Can add 课程',15,'add_course'),(58,'Can change 课程',15,'change_course'),(59,'Can delete 课程',15,'delete_course'),(60,'Can view 课程',15,'view_course'),(61,'Can add 知识库',16,'add_knowledgebase'),(62,'Can change 知识库',16,'change_knowledgebase'),(63,'Can delete 知识库',16,'delete_knowledgebase'),(64,'Can view 知识库',16,'view_knowledgebase'),(65,'Can add 班级',17,'add_classgroup'),(66,'Can change 班级',17,'change_classgroup'),(67,'Can delete 班级',17,'delete_classgroup'),(68,'Can view 班级',17,'view_classgroup'),(69,'Can add 学习笔记',18,'add_note'),(70,'Can change 学习笔记',18,'change_note'),(71,'Can delete 学习笔记',18,'delete_note'),(72,'Can view 学习笔记',18,'view_note'),(73,'Can add AI对话消息',19,'add_aichatmessage'),(74,'Can change AI对话消息',19,'change_aichatmessage'),(75,'Can delete AI对话消息',19,'delete_aichatmessage'),(76,'Can view AI对话消息',19,'view_aichatmessage'),(77,'Can add 角色',20,'add_role'),(78,'Can change 角色',20,'change_role'),(79,'Can delete 角色',20,'delete_role'),(80,'Can view 角色',20,'view_role'),(81,'Can add 学院',21,'add_college'),(82,'Can change 学院',21,'change_college'),(83,'Can delete 学院',21,'delete_college'),(84,'Can view 学院',21,'view_college'),(85,'Can add 专业',22,'add_major'),(86,'Can change 专业',22,'change_major'),(87,'Can delete 专业',22,'delete_major'),(88,'Can view 专业',22,'view_major'),(89,'Can add AI对话',23,'add_aichat'),(90,'Can change AI对话',23,'change_aichat'),(91,'Can delete AI对话',23,'delete_aichat'),(92,'Can view AI对话',23,'view_aichat'),(93,'Can add 积分记录',24,'add_pointsrecord'),(94,'Can change 积分记录',24,'change_pointsrecord'),(95,'Can delete 积分记录',24,'delete_pointsrecord'),(96,'Can view 积分记录',24,'view_pointsrecord'),(97,'Can add 作业提交',25,'add_submission'),(98,'Can change 作业提交',25,'change_submission'),(99,'Can delete 作业提交',25,'delete_submission'),(100,'Can view 作业提交',25,'view_submission'),(101,'Can add 课程资源',26,'add_resource'),(102,'Can change 课程资源',26,'change_resource'),(103,'Can delete 课程资源',26,'delete_resource'),(104,'Can view 课程资源',26,'view_resource'),(105,'Can add 选课记录',27,'add_courseenrollment'),(106,'Can change 选课记录',27,'change_courseenrollment'),(107,'Can delete 选课记录',27,'delete_courseenrollment'),(108,'Can view 选课记录',27,'view_courseenrollment'),(109,'Can add 章节',28,'add_chapter'),(110,'Can change 章节',28,'change_chapter'),(111,'Can delete 章节',28,'delete_chapter'),(112,'Can view 章节',28,'view_chapter'),(113,'Can add 用户',29,'add_user'),(114,'Can change 用户',29,'change_user'),(115,'Can delete 用户',29,'delete_user'),(116,'Can view 用户',29,'view_user'),(117,'Can add 管理员',30,'add_admin'),(118,'Can change 管理员',30,'change_admin'),(119,'Can delete 管理员',30,'delete_admin'),(120,'Can view 管理员',30,'view_admin');
/*!40000 ALTER TABLE `auth_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_user`
--

DROP TABLE IF EXISTS `auth_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `password` varchar(128) COLLATE utf8mb4_bin NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `first_name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `last_name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `email` varchar(254) COLLATE utf8mb4_bin NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_user`
--

LOCK TABLES `auth_user` WRITE;
/*!40000 ALTER TABLE `auth_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_user_groups`
--

DROP TABLE IF EXISTS `auth_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user_groups` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_groups_user_id_group_id_94350c0c_uniq` (`user_id`,`group_id`),
  KEY `auth_user_groups_group_id_97559544_fk_auth_group_id` (`group_id`),
  CONSTRAINT `auth_user_groups_group_id_97559544_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `auth_user_groups_user_id_6a12ed8b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_user_groups`
--

LOCK TABLES `auth_user_groups` WRITE;
/*!40000 ALTER TABLE `auth_user_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_user_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_user_user_permissions`
--

DROP TABLE IF EXISTS `auth_user_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user_user_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_user_permissions_user_id_permission_id_14a6b632_uniq` (`user_id`,`permission_id`),
  KEY `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_user_user_permissions`
--

LOCK TABLES `auth_user_user_permissions` WRITE;
/*!40000 ALTER TABLE `auth_user_user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_user_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_admin_log`
--

DROP TABLE IF EXISTS `django_admin_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_admin_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext COLLATE utf8mb4_bin,
  `object_repr` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `action_flag` smallint(5) unsigned NOT NULL,
  `change_message` longtext COLLATE utf8mb4_bin NOT NULL,
  `content_type_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`),
  KEY `django_admin_log_user_id_c564eba6_fk_auth_user_id` (`user_id`),
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_admin_log`
--

LOCK TABLES `django_admin_log` WRITE;
/*!40000 ALTER TABLE `django_admin_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `django_admin_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_content_type`
--

DROP TABLE IF EXISTS `django_content_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_content_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `model` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_content_type`
--

LOCK TABLES `django_content_type` WRITE;
/*!40000 ALTER TABLE `django_content_type` DISABLE KEYS */;
INSERT INTO `django_content_type` VALUES (1,'admin','logentry'),(3,'auth','group'),(2,'auth','permission'),(4,'auth','user'),(5,'contenttypes','contenttype'),(6,'sessions','session'),(30,'zhkt','admin'),(23,'zhkt','aichat'),(19,'zhkt','aichatmessage'),(7,'zhkt','aiprompt'),(28,'zhkt','chapter'),(17,'zhkt','classgroup'),(21,'zhkt','college'),(15,'zhkt','course'),(27,'zhkt','courseenrollment'),(14,'zhkt','feedback'),(9,'zhkt','homework'),(16,'zhkt','knowledgebase'),(12,'zhkt','knowledgepoint'),(22,'zhkt','major'),(18,'zhkt','note'),(10,'zhkt','order'),(24,'zhkt','pointsrecord'),(11,'zhkt','product'),(26,'zhkt','resource'),(20,'zhkt','role'),(13,'zhkt','student'),(25,'zhkt','submission'),(8,'zhkt','teacher'),(29,'zhkt','user');
/*!40000 ALTER TABLE `django_content_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_migrations`
--

DROP TABLE IF EXISTS `django_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_migrations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_migrations`
--

LOCK TABLES `django_migrations` WRITE;
/*!40000 ALTER TABLE `django_migrations` DISABLE KEYS */;
INSERT INTO `django_migrations` VALUES (1,'contenttypes','0001_initial','2025-04-21 03:58:30.315518'),(2,'auth','0001_initial','2025-04-21 03:58:30.870143'),(3,'admin','0001_initial','2025-04-21 03:58:31.043394'),(4,'admin','0002_logentry_remove_auto_add','2025-04-21 03:58:31.052370'),(5,'admin','0003_logentry_add_action_flag_choices','2025-04-21 03:58:31.061296'),(6,'contenttypes','0002_remove_content_type_name','2025-04-21 03:58:31.140422'),(7,'auth','0002_alter_permission_name_max_length','2025-04-21 03:58:31.189381'),(8,'auth','0003_alter_user_email_max_length','2025-04-21 03:58:31.206347'),(9,'auth','0004_alter_user_username_opts','2025-04-21 03:58:31.215337'),(10,'auth','0005_alter_user_last_login_null','2025-04-21 03:58:31.307445'),(11,'auth','0006_require_contenttypes_0002','2025-04-21 03:58:31.313761'),(12,'auth','0007_alter_validators_add_error_messages','2025-04-21 03:58:31.325381'),(13,'auth','0008_alter_user_username_max_length','2025-04-21 03:58:31.382509'),(14,'auth','0009_alter_user_last_name_max_length','2025-04-21 03:58:31.449477'),(15,'auth','0010_alter_group_name_max_length','2025-04-21 03:58:31.478503'),(16,'auth','0011_update_proxy_permissions','2025-04-21 03:58:31.487497'),(17,'auth','0012_alter_user_first_name_max_length','2025-04-21 03:58:31.550589'),(18,'sessions','0001_initial','2025-04-21 03:58:31.593713'),(19,'zhkt','0001_initial','2025-04-21 08:43:33.309008');
/*!40000 ALTER TABLE `django_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_session`
--

DROP TABLE IF EXISTS `django_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_session` (
  `session_key` varchar(40) COLLATE utf8mb4_bin NOT NULL,
  `session_data` longtext COLLATE utf8mb4_bin NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`),
  KEY `django_session_expire_date_a5c62663` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_session`
--

LOCK TABLES `django_session` WRITE;
/*!40000 ALTER TABLE `django_session` DISABLE KEYS */;
/*!40000 ALTER TABLE `django_session` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_admin`
--

DROP TABLE IF EXISTS `zhkt_admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `admin_id` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `department` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `role` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `user_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_id` (`admin_id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `zhkt_admin_user_id_372532cd_fk_zhkt_user_id` FOREIGN KEY (`user_id`) REFERENCES `zhkt_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_admin`
--

LOCK TABLES `zhkt_admin` WRITE;
/*!40000 ALTER TABLE `zhkt_admin` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_admin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_aichat`
--

DROP TABLE IF EXISTS `zhkt_aichat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_aichat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_aichat_user_id_3f4e5d89_fk_zhkt_user_id` (`user_id`),
  CONSTRAINT `zhkt_aichat_user_id_3f4e5d89_fk_zhkt_user_id` FOREIGN KEY (`user_id`) REFERENCES `zhkt_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_aichat`
--

LOCK TABLES `zhkt_aichat` WRITE;
/*!40000 ALTER TABLE `zhkt_aichat` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_aichat` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_aichatmessage`
--

DROP TABLE IF EXISTS `zhkt_aichatmessage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_aichatmessage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `content` longtext COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `chat_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_aichatmessage_chat_id_84014e3c_fk_zhkt_aichat_id` (`chat_id`),
  CONSTRAINT `zhkt_aichatmessage_chat_id_84014e3c_fk_zhkt_aichat_id` FOREIGN KEY (`chat_id`) REFERENCES `zhkt_aichat` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_aichatmessage`
--

LOCK TABLES `zhkt_aichatmessage` WRITE;
/*!40000 ALTER TABLE `zhkt_aichatmessage` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_aichatmessage` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_aiprompt`
--

DROP TABLE IF EXISTS `zhkt_aiprompt`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_aiprompt` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `content` longtext COLLATE utf8mb4_bin NOT NULL,
  `prompt_type` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_aiprompt`
--

LOCK TABLES `zhkt_aiprompt` WRITE;
/*!40000 ALTER TABLE `zhkt_aiprompt` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_aiprompt` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_chapter`
--

DROP TABLE IF EXISTS `zhkt_chapter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_chapter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `order` int(11) NOT NULL,
  `video_url` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `duration` int(11) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `course_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_chapter_course_id_547feeda_fk_zhkt_course_id` (`course_id`),
  CONSTRAINT `zhkt_chapter_course_id_547feeda_fk_zhkt_course_id` FOREIGN KEY (`course_id`) REFERENCES `zhkt_course` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_chapter`
--

LOCK TABLES `zhkt_chapter` WRITE;
/*!40000 ALTER TABLE `zhkt_chapter` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_chapter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_classgroup`
--

DROP TABLE IF EXISTS `zhkt_classgroup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_classgroup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `code` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `grade` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `head_teacher_id` bigint(20) DEFAULT NULL,
  `major_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `zhkt_classgroup_head_teacher_id_3cda0108_fk_zhkt_teacher_id` (`head_teacher_id`),
  KEY `zhkt_classgroup_major_id_cc929577_fk_zhkt_major_id` (`major_id`),
  CONSTRAINT `zhkt_classgroup_head_teacher_id_3cda0108_fk_zhkt_teacher_id` FOREIGN KEY (`head_teacher_id`) REFERENCES `zhkt_teacher` (`id`),
  CONSTRAINT `zhkt_classgroup_major_id_cc929577_fk_zhkt_major_id` FOREIGN KEY (`major_id`) REFERENCES `zhkt_major` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_classgroup`
--

LOCK TABLES `zhkt_classgroup` WRITE;
/*!40000 ALTER TABLE `zhkt_classgroup` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_classgroup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_college`
--

DROP TABLE IF EXISTS `zhkt_college`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_college` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `code` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_college`
--

LOCK TABLES `zhkt_college` WRITE;
/*!40000 ALTER TABLE `zhkt_college` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_college` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_course`
--

DROP TABLE IF EXISTS `zhkt_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_course` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `code` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `course_type` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `credits` decimal(3,1) NOT NULL,
  `total_hours` int(11) NOT NULL,
  `cover_image` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `is_published` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `major_id` bigint(20) NOT NULL,
  `teacher_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `zhkt_course_major_id_9b914158_fk_zhkt_major_id` (`major_id`),
  KEY `zhkt_course_teacher_id_63a0fa98_fk_zhkt_teacher_id` (`teacher_id`),
  CONSTRAINT `zhkt_course_major_id_9b914158_fk_zhkt_major_id` FOREIGN KEY (`major_id`) REFERENCES `zhkt_major` (`id`),
  CONSTRAINT `zhkt_course_teacher_id_63a0fa98_fk_zhkt_teacher_id` FOREIGN KEY (`teacher_id`) REFERENCES `zhkt_teacher` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_course`
--

LOCK TABLES `zhkt_course` WRITE;
/*!40000 ALTER TABLE `zhkt_course` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_course` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_courseenrollment`
--

DROP TABLE IF EXISTS `zhkt_courseenrollment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_courseenrollment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `enrolled_at` datetime(6) NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `progress` double NOT NULL,
  `last_accessed` datetime(6) NOT NULL,
  `course_id` bigint(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhkt_courseenrollment_student_id_course_id_8d13654d_uniq` (`student_id`,`course_id`),
  KEY `zhkt_courseenrollment_course_id_c5cc0b55_fk_zhkt_course_id` (`course_id`),
  CONSTRAINT `zhkt_courseenrollment_course_id_c5cc0b55_fk_zhkt_course_id` FOREIGN KEY (`course_id`) REFERENCES `zhkt_course` (`id`),
  CONSTRAINT `zhkt_courseenrollment_student_id_ff5b1663_fk_zhkt_student_id` FOREIGN KEY (`student_id`) REFERENCES `zhkt_student` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_courseenrollment`
--

LOCK TABLES `zhkt_courseenrollment` WRITE;
/*!40000 ALTER TABLE `zhkt_courseenrollment` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_courseenrollment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_feedback`
--

DROP TABLE IF EXISTS `zhkt_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_feedback` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `content` longtext COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `submission_id` bigint(20) NOT NULL,
  `teacher_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_feedback_submission_id_d8f44cea_fk_zhkt_submission_id` (`submission_id`),
  KEY `zhkt_feedback_teacher_id_a345547f_fk_zhkt_teacher_id` (`teacher_id`),
  CONSTRAINT `zhkt_feedback_submission_id_d8f44cea_fk_zhkt_submission_id` FOREIGN KEY (`submission_id`) REFERENCES `zhkt_submission` (`id`),
  CONSTRAINT `zhkt_feedback_teacher_id_a345547f_fk_zhkt_teacher_id` FOREIGN KEY (`teacher_id`) REFERENCES `zhkt_teacher` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_feedback`
--

LOCK TABLES `zhkt_feedback` WRITE;
/*!40000 ALTER TABLE `zhkt_feedback` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_feedback` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_homework`
--

DROP TABLE IF EXISTS `zhkt_homework`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_homework` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `start_time` datetime(6) NOT NULL,
  `end_time` datetime(6) NOT NULL,
  `total_score` int(11) NOT NULL,
  `is_online` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `course_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_homework_course_id_54c6ab25_fk_zhkt_course_id` (`course_id`),
  CONSTRAINT `zhkt_homework_course_id_54c6ab25_fk_zhkt_course_id` FOREIGN KEY (`course_id`) REFERENCES `zhkt_course` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_homework`
--

LOCK TABLES `zhkt_homework` WRITE;
/*!40000 ALTER TABLE `zhkt_homework` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_homework` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_knowledgebase`
--

DROP TABLE IF EXISTS `zhkt_knowledgebase`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_knowledgebase` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `is_public` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `course_id` bigint(20) NOT NULL,
  `created_by_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_knowledgebase_created_by_id_750843cb_fk_zhkt_teacher_id` (`created_by_id`),
  KEY `zhkt_knowledgebase_course_id_6ebdb84b_fk_zhkt_course_id` (`course_id`),
  CONSTRAINT `zhkt_knowledgebase_course_id_6ebdb84b_fk_zhkt_course_id` FOREIGN KEY (`course_id`) REFERENCES `zhkt_course` (`id`),
  CONSTRAINT `zhkt_knowledgebase_created_by_id_750843cb_fk_zhkt_teacher_id` FOREIGN KEY (`created_by_id`) REFERENCES `zhkt_teacher` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_knowledgebase`
--

LOCK TABLES `zhkt_knowledgebase` WRITE;
/*!40000 ALTER TABLE `zhkt_knowledgebase` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_knowledgebase` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_knowledgepoint`
--

DROP TABLE IF EXISTS `zhkt_knowledgepoint`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_knowledgepoint` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `content` longtext COLLATE utf8mb4_bin NOT NULL,
  `order` int(11) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `knowledge_base_id` bigint(20) NOT NULL,
  `parent_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_knowledgepoint_knowledge_base_id_05acedfe_fk_zhkt_know` (`knowledge_base_id`),
  KEY `zhkt_knowledgepoint_parent_id_15ac1b6d_fk_zhkt_knowledgepoint_id` (`parent_id`),
  CONSTRAINT `zhkt_knowledgepoint_knowledge_base_id_05acedfe_fk_zhkt_know` FOREIGN KEY (`knowledge_base_id`) REFERENCES `zhkt_knowledgebase` (`id`),
  CONSTRAINT `zhkt_knowledgepoint_parent_id_15ac1b6d_fk_zhkt_knowledgepoint_id` FOREIGN KEY (`parent_id`) REFERENCES `zhkt_knowledgepoint` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_knowledgepoint`
--

LOCK TABLES `zhkt_knowledgepoint` WRITE;
/*!40000 ALTER TABLE `zhkt_knowledgepoint` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_knowledgepoint` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_major`
--

DROP TABLE IF EXISTS `zhkt_major`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_major` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `code` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `duration` int(11) NOT NULL,
  `degree` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `college_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `zhkt_major_college_id_e49a1518_fk_zhkt_college_id` (`college_id`),
  CONSTRAINT `zhkt_major_college_id_e49a1518_fk_zhkt_college_id` FOREIGN KEY (`college_id`) REFERENCES `zhkt_college` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_major`
--

LOCK TABLES `zhkt_major` WRITE;
/*!40000 ALTER TABLE `zhkt_major` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_major` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_note`
--

DROP TABLE IF EXISTS `zhkt_note`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_note` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `content` longtext COLLATE utf8mb4_bin NOT NULL,
  `is_public` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `knowledge_point_id` bigint(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_note_knowledge_point_id_26cfee20_fk_zhkt_knowledgepoint_id` (`knowledge_point_id`),
  KEY `zhkt_note_student_id_f5c35e45_fk_zhkt_student_id` (`student_id`),
  CONSTRAINT `zhkt_note_knowledge_point_id_26cfee20_fk_zhkt_knowledgepoint_id` FOREIGN KEY (`knowledge_point_id`) REFERENCES `zhkt_knowledgepoint` (`id`),
  CONSTRAINT `zhkt_note_student_id_f5c35e45_fk_zhkt_student_id` FOREIGN KEY (`student_id`) REFERENCES `zhkt_student` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_note`
--

LOCK TABLES `zhkt_note` WRITE;
/*!40000 ALTER TABLE `zhkt_note` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_note` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_order`
--

DROP TABLE IF EXISTS `zhkt_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `points_cost` int(11) NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `product_id` bigint(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_order_product_id_4512422a_fk_zhkt_product_id` (`product_id`),
  KEY `zhkt_order_student_id_57bbcdb7_fk_zhkt_student_id` (`student_id`),
  CONSTRAINT `zhkt_order_product_id_4512422a_fk_zhkt_product_id` FOREIGN KEY (`product_id`) REFERENCES `zhkt_product` (`id`),
  CONSTRAINT `zhkt_order_student_id_57bbcdb7_fk_zhkt_student_id` FOREIGN KEY (`student_id`) REFERENCES `zhkt_student` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_order`
--

LOCK TABLES `zhkt_order` WRITE;
/*!40000 ALTER TABLE `zhkt_order` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_pointsrecord`
--

DROP TABLE IF EXISTS `zhkt_pointsrecord`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_pointsrecord` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `points` int(11) NOT NULL,
  `record_type` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_pointsrecord_student_id_64b40398_fk_zhkt_student_id` (`student_id`),
  CONSTRAINT `zhkt_pointsrecord_student_id_64b40398_fk_zhkt_student_id` FOREIGN KEY (`student_id`) REFERENCES `zhkt_student` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_pointsrecord`
--

LOCK TABLES `zhkt_pointsrecord` WRITE;
/*!40000 ALTER TABLE `zhkt_pointsrecord` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_pointsrecord` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_product`
--

DROP TABLE IF EXISTS `zhkt_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `points_price` int(11) NOT NULL,
  `stock` int(11) NOT NULL,
  `image` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_product`
--

LOCK TABLES `zhkt_product` WRITE;
/*!40000 ALTER TABLE `zhkt_product` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_resource`
--

DROP TABLE IF EXISTS `zhkt_resource`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_bin NOT NULL,
  `resource_type` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `file` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `chapter_id` bigint(20) DEFAULT NULL,
  `course_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zhkt_resource_chapter_id_4c8cdb37_fk_zhkt_chapter_id` (`chapter_id`),
  KEY `zhkt_resource_course_id_6cde30ff_fk_zhkt_course_id` (`course_id`),
  CONSTRAINT `zhkt_resource_chapter_id_4c8cdb37_fk_zhkt_chapter_id` FOREIGN KEY (`chapter_id`) REFERENCES `zhkt_chapter` (`id`),
  CONSTRAINT `zhkt_resource_course_id_6cde30ff_fk_zhkt_course_id` FOREIGN KEY (`course_id`) REFERENCES `zhkt_course` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_resource`
--

LOCK TABLES `zhkt_resource` WRITE;
/*!40000 ALTER TABLE `zhkt_resource` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_resource` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_role`
--

DROP TABLE IF EXISTS `zhkt_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `description` longtext COLLATE utf8mb4_bin,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_role`
--

LOCK TABLES `zhkt_role` WRITE;
/*!40000 ALTER TABLE `zhkt_role` DISABLE KEYS */;
INSERT INTO `zhkt_role` VALUES (1,'超级管理员','admin','超级管理员','2025-04-22 10:34:29.000000','2025-04-22 10:34:29.000000');
/*!40000 ALTER TABLE `zhkt_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_role_permissions`
--

DROP TABLE IF EXISTS `zhkt_role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_role_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhkt_role_permissions_role_id_permission_id_f3e5fb11_uniq` (`role_id`,`permission_id`),
  KEY `zhkt_role_permission_permission_id_5022937b_fk_auth_perm` (`permission_id`),
  CONSTRAINT `zhkt_role_permission_permission_id_5022937b_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `zhkt_role_permissions_role_id_9e3677e2_fk_zhkt_role_id` FOREIGN KEY (`role_id`) REFERENCES `zhkt_role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_role_permissions`
--

LOCK TABLES `zhkt_role_permissions` WRITE;
/*!40000 ALTER TABLE `zhkt_role_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_student`
--

DROP TABLE IF EXISTS `zhkt_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_student` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `student_id` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `enrollment_date` date NOT NULL,
  `expected_graduation_date` date NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `points` int(11) NOT NULL,
  `class_group_id` bigint(20) DEFAULT NULL,
  `college_id` bigint(20) DEFAULT NULL,
  `major_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `zhkt_student_class_group_id_644059a7_fk_zhkt_classgroup_id` (`class_group_id`),
  KEY `zhkt_student_college_id_125d70e0_fk_zhkt_college_id` (`college_id`),
  KEY `zhkt_student_major_id_3b2dff14_fk_zhkt_major_id` (`major_id`),
  CONSTRAINT `zhkt_student_class_group_id_644059a7_fk_zhkt_classgroup_id` FOREIGN KEY (`class_group_id`) REFERENCES `zhkt_classgroup` (`id`),
  CONSTRAINT `zhkt_student_college_id_125d70e0_fk_zhkt_college_id` FOREIGN KEY (`college_id`) REFERENCES `zhkt_college` (`id`),
  CONSTRAINT `zhkt_student_major_id_3b2dff14_fk_zhkt_major_id` FOREIGN KEY (`major_id`) REFERENCES `zhkt_major` (`id`),
  CONSTRAINT `zhkt_student_user_id_a85d90ac_fk_zhkt_user_id` FOREIGN KEY (`user_id`) REFERENCES `zhkt_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_student`
--

LOCK TABLES `zhkt_student` WRITE;
/*!40000 ALTER TABLE `zhkt_student` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_student` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_submission`
--

DROP TABLE IF EXISTS `zhkt_submission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_submission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `content` longtext COLLATE utf8mb4_bin NOT NULL,
  `file` varchar(100) COLLATE utf8mb4_bin NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `submitted_at` datetime(6) DEFAULT NULL,
  `score` decimal(5,2) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `homework_id` bigint(20) NOT NULL,
  `student_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhkt_submission_homework_id_student_id_315592f8_uniq` (`homework_id`,`student_id`),
  KEY `zhkt_submission_student_id_2aa650de_fk_zhkt_student_id` (`student_id`),
  CONSTRAINT `zhkt_submission_homework_id_3a855a97_fk_zhkt_homework_id` FOREIGN KEY (`homework_id`) REFERENCES `zhkt_homework` (`id`),
  CONSTRAINT `zhkt_submission_student_id_2aa650de_fk_zhkt_student_id` FOREIGN KEY (`student_id`) REFERENCES `zhkt_student` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_submission`
--

LOCK TABLES `zhkt_submission` WRITE;
/*!40000 ALTER TABLE `zhkt_submission` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_submission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_teacher`
--

DROP TABLE IF EXISTS `zhkt_teacher`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_teacher` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `teacher_id` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `title` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `introduction` longtext COLLATE utf8mb4_bin NOT NULL,
  `college_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `zhkt_teacher_college_id_c24ae561_fk_zhkt_college_id` (`college_id`),
  CONSTRAINT `zhkt_teacher_college_id_c24ae561_fk_zhkt_college_id` FOREIGN KEY (`college_id`) REFERENCES `zhkt_college` (`id`),
  CONSTRAINT `zhkt_teacher_user_id_4707879a_fk_zhkt_user_id` FOREIGN KEY (`user_id`) REFERENCES `zhkt_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_teacher`
--

LOCK TABLES `zhkt_teacher` WRITE;
/*!40000 ALTER TABLE `zhkt_teacher` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_teacher` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_teacher_majors`
--

DROP TABLE IF EXISTS `zhkt_teacher_majors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_teacher_majors` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `teacher_id` bigint(20) NOT NULL,
  `major_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhkt_teacher_majors_teacher_id_major_id_ae6cac4a_uniq` (`teacher_id`,`major_id`),
  KEY `zhkt_teacher_majors_major_id_66f83345_fk_zhkt_major_id` (`major_id`),
  CONSTRAINT `zhkt_teacher_majors_major_id_66f83345_fk_zhkt_major_id` FOREIGN KEY (`major_id`) REFERENCES `zhkt_major` (`id`),
  CONSTRAINT `zhkt_teacher_majors_teacher_id_0fe17653_fk_zhkt_teacher_id` FOREIGN KEY (`teacher_id`) REFERENCES `zhkt_teacher` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_teacher_majors`
--

LOCK TABLES `zhkt_teacher_majors` WRITE;
/*!40000 ALTER TABLE `zhkt_teacher_majors` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_teacher_majors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_user`
--

DROP TABLE IF EXISTS `zhkt_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `password` varchar(128) COLLATE utf8mb4_bin NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `first_name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `last_name` varchar(150) COLLATE utf8mb4_bin NOT NULL,
  `email` varchar(254) COLLATE utf8mb4_bin NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  `phone` varchar(11) COLLATE utf8mb4_bin DEFAULT NULL,
  `gender` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL,
  `avatar` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_user`
--

LOCK TABLES `zhkt_user` WRITE;
/*!40000 ALTER TABLE `zhkt_user` DISABLE KEYS */;
INSERT INTO `zhkt_user` VALUES (3,'pbkdf2_sha256$260000$wLzMwuVVKxwyLI2Yz9E34o$23/k4rBH733Piiut2KNOOD6ALVEVsDdFHblljy0wJOY=',NULL,0,'admin','kt','zh','<EMAIL>',0,1,'2025-04-22 02:49:52.180241','1688888888','男','');
/*!40000 ALTER TABLE `zhkt_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_user_groups`
--

DROP TABLE IF EXISTS `zhkt_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_user_groups` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhkt_user_groups_user_id_group_id_b95ce0a1_uniq` (`user_id`,`group_id`),
  KEY `zhkt_user_groups_group_id_9532e3a0_fk_auth_group_id` (`group_id`),
  CONSTRAINT `zhkt_user_groups_group_id_9532e3a0_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `zhkt_user_groups_user_id_9feb4c7f_fk_zhkt_user_id` FOREIGN KEY (`user_id`) REFERENCES `zhkt_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_user_groups`
--

LOCK TABLES `zhkt_user_groups` WRITE;
/*!40000 ALTER TABLE `zhkt_user_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_user_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_user_roles`
--

DROP TABLE IF EXISTS `zhkt_user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_user_roles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `role_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhkt_user_roles_user_id_role_id_3569eeaf_uniq` (`user_id`,`role_id`),
  KEY `zhkt_user_roles_role_id_2e8ab2f1_fk_zhkt_role_id` (`role_id`),
  CONSTRAINT `zhkt_user_roles_role_id_2e8ab2f1_fk_zhkt_role_id` FOREIGN KEY (`role_id`) REFERENCES `zhkt_role` (`id`),
  CONSTRAINT `zhkt_user_roles_user_id_e7ac2fb9_fk_zhkt_user_id` FOREIGN KEY (`user_id`) REFERENCES `zhkt_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_user_roles`
--

LOCK TABLES `zhkt_user_roles` WRITE;
/*!40000 ALTER TABLE `zhkt_user_roles` DISABLE KEYS */;
INSERT INTO `zhkt_user_roles` VALUES (3,3,1);
/*!40000 ALTER TABLE `zhkt_user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhkt_user_user_permissions`
--

DROP TABLE IF EXISTS `zhkt_user_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zhkt_user_user_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhkt_user_user_permissions_user_id_permission_id_4b828301_uniq` (`user_id`,`permission_id`),
  KEY `zhkt_user_user_permi_permission_id_75b76364_fk_auth_perm` (`permission_id`),
  CONSTRAINT `zhkt_user_user_permi_permission_id_75b76364_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `zhkt_user_user_permissions_user_id_a7b821c1_fk_zhkt_user_id` FOREIGN KEY (`user_id`) REFERENCES `zhkt_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhkt_user_user_permissions`
--

LOCK TABLES `zhkt_user_user_permissions` WRITE;
/*!40000 ALTER TABLE `zhkt_user_user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `zhkt_user_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-04-22 13:39:30
