from django.db import models
from django.utils.translation import gettext_lazy as _

class AIChat(models.Model):
    """AI对话模型"""
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='ai_chats')
    title = models.CharField(_('对话标题'), max_length=200)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('AI对话')
        verbose_name_plural = _('AI对话')

    def __str__(self):
        return f"{self.user.username} - {self.title}"

class AIChatMessage(models.Model):
    """AI对话消息模型"""
    ROLE_CHOICES = (
        ('USER', '用户'),
        ('ASSISTANT', '助手'),
        ('SYSTEM', '系统'),
    )
    
    chat = models.ForeignKey(AIChat, on_delete=models.CASCADE, related_name='messages')
    role = models.CharField(_('角色'), max_length=20, choices=ROLE_CHOICES)
    # content = models.TextField(_('消息内容'))
    content = models.TextField(_('消息内容'), db_collation='utf8mb4_unicode_ci')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('AI对话消息')
        verbose_name_plural = _('AI对话消息')
        ordering = ['created_at']

    def __str__(self):
        return f"{self.chat.title} - {self.role} - {self.created_at}"

class AIPrompt(models.Model):
    """AI提示词模型"""
    PROMPT_TYPE_CHOICES = (
        ('GENERAL', '通用'),
        ('COURSE', '课程'),
        ('HOMEWORK', '作业'),
        ('KNOWLEDGE', '知识'),
    )
    
    title = models.CharField(_('提示词标题'), max_length=200)
    content = models.TextField(_('提示词内容'))
    prompt_type = models.CharField(_('提示词类型'), max_length=20, choices=PROMPT_TYPE_CHOICES)
    is_active = models.BooleanField(_('是否启用'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('AI提示词')
        verbose_name_plural = _('AI提示词')

    def __str__(self):
        return self.title 