import { get, post } from '@/utils/request'
import { useAuthStore } from '@/stores/auth'

/**
 * 获取视频的所有预设对话
 * @param {string} videoId 视频ID
 * @returns {Promise} 包含视频所有对话的Promise
 */
export function getVideoDialogues(videoId) {
  return get(`/video-assistant/${videoId}/dialogues/`).then(response => {
    // 适配Django REST Framework的响应格式
    return {
      code: response.code || 200,
      message: response.message || "获取视频对话成功",
      data: response.data
    };
  });
}

/**
 * 获取视频特定时间点的对话
 * @param {string} videoId 视频ID
 * @param {string} currentTime 当前视频时间，格式为"MM:SS"
 * @param {string} sessionId 会话ID（可选）
 * @returns {Promise} 包含当前时间点对话的Promise
 */
export function getDialoguesAtTimestamp(videoId, currentTime, sessionId) {
  const params = { current_time: currentTime };
  if (sessionId) {
    params.session_id = sessionId;
  }
  
  return get(`/video-assistant/${videoId}/dialogues/at-timestamp/`, { params }).then(response => {
    return {
      code: response.code || 200,
      message: response.message || "获取时间点对话成功",
      data: response.data
    };
  });
}

// 用于模拟会话跟踪的缓存 - 前端不再需要维护
// const sessionDialoguesTracking = {};

/**
 * 获取视频特定时间点的新对话（之前未显示过的）
 * @param {string} videoId 视频ID
 * @param {string} currentTime 当前视频时间，格式为"MM:SS"
 * @param {string} sessionId 会话ID
 * @returns {Promise} 包含当前时间点新对话的Promise
 */
export function getNewDialoguesAtTimestamp(videoId, currentTime, sessionId) {
  const params = {
    current_time: currentTime,
    session_id: sessionId
  };
  
  return get(`/video-assistant/${videoId}/dialogues/new-at-timestamp/`, { params }).then(response => {
    return {
      code: response.code || 200,
      message: response.message || "获取新对话成功",
      data: response.data
    };
  }).catch(error => {
    console.error('获取新对话失败:', error);
    return {
      code: error.response?.data?.code || 500,
      message: error.response?.data?.message || '获取新对话失败',
      data: {
        dialogues: [],
        has_new_dialogues: false
      }
    };
  });
}

/**
 * 重置会话，清除已显示对话的记录
 * @param {string} sessionId 会话ID
 * @returns {Promise} 重置结果的Promise
 */
export function resetSession(sessionId) {
  return post(`/video-assistant/session/${sessionId}/reset/`).then(response => {
    return {
      code: response.code || 200,
      message: response.message || `会话 ${sessionId} 已重置`,
      data: response.data
    };
  });
}

/**
 * 检查特定时间点是否有对话
 * @param {string} videoId 视频ID
 * @param {string} currentTime 当前视频时间，格式为"MM:SS"
 * @returns {Promise<boolean>} 返回是否有对话的Promise
 */
export async function hasDialoguesAtTimestamp(videoId, currentTime) {
  try {
    const params = { current_time: currentTime };
    const response = await get(`/video-assistant/${videoId}/dialogues/check/`, { params });
    
    // 处理Django REST Framework格式的响应
    return response?.data?.has_dialogues || false;
  } catch (error) {
    console.error('检查时间点对话失败:', error);
    return false;
  }
}

/**
 * 生成唯一的会话ID
 * @returns {Promise<string>} 唯一的会话ID的Promise
 */
export async function generateSessionId() {
  // 直接在前端生成会话ID，不通过后端
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 辅助函数：将时间字符串转换为秒数
 * @param {string} timeStr 时间字符串，格式为"MM:SS"
 * @returns {number} 时间秒数
 */
function timeToSeconds(timeStr) {
  try {
    const [minutes, seconds] = timeStr.split(':').map(Number);
    return minutes * 60 + seconds;
  } catch (e) {
    return 0;
  }
}

/**
 * 获取视频的导航数据（关键点和摘要）
 * @param {string} videoId 视频ID
 * @returns {Promise} 包含视频导航数据的Promise
 */
export function getVideoNavigation(videoId) {
  return get(`/video-assistant/${videoId}/navigation/`).then(response => {
    return {
      code: response.code || 200,
      message: response.message || "获取视频导航数据成功",
      data: response.data
    };
  });
}

/**
 * 获取视频关联的教师信息
 * @param {string} videoId 视频ID
 * @returns {Promise} 包含视频教师信息的Promise
 */
export function getVideoTeacherInfo(videoId) {
  return get(`/video-assistant/${videoId}/teacher-info/`).then(response => {
    return {
      code: response.code || 200,
      message: response.message || "获取视频教师信息成功",
      data: response.data
    };
  });
}

/**
 * 获取流式AI对话URL
 * @returns {string} 流式对话接口URL
 */
export function getChatStreamUrl() {
  return `/api/video-assistant/chat-stream/`;
}

/**
 * 与AI助手进行流式对话
 * @param {string} message 用户消息
 * @param {Array} history 对话历史(可选)
 * @param {string} videoId 视频ID(可选)，用于后端获取字幕作为上下文
 * @param {string} currentVideoTime 当前视频播放时间(可选)，格式为"MM:SS"
 * @returns {Promise<Response>} fetch API响应
 */
export function chatWithAIStream(message, history = [], videoId = null,  currentVideoTime = null) {
  const authStore = useAuthStore();
  const url = `/api/video-assistant/chat-stream/`;
  
  return fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${authStore.token}`,
      'Content-Type': 'application/json',
      'Accept': '*/*'
    },
    body: JSON.stringify({ 
      message, 
      history, 
      video_id: videoId,
      current_video_time: currentVideoTime
    }),
    cache: 'no-cache',
    credentials: 'same-origin',
    mode: 'cors'
  }).then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response;
  }).catch(error => {
    console.error('流式对话请求失败:', error);
    throw error;
  });
} 