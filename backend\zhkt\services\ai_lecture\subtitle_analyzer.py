# -*- coding: utf-8 -*-
"""
字幕分析服务
提供语音转录、时间戳提取、页面分割建议等功能
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional

import requests

from .exceptions import AILectureException
from ...config import AI_EDUCATION_TOOL_URL  # Import the API URL from config
from ...utils.gemini_utils import GeminiDialogueGenerator
from ...utils.temp_file_utils import clean_temp_file, clean_temp_dir, create_temp_subdir

logger = logging.getLogger(__name__)


class SubtitleAnalyzer:
    """
    字幕分析服务类
    负责音频转录、字幕生成、页面分割建议等功能
    """
    
    def __init__(self, gemini_api_key: str = "AIzaSyC-ibugmJLJik8xyztgiWM9_OFJKTUyBl8"):
        """
        初始化字幕分析器
        
        :param gemini_api_key: Gemini API密钥
        """
        self.gemini_api_key = gemini_api_key
        # self._setup_environment()
        self._configure_gemini()
    
    # def _setup_environment(self):
    #     """设置环境变量和代理配置"""
    #     # 设置代理（如果需要）
    #     os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:10809'
    #     os.environ['HTTP_PROXY'] = 'http://127.0.0.1:10809'
    #
    #     # 设置Hugging Face缓存路径
    #     cache_path = r'E:\project\ai_model\Hugging\cache'
    #     os.environ['HF_HOME'] = cache_path
    #     os.environ['HUGGINGFACE_HUB_CACHE'] = cache_path
    #     os.environ['XDG_CACHE_HOME'] = cache_path
    
    def _configure_gemini(self):
        """配置Gemini API"""
        try:
            self.gemini_model = GeminiDialogueGenerator()
            logger.info("Gemini API配置成功")
        except Exception as e:
            logger.error(f"Gemini API配置失败: {str(e)}")
            raise AILectureException(f"Gemini API配置失败: {str(e)}")

    # def transcribe_audio_to_subtitles(self, audio_file_path: str, model_size: str = "large-v2") -> List[Dict[str, str]]:
    #     """
    #     将音频文件转录为带时间戳的字幕数据
    #
    #     :param audio_file_path: 音频文件路径
    #     :param model_size: Whisper模型大小，默认"large-v2"
    #     :return: 字幕数据列表，包含时间戳和文本
    #     """
    #     try:
    #         logger.info(f"开始转录音频文件: {audio_file_path}")
    #
    #         # 加载Whisper模型
    #         whisper_model = WhisperModel(model_size, device="cpu", compute_type="float32")
    #
    #         # 转录音频文件
    #         segments, info = whisper_model.transcribe(
    #             audio_file_path,
    #             beam_size=5,
    #             log_progress=True,
    #             word_timestamps=True
    #         )
    #
    #         # 处理转录结果
    #         subtitle_data_list = []
    #         for segment in segments:
    #             start_time = segment.start
    #             end_time = segment.end
    #             text = segment.text.strip().replace('\n', ' ')
    #
    #             # 转换时间格式为 H:MM:SS.cs
    #             start_time_formatted = self._format_timestamp(start_time)
    #             end_time_formatted = self._format_timestamp(end_time)
    #
    #             subtitle_data_list.append({
    #                 "start_time_str": start_time_formatted,
    #                 "end_time_str": end_time_formatted,
    #                 "text": text,
    #                 "start_time_seconds": start_time,
    #                 "end_time_seconds": end_time
    #             })
    #
    #         logger.info(f"音频转录完成，共生成 {len(subtitle_data_list)} 条字幕")
    #         return subtitle_data_list
    #
    #     except Exception as e:
    #         logger.error(f"音频转录失败: {str(e)}")
    #         raise AILectureException(f"音频转录失败: {str(e)}")

    def transcribe_audio_to_subtitles(self, audio_file_path: str, model_size: str = "large-v2") -> List[Dict[str, str]]:
        """
        将音频文件转录为字幕
        
        :param audio_file_path: 音频文件路径
        :param model_size: Whisper模型大小
        :return: 字幕数据列表
        """
        temp_files = []
        
        try:
            logger.info(f"开始转录音频: {audio_file_path}")
            
            # 调用AI Education Tool API进行音频转录
            api_url = f"{AI_EDUCATION_TOOL_URL}/api/transcribe_audio"
            
            with open(audio_file_path, 'rb') as audio_file:
                files = {'file': (os.path.basename(audio_file_path), audio_file, 'audio/wav')}
                data = {'model': model_size}
                
                response = requests.post(api_url, files=files, data=data)
                response.raise_for_status()
                
                result = response.json()
                
                if result.get('status') != 'success':
                    error_msg = result.get('message', '未知错误')
                    raise AILectureException(f"音频转录失败: {error_msg}")
                
                subtitle_data = result.get('subtitles', [])
                
                # 处理字幕数据，添加秒级时间戳
                for item in subtitle_data:
                    # 解析开始和结束时间戳
                    start_time_str = item.get('start_time_str', '0:00:00.00')
                    end_time_str = item.get('end_time_str', '0:00:00.00')
                    
                    # 转换为秒
                    start_parts = start_time_str.split(':')
                    end_parts = end_time_str.split(':')
                    
                    if len(start_parts) == 3:
                        h, m, s = start_parts
                        s_parts = s.split('.')
                        seconds = int(h) * 3600 + int(m) * 60 + int(s_parts[0])
                        if len(s_parts) > 1:
                            seconds += int(s_parts[1]) / 100
                        item['start_time_seconds'] = seconds
                    
                    if len(end_parts) == 3:
                        h, m, s = end_parts
                        s_parts = s.split('.')
                        seconds = int(h) * 3600 + int(m) * 60 + int(s_parts[0])
                        if len(s_parts) > 1:
                            seconds += int(s_parts[1]) / 100
                        item['end_time_seconds'] = seconds
            
            logger.info(f"音频转录完成，生成了 {len(subtitle_data)} 条字幕")
            return subtitle_data
            
        except Exception as e:
            logger.error(f"音频转录失败: {str(e)}")
            raise AILectureException(f"音频转录失败: {str(e)}")
        finally:
            # 清理临时文件
            for temp_file in temp_files:
                if temp_file and os.path.exists(temp_file):
                    try:
                        clean_temp_file(temp_file)
                        logger.info(f"已清理临时文件: {temp_file}")
                    except Exception as cleanup_error:
                        logger.warning(f"清理临时文件失败: {cleanup_error}")
    
    def _format_timestamp(self, seconds: float) -> str:
        """
        将秒数转换为 H:MM:SS.cs 格式
        
        :param seconds: 秒数
        :return: 格式化的时间戳字符串
        """
        hours, remainder = divmod(seconds, 3600)
        minutes, secs = divmod(remainder, 60)
        centiseconds = int((secs % 1) * 100)
        
        return f"{int(hours)}:{int(minutes):02}:{int(secs):02}.{centiseconds:02}"
    
    def suggest_page_divisions_with_timestamps(self, original_content: str, subtitle_data: List[Dict[str, str]], language: str = 'zh') -> Optional[str]:
        """
        基于原始内容和字幕数据，建议HTML页面分割方案
        
        :param original_content: 原始章节内容文本
        :param subtitle_data: 带时间戳的字幕数据列表
        :param language: 语言代码，支持 'zh', 'en', 'vi', 'id'
        :return: Gemini API返回的页面分割建议（JSON格式字符串）
        """
        try:
            logger.info("开始生成页面分割建议")
            
            # 构建字幕文本
            subtitle_text = self._build_subtitle_text(subtitle_data)

            # 构建提示词
            prompt = self._build_page_division_prompt(original_content, subtitle_text, language)
            
            # 调用Gemini API
            page_divisions = self.gemini_model.generate_pro_content(prompt)
            logger.info("页面分割建议生成完成")

            return page_divisions
            
        except Exception as e:
            logger.error(f"生成页面分割建议失败: {str(e)}")
            return None
    
    def _build_subtitle_text(self, subtitle_data: List[Dict[str, str]]) -> str:
        """构建字幕文本供提示词使用"""
        subtitle_lines = []
        for item in subtitle_data:
            start_time = item.get("start_time_str", "")
            end_time = item.get("end_time_str", "")
            text = item.get("text", "")
            subtitle_lines.append(f"[{start_time} - {end_time}] {text}")
        
        return "\n".join(subtitle_lines)
    
    def _build_page_division_prompt(self, original_content: str, subtitle_text: str, language: str = 'zh') -> str:
        """根据语言构建页面分割提示词"""
        # 根据语言选择提示词模板
        if language == 'en':
            return f"""You are an expert educational content designer. Analyze the following content and subtitle data to suggest optimal page divisions for a PPT-style presentation.

## Original Content
{original_content}

## Subtitle Data
{subtitle_text}

## Task Description
Create logical page divisions for this content, following these rules:
1. Identify major topic transitions and create separate pages
2. Each page should be self-contained and focus on one key concept
3. Consider the timestamps in subtitles to align pages with audio segments
4. Create approximately 5-8 pages total, depending on content length and complexity
5. Add descriptive titles for each page
6. Identify 2-3 key points for each page

## Output Format
Return a JSON array of objects, each representing a page division:
```json
[
{{
    "page_number": 1,
    "page_title": "Introduction to the Topic",
    "content_summary": "Brief summary of what this page covers",
    "key_points": ["Key point 1", "Key point 2", "Key point 3"],
    "start_timestamp": "0:00:00.00", 
    "end_timestamp": "0:02:30.00"
  }},
  {{
    "page_number": 2,
    ...
  }}
]
```

Please analyze the content structure and timestamps carefully to suggest the most appropriate page divisions."""
        elif language == 'vi':
            return f"""Bạn là một chuyên gia thiết kế nội dung giáo dục. Hãy phân tích nội dung và dữ liệu phụ đề sau đây để đề xuất cách phân chia trang tối ưu cho một bài thuyết trình kiểu PPT.

## Nội dung gốc
{original_content}

## Dữ liệu phụ đề
{subtitle_text}

## Mô tả nhiệm vụ
Tạo các phân chia trang hợp lý cho nội dung này, tuân theo các quy tắc sau:
1. Xác định các chuyển đổi chủ đề chính và tạo các trang riêng biệt
2. Mỗi trang phải độc lập và tập trung vào một khái niệm chính
3. Xem xét thời gian trong phụ đề để điều chỉnh trang với các phân đoạn âm thanh
4. Tạo khoảng 5-8 trang tổng cộng, tùy thuộc vào độ dài và độ phức tạp của nội dung
5. Thêm tiêu đề mô tả cho mỗi trang
6. Xác định 2-3 điểm chính cho mỗi trang

## Định dạng đầu ra
Trả về một mảng JSON của các đối tượng, mỗi đối tượng đại diện cho một phân chia trang:
```json
[
    {{
      "page_number": 1,
    "page_title": "Giới thiệu về chủ đề",
    "content_summary": "Tóm tắt ngắn gọn về nội dung trang này",
    "key_points": ["Điểm chính 1", "Điểm chính 2", "Điểm chính 3"],
    "start_timestamp": "0:00:00.00", 
    "end_timestamp": "0:02:30.00"
    }},
    {{
      "page_number": 2,
    ...
  }}
]
```

Hãy phân tích cấu trúc nội dung và thời gian một cách cẩn thận để đề xuất cách phân chia trang phù hợp nhất."""
        elif language == 'id':
            return f"""Anda adalah perancang konten pendidikan ahli. Analisis konten berikut dan data subtitle untuk menyarankan pembagian halaman yang optimal untuk presentasi gaya PPT.

## Konten Asli
{original_content}

## Data Subtitle
{subtitle_text}

## Deskripsi Tugas
Buat pembagian halaman yang logis untuk konten ini, mengikuti aturan-aturan berikut:
1. Identifikasi transisi topik utama dan buat halaman terpisah
2. Setiap halaman harus mandiri dan fokus pada satu konsep kunci
3. Pertimbangkan timestamp dalam subtitle untuk menyelaraskan halaman dengan segmen audio
4. Buat sekitar 5-8 halaman total, tergantung pada panjang dan kompleksitas konten
5. Tambahkan judul deskriptif untuk setiap halaman
6. Identifikasi 2-3 poin kunci untuk setiap halaman

## Format Output
Kembalikan array JSON dari objek, masing-masing mewakili pembagian halaman:
```json
[
  {{
    "page_number": 1,
    "page_title": "Pengenalan Topik",
    "content_summary": "Ringkasan singkat tentang apa yang dibahas halaman ini",
    "key_points": ["Poin kunci 1", "Poin kunci 2", "Poin kunci 3"],
    "start_timestamp": "0:00:00.00", 
    "end_timestamp": "0:02:30.00"
  }},
  {{
    "page_number": 2,
    ...
  }}
]
```

Harap analisis struktur konten dan timestamp secara hati-hati untuk menyarankan pembagian halaman yang paling tepat."""
        else:  # 默认中文
            return f"""你是一位专业的教育内容设计师，需要为以下内容建议PPT风格的页面分割方案。

## 原始内容
{original_content}

## 字幕数据（带时间戳）
{subtitle_text}

## 任务描述
请为上述内容创建合理的页面分割建议，遵循以下规则：
1. 识别主要的话题转换，并创建单独的页面
2. 每个页面应该是自包含的，聚焦于一个关键概念
3. 考虑字幕中的时间戳，使页面与音频段落对齐
4. 根据内容长度和复杂度，总共创建约5-8个页面
5. 为每个页面添加描述性的标题
6. 为每个页面识别2-3个关键知识点

## 输出格式
返回一个JSON数组，每个对象表示一个页面分割：
```json
[
  {{
    "page_number": 1,
    "page_title": "话题引入",
    "content_summary": "简要总结此页面涵盖的内容",
    "key_points": ["关键点1", "关键点2", "关键点3"],
    "start_timestamp": "0:00:00.00", 
    "end_timestamp": "0:02:30.00"
  }},
  {{
    "page_number": 2,
    ...
  }}
]
```

请仔细分析内容结构和时间戳，给出最合适的页面分割建议。"""
    
    def analyze_chapter_content(self, audio_file_path: str, script_text, original_content: str, model_size: str = "large-v2", language: str = 'zh') -> Dict[str, Any]:
        """
        完整分析章节内容：转录音频 + 生成页面分割建议
        
        :param audio_file_path: 音频文件路径
        :param script_text: 脚本文本
        :param original_content: 原始章节内容
        :param model_size: Whisper模型大小
        :param language: 语言代码，支持 'zh', 'en', 'vi', 'id'
        :return: 包含字幕数据和页面分割建议的完整分析结果
        """
        temp_files = []
        temp_dir = None
        
        try:
            logger.info(f"开始完整分析章节内容: {audio_file_path} (语言: {language})")
            
            # 创建临时目录用于存储处理过程中的文件
            temp_dir = create_temp_subdir("subtitle_analysis")
            logger.info(f"创建临时目录用于字幕分析: {temp_dir}")

            # 1. 转录音频生成字幕
            subtitle_data = self.transcribe_audio_to_subtitles(audio_file_path, model_size)
            # 1.5 使用AI校准字幕内容
            subtitle_data = self._correct_subtitles_with_ai(subtitle_data, script_text)
            logger.info(f"已使用AI校准字幕，共 {len(subtitle_data)} 条")

            # 2. 生成页面分割建议
            page_suggestions = self.suggest_page_divisions_with_timestamps(original_content, subtitle_data, language)

            analysis_result = {
                "subtitle_data": subtitle_data,
                "page_suggestions": page_suggestions,
                "total_subtitles": len(subtitle_data),
                "total_duration": subtitle_data[-1]["end_time_seconds"] if subtitle_data else 0
            }
            
            logger.info("章节内容分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"章节内容分析失败: {str(e)}")
            raise AILectureException(f"章节内容分析失败: {str(e)}")
        finally:
            # 清理临时文件和目录
            for temp_file in temp_files:
                if temp_file and os.path.exists(temp_file):
                    try:
                        clean_temp_file(temp_file)
                        logger.info(f"已清理临时文件: {temp_file}")
                    except Exception as cleanup_error:
                        logger.warning(f"清理临时文件失败: {cleanup_error}")
            
            if temp_dir and os.path.exists(temp_dir):
                try:
                    clean_temp_dir(temp_dir)
                    logger.info(f"已清理临时目录: {temp_dir}")
                except Exception as cleanup_error:
                    logger.warning(f"清理临时目录失败: {cleanup_error}")
    
    def _correct_subtitles_with_ai(self, subtitle_data: List[Dict[str, Any]], script_text: str) -> List[Dict[str, Any]]:
        """
        使用Gemini AI校准字幕内容，保留时间戳
        
        :param subtitle_data: 原始字幕数据
        :param script_text: 上下文内容，用于辅助校准
        :return: 校准后的字幕数据
        """
        try:
            logger.info("开始使用AI校准字幕内容")
            
            # 如果字幕数据为空，直接返回
            if not subtitle_data:
                logger.warning("字幕数据为空，无法校准")
                return subtitle_data
            
            # 将字幕数据转换为JSON格式，方便AI处理
            subtitles_json = json.dumps(subtitle_data, ensure_ascii=False, indent=2)
            
            # 构建提示词，用于生成对话脚本
            prompt = f"""你是一个专业的字幕校正专家，需要校正以下字幕内容中可能存在的错误。

## 原始讲稿内容(正确的)
{script_text}

## 需要校正的字幕数据(可能有错别字)（JSON格式）
```json
{subtitles_json}
```

## 任务要求
1. 校正每个字幕条目中的"text"字段，修正可能的识别错误
2. 保持原有的时间戳和其他字段不变
3. 确保校正后的文本语义连贯、语法正确
4. 考虑上下文内容，使校正结果与原始章节内容保持一致

## 输出格式
请直接输出校正后的完整JSON数组，不要包含任何解释或额外的文本。
输出的JSON必须保持原有的结构和字段，只修改"text"字段的内容。
"""
            
            try:
                # ai校准字幕
                corrected_json_str = GeminiDialogueGenerator().generate_flash_content(prompt)

                # 提取JSON部分
                import re
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', corrected_json_str)
                if json_match:
                    corrected_json_str = json_match.group(1)
                else:
                    # 尝试直接解析，可能没有使用代码块格式
                    corrected_json_str = corrected_json_str.strip()
                
                # 解析校准后的字幕数据
                try:
                    corrected_subtitles = json.loads(corrected_json_str)
                    
                    # 验证校准结果的格式和数量
                    if (isinstance(corrected_subtitles, list) and 
                        len(corrected_subtitles) == len(subtitle_data) and
                        all('text' in item for item in corrected_subtitles)):
                        
                        # 记录校准前后的差异
                        changes_count = 0
                        for i, (orig, corr) in enumerate(zip(subtitle_data, corrected_subtitles)):
                            if orig['text'] != corr['text']:
                                changes_count += 1
                                logger.debug(f"字幕 #{i+1} 校准: '{orig['text']}' -> '{corr['text']}'")
                        
                        logger.info(f"字幕校准完成，共修正 {changes_count} 条字幕")
                        return corrected_subtitles
                    else:
                        logger.warning("AI校准结果格式不正确或数量不匹配，将使用原始字幕")
                        return subtitle_data
                except json.JSONDecodeError:
                    logger.warning("无法解析AI校准结果，将使用原始字幕")
                    return subtitle_data
            
            except Exception as e:
                logger.error(f"调用对话生成API失败: {str(e)}")
                # 如果对话生成失败，回退到使用Flash模型
                logger.info("回退到使用Gemini Flash模型校准字幕")
                corrected_json_str = self.gemini_model.generate_flash_content(prompt)
                
                # 提取JSON部分
                import re
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', corrected_json_str)
                if json_match:
                    corrected_json_str = json_match.group(1)
                else:
                    # 尝试直接解析，可能没有使用代码块格式
                    corrected_json_str = corrected_json_str.strip()
                
                # 解析校准后的字幕数据
                try:
                    corrected_subtitles = json.loads(corrected_json_str)
                    
                    # 验证校准结果的格式和数量
                    if (isinstance(corrected_subtitles, list) and 
                        len(corrected_subtitles) == len(subtitle_data) and
                        all('text' in item for item in corrected_subtitles)):
                        
                        # 记录校准前后的差异
                        changes_count = 0
                        for i, (orig, corr) in enumerate(zip(subtitle_data, corrected_subtitles)):
                            if orig['text'] != corr['text']:
                                changes_count += 1
                                logger.debug(f"字幕 #{i+1} 校准: '{orig['text']}' -> '{corr['text']}'")
                        
                        logger.info(f"字幕校准完成（使用Flash模型），共修正 {changes_count} 条字幕")
                        return corrected_subtitles
                    else:
                        logger.warning("AI校准结果格式不正确或数量不匹配，将使用原始字幕")
                        return subtitle_data
                except json.JSONDecodeError:
                    logger.warning("无法解析AI校准结果，将使用原始字幕")
                    return subtitle_data
                
        except Exception as e:
            logger.error(f"使用AI校准字幕失败: {str(e)}")
            # 出错时返回原始字幕
            return subtitle_data

    def generate_html_pages_for_chapter(self, chapter_title: str, original_content: str, page_suggestions_json: str, language: str = 'zh') -> List[Dict[str, Any]]:
        """
        根据页面分割建议生成HTML页面内容
        
        :param chapter_title: 章节标题
        :param original_content: 原始章节内容
        :param page_suggestions_json: 页面分割建议（JSON格式字符串）
        :param language: 语言代码，支持 'zh', 'en', 'vi', 'id'
        :return: 生成的HTML页面列表，每项包含页面标题、HTML内容等
        """
        try:
            logger.info(f"开始为章节 {chapter_title} 生成HTML页面 (语言: {language})")
            
            # 解析页面建议JSON
            import json
            try:
                # 提取JSON部分（防止Gemini返回带有额外文本的内容）
                import re
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', page_suggestions_json)
                if json_match:
                    page_suggestions_json = json_match.group(1)

                page_suggestions = json.loads(page_suggestions_json)
                
                if not isinstance(page_suggestions, list):
                    logger.warning("页面建议格式不正确，应为列表")
                    return []
                    
            except json.JSONDecodeError:
                logger.error(f"解析页面建议JSON失败: {page_suggestions_json}")
                # 尝试使用正则表达式提取JSON部分
                import re
                match = re.search(r'\[\s*{.*}\s*\]', page_suggestions_json, re.DOTALL)
                if match:
                    try:
                        page_suggestions_json = match.group(0)
                        page_suggestions = json.loads(page_suggestions_json)
                    except json.JSONDecodeError:
                        logger.error("二次解析页面建议JSON失败")
                        return []
                else:
                    return []
            
            # 使用线程池并行生成HTML页面
            html_pages = []
            import concurrent.futures
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                # 提交任务
                future_to_page = {
                    executor.submit(
                        self._generate_html_task, 
                        original_content, 
                        page_data,
                        language  # 传递语言参数
                    ): page_data 
                    for page_data in page_suggestions
                }

                # 获取结果
                for future in concurrent.futures.as_completed(future_to_page):
                    page_data = future_to_page[future]
                    try:
                        result = future.result()
                        html_pages.append(result)
                    except Exception as e:
                        page_num = page_data.get('page_number', '未知')
                        logger.error(f"生成页面 {page_num} 的HTML内容失败: {str(e)}")

            # 按页码排序
            html_pages.sort(key=lambda x: x.get('page_number', 0))

            logger.info(f"章节 {chapter_title} 的HTML页面生成完成，共 {len(html_pages)} 页")
            return html_pages
            
        except Exception as e:
            logger.error(f"生成章节 {chapter_title} 的HTML页面失败: {str(e)}")
            return []

    def _generate_html_task(self, original_content: str, page_data: Dict[str, Any], language: str = 'zh') -> Dict[str, Any]:
        """
        用于在线程中执行的HTML生成任务
        
        :param original_content: 原始内容
        :param page_data: 页面数据
        :param language: 语言代码，支持 'zh', 'en', 'vi', 'id'
        :return: 包含HTML内容的页面数据字典
        """
        page_number = page_data.get("page_number")
        page_title = page_data.get("title")
        content_summary = page_data.get("content_summary")
        start_timestamp = page_data.get("start_timestamp")
        end_timestamp = page_data.get("end_timestamp")
        key_points = page_data.get("key_points", [])
        
        # 获取当前页面的字幕数据
        page_subtitles = page_data.get("subtitles", [])
        
        # 调用Gemini生成HTML内容
        html_content = self._generate_html_content_for_page(
            page_title=page_title,
            content_summary=content_summary,
            key_points=key_points,
            original_content=original_content,
            subtitles=page_subtitles,  # 传入当前页面的字幕数据
            language=language  # 传递语言参数
        )
        
        return {
            "page_number": page_number,
            "page_title": page_title,
            "html_content": html_content,
            "start_timestamp": start_timestamp,
            "end_timestamp": end_timestamp,
            "key_points": key_points,
            "subtitles": page_subtitles  # 返回字幕数据，以便后续处理
        }
    
    def _generate_html_content_for_page(self, page_title: str,
                                       content_summary: str, key_points: List[str],
                                       original_content: str, subtitles: List[Dict[str, Any]],
                                       language: str = 'zh') -> str:
        """
        为单个页面生成HTML内容
        
        :param page_title: 页面标题
        :param content_summary: 内容摘要
        :param key_points: 关键知识点列表
        :param original_content: 原始章节内容
        :param subtitles: 当前页面的字幕数据
        :param language: 语言代码，支持 'zh', 'en', 'vi', 'id'
        :return: 生成的HTML内容
        """
        try:
            # 构建字幕文本
            subtitle_text = ""
            if subtitles:
                subtitle_lines = []
                for item in subtitles:
                    start_time_str = item.get('start_time_str', '')
                    end_time_str = item.get('end_time_str', '')
                    text = item.get('text', '')
                    subtitle_lines.append(f"[{start_time_str} - {end_time_str}] {text}")
                subtitle_text = "\n".join(subtitle_lines)
            
            # 根据语言选择提示词模板
            if language == 'en':
                prompt = f"""You are a professional educational content designer. Create a clean, minimalist PPT-style HTML page for the following educational content.

## Content Summary
{content_summary}

## Key Points
{', '.join(key_points)}

## Original Content Reference
{original_content}

## Subtitles for this page
{subtitle_text}

## Requirements
Please create a minimalist, elegant PPT-style HTML page with:
1. Strict size control: 1200px × 680px, content must not exceed this range
2. Modern, minimalist design style, similar to high-quality PPT
3. Include title, core content, and key points
4. Use clean layout, appropriate whitespace, and harmonious colors
5. Ensure content is clear, readable, with highlights on important points
6. Use inline CSS styles, no external dependencies
7. Page should be self-contained, displayable on its own
8. Avoid overcrowding, keep the page neat
9. 50px whitespace at top and bottom
10. Design based on subtitle content, ensuring page matches subtitles
11. Note this size constraint - only include essential content to avoid overflow
12. Page should be centered, with 10px rounded corners

## Output Format
Output the complete HTML code directly, without any explanation or additional text."""
            elif language == 'vi':
                prompt = f"""Bạn là một nhà thiết kế nội dung giáo dục chuyên nghiệp. Hãy tạo một trang HTML phong cách PPT đẹp mắt, đơn giản cho nội dung giảng dạy sau đây.

## Tóm tắt nội dung trang
{content_summary}

## Điểm kiến thức chính
{', '.join(key_points)}

## Tham khảo nội dung gốc
{original_content}

## Phụ đề cho trang này
{subtitle_text}

## Yêu cầu nhiệm vụ
Hãy tạo một trang HTML phong cách PPT đơn giản và đẹp mắt, yêu cầu:
1. Kiểm soát kích thước chặt chẽ: 1200px × 680px, nội dung không được vượt quá phạm vi này
2. Sử dụng phong cách thiết kế hiện đại, tối giản, tương tự như PPT chất lượng cao
3. Bao gồm tiêu đề, nội dung cốt lõi và điểm kiến thức chính
4. Sử dụng bố cục đơn giản, khoảng trắng phù hợp và phối màu hài hòa
5. Đảm bảo nội dung rõ ràng, dễ đọc, điểm nổi bật đáng chú ý
6. Sử dụng kiểu CSS nội tuyến, không cần phụ thuộc bên ngoài
7. Trang phải hoàn toàn độc lập, có thể hiển thị riêng
8. Tránh quá nhiều nội dung gây chật chội, giữ trang gọn gàng
9. Khoảng trắng 50px di bagian atas dan bawah
10. Desain berdasarkan konten subtitle, memastikan halaman cocok dengan subtitle
11. Lưu ý kích thước này, chỉ viết điểm chính, không để nội dung quá nhiều và bị tràn
12. Halaman harus dipusatkan, dengan sudut bulat 10px

## Định dạng đầu ra
Vui lòng xuất trực tiếp mã HTML hoàn chỉnh, không bao gồm bất kỳ giải thích hoặc văn bản bổ sung nào."""
            elif language == 'id':
                prompt = f"""Anda adalah desainer konten pendidikan profesional. Buatlah halaman HTML bergaya PPT yang bersih dan minimalis untuk konten pendidikan berikut.

## Ringkasan Konten
{content_summary}

## Poin-Poin Penting
{', '.join(key_points)}

## Referensi Konten Asli
{original_content}

## Subtitle untuk halaman ini
{subtitle_text}

## Persyaratan
Harap buat halaman HTML bergaya PPT yang minimalis dan elegan dengan:
1. Kontrol ukuran ketat: 1200px × 680px, konten tidak boleh melebihi rentang ini
2. Gaya desain modern, minimalis, mirip PPT berkualitas tinggi
3. Sertakan judul, konten inti, dan poin-poin kunci
4. Gunakan tata letak yang bersih, ruang putih yang tepat, dan warna yang harmonis
5. Pastikan konten jelas, mudah dibaca, dengan sorotan pada poin-poin penting
6. Gunakan gaya CSS inline, tanpa ketergantungan eksternal
7. Halaman harus mandiri, dapat ditampilkan sendiri
8. Hindari kepadatan, jaga halaman tetap rapi
9. Ruang putih 50px di bagian atas dan bawah
10. Desain berdasarkan konten subtitle, memastikan halaman cocok dengan subtitle
11. Perhatikan batasan ukuran ini - hanya sertakan konten penting untuk menghindari overflow
12. Halaman harus dipusatkan, dengan sudut bulat 10px

## Format Keluaran
Keluarkan kode HTML lengkap secara langsung, tanpa penjelasan atau teks tambahan."""
            else:  # default to Chinese
                prompt = f"""你是一位专业的教育内容设计师，需要为以下教学内容创建一个美观、简约的PPT风格HTML页面。

## 页面内容摘要
{content_summary}

## 关键知识点
{', '.join(key_points)}

## 原始内容参考
{original_content}

## 页面对应的字幕内容
{subtitle_text}

## 任务要求
请创建一个简约精美的PPT风格HTML页面，要求：
1. 严格控制尺寸为1200px × 680px，内容不能超出此范围
2. 使用现代、极简的设计风格，类似高质量PPT
3. 包含标题、核心内容和关键知识点
4. 使用简洁的排版、适当的留白和协调的配色
5. 确保内容清晰、易读、重点突出
6. 使用内联CSS样式，不需要外部依赖
7. 页面应该是完全自包含的，可以单独显示
8. 避免内容过多导致拥挤，保持页面整洁
9. 上下需要50px的留白
10. 根据字幕内容设计页面，确保页面内容与字幕内容相匹配
11. 请注意这个尺寸，内容不要太多，不然会超尺寸的，只写重点
12. 页面需要居中，然后四个角需要有点10px圆角

## 输出格式
请直接输出完整的HTML代码，不要包含任何解释或额外的文本。"""

            # 调用Gemini API
            html_content = self.gemini_model.generate_pro_content(prompt)

            # 处理可能的代码块格式
            if "```html" in html_content:
                html_content = html_content.split("```html")[1].split("```")[0].strip()
            elif "```" in html_content:
                html_content = html_content.split("```")[1].split("```")[0].strip()
                
            return html_content
            
        except Exception as e:
            logger.error(f"生成页面 {page_title} 的HTML内容失败: {str(e)}")
            raise AILectureException(f"生成页面 {page_title} 的HTML内容失败: {str(e)}") 

