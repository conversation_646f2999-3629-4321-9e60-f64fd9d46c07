<template>
  <div class="course-grades">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">成绩查询</h1>
      <div class="flex gap-3">
        <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
          <span class="material-icons text-sm">file_download</span>
          导出数据
        </button>
        <button 
          class="bg-blue-50 hover:bg-blue-100 text-blue-700 py-2 px-4 rounded-md flex items-center gap-2"
          @click="generateAnalysisReport"
        >
          <span class="material-icons text-sm">analytics</span>
          生成分析报告
        </button>
      </div>
    </div>

    <!-- 整体表现分析卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
      <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-6 flex flex-col">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">作业平均分</h3>
          <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
            <span class="material-icons text-blue-600">analytics</span>
          </div>
        </div>
        <div class="text-3xl font-bold text-gray-900 mb-2">{{ stats.averageScore }}</div>
        <div class="flex items-center text-sm">
          <span class="text-gray-500">已批改人数：{{ grades.filter(g => g.score !== null).length }}</span>
        </div>
      </div>

      <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-6 flex flex-col">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">优秀人数</h3>
          <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
            <span class="material-icons text-green-600">emoji_events</span>
          </div>
        </div>
        <div class="text-3xl font-bold text-gray-900 mb-2">{{ stats.excellentCount }}</div>
        <div class="flex items-center text-sm">
          <span class="text-gray-500">占比 <span class="font-medium text-gray-700">{{ stats.excellentRate }}%</span></span>
        </div>
      </div>

      <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-6 flex flex-col">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">不及格人数</h3>
          <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
            <span class="material-icons text-red-600">warning</span>
          </div>
        </div>
        <div class="text-3xl font-bold text-gray-900 mb-2">{{ stats.failCount }}</div>
        <div class="flex items-center text-sm">
          <span class="text-gray-500">占比 <span class="font-medium text-gray-700">{{ stats.failRate }}%</span></span>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow">
      <!-- 搜索和筛选区域 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex gap-3 items-center">
          <!-- 作业选择下拉框 -->
          <div class="relative flex-1">
            <el-select 
              v-model="selectedHomework" 
              placeholder="请选择作业" 
              style="width: 100%;"
              @change="handleHomeworkChange"
            >
              <el-option
                v-for="homework in homeworks"
                :key="homework.id"
                :label="homework.title"
                :value="homework.id"
              />
            </el-select>
          </div>
          <div class="relative flex-1">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索学生..." 
              class="w-full border border-gray-300 rounded-md pl-9 pr-4 h-[32px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
              <span class="material-icons text-gray-600">filter_list</span>
              筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <div class="space-y-3">
                <div class="font-medium flex items-center gap-1.5">
                  <span class="material-icons text-base text-gray-600">label</span>
                  成绩类型
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="type in gradeTypes" 
                    :key="type"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.types.includes(type) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('types', type)"
                  >
                    <span v-if="selectedFilters.types.includes(type)" class="material-icons text-xs">check_circle</span>
                    {{ type }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 成绩列表 -->
      <div class="p-4">
        <el-table :data="filteredGrades" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="studentId" label="学号" width="120"></el-table-column>
          <el-table-column prop="name" label="姓名" width="120"></el-table-column>
          <el-table-column prop="score" label="成绩" width="120">
            <template #default="{ row }">
              <span :class="getScoreClass(row.score)">{{ row.score || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submittedAt" label="提交时间" width="180">
            <template #default="{ row }">
              {{ row.submittedAt ? formatDate(row.submittedAt) : '-' }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getHomeworkList, getHomeworkGrades } from '@/api/homework'

const route = useRoute()
const courseId = route.params.courseId

// 统计数据
const stats = computed(() => {
  const validScores = grades.value.filter(grade => grade.score !== null).map(grade => grade.score)
  const totalStudents = grades.value.length
  
  // 计算平均分
  const averageScore = validScores.length > 0 
    ? Math.round(validScores.reduce((sum, score) => sum + score, 0) / validScores.length * 10) / 10
    : 0

  // 计算优秀人数（>80分）
  const excellentCount = validScores.filter(score => score > 80).length
  const excellentRate = totalStudents > 0 
    ? Math.round(excellentCount / totalStudents * 1000) / 10
    : 0

  // 计算不及格人数（<60分）
  const failCount = validScores.filter(score => score < 60).length
  const failRate = totalStudents > 0 
    ? Math.round(failCount / totalStudents * 1000) / 10
    : 0

  return {
    averageScore,
    excellentCount,
    excellentRate,
    failCount,
    failRate
  }
})

// 作业相关数据
const homeworks = ref([])
const selectedHomework = ref(null)
const grades = ref([])

// 搜索和筛选
const searchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  types: []
})

// 成绩类型
const gradeTypes = ['已提交', '未提交', '已批改']

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 加载状态
const loading = ref(false)

// 过滤后的成绩列表
const filteredGrades = computed(() => {
  let result = [...grades.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(grade =>
      grade.studentId.includes(searchQuery.value) ||
      grade.name.includes(searchQuery.value)
    )
  }
  
  // 类型过滤
  if (selectedFilters.value.types.length > 0) {
    result = result.filter(grade => {
      if (selectedFilters.value.types.includes('已提交')) {
        return grade.status === 'submitted'
      }
      if (selectedFilters.value.types.includes('未提交')) {
        return !grade.status
      }
      if (selectedFilters.value.types.includes('已批改')) {
        return grade.score !== null
      }
      return true
    })
  }
  
  return result
})

// 获取课程作业列表
const fetchHomeworks = async () => {
  try {
    const response = await getHomeworkList(courseId)
    const data = response.results
    homeworks.value = data
    if (data.length > 0) {
      selectedHomework.value = data[0].id
      await fetchHomeworkGrades(data[0].id)
    }
  } catch (error) {
    ElMessage.error('获取作业列表失败')
  }
}

// 获取作业成绩列表
const fetchHomeworkGrades = async (homeworkId) => {
  if (!homeworkId) return
  
  loading.value = true
  try {
    const data = await getHomeworkGrades(homeworkId)
    grades.value = data
    total.value = data.length
  } catch (error) {
    ElMessage.error('获取成绩列表失败')
  } finally {
    loading.value = false
  }
}

// 作业切换处理
const handleHomeworkChange = async (homeworkId) => {
  await fetchHomeworkGrades(homeworkId)
}

// 生成分析报告
const generateAnalysisReport = () => {
  ElMessage.info('生成分析报告功能开发中...')
}

// 切换筛选条件
const toggleFilter = (type, value) => {
  const index = selectedFilters.value[type].indexOf(value)
  if (index === -1) {
    selectedFilters.value[type].push(value)
  } else {
    selectedFilters.value[type].splice(index, 1)
  }
}

// 获取成绩显示样式
const getScoreClass = (score) => {
  if (!score) return 'text-gray-500'
  if (score >= 90) return 'text-green-600 font-medium'
  if (score >= 60) return 'text-gray-900'
  return 'text-red-600 font-medium'
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'submitted':
      return 'info'
    case 'late':
      return 'warning'
    case 'graded':
      return 'success'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'submitted':
      return '已提交'
    case 'late':
      return '迟交'
    case 'graded':
      return '已批改'
    default:
      return '未提交'
  }
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 页面加载时获取作业列表
onMounted(() => {
  fetchHomeworks()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 