/* 
 * 认证页面专用样式
 * 使用Element Plus变量确保样式一致性
 */

.auth-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: var(--el-fill-color-light, var(--el-bg-color));
  padding: 16px;
}

.auth-box {
  max-width: 420px;
  width: 100%;
  margin: 0 auto;
}

.auth-card {
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  box-shadow: var(--el-box-shadow-light);
}

.auth-header {
  padding: 24px;
  background-color: var(--el-color-primary);
  text-align: center;
}

.auth-title {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-white, #fff);
  margin: 0;
}

.auth-subtitle {
  color: var(--el-color-primary-light-8);
  margin-top: 8px;
  font-size: 14px;
}

.auth-form-container {
  padding: 24px;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 24px;
  text-align: center;
}

.form-description {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
  line-height: 1.5;
}

.password-tip {
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.role-group {
  display: flex;
  width: 100%;
}

.role-button {
  flex: 1;
  text-align: center;
}

.role-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.role-icon {
  margin-bottom: 4px;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.role-label {
  font-size: 12px;
}

.auth-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.remember-text,
.terms-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.remember-text .el-icon {
  margin-right: 4px;
}

/* 链接样式统一使用Element变量 */
.auth-link,
.forgot-link,
.register-link,
.login-link,
.terms-link,
.back-link {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color var(--el-transition-duration);
}

.auth-link:hover,
.forgot-link:hover,
.register-link:hover,
.login-link:hover,
.terms-link:hover,
.back-link:hover {
  color: var(--el-color-primary-light-3);
}

.auth-link .el-icon,
.forgot-link .el-icon,
.back-link .el-icon {
  margin-right: 4px;
}

.auth-button {
  width: 100%;
}

/* 社交登录按钮 */
.social-login {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-top: 24px;
}

.social-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
  border-radius: var(--el-border-radius-base);
  transition: all var(--el-transition-duration);
  box-shadow: none;
}

.social-btn:hover {
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.wechat .el-icon {
  color: #07C160; /* 微信官方绿色 */
  font-size: 20px;
}

.qq .el-icon {
  color: #12B7F5; /* QQ官方蓝色 */
  font-size: 20px;
}

.mobile .el-icon {
  color: var(--el-color-info);
  font-size: 20px;
}

.link-container {
  margin-top: 24px;
  text-align: center;
}

.link-text {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.footer {
  margin-top: 16px;
  text-align: center;
}

.copyright {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 特殊元素样式重写 - 确保与Element组件兼容 */
.el-radio-button__inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: auto;
  padding: 8px;
  transition: all var(--el-transition-duration);
}

/* 暗黑模式适配 */
.dark-mode .auth-card {
  background-color: var(--el-bg-color-overlay-dark, #1d1e1f);
}

.dark-mode .auth-title {
  color: var(--el-color-white, #fff);
}

.dark-mode .auth-subtitle {
  color: var(--el-color-primary-light-7);
}

.dark-mode .form-title,
.dark-mode .role-icon {
  color: var(--el-text-color-primary-dark, #E5EAF3);
}

.dark-mode .form-description,
.dark-mode .password-tip,
.dark-mode .remember-text,
.dark-mode .terms-text,
.dark-mode .link-text,
.dark-mode .copyright {
  color: var(--el-text-color-secondary-dark, #A3A6AD);
} 