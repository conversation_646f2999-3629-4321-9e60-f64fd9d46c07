from django.http import StreamingHttpResponse
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ViewSet

from ..services.ai_lecture import SubtitleAnalyzer, DocumentProcessor
from ..services.video_assistant_service import VideoAssistantService
from ..tasks.ai_lecture_tasks import process_chapter_gemini_audio_sync
from ..utils.gemini_utils import generate_dialogue_and_audio
from ..utils.response import ResponseResult


class VideoAssistantViewSet(ViewSet):
    """视频助手接口视图集"""
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['get'], url_path='dialogues')
    def get_all_dialogues(self, request, pk=None):
        """
        获取视频的所有预设对话
        
        Args:
            pk: 视频ID
            
        Returns:
            Response: 包含对话列表的响应
        """
        try:
            if not pk:
                return ResponseResult.error(
                    code=400,
                    message='视频ID不能为空'
                )
                
            result = VideoAssistantService.get_video_dialogues(pk)
            
            return ResponseResult.success(
                data=result,
                message='获取视频对话成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取视频对话失败: {str(e)}'
            )
            
    @action(detail=True, methods=['get'], url_path='navigation')
    def get_video_navigation(self, request, pk=None):
        """
        获取视频的导航数据（关键点和摘要）
        
        Args:
            pk: 视频ID
            
        Returns:
            Response: 包含视频导航数据的响应
        """
        try:
            if not pk:
                return ResponseResult.error(
                    code=400,
                    message='视频ID不能为空'
                )
                
            result = VideoAssistantService.get_video_navigation(pk)
            
            return ResponseResult.success(
                data=result,
                message='获取视频导航数据成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取视频导航数据失败: {str(e)}'
            )
            
    @action(detail=True, methods=['get'], url_path='teacher-info')
    def get_teacher_info(self, request, pk=None):
        """
        获取视频关联的教师信息
        
        Args:
            pk: 视频ID
            
        Returns:
            Response: 包含视频教师信息的响应
        """
        try:
            if not pk:
                return ResponseResult.error(
                    code=400,
                    message='视频ID不能为空'
                )
                
            result = VideoAssistantService.get_video_teacher_info(pk)
            
            return ResponseResult.success(
                data=result,
                message='获取视频教师信息成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取视频教师信息失败: {str(e)}'
            )
            
    @action(detail=False, methods=['post'], url_path='chat-stream')
    def chat_with_ai_stream(self, request):
        """
        与AI助手进行流式对话
        
        Request Body:
            - message: 用户消息
            - history: 对话历史(可选)
            - video_id: 视频ID(可选)，用于获取字幕数据作为上下文
            - current_video_time: 当前视频播放时间(可选)，格式为"MM:SS"
        
        Returns:
            StreamingHttpResponse: 流式响应，包含SSE格式的AI回复
        """
        try:
            message = request.data.get('message')
            history = request.data.get('history', [])
            video_id = request.data.get('video_id')
            current_video_time = request.data.get('current_video_time')
            
            if not message:
                return ResponseResult.error(
                    code=400,
                    message='消息不能为空'
                )
            
            # 使用流式响应
            return StreamingHttpResponse(
                VideoAssistantService.chat_with_ai_stream(message, history, video_id, current_video_time),
                content_type='text/event-stream'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'启动流式对话失败: {str(e)}'
            )

    @action(detail=False, methods=['post'], url_path='process-video')
    def process_video(self, request):
        """
        启动视频处理任务
        
        Request Body:
            - lesson_id: 课时ID (可选，默认为18)

        Returns:
            Response: 包含任务ID的响应
        """
        try:
            # lesson_id = request.data.get('lesson_id', 15)
            
            # 启动Celery任务
            # process_ai_lecture_doc_task.delay(1)
            # process_ai_lecture_doc(1)

            # dataset_ids = ["66c954582efd11f0b43c0242ac150002"]
            # document_ids = ["70f3df982efd11f0bb050242ac150002"]
            # generate_ai_lecture_markdown_sync(139,  dataset_ids, document_ids)
            # process_chapter_gemini_audio_sync(633)

            # subtitle_analyzer = SubtitleAnalyzer()
            # subtitle_analyzer.transcribe_audio_to_subtitles(r"E:\project\python\ai-education\backend\gemini_audio\document_177\chapter_747\dialogue_20250614_194822.wav")

            # generate_dialogue_and_audio("你好，我是Anya，今天我们聊一下关于AI的话题。", "output_audio")

            d = DocumentProcessor()
            task_id = d._upload_pdf_for_parsing(r"E:\project\python\ai-education\backend\temp\项目三_揭开人工智能的神秘面纱.pdf")
            d._wait_for_parsing_result(task_id)


            return ResponseResult.success(
                data={
                    'task_id': "1",
                    'lesson_id': "1"
                },
                message='视频处理任务已启动'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'启动视频处理任务失败: {str(e)}'
            ) 