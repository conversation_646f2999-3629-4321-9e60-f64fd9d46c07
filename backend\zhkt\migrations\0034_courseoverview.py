# Generated by Django 3.2.20 on 2025-05-13 09:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0033_comment_deleted_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='CourseOverview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key_points', models.TextField(blank=True, verbose_name='课程要点')),
                ('important_points', models.TextField(blank=True, verbose_name='重点知识点')),
                ('notes', models.TextField(blank=True, verbose_name='注意事项')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('course', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='overview', to='zhkt.course', verbose_name='课程')),
            ],
            options={
                'verbose_name': '课程概述',
                'verbose_name_plural': '课程概述',
                'ordering': ['-created_at'],
            },
        ),
    ]
