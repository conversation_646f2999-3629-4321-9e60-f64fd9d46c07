from rest_framework import permissions
from rest_framework.decorators import action
import json
from .base_model_view import BaseModelViewSet
from zhkt.serializers import (
    AIChatSerializer,
    AIChatMessageSerializer,
    AIPromptSerializer,
)
from zhkt.entitys import (
    AIChat, 
    AIChatMessage,
    AIPrompt,
)
from zhkt.services.knowledge_service import KnowledgeService
from zhkt.utils.knowledge_utils import KnowledgeBaseAPI
from zhkt.utils.deepseek_api import DeepSeekAPI
from django.http import StreamingHttpResponse

# AI助手相关视图集
class AIChatViewSet(BaseModelViewSet):
    queryset = AIChat.objects.all()
    serializer_class = AIChatSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):
            # 为 Swagger 文档生成返回空查询集
            return AIChat.objects.none()
        return AIChat.objects.filter(user=self.request.user, deleted_at__isnull=True).order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class AIChatMessageViewSet(BaseModelViewSet):
    queryset = AIChatMessage.objects.all()
    serializer_class = AIChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None  # 禁用分页

    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):
            # 为 Swagger 文档生成返回空查询集
            return AIChatMessage.objects.none()
            
        chat_id = self.request.query_params.get('chat', None)
        if chat_id:
            return AIChatMessage.objects.filter(chat_id=chat_id, chat__user=self.request.user, deleted_at__isnull=True).order_by('created_at')
        return AIChatMessage.objects.none()

    def perform_create(self, serializer):
        chat_id = self.request.data.get('chat')
        chat = AIChat.objects.get(id=chat_id, user=self.request.user)
        serializer.save(chat=chat)

    @action(detail=False, methods=['post'])
    def stream_response(self, request):
        """流式返回AI回复"""
        chat_id = request.data.get('chat')
        message = request.data.get('content', '')
        model = request.data.get('model', 'deepseek-chat')
        selected_func_values = request.data.get('selectedFuncValues')
        # print('select_func_values', selected_func_values)

        # 创建用户消息记录
        chat = AIChat.objects.get(id=chat_id, user=request.user)
        user_message = AIChatMessage.objects.create(
            chat=chat,
            role='USER',
            content=message
        )

        dataset_ids = []
        document_ids = []
        if 'knowledge' in selected_func_values:
            # 查询所有knowledgeDateset以及其下的knowledgeDocument
            datasets = KnowledgeService.get_datasets_by_category(4)
            for dataset in datasets:
                dataset_ids.append(dataset.get('id'))
                documents = KnowledgeService.get_documents_by_dataset(dataset.get('id'), request.user.id)
                for document in documents.get('list'):
                    document_ids.append(document.get('id'))
        else:
            selected_dataset_name_list = []
            if 'python' in selected_func_values:
                selected_dataset_name_list.append('Python课程')
            if 'ecommerce' in selected_func_values:
                selected_dataset_name_list.append('电商课程')
            if 'ai' in selected_func_values:
                selected_dataset_name_list.append('AI课程')
            # 根据名称查询knowledgeDateset以及其下的knowledgeDocument
            datasets = KnowledgeService.get_datasets_by_category(4)
            for dataset in datasets:
                if dataset.get('name') in selected_dataset_name_list:
                    dataset_ids.append(dataset.get('id'))
                    documents = KnowledgeService.get_documents_by_dataset(dataset.get('id'), request.user.id)
                    for document in documents.get('list'):
                        document_ids.append(document.get('id'))
            
        # 开始检索
        retrieve_result = None
        if len(dataset_ids)>0:
            try:
                api_client = KnowledgeBaseAPI()
                retrieve_result = api_client.retrieve(question=message, 
                                                    dataset_ids=dataset_ids, 
                                                    document_ids=document_ids)
            except Exception as ex:
                print(ex)
        # print('retrieve_result', retrieve_result)

        if retrieve_result:
            data = retrieve_result.get('data')
            chunks = data.get('chunks')
            if chunks and len(chunks)>0:
                chunks = chunks[:5]
                chunk_contents = []
                for chunk in chunks:
                    chunk_content = chunk.get('content')
                    chunk_contents.append(chunk_content)
                message = " ".join(chunk_contents)+" "+message

        def generate_response():
            try:
                # 用于存储完整的AI回复
                full_response = {
                    'content': '',
                    'reasoning': ''
                }

                # 初始化DeepSeekAPI对象
                deepseek_api = DeepSeekAPI()

                # 构建正确的消息格式
                messages = [{
                    "role": "user",
                    "content": message
                }]

                # 调用DeepSeek API获取流式响应
                for chunk in deepseek_api.chat_stream(messages, model=model):
                    if chunk:
                        # 更新完整响应
                        full_response['content'] += chunk.get('content') if chunk.get('content') else ''
                        full_response['reasoning'] += chunk.get('reasoning') if chunk.get('reasoning') else ''
                        
                        # 发送数据到前端
                        yield f"data: {json.dumps(chunk)}\n\n"

                # 保存完整的AI回复到数据库
                AIChatMessage.objects.create(
                    chat=chat,
                    role='ASSISTANT',
                    content=full_response['content'],
                )

            except Exception as e:
                error_message = str(e)
                yield f"data: {json.dumps({'error': error_message})}\n\n"

        return StreamingHttpResponse(
            generate_response(),
            content_type='text/event-stream'
        )

class AIPromptViewSet(BaseModelViewSet):
    queryset = AIPrompt.objects.filter(is_active=True)
    serializer_class = AIPromptSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = AIPrompt.objects.filter(is_active=True,deleted_at__isnull=True)
        prompt_type = self.request.query_params.get('type', None)
        if prompt_type:
            queryset = queryset.filter(prompt_type=prompt_type)
        return queryset