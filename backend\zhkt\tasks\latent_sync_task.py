# -*- coding: utf-8 -*-
# @Time    : 2025/1/21
# <AUTHOR> ai-education
# @File    : latent_sync_task.py
# @Description : LatentSync视频生成Celery任务

import logging
import os
import requests
import time
from pathlib import Path
from typing import Dict, Any, Optional

from celery import shared_task
from django.utils import timezone
from tenacity import retry, stop_after_attempt, wait_fixed

from ..config import DIGITAL_HUMAN_INFERENCE_URL
from ..entitys.generated_video import GeneratedVideo
from ..utils.temp_file_utils import create_temp_subdir, clean_temp_dir

logger = logging.getLogger(__name__)


class LatentSyncClient:
    """LatentSync API 客户端"""
    
    def __init__(self, base_url: str = DIGITAL_HUMAN_INFERENCE_URL):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        # 设置请求超时
        self.session.timeout = 30
    
    def upload_files(self, video_path: str, audio_path: str) -> dict:
        """上传视频和音频文件"""
        video_path = Path(video_path)
        audio_path = Path(audio_path)
        
        if not video_path.exists():
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        if not audio_path.exists():
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        with open(video_path, 'rb') as video_file, open(audio_path, 'rb') as audio_file:
            files = {
                'video': (video_path.name, video_file, 'video/mp4'),
                'audio': (audio_path.name, audio_file, 'audio/mpeg')
            }
            
            response = self.session.post(f"{self.base_url}/upload", files=files, timeout=300)
            response.raise_for_status()
            return response.json()
    
    def submit_task(self, file_id: str, **params) -> dict:
        """提交推理任务"""
        response = self.session.post(
            f"{self.base_url}/submit",
            params={"file_id": file_id},
            json=params,
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    
    def get_task_status(self, task_id: str) -> dict:
        """获取任务状态"""
        response = self.session.get(f"{self.base_url}/status/{task_id}", timeout=30)
        response.raise_for_status()
        return response.json()
    
    def download_result(self, task_id: str, output_path: str) -> bool:
        """下载结果文件"""
        response = self.session.get(f"{self.base_url}/download/{task_id}", timeout=300)
        response.raise_for_status()
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'wb') as f:
            f.write(response.content)
        return True
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        response = self.session.get(f"{self.base_url}/system/status", timeout=30)
        response.raise_for_status()
        return response.json()


@shared_task(bind=True, max_retries=3)
def process_latent_sync_video_task(self, generated_video_id: int, video_url: str, audio_url: str, 
                                 guidance_scale: float = 2.0, inference_steps: int = 20) -> Dict[str, Any]:
    """
    Celery任务：处理LatentSync视频生成
    Args:
        generated_video_id: GeneratedVideo主键
        video_path: 输入视频文件路径
        audio_path: 输入音频文件路径
        guidance_scale: 引导比例
        inference_steps: 推理步数
    Returns:
        dict: 处理结果
    """
    try:
        # 获取生成视频记录
        try:
            generated_video = GeneratedVideo.objects.get(id=generated_video_id)
        except GeneratedVideo.DoesNotExist:
            error_msg = f'生成视频记录 ID {generated_video_id} 不存在'
            logger.error(error_msg)
            return {'status': 'fail', 'error': error_msg}

        # 更新状态为处理中
        generated_video.status = 'processing'
        generated_video.save()
        
        self.update_state(state='PROGRESS', meta={
            'status': f'开始处理视频: {generated_video.name}',
            'progress': 0
        })

        # 执行视频生成
        result = process_latent_sync_video_sync(
            generated_video_id=generated_video_id,
            video_url=video_url,
            audio_url=audio_url,
            guidance_scale=guidance_scale,
            inference_steps=inference_steps,
            task_instance=self
        )

        if result.get('status') == 'fail':
            # 更新数据库状态为失败
            try:
                generated_video.refresh_from_db()
                generated_video.status = 'failed'
                generated_video.save()
                logger.warning(f"视频生成失败，已更新状态: {generated_video.name}")
            except Exception as e:
                logger.error(f"更新视频状态失败: {str(e)}")
            
            return {'status': 'fail', 'error': result.get('error', '未知错误')}

        # 更新数据库状态为成功
        try:
            generated_video.refresh_from_db()
            generated_video.status = 'success'
            generated_video.video_url = result.get('video_url')
            generated_video.duration = result.get('duration')
            generated_video.cover_image = result.get('cover_image')
            generated_video.update_time = timezone.now()
            generated_video.save()
            logger.info(f"视频生成成功，已更新状态: {generated_video.name}")
        except Exception as e:
            logger.error(f"更新视频状态失败: {str(e)}")

        return result

    except Exception as e:
        # 处理异常，将状态重置为失败
        try:
            generated_video = GeneratedVideo.objects.get(id=generated_video_id)
            generated_video.status = 'failed'
            generated_video.save()
            logger.warning(f"处理异常，已更新视频状态为失败: {generated_video.name}")
        except Exception:
            pass
            
        error_msg = f"处理视频生成任务时发生错误: {str(e)}"
        logger.error(error_msg)
        return {'status': 'fail', 'error': error_msg}


@retry(stop=stop_after_attempt(3), wait=wait_fixed(5))
def process_latent_sync_video_sync(generated_video_id: int, video_url: str, audio_url: str,
                                 guidance_scale: float = 2.0, inference_steps: int = 20,
                                 task_instance=None) -> Dict[str, Any]:
    """
    处理LatentSync视频生成 - 同步版本
    Args:
        generated_video_id: GeneratedVideo主键
        video_path: 输入视频文件路径
        audio_path: 输入音频文件路径
        guidance_scale: 引导比例
        inference_steps: 推理步数
        task_instance: Celery任务实例，用于更新状态
    Returns:
        dict: 处理结果
    """
    temp_dir = None
    client = None
    
    try:
        # 获取生成视频记录
        generated_video = GeneratedVideo.objects.get(id=generated_video_id)
        logger.info(f"开始处理LatentSync视频生成: {generated_video.name}")

        # 创建临时目录用于下载和处理文件
        temp_dir = create_temp_subdir(f"latent_sync_{generated_video_id}")
        
        # 下载视频和音频文件到临时目录
        from ..utils.temp_file_utils import get_temp_filepath
        from ..utils.file_utils import FileUtils
        
        # 准备临时文件路径，根据URL确定文件扩展名
        # 获取视频文件扩展名
        video_ext = os.path.splitext(video_url)[1] or '.mp4'
        video_temp_path = os.path.join(temp_dir, f"input_video_{generated_video_id}{video_ext}")
        
        # 获取音频文件扩展名
        audio_ext = os.path.splitext(audio_url)[1] or '.mp3'
        audio_temp_path = os.path.join(temp_dir, f"input_audio_{generated_video_id}{audio_ext}")
        
        # 使用FileUtils下载视频文件
        try:
            video_data = FileUtils.download_from_oss(video_url)
            with open(video_temp_path, 'wb') as f:
                f.write(video_data)
        except Exception as e:
            raise FileNotFoundError(f"下载视频文件失败: {video_url}, 错误: {str(e)}")
            
        # 使用FileUtils下载音频文件
        try:
            audio_data = FileUtils.download_from_oss(audio_url)
            with open(audio_temp_path, 'wb') as f:
                f.write(audio_data)
        except Exception as e:
            raise FileNotFoundError(f"下载音频文件失败: {audio_url}, 错误: {str(e)}")
        
        # 检查临时文件是否存在
        if not os.path.exists(video_temp_path):
            raise FileNotFoundError(f"视频文件下载失败: {video_temp_path}")
        
        if not os.path.exists(audio_temp_path):
            raise FileNotFoundError(f"音频文件下载失败: {audio_temp_path}")

        # 创建LatentSync客户端
        client = LatentSyncClient()
        
        # 更新任务状态
        if task_instance:
            task_instance.update_state(state='PROGRESS', meta={
                'status': '上传文件到LatentSync服务器',
                'progress': 10
            })

        # 1. 上传文件
        logger.info("上传文件到LatentSync服务器...")
        upload_result = client.upload_files(video_temp_path, audio_temp_path)
        file_id = upload_result['file_id']
        logger.info(f"文件上传成功，ID: {file_id}")

        # 更新任务状态
        if task_instance:
            task_instance.update_state(state='PROGRESS', meta={
                'status': '提交视频生成任务',
                'progress': 20
            })

        # 2. 提交任务
        logger.info("提交视频生成任务...")
        task_result = client.submit_task(
            file_id=file_id,
            guidance_scale=guidance_scale,
            inference_steps=inference_steps
        )
        task_id = task_result['task_id']
        logger.info(f"任务提交成功，ID: {task_id}")

        # 更新任务状态
        if task_instance:
            task_instance.update_state(state='PROGRESS', meta={
                'status': '等待视频生成完成',
                'progress': 30
            })

        # 3. 等待任务完成
        logger.info("等待视频生成完成...")
        final_status = wait_for_latent_sync_completion(
            client, task_id, timeout=3600, task_instance=task_instance
        )

        if final_status['status'] != 'completed':
            error_msg = f"视频生成失败: {final_status.get('error', '未知错误')}"
            logger.error(error_msg)
            return {'status': 'fail', 'error': error_msg}

        # 更新任务状态
        if task_instance:
            task_instance.update_state(state='PROGRESS', meta={
                'status': '下载生成的视频',
                'progress': 90
            })

        # 4. 下载结果
        logger.info("下载生成的视频...")
        
        # 创建临时目录存储下载的视频
        temp_dir = create_temp_subdir(f"latent_sync_video_{generated_video_id}")
        temp_video_path = os.path.join(temp_dir, f"result_{generated_video_id}.mp4")
        
        success = client.download_result(task_id, temp_video_path)
        if not success:
            raise Exception("下载生成的视频失败")

        # 5. 保存结果文件
        from ..utils.file_utils import FileUtils
        
        # 使用FileUtils保存视频文件到指定目录
        video_rel_path = FileUtils.save_video_file(temp_video_path)
        
        # 注意：video_rel_path 就是要存储到数据库的相对路径，不需要调用 get_file_url
        
        # 生成视频封面
        cover_image_path = None
        try:
            # 尝试从视频中提取第一帧作为封面
            cover_image_path = FileUtils.extract_video_frame(temp_video_path, frame_number=1)
            logger.info(f"成功生成视频封面: {cover_image_path}")
        except Exception as e:
            logger.warning(f"生成视频封面失败: {str(e)}")
        
        # 获取视频时长（可选）
        duration = get_video_duration(temp_video_path)

        logger.info(f"视频生成完成: {video_rel_path}")

        return {
            'status': 'success',
            'message': f'视频 {generated_video.name} 生成完成',
            'video_id': generated_video_id,
            'video_name': generated_video.name,
            'video_url': video_rel_path,  # 存储相对路径到数据库
            'cover_image': cover_image_path,
            'duration': duration,
            'task_id': task_id,
            'file_id': file_id
        }

    except Exception as e:
        logger.error(f"处理LatentSync视频生成时发生错误: {str(e)}")
        raise e
    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            try:
                clean_temp_dir(temp_dir)
                logger.info(f"已清理临时目录: {temp_dir}")
            except Exception as cleanup_error:
                logger.warning(f"清理临时目录失败: {cleanup_error}")


def wait_for_latent_sync_completion(client: LatentSyncClient, task_id: str, 
                                  timeout: int = 3600, check_interval: int = 30,
                                  task_instance=None) -> dict:
    """等待LatentSync任务完成"""
    start_time = time.time()
    retry_count = 0
    max_retries = 5
    
    while True:
        try:
            status = client.get_task_status(task_id)
            retry_count = 0  # 重置重试计数
            
            progress = status.get('progress', 0)
            logger.info(f"任务状态: {status['status']}")
            
            # 更新Celery任务状态
            if task_instance:
                celery_progress = min(30 + int(progress * 0.6), 89)  # 30-89%
                task_instance.update_state(state='PROGRESS', meta={
                    'status': f"视频生成中: {progress:.1f}%",
                    'progress': celery_progress
                })
            
            if status['status'] == 'completed':
                logger.info("LatentSync任务完成!")
                return status
            elif status['status'] == 'failed':
                logger.error(f"LatentSync任务失败: {status.get('error', '未知错误')}")
                return status
            elif status['status'] == 'cancelled':
                logger.warning("LatentSync任务已取消")
                return status
            
            # 检查超时
            if time.time() - start_time > timeout:
                logger.error("等待LatentSync任务完成超时")
                return {
                    'task_id': task_id,
                    'status': 'timeout',
                    'error': '任务执行超时'
                }
            
            time.sleep(check_interval)
            
        except (requests.exceptions.ConnectionError, 
                requests.exceptions.RequestException) as e:
            retry_count += 1
            logger.warning(f"网络连接错误 (第{retry_count}次): {str(e)}")
            
            if retry_count >= max_retries:
                logger.error("达到最大重试次数，放弃轮询")
                return {
                    'task_id': task_id,
                    'status': 'network_error',
                    'error': f'网络连接失败: {str(e)}'
                }
            
            logger.info(f"等待 {check_interval * 2} 秒后重试...")
            time.sleep(check_interval * 2)
            
        except Exception as e:
            logger.error(f"轮询任务状态时发生未知错误: {str(e)}")
            return {
                'task_id': task_id,
                'status': 'error',
                'error': str(e)
            }


def get_video_duration(video_path: str) -> Optional[str]:
    """获取视频时长"""
    try:
        import subprocess
        result = subprocess.run([
            'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1', video_path
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            duration_seconds = float(result.stdout.strip())
            minutes = int(duration_seconds // 60)
            seconds = int(duration_seconds % 60)
            return f"{minutes:02d}:{seconds:02d}"
    except Exception as e:
        logger.warning(f"获取视频时长失败: {str(e)}")
    
    return None 