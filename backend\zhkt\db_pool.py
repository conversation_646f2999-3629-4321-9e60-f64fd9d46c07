import logging
from django.db import connection
from django.db.backends.mysql.base import DatabaseWrapper

# 配置日志
logger = logging.getLogger('django_db_pool')
logger.setLevel(logging.INFO)

# 创建文件处理器
file_handler = logging.FileHandler('db_pool.log')
file_handler.setLevel(logging.INFO)

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# 添加处理器到日志记录器
logger.addHandler(file_handler)
logger.addHandler(console_handler)

def get_pool_stats():
    """获取数据库连接信息"""
    try:
        with connection.cursor() as cursor:
            # 获取当前连接信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_connections,
                    SUM(CASE WHEN COMMAND != 'Sleep' THEN 1 ELSE 0 END) as active_connections,
                    SUM(CASE WHEN COMMAND = 'Sleep' THEN 1 ELSE 0 END) as idle_connections
                FROM information_schema.processlist
                WHERE USER = %s
            """, [connection.settings_dict['USER']])
            total, active, idle = cursor.fetchone()

            # 获取全局状态信息
            cursor.execute("""
                SHOW GLOBAL STATUS 
                WHERE Variable_name IN (
                    'Max_used_connections',
                    'Threads_connected',
                    'Threads_running',
                    'Connections'
                )
            """)
            status_info = dict(cursor.fetchall())

            return {
                'total_connections': total,
                'active_connections': active,
                'idle_connections': idle,
                'max_used_connections': int(status_info.get('Max_used_connections', 0)),
                'threads_connected': int(status_info.get('Threads_connected', 0)),
                'threads_running': int(status_info.get('Threads_running', 0)),
                'total_connection_attempts': int(status_info.get('Connections', 0)),
                'connection_max_age': connection.settings_dict.get('CONN_MAX_AGE', 0),
                'health_checks_enabled': connection.settings_dict.get('CONN_HEALTH_CHECKS', False)
            }
    except Exception as e:
        logger.error(f"获取连接池统计信息失败: {str(e)}")
        return {
            'error': str(e),
            'total_connections': 0,
            'active_connections': 0,
            'idle_connections': 0,
            'max_used_connections': 0,
            'threads_connected': 0,
            'threads_running': 0,
            'total_connection_attempts': 0,
            'connection_max_age': 0,
            'health_checks_enabled': False
        } 