import os
import traceback
import uuid
from typing import Optional, List, Tuple

from django.conf import settings
from django.core.files.uploadedfile import UploadedFile
from django.utils import timezone
from django.db.models import Q

from ..entitys.digital_human import DigitalHuman
from ..utils.file_utils import FileUtils
from ..utils.temp_file_utils import get_temp_filepath, clean_temp_file


class DigitalHumanService:
    """数字人服务类"""

    @staticmethod
    def get_digital_humans(
        user_id: int,
        skip: int = 0,
        limit: int = 10,
        search: Optional[str] = None
    ) -> dict:
        """
        获取用户的数字人列表
        
        Args:
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数
            search: 搜索关键词，可选
            
        Returns:
            包含总数和数字人列表的字典
        """
        try:
            # 创建基础查询
            base_query = DigitalHuman.objects.filter(
                user_id=user_id,
                deleted_at__isnull=True,
                type='real'  # 只返回用户创建的真人数字人
            )
            
            # 添加搜索条件
            if search:
                base_query = base_query.filter(
                    Q(name__icontains=search) | Q(description__icontains=search)
                )
            
            # 获取总数
            total = base_query.count()
            
            # 获取分页数据
            digital_humans = base_query.order_by('-created_at')[skip:skip+limit]
            
            return {
                "total": total,
                "digital_humans": digital_humans,
                "page": (skip // limit) + 1 if limit > 0 else 1,
                "size": limit,
                "pages": (total + limit - 1) // limit if limit > 0 else 1
            }
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取数字人列表失败: {str(e)}")
    
    @staticmethod
    def get_digital_human_detail(user_id: int, digital_human_id: int) -> Optional[DigitalHuman]:
        """
        获取数字人详情
        
        Args:
            user_id: 用户ID
            digital_human_id: 数字人ID
            
        Returns:
            数字人对象或None
        """
        try:
            return DigitalHuman.objects.filter(
                id=digital_human_id,
                user_id=user_id,
                deleted_at__isnull=True
            ).first()
        except Exception as e:
            traceback.print_exc()
            return None
    
    @staticmethod
    def create_digital_human(
        user_id: int,
        name: str,
        description: Optional[str] = None,
        video: Optional[UploadedFile] = None
    ) -> DigitalHuman:
        """
        创建数字人
        
        Args:
            user_id: 用户ID
            name: 数字人名称
            description: 数字人描述，可选
            video: 训练视频，可选
            
        Returns:
            创建的数字人对象
        """
        try:
            avatar_url = None
            video_url = None
            
            # 处理视频文件
            if video:
                # 保存视频
                video_ext = os.path.splitext(getattr(video, 'name', ''))[1]
                temp_video_path = get_temp_filepath(suffix=video_ext)
                with open(temp_video_path, 'wb') as tmp_f:
                    for chunk in video.chunks():
                        tmp_f.write(chunk)
                try:
                    # 保存视频文件
                    video_url = FileUtils.save_video_file(temp_video_path)
                    
                    # 从视频中截取第三帧作为头像
                    avatar_url = FileUtils.extract_video_frame(temp_video_path, frame_number=3)
                    
                finally:
                    clean_temp_file(temp_video_path)
            
            # 创建数字人
            digital_human = DigitalHuman.objects.create(
                user_id=user_id,
                name=name,
                description=description,
                avatar_url=avatar_url,
                video_url=video_url,
                type='real',
                status='processing' if video else 'inactive'
            )
            
            return digital_human
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"创建数字人失败: {str(e)}")
    
    @staticmethod
    def update_digital_human(
        user_id: int,
        digital_human_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        video: Optional[UploadedFile] = None
    ) -> Optional[DigitalHuman]:
        """
        更新数字人
        
        Args:
            user_id: 用户ID
            digital_human_id: 数字人ID
            name: 数字人名称，可选
            description: 数字人描述，可选
            video: 训练视频文件，可选（如果提供将自动从第三帧截取头像）
            
        Returns:
            更新后的数字人对象或None
        """
        try:
            digital_human = DigitalHuman.objects.filter(
                id=digital_human_id,
                user_id=user_id,
                deleted_at__isnull=True
            ).first()
            
            if not digital_human:
                return None
            
            # 更新基本信息
            if name is not None:
                digital_human.name = name
            if description is not None:
                digital_human.description = description
            
            # 更新视频和头像
            if video:
                # 删除旧视频
                if digital_human.video_url:
                    FileUtils.delete_file(digital_human.video_url)
                
                # 删除旧头像
                if digital_human.avatar_url:
                    FileUtils.delete_file(digital_human.avatar_url)
                
                # 保存新视频
                video_ext = os.path.splitext(getattr(video, 'name', ''))[1]
                temp_video_path = get_temp_filepath(suffix=video_ext)
                with open(temp_video_path, 'wb') as tmp_f:
                    for chunk in video.chunks():
                        tmp_f.write(chunk)
                try:
                    # 保存视频文件
                    digital_human.video_url = FileUtils.save_video_file(temp_video_path)
                    
                    # 从新视频中截取第三帧作为头像
                    digital_human.avatar_url = FileUtils.extract_video_frame(temp_video_path, frame_number=3)
                    
                finally:
                    clean_temp_file(temp_video_path)
            
            digital_human.save()
            return digital_human
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"更新数字人失败: {str(e)}")
    
    @staticmethod
    def delete_digital_human(user_id: int, digital_human_id: int) -> bool:
        """
        删除数字人（软删除）
        
        Args:
            user_id: 用户ID
            digital_human_id: 数字人ID
            
        Returns:
            是否删除成功
        """
        try:
            digital_human = DigitalHuman.objects.filter(
                id=digital_human_id,
                user_id=user_id,
                deleted_at__isnull=True
            ).first()
            
            if not digital_human:
                return False
            
            # 软删除
            digital_human.deleted_at = timezone.now()
            digital_human.save()
            
            return True
            
        except Exception as e:
            traceback.print_exc()
            return False
    
    @staticmethod
    def get_active_digital_humans(
        user_id: int,
        skip: int = 0,
        limit: int = 10,
        search: Optional[str] = None
    ) -> dict:
        """
        获取用户的可用数字人列表（仅限已激活状态）
        用于视频生成等需要可用数字人的场景
        
        Args:
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数
            search: 搜索关键词，可选
            
        Returns:
            包含总数和数字人列表的字典
        """
        try:
            # 创建基础查询 - 只返回已激活的数字人
            base_query = DigitalHuman.objects.filter(
                user_id=user_id,
                deleted_at__isnull=True,
                type='real',  # 只返回用户创建的真人数字人
                status='active'  # 只返回已激活的数字人
            )
            
            # 添加搜索条件
            if search:
                base_query = base_query.filter(
                    Q(name__icontains=search) | Q(description__icontains=search)
                )
            
            # 获取总数
            total = base_query.count()
            
            # 获取分页数据
            digital_humans = base_query.order_by('-created_at')[skip:skip+limit]
            
            return {
                "total": total,
                "digital_humans": digital_humans,
                "page": (skip // limit) + 1 if limit > 0 else 1,
                "size": limit,
                "pages": (total + limit - 1) // limit if limit > 0 else 1
            }
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取可用数字人列表失败: {str(e)}")

    @staticmethod
    def get_system_presets() -> List[dict]:
        """
        获取系统预设数字人列表
        
        Returns:
            系统预设数字人列表
        """
        try:
            # 从数据库获取系统预设
            presets = DigitalHuman.objects.filter(
                type='system',
                deleted_at__isnull=True
            ).order_by('id')
            
            # 如果数据库中没有预设，返回默认预设
            if not presets.exists():
                return [
                    {
                        'id': 101,
                        'name': '标准授课数字人',
                        'description': '专为常规课堂讲授设计的数字人模板，具有清晰的发音和自然的教学姿态。',
                        'avatar': '/static/images/presets/teacher1.png',
                        'category': '通用教学',
                    },
                    {
                        'id': 102,
                        'name': '实验指导数字人',
                        'description': '适用于实验室教学和演示，擅长详细讲解实验步骤和注意事项。',
                        'avatar': '/static/images/presets/teacher2.png',
                        'category': '实验教学',
                    },
                    {
                        'id': 103,
                        'name': '语言教学数字人',
                        'description': '针对语言教学优化，具有标准的发音和丰富的表情，适合语言课程。',
                        'avatar': '/static/images/presets/teacher3.png',
                        'category': '语言教学',
                    },
                    {
                        'id': 104,
                        'name': '辅导答疑数字人',
                        'description': '设计用于一对一辅导和问题解答，反应灵活，表达亲和。',
                        'avatar': '/static/images/presets/teacher4.png',
                        'category': '辅导答疑',
                    }
                ]
            
            # 转换数据库记录为字典格式
            return [
                {
                    'id': preset.id,
                    'name': preset.name,
                    'description': preset.description,
                    'avatar': FileUtils.get_file_url(preset.avatar_url) if preset.avatar_url else '/static/images/presets/default.png',
                    'category': preset.category or '通用',
                }
                for preset in presets
            ]
            
        except Exception as e:
            traceback.print_exc()
            return []
    
    @staticmethod
    def use_system_preset(
        user_id: int,
        preset_id: int,
        name: str,
        description: Optional[str] = None
    ) -> DigitalHuman:
        """
        基于系统预设创建数字人
        
        Args:
            user_id: 用户ID
            preset_id: 预设ID
            name: 数字人名称
            description: 数字人描述，可选
            
        Returns:
            创建的数字人对象
        """
        try:
            # 查找预设
            preset = DigitalHuman.objects.filter(
                id=preset_id,
                type='system',
                deleted_at__isnull=True
            ).first()
            
            if not preset:
                raise Exception("系统预设不存在")
            
            # 基于预设创建新的数字人
            digital_human = DigitalHuman.objects.create(
                user_id=user_id,
                name=name,
                description=description or preset.description,
                avatar_url=preset.avatar_url,
                video_url=preset.video_url,
                type='real',
                status='active',
                category=preset.category
            )
            
            return digital_human
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"基于预设创建数字人失败: {str(e)}")
    
    @staticmethod
    def update_usage_count(digital_human_id: int):
        """
        更新数字人使用次数
        
        Args:
            digital_human_id: 数字人ID
        """
        try:
            from django.db import models
            DigitalHuman.objects.filter(
                id=digital_human_id,
                deleted_at__isnull=True
            ).update(usage_count=models.F('usage_count') + 1)
        except Exception as e:
            print(f"更新使用次数失败: {e}")
            pass
    
 