<template>
  <el-dialog
    :model-value="show"
    @update:model-value="$emit('update:show', $event)"
    :title="course?.name || '课程详情'"
    width="50%"
    :show-close="true"
    @close="close"
  >
    <template #header>
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center">
          <el-icon class="mr-2 text-xl text-blue-500"><Reading /></el-icon>
          <div>
            <h3 class="text-xl font-medium text-gray-900">{{ course?.name || '课程详情' }}</h3>
            <p class="mt-1 text-sm text-gray-500 flex items-center">
              <el-icon class="mr-1"><School /></el-icon>
              {{ course?.college || '-' }}
            </p>
          </div>
        </div>
        <el-tag
          :type="getStatusType(course?.status)"
          :effect="course?.status === 'active' ? 'dark' : 'light'"
          class="ml-4"
        >
          <template #icon>
            <el-icon><component :is="getStatusIcon(course?.status)" /></el-icon>
          </template>
          {{ getStatusText(course?.status) }}
        </el-tag>
      </div>
    </template>

    <!-- 课程详细信息 -->
    <div class="mt-6">
      <el-descriptions :column="3" border>
        <el-descriptions-item>
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-1"><User /></el-icon>
              授课教师
            </div>
          </template>
          {{ course?.teacher || '-' }}
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-1"><UserFilled /></el-icon>
              学生人数
            </div>
          </template>
          <el-statistic :value="course?.studentCount || 0">
            <template #suffix>人</template>
          </el-statistic>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-1"><Calendar /></el-icon>
              开课时间
            </div>
          </template>
          {{ course?.startDate || '-' }}
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-1"><Timer /></el-icon>
              课程时长
            </div>
          </template>
          <el-statistic :value="course?.duration || 0">
            <template #suffix>周</template>
          </el-statistic>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-1"><Grid /></el-icon>
              班级数量
            </div>
          </template>
          <el-statistic :value="course?.classCount || 0">
            <template #suffix>个班级</template>
          </el-statistic>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <el-button @click="close">
          <el-icon class="mr-1"><Close /></el-icon>
          关闭
        </el-button>
        <template v-if="course?.status !== 'ended'">
          <el-button type="primary" @click="handleEdit">
            <el-icon class="mr-1"><Edit /></el-icon>
            编辑课程
          </el-button>
        </template>
        <template v-else>
          <el-button @click="handleArchive">
            <el-icon class="mr-1"><Folder /></el-icon>
            归档课程
          </el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import {
  Reading,
  School,
  User,
  UserFilled,
  Calendar,
  Timer,
  Grid,
  Edit,
  Folder,
  Close,
  VideoPlay,
  Clock,
  CircleCheck
} from '@element-plus/icons-vue'

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  course: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'edit', 'archive', 'update:show'])

const getStatusType = (status) => {
  const types = {
    active: 'success',
    planned: 'warning',
    ended: 'info'
  }
  return types[status] || 'info'
}

const getStatusIcon = (status) => {
  const icons = {
    active: VideoPlay,
    planned: Clock,
    ended: CircleCheck
  }
  return icons[status] || Clock
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    planned: '计划中',
    ended: '已结束'
  }
  return texts[status] || '未知状态'
}

const handleEdit = () => {
  emit('edit')
}

const handleArchive = () => {
  emit('archive')
}

const close = () => {
  emit('close')
}
</script>

<style>
.el-descriptions__label {
  width: 110px;
}

.el-descriptions__cell {
  padding: 16px !important;
}

.el-descriptions__content {
  min-width: 120px;
}

.el-tag .el-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.el-statistic__content {
  font-size: 16px !important;
  min-width: 80px;
}

@media screen and (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
  }
  .el-descriptions__label {
    width: 95px;
  }
}
</style> 