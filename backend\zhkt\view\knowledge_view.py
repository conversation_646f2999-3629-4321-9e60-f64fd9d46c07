from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser

from ..entitys.knowledge import KnowledgeCategory, KnowledgeDataset
from ..entitys.user import Teacher
from ..services.knowledge_service import KnowledgeService
from ..utils.response import ResponseResult

class KnowledgeViewSet(ViewSet):
    """知识库接口视图集"""
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'], url_path='categories')
    def get_categories(self, request):
        """
        获取所有知识库大分类列表
        
        Query Params:
            category_type: 可选，分类类型 ('personal' 或 'knowledge')
        
        Returns:
            Response: 包含大分类列表的响应
        """
        try:
            # 获取分类类型过滤参数
            category_type = request.query_params.get('category_type')
            
            categories = KnowledgeService.get_all_categories(category_type)
            return ResponseResult.success(
                data=categories,
                message='获取知识库大分类列表成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取知识库大分类列表失败: {str(e)}'
            )

    @action(detail=True, methods=['get'], url_path='datasets')
    def get_datasets(self, request, pk=None):
        """
        获取指定大分类下的所有知识库数据集
        
        Args:
            pk: 大分类ID
        
        Returns:
            Response: 包含数据集列表的响应
        """
        try:
            datasets = KnowledgeService.get_datasets_by_category(pk)
            return ResponseResult.success(
                data=datasets,
                message='获取知识库数据集列表成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取知识库数据集列表失败: {str(e)}'
            )

    @action(detail=True, methods=['post'], url_path='create-dataset')
    def create_dataset(self, request, pk=None):
        """
        创建新的知识库数据集
        
        Args:
            pk: 大分类ID，指定数据集所属的大分类
            
        Request Body:
            name: 数据集名称（必填）
            description: 数据集描述（选填）
            avatar: 头像Base64编码（选填）
            embedding_model: 嵌入模型名称（选填，默认为text-embedding-v3）
            icon: 数据集图标（选填，默认为Notebook）
        
        Returns:
            Response: 包含新创建的数据集信息的响应
        """
        try:
            # 获取请求数据
            name = request.data.get('name')
            description = request.data.get('description')
            avatar = request.data.get('avatar')
            embedding_model = request.data.get('embedding_model', 'text-embedding-v3')
            icon = request.data.get('icon')  # 获取图标参数
            
            # 验证必填字段
            if not name:
                return ResponseResult.error(
                    code=400,
                    message='数据集名称不能为空'
                )
            
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            # 获取大分类信息，检查是否为知识库类型，若是则需要验证是否为老师
            try:
                category = KnowledgeCategory.objects.get(id=pk, status='1', deleted_at__isnull=True)
                is_knowledge_type = category.category_type == 'knowledge'
                
                # 验证权限：如果是知识库类型，需要检查是否为老师
                if is_knowledge_type:
                    # 通过查询Teacher表检查当前用户是否为老师
                    is_teacher = Teacher.objects.filter(user_id=user_id, deleted_at__isnull=True).exists()
                    # if not is_teacher:
                    if not is_teacher:
                        return ResponseResult.error(
                            code=403,
                            message='只有老师可以创建知识库'
                        )
            except KnowledgeCategory.DoesNotExist:
                return ResponseResult.error(
                    code=404,
                    message=f'指定的大分类ID {pk} 不存在'
                )
            
            # 创建数据集
            dataset = KnowledgeService.create_dataset(
                category_id=pk,
                name=name,
                description=description,
                avatar=avatar,
                embedding_model=embedding_model,
                icon=icon,  # 传递图标参数
                user_id=user_id  # 传递用户ID
            )
            
            return ResponseResult.success(
                data=dataset,
                message='创建知识库数据集成功'
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'创建知识库数据集失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='datasets/(?P<dataset_id>[^/.]+)/documents')
    def get_dataset_documents(self, request, dataset_id=None):
        """
        获取指定数据集下的所有文档（匹配前端URL格式），支持分页
        
        Args:
            dataset_id: 数据集ID
            
        Query Params:
            page: 当前页码，默认为1
            page_size: 每页记录数，默认为10
        
        Returns:
            Response: 包含文档列表的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            # 验证用户身份
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='请先登录后再查看知识库文档'
                )
            
            # 获取分页参数
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            
            # 参数校验
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:  # 限制每页最大记录数为100
                page_size = 10
                
            # 调用服务获取分页数据，传递用户ID
            documents = KnowledgeService.get_documents_by_dataset(
                dataset_id,
                user_id,
                page=page, 
                page_size=page_size
            )
            
            return ResponseResult.success(
                data=documents,
                message='获取知识库文档列表成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取知识库文档列表失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='all-documents')
    def get_all_documents(self, request):
        """
        获取所有知识库文档，支持分页
        
        Query Params:
            page: 当前页码，默认为1
            page_size: 每页记录数，默认为10
        
        Returns:
            Response: 包含所有文档列表的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            # 验证用户身份
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='请先登录后再查看知识库文档'
                )
            
            # 获取分页参数
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            
            # 参数校验
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:  # 限制每页最大记录数为100
                page_size = 10
                
            # 调用服务获取分页数据
            result = KnowledgeService.get_all_documents(
                user_id,
                page=page, 
                page_size=page_size
            )
            
            return ResponseResult.success(
                data=result,
                message='获取所有知识库文档成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取所有知识库文档失败: {str(e)}'
            )

    @action(detail=False, methods=['post'], url_path='datasets/(?P<dataset_id>[^/.]+)/upload-documents', parser_classes=[MultiPartParser, FormParser])
    def upload_documents(self, request, dataset_id=None):
        """
        上传文档到指定数据集
        
        Args:
            dataset_id: 数据集ID
        
        Returns:
            Response: 包含上传结果的响应
        """
        try:
            # 检查是否有文件上传
            if 'files' not in request.FILES:
                return ResponseResult.error(
                    code=400,
                    message='未找到上传文件'
                )
            
            files = request.FILES.getlist('files')
            if not files:
                return ResponseResult.error(
                    code=400,
                    message='文件列表为空'
                )
            
            # 获取图标参数
            icon = request.POST.get('icon')
            
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            # 检查数据集是否存在以及是否属于知识库大分类
            try:
                dataset = KnowledgeDataset.objects.get(id=dataset_id, status='1', deleted_at__isnull=True)
                category = dataset.category
                
                # 如果是知识库大分类，检查用户是否为老师
                if category and category.category_type == 'knowledge':
                    # 通过查询Teacher表检查当前用户是否为老师
                    is_teacher = Teacher.objects.filter(user_id=user_id, deleted_at__isnull=True).exists()
                    if not is_teacher:
                        return ResponseResult.error(
                            code=403,
                            message='只有老师可以上传知识库文档'
                        )
                
                # 如果是个人文档，检查是否属于当前用户
                elif category and category.category_type == 'personal' and dataset.user_id != user_id:
                    return ResponseResult.error(
                        code=403,
                        message='您无权在此数据集中上传文档'
                    )
            except KnowledgeDataset.DoesNotExist:
                return ResponseResult.error(
                    code=404,
                    message=f'指定的数据集ID {dataset_id} 不存在'
                )
            
            # 上传文档
            result = KnowledgeService.upload_documents(dataset_id, files, icon=icon, user_id=user_id)
            
            return ResponseResult.success(
                data=result,
                message='上传文档成功'
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'上传文档失败: {str(e)}'
            )
    
    @action(detail=False, methods=['post'], url_path='datasets/(?P<dataset_id>[^/.]+)/parse-documents')
    def parse_documents(self, request, dataset_id=None):
        """
        解析指定数据集中的文档
        
        Args:
            dataset_id: 数据集ID
        
        Request Body:
            document_ids: 要解析的文档ID列表
        
        Returns:
            Response: 包含解析结果的响应
        """
        try:
            document_ids = request.data.get('document_ids', [])
            if not document_ids:
                return ResponseResult.error(
                    code=400,
                    message='文档ID列表为空'
                )
            
            # 解析文档
            result = KnowledgeService.parse_documents(dataset_id, document_ids)
            
            return ResponseResult.success(
                data=result,
                message='解析文档成功'
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'解析文档失败: {str(e)}'
            )
    
    @action(detail=False, methods=['get'], url_path='datasets/(?P<dataset_id>[^/.]+)/documents-status')
    def get_dataset_documents_status(self, request, dataset_id=None):
        """
        获取指定数据集中所有文档的解析状态
        
        Args:
            dataset_id: 数据集ID
            
        Query Params:
            document_ids: 可选，逗号分隔的文档ID列表
        
        Returns:
            Response: 包含文档解析状态的响应
        """
        try:
            # 获取要查询的文档ID列表
            document_ids_param = request.query_params.get('document_ids', None)
            document_ids = document_ids_param.split(',') if document_ids_param else None
            
            # 获取文档状态
            results = KnowledgeService.batch_update_document_status(dataset_id, document_ids)
            
            return ResponseResult.success(
                data=results,
                message='获取文档解析状态成功'
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取文档解析状态失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='personal/datasets')
    def get_personal_datasets(self, request):
        """
        获取当前用户的个人文档子分类
        
        Returns:
            Response: 包含用户个人文档子分类列表的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='无法识别当前用户身份'
                )
                
            datasets = KnowledgeService.get_personal_datasets(user_id)
            return ResponseResult.success(
                data=datasets,
                message='获取个人文档子分类成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取个人文档子分类失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='favorites')
    def get_favorites(self, request):
        """
        获取当前用户收藏的文档
        
        Query Params:
            page: 当前页码，默认为1
            page_size: 每页记录数，默认为10
        
        Returns:
            Response: 包含用户收藏文档列表的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='无法识别当前用户身份'
                )
                
            # 获取分页参数
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            
            # 获取用户收藏
            favorites = KnowledgeService.get_user_favorites(user_id, page, page_size)
            
            return ResponseResult.success(
                data=favorites,
                message='获取收藏列表成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取收藏列表失败: {str(e)}'
            )
            
    @action(detail=True, methods=['post'], url_path='documents/(?P<document_id>[^/.]+)/toggle-favorite')
    def toggle_favorite(self, request, pk=None, document_id=None):
        """
        切换文档收藏状态
        
        Args:
            pk: 分类ID（占位参数，不使用）
            document_id: 文档ID
            
        Returns:
            Response: 操作结果
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='无法识别当前用户身份'
                )
                
            # 切换收藏状态
            result = KnowledgeService.toggle_document_favorite(user_id, document_id)
            
            return ResponseResult.success(
                data=result,
                message=result.get('message', '操作成功')
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'操作失败: {str(e)}'
            )

    @action(detail=False, methods=['delete'], url_path='del_datasets/(?P<dataset_id>[^/.]+)/documents')
    def batch_delete_documents(self, request, dataset_id=None):
        """
        批量删除文档，也可用于删除单个文档
        
        Args:
            dataset_id: 数据集ID
            
        Request Body:
            document_ids: 要删除的文档ID列表，可以是单个ID组成的列表(删除单个文档)
        
        Returns:
            Response: 包含删除结果的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='无法识别当前用户身份'
                )
            
            # 获取要删除的文档ID列表
            document_ids = request.data.get('document_ids', [])
            
            if not document_ids:
                return ResponseResult.error(
                    code=400,
                    message='未提供要删除的文档ID列表'
                )
            
            # 调用服务批量删除文档
            result = KnowledgeService.batch_delete_documents(dataset_id, document_ids, user_id)
            
            return ResponseResult.success(
                data=result,
                message=result.get('message', '文档删除成功')
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'删除文档失败: {str(e)}'
            )

    @action(detail=False, methods=['delete'], url_path='del_datasets/(?P<dataset_id>[^/.]+)')
    def delete_dataset(self, request, dataset_id=None):
        """
        删除指定数据集
        
        Args:
            dataset_id: 要删除的数据集ID
            
        Returns:
            Response: 包含删除结果的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='无法识别当前用户身份'
                )
            
            # 调用服务删除数据集
            result = KnowledgeService.delete_dataset(dataset_id, user_id)
            
            return ResponseResult.success(
                data=result,
                message=result.get('message', '数据集删除成功')
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'删除数据集失败: {str(e)}'
            )
            
    @action(detail=False, methods=['put'], url_path='del_datasets/(?P<dataset_id>[^/.]+)/update')
    def update_dataset(self, request, dataset_id=None):
        """
        更新指定数据集的信息
        
        Args:
            dataset_id: 要更新的数据集ID
            
        Request Body:
            name: 数据集名称（可选）
            description: 数据集描述（可选）
            icon: 数据集图标（可选）
            avatar: 数据集头像（可选，Base64编码）
            
        Returns:
            Response: 包含更新结果的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='无法识别当前用户身份'
                )
            
            # 获取更新数据
            update_data = {}
            
            if 'name' in request.data and request.data['name']:
                update_data['name'] = request.data['name']
                
            if 'description' in request.data:
                update_data['description'] = request.data['description']
                
            if 'icon' in request.data and request.data['icon']:
                update_data['icon'] = request.data['icon']
                
            if 'avatar' in request.data:
                update_data['avatar'] = request.data['avatar']
            
            # 如果没有提供任何更新字段，返回错误
            if not update_data:
                return ResponseResult.error(
                    code=400,
                    message='未提供任何要更新的字段'
                )
            
            # 调用服务更新数据集
            result = KnowledgeService.update_dataset(dataset_id, update_data, user_id)
            
            return ResponseResult.success(
                data=result,
                message=result.get('message', '数据集更新成功')
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'更新数据集失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='search')
    def search_documents(self, request):
        """
        搜索文档
        
        Query Params:
            q: 搜索关键词
            page: 当前页码，默认为1
            page_size: 每页记录数，默认为10
            dataset_id: 可选，限定特定数据集搜索
            document_type: 可选，限定文档类型
            
        Returns:
            Response: 包含搜索结果的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            # 验证用户身份
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='请先登录后再搜索知识库文档'
                )
                
            # 获取搜索关键词
            search_query = request.query_params.get('q', '')
            
            # 获取分页参数
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            
            # 获取过滤参数
            dataset_id = request.query_params.get('dataset_id')
            document_type = request.query_params.get('document_type')
            
            # 参数校验
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:  # 限制每页最大记录数为100
                page_size = 10
                
            # 调用服务层搜索文档
            search_results = KnowledgeService.search_documents(
                search_query=search_query,
                user_id=user_id,
                page=page,
                page_size=page_size,
                dataset_id=dataset_id,
                document_type=document_type
            )
            
            return ResponseResult.success(
                data=search_results,
                message='搜索文档成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'搜索文档失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='documents/(?P<document_id>[^/.]+)/file-url')
    def get_document_file_url(self, request, document_id=None):
        """
        获取知识库文档的文件URL
        
        Args:
            document_id: 文档ID
            
        Returns:
            Response: 包含文件URL的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id if hasattr(request, 'user') and hasattr(request.user, 'id') else None
            
            # 验证用户身份
            if not user_id:
                return ResponseResult.error(
                    code=401,
                    message='请先登录后再查看文件'
                )
                
            # 调用服务获取文件URL
            result = KnowledgeService.get_document_file_url(document_id, user_id)
            
            return ResponseResult.success(
                data=result,
                message='获取文件URL成功'
            )
        except ValueError as e:
            return ResponseResult.error(
                code=400,
                message=str(e)
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取文件URL失败: {str(e)}'
            )

