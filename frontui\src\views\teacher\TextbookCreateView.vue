<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="新建教材项目"
    activePage="content-creation"
    activeSubPage="textbook-create"
  >
    <div class="max-w-6xl mx-auto">
      <!-- 顶部栏：操作按钮 -->
      <div class="flex justify-between items-center mb-6">
        <!-- 左侧可放置状态提示区域，目前使用空白区 -->
        <div class="w-3/4">
          <!-- 创建时可以放提示信息 -->
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex gap-2">
          <button 
            class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center gap-2 text-sm shadow-sm"
            @click="goBackToList"
          >
            <i class="material-icons text-sm">arrow_back</i>
            返回列表
          </button>

          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2 text-sm shadow-sm"
            @click="saveAndReturn"
          >
            <i class="material-icons text-sm">save</i>
            保存并返回
          </button>
          

        </div>
      </div>

      <!-- 步骤指示器 -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div 
            v-for="(step, index) in steps" 
            :key="index" 
            class="flex items-center"
            :class="{'flex-1': index < steps.length - 1}"
          >
            <div class="flex items-center">
              <div 
                class="w-10 h-10 rounded-full flex items-center justify-center text-white font-medium"
                :class="[
                  currentStep > index 
                    ? 'bg-green-500' 
                    : currentStep === index 
                      ? 'bg-blue-600' 
                      : 'bg-gray-300'
                ]"
              >
                <span v-if="currentStep > index" class="material-icons text-sm">check</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="ml-2 text-sm font-medium" :class="{'text-blue-600': currentStep === index}">
                {{ step.name }}
              </div>
            </div>
            <div 
              v-if="index < steps.length - 1" 
              class="flex-1 h-1 mx-2"
              :class="[
                currentStep > index 
                  ? 'bg-green-500' 
                  : 'bg-gray-300'
              ]"
            ></div>
          </div>
        </div>
      </div>

      <!-- 步骤内容 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <!-- 步骤1: 基本信息 -->
        <div v-if="currentStep === 0">
          <h2 class="text-xl font-bold mb-6">基本信息</h2>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧面板：基础配置 -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">description</i>
                    教材基本配置
                  </h3>
                </div>
                
                <div class="p-4 space-y-5">
                  <!-- 教材名称 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">title</i>
                      教材名称
                    </label>
                    <input 
                      v-model="textbookData.title" 
                      type="text" 
                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="输入教材名称..."
                    />
                    <div class="text-xs text-gray-500 mt-1 flex items-center">
                      <i class="material-icons text-xs mr-1">info</i>
                      清晰简洁的名称有助于更好地归类和查找
                    </div>
                  </div>
                  
                  <!-- 学科选择 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">category</i>
                      所属学科
                    </label>
                    <div class="relative">
                      <select 
                        v-model="textbookData.subject" 
                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                      >
                        <option value="">请选择学科</option>
                        <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                          {{ subject.name }}
                        </option>
                      </select>
                      <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="material-icons text-gray-400">arrow_drop_down</i>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 教材简介 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">notes</i>
                      教材简介
                    </label>
                    <textarea
                      v-model="textbookData.description"
                      rows="3"
                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="简要描述教材内容和适用对象..."
                    ></textarea>
                  </div>
                  
                  <!-- 下一步按钮 -->
                  <button 
                    @click="nextStep" 
                    :disabled="!canProceed"
                    :class="{'opacity-50 cursor-not-allowed': !canProceed}"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md text-sm font-medium flex items-center justify-center gap-2 transition-all duration-200 transform hover:scale-105"
                  >
                    <i class="material-icons">arrow_forward</i>
                    下一步：目录生成
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 右侧面板：参考资料 -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">folder_open</i>
                    参考资料导入
                  </h3>
                </div>
                
                <div class="p-6">
                  <div 
                    class="border-2 border-dashed border-gray-300 rounded-lg p-8 bg-gray-50 flex flex-col items-center justify-center"
                    @dragover.prevent="dragover = true"
                    @dragleave.prevent="dragover = false"
                    @drop.prevent="onFileDrop"
                    :class="{'border-blue-400 bg-blue-50': dragover}"
                  >
                    <div class="bg-blue-100 rounded-full p-4 mb-4">
                      <i class="material-icons text-blue-500 text-3xl">cloud_upload</i>
                    </div>
                    <h4 class="text-gray-700 text-lg mb-2">拖拽文件到此处或点击上传</h4>
                    <p class="text-gray-500 text-sm max-w-md mb-6 text-center">
                      上传您的参考资料，系统将自动分析内容并辅助教材生成。推荐上传包含完整内容结构的文档。
                    </p>
                    <button @click="triggerFileUpload" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2">
                      <i class="material-icons text-sm">file_upload</i>
                      选择文件
                    </button>
                    <input 
                      type="file" 
                      ref="fileInput" 
                      @change="handleFileUpload"
                      class="hidden" 
                      accept=".pdf,.doc,.docx,.txt"
                    />

                    <!-- 文件类型说明 -->
                    <div class="grid grid-cols-3 gap-3 w-full mt-8" v-if="!uploadedFiles.length">
                      <div class="text-center">
                        <div class="bg-green-100 rounded-full p-2 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                          <i class="material-icons text-green-600">description</i>
                        </div>
                        <p class="text-xs text-gray-600">PDF文档</p>
                      </div>
                      <div class="text-center">
                        <div class="bg-blue-100 rounded-full p-2 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                          <i class="material-icons text-blue-600">article</i>
                        </div>
                        <p class="text-xs text-gray-600">Word文档</p>
                      </div>
                      <div class="text-center">
                        <div class="bg-gray-100 rounded-full p-2 mx-auto w-12 h-12 flex items-center justify-center mb-2">
                          <i class="material-icons text-gray-600">text_snippet</i>
                        </div>
                        <p class="text-xs text-gray-600">文本文件</p>
                      </div>
                    </div>

                    <!-- 已上传文件列表 -->
                    <div class="w-full mt-6" v-if="uploadedFiles.length">
                      <h5 class="font-medium text-sm mb-3 text-gray-700">已上传文件</h5>
                      <div class="space-y-2">
                        <div 
                          v-for="(file, index) in uploadedFiles" 
                          :key="index"
                          class="border border-gray-200 rounded-md p-3 flex items-center bg-white"
                        >
                          <div class="rounded-full p-2 mr-3" :class="getFileIconClass(file.type)">
                            <i class="material-icons" :class="getFileIconColorClass(file.type)">{{ getFileIcon(file.type) }}</i>
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                              <p class="text-sm font-medium text-gray-700 truncate" :title="file.name">{{ file.name }}</p>
                              <span class="text-xs text-gray-500 ml-2">{{ formatFileSize(file.size) }}</span>
                            </div>
                            
                            <!-- 上传状态 -->
                            <div class="mt-1">
                              <div v-if="file.status === 'uploading'" class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-1.5 mr-2">
                                  <div class="bg-blue-600 h-1.5 rounded-full" :style="`width: ${file.progress}%`"></div>
                                </div>
                                <span class="text-xs text-gray-500">{{ file.progress }}%</span>
                              </div>
                              <div v-else-if="file.status === 'success'" class="flex items-center text-green-600 text-xs">
                                <i class="material-icons text-xs mr-1">check_circle</i>
                                <span>已上传</span>
                              </div>
                              <div v-else-if="file.status === 'error'" class="flex items-center text-red-600 text-xs">
                                <i class="material-icons text-xs mr-1">error</i>
                                <span>{{ file.error || '上传失败' }}</span>
                              </div>
                            </div>
                          </div>
                          <button 
                            @click="removeFile(index)" 
                            class="ml-2 text-gray-400 hover:text-red-500"
                            :disabled="file.status === 'uploading'"
                            :class="{'opacity-50 cursor-not-allowed': file.status === 'uploading'}"
                          >
                            <i class="material-icons">delete</i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="mt-4 text-xs text-gray-500 flex items-center">
                    <i class="material-icons text-xs mr-2">info</i>
                    支持格式：PDF, DOCX, TXT（最大100MB）
                  </div>
                  <div v-if="fileError" class="mt-2 text-xs text-red-500 flex items-center">
                    <i class="material-icons text-xs mr-2">error</i>
                    {{ fileError }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 步骤2: 目录生成与确认 -->
        <div v-if="currentStep === 1">
          <h2 class="text-xl font-bold mb-6">目录生成与确认</h2>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧面板：生成配置 -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">settings</i>
                    目录生成配置
                  </h3>
                </div>
                
                <div class="p-4 space-y-5">
                  <!-- 关键词/主题词 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">label</i>
                      关键词/主题词
                    </label>
                    <div class="flex items-center gap-2">
                      <input 
                        v-model="keyword" 
                        type="text" 
                        class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入关键词..."
                      />
                      <button 
                        @click="addKeyword" 
                        class="px-3 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md text-sm flex items-center"
                      >
                        <i class="material-icons text-sm mr-1">add</i>
                        添加
                      </button>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mt-2">
                      <div 
                        v-for="(kw, idx) in keywords" 
                        :key="idx" 
                        class="px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-xs flex items-center"
                      >
                        {{ kw }}
                        <button @click="removeKeyword(idx)" class="ml-1 text-blue-500 hover:text-blue-700">
                          <i class="material-icons text-xs">close</i>
                        </button>
                      </div>
                      <div v-if="keywords.length === 0" class="text-xs text-gray-500 italic px-1">
                        添加关键词以提高目录生成质量
                      </div>
                    </div>
                  </div>
                  
                  <!-- 章节数量设置 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">book</i>
                      章节数量范围
                    </label>
                    <div class="flex items-center">
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${(textbookData.minChapters/20)*100}%`"></div>
                      </div>
                    </div>
                    <div class="flex items-center justify-between mt-2">
                      <div class="flex items-center">
                        <button 
                          @click="textbookData.minChapters = Math.max(1, textbookData.minChapters - 1)"
                          class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                        >
                          <i class="material-icons text-xs">remove</i>
                        </button>
                        <input 
                          v-model="textbookData.minChapters" 
                          type="number" 
                          min="1"
                          class="w-12 mx-1 border border-gray-300 rounded text-center py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <button 
                          @click="textbookData.minChapters++"
                          class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                        >
                          <i class="material-icons text-xs">add</i>
                        </button>
                      </div>
                      
                      <span class="text-sm text-gray-500 mx-2">至</span>
                      
                      <div class="flex items-center">
                        <button 
                          @click="textbookData.maxChapters = Math.max(textbookData.minChapters, textbookData.maxChapters - 1)"
                          class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                        >
                          <i class="material-icons text-xs">remove</i>
                        </button>
                        <input 
                          v-model="textbookData.maxChapters" 
                          type="number"
                          min="1"
                          class="w-12 mx-1 border border-gray-300 rounded text-center py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <button 
                          @click="textbookData.maxChapters++"
                          class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                        >
                          <i class="material-icons text-xs">add</i>
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 生成规则 -->
                  <div>
                    <div class="flex justify-between items-center mb-2">
                      <label class="flex items-center text-sm font-medium text-gray-700">
                        <i class="material-icons text-gray-400 text-sm mr-1">rule</i>
                        生成规则
                        <input 
                          type="checkbox" 
                          v-model="applyRules"
                          class="form-checkbox text-blue-600 h-4 w-4 ml-2"
                        />
                      </label>
                    </div>
                    
                    <div v-if="applyRules" class="border border-gray-200 rounded-lg p-2 bg-gray-50 max-h-32 overflow-y-auto">
                      <div v-for="(rule, idx) in rules" :key="idx" class="flex items-center justify-between mb-1 last:mb-0">
                        <div class="flex items-center gap-1">
                          <input 
                            type="checkbox" 
                            :checked="true" 
                            class="form-checkbox text-blue-600 h-3 w-3"
                          />
                          <span class="text-xs text-gray-700">{{ rule }}</span>
                        </div>
                        <button @click="removeRule(idx)" class="text-red-500 hover:text-red-700">
                          <i class="material-icons text-xs">close</i>
                        </button>
                      </div>
                      
                      <!-- 添加自定义规则 -->
                      <div class="flex items-center gap-1 mt-2 border-t border-gray-200 pt-2">
                        <input 
                          v-model="newRule" 
                          type="text" 
                          class="flex-1 border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="添加自定义规则..."
                          @keyup.enter="addRule"
                        />
                        <button 
                          @click="addRule" 
                          class="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs"
                        >
                          <i class="material-icons text-xs">add</i>
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 生成目录按钮 -->
                  <button 
                    @click="generateToc" 
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md text-sm font-medium flex items-center justify-center gap-2 transition-all duration-200 transform hover:scale-105"
                  >
                    <i class="material-icons">auto_awesome</i>
                    生成目录
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 右侧面板：目录结构 -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">format_list_bulleted</i>
                    目录结构
                  </h3>
                  <div v-if="tocGenerated" class="text-xs text-gray-500 flex items-center">
                    <i class="material-icons text-xs mr-1">drag_indicator</i>
                    拖拽可调整顺序
                  </div>
                </div>
                
                <!-- 未生成目录时的提示 -->
                <div v-if="!tocGenerated" class="p-8 flex flex-col items-center justify-center text-center h-[400px]">
                  <div class="bg-gray-100 rounded-full p-3 mb-4">
                    <i class="material-icons text-gray-400 text-4xl">description</i>
                  </div>
                  <h4 class="text-gray-700 text-lg mb-2">尚未生成目录</h4>
                  <p class="text-gray-500 text-sm max-w-md mb-6">
                    请在左侧设置关键词、章节数量和规则，然后点击"生成目录"按钮自动创建内容结构
                  </p>
                  <button 
                    @click="generateToc" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium inline-flex items-center gap-2"
                  >
                    <i class="material-icons text-sm">auto_awesome</i>
                    立即生成目录
                  </button>
                </div>
                
                <!-- 生成的目录 -->
                <div v-if="tocGenerated" class="p-4 h-[400px] overflow-y-auto">
                  <div class="space-y-3">
                    <div v-for="(chapter, chIdx) in textbookData.toc" :key="'ch'+chIdx" class="toc-item">
                      <!-- 章节标题 -->
                      <div class="flex items-center gap-2 py-2 px-3 bg-gray-50 border border-gray-200 hover:bg-gray-100 rounded-md">
                        <div class="w-7 h-7 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 text-sm mr-1">
                          {{ chIdx + 1 }}
                        </div>
                        <div class="flex-1">
                          <input 
                            v-model="chapter.title" 
                            class="w-full bg-transparent focus:outline-none focus:border-b border-blue-400 font-medium"
                            :placeholder="`第${chIdx+1}章`"
                          />
                        </div>
                        <div class="flex items-center gap-1">
                          <button @click="addSection(chIdx)" class="text-blue-600 hover:text-blue-800 w-6 h-6 rounded-full hover:bg-blue-100 flex items-center justify-center">
                            <i class="material-icons text-sm">add</i>
                          </button>
                          <button @click="removeChapter(chIdx)" class="text-red-600 hover:text-red-800 w-6 h-6 rounded-full hover:bg-red-100 flex items-center justify-center">
                            <i class="material-icons text-sm">delete</i>
                          </button>
                          <i class="material-icons text-gray-400 cursor-move">drag_indicator</i>
                        </div>
                      </div>
                      
                      <!-- 小节 -->
                      <div class="ml-8 mt-2 space-y-2">
                        <div v-for="(section, secIdx) in chapter.sections" :key="'sec'+secIdx" class="toc-section">
                          <div class="flex items-center gap-2 py-1.5 px-3 rounded-md hover:bg-gray-50 border border-gray-100">
                            <div class="w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 text-xs">
                              {{ secIdx + 1 }}
                            </div>
                            <div class="flex-1">
                              <input 
                                v-model="section.title" 
                                class="w-full bg-transparent focus:outline-none focus:border-b border-gray-300"
                                :placeholder="`${chIdx+1}.${secIdx+1} 小节标题`"
                              />
                            </div>
                            <button @click="removeSection(chIdx, secIdx)" class="text-red-500 hover:text-red-700 w-5 h-5 rounded-full hover:bg-red-50 flex items-center justify-center">
                              <i class="material-icons text-xs">close</i>
                            </button>
                            <i class="material-icons text-gray-300 text-sm cursor-move">drag_indicator</i>
                          </div>
                        </div>
                        
                        <!-- 添加小节提示 -->
                        <div v-if="chapter.sections.length === 0" class="text-center py-2">
                          <button 
                            @click="addSection(chIdx)" 
                            class="text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center mx-auto"
                          >
                            <i class="material-icons text-sm mr-1">add</i>
                            添加小节
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 添加章节按钮 -->
                  <button 
                    @click="addChapter" 
                    class="mt-4 flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium border border-dashed border-blue-300 rounded-md py-2 px-4 hover:bg-blue-50 w-full justify-center"
                  >
                    <i class="material-icons text-base">add_circle</i>
                    添加新章节
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 步骤3: 内容创建 -->
        <div v-if="currentStep === 2">
          <h2 class="text-xl font-bold mb-6">内容创建</h2>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧面板：章节导航 -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">menu_book</i>
                    章节导航
                  </h3>
                </div>
                
                <div class="p-2 h-[600px] overflow-y-auto">
                  <div class="space-y-1">
                    <div v-for="(chapter, chIdx) in textbookData.toc" :key="'nav'+chIdx">
                      <!-- 章节标题 -->
                      <button 
                        class="w-full text-left py-2 px-3 rounded-md flex items-center gap-2 font-medium transition-colors"
                        :class="[
                          currentChapter === chIdx
                            ? 'bg-blue-100 text-blue-700'
                            : 'hover:bg-gray-100 text-gray-700'
                        ]"
                        @click="selectChapter(chIdx, -1)"
                      >
                        <div class="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                          {{ chIdx + 1 }}
                        </div>
                        <span class="truncate">{{ chapter.title || `第${chIdx+1}章` }}</span>
                        <i class="material-icons text-gray-400 ml-auto">
                          {{ currentChapter === chIdx ? 'expand_less' : 'expand_more' }}
                        </i>
                      </button>
                      
                      <!-- 小节列表 -->
                      <div v-if="currentChapter === chIdx" class="ml-8 mt-1 space-y-1 mb-2">
                        <button 
                          v-for="(section, secIdx) in chapter.sections" 
                          :key="'nav-sec'+secIdx"
                          class="w-full text-left py-1.5 px-3 rounded-md flex items-center gap-2 text-sm transition-colors"
                          :class="[
                            currentSection === secIdx
                              ? 'bg-gray-200 text-gray-800'
                              : 'hover:bg-gray-100 text-gray-600'
                          ]"
                          @click="selectChapter(chIdx, secIdx)"
                        >
                          <span class="truncate">{{ section.title || `${chIdx+1}.${secIdx+1} 小节` }}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 进度指示 -->
                  <div class="mt-4 p-3 bg-gray-50 rounded-md border border-gray-100">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-xs font-medium text-gray-700">完成进度</span>
                      <span class="text-xs text-blue-600">20%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-blue-600 h-2 rounded-full" style="width: 20%"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 右侧面板：内容编辑 -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">edit</i>
                    内容编辑
                  </h3>
                  <div class="flex items-center gap-3">
                    <button class="text-gray-700 hover:text-blue-600 flex items-center text-xs">
                      <i class="material-icons text-sm mr-1">auto_awesome</i>
                      AI生成
                    </button>
                    <button class="text-gray-700 hover:text-blue-600 flex items-center text-xs">
                      <i class="material-icons text-sm mr-1">save</i>
                      保存
                    </button>
                  </div>
                </div>
                
                <div class="p-6 h-[550px] overflow-y-auto">
                  <!-- 章节显示 -->
                  <div v-if="currentSection === -1">
                    <div v-if="currentChapter !== null && textbookData.toc[currentChapter]" class="mb-6">
                      <div class="flex items-center gap-2 mb-4">
                        <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
                          {{ currentChapter + 1 }}
                        </div>
                        <input 
                          v-model="textbookData.toc[currentChapter].title" 
                          class="text-2xl font-bold focus:outline-none focus:border-b-2 border-blue-400 w-full"
                          :placeholder="`第${currentChapter+1}章`"
                        />
                      </div>
                      
                      <!-- 章节概述 -->
                      <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">章节概述</label>
                        <textarea
                          v-model="chapterOverview"
                          rows="3"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="简要概述本章内容和学习目标..."
                        ></textarea>
                      </div>
                      
                      <!-- 小节列表 -->
                      <div class="mt-8">
                        <h4 class="text-lg font-medium mb-3 flex items-center">
                          <i class="material-icons text-gray-400 text-sm mr-2">format_list_numbered</i>
                          章节内容
                        </h4>
                        <div class="space-y-3">
                          <div 
                            v-for="(section, secIdx) in textbookData.toc[currentChapter].sections" 
                            :key="'edit-sec'+secIdx"
                            class="border border-gray-200 rounded-md p-3 hover:bg-gray-50 transition-colors"
                          >
                            <div class="flex items-center justify-between">
                              <div class="flex items-center gap-2">
                                <div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 text-xs">
                                  {{ secIdx + 1 }}
                                </div>
                                <h5 class="font-medium">{{ section.title || `${currentChapter+1}.${secIdx+1} 小节` }}</h5>
                              </div>
                              <div class="flex items-center gap-2">
                                <span class="text-xs text-gray-400">未完成</span>
                                <button 
                                  @click="selectChapter(currentChapter, secIdx)"
                                  class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                                >
                                  <i class="material-icons text-sm mr-1">edit</i>
                                  编辑
                                </button>
                              </div>
                            </div>
                          </div>
                          
                          <!-- 添加小节 -->
                          <button 
                            @click="addSection(currentChapter)"
                            class="w-full border border-dashed border-gray-300 rounded-md py-2 text-blue-600 hover:bg-blue-50 hover:border-blue-300 transition-colors flex items-center justify-center"
                          >
                            <i class="material-icons text-sm mr-1">add</i>
                            添加小节
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 选择章节提示 -->
                    <div v-else class="flex flex-col items-center justify-center h-full text-center py-16">
                      <div class="bg-gray-100 rounded-full p-4 mb-4">
                        <i class="material-icons text-gray-400 text-3xl">menu_book</i>
                      </div>
                      <h4 class="text-lg text-gray-700 mb-2">请从左侧选择章节</h4>
                      <p class="text-gray-500 text-sm max-w-md">
                        从左侧导航中选择要编辑的章节或小节，开始创建教材内容
                      </p>
                    </div>
                  </div>
                  
                  <!-- 小节编辑 -->
                  <div v-else-if="currentChapter !== null && currentSection !== null">
                    <div class="mb-6">
                      <div class="flex items-start gap-4 mb-4">
                        <div>
                          <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm mb-1">
                            {{ currentChapter + 1 }}
                          </div>
                          <div class="w-6 h-6 rounded-full bg-gray-300 mx-auto flex items-center justify-center text-gray-700 text-xs">
                            {{ currentSection + 1 }}
                          </div>
                        </div>
                        <div class="flex-1">
                          <div class="text-sm text-gray-500 mb-1">
                            {{ textbookData.toc[currentChapter].title || `第${currentChapter+1}章` }}
                          </div>
                          <input 
                            v-model="textbookData.toc[currentChapter].sections[currentSection].title" 
                            class="text-xl font-bold focus:outline-none focus:border-b-2 border-blue-400 w-full"
                            :placeholder="`${currentChapter+1}.${currentSection+1} 小节标题`"
                          />
                        </div>
                      </div>
                      
                      <!-- 内容编辑器 -->
                      <div class="mt-6">
                        <div class="border border-gray-300 rounded-md overflow-hidden">
                          <!-- 编辑器工具栏 -->
                          <div class="bg-gray-50 border-b border-gray-300 px-3 py-1.5 flex items-center gap-1">
                            <button class="p-1 rounded hover:bg-gray-200">
                              <i class="material-icons text-gray-700 text-lg">format_bold</i>
                            </button>
                            <button class="p-1 rounded hover:bg-gray-200">
                              <i class="material-icons text-gray-700 text-lg">format_italic</i>
                            </button>
                            <button class="p-1 rounded hover:bg-gray-200">
                              <i class="material-icons text-gray-700 text-lg">format_underlined</i>
                            </button>
                            <span class="border-l border-gray-300 h-6 mx-1"></span>
                            <button class="p-1 rounded hover:bg-gray-200">
                              <i class="material-icons text-gray-700 text-lg">format_list_bulleted</i>
                            </button>
                            <button class="p-1 rounded hover:bg-gray-200">
                              <i class="material-icons text-gray-700 text-lg">format_list_numbered</i>
                            </button>
                            <span class="border-l border-gray-300 h-6 mx-1"></span>
                            <button class="p-1 rounded hover:bg-gray-200">
                              <i class="material-icons text-gray-700 text-lg">image</i>
                            </button>
                            <button class="p-1 rounded hover:bg-gray-200">
                              <i class="material-icons text-gray-700 text-lg">table_chart</i>
                            </button>
                            <div class="ml-auto flex items-center gap-2">
                              <button class="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm hover:bg-blue-200 flex items-center">
                                <i class="material-icons text-sm mr-1">auto_awesome</i>
                                AI生成
                              </button>
                            </div>
                          </div>
                          <!-- 编辑区域 -->
                          <textarea
                            v-model="sectionContent"
                            rows="15"
                            class="w-full px-4 py-3 focus:outline-none"
                            placeholder="编辑小节内容..."
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 步骤4: 教材导出 -->
        <div v-if="currentStep === 3">
          <h2 class="text-xl font-bold mb-6">教材导出</h2>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧面板：导出配置 -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">settings</i>
                    导出配置
                  </h3>
                </div>
                
                <div class="p-4 space-y-5">
                  <!-- 导出格式选择 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">description</i>
                      文件格式
                    </label>
                    <div class="grid grid-cols-2 gap-3">
                      <label class="border border-gray-300 rounded-md p-3 flex items-center cursor-pointer hover:bg-gray-50 transition-colors"
                         :class="{'bg-blue-50 border-blue-400': exportFormat === 'pdf'}"
                      >
                        <input type="radio" v-model="exportFormat" value="pdf" class="hidden" />
                        <div class="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                          <i class="material-icons text-red-600">picture_as_pdf</i>
                        </div>
                        <div>
                          <div class="font-medium text-sm">PDF</div>
                          <div class="text-xs text-gray-500">适合打印和跨平台阅读</div>
                        </div>
                      </label>
                      
                      <label class="border border-gray-300 rounded-md p-3 flex items-center cursor-pointer hover:bg-gray-50 transition-colors"
                         :class="{'bg-blue-50 border-blue-400': exportFormat === 'docx'}"
                      >
                        <input type="radio" v-model="exportFormat" value="docx" class="hidden" />
                        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                          <i class="material-icons text-blue-600">article</i>
                        </div>
                        <div>
                          <div class="font-medium text-sm">Word</div>
                          <div class="text-xs text-gray-500">适合二次编辑</div>
                        </div>
                      </label>
                    </div>
                  </div>
                  
                  <!-- 封面设置 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">book</i>
                      封面设置
                    </label>
                    
                    <div class="space-y-3">
                      <label class="flex items-center">
                        <input type="checkbox" v-model="includeCover" class="form-checkbox text-blue-600 h-4 w-4" />
                        <span class="ml-2 text-sm text-gray-700">添加封面</span>
                      </label>
                      
                      <div v-if="includeCover" class="border border-gray-200 rounded-md p-3 bg-gray-50">
                        <div class="flex items-center justify-between mb-3">
                          <span class="text-xs font-medium text-gray-700">封面模板</span>
                          <button class="text-xs text-blue-600 hover:text-blue-800">自定义</button>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2">
                          <div 
                            v-for="template in coverTemplates" 
                            :key="template.id"
                            class="border rounded-md overflow-hidden cursor-pointer transition-transform hover:scale-105"
                            :class="{'ring-2 ring-blue-500': selectedCoverTemplate === template.id}"
                            @click="selectedCoverTemplate = template.id"
                          >
                            <div class="bg-gray-200 h-20 flex items-center justify-center text-gray-500">
                              <i class="material-icons">image</i>
                            </div>
                            <div class="p-1 text-center text-xs">{{ template.name }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 目录设置 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">list</i>
                      目录设置
                    </label>
                    
                    <div class="space-y-2">
                      <label class="flex items-center">
                        <input type="checkbox" v-model="includeTableOfContents" class="form-checkbox text-blue-600 h-4 w-4" />
                        <span class="ml-2 text-sm text-gray-700">包含目录页</span>
                      </label>
                      
                      <label class="flex items-center">
                        <input type="checkbox" v-model="includePageNumbers" class="form-checkbox text-blue-600 h-4 w-4" />
                        <span class="ml-2 text-sm text-gray-700">显示页码</span>
                      </label>
                    </div>
                  </div>
                  
                  <!-- 水印设置 -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <i class="material-icons text-gray-400 text-sm mr-1">water_drop</i>
                      水印设置
                    </label>
                    
                    <div class="space-y-2">
                      <label class="flex items-center">
                        <input type="checkbox" v-model="includeWatermark" class="form-checkbox text-blue-600 h-4 w-4" />
                        <span class="ml-2 text-sm text-gray-700">添加水印</span>
                      </label>
                      
                      <input 
                        v-if="includeWatermark"
                        v-model="watermarkText" 
                        type="text" 
                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入水印文字..."
                      />
                    </div>
                  </div>
                  
                  <!-- 导出按钮 -->
                  <button 
                    @click="exportTextbook" 
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md text-sm font-medium flex items-center justify-center gap-2 transition-all duration-200 transform hover:scale-105"
                  >
                    <i class="material-icons">download</i>
                    导出教材
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 右侧面板：预览 -->
            <div class="lg:col-span-2">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden">
                <div class="bg-blue-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                  <h3 class="text-md font-medium text-gray-800 flex items-center">
                    <i class="material-icons text-blue-500 mr-2">visibility</i>
                    预览
                  </h3>
                  <div class="flex items-center gap-2">
                    <button class="text-gray-700 hover:text-blue-600 flex items-center text-xs">
                      <i class="material-icons text-sm mr-1">print</i>
                      打印
                    </button>
                    <button class="text-gray-700 hover:text-blue-600 flex items-center text-xs">
                      <i class="material-icons text-sm mr-1">refresh</i>
                      刷新预览
                    </button>
                  </div>
                </div>
                
                <div class="p-6 h-[550px] bg-gray-100 flex justify-center overflow-hidden">
                  <div class="relative shadow-xl bg-white max-w-md w-full h-[500px] transition-all duration-300" :class="{'rounded-lg': exportFormat === 'pdf'}">
                    <!-- 封面 -->
                    <div v-if="includeCover" class="absolute inset-0 bg-gray-800 flex flex-col items-center justify-center px-8 text-white">
                      <div class="text-2xl font-bold mb-2 text-center">{{ textbookData.title || '教材标题' }}</div>
                      <div class="h-0.5 w-16 bg-white my-4"></div>
                      <div class="text-lg opacity-80">{{ subjects.find(s => s.id === textbookData.subject)?.name }}</div>
                      <div class="mt-auto mb-8 text-sm opacity-70">教师：{{ teacherData.name }}</div>
                    </div>
                    
                    <!-- 目录预览 -->
                    <div v-else-if="includeTableOfContents" class="absolute inset-0 bg-white p-8">
                      <div class="text-xl font-bold mb-6 text-center">目录</div>
                      <div class="space-y-4">
                        <div v-for="(chapter, chIdx) in textbookData.toc" :key="'prev-'+chIdx" class="space-y-1">
                          <div class="flex items-center">
                            <span class="font-medium">第{{ chIdx + 1 }}章 {{ chapter.title }}</span>
                            <div v-if="includePageNumbers" class="flex-1 border-b border-dotted border-gray-300 mx-2"></div>
                            <span v-if="includePageNumbers" class="text-gray-500">{{ chIdx + 1 }}</span>
                          </div>
                          
                          <div v-for="(section, secIdx) in chapter.sections" :key="'prev-sec-'+secIdx" class="ml-4 flex items-center">
                            <span>{{ chIdx + 1 }}.{{ secIdx + 1 }} {{ section.title }}</span>
                            <div v-if="includePageNumbers" class="flex-1 border-b border-dotted border-gray-300 mx-2"></div>
                            <span v-if="includePageNumbers" class="text-gray-500">{{ chIdx + 1 }}.{{ secIdx + 1 }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 内容预览 -->
                    <div v-else class="absolute inset-0 bg-white p-8 flex flex-col">
                      <div class="text-lg font-bold mb-4">第1章 {{ textbookData.toc[0]?.title || '章节标题' }}</div>
                      <div class="space-y-2 text-sm">
                        <p>这里是教材内容的预览，实际导出的文档将包含所有您编辑的章节和小节内容。</p>
                        <p>根据您的设置，导出的文件将采用合适的格式和布局，便于阅读和学习。</p>
                      </div>
                      
                      <!-- 水印显示 -->
                      <div v-if="includeWatermark" class="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div class="text-2xl text-gray-200 opacity-20 transform rotate-45 font-bold">
                          {{ watermarkText || teacherData.name }}
                        </div>
                      </div>
                      
                      <!-- 页码 -->
                      <div v-if="includePageNumbers" class="mt-auto text-center text-gray-500 text-sm">
                        - 1 -
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤操作按钮 - 转移主要的返回/保存功能到顶部，这里只保留步骤控制按钮 -->
      <div class="flex justify-between mt-6">
        <div>
          <button 
            v-if="currentStep > 0" 
            @click="currentStep--" 
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            上一步
          </button>
        </div>
        
        <div>          
          <button 
            v-if="currentStep < steps.length - 1" 
            @click="nextStep" 
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            :disabled="!canProceed"
            :class="{'opacity-50 cursor-not-allowed': !canProceed}"
          >
            下一步
          </button>
          
          <button 
            v-else 
            @click="createTextbook" 
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            创建教材
          </button>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

const router = useRouter()

// 教师数据
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar
})

// 步骤定义
const steps = [
  { name: '基本信息', completed: false },
  { name: '目录生成', completed: false },
  { name: '内容创建', completed: false },
  { name: '教材导出', completed: false }
]

const currentStep = ref(0)

// 教材数据
const textbookData = ref({
  title: '',
  subject: '',
  minChapters: 5,
  maxChapters: 10,
  toc: []
})

// 学科列表（模拟数据）
const subjects = ref([
  { id: 'computer', name: '计算机科学' },
  { id: 'math', name: '数学' },
  { id: 'english', name: '英语' },
  { id: 'management', name: '工商管理' },
  { id: 'ai', name: '人工智能' }
])

// 关键词相关
const keyword = ref('')
const keywords = ref([])

const addKeyword = () => {
  if (keyword.value.trim() && !keywords.value.includes(keyword.value.trim())) {
    keywords.value.push(keyword.value.trim())
    keyword.value = ''
  }
}

const removeKeyword = (index) => {
  keywords.value.splice(index, 1)
}

// 规则应用
const applyRules = ref(true)
const rules = ref([
  '教材需包含章节小结',
  '每章需有思考题'
])
const newRule = ref('')

const addRule = () => {
  if (newRule.value.trim()) {
    rules.value.push(newRule.value.trim())
    newRule.value = ''
  }
}

const removeRule = (index) => {
  rules.value.splice(index, 1)
}

// 目录生成
const tocGenerated = ref(false)

const generateToc = () => {
  // 模拟目录生成
  textbookData.value.toc = [
    {
      title: '绪论',
      sections: [
        { title: '课程背景与目标' },
        { title: '学习方法指导' }
      ]
    },
    {
      title: '基础概念',
      sections: [
        { title: '核心术语定义' },
        { title: '技术发展历程' },
        { title: '应用场景分析' }
      ]
    },
    {
      title: '基本原理',
      sections: [
        { title: '理论基础' },
        { title: '计算模型' },
        { title: '实现方法' }
      ]
    },
    {
      title: '实践应用',
      sections: [
        { title: '案例分析' },
        { title: '实验设计' },
        { title: '操作指南' }
      ]
    }
  ]
  
  tocGenerated.value = true
}

// 目录操作
const addChapter = () => {
  textbookData.value.toc.push({
    title: '',
    sections: [{ title: '' }]
  })
}

const removeChapter = (chapterIndex) => {
  textbookData.value.toc.splice(chapterIndex, 1)
}

const addSection = (chapterIndex) => {
  textbookData.value.toc[chapterIndex].sections.push({ title: '' })
}

const removeSection = (chapterIndex, sectionIndex) => {
  textbookData.value.toc[chapterIndex].sections.splice(sectionIndex, 1)
}

// 表单验证
const canProceed = computed(() => {
  if (currentStep.value === 0) {
    return textbookData.value.title && 
           textbookData.value.subject
  }
  
  if (currentStep.value === 1) {
    return tocGenerated.value && textbookData.value.toc.length > 0
  }
  
  return true
})

// 下一步
const nextStep = () => {
  if (canProceed.value) {
    steps[currentStep.value].completed = true
    currentStep.value++
  }
}

// 保存草稿
const saveAsDraft = () => {
  // 实际应用中调用API保存草稿
  console.log('保存草稿:', textbookData.value)
  alert('已保存草稿')
}

// 创建教材
const createTextbook = () => {
  // 这里将跳转到编辑页面，并带上ID参数
  router.push('/teacher/textbook/1/edit')
}

// 返回列表
const goBackToList = () => {
  // 返回教材制作主页
  router.push('/teacher/textbook-projects')
}

// 保存并返回
const saveAndReturn = () => {
  // 保存当前数据
  console.log('保存数据:', textbookData.value)
  // 显示保存成功提示
  alert('保存成功')
  // 返回教材制作主页
  router.push('/teacher/textbook-projects')
}

// 内容创建相关
const currentChapter = ref(null)
const currentSection = ref(null)
const chapterOverview = ref('')
const sectionContent = ref('')

// 选择章节和小节
const selectChapter = (chapterIndex, sectionIndex) => {
  currentChapter.value = chapterIndex
  currentSection.value = sectionIndex
  
  // 如果是选择了章节，加载章节概述
  if (sectionIndex === -1) {
    chapterOverview.value = textbookData.value.toc[chapterIndex].overview || ''
  } else {
    // 如果是选择了小节，加载小节内容
    const section = textbookData.value.toc[chapterIndex].sections[sectionIndex]
    sectionContent.value = section.content || ''
  }
}

// 教材导出相关
const exportFormat = ref('pdf')
const includeCover = ref(true)
const selectedCoverTemplate = ref('template1')
const includeTableOfContents = ref(true)
const includePageNumbers = ref(true)
const includeWatermark = ref(false)
const watermarkText = ref('')

// 封面模板（模拟数据）
const coverTemplates = ref([
  { id: 'template1', name: '简约风格' },
  { id: 'template2', name: '学术风格' },
  { id: 'template3', name: '彩色风格' },
])

// 导出教材
const exportTextbook = () => {
  // 实际应用中调用API导出教材
  console.log('导出教材:', {
    format: exportFormat.value,
    settings: {
      cover: includeCover.value ? selectedCoverTemplate.value : null,
      toc: includeTableOfContents.value,
      pageNumbers: includePageNumbers.value,
      watermark: includeWatermark.value ? watermarkText.value || teacherData.value.name : null
    },
    textbook: textbookData.value
  })
  
  alert(`教材已导出为${exportFormat.value === 'pdf' ? 'PDF' : 'Word'}格式`)
}

// 文件上传相关
const fileInput = ref(null)
const dragover = ref(false)
const uploadedFiles = ref([])
const fileError = ref('')
const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB

const triggerFileUpload = () => {
  fileInput.value.click()
}

const handleFileUpload = (event) => {
  const files = event.target.files
  if (files.length) {
    uploadFiles(files)
  }
}

const onFileDrop = (event) => {
  dragover.value = false
  const files = event.dataTransfer.files
  if (files.length) {
    uploadFiles(files)
  }
}

const uploadFiles = (files) => {
  fileError.value = ''
  
  // 将FileList转换为数组以便处理
  Array.from(files).forEach(file => {
    // 验证文件类型
    const validTypes = ['.pdf', '.doc', '.docx', '.txt', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
    const isValidType = validTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type)
      } else {
        return file.type === type
      }
    })
    
    if (!isValidType) {
      fileError.value = `不支持的文件类型: ${file.name}`
      return
    }
    
    // 验证文件大小
    if (file.size > MAX_FILE_SIZE) {
      fileError.value = `文件大小超过限制: ${file.name}`
      return
    }
    
    // 添加到上传列表
    const fileInfo = {
      id: Date.now() + Math.random().toString(36).substr(2, 5),
      name: file.name,
      size: file.size,
      type: file.type || file.name.split('.').pop(),
      progress: 0,
      status: 'uploading',
      file
    }
    
    uploadedFiles.value.push(fileInfo)
    
    // 模拟上传进度
    simulateFileUpload(fileInfo.id)
  })
  
  // 清空文件输入以允许重复选择相同文件
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 模拟文件上传过程
const simulateFileUpload = (fileId) => {
  let progress = 0
  const interval = setInterval(() => {
    progress += Math.floor(Math.random() * 15) + 5
    
    const fileIndex = uploadedFiles.value.findIndex(f => f.id === fileId)
    if (fileIndex !== -1) {
      if (progress >= 100) {
        clearInterval(interval)
        progress = 100
        uploadedFiles.value[fileIndex].status = 'success'
      }
      uploadedFiles.value[fileIndex].progress = progress
    } else {
      clearInterval(interval)
    }
  }, 500)
}

// 移除文件
const removeFile = (index) => {
  if (uploadedFiles.value[index].status !== 'uploading') {
    uploadedFiles.value.splice(index, 1)
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件图标
const getFileIcon = (fileType) => {
  if (fileType === 'application/pdf' || fileType === 'pdf') {
    return 'description'
  } else if (fileType === 'application/msword' || 
             fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
             fileType === 'doc' || 
             fileType === 'docx') {
    return 'article'
  } else {
    return 'text_snippet'
  }
}

// 获取文件图标背景类
const getFileIconClass = (fileType) => {
  if (fileType === 'application/pdf' || fileType === 'pdf') {
    return 'bg-green-100'
  } else if (fileType === 'application/msword' || 
             fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
             fileType === 'doc' || 
             fileType === 'docx') {
    return 'bg-blue-100'
  } else {
    return 'bg-gray-100'
  }
}

// 获取文件图标颜色类
const getFileIconColorClass = (fileType) => {
  if (fileType === 'application/pdf' || fileType === 'pdf') {
    return 'text-green-600'
  } else if (fileType === 'application/msword' || 
             fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
             fileType === 'doc' || 
             fileType === 'docx') {
    return 'text-blue-600'
  } else {
    return 'text-gray-600'
  }
}

onMounted(() => {
  // 可以加载用户之前保存的草稿或初始化数据
})
</script>

<style scoped>
.toc-item, .toc-section {
  transition: all 0.2s;
}

input:focus::placeholder {
  color: transparent;
}
</style> 