import request from '@/utils/request'

export const usersApi = {
 /**
   * 获取教师列表
   * @returns {Promise} 包含教师列表的Promise
   */
 getTeachers() {
    return request({
      url: '/teachers/',
      method: 'get'
    })
  },

  getTeacherInfo(teacherId) {
    return request({
      url: `/teachers/${teacherId}/`,
      method: 'get'
    })
  },

  /**
   * 获取当前登录教师信息
   * @returns {Promise} 返回当前教师信息
   */
  getCurrentTeacherInfo() {
    return request({
      url: '/teachers/current/',
      method: 'get'
    })
  },

  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @param {number} [params.page] - 页码
   * @param {number} [params.page_size] - 每页条数
   * @param {string} [params.search] - 搜索关键词
   * @returns {Promise} 包含用户列表的Promise
   */
  getUsers(params) {
    return request({
      url: '/users/',
      method: 'get',
      params
    })
  },

  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @param {string} data.username - 用户名
   * @param {string} data.email - 邮箱
   * @param {string} data.password - 密码
   * @param {Array} data.roles - 角色列表
   * @param {number} data.dept - 部门ID
   * @returns {Promise} 包含创建的用户数据的Promise
   */
  createUser(data) {
    return request({
      url: '/users/',
      method: 'post',
      data
    })
  },

  /**
   * 更新用户
   * @param {number} id - 用户ID
   * @param {Object} data - 用户数据
   * @returns {Promise} 包含更新后的用户数据的Promise
   */
  updateUser(id, data) {
    return request({
      url: `/users/${id}/`,
      method: 'put',
      data
    })
  },

  /**
   * 删除用户
   * @param {number} id - 用户ID
   * @returns {Promise} 删除结果的Promise
   */
  deleteUser(id) {
    return request({
      url: `/users/${id}/`,
      method: 'delete'
    })
  },

  /**
   * 获取用户详情
   * @param {number} id - 用户ID
   * @returns {Promise} 包含用户详情的Promise
   */
  getUserDetail(id) {
    return request({
      url: `/users/${id}/`,
      method: 'get'
    })
  },

  /**
   * 获取角色列表
   * @returns {Promise} 包含角色列表的Promise
   */
  getRoles() {
    return request({
      url: '/roles/',
      method: 'get'
    })
  },

  /**
   * 获取部门列表
   * @returns {Promise} 包含部门列表的Promise
   */
  getDepts() {
    return request({
      url: '/depts/',
      method: 'get'
    })
  },

  /**
   * 通过用户名或手机号搜索用户
   * @param {string} query - 用户名或手机号
   * @returns {Promise}
   */
  searchUserByQuery(query) {
    return request({
      url: '/users/search/',
      method: 'get',
      params: { query }
    })
  },

  /**
   * 添加学生到班级
   * @param {number} classId - 班级ID
   * @param {number[]} userIds - 用户ID列表
   * @returns {Promise}
   */
  addUsersToClass(classId, userIds) {
    return request({
      url: `/class-groups/${classId}/add_students/`,
      method: 'post',
      data: { user_ids: userIds }
    })
  },

  /**
   * 获取班级的学生列表
   * @param {number} classId - 班级ID
   * @returns {Promise}
   */
  getClassStudents(classId) {
    return request({
      url: `/class-groups/${classId}/students/`,
      method: 'get'
    })
  },

  /**
   * 从班级移除学生
   * @param {number} classId - 班级ID
   * @param {number[]} studentIds - 学生ID列表
   * @returns {Promise}
   */
  removeStudentsFromClass(classId, studentIds) {
    return request({
      url: `/class-groups/${classId}/remove_students/`,
      method: 'post',
      data: { student_ids: studentIds }
    })
  }
} 