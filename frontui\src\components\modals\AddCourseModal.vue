<template>
  <el-dialog
    :model-value="show"
    @update:model-value="$emit('update:show', $event)"
    title="添加新课程"
    width="50%"
    :show-close="true"
    @close="close"
  >
    <template #header>
      <div class="flex items-center">
        <el-icon class="mr-2 text-xl text-blue-500"><CirclePlus /></el-icon>
        <span class="text-xl font-medium">添加新课程</span>
      </div>
    </template>

    <form @submit.prevent="handleSubmit" class="mt-4">
      <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-3">
        <!-- 课程名称 -->
        <div class="sm:col-span-3">
          <label for="name" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Reading /></el-icon>
            课程名称
          </label>
          <div class="mt-2">
            <el-input
              v-model="formData.name"
              placeholder="请输入课程名称"
              :prefix-icon="Reading"
              required
            />
          </div>
        </div>

        <!-- 所属学院 -->
        <div>
          <label for="college" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><School /></el-icon>
            所属学院
          </label>
          <div class="mt-2">
            <el-select
              v-model="formData.college"
              placeholder="选择学院"
              class="w-full"
              required
            >
              <el-option
                v-for="college in colleges"
                :key="college.id"
                :label="college.name"
                :value="college.id"
              />
            </el-select>
          </div>
        </div>

        <!-- 课程类型 -->
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Collection /></el-icon>
            课程类型
          </label>
          <div class="mt-2">
            <el-select
              v-model="formData.subject"
              placeholder="选择课程类型"
              class="w-full"
              required
            >
              <el-option label="数学类" value="math">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">calculate</span>
                  数学类
                </span>
              </el-option>
              <el-option label="编程类" value="programming">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">code</span>
                  编程类
                </span>
              </el-option>
              <el-option label="物理类" value="physics">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">science</span>
                  物理类
                </span>
              </el-option>
              <el-option label="化学类" value="chemistry">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">biotech</span>
                  化学类
                </span>
              </el-option>
              <el-option label="数据库类" value="database">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">database</span>
                  数据库类
                </span>
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 授课教师 -->
        <div>
          <label for="teacher" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><User /></el-icon>
            授课教师
          </label>
          <div class="mt-2">
            <el-input
              v-model="formData.teacher"
              placeholder="请输入教师姓名"
              :prefix-icon="User"
              required
            />
          </div>
        </div>

        <!-- 开始日期 -->
        <div>
          <label for="startDate" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Calendar /></el-icon>
            开始日期
          </label>
          <div class="mt-2">
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              placeholder="选择开始日期"
              format="YYYY-MM-DD"
              class="w-full"
              required
            />
          </div>
        </div>

        <!-- 课程周数 -->
        <div>
          <label for="duration" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Timer /></el-icon>
            课程周数
          </label>
          <div class="mt-2">
            <el-input-number
              v-model="formData.duration"
              :min="1"
              :max="52"
              class="w-full"
              required
            />
          </div>
        </div>

        <!-- 班级数量 -->
        <div>
          <label for="classCount" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Avatar /></el-icon>
            班级数量
          </label>
          <div class="mt-2">
            <el-input-number
              v-model="formData.classCount"
              :min="1"
              class="w-full"
              required
            />
          </div>
        </div>
      </div>
    </form>

    <template #footer>
      <div class="flex justify-end gap-2">
        <el-button @click="close">
          <el-icon class="mr-1"><Close /></el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit">
          <el-icon class="mr-1"><Check /></el-icon>
          确认添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import {
  CirclePlus,
  Reading,
  School,
  Collection,
  User,
  Calendar,
  Timer,
  Avatar,
  Check,
  Close
} from '@element-plus/icons-vue'

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  colleges: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['close', 'submit', 'update:show'])

const formData = ref({
  name: '',
  college: '',
  subject: '',
  teacher: '',
  startDate: '',
  duration: 16,
  classCount: 1,
  status: 'planned'
})

const handleSubmit = () => {
  emit('submit', { ...formData.value })
  resetForm()
}

const close = () => {
  resetForm()
  emit('close')
}

const resetForm = () => {
  formData.value = {
    name: '',
    college: '',
    subject: '',
    teacher: '',
    startDate: '',
    duration: 16,
    classCount: 1,
    status: 'planned'
  }
}
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200');

:deep(.el-input-number .el-input__wrapper) {
  padding-left: 11px;
  padding-right: 11px;
}

:deep(.el-dialog__body) {
  padding-top: 10px;
}

.material-symbols-outlined {
  font-size: 20px;
  line-height: 1;
  vertical-align: middle;
}

.el-icon {
  vertical-align: middle;
}

.el-button .el-icon {
  margin-right: 4px;
}

@media screen and (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
  }
}
</style> 