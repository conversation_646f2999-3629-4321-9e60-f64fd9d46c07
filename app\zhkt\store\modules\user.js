import { defineStore } from 'pinia'
import request from '@/utils/request'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: uni.getStorageSync('token') || '',
    userInfo: uni.getStorageSync('userInfo') || null
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    username: (state) => state.userInfo?.username || '',
    avatar: (state) => state.userInfo?.avatar || ''
  },
  
  actions: {
    // 登录
    async login(username, password) {
      try {
        const response = await request({
          url: '/users/login/',
          method: 'POST',
          data: { username, password }
        })
        
        const { token, user } = response
        this.setToken(token)
        this.setUserInfo(user)
        return response
      } catch (error) {
        throw error
      }
    },
    
    // 登出
    logout() {
      this.token = ''
      this.userInfo = null
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
    },
    
    // 设置Token
    setToken(token) {
      this.token = token
      uni.setStorageSync('token', token)
    },
    
    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      uni.setStorageSync('userInfo', userInfo)
    },
    
    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await request({
          url: '/users/info/',
          method: 'GET'
        })
        this.setUserInfo(response)
        return response
      } catch (error) {
        throw error
      }
    }
  }
}) 