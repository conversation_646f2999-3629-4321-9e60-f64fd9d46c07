/**
 * 格式化日期时间
 * @param {string|Date} date 日期时间字符串或Date对象
 * @returns {string} 格式化后的日期时间字符串
 */
export const formatDate = (date) => {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 获取相对时间
 * @param {string|Date} date - 要计算的日期
 * @returns {string} 相对时间字符串
 */
export function getRelativeTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const now = new Date()
  const diff = now.getTime() - d.getTime()
  const diffMinutes = Math.floor(diff / (1000 * 60))
  const diffHours = Math.floor(diff / (1000 * 60 * 60))
  const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 30) return `${diffDays}天前`
  
  return formatDate(date)
}

/**
 * 检查是否过期
 * @param {string|Date} date - 要检查的日期
 * @returns {boolean} 是否过期
 */
export function isOverdue(date) {
  if (!date) return false
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return false

  return d.getTime() < new Date().getTime()
}

/**
 * 获取剩余时间
 * @param {string|Date} date - 截止日期
 * @returns {string} 剩余时间字符串
 */
export function getRemainingTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const now = new Date()
  const diff = d.getTime() - now.getTime()
  
  if (diff < 0) return '已过期'
  
  const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (diffDays > 0) return `${diffDays}天${diffHours}小时`
  if (diffHours > 0) return `${diffHours}小时${diffMinutes}分钟`
  return `${diffMinutes}分钟`
} 