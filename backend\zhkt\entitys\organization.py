from django.db import models
from django.utils.translation import gettext_lazy as _

class College(models.Model):
    """学院模型"""
    name = models.CharField(_('学院名称'), max_length=100)
    code = models.CharField(_('学院代码'), max_length=20, unique=True)
    description = models.TextField(_('学院描述'), blank=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    class Meta:
        verbose_name = _('学院')
        verbose_name_plural = _('学院')

    def __str__(self):
        return self.name

class Major(models.Model):
    """专业模型"""
    name = models.CharField(_('专业名称'), max_length=100)
    code = models.CharField(_('专业代码'), max_length=20, unique=True)
    college = models.ForeignKey(College, on_delete=models.CASCADE, related_name='majors')
    description = models.TextField(_('专业描述'), blank=True)
    duration = models.IntegerField(_('学制(年)'), default=4)
    degree = models.CharField(_('学位类型'), max_length=50)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    class Meta:
        verbose_name = _('专业')
        verbose_name_plural = _('专业')

    def __str__(self):
        return self.name

class ClassGroup(models.Model):
    """班级模型"""
    name = models.CharField(_('班级名称'), max_length=100)
    code = models.CharField(_('班级代码'), max_length=20, unique=True, null=True)
    description = models.TextField(_('班级简介'), blank=True)
    # major = models.ForeignKey(Major, on_delete=models.CASCADE, related_name='class_groups')
    # grade = models.CharField(_('年级'), max_length=20, null=True)
    head_teacher = models.ForeignKey('Teacher', on_delete=models.SET_NULL, null=True, related_name='managed_classes')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    class Meta:
        verbose_name = _('班级')
        verbose_name_plural = _('班级')

    def __str__(self):
        return self.name 