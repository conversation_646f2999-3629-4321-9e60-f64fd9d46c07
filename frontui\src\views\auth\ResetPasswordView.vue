<template>
  <div class="bg-gray-50 flex items-center justify-center min-h-screen">
    <div class="max-w-md w-full mx-auto">
      <el-card class="box-card">
        <!-- 头部 -->
        <div class="p-6 bg-blue-500 text-center">
          <h1 class="text-2xl font-bold text-white">智慧课堂</h1>
          <p class="text-blue-100 mt-2">知识改变命运，智慧点亮未来</p>
        </div>
        
        <!-- 重置密码表单 -->
        <div class="p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-6 text-center">重置密码</h2>
          
          <el-form @submit.native.prevent="handleResetPassword" :model="formData" :rules="rules" ref="ruleFormRef">
            <!-- 新密码 -->
            <el-form-item prop="password">
              <el-input 
                v-model="formData.password" 
                type="password"
                show-password
                placeholder="请输入新密码"
                prefix-icon="Lock"
              />
              <p class="mt-1 text-xs text-gray-500">密码长度至少8位，包含字母和数字</p>
            </el-form-item>
            
            <!-- 确认新密码 -->
            <el-form-item prop="confirmPassword">
              <el-input 
                v-model="formData.confirmPassword" 
                type="password"
                show-password
                placeholder="请再次输入新密码"
                prefix-icon="Lock"
              />
            </el-form-item>
            
            <!-- 提交按钮 -->
            <el-button type="primary" @click="submitForm(ruleFormRef)" class="w-full">
              确认重置
            </el-button>
          </el-form>
          
          <!-- 返回登录 -->
          <div class="mt-6 text-center">
            <router-link to="/auth/login" class="font-medium text-blue-600 hover:text-blue-500 flex items-center justify-center">
              <el-icon class="mr-1"><Back /></el-icon> 返回登录
            </router-link>
          </div>
        </div>
      </el-card>
      
      <!-- 页脚 -->
      <div class="mt-4 text-center">
        <p class="text-xs text-gray-500">
          © 2025 智慧课堂. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Lock, Back } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const ruleFormRef = ref()

// 表单数据
const formData = reactive({
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 8) {
    callback(new Error('密码长度至少8位'))
  } else if (!/(?=.*[A-Za-z])(?=.*\d)/.test(value)) {
    callback(new Error('密码需包含字母和数字'))
  } else {
    if (formData.confirmPassword !== '') {
      ruleFormRef.value?.validateField('confirmPassword')
    }
    callback()
  }
}

const validatePass2 = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== formData.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const rules = reactive({
  password: [
    { validator: validatePass, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validatePass2, trigger: 'blur' }
  ]
})

// 提交表单
const submitForm = async (formEl) => {
  if (!formEl) return
  
  await formEl.validate((valid, fields) => {
    if (valid) {
      handleResetPassword()
    } else {
      ElMessage.error('表单验证失败，请检查输入')
    }
  })
}

// 处理重置密码
const handleResetPassword = () => {
  // 演示用，简单提示
  ElMessage.success('密码重置成功！请使用新密码登录。')
  
  // 跳转到登录页
  router.push('/auth/login')
}
</script>

<style scoped>
/* 可以添加组件特定的样式 */
</style> 