import request from '@/utils/request'

/**
 * 书架相关API
 */
export const bookshelfApi = {
  /**
   * 获取书架列表
   * @returns {Promise} 包含书架文档列表的Promise
   */
  getBookshelfList() {
    return request({
      url: '/bookshelf/',
      method: 'get'
    })
  },

  /**
   * 获取文档章节分页信息
   * @param {number|string} docId
   * @returns {Promise}
   */
  getChapters(docId) {
    return request({
      url: `/bookshelf/${docId}/chapters/`,
      method: 'get'
    })
  },

  /**
   * 获取指定章节下所有要点
   * @param {number|string} docId
   * @param {number} chapterId
   * @returns {Promise}
   */
  getChapterPages(docId, chapterId) {
    return request({
      url: `/bookshelf/${docId}/chapter/${chapterId}/pages/`,
      method: 'get'
    })
  },

  /**
   * 获取指定页HTML内容
   * @param {number|string} docId
   * @param {object} options 选项
   * @returns {Promise}
   */
  getHtmlContent(docId, options = {}) {
    const { page, keyPointId } = options;
    return request({
      url: `/bookshelf/${docId}/html/`,
      method: 'get',
      params: keyPointId ? { key_point_id: keyPointId } : { page }
    })
  },

  /**
   * 获取指定要点下所有音频及字幕
   * @param {number|string} docId
   * @param {number} keyPointId
   * @returns {Promise}
   */
  getKeyPointAudios(docId, keyPointId) {
    return request({
      url: `/bookshelf/${docId}/keypoint/${keyPointId}/audios/`,
      method: 'get'
    })
  },

  /**
   * 获取音频对应的字幕
   * @param {number|string} docId
   * @param {number} speechContentId 语音内容ID
   * @returns {Promise} 包含字幕列表的Promise
   */
  getSpeechSubtitles(docId, speechContentId) {
    return request({
      url: `/bookshelf/${docId}/speech/${speechContentId}/subtitles/`,
      method: 'get'
    })
  },

  /**
   * 获取章节状态
   * @param {number|string} docId
   * @param {number} chapterId
   * @returns {Promise}
   */
  getChapterStatus(docId, chapterId) {
    return request({
      url: `/bookshelf/${docId}/chapter/${chapterId}/status/`,
      method: 'get'
    })
  },

  /**
   * 触发章节生成
   * @param {number|string} docId
   * @param {number} chapterId
   * @returns {Promise}
   */
  triggerChapterGeneration(docId, chapterId) {
    return request({
      url: `/bookshelf/${docId}/chapter/${chapterId}/generate/`,
      method: 'post'
    })
  },

  /**
   * 通用GET请求
   * @param {string} url
   * @returns {Promise}
   */
  get(url) {
    return request({
      url,
      method: 'get'
    })
  },

  /**
   * 通用POST请求
   * @param {string} url
   * @param {Object} data
   * @returns {Promise}
   */
  post(url, data) {
    return request({
      url,
      method: 'post',
      data
    })
  },

  /**
   * 上传书架文件（只支持pdf/docx）
   * @param {File|FormData} fileOrFormData 文件对象或FormData对象
   * @returns {Promise} 上传结果
   */
  uploadBookshelfFile(fileOrFormData) {
    let formData;
    
    // 兼容两种调用方式
    if (fileOrFormData instanceof FormData) {
      // 如果传入的已经是 FormData，直接使用
      formData = fileOrFormData;
    } else {
      // 如果传入的是文件对象，创建新的 FormData
      formData = new FormData();
      formData.append('file', fileOrFormData);
    }
    
    return request({
      url: '/bookshelf/',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 删除书架文件
   * @param {number|string} id 文档ID
   * @returns {Promise} 删除结果
   */
  deleteBookshelfFile(id) {
    return request({
      url: `/bookshelf/${id}/`,
      method: 'delete'
    })
  },

  /**
   * 批量删除书架文件
   * @param {Array<number|string>} ids 文档ID数组
   * @returns {Promise} 删除结果
   */
  deleteDocumentsFromBookshelf(ids) {
    return request({
      url: '/bookshelf/batch_delete/',
      method: 'post',
      data: { document_ids: ids }
    })
  },

  /**
   * 获取文档风格设置
   * @param {number|string} docId 文档ID
   * @returns {Promise} 风格设置信息
   */
  getDocumentStyle(docId) {
    return request({
      url: `/bookshelf/${docId}/style/`,
      method: 'get'
    })
  },

  /**
   * 获取所有可用的语音风格及描述
   * @returns {Promise} 所有可用的风格及其描述
   */
  getAllSpeechStyles() {
    return request({
      url: '/bookshelf/styles/',
      method: 'get'
    })
  },

  /**
   * 更新章节风格
   * @param {number|string} docId 文档ID
   * @param {number|string} chapterId 章节ID
   * @param {string} style 风格代码
   * @returns {Promise} 更新结果
   */
  updateChapterStyle(docId, chapterId, style) {
    return request({
      url: `/bookshelf/${docId}/chapter/${chapterId}/style/`,
      method: 'post',
      data: { style }
    })
  },

  /**
   * 检查文档大纲生成状态
   * @param {number|string} docId 文档ID
   * @returns {Promise} 大纲生成状态
   */
  checkOutlineStatus(docId) {
    return request({
      url: `/bookshelf/${docId}/outline-status/`,
      method: 'get'
    })
  }
} 