<template>
  <view class="mall-container">
    <!-- 顶部区域 -->
    <view class="header">
      <view class="points-info">
        <text class="label">我的积分</text>
        <text class="value">{{ myPoints }}</text>
      </view>
      <view class="search-box">
        <uni-icons type="search" size="20" color="#666"></uni-icons>
        <input 
          type="text" 
          v-model="searchKeyword"
          placeholder="搜索商品"
          @confirm="handleSearch"
        />
      </view>
    </view>

    <!-- 分类导航 -->
    <scroll-view class="category-nav">
      <view 
        class="category-item"
        v-for="(item, index) in categories"
        :key="index"
        :class="{ active: currentCategory === item.id }"
        @tap="switchCategory(item.id)"
      >
        {{ item.name }}
      </view>
    </scroll-view>

    <!-- 排序选项 -->
    <view class="sort-options">
      <view 
        class="sort-item"
        v-for="(item, index) in sortOptions"
        :key="index"
        :class="{ active: currentSort === item.value }"
        @tap="switchSort(item.value)"
      >
        {{ item.label }}
        <uni-icons 
          v-if="item.value !== 'default'"
          :type="getSortIcon(item.value)"
          size="12"
          :color="currentSort === item.value ? '#007AFF' : '#666'"
        ></uni-icons>
      </view>
    </view>

    <!-- 商品列表 -->
    <scroll-view 
      class="goods-list"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="list-content">
        <view 
          class="goods-item"
          v-for="item in goodsList"
          :key="item.id"
          @tap="viewDetail(item)"
        >
          <image class="goods-image" :src="getProductImage(item.image)" mode="aspectFill"></image>
          <view class="goods-info">
            <text class="name">{{ item.name }}</text>
            <text class="desc">{{ item.description }}</text>
            <view class="bottom">
              <text class="points">{{ item.points_price }}积分</text>
              <text class="stock" :class="{ low: item.stock < 10 }">
                库存: {{ item.stock }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <uni-load-more :status="loadMoreStatus" :content-text="loadMoreText"></uni-load-more>

      <!-- 空状态 -->
      <view class="empty-state" v-if="goodsList.length === 0">
        <text>暂无商品</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getGoodsList } from '@/api/modules/points'
import { useUserStore } from '@/store/modules/user'
import defaultProductImage from '@/static/images/product/coffee.jpg'

const userStore = useUserStore()

// 响应式状态
const searchKeyword = ref('')
const currentCategory = ref('all')
const currentSort = ref('default')
const page = ref(1)
const isRefreshing = ref(false)
const loadMoreStatus = ref('more')
const myPoints = ref(0)
const goodsList = ref([])
const has_more = ref(false)

// 常量数据
const categories = [
  { id: 'all', name: '全部' },
  { id: 'physical', name: '实物商品' },
  { id: 'virtual', name: '虚拟商品' },
  { id: 'learning', name: '学习资源' }
]

const sortOptions = [
  { label: '默认', value: 'default' },
  { label: '积分', value: 'points' },
  { label: '热门', value: 'popular' }
]

const loadMoreText = {
  contentdown: '上拉加载更多',
  contentrefresh: '加载中...',
  contentnomore: '没有更多了'
}

// 获取商品图片
const getProductImage = (image) => {
  return image || defaultProductImage
}

// 计算属性
const getSortIcon = computed(() => {
  return (sortType) => {
    if (sortType === currentSort.value) {
      return sortType === 'points' ? 'arrow-down' : 'fire'
    }
    return sortType === 'points' ? 'arrow-down' : 'fire-filled'
  }
})

// 方法
const initPage = async () => {
  loadMoreStatus.value = 'loading'
  try {
    myPoints.value = userStore.userInfo.role_info.points
    await loadGoodsList()
  } catch (error) {
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  }
  loadMoreStatus.value = 'more'
}

const loadGoodsList = async () => {
  try {
    const { results, next } = await getGoodsList({
      page: page.value,
      category: currentCategory.value === 'all' ? '' : currentCategory.value,
      keyword: searchKeyword.value,
      sort: currentSort.value
    })
    if (page.value === 1) {
      goodsList.value = results
    } else {
      goodsList.value = [...goodsList.value, ...results]
    }
    has_more.value = next!==null?true:false
  } catch (error) {
    throw error
  }
}

const switchCategory = async (categoryId) => {
  if (currentCategory.value === categoryId) return
  currentCategory.value = categoryId
  page.value = 1
  await initPage()
}

const switchSort = async (sortType) => {
  if (currentSort.value === sortType) return
  currentSort.value = sortType
  page.value = 1
  await initPage()
}

const handleSearch = async () => {
  page.value = 1
  await initPage()
}

const onRefresh = async () => {
  isRefreshing.value = true
  page.value = 1
  try {
    await initPage()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  }
  isRefreshing.value = false
}

const loadMore = async () => {
  if (loadMoreStatus.value !== 'more' || !has_more.value) return
  loadMoreStatus.value = 'loading'
  page.value++
  try {
    await loadGoodsList()
  } catch (error) {
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
    page.value--
  }
  loadMoreStatus.value = 'more'
}

const viewDetail = (item) => {
  uni.navigateTo({
    url: `/pages/points/detail?id=${item.id}`
  })
}

// 生命周期
onMounted(() => {
  initPage()
})
</script>

<style lang="scss">
.mall-container {
  min-height: 100vh;
  background: #f5f5f5;

  .header {
    background: linear-gradient(to right, #007AFF, #00BFFF);
    padding: 30rpx;

    .points-info {
      margin-bottom: 30rpx;
      color: #fff;

      .label {
        font-size: 28rpx;
        opacity: 0.9;
      }

      .value {
        font-size: 48rpx;
        font-weight: bold;
        margin-top: 10rpx;
        display: block;
      }
    }

    .search-box {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 30rpx;
      padding: 15rpx 30rpx;

      uni-icons {
        margin-right: 10rpx;
      }

      input {
        flex: 1;
        font-size: 28rpx;
        color: #fff;

        &::placeholder {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .category-nav {
    background: #fff;
    white-space: nowrap;
    padding: 20rpx 0;

    .category-item {
      display: inline-block;
      padding: 10rpx 10rpx;
      margin: 0 20rpx;
      font-size: 28rpx;
      color: #666;
      position: relative;

      &.active {
        color: #007AFF;
        font-weight: bold;

        &::after {
          content: '';
          position: absolute;
          bottom: -10rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background: #007AFF;
          border-radius: 2rpx;
        }
      }

      &:first-child {
        margin-left: 30rpx;
      }

      &:last-child {
        margin-right: 30rpx;
      }
    }
  }

  .sort-options {
    display: flex;
    background: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    border-top: 1rpx solid #eee;

    .sort-item {
      display: flex;
      align-items: center;
      margin-right: 40rpx;
      font-size: 26rpx;
      color: #666;

      &.active {
        color: #007AFF;
        font-weight: bold;
      }

      uni-icons {
        margin-left: 6rpx;
      }
    }
  }

  .goods-list {
    height: calc(100vh - 300rpx);
    padding: 0 20rpx;

    .list-content {
      .goods-item {
        margin-right: 30rpx;
        background: #fff;
        border-radius: 10rpx;
        border: 1rpx solid #ccc;
        margin-bottom: 20rpx;
        overflow: hidden;

        .goods-image {
          width: 100%;
          height: 300rpx;
        }

        .goods-info {
          padding: 30rpx;

          .name {
            font-size: 28rpx;
            color: #333;
            font-weight: bold;
            margin-bottom: 10rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .desc {
            font-size: 24rpx;
            color: #666;
            margin-bottom: 20rpx;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }

          .bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;

            .points {
              font-size: 32rpx;
              color: #ff5a5f;
              font-weight: bold;
              max-width: 60%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .stock {
              font-size: 24rpx;
              color: #999;
              max-width: 35%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              &.low {
                color: #ff5a5f;
              }
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 100rpx 0;

      image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }

      text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}
</style> 