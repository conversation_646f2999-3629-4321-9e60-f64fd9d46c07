<template>
  <div class="flex items-center">
    <el-checkbox
      v-model="checkboxValue"
      :disabled="disabled"
      :label="label"
      :size="size"
      :indeterminate="indeterminate"
      @change="handleChange"
    >
      <slot />
    </el-checkbox>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  label: {
    type: [String, Number, Boolean],
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'default',
    validator: (val) => ['large', 'default', 'small'].includes(val),
  },
  indeterminate: {
    type: Boolean,
    default: false,
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const checkboxValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

const handleChange = (val) => {
  emit('change', val);
};
</script> 