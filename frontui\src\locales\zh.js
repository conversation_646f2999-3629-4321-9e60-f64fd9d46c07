export default {
  system: {
    title: '智慧课堂',
    slogan: '知识改变命运，智慧点亮未来',
    copyright: '© 2025 智慧课堂. 保留所有权利.'
  },
  login: {
    title: '用户登录',
    username: '请输入账号',
    password: '请输入密码',
    remember: '记住密码',
    forgot: '忘记密码?',
    button: '登录',
    otherMethods: '其他登录方式',
    noAccount: '还没有账号?',
    register: '立即注册'
  },
  role: {
    student: '学生',
    teacher: '教师',
    admin: '管理员'
  },
  errors: {
    loginFailed: '登录失败，请重试！',
    wrongCredentials: '用户名或密码错误，请重试！',
    roleError: '用户角色错误，请重试！',
    unknown: '未知错误，请稍后重试！',
    loadCoursesListFailed: '加载课程列表失败'
  },
  dashboard: {
    student: {
      title: '学生首页',
      welcome: '欢迎回来，{name}',
      today: '今天是 {date}',
      learningStatus: {
        continuousLearning: '连续学习 {days} 天',
        weeklyLearning: '本周学习 {hours} 小时'
      },
      learningGoal: {
        title: '学习目标',
        remainingCourses: '距离下阶段目标还需完成{count}门课程'
      },
      stats: {
        courseCompletion: {
          title: '课程完成率',
          totalCourses: '{total}门课程中已完成{completed}门',
          monthlyChange: '较上月↑{percent}%'
        },
        homeworkCompletion: {
          title: '作业完成率',
          totalHomeworks: '{total}份作业中已完成{completed}份',
          averageScore: '平均分{score}分'
        },
        learningHours: {
          title: '学习时长',
          hours: '{hours}小时',
          increase: '↑ {hours}小时',
          weekday: {
            monday: '周一',
            sunday: '周日'
          }
        },
        points: {
          title: '积分与成就',
          points: '{points}分',
          increase: '↑ {points}',
          medalLevel: {
            bronze: '铜牌学员',
            silver: '银牌学员',
            gold: '金牌学员'
          },
          nextLevel: '距{level}还需{points}积分'
        }
      },
      recommendations: {
        title: '个性化学习建议',
        efficiency: '学习效率提升',
        reinforcement: '知识点巩固',
        habit: '学习习惯养成'
      },
      errors: {
        fetchCoursesListFailed: '获取课程列表失败',
        fetchLearningStatsFailed: '获取学习统计数据失败',
        fetchStudentInfoFailed: '获取学生信息失败',
        joinClassFailed: '加入班级失败'
      }
    }
  },
  courses: {
    title: '我的课程',
    joinClass: '加入班级',
    points: '积分',
    courseStats: {
      total: '全部课程',
      completed: '已完成',
      inProgress: '进行中',
      weeklyHours: '本周学习'
    },
    joinClassModal: {
      title: '加入班级',
      enterCode: '请输入班级邀请码',
      codePlaceholder: '请输入6位邀请码',
      joining: '加入中...',
      confirm: '确认加入',
      success: '成功加入班级',
      failed: '加入班级失败',
      failedWithError: '加入班级失败: {error}'
    },
    coursesList: {
      title: '全部课程',
      teacher: '主讲老师',
      hours: '课时',
      students: '已有{count}人学习',
      continueLearning: '继续学习 →',
      review: '我要复习 →'
    },
    pagination: {
      previous: '上一页',
      next: '下一页'
    },
    notes: {
      button: '笔记',
      title: '{course} - 笔记',
      listTitle: '笔记列表',
      count: '{count}条笔记',
      searchPlaceholder: '搜索笔记...',
      loading: '加载笔记列表中...',
      loadingContent: '加载笔记内容中...',
      empty: '暂无笔记',
      untitled: '无标题笔记',
      createdAt: '创建于',
      loadFailed: '加载笔记列表失败',
      loadDetailFailed: '加载笔记详情失败'
    }
  },

  learningRecommendations: {
    efficiency: {
      title: '提高学习效率',
      recommendations: [
        '建议在19:00-21:00时段学习，专注度更高',
        '增加视频学习方式，效率提升12%'
      ]
    },
    reinforcement: {
      title: '知识点巩固',
      recommendations: [
        '针对微分中值定理进行专项练习',
        '不定积分计算薄弱，建议复习基础公式'
      ]
    },
    habit: {
      title: '学习习惯优化',
      recommendations: [
        '建立更稳定的学习连续性，避免间断',
        '增加练习题解答频率，提高应用能力'
      ]
    }
  },



  courseContent: {
    videoPlayerNotSupported: '您的浏览器不支持 HTML5 视频播放器',
    
    tabs: {
      overview: '课程概述',
      discussion: '评论讨论'
    },
    
    controls: {
      notes: '笔记',
      navigation: '导航',
      rating: '评分'
    },
    
    stats: {
      totalHours: '总学时',
      hour: '小时',
      studentsCount: '学习人数',
      rating: '评分',
      ratingCount: '人评价'
    },
    
    comments: {
      title: '评论讨论',
      placeholder: '分享你的想法...',
      submit: '发表评论',
      reply: '回复',
      replyPlaceholder: '回复...',
      submitReply: '发表回复',
      instructor: '讲师'
    },
    
    sidebar: {
      progress: '学习进度',
      lessons: '课时',
      lessonsUnit: '课时'
    },
    
    aiCompanion: {
      title: 'AI学习助手',
      subtitle: '随时为你解答疑问',
      inputPlaceholder: '请在此输入问题...',
      selectCompanion: '选择学伴',
      close: '关闭'
    },
    
    aiCompanionDialog: {
      title: '选择你的学习伙伴',
      requiredRoles: '必选角色',
      requiredRolesNote: '(王老师已自动选择)',
      companionRoles: '学伴角色',
      optionalRolesNote: '(可自由选择)',
      confirm: '确认选择',
      roles: {
        teacher: {
          name: '王老师',
          desc: '协助教学支持'
        },
        funnyKid: {
          name: '小明(班宠萌娃)',
          desc: '活跃开朗，喜欢开玩笑'
        },
        thinker: {
          name: '李华(沉思者)',
          desc: '思考深入，擅长分析'
        },
        curious: {
          name: '张颖(好奇宝宝)',
          desc: '爱提问，善于引出概念'
        },
        topStudent: {
          name: '赵阳(卷王学霸)',
          desc: '整理要点，语言简洁有力'
        }
      }
    },
    
    noteModal: {
      title: '课程笔记',
      search: '搜索笔记...',
      new: '新建笔记',
      titlePlaceholder: '笔记标题',
      contentPlaceholder: '在此输入笔记内容...',
      timePoint: '时间点',
      save: '保存笔记',
      cancel: '取消',
      delete: '删除笔记',
      markdownSupport: '支持Markdown格式',
      charCount: '{count} 字符',
      confirmDelete: '确定要删除这条笔记吗？',
      // 通知消息
      titleRequired: '请输入笔记标题',
      updateSuccess: '笔记更新成功',
      createSuccess: '笔记创建成功',
      saveFailed: '保存笔记失败',
      loadFailed: '加载笔记失败',
      deleteSuccess: '笔记删除成功',
      deleteFailed: '删除笔记失败',
      courseInfoError: '无法获取课程信息'
    },
    
    videoNavModal: {
      title: 'AI视频导航',
      summary: '视频内容概要',
      noSummary: '暂无视频概要',
      keyPoints: '关键点导航',
      noKeyPoints: '暂无关键点数据'
    },
    
    ratingDialog: {
      title: '课程评分',
      submit: '提交评分',
      cancel: '取消'
    }
  },
  
  courseOverview: {
    keyPoints: '课程要点',
    importantPoints: '重点知识点',
    notes: '注意事项',
    noContent: '暂无内容',
    edit: '编辑课程概述',
    enterKeyPoints: '请输入课程要点',
    enterImportantPoints: '请输入重点知识点',
    enterNotes: '请输入注意事项',
    updateSuccess: '课程概述已更新',
    saveFailed: '保存失败'
  },
  general: {
    confirm: '确认',
    cancel: '取消',
    confirmation: '确认',
    error: '发生错误',
    uploadFailed: '上传失败',
    pleaseWait: '请稍候...',
    defaultStudentName: '张同学',
    home: '首页',
    points: '积分',
    save: '保存'
  },
  
  userHeader: {
    profile: '个人资料',
    settings: '账号设置',
    activate: '激活认证',
    logout: '退出登录',
    logoutFailed: '退出登录失败',
    verification: {
      title: '激活认证',
      alertTitle: '请完成实名认证',
      successTitle: '已完成认证',
      teacherDescription: '作为教师，激活认证可以使用更多功能，请填写真实信息进行三要素实名认证',
      studentDescription: '激活认证可以使用更多功能，请填写真实信息进行三要素实名认证',
      teacherSuccess: '您作为教师已通过实名认证，账号已激活',
      studentSuccess: '您已完成实名认证，账号已激活',
      step1: '填写信息',
      step2: '验证手机',
      step3: '完成认证',
      name: '姓名',
      idCard: '身份证号',
      phone: '手机号',
      code: '验证码',
      namePlaceholder: '请输入真实姓名',
      idCardPlaceholder: '请输入身份证号码',
      phonePlaceholder: '请输入手机号码',
      codePlaceholder: '请输入验证码',
      nameRequired: '请输入真实姓名',
      nameLength: '姓名长度应在2-20个字符之间',
      idCardRequired: '请输入身份证号码',
      idCardFormat: '请输入正确的身份证号码',
      phoneRequired: '请输入手机号码',
      phoneFormat: '请输入正确的手机号码',
      codeRequired: '请输入验证码',
      codeFormat: '验证码为6位数字',
      next: '下一步',
      previous: '上一步',
      modify: '修改',
      getCode: '获取验证码',
      resend: '{seconds}秒后重新获取',
      verify: '验证',
      complete: '完成',
      codeSentSuccess: '验证码已发送至 {phone}',
      codeSendFailed: '验证码发送失败，请稍后重试',
      verificationFailed: '验证失败，请检查信息是否正确',
      enterCorrectCode: '请输入正确的验证码',
      fillRequiredFields: '请正确填写所有必填信息',
      verifying: '验证',
      threeElements: '三要素和验证码',
      sendCodeTo: '发送验证码到'
    }
  },

  breadcrumb: {
    adminDashboard: '控制台',
    teacherDashboard: '教师首页',
    studentDashboard: '学生首页',
    myCourses: '我的课程',
    courseContent: '课程内容',
    myAssignments: '我的作业',
    assignmentManagement: '作业管理',
    courseManagement: '课程管理',
    studentManagement: '学生管理',
    classManagement: '班级管理',
    gradeManagement: '成绩管理',
    questionBank: '题库管理',
    textbookProjects: '教材制作',
    contentCreation: '工具箱',
    lessonPlanProjects: '教学设计',
    contentPptProjects: 'PPT制作',
    contentScriptProjects: '讲稿内容',
    contentVideoProjects: '合成视频',
    videoManagement: '视频管理',
    digitalTeacher: '数字形象',
    aiVoice: 'AI语音',
    aiLecture: 'AI讲课',
    aiLearning: 'AI学习',
    bookshelf: '书架',
    systemManagement: '系统管理',
    userManagement: '用户管理',
    storeManagement: '商城管理',
    pointsManagement: '积分管理',
    notificationManagement: '通知管理',
    auditLogs: '日志审计',
    knowledgeBase: '知识库',
    personalCenter: '个人中心',
    profile: '个人资料',
    aiAssistant: 'AI问答',
    pointsMall: '积分商城',
    settings: '系统设置',
    chapter: '章节',
    class: '班级',
    homework: '作业',
    grades: '成绩',
    dograde: '批改作业',
    grading: '批改详情'
  },
  
  sidebar: {
    home: '主页',
    aiLecture: 'AI讲课',
    bookshelf: '书架',
    courses: '我的课程',
    assignments: '我的作业',
    knowledgeBase: '知识库',
    aiAssistant: 'AI问答',
    pointsMall: '积分商城',
    personalCenter: '个人中心',
    collapse: '收起菜单'
  },
  
  todayLearn: {
    title: 'AI讲课',
    upload: {
      area: '拖拽文件到这里，或者',
      button: '点击上传',
      supportTypes: '支持的文件类型: .doc, .docx, .txt, .pdf',
      dropHint: '松开鼠标上传文件',
      uploading: '正在上传...'
    },
    styleDialog: {
      title: '选择讲课风格',
      description: '请为您上传的文档选择一种讲课风格：',
      cancel: '取消上传',
      confirm: '确认并上传'
    }
  },
  
  bookshelf: {
    title: '书架',
    stats: '{count} 个文档',
    slogan: '重塑您的阅读体验',
    search: '搜索文档...',
    generating: '{count} 个文档正在生成',
    batchOperation: '批量操作',
    exitBatch: '退出批量',
    selectAll: '全选',
    cancelSelect: '取消全选',
    delete: '删除',
    outlineCompleted: '大纲生成完成',
    processingTime: '预计10-15分钟',
    document: '文档',
    
    addDocument: {
      title: '添加文档',
      dropHint: '拖放文件到这里',
      supportDrag: '支持拖放'
    },
    
    upload: {
      uploading: '正在上传...',
      deleting: '正在删除...',
      completed: '已完成',
      pleaseWait: '请稍候...',
      progress: '已完成 {completed} / {total}'
    },
    
    documentCard: {
      editName: '编辑名称',
      delete: '删除',
      moreActions: '更多操作'
    },
    
    editNameDialog: {
      title: '编辑文档名称',
      placeholder: '输入文档名称',
      confirm: '确认',
      cancel: '取消'
    },
    
    styleDialog: {
      title: '选择讲解风格',
      description: '请选择您希望AI用什么风格讲解这个文档',
      selectStyle: '请选择讲解风格:',
      confirm: '确认',
      cancel: '取消',
      cancelUpload: '取消上传',
      confirmAndUpload: '确认并上传'
    },
    
    preview: {
      generating: '大纲正在生成中，请稍后再试',
      failed: '预览加载失败'
    },
    
    notifications: {
      nameUpdated: '文档名称已更新',
      deleteConfirm: '确定要删除 {name} 吗？',
      deleteFailed: '删除失败',
      fileTypeError: '不支持的文件类型',
      uploadSuccess: '{name} 上传成功',
      generatingOutline: '正在生成大纲，这可能需要几分钟时间',
      selectDocumentsToDelete: '请选择要删除的文档',
      batchDeleteConfirm: '确定要删除选中的 {count} 个文档吗？',
      batchDeleteSuccess: '成功删除 {count} 个文档',
      batchDeleteFailed: '批量删除失败',
      confirmDelete: '确定要删除 {name} 吗？',
      deleteConfirmTitle: '确认删除',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      deleteSuccess: '删除成功',
      fileTypesHint: '只支持PDF和DOCX文件',
      confirmBatchDelete: '确定要删除选中的 {count} 个文档吗？',
      batchDeleteConfirmTitle: '确认批量删除'
    }
  },
  
  htmlPreview: {
    actions: {
      chapterList: '章节列表',
      switchStyle: '切换讲课风格',
      back: '返回书架'
    },
    chapterDialog: {
      title: '章节列表',
      noChapters: '暂无章节',
      page: 'p. {start}-{end}'
    },
    styleDialog: {
      title: '选择讲课风格',
      currentStyle: '当前风格：',
      loading: '加载风格列表中...',
      cancel: '取消',
      confirm: '确认切换',
      switching: '切换中...'
    },
    status: {
      loading: '加载中...',
      generating: '正在生成章节内容...',
      pointsGenerated: '已生成 {count} 个要点',
      completed: '生成完成！',
      notGenerated: '章节内容尚未生成',
      startGenerate: '开始生成',
      loadFailed: '加载失败'
    },
    controls: {
      play: '播放',
      pause: '暂停',
      noAudio: '暂无音频',
      generating: '音频生成中...',
      prev: '上一页',
      next: '下一页',
      page: '第 {current} / {total} 页',
      generating: '内容生成中...'
    },
    notifications: {
      styleChanged: '风格切换成功',
      styleChangedDetail: '已切换到{style}，页面将刷新，音频将在后台生成',
      styleChangeFailed: '风格切换失败',
      retryLater: '请稍后重试',
      audioUpdated: '音频已更新',
      audioUpdateDetail: '讲课风格切换的音频已生成完成',
      generationStarted: '已开始生成',
      generationDetail: '章节内容生成已开始，请等待...',
      generationFailed: '生成失败',
      triggerFailed: '触发章节生成失败',
      autoPaging: '自动翻页',
      pagingMessage: '正在翻到第 {page} 页并继续播放...',
      playCompleted: '播放完成',
      playCompletedDetail: '已播放完所有内容',
      firstPointGenerated: '已生成首个要点',
      firstPointDetail: '可以开始预览内容，后台将继续生成更多内容',
      generationCompleted: '生成完成',
      allContentGenerated: '章节内容已全部生成完成',
      outlineCompleted: '{names} 大纲生成完成！'
    },
    status: {
      notGenerated: '未生成',
      generating: '生成中',
      completed: '已完成'
    }
  }
} 