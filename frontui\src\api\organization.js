import request from '@/utils/request'

export const organizationApi = {
  /**
   * 获取学院列表
   * @returns {Promise} 包含学院列表的Promise
   */
  getColleges() {
    return request({
      url: '/colleges/',
      method: 'get'
    })
  },

  /**
   * 获取专业列表
   * @returns {Promise} 包含专业列表的Promise
   */
  getMajors() {
    return request({
      url: '/majors/',
      method: 'get'
    })
  },

  /**
   * 获取班级列表
   * @returns {Promise} 包含班级列表的Promise
   */
  getClassGroups(courseId) {
    return request({
      url: `/class-groups/?course_id=${courseId}`,
      method: 'get'
    })
  },

  /**
   * 创建班级
   * @param {Object} data 班级数据
   * @returns {Promise} 创建结果
   */
  createClassGroup(data) {
    return request({
      url: '/class-groups/',
      method: 'post',
      data
    })
  },

  /**
   * 更新班级
   * @param {number} id 班级ID
   * @param {Object} data 班级数据
   * @returns {Promise} 更新结果
   */
  updateClassGroup(id, data) {
    return request({
      url: `/class-groups/${id}/`,
      method: 'put',
      data
    })
  },

  /**
   * 删除班级
   * @param {number} id 班级ID
   * @returns {Promise} 删除结果
   */
  deleteClassGroup(id) {
    return request({
      url: `/class-groups/${id}/`,
      method: 'delete'
    })
  },

  /**
   * 获取班级邀请码
   * @param {number} classId - 班级ID
   * @returns {Promise} 包含邀请码和加入URL的Promise
   */
  getClassInvitationCode(classId) {
    return request({
      url: `/class-groups/${classId}/invitation_code/`,
      method: 'get'
    })
  },

  /**
   * 通过邀请码加入班级
   * @param {string} code - 邀请码
   * @returns {Promise}
   */
  joinClassByCode(code) {
    return request({
      url: '/class-groups/join_by_code/',
      method: 'post',
      data: { code }
    })
  }
} 