import request from '@/utils/request'
import downloadUtils from '@/utils/download'

/**
 * 语音管理相关 API
 * 对应后端 AudioViewSet 中的接口
 */
export const voiceApi = {
  /**
   * 上传原始音频文件（非克隆）
   * @param {File|FormData} fileOrFormData 音频文件或已构造的 FormData
   * @param {Object} extra 额外字段，例如 { name, description }
   */
  uploadAudio(fileOrFormData, extra = {}) {
    const formData = fileOrFormData instanceof FormData ? fileOrFormData : (() => {
      const fd = new FormData()
      fd.append('file', fileOrFormData)
      return fd
    })()
    // 追加文本字段
    Object.entries(extra).forEach(([k, v]) => formData.append(k, v))
    return request({
      url: '/audio/upload/',
      method: 'post',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 创建语音克隆模型
   * @param {Object} payload { name, description, audio_file, avatar }
   */
  createVoiceModel(payload) {
    const formData = new FormData()
    // 确保只添加有值的字段
    Object.entries(payload).forEach(([k, v]) => {
      if (v !== null && v !== undefined && v !== '') {
        formData.append(k, v)
      }
    })
    
    return request({
      url: '/audio/clone/',
      method: 'post',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 获取用户语音克隆列表
   * @param {Object} params { skip, limit, search }
   */
  getVoiceModels(params = {}) {
    return request({
      url: '/audio/clones/',
      method: 'get',
      params
    })
  },

  /**
   * 更新语音模型
   * @param {Number|String} id 模型 ID
   * @param {Object} payload { name, description, avatar }
   */
  updateVoiceModel(id, payload) {
    const formData = new FormData()
    Object.entries(payload).forEach(([k, v]) => v && formData.append(k, v))
    return request({
      url: `/audio/${id}/update/`,
      method: 'put',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 删除语音模型
   * @param {Number|String} id 模型 ID
   */
  deleteVoiceModel(id) {
    return request({
      url: `/audio/${id}/delete/`,
      method: 'delete'
    })
  },

  /**
   * 使用模型生成音频
   * @param {Object} payload { title, text_content, audio_clone_id, speed, copywriting_id }
   */
  generateAudio(payload) {
    const formData = new FormData()
    Object.entries(payload).forEach(([k, v]) => formData.append(k, v))
    return request({
      url: '/audio/generate/',
      method: 'post',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 获取生成的音频列表
   * @param {Object} params { skip, limit }
   */
  getGeneratedAudios(params = {}) {
    return request({
      url: '/audio/generated/',
      method: 'get',
      params
    })
  },

  /**
   * 删除生成的音频
   * @param {Number|String} id 音频 ID
   */
  deleteGeneratedAudio(id) {
    return request({
      url: `/audio/${id}/delete-generated/`,
      method: 'delete'
    })
  },

  /**
   * 获取系统预设声音列表
   */
  getSystemVoicePresets() {
    return request({
      url: '/audio/system-presets/',
      method: 'get'
    })
  },

  /**
   * 下载音频文件
   * @param {Number|String} id 音频ID
   * @param {String} defaultFilename 默认文件名
   * @returns {Promise} 下载Promise
   */
  downloadAudio(id, defaultFilename = null) {
    // 统一使用后端API进行流式下载，避免跨域问题
    const url = `${import.meta.env.VITE_API_BASE_URL || '/api'}/audio/${id}/download/`
    
    return downloadUtils.downloadWithMessage(
      url, 
      defaultFilename || `音频_${id}.mp3`,
      {
        start: '开始下载音频...',
        success: '音频下载完成',
        error: '音频下载失败'
      }
    )
  }
} 