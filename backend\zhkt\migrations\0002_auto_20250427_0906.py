# Generated by Django 3.2.20 on 2025-04-27 01:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='KnowledgeCategory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='大分类ID')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='大分类名称')),
                ('icon', models.CharField(blank=True, max_length=255, null=True, verbose_name='分类图标')),
                ('color', models.CharField(blank=True, max_length=20, null=True, verbose_name='分类颜色代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='大分类描述信息')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序顺序')),
                ('status', models.Char<PERSON>ield(default='1', max_length=20, verbose_name='状态')),
                ('created_by', models.Char<PERSON>ield(blank=True, max_length=64, null=True, verbose_name='创建者ID')),
                ('create_at', models.DateTimeField(blank=True, null=True, verbose_name='创建日期时间')),
                ('update_at', models.DateTimeField(blank=True, null=True, verbose_name='更新日期时间')),
                ('delete_at', models.DateTimeField(blank=True, null=True, verbose_name='逻辑删除时间')),
            ],
            options={
                'verbose_name': '知识库大分类',
                'verbose_name_plural': '知识库大分类',
                'db_table': 'zhkt_kb_categories',
                'ordering': ['sort_order'],
            },
        ),
        migrations.CreateModel(
            name='KnowledgeDataset',
            fields=[
                ('id', models.CharField(max_length=64, primary_key=True, serialize=False, verbose_name='数据集ID')),
                ('icon', models.CharField(blank=True, max_length=255, null=True, verbose_name='数据集图标')),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='数据集名称')),
                ('avatar', models.TextField(blank=True, null=True, verbose_name='数据集头像')),
                ('description', models.TextField(blank=True, null=True, verbose_name='数据集描述信息')),
                ('embedding_model', models.CharField(max_length=255, verbose_name='嵌入模型名称')),
                ('status', models.CharField(default='1', max_length=20, verbose_name='数据集状态')),
                ('created_by', models.CharField(blank=True, max_length=64, null=True, verbose_name='创建者ID')),
                ('create_at', models.DateTimeField(blank=True, null=True, verbose_name='创建日期时间')),
                ('update_at', models.DateTimeField(blank=True, null=True, verbose_name='更新日期时间')),
                ('delete_at', models.DateTimeField(blank=True, null=True, verbose_name='逻辑删除时间')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='datasets', to='zhkt.knowledgecategory', verbose_name='所属大分类')),
            ],
            options={
                'verbose_name': '知识库分类',
                'verbose_name_plural': '知识库分类',
                'db_table': 'zhkt_kb_datasets',
            },
        ),
        migrations.CreateModel(
            name='KnowledgeDocument',
            fields=[
                ('id', models.CharField(max_length=64, primary_key=True, serialize=False, verbose_name='文档ID')),
                ('icon', models.CharField(blank=True, max_length=255, null=True, verbose_name='文档图标')),
                ('name', models.CharField(max_length=512, verbose_name='文档名称')),
                ('size', models.BigIntegerField(default=0, verbose_name='文档大小')),
                ('type', models.CharField(blank=True, max_length=50, null=True, verbose_name='文档类型')),
                ('chunk_method', models.CharField(blank=True, max_length=50, null=True, verbose_name='文档分块方法')),
                ('chunk_count', models.IntegerField(default=0, verbose_name='文档分块数量')),
                ('token_count', models.IntegerField(default=0, verbose_name='文档token数量')),
                ('process_duration', models.IntegerField(default=0, verbose_name='文档处理时间')),
                ('progress', models.FloatField(default=0, verbose_name='处理进度')),
                ('run', models.CharField(blank=True, max_length=20, null=True, verbose_name='运行状态')),
                ('status', models.CharField(default='1', max_length=20, verbose_name='文档状态')),
                ('create_at', models.DateTimeField(blank=True, null=True, verbose_name='创建日期时间')),
                ('update_at', models.DateTimeField(blank=True, null=True, verbose_name='更新日期时间')),
                ('delete_at', models.DateTimeField(blank=True, null=True, verbose_name='逻辑删除时间')),
                ('dataset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='zhkt.knowledgedataset', verbose_name='所属数据集')),
            ],
            options={
                'verbose_name': '知识库文件',
                'verbose_name_plural': '知识库文件',
                'db_table': 'zhkt_kb_documents',
            },
        ),
        migrations.RemoveField(
            model_name='knowledgepoint',
            name='knowledge_base',
        ),
        migrations.RemoveField(
            model_name='knowledgepoint',
            name='parent',
        ),
        migrations.RemoveField(
            model_name='note',
            name='knowledge_point',
        ),
        migrations.RemoveField(
            model_name='note',
            name='student',
        ),
        migrations.AddField(
            model_name='admin',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='aichat',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='aichatmessage',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='aiprompt',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='chapter',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='course',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='courseenrollment',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='dept',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='feedback',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='homework',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='menu',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='order',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='pointsrecord',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='product',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='resource',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='role',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='student',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='submission',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='teacher',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='user',
            name='delete_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AlterField(
            model_name='aichatmessage',
            name='content',
            field=models.TextField(db_collation='utf8mb4_unicode_ci', verbose_name='消息内容'),
        ),
        migrations.DeleteModel(
            name='KnowledgeBase',
        ),
        migrations.DeleteModel(
            name='KnowledgePoint',
        ),
        migrations.DeleteModel(
            name='Note',
        ),
    ]
