<template>
  <div class="bg-gray-50 flex items-center justify-center min-h-screen py-8">
    <div class="max-w-md w-full mx-auto">
      <el-card class="box-card">
        <!-- 头部 -->
        <div class="p-6 bg-blue-500 text-center">
          <h1 class="text-2xl font-bold text-white">智慧课堂</h1>
          <p class="text-blue-100 mt-2">知识改变命运，智慧点亮未来</p>
        </div>
        
        <!-- 注册表单 -->
        <div class="p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-6 text-center">用户注册</h2>
          
          <el-form @submit.native.prevent="handleRegister" :model="formData" :rules="rules" ref="ruleFormRef">
            <!-- 用户名 -->
            <el-form-item prop="username">
              <el-input 
                v-model="formData.username" 
                placeholder="请输入用户名"
                prefix-icon="User"
              />
            </el-form-item>
            
            <!-- 昵称 -->
            <el-form-item prop="alias">
              <el-input 
                v-model="formData.alias" 
                placeholder="请输入昵称"
                prefix-icon="Postcard"
              />
            </el-form-item>
            
            <!-- 邮箱 -->
            <el-form-item prop="email">
              <el-input 
                v-model="formData.email" 
                placeholder="请输入邮箱"
                prefix-icon="Message"
              />
            </el-form-item>
            
            <!-- 密码 -->
            <el-form-item prop="password">
              <el-input 
                v-model="formData.password" 
                type="password"
                show-password
                placeholder="请输入密码"
                prefix-icon="Lock"
              />
              <p class="mt-1 text-xs text-gray-500">密码长度至少8位，包含字母和数字</p>
            </el-form-item>
            
            <!-- 确认密码 -->
            <el-form-item prop="confirmPassword">
              <el-input 
                v-model="formData.confirmPassword" 
                type="password"
                show-password
                placeholder="请再次输入密码"
                prefix-icon="Lock"
              />
            </el-form-item>
            
            <!-- 用户类型选择 -->
            <!--
            <el-form-item prop="role">
              <el-radio-group v-model="formData.role" size="large" class="flex justify-between w-full">
                <el-radio-button v-for="role in roles" :key="role.value" :label="role.value" class="flex-1 text-center">
                  <div class="flex flex-col items-center">
                    <el-icon class="mb-1">
                      <School v-if="role.value === 'student'" />
                      <Reading v-else-if="role.value === 'teacher'" />
                      <Setting v-else-if="role.value === 'admin'" />
                    </el-icon>
                    <span class="text-xs">{{ role.label }}</span>
                  </div>
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            -->
            <!-- 用户协议 -->
            <el-form-item prop="terms">
              <el-checkbox v-model="formData.terms">
                <span class="text-sm">我已阅读并同意</span>
                <router-link to="/auth/terms" class="text-blue-600 hover:text-blue-500">《用户协议》</router-link>
                <span class="text-gray-700">和</span>
                <router-link to="/auth/privacy" class="text-blue-600 hover:text-blue-500">《隐私政策》</router-link>
              </el-checkbox>
            </el-form-item>
            
            <!-- 注册按钮 -->
            <el-button type="primary" @click="submitForm(ruleFormRef)" class="w-full">
              立即注册
            </el-button>
          </el-form>
          
          <!-- 登录链接 -->
          <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
              已有账号? <router-link to="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">立即登录</router-link>
            </p>
          </div>
        </div>
      </el-card>
      
      <!-- 页脚 -->
      <div class="mt-4 text-center">
        <p class="text-xs text-gray-500">
          © 2025 智慧课堂. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api/auth'

const router = useRouter()
const authStore = useAuthStore()
const showPassword = ref(false)
const ruleFormRef = ref()

// 角色数据
const roles = [
  { value: 'student', label: '学生', icon: 'fas fa-user-graduate' },
  { value: 'teacher', label: '教师', icon: 'fas fa-chalkboard-teacher' },
  { value: 'admin', label: '管理员', icon: 'fas fa-user-cog' }
]

// 表单数据
const formData = reactive({
  username: '',
  alias: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'student',
  terms: false
})

// 表单验证规则
const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 8) {
    callback(new Error('密码长度至少8位'))
  } else if (!/(?=.*[A-Za-z])(?=.*\d)/.test(value)) {
    callback(new Error('密码需包含字母和数字'))
  } else {
    callback()
  }
}

const validatePass2 = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== formData.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  alias: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { validator: validatePass, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validatePass2, trigger: 'blur' }
  ],
  terms: [
    { type: 'boolean', message: '请阅读并同意用户协议和隐私政策', trigger: 'change' },
    { required: true, message: '请阅读并同意用户协议和隐私政策', trigger: 'change' }
  ]
})

const submitForm = async (formEl) => {
  if (!formEl) return
  
  await formEl.validate((valid, fields) => {
    if (valid) {
      handleRegister()
    } else {
      ElMessage.error('表单验证失败，请检查输入')
    }
  })
}

// 处理注册
const handleRegister = async () => {
  try {
    // 检查是否同意用户协议
    if (!formData.terms) {
      ElMessage.error('请阅读并同意用户协议和隐私政策')
      return
    }
    
    // 移除确认密码字段，因为后端不需要
    const { confirmPassword, terms, ...registerData } = formData
    
    // 发送注册请求
    await authApi.register(registerData)
    
    // 注册成功提示
    ElMessage.success('注册成功！请登录您的账号。')
    
    // 跳转到登录页面
    router.push('/auth/login')
  } catch (error) {
    // 处理错误情况
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          ElMessage.error(JSON.stringify(data) || '注册信息有误，请检查后重试')
          break
        default:
          ElMessage.error('注册失败，请稍后重试')
      }
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
  }
}
</script>

<style scoped>
:deep(.el-radio-button__inner) {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: auto;
  padding: 8px;
}
</style> 