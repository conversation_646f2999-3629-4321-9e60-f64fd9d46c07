import os
import requests
from typing import List, Optional, Dict, Any

from .. import config


class KnowledgeBaseAPI:
    """知识库API封装类"""

    def __init__(self):
        """
        初始化知识库API客户端
        
        Args:
            base_url: API基础URL
            api_key: API密钥
        """
        self.base_url = config.RAG_FLOW_API_URL
        self.api_key = config.RAG_FLOW_API_KEY
        self.headers = {
            'Authorization': f'Bearer {self.api_key}'
        }

    # 数据集操作
    def create_dataset(
            self,
            name: str,
            avatar: Optional[str] = None,
            description: Optional[str] = None,
            embedding_model: Optional[str] = "text-embedding-v3",
            permission: str = "me",
            chunk_method: str = "naive"
    ) -> Dict[str, Any]:
        """
        创建新的数据集

        Args:
            name: 要创建的数据集的唯一名称
            avatar: 头像的Base64编码
            description: 数据集的简要说明
            embedding_model: 要使用的嵌入模型名称，例如："BAAI/bge-zh-v1.5"
            permission: 指定谁可以访问数据集，可选值："me"(默认)或"team"
            chunk_method: 分块方法，可选值："naive"(默认)、"manual"、"qa"、"table"、
                         "paper"、"book"、"laws"、"presentation"、"picture"、
                         "one"、"knowledge_graph"、"email"

        Returns:
            API响应
        """
        url = f"{self.base_url}/api/v1/datasets"

        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }

        # 构建请求数据
        data = {
            "name": name
        }

        data["embedding_model"] = embedding_model
        # 添加可选参数
        if avatar:
            data["avatar"] = avatar
        if description:
            data["description"] = description
        if permission != "me":
            data["permission"] = permission
        if chunk_method != "naive":
            data["chunk_method"] = chunk_method

        # 根据chunk_method构建正确的parser_config
        if chunk_method == "naive":
            data["parser_config"] = {
                "chunk_token_num": 1024,  # 使用正确的属性名chunk_token_num而非chunk_token_count
                "layout_recognize": True,
                "html4excel": False,
                "delimiter": "\\n",
                "raptor": {
                    "use_raptor": False
                }
            }
        elif chunk_method in ["qa", "manual", "paper", "book", "laws", "presentation"]:
            data["parser_config"] = {
                "raptor": {
                    "use_raptor": False
                }
            }
        else:  # 对于table、picture、one、email等
            data["parser_config"] = {}

        response = requests.post(url, headers=headers, json=data)
        return response.json()
    
    def delete_datasets(self, dataset_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        删除数据集
        
        Args:
            dataset_ids: 要删除的数据集ID列表，如果为None则删除所有数据集
            
        Returns:
            Dict[str, Any]: API响应
        """
        url = f"{self.base_url}/api/v1/datasets"
        
        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }
        
        data = {}
        if dataset_ids:
            data["ids"] = dataset_ids
        
        response = requests.delete(url, headers=headers, json=data)
        return response.json()

    # 文档操作
    def upload_documents(self, dataset_id: str, file_paths: List[str]) -> Dict[str, Any]:
        """
        上传文档到指定数据集
        
        Args:
            dataset_id: 数据集ID
            file_paths: 要上传的文件路径列表
        
        Returns:
            API响应
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents"
        
        headers = {
            **self.headers,
            # multipart/form-data的Content-Type会由requests自动设置
        }
        
        files = []
        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            files.append(('file', (file_name, open(file_path, 'rb'))))
        
        response = requests.post(url, headers=headers, files=files)
        
        # 关闭所有打开的文件
        for _, (_, file_obj) in files:
            file_obj.close()
            
        return response.json()

    def parse_documents(self, dataset_id: str, document_ids: List[str]) -> Dict[str, Any]:
        """
        解析指定数据集中的文档

        Args:
            dataset_id: 数据集ID
            document_ids: 要解析的文档ID列表

        Returns:
            API响应
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/chunks"

        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }

        data = {
            "document_ids": document_ids
        }

        response = requests.post(url, headers=headers, json=data)
        return response.json()

    def list_documents(
        self,
        dataset_id: str,
        page: int = 1,
        page_size: int = 30,
        orderby: str = "create_time",
        desc: bool = True,
        keywords: Optional[str] = None,
        document_id: Optional[str] = None,
        document_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取指定数据集中的文档列表
        
        Args:
            dataset_id: 数据集ID
            page: 指定文档的显示页面，默认为1
            page_size: 每页文档的数量，默认为30
            orderby: 文档排序字段，可选值："create_time"(默认)或"update_time"
            desc: 是否按降序排序，默认为True
            keywords: 用于匹配文档标题的关键字
            document_id: 按文档ID过滤
            document_name: 按文档名称过滤
        
        Returns:
            API响应
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents"
        
        # 构建查询参数
        params = {
            "page": page,
            "page_size": page_size,
            "orderby": orderby,
            "desc": str(desc).lower()  # 转换为小写的"true"或"false"
        }
        
        # 添加可选参数
        if keywords:
            params["keywords"] = keywords
        if document_id:
            params["id"] = document_id
        if document_name:
            params["name"] = document_name
            
        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }
            
        response = requests.get(url, headers=headers, params=params)
        return response.json()

    def get_document_status(self, dataset_id: str, document_id: str) -> Dict[str, Any]:
        """
        获取文档解析状态
        
        Args:
            dataset_id: 数据集ID
            document_id: 文档ID
            
        Returns:
            Dict[str, Any]: API响应，包含文档的解析状态信息
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        
        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }
        
        response = requests.get(url, headers=headers)
        return response.json()
    
    def get_documents_status(self, dataset_id: str, document_ids: List[str] = None) -> Dict[str, Any]:
        """
        批量获取文档解析状态
        
        Args:
            dataset_id: 数据集ID
            document_ids: 文档ID列表，如果为None则获取数据集中所有文档
            
        Returns:
            Dict[str, Any]: API响应，包含文档列表及其解析状态
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents"
        
        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }
        
        # 构建查询参数
        params = {
            "page": 1,
            "page_size": 100,  # 设置较大的页面大小
            "orderby": "create_time",
            "desc": "true"
        }
        
        # 如果指定了文档ID列表，则添加到参数中
        if document_ids:
            params["ids"] = ",".join(document_ids)
        
        response = requests.get(url, headers=headers, params=params)
        return response.json()
    
    def delete_documents(self, dataset_id: str, document_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        删除指定数据集中的文档
        
        Args:
            dataset_id: 数据集ID
            document_ids: 要删除的文档ID列表，如果为None则删除数据集中所有文档
            
        Returns:
            Dict[str, Any]: API响应
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents"
        
        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }
        
        data = {}
        if document_ids:
            data["ids"] = document_ids
        
        response = requests.delete(url, headers=headers, json=data)
        return response.json()
    
    # 检索操作
    def retrieve(
        self,
        question: str,
        dataset_ids: Optional[List[str]] = None,
        document_ids: Optional[List[str]] = None,
        similarity_threshold: float = 0.2,
        vector_similarity_weight: float = 0.3,
        top_k: int = 1024,
        rerank_id: Optional[str] = None,
        keyword: bool = False,
        highlight: bool = False
    ) -> Dict[str, Any]:
        """
        从指定的数据集中检索块
        
        Args:
            question: 用户查询或查询的关键字
            dataset_ids: 要搜索的数据集ID列表
            document_ids: 要搜索的文档ID列表
            page: 指定显示区块的页面，默认为1
            page_size: 每页的最大块数，默认为30
            similarity_threshold: 最小相似度得分，默认为0.2
            vector_similarity_weight: 向量余弦相似度的权重，默认为0.3
            top_k: 参与向量余弦计算的块数，默认为1024
            rerank_id: 重排序模型的ID
            keyword: 是否启用基于关键字的匹配，默认为False
            highlight: 是否启用结果中匹配术语的突出显示，默认为False
        
        Returns:
            API响应
        """
        url = f"{self.base_url}/api/v1/retrieval"
        
        headers = {
            **self.headers,
            'Content-Type': 'application/json'
        }
        
        data = {
            "question": question
        }
        
        # 添加可选参数
        if dataset_ids:
            data["dataset_ids"] = dataset_ids
        if document_ids:
            data["document_ids"] = document_ids
        if similarity_threshold != 0.2:
            data["similarity_threshold"] = similarity_threshold
        if vector_similarity_weight != 0.3:
            data["vector_similarity_weight"] = vector_similarity_weight
        if top_k != 1024:
            data["top_k"] = top_k
        if rerank_id:
            data["rerank_id"] = rerank_id
        if keyword:
            data["keyword"] = keyword
        if highlight:
            data["highlight"] = highlight
        
        response = requests.post(url, headers=headers, json=data)
        return response.json()


