<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="作业管理"
    activePage="assignment-management"
  >
    <div class="space-y-6">
      <!-- 功能区 -->
      <div class="flex flex-wrap justify-between items-center gap-4 mb-4">
        <div class="flex gap-3">
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
            @click="showCreateAssignmentModal = true"
          >
            <span class="material-icons text-sm">add</span>
            创建作业
          </button>
          
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_upload</span>
            导入作业
          </button>
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_download</span>
            导出记录
          </button>
        </div>
        <div class="flex gap-3">
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索作业..." 
              class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
              <span class="material-icons text-gray-600">filter_list</span>
              筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <div class="space-y-3">
                <div class="font-medium flex items-center gap-1.5">
                  <span class="material-icons text-base text-gray-600">school</span>
                  课程
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="course in ['人工智能基础', '高级数据结构', '计算机网络']" 
                    :key="course"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.courses.includes(course) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('courses', course)"
                  >
                    <span v-if="selectedFilters.courses.includes(course)" class="material-icons text-xs">check_circle</span>
                    {{ course }}
                  </span>
                </div>
                <div class="font-medium mt-3 flex items-center gap-1.5">
                   <span class="material-icons text-base text-gray-600">label</span>
                   状态
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="status in ['进行中', '已截止', '草稿']" 
                    :key="status"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.statuses.includes(status) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('statuses', status)"
                  >
                    <span v-if="selectedFilters.statuses.includes(status)" class="material-icons text-xs">check_circle</span>
                    {{ status }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="flex flex-wrap gap-4">
        <div class="bg-blue-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">总作业数量</p>
              <p class="text-2xl font-bold text-gray-800">32</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <span class="material-icons text-blue-600 text-xl">description</span>
            </div>
          </div>
        </div>
        <div class="bg-green-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">进行中作业</p>
              <p class="text-2xl font-bold text-gray-800">15</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
              <span class="material-icons text-green-600 text-xl">hourglass_top</span>
            </div>
          </div>
        </div>
        <div class="bg-red-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">已截止作业</p>
              <p class="text-2xl font-bold text-gray-800">24</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
              <span class="material-icons text-red-600 text-xl">assignment</span>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">草稿</p>
              <p class="text-2xl font-bold text-gray-800">10</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
              <span class="material-icons text-gray-600 text-xl">edit_note</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类筛选区 -->
      <div class="flex border-b border-gray-200">
        <button 
          v-for="tab in tabs" 
          :key="tab.value"
          @click="currentTab = tab.value"
          class="py-3 px-6 font-medium relative"
          :class="currentTab === tab.value ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'"
        >
          {{ tab.label }}
          <span 
            v-if="currentTab === tab.value" 
            class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"
          ></span>
        </button>
      </div>

      <!-- 表格控制区 -->
      <div class="flex justify-between items-center mb-4 mt-6">
        <div class="flex items-center">
          <input 
            type="checkbox" 
            id="select-all"
            class="rounded text-blue-600 focus:ring-blue-500"
            @change="toggleSelectAll"
            :checked="selectedAssignments.length === filteredAssignments.length && filteredAssignments.length > 0"
          />
          <label for="select-all" class="ml-2 text-sm text-gray-600">全选</label>
          
          <div class="ml-4 flex items-center" v-if="selectedAssignments.length > 0">
            <span class="text-sm text-gray-600">已选择 {{ selectedAssignments.length }} 个作业</span>
            <button class="ml-3 text-sm text-blue-600 hover:text-blue-800">批量操作</button>
          </div>
        </div>
        
        <div class="flex items-center gap-4">
          <div class="flex items-center">
            <span class="text-sm text-gray-600 mr-2">每页显示：</span>
            <select 
              v-model="pageSize" 
              class="border border-gray-300 rounded-md px-2 py-1 text-sm"
            >
              <option :value="10">10条</option>
              <option :value="20">20条</option>
              <option :value="50">50条</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 作业表格 -->
      <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="w-16 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                选择
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                作业信息
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                课程
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                截止日期
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                提交情况
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="assignment in paginatedAssignments" :key="assignment.id" class="hover:bg-gray-50 cursor-pointer" @click="viewAssignmentDetails(assignment)">
              <td class="px-3 py-4 whitespace-nowrap" @click.stop>
                <input 
                  type="checkbox" 
                  :checked="selectedAssignments.includes(assignment.id)"
                  @change="toggleAssignmentSelection(assignment.id)"
                  class="rounded text-blue-600 focus:ring-blue-500"
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                    <span class="material-icons text-blue-500 text-lg">description</span>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ assignment.title }}</div>
                    <div class="text-xs text-gray-500">{{ assignment.type }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ assignment.course }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ assignment.dueDate }}</div>
                <div class="text-xs text-gray-500">{{ getDaysRemaining(assignment.dueDate) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden mr-2">
                    <div 
                      class="h-full bg-blue-500 rounded-full" 
                      :style="{ width: `${assignment.submissionRate}%` }"
                    ></div>
                  </div>
                  <span class="text-sm text-gray-500">{{ assignment.submissionRate }}%</span>
                </div>
                <div class="text-xs text-gray-500 mt-1">{{ assignment.submittedCount }}/{{ assignment.totalStudents }} 已提交</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" 
                  :class="{
                    'bg-green-100 text-green-800': assignment.status === 'active',
                    'bg-red-100 text-red-800': assignment.status === 'closed',
                    'bg-gray-100 text-gray-800': assignment.status === 'draft'
                  }">
                  {{ getStatusLabel(assignment.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" @click.stop>
                <button 
                  class="inline-flex items-center justify-center w-8 h-8 rounded-full text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors mr-1" 
                  @click="viewAssignmentDetails(assignment)"
                  title="查看详情"
                >
                  <span class="material-icons text-sm">visibility</span>
                </button>
                <button 
                  class="inline-flex items-center justify-center w-8 h-8 rounded-full text-green-600 bg-green-50 hover:bg-green-100 transition-colors mr-1" 
                  @click="editAssignment(assignment)"
                  title="编辑作业"
                >
                  <span class="material-icons text-sm">edit</span>
                </button>
                <button 
                  class="inline-flex items-center justify-center w-8 h-8 rounded-full text-red-600 bg-red-50 hover:bg-red-100 transition-colors" 
                  @click="confirmDeleteAssignment(assignment)"
                  title="删除作业"
                >
                  <span class="material-icons text-sm">delete</span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页控制 -->
      <div class="mt-5 flex justify-between items-center">
        <div class="text-sm text-gray-700">
          共 <span class="font-medium">{{ assignments.length }}</span> 条记录
        </div>
        <div class="flex items-center space-x-2">
          <button 
            class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="currentPage === 1"
            @click="currentPage--"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            上一页
          </button>
          
          <div v-for="pageNumber in displayedPageNumbers" :key="pageNumber">
            <button 
              v-if="pageNumber !== '...'"
              @click="currentPage = pageNumber"
              class="px-3 py-1 rounded-md text-sm font-medium"
              :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'"
            >
              {{ pageNumber }}
            </button>
            <span v-else class="text-gray-500 px-2">...</span>
          </div>
          
          <button 
            class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            下一页
          </button>
          
          <div class="flex items-center ml-2">
            <span class="text-sm text-gray-700 mr-2">前往</span>
            <input 
              type="number" 
              v-model.number="goToPage" 
              min="1" 
              :max="totalPages"
              class="w-12 border border-gray-300 rounded-md px-2 py-1 text-sm"
            />
            <span class="text-sm text-gray-700 mx-2">页</span>
            <button 
              @click="jumpToPage"
              class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-blue-700 hover:bg-blue-50"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>

  <!-- 题库选择面板 -->
  <div v-if="showQuestionBankPanel" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-[60]">
    <div class="bg-white rounded-lg shadow-xl w-11/12 h-[85vh] overflow-hidden">
      <div class="flex h-full">
        <!-- 左侧知识结构 -->
        <div class="w-1/4 border-r border-gray-200 flex flex-col h-full">
          <div class="py-3 px-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-base font-medium text-gray-700">知识结构</h3>
            <button class="p-1 rounded-full hover:bg-gray-100">
              <span class="material-icons text-gray-500 text-sm">add</span>
            </button>
          </div>
          <div class="flex-1 overflow-auto p-3">
            <div v-for="(course, index) in courses" :key="index" class="mb-2">
              <div 
                class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer"
                @click="toggleCourseInBank(index)"
              >
                <span class="material-icons text-gray-500 text-sm mr-1">
                  {{ course.expanded ? 'expand_more' : 'chevron_right' }}
                </span>
                <span class="material-icons text-blue-500 text-sm mr-1.5">menu_book</span>
                <span class="text-sm">{{ course.name }}</span>
              </div>
              
              <!-- 章节列表 -->
              <div v-if="course.expanded" class="ml-6">
                <div v-for="(chapter, chapterIndex) in course.chapters" :key="chapterIndex" class="my-1">
                  <div 
                    class="flex items-center p-1.5 hover:bg-gray-50 rounded cursor-pointer group"
                    @click="toggleChapterInBank(index, chapterIndex)"
                  >
                    <span class="material-icons text-gray-500 text-sm mr-1">
                      {{ chapter.expanded ? 'expand_more' : 'chevron_right' }}
                    </span>
                    <span class="material-icons text-green-500 text-sm mr-1.5">folder</span>
                    <span class="text-sm">{{ chapter.name }}</span>
                  </div>
                  
                  <!-- 小节列表 -->
                  <div v-if="chapter.expanded" class="ml-6">
                    <div 
                      v-for="(section, sectionIndex) in chapter.sections" 
                      :key="sectionIndex"
                      class="flex items-center p-1.5 hover:bg-gray-50 rounded cursor-pointer group"
                      @click="selectSectionInBank(index, chapterIndex, sectionIndex)"
                      :class="{'bg-blue-50': isSelectedSectionInBank(index, chapterIndex, sectionIndex)}"
                    >
                      <span class="material-icons text-yellow-500 text-sm mr-1.5">article</span>
                      <span class="text-sm">{{ section.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧题目列表 -->
        <div class="w-3/4 flex flex-col">
          <div class="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">从题库选择题目</h3>
            <button @click="showQuestionBankPanel = false" class="text-gray-400 hover:text-gray-500">
              <span class="material-icons">close</span>
            </button>
          </div>
          
          <!-- 搜索和筛选 -->
          <div class="p-4 border-b border-gray-200">
            <div class="flex gap-4">
              <div class="flex-1 relative">
                <input 
                  type="text" 
                  v-model="questionBankSearchQuery"
                  placeholder="搜索题目..." 
                  class="w-full border border-gray-300 rounded-md pl-9 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
              </div>
              
              <select 
                v-model="questionBankTypeFilter" 
                class="border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">所有题型</option>
                <option value="单选题">单选题</option>
                <option value="多选题">多选题</option>
                <option value="判断题">判断题</option>
                <option value="填空题">填空题</option>
                <option value="简答题">简答题</option>
                <option value="编程题">编程题</option>
              </select>
              
              <select 
                v-model="questionBankDifficultyFilter" 
                class="border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">所有难度</option>
                <option value="简单">简单</option>
                <option value="中等">中等</option>
                <option value="困难">困难</option>
              </select>
            </div>
          </div>
          
          <!-- 题目表格 -->
          <div class="flex-1 overflow-auto p-4">
            <div class="border border-gray-200 rounded-md">
              <div class="flex items-center bg-gray-50 px-4 py-2 border-b">
                <div class="w-12 text-sm font-medium text-gray-500">选择</div>
                <div class="flex-1 text-sm font-medium text-gray-500">题目</div>
                <div class="w-20 text-sm font-medium text-gray-500">题型</div>
                <div class="w-20 text-sm font-medium text-gray-500">难度</div>
                <div class="w-32 text-sm font-medium text-gray-500">课程</div>
                <div class="w-20 text-sm font-medium text-gray-500">分值</div>
                <div class="w-24 text-sm font-medium text-gray-500">操作</div>
              </div>
              
              <div class="max-h-[400px] overflow-y-auto">
                <div 
                  v-for="question in filteredBankQuestions" 
                  :key="question.id"
                  class="flex items-center px-4 py-3 border-b last:border-b-0 hover:bg-gray-50"
                >
                  <div class="w-12">
                    <input 
                      type="checkbox" 
                      :checked="selectedBankQuestions.includes(question.id)"
                      @change="toggleQuestionSelection(question.id)"
                      class="rounded text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                  <div class="flex-1 text-sm text-gray-800 truncate" :title="question.title">{{ question.title }}</div>
                  <div class="w-20 text-xs">
                    <span 
                      class="px-2 py-0.5 rounded-full" 
                      :class="getQuestionTypeClass(question.type)"
                    >
                      {{ question.type }}
                    </span>
                  </div>
                  <div class="w-20 text-xs">
                    <span 
                      class="px-2 py-0.5 rounded-full" 
                      :class="getDifficultyClass(question.difficulty)"
                    >
                      {{ question.difficulty }}
                    </span>
                  </div>
                  <div class="w-32 text-xs text-gray-600 truncate">{{ question.course }}</div>
                  <div class="w-20 text-sm text-gray-700">{{ question.points }}分</div>
                  <div class="w-24 flex gap-1 justify-end">
                    <button 
                      @click="showQuestionPreviewModal(question)" 
                      class="p-1 text-blue-600 hover:bg-blue-50 rounded"
                    >
                      <span class="material-icons text-sm">visibility</span>
                    </button>
                    <button 
                      @click="addQuestionFromBankToAssignment(question)" 
                      class="p-1 text-green-600 hover:bg-green-50 rounded"
                    >
                      <span class="material-icons text-sm">add</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页和操作按钮 -->
          <div class="p-4 border-t border-gray-200 flex justify-between items-center">
            <div>
              <span v-if="selectedBankQuestions.length > 0" class="text-sm text-gray-700">
                已选择 <span class="font-medium">{{ selectedBankQuestions.length }}</span> 题
              </span>
            </div>
            
            <div class="flex gap-3">
              <button 
                @click="showQuestionBankPanel = false" 
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button 
                @click="addSelectedQuestionsToAssignment" 
                class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                :disabled="selectedBankQuestions.length === 0"
                :class="{ 'opacity-50 cursor-not-allowed': selectedBankQuestions.length === 0 }"
              >
                添加所选题目
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 创建作业模态框 -->
  <div v-if="showCreateAssignmentModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-2xl w-11/12 max-w-7xl max-h-[90vh] overflow-auto">
      <div class="flex flex-col h-full">
        <!-- 标题栏 -->
        <div class="py-5 px-8 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <span class="material-icons text-blue-600 text-xl">assignment</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-800">创建新作业</h3>
          </div>
          <button @click="showCreateAssignmentModal = false" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
            <span class="material-icons">close</span>
          </button>
        </div>
        
        <div class="flex flex-1 overflow-hidden">
          <!-- 左侧作业信息表单 -->
          <div class="w-2/5 p-6 border-r border-gray-200 overflow-y-auto bg-white">
            <div class="max-w-lg mx-auto space-y-6">
              <!-- 基本信息部分 -->
              <div class="space-y-5">
                <div class="flex items-center gap-2 pb-2 border-b border-gray-100">
                  <span class="material-icons text-blue-500 p-1 bg-blue-50 rounded">info</span>
                  <h4 class="text-md font-semibold text-gray-800">基本信息</h4>
                </div>
                
                <div class="grid grid-cols-1 gap-5">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">作业标题 <span class="text-red-500">*</span></label>
                    <input 
                      type="text" 
                      v-model="assignmentForm.title"
                      placeholder="请输入作业标题" 
                      class="w-full border border-gray-300 rounded-lg px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <p v-if="showFormValidation && !assignmentForm.title.trim()" class="mt-1 text-xs text-red-500">
                      请输入作业标题
                    </p>
                  </div>
                  
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">所属课程 <span class="text-red-500">*</span></label>
                      <div class="relative">
                        <select 
                          v-model="assignmentForm.course"
                          class="appearance-none w-full border border-gray-300 rounded-lg pl-4 pr-10 py-2.5 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">请选择课程</option>
                          <option v-for="course in courses" :key="course.name" :value="course.name">
                            {{ course.name }}
                          </option>
                        </select>
                        <span class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                          arrow_drop_down
                        </span>
                      </div>
                      <p v-if="showFormValidation && !assignmentForm.course" class="mt-1 text-xs text-red-500">
                        请选择所属课程
                      </p>
                    </div>
                    
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">作业类型 <span class="text-red-500">*</span></label>
                      <div class="relative">
                        <select 
                          v-model="assignmentForm.type"
                          class="appearance-none w-full border border-gray-300 rounded-lg pl-4 pr-10 py-2.5 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">请选择类型</option>
                          <option value="课后作业">课后作业</option>
                          <option value="实验报告">实验报告</option>
                          <option value="测验">测验</option>
                          <option value="项目作业">项目作业</option>
                          <option value="编程题">编程题</option>
                        </select>
                        <span class="material-icons absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                          arrow_drop_down
                        </span>
                      </div>
                      <p v-if="showFormValidation && !assignmentForm.type" class="mt-1 text-xs text-red-500">
                        请选择作业类型
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 时间设置部分 -->
              <div class="space-y-5">
                <div class="flex items-center gap-2 pb-2 border-b border-gray-100">
                  <span class="material-icons text-green-500 p-1 bg-green-50 rounded">event</span>
                  <h4 class="text-md font-semibold text-gray-800">时间设置</h4>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">截止日期 <span class="text-red-500">*</span></label>
                  <div class="relative">
                    <el-date-picker
                      v-model="assignmentForm.dueDate"
                      type="datetime"
                      placeholder="选择截止日期时间"
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DDThh:mm"
                      :size="'large'"
                      class="!w-full"
                      :popper-class="'text-base'"
                    />
                  </div>
                  <p v-if="showFormValidation && !assignmentForm.dueDate" class="mt-1 text-xs text-red-500">
                    请设置截止日期
                  </p>
                </div>
              </div>
              
              <!-- 详细描述部分 -->
              <div class="space-y-5">
                <div class="flex items-center gap-2 pb-2 border-b border-gray-100">
                  <span class="material-icons text-amber-500 p-1 bg-amber-50 rounded">description</span>
                  <h4 class="text-md font-semibold text-gray-800">详细描述</h4>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">作业描述</label>
                  <textarea 
                    v-model="assignmentForm.description"
                    rows="4"
                    placeholder="请输入作业描述..." 
                    class="w-full border border-gray-300 rounded-lg px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ></textarea>
                </div>
              </div>
              
              <!-- 其他设置部分 -->
              <div class="space-y-5">
                <div class="flex items-center gap-2 pb-2 border-b border-gray-100">
                  <span class="material-icons text-purple-500 p-1 bg-purple-50 rounded">settings</span>
                  <h4 class="text-md font-semibold text-gray-800">其他设置</h4>
                </div>
                
                <div class="flex justify-between items-center">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">总分值</label>
                    <div class="flex items-center gap-2">
                      <div class="flex items-center bg-blue-50 rounded-lg px-3 py-1.5 border border-blue-100">
                        <input 
                          type="number" 
                          v-model="assignmentForm.totalPoints"
                          readonly
                          class="w-12 bg-transparent text-blue-700 font-semibold text-lg border-none focus:outline-none"
                        />
                        <span class="text-blue-600 font-medium">分</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex items-center">
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input 
                        type="checkbox" 
                        v-model="assignmentForm.isDraft"
                        class="sr-only peer"
                      />
                      <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      <span class="ml-3 text-sm font-medium text-gray-700">保存为草稿</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧题目管理 -->
          <div class="w-3/5 flex flex-col overflow-hidden">
            <!-- 题目管理标题栏 -->
            <div class="sticky top-0 bg-white z-10 border-b border-gray-200">
              <div class="px-6 py-4">
                <div class="flex justify-between items-center">
                  <div>
                    <h4 class="text-lg font-medium text-gray-800 flex items-center gap-2">
                      <span class="material-icons text-blue-500">quiz</span>
                      题目管理
                    </h4>
                    <p class="text-sm text-gray-500 mt-1">添加至少一个题目 <span class="text-red-500">*</span></p>
                  </div>
                  <div class="flex gap-2">
                    <button 
                      @click="openQuestionBankFromCreate"
                      class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 flex items-center gap-1.5 transition-colors"
                    >
                      <span class="material-icons text-sm">format_list_bulleted</span>
                      从题库选择
                    </button>
                    <button 
                      @click="openCreateQuestionForm"
                      class="px-4 py-2 bg-white border border-blue-500 text-blue-600 rounded-lg text-sm font-medium hover:bg-blue-50 flex items-center gap-1.5 transition-colors"
                    >
                      <span class="material-icons text-sm">add</span>
                      创建题目
                    </button>
                    <button 
                      class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 flex items-center gap-1.5 transition-colors"
                    >
                      <span class="material-icons text-sm">file_upload</span>
                      批量导入
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 当前选择的题目列表 -->
            <div class="flex-1 p-4 overflow-y-auto">
              <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                <div class="flex items-center bg-gray-50 px-4 py-3 border-b rounded-t-lg">
                  <div class="w-10 text-sm font-medium text-gray-500">序号</div>
                  <div class="flex-1 text-sm font-medium text-gray-500">题目</div>
                  <div class="w-20 text-sm font-medium text-gray-500">题型</div>
                  <div class="w-20 text-sm font-medium text-gray-500">难度</div>
                  <div class="w-20 text-sm font-medium text-gray-500">分值</div>
                  <div class="w-28 text-sm font-medium text-gray-500 text-right">操作</div>
                </div>
              
                <div v-if="assignmentForm.questions.length === 0" class="py-10 flex flex-col items-center justify-center bg-white rounded-b-lg">
                  <div class="w-16 h-16 rounded-full bg-blue-50 flex items-center justify-center mb-3">
                    <span class="material-icons text-blue-400 text-3xl">assignment</span>
                  </div>
                  <p class="text-gray-700 text-base font-medium mb-1">暂无题目</p>
                  <p class="text-gray-500 text-sm mb-3">请使用上方按钮添加题目到作业中</p>
                </div>
                
                <div v-else>
                  <div 
                    v-for="(question, index) in assignmentForm.questions" 
                    :key="question.id"
                    class="flex items-center px-4 py-3 border-b last:border-b-0 hover:bg-gray-50 transition-colors"
                  >
                    <div class="w-10 flex-shrink-0">
                      <div class="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium">
                        {{ index + 1 }}
                      </div>
                    </div>
                    <div class="flex-1 text-sm text-gray-800 truncate" :title="question.title">{{ question.title }}</div>
                    <div class="w-20 flex-shrink-0">
                      <span 
                        class="px-2 py-0.5 rounded-full text-xs font-medium" 
                        :class="getQuestionTypeClass(question.type)"
                      >
                        {{ question.type }}
                      </span>
                    </div>
                    <div class="w-20 flex-shrink-0">
                      <span 
                        class="px-2 py-0.5 rounded-full text-xs font-medium" 
                        :class="getDifficultyClass(question.difficulty)"
                      >
                        {{ question.difficulty }}
                      </span>
                    </div>
                    <div class="w-20 flex-shrink-0">
                      <input 
                        type="number" 
                        v-model="question.points"
                        min="0"
                        class="w-14 border border-gray-300 rounded-md px-2 py-1 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                        @change="updateTotalPoints"
                      />
                    </div>
                    <div class="w-28 flex gap-1 justify-end flex-shrink-0">
                      <button 
                        @click="showQuestionPreviewModal(question)" 
                        class="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
                        title="预览"
                      >
                        <span class="material-icons text-sm">visibility</span>
                      </button>
                      <button 
                        @click="editQuestionInAssignment(index)" 
                        class="p-1 text-amber-600 hover:bg-amber-100 rounded transition-colors"
                        title="编辑"
                      >
                        <span class="material-icons text-sm">edit</span>
                      </button>
                      <button 
                        @click="removeQuestionFromAssignment(index)" 
                        class="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                        title="删除"
                      >
                        <span class="material-icons text-sm">delete</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <div v-if="showFormValidation && assignmentForm.questions.length === 0" class="mt-2 text-xs text-red-500 pl-2">
                请至少添加一个题目
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="px-6 py-4 border-t border-gray-200 bg-white flex justify-between items-center">
              <div class="text-sm text-gray-500">
                <span class="text-red-500">*</span> 为必填项
              </div>
              <div class="flex gap-3">
                <button 
                  @click="showCreateAssignmentModal = false"
                  class="px-5 py-2.5 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button 
                  @click="saveAssignmentAsDraft"
                  class="px-5 py-2.5 border border-gray-300 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors"
                >
                  保存为草稿
                </button>
                <button 
                  @click="saveAssignment"
                  class="px-5 py-2.5 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  :class="{ 'opacity-80 cursor-not-allowed': !isAssignmentFormValid }"
                >
                  发布作业
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 作业详情模态框 -->
  <div v-if="showAssignmentDetailsModal && selectedAssignment" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-xl w-11/12 max-w-4xl max-h-[90vh] overflow-auto">
      <div class="flex flex-col">
        <!-- 标题栏 -->
        <div class="py-4 px-6 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <span class="material-icons text-blue-600 text-xl">assignment</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-800">作业详情</h3>
          </div>
          <button @click="showAssignmentDetailsModal = false" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full transition-colors">
            <span class="material-icons">close</span>
          </button>
        </div>
        
        <!-- 内容区域 -->
        <div class="p-6">
          <!-- 作业基本信息卡片 -->
          <div class="bg-white border border-gray-200 rounded-lg p-5 mb-6 shadow-sm">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-4">
              <div>
                <h4 class="text-lg font-medium text-gray-900">{{ selectedAssignment.title }}</h4>
                <p class="text-sm text-gray-500 mt-1">{{ selectedAssignment.type }} | {{ selectedAssignment.course }}</p>
              </div>
              <div class="mt-3 md:mt-0">
                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full" 
                  :class="{
                    'bg-green-100 text-green-800': selectedAssignment.status === 'active',
                    'bg-red-100 text-red-800': selectedAssignment.status === 'closed',
                    'bg-gray-100 text-gray-800': selectedAssignment.status === 'draft'
                  }">
                  {{ getStatusLabel(selectedAssignment.status) }}
                </span>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 py-3 border-t border-b border-gray-100">
              <div class="flex items-center">
                <span class="material-icons text-blue-500 mr-3">event</span>
                <div>
                  <p class="text-xs text-gray-500">截止日期</p>
                  <p class="text-sm font-medium">{{ selectedAssignment.dueDate }}</p>
                  <p class="text-xs text-gray-500">{{ getDaysRemaining(selectedAssignment.dueDate) }}</p>
                </div>
              </div>
              
              <div class="flex items-center">
                <span class="material-icons text-green-500 mr-3">group</span>
                <div>
                  <p class="text-xs text-gray-500">学生人数</p>
                  <p class="text-sm font-medium">{{ selectedAssignment.totalStudents }} 人</p>
                </div>
              </div>
              
              <div class="flex items-center">
                <span class="material-icons text-purple-500 mr-3">timeline</span>
                <div>
                  <p class="text-xs text-gray-500">提交情况</p>
                  <div class="flex items-center mt-1">
                    <div class="w-24 h-2 bg-gray-200 rounded-full overflow-hidden mr-2">
                      <div 
                        class="h-full bg-blue-500 rounded-full" 
                        :style="{ width: `${selectedAssignment.submissionRate}%` }"
                      ></div>
                    </div>
                    <span class="text-sm text-gray-600">{{ selectedAssignment.submissionRate }}%</span>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">{{ selectedAssignment.submittedCount }}/{{ selectedAssignment.totalStudents }} 已提交</p>
                </div>
              </div>
            </div>
            
            <!-- 作业描述 -->
            <div class="mt-4">
              <h5 class="text-sm font-medium text-gray-700 mb-2">作业描述</h5>
              <p class="text-sm text-gray-600">
                这是《{{ selectedAssignment.title }}》作业的详细描述内容。学生需要按照要求完成作业并在截止日期前提交。
              </p>
            </div>
          </div>
          
          <!-- 题目列表 -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-base font-medium text-gray-800">题目列表</h4>
              <span class="text-sm text-gray-500">共 5 题 / 总分 100 分</span>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div class="flex items-center bg-gray-50 px-4 py-2">
                <div class="w-10 text-sm font-medium text-gray-500">序号</div>
                <div class="flex-1 text-sm font-medium text-gray-500">题目</div>
                <div class="w-20 text-sm font-medium text-gray-500">题型</div>
                <div class="w-20 text-sm font-medium text-gray-500">难度</div>
                <div class="w-16 text-sm font-medium text-gray-500">分值</div>
              </div>
              
              <!-- 模拟题目数据 -->
              <div>
                <div class="flex items-center px-4 py-3 border-t border-gray-100">
                  <div class="w-10">
                    <div class="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium">1</div>
                  </div>
                  <div class="flex-1 text-sm text-gray-800 truncate">Python 基础语法与数据类型</div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      单选题
                    </span>
                  </div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      简单
                    </span>
                  </div>
                  <div class="w-16 text-sm">20分</div>
                </div>
                
                <div class="flex items-center px-4 py-3 border-t border-gray-100">
                  <div class="w-10">
                    <div class="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium">2</div>
                  </div>
                  <div class="flex-1 text-sm text-gray-800 truncate">列表和元组的区别与应用场景</div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                      多选题
                    </span>
                  </div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      中等
                    </span>
                  </div>
                  <div class="w-16 text-sm">20分</div>
                </div>
                
                <div class="flex items-center px-4 py-3 border-t border-gray-100">
                  <div class="w-10">
                    <div class="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium">3</div>
                  </div>
                  <div class="flex-1 text-sm text-gray-800 truncate">Python 函数定义与调用方式</div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      判断题
                    </span>
                  </div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      简单
                    </span>
                  </div>
                  <div class="w-16 text-sm">10分</div>
                </div>
                
                <div class="flex items-center px-4 py-3 border-t border-gray-100">
                  <div class="w-10">
                    <div class="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium">4</div>
                  </div>
                  <div class="flex-1 text-sm text-gray-800 truncate">字典操作与性能分析</div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      简答题
                    </span>
                  </div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      中等
                    </span>
                  </div>
                  <div class="w-16 text-sm">20分</div>
                </div>
                
                <div class="flex items-center px-4 py-3 border-t border-gray-100">
                  <div class="w-10">
                    <div class="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium">5</div>
                  </div>
                  <div class="flex-1 text-sm text-gray-800 truncate">实现简单的文件处理程序</div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      编程题
                    </span>
                  </div>
                  <div class="w-20">
                    <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      困难
                    </span>
                  </div>
                  <div class="w-16 text-sm">30分</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex justify-end gap-3 mt-6">
            <button 
              @click="showAssignmentDetailsModal = false"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
            >
              关闭
            </button>
            <button 
              @click="showAssignmentDetailsModal = false; editAssignment(selectedAssignment)"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              编辑作业
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 删除确认对话框 -->
  <div v-if="showDeleteConfirmModal && selectedAssignment" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="p-6">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0 mr-4">
            <span class="material-icons text-red-600">warning</span>
          </div>
          <h3 class="text-lg font-medium text-gray-900">确认删除作业</h3>
        </div>
        <p class="text-sm text-gray-500 mb-2">
          您确定要删除作业「{{ selectedAssignment.title }}」吗？此操作不可撤销。
        </p>
        <p class="text-sm text-gray-500 mb-6">
          删除后，该作业的所有数据（包括学生提交的内容和成绩）将永久丢失。
        </p>
        <div class="flex justify-end gap-3">
          <button 
            @click="showDeleteConfirmModal = false"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button 
            @click="deleteAssignment"
            class="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
          >
            确认删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 作业状态选项卡
const tabs = [
  { label: '全部作业', value: 'all' },
  { label: '进行中', value: 'active' },
  { label: '已截止', value: 'closed' },
  { label: '草稿', value: 'draft' }
]
const currentTab = ref('all')

// 搜索和筛选
const searchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  courses: [],
  statuses: []
})

// 模态框状态
const showCreateAssignmentModal = ref(false)
const showAssignmentDetailsModal = ref(false)
const showDeleteConfirmModal = ref(false)
const selectedAssignment = ref(null)
const isEditMode = ref(false)

// 切换筛选器
const toggleFilter = (category, value) => {
  if (selectedFilters.value[category].includes(value)) {
    selectedFilters.value[category] = selectedFilters.value[category].filter(item => item !== value)
  } else {
    selectedFilters.value[category].push(value)
  }
}

// 模拟作业数据
const assignments = ref([
  {
    id: 1,
    title: '第一章课后习题',
    type: '课后作业',
    course: '人工智能基础',
    dueDate: '2023-07-15',
    submissionRate: 85,
    submittedCount: 102,
    totalStudents: 120,
    status: 'active'
  },
  {
    id: 2,
    title: '数据结构实验报告',
    type: '实验报告',
    course: '高级数据结构',
    dueDate: '2023-07-20',
    submissionRate: 60,
    submittedCount: 51,
    totalStudents: 85,
    status: 'active'
  },
  {
    id: 3,
    title: '计算机网络期中测验',
    type: '测验',
    course: '计算机网络',
    dueDate: '2023-06-30',
    submissionRate: 100,
    submittedCount: 95,
    totalStudents: 95,
    status: 'closed'
  },
  {
    id: 4,
    title: '期末分组项目',
    type: '项目作业',
    course: '人工智能基础',
    dueDate: '2023-08-10',
    submissionRate: 0,
    submittedCount: 0,
    totalStudents: 120,
    status: 'draft'
  },
  {
    id: 5,
    title: '第二章编程作业',
    type: '编程题',
    course: '高级数据结构',
    dueDate: '2023-07-05',
    submissionRate: 95,
    submittedCount: 81,
    totalStudents: 85,
    status: 'closed'
  }
])

// 表格选择和分页
const selectedAssignments = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const goToPage = ref(1)

// 切换作业选择
const toggleAssignmentSelection = (assignmentId) => {
  if (selectedAssignments.value.includes(assignmentId)) {
    selectedAssignments.value = selectedAssignments.value.filter(id => id !== assignmentId)
  } else {
    selectedAssignments.value.push(assignmentId)
  }
}

// 全选/取消全选
const toggleSelectAll = (e) => {
  if (e.target.checked) {
    selectedAssignments.value = filteredAssignments.value.map(assignment => assignment.id)
  } else {
    selectedAssignments.value = []
  }
}

// 根据过滤条件筛选作业
const filteredAssignments = computed(() => {
  let result = assignments.value

  // 按标签筛选
  if (currentTab.value !== 'all') {
    result = result.filter(assignment => assignment.status === currentTab.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(assignment => 
      assignment.title.toLowerCase().includes(query) || 
      assignment.course.toLowerCase().includes(query)
    )
  }

  // 按选中的筛选器筛选
  if (selectedFilters.value.courses.length > 0) {
    result = result.filter(assignment => selectedFilters.value.courses.includes(assignment.course))
  }
  
  if (selectedFilters.value.statuses.length > 0) {
    result = result.filter(assignment => {
      const statusLabel = getStatusLabel(assignment.status)
      return selectedFilters.value.statuses.includes(statusLabel)
    })
  }

  return result
})

// 分页后的作业列表
const paginatedAssignments = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredAssignments.value.slice(startIndex, startIndex + pageSize.value)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredAssignments.value.length / pageSize.value) || 1
})

// 显示页码
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 跳转到指定页面
const jumpToPage = () => {
  if (goToPage.value >= 1 && goToPage.value <= totalPages.value) {
    currentPage.value = goToPage.value
  } else {
    goToPage.value = currentPage.value
  }
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'active': '进行中',
    'closed': '已截止',
    'draft': '草稿'
  }
  return statusMap[status] || status
}

// 计算剩余天数
const getDaysRemaining = (dueDate) => {
  const now = new Date()
  const due = new Date(dueDate)
  const diffTime = due - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return '已截止'
  } else if (diffDays === 0) {
    return '今日截止'
  } else {
    return `剩余 ${diffDays} 天`
  }
}

// 查看作业详情
const viewAssignmentDetails = (assignment) => {
  selectedAssignment.value = assignment
  showAssignmentDetailsModal.value = true
}

// 编辑作业
const editAssignment = (assignment) => {
  // 复制一份作业数据到表单中
  Object.assign(assignmentForm.value, {
    id: assignment.id,
    title: assignment.title,
    course: assignment.course,
    type: assignment.type,
    dueDate: assignment.dueDate,
    description: assignment.description || '',
    totalPoints: 0,
    isDraft: assignment.status === 'draft',
    questions: []
  })
  
  // 这里应该调用API获取作业的题目列表
  // 暂时模拟一些题目数据
  assignmentForm.value.questions = [
    {
      id: 101,
      title: `${assignment.title}的示例题目1`,
      type: '单选题',
      difficulty: '中等',
      points: 5
    },
    {
      id: 102,
      title: `${assignment.title}的示例题目2`,
      type: '多选题',
      difficulty: '困难',
      points: 10
    }
  ]
  
  // 计算总分
  updateTotalPoints()
  
  // 显示编辑模态框（复用创建作业的模态框）
  showCreateAssignmentModal.value = true
  isEditMode.value = true
}

// 确认删除作业
const confirmDeleteAssignment = (assignment) => {
  selectedAssignment.value = assignment
  showDeleteConfirmModal.value = true
}

// 执行删除操作
const deleteAssignment = () => {
  if (!selectedAssignment.value) return
  
  // 在真实应用中，这里会调用API删除作业
  // 目前仅在前端模拟删除操作
  assignments.value = assignments.value.filter(a => a.id !== selectedAssignment.value.id)
  
  // 关闭确认对话框并重置选中的作业
  showDeleteConfirmModal.value = false
  selectedAssignment.value = null
  
  // 显示成功提示
  alert('作业删除成功')
}

// 题目预览
const previewQuestion = ref({})

// 题库面板中的当前选中节点
const selectedSectionInBankRef = ref({
  courseIndex: null,
  chapterIndex: null,
  sectionIndex: null
})

// 课程知识结构数据（添加这段代码）
const courses = ref([
  {
    name: 'Python程序设计',
    expanded: true,
    chapters: [
      {
        name: '第一章 Python基础',
        expanded: true,
        sections: [
          { name: '1.1 Python环境搭建', id: 'py-1-1' },
          { name: '1.2 变量与数据类型', id: 'py-1-2' },
          { name: '1.3 控制结构', id: 'py-1-3' }
        ]
      },
      {
        name: '第二章 数据结构',
        expanded: false,
        sections: [
          { name: '2.1 列表和元组', id: 'py-2-1' },
          { name: '2.2 字典和集合', id: 'py-2-2' }
        ]
      }
    ]
  },
  {
    name: '人工智能基础',
    expanded: false,
    chapters: [
      {
        name: '第一章 人工智能概述',
        expanded: false,
        sections: [
          { name: '1.1 人工智能历史', id: 'ai-1-1' },
          { name: '1.2 人工智能应用', id: 'ai-1-2' }
        ]
      },
      {
        name: '第二章 机器学习基础',
        expanded: false,
        sections: [
          { name: '2.1 监督学习', id: 'ai-2-1' },
          { name: '2.2 无监督学习', id: 'ai-2-2' }
        ]
      }
    ]
  }
])

// 切换课程展开/收起 (题库面板)
const toggleCourseInBank = (courseIndex) => {
  courses.value[courseIndex].expanded = !courses.value[courseIndex].expanded
}

// 切换章节展开/收起 (题库面板)
const toggleChapterInBank = (courseIndex, chapterIndex) => {
  courses.value[courseIndex].chapters[chapterIndex].expanded = !courses.value[courseIndex].chapters[chapterIndex].expanded
}

// 选择小节 (题库面板)
const selectSectionInBank = (courseIndex, chapterIndex, sectionIndex) => {
  selectedSectionInBankRef.value = {
    courseIndex,
    chapterIndex,
    sectionIndex
  }
  
  // 更新筛选条件
  const course = courses.value[courseIndex]
  const chapter = course.chapters[chapterIndex]
  const section = chapter.sections[sectionIndex]
  
  questionBankCourseFilter.value = course.name
  
  // 刷新题库题目列表
  console.log(`已选择: ${course.name} - ${chapter.name} - ${section.name}`)
}

// 判断是否是当前选中的小节 (题库面板)
const isSelectedSectionInBank = (courseIndex, chapterIndex, sectionIndex) => {
  return selectedSectionInBankRef.value.courseIndex === courseIndex &&
         selectedSectionInBankRef.value.chapterIndex === chapterIndex &&
         selectedSectionInBankRef.value.sectionIndex === sectionIndex
}

// 题库面板状态
const showQuestionBankPanel = ref(false)

// 题库搜索和筛选
const questionBankSearchQuery = ref('')
const questionBankTypeFilter = ref('')
const questionBankDifficultyFilter = ref('')

// 题库题目列表
const bankQuestions = ref([
  {
    id: 1,
    title: 'Python基础知识',
    type: '单选题',
    difficulty: '简单',
    course: 'Python程序设计',
    points: 2
  },
  {
    id: 2,
    title: '数据结构与算法',
    type: '多选题',
    difficulty: '中等',
    course: '高级数据结构',
    points: 3
  },
  {
    id: 3,
    title: '计算机网络基础',
    type: '判断题',
    difficulty: '简单',
    course: '计算机网络',
    points: 1
  },
  {
    id: 4,
    title: '人工智能应用案例',
    type: '简答题',
    difficulty: '困难',
    course: '人工智能基础',
    points: 5
  },
  {
    id: 5,
    title: 'Python编程实践',
    type: '编程题',
    difficulty: '困难',
    course: 'Python程序设计',
    points: 10
  }
])

// 过滤后的题库题目列表
const filteredBankQuestions = computed(() => {
  let result = bankQuestions.value

  // 按搜索关键词筛选
  if (questionBankSearchQuery.value) {
    const query = questionBankSearchQuery.value.toLowerCase()
    result = result.filter(question => 
      question.title.toLowerCase().includes(query) || 
      question.course.toLowerCase().includes(query)
    )
  }

  // 按题型筛选
  if (questionBankTypeFilter.value) {
    result = result.filter(question => question.type === questionBankTypeFilter.value)
  }

  // 按难度筛选
  if (questionBankDifficultyFilter.value) {
    result = result.filter(question => question.difficulty === questionBankDifficultyFilter.value)
  }

  return result
})

// 题库题目选择
const selectedBankQuestions = ref([])

// 切换题目选择
const toggleQuestionSelection = (questionId) => {
  if (selectedBankQuestions.value.includes(questionId)) {
    selectedBankQuestions.value = selectedBankQuestions.value.filter(id => id !== questionId)
  } else {
    selectedBankQuestions.value.push(questionId)
  }
}

// 添加题目到作业
const addQuestionFromBankToAssignment = (question) => {
  console.log('添加题目到作业:', question)
  // 检查题目是否已存在
  const existingIndex = assignmentForm.value.questions.findIndex(q => q.id === question.id)
  if (existingIndex !== -1) {
    // 题目已存在，可以提示用户
    alert('该题目已添加到作业中')
    return
  }
  
  // 添加题目
  assignmentForm.value.questions.push({...question})
  
  // 更新总分值
  updateTotalPoints()
}

// 添加所选题目到作业
const addSelectedQuestionsToAssignment = () => {
  // 获取所选题目
  const selectedQuestions = bankQuestions.value.filter(q => selectedBankQuestions.value.includes(q.id))
  
  // 逐个添加到作业中
  let addedCount = 0
  selectedQuestions.forEach(question => {
    const existingIndex = assignmentForm.value.questions.findIndex(q => q.id === question.id)
    if (existingIndex === -1) {
      assignmentForm.value.questions.push({...question})
      addedCount++
    }
  })
  
  // 提示用户
  alert(`成功添加 ${addedCount} 个题目${addedCount < selectedQuestions.length ? '，其余题目已存在' : ''}`)
  
  // 关闭题库面板
  showQuestionBankPanel.value = false
  
  // 清空选择
  selectedBankQuestions.value = []
}

// 显示题目预览
const showQuestionPreviewModal = (question) => {
  console.log('显示题目预览:', question)
  // 这里应该打开题目预览模态框
}

// 获取题型对应的样式类
const getQuestionTypeClass = (type) => {
  switch (type) {
    case '单选题':
      return 'bg-blue-100 text-blue-800'
    case '多选题':
      return 'bg-indigo-100 text-indigo-800'
    case '填空题':
      return 'bg-green-100 text-green-800'
    case '判断题':
      return 'bg-yellow-100 text-yellow-800'
    case '简答题':
      return 'bg-purple-100 text-purple-800'
    case '编程题':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取难度对应的样式类
const getDifficultyClass = (difficulty) => {
  switch (difficulty) {
    case '简单':
      return 'bg-green-100 text-green-800'
    case '中等':
      return 'bg-yellow-100 text-yellow-800'
    case '困难':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 表单验证
const showFormValidation = ref(false)

// 作业表单
const assignmentForm = ref({
  title: '',
  course: '',
  type: '',
  dueDate: '',
  description: '',
  totalPoints: 0,
  isDraft: false,
  questions: []
})

// 判断作业表单是否有效
const isAssignmentFormValid = computed(() => {
  return assignmentForm.value.title.trim() !== '' && 
         assignmentForm.value.course !== '' &&
         assignmentForm.value.type !== '' &&
         assignmentForm.value.dueDate !== '' &&
         assignmentForm.value.questions.length > 0
})

// 保存作业
const saveAssignment = () => {
  showFormValidation.value = true
  
  if (!isAssignmentFormValid.value) {
    return
  }

  // 这里应该调用API保存作业
  console.log('保存作业:', assignmentForm.value)
  
  // 关闭模态框并重置表单
  showCreateAssignmentModal.value = false
  resetAssignmentForm()
}

// 保存作业为草稿
const saveAssignmentAsDraft = () => {
  if (!assignmentForm.value.title.trim()) {
    showFormValidation.value = true
    return
  }

  assignmentForm.value.isDraft = true
  
  // 这里应该调用API保存草稿
  console.log('保存草稿:', assignmentForm.value)
  
  // 关闭模态框并重置表单
  showCreateAssignmentModal.value = false
  resetAssignmentForm()
}

// 重置作业表单
const resetAssignmentForm = () => {
  assignmentForm.value = {
    title: '',
    course: '',
    type: '',
    dueDate: '',
    description: '',
    totalPoints: 0,
    isDraft: false,
    questions: []
  }
  showFormValidation.value = false
}

// 从作业中移除题目
const removeQuestionFromAssignment = (index) => {
  assignmentForm.value.questions.splice(index, 1)
  
  // 更新总分值
  updateTotalPoints()
}

// 编辑作业中的题目
const editQuestionInAssignment = (index) => {
  // 这里应该打开编辑题目表单
  console.log('编辑作业中的题目:', index, assignmentForm.value.questions[index])
}

// 更新总分值
const updateTotalPoints = () => {
  assignmentForm.value.totalPoints = assignmentForm.value.questions.reduce((sum, question) => sum + Number(question.points || 0), 0)
}

// 从创建作业模态框中打开题库选择面板
const openQuestionBankFromCreate = () => {
  showQuestionBankPanel.value = true
}

// 打开创建题目表单
const openCreateQuestionForm = () => {
  // 这里应该打开创建题目表单
  console.log('打开创建题目表单')
}

// 添加题目到作业时更新总分值
watch(assignmentForm.value.questions, () => {
  updateTotalPoints()
}, { deep: true })
</script>

<style>
/* 增大日期选择器的尺寸 */
input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  padding: 12px;
  margin-right: 8px;
  transform: scale(1.5);
}

/* 移动端优化 */
@media (max-width: 768px) {
  input[type="datetime-local"] {
    min-height: 60px !important;
    font-size: 18px !important;
  }
  input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    padding: 15px;
    transform: scale(1.8);
  }
}
</style> 