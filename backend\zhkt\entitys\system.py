from django.db import models
from django.utils.translation import gettext_lazy as _

class Dept(models.Model):
    """机构模型"""
    name = models.CharField(_('机构名称'), max_length=50)
    code = models.CharField(_('机构代码'), max_length=50, unique=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children', verbose_name=_('父级机构'))
    order = models.IntegerField(_('排序'), default=0)
    leader = models.CharField(_('负责人'), max_length=50, null=True, blank=True)
    phone = models.CharField(_('联系电话'), max_length=20, null=True, blank=True)
    email = models.EmailField(_('邮箱'), null=True, blank=True)
    status = models.BooleanField(_('状态'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('机构')
        verbose_name_plural = _('机构')
        ordering = ['order', 'id']

    def __str__(self):
        return self.name

class Menu(models.Model):
    """菜单模型"""
    name = models.CharField(_('菜单名称'), max_length=50)
    code = models.CharField(_('菜单代码'), max_length=50, unique=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children', verbose_name=_('父级菜单'))
    path = models.CharField(_('路由路径'), max_length=200, null=True, blank=True)
    component = models.CharField(_('组件路径'), max_length=200, null=True, blank=True)
    icon = models.CharField(_('图标'), max_length=50, null=True, blank=True)
    order = models.IntegerField(_('排序'), default=0)
    type = models.CharField(_('类型'), max_length=20, choices=[
        ('M', '目录'),
        ('C', '菜单'),
        ('F', '按钮')
    ])
    permission = models.CharField(_('权限标识'), max_length=100, null=True, blank=True)
    status = models.BooleanField(_('状态'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('菜单')
        verbose_name_plural = _('菜单')
        ordering = ['order', 'id']

    def __str__(self):
        return self.name 