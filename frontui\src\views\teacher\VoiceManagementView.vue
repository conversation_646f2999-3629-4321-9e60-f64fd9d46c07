<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="我的语音"
    activePage="digital-teacher"
    activeSubPage="my-voice"
  >
    <div class="space-y-6">
      <!-- 功能区 -->
      <div class="flex flex-wrap justify-between items-center gap-4 mb-4">
        <div class="flex gap-3">
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
            @click="openCreateVoiceModal"
          >
            <span class="material-icons text-sm">add</span>
            创建语音
          </button>
        </div>
        <div class="flex gap-3">
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索语音..." 
              class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <button 
            class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
          >
            <span class="material-icons text-gray-600">sort</span>
            排序
          </button>
        </div>
      </div>

      <!-- 标签页容器 -->
      <div class="bg-white rounded-lg shadow">
        <!-- 标签页导航 -->
        <div class="border-b">
          <nav class="flex space-x-8 px-4" aria-label="Tabs">
            <button
              @click="activeTab = 'voice-models'"
              :class="[
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                activeTab === 'voice-models'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <i class="material-icons mr-2 text-sm">record_voice_over</i>
              语音模型列表
            </button>
            <button
              @click="activeTab = 'generation-history'"
              :class="[
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm',
                activeTab === 'generation-history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <i class="material-icons mr-2 text-sm">history</i>
              生成历史
            </button>
          </nav>
        </div>

        <!-- 标签页内容 -->
        <div class="p-4">
          <!-- 语音模型列表标签页 -->
          <div v-show="activeTab === 'voice-models'">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-800">语音模型管理</h3>
              <div class="flex gap-2">
                <select 
                  v-model="sortOption" 
                  class="border border-gray-300 rounded px-2 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="newest">最新添加</option>
                  <option value="oldest">最早添加</option>
                  <option value="usage">使用频率</option>
                </select>
              </div>
            </div>

            <!-- 我的声音区域 -->
            <div class="mb-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-base font-medium text-gray-800 flex items-center">
                  <i class="material-icons mr-2">record_voice_over</i>
                  我的声音
                </h3>
              </div>
              
              <div v-if="!voiceModels || voiceModels.length === 0" class="min-h-[300px] py-16 text-center text-gray-500">
                <div v-if="loadingVoiceModels" class="flex flex-col items-center justify-center">
                  <div class="loader">
                    <svg class="circular" viewBox="25 25 50 50">
                      <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="4" stroke-miterlimit="10"/>
                    </svg>
                  </div>
                  <p class="text-sm mt-5 text-blue-600 font-medium">正在加载语音模型...</p>
                </div>
                <div v-else>
                <div class="material-icons text-4xl mb-2">record_voice_over</div>
                <p>您还没有创建语音模型</p>
                <button 
                  class="mt-3 text-blue-600 hover:text-blue-800"
                  @click="openCreateVoiceModal"
                >
                  点击创建新语音模型
                </button>
                </div>
              </div>
              
              <div v-else-if="loadingVoiceModels" class="min-h-[300px] py-24 flex flex-col items-center justify-center">
                <div class="loader">
                  <svg class="circular" viewBox="25 25 50 50">
                    <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="4" stroke-miterlimit="10"/>
                  </svg>
                </div>
                <p class="text-sm mt-5 text-blue-600 font-medium">正在加载语音模型...</p>
              </div>
              <div v-else class="grid grid-cols-5 gap-4">
                <div 
                  v-for="model in voiceModels" 
                  :key="model.id" 
                  class="border rounded-lg overflow-hidden bg-white hover:shadow-md transition-shadow"
                  :class="{'playing-voice': currentlyPlayingId === model.id}"
                >
                  <div class="relative bg-blue-50 h-30 flex items-center justify-center">
                    <!-- 显示头像或默认图标 -->
                    <img 
                      v-if="model.avatar_url" 
                      :src="model.avatar_url" 
                      :alt="model.name"
                      class="w-full h-full object-cover"
                    />
                    <i v-else class="material-icons text-4xl text-blue-300">record_voice_over</i>
                    
                    <div class="absolute top-1 right-1 px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                      自定义
                    </div>
                    <div v-if="currentlyPlayingId === model.id" class="voice-wave-icon">
                      <div v-for="n in 3" :key="n" class="wave-line"></div>
                    </div>
                  </div>
                  
                  <div class="p-2.5">
                    <div class="flex items-start justify-between">
                      <div>
                        <h4 class="text-xs font-medium text-gray-800 truncate max-w-[90px]">{{ model.name }}</h4>
                        <div class="flex flex-col mt-1.5 text-xs text-gray-500">
                          <span class="flex items-center text-xs mb-0.5">
                            {{ model.createdAt }}
                          </span>
                          <span class="flex items-center text-xs">
                            <i class="material-icons text-xs mr-0.5">headphones</i>
                            {{ model.usageCount }}次
                          </span>
                        </div>
                      </div>
                      
                      <div class="flex items-start gap-1">
                        <button 
                          class="p-1.5 text-blue-500 hover:bg-blue-50 rounded-full"
                          @click.stop="playVoiceSample(model.id)"
                          title="试听"
                        >
                          <i class="material-icons text-lg">
                            {{ currentlyPlayingId === model.id ? 'pause_circle' : 'play_circle' }}
                          </i>
                        </button>
                        
                        <div class="dropdown relative">
                          <button class="p-1.5 text-gray-500 hover:bg-gray-100 rounded-full">
                            <i class="material-icons text-xs">more_vert</i>
                          </button>
                          <div class="dropdown-menu absolute right-0 mt-1 bg-white shadow-lg rounded-md overflow-hidden hidden z-10 w-24">
                            <button 
                              class="w-full text-left px-2 py-1 text-xs text-gray-700 hover:bg-gray-100 flex items-center"
                              @click="editVoiceModel(model)"
                            >
                              <i class="material-icons text-xs mr-1">edit</i> 编辑
                            </button>
                            <button 
                              class="w-full text-left px-2 py-1 text-xs text-red-600 hover:bg-red-50 flex items-center"
                              @click="confirmDeleteVoice(model)"
                            >
                              <i class="material-icons text-xs mr-1">delete</i> 删除
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <button 
                      class="w-full mt-2 bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-2 rounded-md flex items-center justify-center gap-1 text-xs"
                      @click="useVoiceModel(model.id)"
                    >
                      <i class="material-icons text-xs">record_voice_over</i>
                      使用此声音
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Pagination for My Voices -->
              <div v-if="myVoicesTotalPages > 1" class="flex justify-center items-center mt-4 space-x-2">
                <button
                  @click="handleMyVoicesPageChange(myVoicesCurrentPage - 1)"
                  :disabled="myVoicesCurrentPage === 1"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <span class="text-sm text-gray-700">第 {{ myVoicesCurrentPage }} 页 / 共 {{ myVoicesTotalPages }} 页</span>
                <button
                  @click="handleMyVoicesPageChange(myVoicesCurrentPage + 1)"
                  :disabled="myVoicesCurrentPage >= myVoicesTotalPages"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
            </div>
            
            <!-- 系统预设区域 -->
            <div>
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-base font-medium text-gray-800 flex items-center">
                  <i class="material-icons mr-2">auto_awesome</i>
                  系统预设
                </h3>
              </div>
              
              <div class="grid grid-cols-5 gap-4">
                <div 
                  v-for="preset in systemVoicePresets" 
                  :key="preset.id" 
                  class="border rounded-lg overflow-hidden bg-white hover:shadow-md transition-shadow"
                  :class="{'playing-voice': currentlyPlayingId === preset.id}"
                >
                  <div class="relative bg-green-50 h-30 flex items-center justify-center">
                    <i class="material-icons text-4xl text-green-300">record_voice_over</i>
                    <div class="absolute top-1 right-1 px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                      系统
                    </div>
                    <div v-if="currentlyPlayingId === preset.id" class="voice-wave-icon">
                      <div v-for="n in 3" :key="n" class="wave-line"></div>
                    </div>
                  </div>
                  
                  <div class="p-2.5">
                    <div class="flex items-start justify-between">
                      <div class="w-full">
                        <h4 class="text-xs font-medium text-gray-800 truncate">{{ preset.name }}</h4>
                        <div class="flex flex-col mt-1.5 text-xs text-gray-500">
                          <span class="flex items-center text-xs">
                            <i class="material-icons text-xs mr-0.5">category</i>
                            {{ preset.category }}
                          </span>
                        </div>
                      </div>
                      
                      <button 
                        class="p-1.5 text-green-500 hover:bg-green-50 rounded-full"
                        @click.stop="playVoiceSample(preset.id)"
                        title="试听"
                      >
                        <i class="material-icons text-lg">
                          {{ currentlyPlayingId === preset.id ? 'pause_circle' : 'play_circle' }}
                        </i>
                      </button>
                    </div>
                    
                    <button 
                      class="w-full mt-2 bg-gray-600 hover:bg-gray-700 text-white py-1.5 px-2 rounded-md flex items-center justify-center gap-1 text-xs"
                      @click="useSystemVoice(preset.id)"
                    >
                      <i class="material-icons text-xs">record_voice_over</i>
                      使用此声音
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 生成历史标签页 -->
          <div v-show="activeTab === 'generation-history'">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-800">生成历史管理</h3>
              <div class="flex gap-2">
                <select 
                  v-model="historyFilterOption" 
                  class="border border-gray-300 rounded px-2 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部</option>
                  <option value="success">成功</option>
                  <option value="failed">失败</option>
                  <option value="processing">生成中</option>
                </select>
              </div>
            </div>

            <!-- 生成历史内容 -->
                          <div v-if="!generationHistory || generationHistory.length === 0" class="min-h-[300px] py-16 text-center text-gray-500">
                <div v-if="loadingGenerationHistory" class="flex flex-col items-center justify-center">
                  <div class="loader">
                    <svg class="circular" viewBox="25 25 50 50">
                      <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="4" stroke-miterlimit="10"/>
                    </svg>
                  </div>
                  <p class="text-sm mt-5 text-blue-600 font-medium">正在加载历史记录...</p>
                </div>
                <div v-else>
              <div class="material-icons text-4xl mb-2">history</div>
              <p>暂无生成历史记录</p>
                </div>
            </div>

            <div v-else-if="loadingGenerationHistory" class="min-h-[300px] py-24 flex flex-col items-center justify-center">
              <div class="loader">
                <svg class="circular" viewBox="25 25 50 50">
                  <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="4" stroke-miterlimit="10"/>
                </svg>
              </div>
              <p class="text-sm mt-5 text-blue-600 font-medium">正在加载历史记录...</p>
            </div>
            <div v-else>
              <div class="divide-y">
                <div 
                  v-for="item in generationHistory" 
                  :key="item.id" 
                  class="py-4 first:pt-0 last:pb-0"
                >
                  <div class="flex items-start justify-between">
                    <div class="flex-grow">
                      <div class="flex items-center gap-2 mb-1">
                        <h4 class="font-medium text-gray-800">{{ item.name }}</h4>
                        <span 
                          class="px-2 py-0.5 text-xs rounded-full" 
                          :class="{
                            'bg-green-100 text-green-800': item.status === 'success',
                            'bg-red-100 text-red-800': item.status === 'failed',
                            'bg-blue-100 text-blue-800': item.status === 'processing'
                          }"
                        >
                          {{ statusText(item.status) }}
                        </span>
                      </div>
                      
                      <div class="text-sm text-gray-600 line-clamp-2 mb-2 cursor-pointer hover:text-blue-600" @click="showTextDetail(item)">
                        {{ item.text }}
                      </div>
                      
                      <!-- Audio progress bar - only show when this audio is playing -->
                      <div v-if="currentlyPlayingAudioId === item.id && item.status === 'success'" class="mb-2">
                        <div class="flex items-center gap-2">
                          <span class="text-xs text-gray-500">{{ formatTime(currentAudioTime) }}</span>
                          <div 
                            class="relative flex-grow h-2 bg-gray-200 rounded-full cursor-pointer" 
                            @click="seekAudio($event)"
                            @mousedown="startDrag($event)"
                          >
                            <div 
                              class="absolute top-0 left-0 h-full bg-blue-500 rounded-full"
                              :style="{ width: `${audioProgress}%` }"
                            ></div>
                            <div 
                              class="absolute top-0 w-3 h-3 bg-white border border-blue-500 rounded-full cursor-grab transform -translate-y-1/4"
                              :style="{ left: `calc(${audioProgress}% - 6px)` }"
                              :class="{ 'cursor-grabbing': isDragging }"
                            ></div>
                          </div>
                          <span class="text-xs text-gray-500">{{ formatTime(currentAudioDuration) }}</span>
                        </div>
                      </div>
                      
                      <div class="flex items-center gap-4 text-xs text-gray-500">
                        <span class="flex items-center">
                          <i class="material-icons text-xs mr-1">schedule</i>
                          {{ item.createdAt }}
                        </span>
                        <span class="flex items-center">
                          <i class="material-icons text-xs mr-1">record_voice_over</i>
                          {{ item.model }}
                        </span>
                        <span class="flex items-center">
                          <i class="material-icons text-xs mr-1">timer</i>
                          {{ item.duration }}
                        </span>
                      </div>
                    </div>
                    
                    <div class="flex items-center gap-2">
                      <button 
                        v-if="item.status === 'success'"
                        class="p-1.5 text-blue-600 hover:bg-blue-50 rounded-full"
                        @click="playGeneratedAudio(item.id)"
                        title="播放"
                      >
                        <i class="material-icons">
                          {{ currentlyPlayingAudioId === item.id ? 'pause_circle' : 'play_circle' }}
                        </i>
                      </button>
                      
                      <button 
                        v-if="item.status === 'success'"
                        class="p-1.5 text-gray-600 hover:bg-gray-50 rounded-full"
                        @click="downloadGeneratedAudio(item.id)"
                        title="下载"
                      >
                        <i class="material-icons">download</i>
                      </button>
                      
                      <button 
                        class="p-1.5 text-red-500 hover:bg-red-50 rounded-full"
                        @click="confirmDeleteAudio(item)"
                        title="删除"
                      >
                        <i class="material-icons">delete</i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 生成历史分页 -->
              <div v-if="generationHistoryTotalPages > 1" class="flex justify-center items-center mt-4 space-x-2">
                <button
                  @click="handleGenerationHistoryPageChange(generationHistoryCurrentPage - 1)"
                  :disabled="generationHistoryCurrentPage === 1"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <span class="text-sm text-gray-700">第 {{ generationHistoryCurrentPage }} 页 / 共 {{ generationHistoryTotalPages }} 页</span>
                <button
                  @click="handleGenerationHistoryPageChange(generationHistoryCurrentPage + 1)"
                  :disabled="generationHistoryCurrentPage >= generationHistoryTotalPages"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        </div>
        

      </div>
    </div>

    <!-- 文本转语音弹窗 -->
    <el-dialog
      v-model="showTextToSpeech"
      title="文本转语音"
      width="80%"
      destroy-on-close
      :close-on-click-modal="false"
      class="text-to-speech-dialog"
    >
      <div class="flex gap-6">
        <!-- 左侧文本输入和设置 -->
        <div class="flex-grow">
          <el-form label-position="top">
            <el-form-item label="选择语音">
              <el-select 
                v-model="selectedVoiceId" 
                placeholder="请选择语音模型"
                class="w-full"
              >
                <el-option-group v-if="voiceModels.length > 0" label="我的声音">
                  <el-option 
                    v-for="model in voiceModels" 
                    :key="model.id" 
                    :label="model.name"
                    :value="model.id"
                  />
                </el-option-group>
                <el-option-group v-if="systemVoicePresets.length > 0" label="系统预设">
                  <el-option 
                    v-for="preset in systemVoicePresets" 
                    :key="preset.id" 
                    :label="`${preset.name} (${preset.category})`"
                    :value="preset.id"
                  />
                </el-option-group>
              </el-select>
            </el-form-item>
            
            <el-form-item label="您的文本">
              <el-input 
                v-model="textToConvert" 
                type="textarea"
                :rows="6"
                placeholder="输入要转换为语音的文本..."
                resize="none"
              />
            </el-form-item>
            
            <div class="bg-gray-50 p-4 rounded-lg mb-4">
              <h4 class="font-medium text-gray-700 mb-3">高级设置</h4>
              
              <el-form-item label="语速">
                <div class="flex items-center w-full">
                  <el-button 
                    type="primary" 
                    size="small" 
                    icon="el-icon-minus" 
                    circle 
                    @click="decreaseSpeed"
                    :disabled="voiceSettings.speed <= 0.5"
                    class="mr-3"
                  />
                  <el-slider
                    v-model="voiceSettings.speed"
                    :min="0.5"
                    :max="2"
                    :step="0.1"
                    :format-tooltip="val => `${val}x`"
                    class="flex-grow"
                  />
                  <el-button 
                    type="primary" 
                    size="small" 
                    icon="el-icon-plus" 
                    circle 
                    @click="increaseSpeed"
                    :disabled="voiceSettings.speed >= 2"
                    class="ml-3"
                  />
                </div>
                <div class="mt-1 flex justify-between text-xs text-gray-500">
                  <span>较慢 (0.5x)</span>
                  <span class="font-medium">{{ voiceSettings.speed }}x</span>
                  <span>较快 (2.0x)</span>
                </div>
              </el-form-item>
              
              <el-form-item label="语音模型">
                <el-select 
                  v-model="voiceSettings.model" 
                  class="w-full"
                  popper-class="voice-model-options"
                >
                  <el-option 
                    v-for="option in voiceModelOptions" 
                    :key="option.value" 
                    :label="option.label" 
                    :value="option.value"
                  >
                    <div class="flex items-center">
                      <div 
                        :class="[
                          'w-3 h-3 rounded-full mr-2', 
                          option.value === 'speech-1.5' ? 'bg-blue-500' : 
                          option.value === 'speech-1.6' ? 'bg-green-500' : 'bg-purple-500'
                        ]"
                      ></div>
                      <span>{{ option.label }}</span>
                      <span v-if="option.isRecommended" class="ml-2 text-xs text-green-600">(推荐)</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
        
        <!-- 右侧最近生成记录 -->
        <div class="w-80 bg-gray-50 rounded-lg p-4">
          <h3 class="font-medium text-gray-700 mb-3 flex items-center">
            <i class="el-icon-time mr-2"></i>最近生成
          </h3>
          <div v-if="generatedAudio.length > 0" class="recent-generated max-h-80 overflow-y-auto">
            <el-card 
              v-for="audio in generatedAudio" 
              :key="audio.id" 
              shadow="hover" 
              class="mb-2"
            >
              <div class="flex items-center mb-2">
                <el-button 
                  type="primary" 
                  circle 
                  size="small"
                  icon="el-icon-video-play"
                  class="mr-2"
                />
                <div class="text-sm flex-grow truncate">{{ audio.text }}</div>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>{{ audio.createdAt }}</span>
                <el-button 
                  type="text" 
                  icon="el-icon-download"
                  size="small"
                />
              </div>
            </el-card>
          </div>
          
          <el-empty
            v-else
            description="暂无生成记录"
          >
            <i class="el-icon-microphone text-3xl text-gray-400"></i>
          </el-empty>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTextToSpeech = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="generateSpeechAndClose" 
            :disabled="!selectedVoiceId || !textToConvert.trim()"
          >
            <i class="el-icon-microphone mr-1"></i> 生成语音
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 创建语音模型弹窗 -->
    <el-dialog
      v-model="showCreateVoiceModal"
      title="创建新语音模型"
      width="60%"
      destroy-on-close
      :close-on-click-modal="false"
      class="create-voice-dialog"
    >
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-2">
        <!-- 左侧：模型信息 -->
        <div class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">模型名称 <span class="text-red-500">*</span></label>
            <el-input 
              v-model="newVoiceModel.name" 
              placeholder="为您的语音模型命名，例如'教学语音'"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">模型描述</label>
            <el-input 
              v-model="newVoiceModel.description" 
              type="textarea"
              :rows="3"
              resize="none"
              placeholder="简单描述您的语音模型特点，例如'适合教学场景的温和语音'"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">模型头像</label>
            <el-upload
              class="avatar-uploader"
              action="#"
              :auto-upload="false"
              :on-change="handleAvatarUpload"
              :show-file-list="false"
            >
              <div class="relative w-32 h-32 border-2 border-dashed border-gray-300 rounded-xl flex items-center justify-center text-gray-400 hover:border-blue-500 hover:text-blue-500 transition-colors cursor-pointer">
                <img v-if="avatarPreview" :src="avatarPreview" class="w-full h-full object-cover rounded-xl" />
                <div v-else class="text-center">
                  <i class="material-icons text-4xl">add_a_photo</i>
                  <p class="text-xs mt-1">上传头像</p>
                </div>
                <div v-if="avatarPreview" class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 flex items-center justify-center rounded-xl transition-opacity">
                  <span class="text-white text-xs font-medium">更换头像</span>
                </div>
              </div>
            </el-upload>
          </div>
        </div>
        
        <!-- 右侧：语音样本 -->
        <div class="bg-gray-50 p-6 rounded-2xl">
          <label class="block text-sm font-medium text-gray-700 mb-1">录制语音样本 <span class="text-red-500">*</span></label>
          <p class="text-xs text-gray-500 mb-4">建议录制5-10分钟样本以获得最佳效果</p>
          
          <div class="h-64 border-2 border-dashed border-gray-300 rounded-xl flex flex-col items-center justify-center p-4 transition-colors"
               :class="{'border-blue-500': isRecording, 'border-green-500': recordedAudio}">
            
            <div v-if="!isRecording && !recordedAudio" class="text-center space-y-3">
              <i class="material-icons text-5xl text-gray-400">mic</i>
              <p class="text-sm text-gray-600">点击下方按钮开始录制</p>
              <div class="flex gap-4 justify-center">
                <el-button 
                  type="primary"
                  round
                  @click="startRecording"
                >
                  <i class="material-icons mr-2 text-sm">mic</i>
                  开始录制
                </el-button>
                <el-upload
                  action="#"
                  :auto-upload="false"
                  :on-change="handleAudioUpload"
                  :show-file-list="false"
                >
                  <el-button round><i class="material-icons mr-2 text-sm">upload</i>上传文件</el-button>
                </el-upload>
              </div>
            </div>
            
            <div v-else-if="isRecording" class="text-center space-y-4">
              <div class="recording-wave-2">
                <div v-for="n in 5" :key="n" class="wave-bar-2"></div>
              </div>
              <p class="text-lg font-mono text-red-500">{{ recordingTime }}</p>
              <el-button
                type="danger"
                circle
                size="large"
                @click="stopRecording"
              >
                <i class="material-icons">stop</i>
              </el-button>
            </div>
            
            <div v-else class="text-center space-y-4">
              <i class="material-icons text-5xl text-green-500">check_circle</i>
              <p class="font-medium text-gray-800">录制完成</p>
              <p class="text-sm text-gray-600">时长: {{ recordingTime }}</p>
              <div class="flex gap-4 justify-center mt-2">
                <el-button
                  round
                  @click="resetRecording"
                >
                  <i class="material-icons mr-2 text-sm">refresh</i>
                  重新录制
                </el-button>
                <el-button
                  type="primary"
                  plain
                  round
                  @click="playRecording"
                >
                  <i class="material-icons mr-2 text-sm">
                    {{ isPlayingRecording ? 'pause' : 'play_arrow' }}
                  </i>
                  {{ isPlayingRecording ? '暂停' : '试听' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="showCreateVoiceModal = false" round>取消</el-button>
          <el-button 
            type="primary" 
            @click="createVoiceModel" 
            :disabled="!uploadedAudioFile || !newVoiceModel.name"
            round
          >
            <i class="material-icons mr-2 text-sm">add_circle</i>
            确认创建
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="showDeleteConfirm"
      title="确认删除"
      width="30%"
      destroy-on-close
    >
      <div class="text-gray-700 mb-4">
        您确定要删除{{ voiceToDelete ? '语音模型' : '生成历史' }} 
        <span class="font-medium">{{ voiceToDelete ? voiceToDelete.name : (audioToDelete ? audioToDelete.name : '') }}</span> 吗？此操作不可恢复。
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDeleteConfirm = false">取消</el-button>
          <el-button 
            type="danger" 
            @click="voiceToDelete ? deleteVoiceModel() : deleteAudio()"
          >
            确认删除
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 文本详情弹窗 -->
    <el-dialog
      v-model="showTextDetailModal"
      title="文本详情"
      width="60%"
      destroy-on-close
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="生成时间">{{ selectedTextDetail.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="语音模型">{{ selectedTextDetail.model }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag 
            :type="selectedTextDetail.status === 'success' ? 'success' : 
                   selectedTextDetail.status === 'failed' ? 'danger' : 'info'"
            size="small"
          >
            {{ statusText(selectedTextDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="完整文本内容">
          <div class="bg-gray-50 border border-gray-200 rounded-md p-4 max-h-96 overflow-y-auto">
            <p class="text-sm text-gray-800 whitespace-pre-wrap">{{ selectedTextDetail.text }}</p>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTextDetailModal = false">关闭</el-button>
          <el-button 
            v-if="selectedTextDetail.status === 'success'"
            type="primary"
            @click="playTextDetailAudio"
            icon="el-icon-video-play"
          >
            播放音频
          </el-button>
        </span>
      </template>
    </el-dialog>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { voiceApi } from '@/api/voice'

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 搜索
const searchQuery = ref('')
const sortOption = ref('newest')

// 我的声音分页
const myVoicesCurrentPage = ref(1)
const myVoicesPageSize = ref(10)
const myVoicesTotalCount = ref(0)
const myVoicesTotalPages = computed(() => Math.ceil(myVoicesTotalCount.value / myVoicesPageSize.value))
const loadingVoiceModels = ref(false)

// 生成历史分页
const generationHistoryCurrentPage = ref(1)
const generationHistoryPageSize = ref(10)
const generationHistoryTotalCount = ref(0)
const generationHistoryTotalPages = computed(() => Math.ceil(generationHistoryTotalCount.value / generationHistoryPageSize.value))
const loadingGenerationHistory = ref(false)

// 标签页状态
const activeTab = ref('voice-models')

// 语音模型列表 (自定义 & 系统)
const voiceModels = ref([])
const systemVoicePresets = ref([])

const loadMyVoiceModels = async () => {
  loadingVoiceModels.value = true
  try {
    // 加载用户自定义语音模型
    const params = {
      page: myVoicesCurrentPage.value,
      page_size: myVoicesPageSize.value
    }
    const userRes = await voiceApi.getVoiceModels(params)
    voiceModels.value = userRes.data?.results || []
    myVoicesTotalCount.value = userRes.data?.count || 0
  } catch (e) {
    console.error('加载我的语音模型失败', e)
  } finally {
    loadingVoiceModels.value = false
  }
}

const loadSystemVoicePresets = async () => {
  try {
    const systemRes = await voiceApi.getSystemVoicePresets()
    systemVoicePresets.value = systemRes.data || []
  } catch (e) {
    console.error('加载系统语音模型失败', e)
  }
}

const loadData = async () => {
  await loadMyVoiceModels()
  await loadSystemVoicePresets()

  // 默认选第一个模型
  if (!selectedVoiceId.value) {
    if (voiceModels.value.length) {
      selectedVoiceId.value = voiceModels.value[0].id
    } else if (systemVoicePresets.value.length) {
      selectedVoiceId.value = systemVoicePresets.value[0].id
    }
  }
}


// 最近生成的语音
const generatedAudio = ref([])

const loadGeneratedAudio = async () => {
  loadingGenerationHistory.value = true
  try {
    const params = {
      page: generationHistoryCurrentPage.value,
      page_size: generationHistoryPageSize.value
    }
    const res = await voiceApi.getGeneratedAudios(params)
    generatedAudio.value = res.data?.results || res.data?.audios || []
    generationHistoryTotalCount.value = res.data?.count || res.data?.total || 0
  } catch (e) {
    console.error('加载生成历史失败', e)
  } finally {
    loadingGenerationHistory.value = false
  }
}

const generationHistory = computed(() => generatedAudio.value) // 直接用同一数据

onMounted(() => {
  loadData()
  loadGeneratedAudio()
})

const handleMyVoicesPageChange = async (newPage) => {
  if (newPage > 0 && newPage <= myVoicesTotalPages.value) {
    // 直接设置加载状态，确保UI立即更新
    loadingVoiceModels.value = true
    myVoicesCurrentPage.value = newPage
    
    // 添加微小延迟，确保动画显示
    setTimeout(() => {
    loadMyVoiceModels()
    }, 100)
  }
}

const handleGenerationHistoryPageChange = async (newPage) => {
  if (newPage > 0 && newPage <= generationHistoryTotalPages.value) {
    // 直接设置加载状态，确保UI立即更新
    loadingGenerationHistory.value = true
    generationHistoryCurrentPage.value = newPage
    
    // 添加微小延迟，确保动画显示
    setTimeout(() => {
    loadGeneratedAudio()
    }, 100)
  }
}

// 文本转语音界面状态
const showTextToSpeech = ref(false)
const selectedVoiceId = ref(null)
const textToConvert = ref('欢迎使用AI语音克隆系统，这是一个样本文本。您可以输入任何文字，系统将使用您的声音进行朗读。')
const voiceSettings = ref({
  speed: 1.0,
  model: 'speech-1.6'  // 默认使用 speech-1.6
})

// 生成历史
const historyFilterOption = ref('all')
const currentlyPlayingAudioId = ref(null)

// 删除确认
const showDeleteConfirm = ref(false)
const voiceToDelete = ref(null)
const audioToDelete = ref(null)

// 文本详情弹窗
const showTextDetailModal = ref(false)
const selectedTextDetail = ref({})

// 创建语音模型
const showCreateVoiceModal = ref(false)
const isRecording = ref(false)
const recordedAudio = ref(null)
const recordingTime = ref('00:00')
const newVoiceModel = ref({
  name: ''
})

// 录制和文件
const mediaRecorder = ref(null)
const recordingTimer = ref(null)
const uploadedAudioFile = ref(null)
const avatarPreview = ref(null)
const avatarFile = ref(null)
const isPlayingRecording = ref(false)
const recordingAudioPlayer = ref(null)


// 全局音频播放器，避免多路同时播放
const audioPlayer = ref(null)

// 当前正在播放的模型 ID（语音克隆或系统预设）
const currentlyPlayingId = ref(null)
const currentAudioTime = ref(0)
const currentAudioDuration = ref(0)
const audioProgress = ref(0)
const progressBarRef = ref(null)
const isDragging = ref(false)

// 辅助函数
const truncateText = (text, maxLength) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const removeAvatar = () => {
  avatarPreview.value = null
  avatarFile.value = null
}

const openCreateVoiceModal = () => {
  showCreateVoiceModal.value = true
  resetRecording()
  
  // 重置头像
  removeAvatar()
  
  // 重置表单
  newVoiceModel.value = {
    name: '',
    description: ''
  }
}

const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    mediaRecorder.value = new MediaRecorder(stream)
    
    const audioChunks = []
    mediaRecorder.value.ondataavailable = (event) => {
      audioChunks.push(event.data)
    }
    
    mediaRecorder.value.onstop = () => {
      const audioBlob = new Blob(audioChunks, { type: 'audio/webm' })
      recordedAudio.value = audioBlob
      uploadedAudioFile.value = audioBlob
      
      // 停止所有音轨
      stream.getTracks().forEach(track => track.stop())
    }
    
    mediaRecorder.value.start()
    isRecording.value = true
    
    // 简化计时
    let seconds = 0
    recordingTimer.value = setInterval(() => {
      seconds++
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      recordingTime.value = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }, 1000)
    
  } catch (error) {
    console.error('无法访问麦克风:', error)
    ElMessage.error('无法访问麦克风，请检查浏览器权限设置')
  }
}

const stopRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop()
  }
  
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }
  
  isRecording.value = false
}

const resetRecording = () => {
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }
  
  if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop()
  }
  
  // 停止播放
  if (recordingAudioPlayer.value) {
    recordingAudioPlayer.value.pause()
  }
  
  isRecording.value = false
  recordedAudio.value = null
  uploadedAudioFile.value = null
  recordingTime.value = '00:00'
  isPlayingRecording.value = false
}

// 处理头像上传 - 修改为适应ElementUI上传组件
const handleAvatarUpload = (file) => {
  if (file) {
    avatarFile.value = file.raw
    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      avatarPreview.value = e.target.result
    }
    reader.readAsDataURL(file.raw)
  }
}

// 处理音频文件上传 - 修改为适应ElementUI上传组件
const handleAudioUpload = (file) => {
  if (file) {
    uploadedAudioFile.value = file.raw
    recordedAudio.value = file.raw
    // 重置录制状态
    isRecording.value = false
    recordingTime.value = '文件已上传'
  }
}

// 播放录制的音频
const playRecording = () => {
  if (!recordedAudio.value) return
  
  if (isPlayingRecording.value) {
    // 暂停播放
    if (recordingAudioPlayer.value) {
      recordingAudioPlayer.value.pause()
    }
    isPlayingRecording.value = false
    return
  }
  
  // 开始播放
  if (recordingAudioPlayer.value) {
    recordingAudioPlayer.value.pause()
  }
  
  const audioUrl = URL.createObjectURL(recordedAudio.value)
  recordingAudioPlayer.value = new Audio(audioUrl)
  
  recordingAudioPlayer.value.onended = () => {
    isPlayingRecording.value = false
    URL.revokeObjectURL(audioUrl)
  }
  
  recordingAudioPlayer.value.onerror = () => {
    isPlayingRecording.value = false
    ElMessage.error('音频播放失败')
    URL.revokeObjectURL(audioUrl)
  }
  
  recordingAudioPlayer.value.play().then(() => {
    isPlayingRecording.value = true
  }).catch((error) => {
    console.error('播放失败:', error)
    ElMessage.error('音频播放失败')
    URL.revokeObjectURL(audioUrl)
  })
}

const createVoiceModel = async () => {
  if (!uploadedAudioFile.value || !newVoiceModel.value.name) {
    ElMessage.warning('请提供语音模型名称和音频文件')
    return
  }
  
  // 显示加载状态
  const loading = ElLoading.service({
    lock: true,
    text: '正在创建语音模型...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    // 构造payload对象，让API函数自己处理FormData
    const payload = {
      name: newVoiceModel.value.name,
      description: newVoiceModel.value.description || ''
    }
    
    // 添加音频文件
    if (uploadedAudioFile.value instanceof File) {
      payload.audio_file = uploadedAudioFile.value
    } else {
      // 录制的音频是 Blob，需要转换为 File
      const audioFile = new File([uploadedAudioFile.value], 'recording.webm', { 
        type: 'audio/webm' 
      })
      payload.audio_file = audioFile
    }
    
    // 添加头像文件（如果有）
    if (avatarFile.value) {
      payload.avatar = avatarFile.value
    }
    
    await voiceApi.createVoiceModel(payload)
    showCreateVoiceModal.value = false
    myVoicesCurrentPage.value = 1 // 创建后回到第一页
    await loadMyVoiceModels()
    ElMessage.success('语音模型创建成功！')
  } catch (e) {
    console.error('创建语音模型失败', e)
    ElMessage.error(e.response?.data?.message || '创建语音模型失败，请重试')
  } finally {
    loading.close()
  }
}

const useVoiceModel = (id) => {
  selectedVoiceId.value = id
  showTextToSpeech.value = true
}

const editVoiceModel = (model) => {
  // 编辑模型功能（可以实现为打开创建弹窗但预填数据）
  newVoiceModel.value = { ...model }
  showCreateVoiceModal.value = true
}

const confirmDeleteVoice = (model) => {
  voiceToDelete.value = model
  showDeleteConfirm.value = true
}

const deleteVoiceModel = async () => {
  if (!voiceToDelete.value) return
  try {
    await voiceApi.deleteVoiceModel(voiceToDelete.value.id)
    // 如果删除的是当前页的最后一个，并且不是第一页，则返回上一页
    if (voiceModels.value.length === 1 && myVoicesCurrentPage.value > 1) {
      myVoicesCurrentPage.value--
    }
    await loadMyVoiceModels()
  } catch (e) { console.error(e) }
  showDeleteConfirm.value = false
  voiceToDelete.value = null
}

const generateSpeechAndClose = async () => {
  if (!selectedVoiceId.value || !textToConvert.value.trim()) {
    ElMessage.warning('请选择语音模型并输入文本')
    return
  }
  
  const loading = ElLoading.service({ text: '正在生成语音...' })
  
  try {
    await voiceApi.generateAudio({
      title: '文本转语音',
      text_content: textToConvert.value,
      audio_clone_id: selectedVoiceId.value,
      speed: voiceSettings.value.speed,
      model: voiceSettings.value.model
    })
    await loadGeneratedAudio()
    ElMessage.success('语音生成成功！')
    showTextToSpeech.value = false
  } catch (e) {
    ElMessage.error('语音生成失败')
    console.error(e)
  } finally {
    loading.close()
  }
}

const useSystemVoice = (id) => {
  const preset = systemVoicePresets.value.find(p => p.id === id)
  if (!preset) return
  
  // Select this voice and open the text-to-speech dialog
  selectedVoiceId.value = id
  showTextToSpeech.value = true
  
  console.log('使用系统预设声音:', preset.name)
}

const playVoiceSample = (id) => {
  // 若再次点击同一条 -> 暂停
  if (currentlyPlayingId.value === id) {
    if (audioPlayer.value) audioPlayer.value.pause()
    currentlyPlayingId.value = null
    return
  }

  // 找到模型（自定义或系统）
  const model = [...voiceModels.value, ...systemVoicePresets.value].find(m => m.id === id)
  if (!model || (!model.clone_audio_url && !model.sample_audio_url)) return

  // 停止之前播放
  if (audioPlayer.value) audioPlayer.value.pause()

  // 优先使用 clone_audio_url，回退到 sample_audio_url（向前兼容）
  const audioUrl = model.clone_audio_url || model.sample_audio_url
  audioPlayer.value = new Audio(audioUrl)
  audioPlayer.value.play().catch(err => console.error('播放失败', err))

  currentlyPlayingId.value = id
  audioPlayer.value.onended = () => {
    if (currentlyPlayingId.value === id) currentlyPlayingId.value = null
  }
}

const playGeneratedAudio = (id) => {
  if (currentlyPlayingAudioId.value === id) {
    if (audioPlayer.value) audioPlayer.value.pause()
    currentlyPlayingAudioId.value = null
    return
  }

  const audioItem = generationHistory.value.find(a => a.id === id)
  if (!audioItem || !audioItem.audio_url) return

  if (audioPlayer.value) audioPlayer.value.pause()

  audioPlayer.value = new Audio(audioItem.audio_url)
  
  // Add time update event listener
  audioPlayer.value.addEventListener('timeupdate', updateAudioProgress)
  
  // Set duration once metadata is loaded
  audioPlayer.value.addEventListener('loadedmetadata', () => {
    currentAudioDuration.value = audioPlayer.value.duration
  })
  
  // Reset time and progress
  currentAudioTime.value = 0
  audioProgress.value = 0
  
  audioPlayer.value.play().catch(err => console.error('播放失败', err))

  currentlyPlayingAudioId.value = id
  audioPlayer.value.onended = () => {
    if (currentlyPlayingAudioId.value === id) {
      currentlyPlayingAudioId.value = null
      currentAudioTime.value = 0
      audioProgress.value = 0
    }
  }
}

const downloadGeneratedAudio = (id) => {
  const audio = generationHistory.value.find(a => a.id === id)
  if (!audio) {
    ElMessage.error('音频不存在')
    return
  }
  
  // 检查音频状态
  if (audio.status !== 'success') {
    ElMessage.error('音频未成功生成，无法下载')
    return
  }
  
  // 使用后端API下载，不传递audioUrl参数，强制使用流式下载
  voiceApi.downloadAudio(id, audio.name)
    .catch(error => {
      console.error('下载失败:', error)
      ElMessage.error('下载失败，请重试')
    })
}

const confirmDeleteAudio = (audio) => {
  audioToDelete.value = audio
  showDeleteConfirm.value = true
}

const deleteAudio = async () => {
  if (!audioToDelete.value) return
  try {
    await voiceApi.deleteGeneratedAudio(audioToDelete.value.id)
    await loadGeneratedAudio()
  } catch (e) { console.error(e) }
  showDeleteConfirm.value = false
  audioToDelete.value = null
}

const showTextDetail = (item) => {
  selectedTextDetail.value = { ...item }
  showTextDetailModal.value = true
}

const playTextDetailAudio = () => {
  if (!selectedTextDetail.value.audio_url) {
    ElMessage.error('音频文件不存在')
    return
  }
  
  if (audioPlayer.value) audioPlayer.value.pause()
  
  audioPlayer.value = new Audio(selectedTextDetail.value.audio_url)
  audioPlayer.value.play().catch(err => {
    console.error('播放失败', err)
    ElMessage.error('音频播放失败')
  })
}

const statusText = (status) => {
  switch (status) {
    case 'success': return '成功'
    case 'failed': return '失败'
    case 'processing': return '生成中'
    default: return '未知'
  }
}

// 语音模型选项
const voiceModelOptions = computed(() => {
  const options = [
    { label: 'Speech 1.5', value: 'speech-1.5', isRecommended: false },
    { label: 'Speech 1.6 (推荐)', value: 'speech-1.6', isRecommended: true },
    { label: 'S1', value: 's1', isRecommended: false }
  ]
  return options
})

const decreaseSpeed = () => {
  voiceSettings.value.speed = Math.max(0.5, voiceSettings.value.speed - 0.1)
}

const increaseSpeed = () => {
  voiceSettings.value.speed = Math.min(2.0, voiceSettings.value.speed + 0.1)
}

const formatTime = (seconds) => {
  if (!seconds) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const updateAudioProgress = () => {
  if (audioPlayer.value) {
    currentAudioTime.value = audioPlayer.value.currentTime
    currentAudioDuration.value = audioPlayer.value.duration || 0
    audioProgress.value = (currentAudioTime.value / currentAudioDuration.value) * 100 || 0
  }
}

const seekAudio = (event) => {
  if (!audioPlayer.value) return
  
  const progressBar = event.currentTarget
  const rect = progressBar.getBoundingClientRect()
  const offsetX = event.clientX - rect.left
  const progressBarWidth = rect.width
  
  // Calculate seek position
  const seekPercentage = offsetX / progressBarWidth
  const seekTime = seekPercentage * currentAudioDuration.value
  
  // Set the current time of the audio
  audioPlayer.value.currentTime = seekTime
  currentAudioTime.value = seekTime
  audioProgress.value = seekPercentage * 100
}

const startDrag = (event) => {
  if (!audioPlayer.value) return
  
  // 获取当前进度条元素
  const progressBar = event.currentTarget
  
  // 暂停音频 - 在拖动时暂停播放
  audioPlayer.value.pause()
  
  // 记录开始拖动
  isDragging.value = true
  
  // 设置初始位置
  updateDragPosition(event, progressBar)
  
  // 保存当前进度条元素的引用，供移动事件使用
  currentProgressBar.value = progressBar
  
  // 添加事件监听
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

// 保存当前正在拖动的进度条元素
const currentProgressBar = ref(null)

const onDrag = (event) => {
  if (isDragging.value && currentProgressBar.value) {
    updateDragPosition(event, currentProgressBar.value)
    event.preventDefault() // 防止选中文本
  }
}

const updateDragPosition = (event, progressBar) => {
  const rect = progressBar.getBoundingClientRect()
  const offsetX = Math.max(0, Math.min(event.clientX - rect.left, rect.width))
  const progressBarWidth = rect.width
  
  const seekPercentage = offsetX / progressBarWidth
  const seekTime = seekPercentage * currentAudioDuration.value
  
  // 只更新UI，不实际改变音频位置
  currentAudioTime.value = seekTime
  audioProgress.value = seekPercentage * 100
}

const stopDrag = () => {
  if (isDragging.value) {
    isDragging.value = false
    
    // 拖动结束后，设置音频位置并恢复播放
    if (audioPlayer.value) {
      audioPlayer.value.currentTime = currentAudioTime.value
      audioPlayer.value.play().catch(err => console.error('播放失败', err))
    }
    
    // 清除引用
    currentProgressBar.value = null
    
    // 移除事件监听
    document.removeEventListener('mousemove', onDrag)
    document.removeEventListener('mouseup', stopDrag)
  }
}
</script>

<style scoped>
/* 移除旧的响应式布局样式，使用 grid-cols-5 替代 */

.recording-wave {
  display: flex;
  align-items: flex-end;
  height: 60px;
  gap: 4px;
}

.wave-bar {
  width: 4px;
  height: 20px;
  border-radius: 2px;
  animation: wave 0.5s infinite alternate;
}

@keyframes wave {
  0% {
    height: 10px;
  }
  100% {
    height: 50px;
  }
}

.wave-bar:nth-child(1) { animation-delay: -0.45s; }
.wave-bar:nth-child(2) { animation-delay: -0.4s; }
.wave-bar:nth-child(3) { animation-delay: -0.35s; }
.wave-bar:nth-child(4) { animation-delay: -0.3s; }
.wave-bar:nth-child(5) { animation-delay: -0.25s; }
.wave-bar:nth-child(6) { animation-delay: -0.2s; }
.wave-bar:nth-child(7) { animation-delay: -0.15s; }
.wave-bar:nth-child(8) { animation-delay: -0.1s; }
.wave-bar:nth-child(9) { animation-delay: -0.05s; }
.wave-bar:nth-child(10) { animation-delay: 0s; }

.recording-wave-2 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  gap: 8px;
}

.wave-bar-2 {
  width: 6px;
  height: 10px;
  border-radius: 3px;
  background-color: #ef4444;
  animation: wave-2 1.2s infinite ease-in-out alternate;
}

@keyframes wave-2 {
  0% { height: 10px; }
  50% { height: 60px; }
  100% { height: 10px; }
}

.wave-bar-2:nth-child(1) { animation-delay: -0.4s; }
.wave-bar-2:nth-child(2) { animation-delay: -0.2s; }
.wave-bar-2:nth-child(3) { animation-delay: 0s; }
.wave-bar-2:nth-child(4) { animation-delay: 0.2s; }
.wave-bar-2:nth-child(5) { animation-delay: 0.4s; }

/* Dropdown menu */
.dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-menu {
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Voice wave animation */
.playing-voice {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.voice-wave-icon {
  position: absolute;
  display: flex;
  align-items: flex-end;
  height: 20px;
  gap: 2px;
  z-index: 10;
}

.wave-line {
  width: 2px;
  height: 10px;
  background-color: #3b82f6;
  border-radius: 1px;
  animation: voice-wave 0.5s infinite alternate;
}

.wave-line:nth-child(1) { animation-delay: -0.3s; }
.wave-line:nth-child(2) { animation-delay: -0.15s; }
.wave-line:nth-child(3) { animation-delay: 0s; }

@keyframes voice-wave {
  0% {
    height: 5px;
  }
  100% {
    height: 15px;
  }
}

/* 加载动画样式 */
.loader {
  position: relative;
  margin: 0 auto;
  width: 60px;
  height: 60px;
}

.circular {
  animation: rotate 2s linear infinite;
  height: 100%;
  transform-origin: center center;
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

.path {
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
  stroke-linecap: round;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}

@keyframes color {
  0%, 100% {
    stroke: #3b82f6;
  }
  40% {
    stroke: #60a5fa;
  }
  66% {
    stroke: #2563eb;
  }
  80%, 90% {
    stroke: #1d4ed8;
  }
}

/* ElementUI自定义样式 */
:deep(.el-dialog__body) {
  padding-top: 10px;
}

:deep(.el-card__body) {
  padding: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.text-to-speech-dialog :deep(.el-dialog__body) {
  display: flex;
  flex-direction: column;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-upload) {
  width: auto;
}

.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

:deep(.el-button [class*="el-icon-"]) {
  margin-right: 4px;
}

/* 音频进度条 */
.relative {
  position: relative;
}

.flex-grow {
  flex-grow: 1;
}

.cursor-pointer {
  cursor: pointer;
}

.h-2 {
  height: 0.5rem;
}

.bg-gray-200 {
  background-color: #e5e7eb;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.rounded-full {
  border-radius: 9999px;
}

/* 增强波形动画 */
.wave-bar {
  width: 4px;
  height: 20px;
  border-radius: 2px;
  animation: wave 0.5s infinite alternate;
  background: linear-gradient(to bottom, #f56c6c, #e6a23c);
}

@keyframes wave {
  0% {
    height: 10px;
  }
  100% {
    height: 50px;
  }
}

.wave-line {
  width: 2px;
  height: 10px;
  background: linear-gradient(to bottom, #409eff, #67c23a);
  border-radius: 1px;
  animation: voice-wave 0.5s infinite alternate;
}

@keyframes voice-wave {
  0% {
    height: 5px;
  }
  100% {
    height: 15px;
  }
}
</style> 