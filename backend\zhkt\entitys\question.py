from django.db import models
from .course import Course

class Question(models.Model):
    """题目模型"""
    QUESTION_TYPES = (
        ('single_choice', '单选题'),
        ('multiple_choice', '多选题'),
        ('true_false', '判断题'),
        ('fill_blank', '填空题'),
        ('short_answer', '简答题'),
    )
    
    DIFFICULTY_LEVELS = (
        ('easy', '简单'),
        ('medium', '中等'),
        ('hard', '困难'),
    )
    
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='questions', verbose_name='所属课程')
    title = models.CharField(max_length=500, verbose_name='题目标题')
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES, verbose_name='题目类型')
    difficulty = models.CharField(max_length=10, choices=DIFFICULTY_LEVELS, verbose_name='难度等级')
    content = models.TextField(verbose_name='题目内容')
    answer = models.TextField(verbose_name='参考答案', null=True, blank=True)
    analysis = models.TextField(blank=True, null=True, verbose_name='题目解析')
    score = models.DecimalField(max_digits=5, decimal_places=2, default=1.0, verbose_name='分值')
    tags = models.CharField(max_length=200, blank=True, null=True, verbose_name='标签')
    order = models.IntegerField(default=0, verbose_name='排序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    
    class Meta:
        db_table = 'zhkt_question'
        ordering = ['order', 'created_at']
        verbose_name = '题目'
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return self.title

class Option(models.Model):
    """选项模型"""
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='options', verbose_name='所属题目')
    content = models.CharField(max_length=500, verbose_name='选项内容')
    is_correct = models.BooleanField(default=False, verbose_name='是否正确答案')
    order = models.IntegerField(default=0, verbose_name='排序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    
    class Meta:
        db_table = 'zhkt_question_option'
        ordering = ['order']
        verbose_name = '题目选项'
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return f"{self.question.title} - 选项{self.order}"