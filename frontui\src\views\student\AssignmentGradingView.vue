<template>
  <StudentLayout 
    pageTitle="作业批改详情" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">

    <!-- 作业信息卡片 -->
    <div v-loading="isLoading" 
         element-loading-text="加载中..." 
         element-loading-background="rgba(255, 255, 255, 0.9)"
         class="bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="flex-col-md-row">
        <div>
          <h1 class="text-xl font-bold text-gray-900 mb-2">{{ assignment.title }}</h1>
          <!--<p class="text-sm text-gray-600">作业总分: {{ assignment.total_score }}</p>-->
          <div class="text-sm text-blue-600">作业总分: {{ assignment.total_score }}</div>
        </div>
        <div class="mt-4 md:mt-0">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            已批改
          </span>
        </div>
      </div>
      
      <div class="flex-col-md-row" style="width: 75%; margin: 15px 0;">
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">school</span>
          <div>
            <p class="text-xs text-gray-500">所属课程</p>
            <p class="text-sm font-medium">{{ assignment.courseName }}</p>
          </div>
        </div>
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">person</span>
          <div>
            <p class="text-xs text-gray-500">授课教师</p>
            <p class="text-sm font-medium">{{ assignment.teacherName }}</p>
          </div>
        </div>
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">event</span>
          <div>
            <p class="text-xs text-gray-500">截止时间</p>
            <p class="text-sm font-medium">{{ formatDate(assignment.end_time) }}</p>
          </div>
        </div>
      </div>
      
      <div class="border-t border-gray-200 pt-4">
        <h2 class="text-lg font-medium text-gray-900 mb-3">作业说明</h2>
        <div class="prose max-w-none text-gray-700 mb-4">
          <p>{{ assignment.description }}</p>
        </div>
      </div>
    </div>

    <!-- 批改详情 -->
    <div v-loading="isLoading" 
         element-loading-text="加载中..." 
         element-loading-background="rgba(255, 255, 255, 0.9)"
         class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">批改详情</h2>
      
      <!-- 题目列表 -->
      <div v-for="question in assignment.questions" :key="question.id" class="border rounded-lg p-4 mb-4">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h4 class="text-base font-medium text-gray-900">{{ question.question_detail.title }}</h4>
            <div class="flex gap-2 mt-1">
              <span class="px-2 py-0.5 rounded-full text-xs"
                :class="getQuestionTypeClass(question.question_detail.question_type)">
                {{ getQuestionTypeText(question.question_detail.question_type) }}
              </span>
              <span class="text-sm text-gray-500">{{ question.score }}分</span>
            </div>
          </div>
        </div>

        <!-- 题目选项（仅选择题显示） -->
        <div v-if="['single_choice', 'multiple_choice'].includes(question.question_detail.question_type)" class="mb-4">
          <div class="text-sm font-medium text-gray-700 mb-2">选项：</div>
          <div class="space-y-2">
            <div v-for="option in question.question_detail.options" :key="option.id" 
              class="flex items-center p-2 rounded"
              :class="{
                'bg-green-50': option.is_correct,
                'bg-gray-50': !option.is_correct
              }"
            >
              <span class="text-sm text-gray-600">{{ option.content }}</span>
              <span v-if="option.is_correct" class="ml-2 text-xs text-green-600">(正确答案)</span>
            </div>
          </div>
        </div>

        <!-- 参考答案（非选择题） -->
        <div v-if="!['single_choice', 'multiple_choice'].includes(question.question_detail.question_type) && question.question_detail.answer" class="mb-4">
          <div class="text-sm font-medium text-gray-700 mb-2">参考答案：</div>
          <div class="bg-green-50 p-3 rounded">
            <div class="text-sm text-gray-700">{{ question.question_detail.answer }}</div>
          </div>
        </div>

        <!-- 学生答案 -->
        <div class="bg-gray-50 rounded p-4 mb-4">
          <div class="text-sm text-gray-700">你的答案：</div>
          <div class="mt-2" v-html="formatAnswer(question.id)"></div>
        </div>

        <!-- 得分 -->
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <label class="text-sm font-medium text-gray-700">得分：</label>
            <div class="text-xl font-bold" :class="getScoreClass(getQuestionScore(question.id))">
              {{ getQuestionScore(question.id) || 0 }}
            </div>
            <span class="text-sm text-gray-500">/ {{ question.score }}分</span>
          </div>
        </div>
      </div>

      <!-- 总评 -->
      <div class="border rounded-lg p-4">
        <h4 class="text-base font-medium text-gray-900 mb-4">总评</h4>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">你的总分</label>
            <div class="text-2xl font-bold text-blue-600">{{ totalScore }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">教师总评语</label>
            <div class="bg-gray-50 p-4 rounded">
              <p class="text-gray-700">{{ overallComment || '暂无评语' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div class="flex justify-between mb-6 mt-8">
      <router-link 
        to="/student/assignments" 
        class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none flex items-center"
      >
        <span class="material-icons mr-2">arrow_back</span>
        返回我的作业
      </router-link>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { formatDate } from '@/utils/date.js'
import { useRoute } from 'vue-router'
import { getHomeworkDetail, getStudentStatus, getStudentFeedback } from '@/api/homework'
import { courseApi } from '@/api/course'
import { ElMessage } from 'element-plus'

const studentStore = useStudentStore()
const route = useRoute()

// 作业数据
const assignment = ref({
  id: '',
  title: '',
  description: '',
  courseName: '',
  teacherName: '',
  end_time: '',
  questions: [],
  answers: null,
  feedback: null
})

// 批改数据
const questionScores = ref({})
const overallComment = ref('')
const isLoading = ref(true)

// 计算属性
const totalScore = computed(() => {
  return Object.values(questionScores.value).reduce((sum, score) => sum + (Number(score) || 0), 0)
})

// 工具方法
const getQuestionTypeClass = (type) => {
  switch (type) {
    case 'single_choice':
      return 'bg-blue-100 text-blue-800'
    case 'multiple_choice':
      return 'bg-indigo-100 text-indigo-800'
    case 'true_false':
      return 'bg-green-100 text-green-800'
    case 'fill_blank':
      return 'bg-yellow-100 text-yellow-800'
    case 'short_answer':
      return 'bg-orange-100 text-orange-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getQuestionTypeText = (type) => {
  switch (type) {
    case 'single_choice':
      return '单选题'
    case 'multiple_choice':
      return '多选题'
    case 'true_false':
      return '判断题'
    case 'fill_blank':
      return '填空题'
    case 'short_answer':
      return '简答题'
    default:
      return type
  }
}

const getScoreClass = (score) => {
  if (score === null || score === undefined) return 'text-gray-500'
  if (score >= 90) return 'text-green-600'
  if (score >= 60) return 'text-blue-600'
  return 'text-red-600'
}

const getQuestionScore = (questionId) => {
  return questionScores.value[questionId]
}

const formatAnswer = (questionId) => {
  const questions = assignment.value.questions
  const studentAnswerObj = assignment.value.answers
  const studentAnswer = studentAnswerObj[questionId]
  const question = questions.find(q => q.id === questionId)
  const questionType = question.question_detail.question_type
  
  // 如果是选择题，需要转换选项ID为选项内容
  if (['single_choice', 'multiple_choice'].includes(questionType)) {
    const options = question.question_detail.options
    
    // 如果是多选题，答案可能是数组
    if (Array.isArray(studentAnswer)) {
      return studentAnswer.map(answerId => {
        const option = options.find(opt => opt.id === answerId)
        return option ? option.content : answerId
      }).join('、')
    } else {
      // 单选题
      const option = options.find(opt => opt.id === studentAnswer)
      return option ? option.content : studentAnswer
    }
  } else {
    return studentAnswer
  }
}

// 页面加载时获取作业数据
onMounted(async () => {
  const assignmentId = route.params.id
  try {
    isLoading.value = true
    
    // 获取作业详情
    const response = await getHomeworkDetail(assignmentId)
    
    // 获取课程信息
    const course = await courseApi.getCourseById(response.course)
    response.courseName = course.name
    response.teacherName = course.teacher.user.alias
    
    // 获取学生提交状态和答案
    const statusResponse = await getStudentStatus(assignmentId)
    
    // 获取作业反馈
    const feedbackResponse = await getStudentFeedback(assignmentId)
    
    // 合并数据
    assignment.value = {
      ...response,
      answers: statusResponse.answers
    }

    // 解析批改信息
    if (feedbackResponse.feedback) {
      const feedback = JSON.parse(feedbackResponse.feedback)
      questionScores.value = feedback.scores || {}
      overallComment.value = feedback.overall || ''
    }
    
  } catch (error) {
    console.error('获取作业批改详情失败:', error)
    ElMessage.error('获取作业批改详情失败')
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
.score-bar {
  height: 8px;
  border-radius: 4px;
  background-color: #e5e7eb;
}

.score-fill {
  height: 100%;
  border-radius: 4px;
  background-color: #10b981;
}

.comment-bubble {
  position: relative;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
}

.comment-bubble::after {
  content: '';
  position: absolute;
  left: -10px;
  top: 15px;
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent #f3f4f6 transparent transparent;
}

.annotation {
  border-left: 3px solid #3b82f6;
  background-color: #eff6ff;
}

.flex-col-md-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 添加加载动画样式 */
:deep(.el-loading-spinner) {
  .el-loading-text {
    color: #409EFF;
    margin: 3px 0;
    font-size: 14px;
  }
  .circular {
    width: 42px;
    height: 42px;
  }
}
</style> 