<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="视频课程管理"
    activePage="content-creation"
    activeSubPage="video-course"
  >
    <div class="space-y-4">
      <!-- 数据看板 -->
      <div class="flex flex-wrap gap-4 mb-3">
        <div class="bg-blue-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">全部视频</p>
              <p class="text-2xl font-bold text-gray-800">{{ totalVideos }}</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
              <i class="material-icons text-blue-600 text-2xl">movie</i>
            </div>
          </div>
        </div>
        
        <div class="bg-orange-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">生成中</p>
              <p class="text-2xl font-bold text-gray-800">{{ processingVideos }}</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
              <i class="material-icons text-orange-600 text-2xl">hourglass_empty</i>
            </div>
          </div>
        </div>
        
        <div class="bg-green-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">已完成</p>
              <p class="text-2xl font-bold text-gray-800">{{ completedVideos }}</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
              <i class="material-icons text-green-600 text-2xl">check_circle</i>
            </div>
          </div>
        </div>
        
        <div class="bg-red-50 rounded-md p-4 shadow-sm border border-gray-100 flex-1 min-w-[200px]">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">生成失败</p>
              <p class="text-2xl font-bold text-gray-800">{{ failedVideos }}</p>
            </div>
            <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
              <i class="material-icons text-red-600 text-2xl">error</i>
            </div>
          </div>
        </div>
      </div>

      <!-- 合并的内容区域 -->
      <div class="bg-white rounded-lg p-3 shadow border border-gray-200">
        <!-- 顶部功能区 -->
        <div class="flex flex-wrap justify-between items-center gap-4 mb-3">
          <div class="flex gap-3">
            <!-- 预留位置，可添加其他功能按钮 -->
          </div>
          
          <div class="flex items-center gap-4">
            <div class="relative">
              <el-input 
                v-model="searchQuery"
                placeholder="搜索视频..." 
                clearable
                :prefix-icon="Search"
                class="w-64"
              />
            </div>
          </div>
        </div>



        <!-- 视频区域 -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-base font-medium text-gray-800 flex items-center">
              <el-icon class="mr-2"><VideoPlay /></el-icon>
              视频课程
            </h3>
            
            <div class="flex items-center gap-2">
              <el-select v-model="statusFilter" placeholder="状态筛选" size="default">
                <el-option label="全部状态" value="all" />
                <el-option label="生成中" value="processing" />
                <el-option label="已完成" value="completed" />
                <el-option label="失败" value="failed" />
              </el-select>
              

            </div>
          </div>

          <div class="grid gap-4 responsive-video-grid">
            <template v-for="video in filteredVideos" :key="'video-' + video.id">
              <el-card
                :body-style="{ padding: '0px' }"
                shadow="hover"
                class="video-card overflow-hidden"
                @click="playVideo(video)"
                :class="{ 'cursor-pointer': video.status === 'success' }"
              >
                <div class="relative">
                  <!-- 视频缩略图 -->
                  <div class="relative h-[320px] bg-gray-100 overflow-hidden">
                    <img 
                      :src="video.thumbnail" 
                      class="w-full h-full object-cover"
                      :alt="video.name"
                    />
                    <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                      {{ formatDuration(video.duration) }}
                    </div>
                    <div 
                      v-if="video.status === 'processing'" 
                      class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
                    >
                      <div class="text-center">
                        <div class="processing-icon-container">
                          <el-progress
                            type="dashboard"
                            :percentage="video.progress || 50"
                            :width="70"
                            :stroke-width="6"
                            :color="'rgba(255, 255, 255, 0.8)'"
                            :show-text="false"
                            class="processing-progress"
                          />
                          <div class="processing-inner-circle">
                            <el-icon class="processing-gear-icon"><Loading /></el-icon>
                          </div>
                        </div>
                        <div class="text-white text-xs mt-2 font-medium">处理中</div>
                      </div>
                    </div>
                    <div 
                      v-if="video.status === 'success'" 
                      class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300"
                    >
                      <div class="play-button-container">
                        <div class="play-button-ring bg-gray-100"></div>
                        <el-button circle class="play-button" size="large">
                          <el-icon><CaretRight class="play-icon" /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 视频信息 -->
                  <div class="p-3">
                    <div class="flex items-start justify-between">
                      <div>
                        <div class="text-sm font-medium text-gray-900 line-clamp-1">{{ video.name }}</div>
                        <div class="flex items-center mt-1 space-x-2">
                          <el-tag size="small" :type="getStatusType(video.status)">
                            {{ getStatusLabel(video.status) }}
                          </el-tag>
                          <span class="text-xs text-gray-500">{{ new Date(video.create_time).toLocaleDateString() }}</span>
                        </div>
                      </div>
                      <el-dropdown trigger="click" @command="handleVideoCommand($event, video)">
                        <el-button type="text" size="small" @click.stop>
                          <el-icon><MoreFilled /></el-icon>
                        </el-button>
                        <template #dropdown>
                                                  <el-dropdown-item command="view">查看</el-dropdown-item>
                        <el-dropdown-item command="rename">重命名</el-dropdown-item>
                        <el-dropdown-item command="delete" class="text-red-500">删除</el-dropdown-item>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
              </el-card>
            </template>

            <div 
              v-if="filteredVideos.length === 0" 
              class="col-span-full py-8 text-center text-gray-500"
            >
              <el-icon class="text-4xl mb-2"><VideoCamera /></el-icon>
              <div>暂无视频课程</div>
              <button
                class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2 mx-auto mt-3"
                @click="createNewVideoCourse"
              >
                <i class="material-icons text-sm">add</i>
                创建合生视频
              </button>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="mt-4 flex justify-center" v-if="filteredVideos.length > 0">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 30, 40]"
              layout="sizes, prev, pager, next, jumper, total"
              :total="filteredVideos.length"
              background
            />
          </div>
        </div>
      </div>
    </div>
    

    
    <!-- 重命名视频对话框 -->
    <el-dialog
      v-model="showRenameVideoDialog"
      title="重命名视频"
      width="400px"
    >
      <el-form :model="videoForm" label-position="top">
        <el-form-item label="视频名称">
          <el-input v-model="videoForm.title" placeholder="请输入视频名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRenameVideoDialog = false">取消</el-button>
          <el-button type="primary" @click="handleRenameVideoConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
    

    
    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="showDeleteConfirmDialog"
      title="删除确认"
      width="420px"
      align-center
    >
      <div class="text-center p-4">
        <el-icon color="#E6A23C" :size="50" class="mb-4"><WarningFilled /></el-icon>
        <p class="text-base text-gray-700">
          您确定要删除视频
        </p>
        <p class="text-lg font-medium text-gray-900 my-2">"{{ deleteTarget?.title }}"</p>
        <p class="text-sm text-gray-500 mt-2">此操作将无法恢复，请谨慎操作。</p>
      </div>
      <template #footer>
        <div class="dialog-footer flex justify-center">
          <el-button @click="showDeleteConfirmDialog = false" class="px-6">取消</el-button>
          <el-button type="danger" @click="handleDeleteConfirm" class="px-6">确认删除</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 视频播放器对话框 -->
    <el-dialog
      v-model="showVideoPlayerDialog"
      :title="currentVideoTitle"
      width="60%"
      @close="handleVideoPlayerClose"
      destroy-on-close
    >
      <video v-if="currentVideoUrl" :src="currentVideoUrl" controls autoplay class="w-full max-h-[70vh]"></video>
    </el-dialog>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { 
  Search, 
  MoreFilled, 
  VideoPlay, 
  VideoCamera,
  Loading,
  CaretRight,
  WarningFilled
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { videoGenerationApi } from '@/api/videoGeneration'

// Router
const router = useRouter()

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('all')
const sortOption = ref('recent')

// 分页
const currentPage = ref(1)
const pageSize = ref(8)

// 加载状态
const loading = ref(false)

// 对话框状态
const showRenameVideoDialog = ref(false)
const showDeleteConfirmDialog = ref(false)
const showVideoPlayerDialog = ref(false)

// 表单数据
const videoForm = ref({ id: null, title: '' })
const currentVideoUrl = ref('')
const currentVideoTitle = ref('')

// 删除相关
const deleteType = ref('') // 'folder' or 'video'
const deleteTarget = ref(null)

// 统计数据
const statistics = ref({
  total: 0,
  processing: 0,
  completed: 0,
  failed: 0
})

// 视频课程数据 - 改为从API获取
const videoCourseProjects = ref([])

// 过滤视频
const filteredVideos = computed(() => {
  let result = [...videoCourseProjects.value]
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(video => 
      video.name.toLowerCase().includes(query) || 
      video.text_content?.toLowerCase().includes(query)
    )
  }
  
  if (statusFilter.value !== 'all') {
    const statusMap = {
      'completed': 'success',
      'processing': 'processing', 
      'failed': 'failed'
    }
    const actualStatus = statusMap[statusFilter.value]
    result = result.filter(video => video.status === actualStatus)
  }

  // 排序
  if (sortOption.value === 'recent') {
    result = [...result].sort((a, b) => new Date(b.create_time) - new Date(a.create_time))
  } else if (sortOption.value === 'az') {
    result = [...result].sort((a, b) => a.name.localeCompare(b.name))
  } else if (sortOption.value === 'za') {
    result = [...result].sort((a, b) => b.name.localeCompare(a.name))
  }

  return result
})

// 格式化时长函数
const formatDuration = (duration) => {
  if (!duration) return '00:00'
  
  // 如果duration是字符串格式如"12:34"，直接返回
  if (typeof duration === 'string' && duration.includes(':')) {
    return duration
  }
  
  // 如果是秒数，转换为分:秒格式
  const seconds = parseInt(duration)
  if (isNaN(seconds)) return '00:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 获取默认缩略图
const getDefaultThumbnail = (inputType) => {
  const typeMap = {
    'text': 'https://picsum.photos/300/200?blue',
    'audio': 'https://picsum.photos/300/200?green'
  }
  return typeMap[inputType] || 'https://picsum.photos/300/200?gray'
}

// 获取状态标签和样式
const getStatusLabel = (status) => {
  const statusMap = {
    'success': '已完成',
    'processing': '生成中',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const statusTypeMap = {
    'success': 'success',
    'processing': 'warning',
    'failed': 'danger'
  }
  return statusTypeMap[status] || 'info'
}



// 加载视频数据
const loadVideos = async () => {
  try {
    loading.value = true
    const params = {
      skip: 0,
      limit: 100, // 先获取全部数据，前端分页
      search: searchQuery.value || undefined,
      status: statusFilter.value !== 'all' ? statusFilter.value : undefined
    }
    
    const response = await videoGenerationApi.getGeneratedVideos(params)
    
    if (response.code === 200) {
      // 转换数据格式以适配现有UI
      videoCourseProjects.value = response.data.videos.map(video => ({
        id: video.id,
        title: video.name,
        name: video.name,
        description: video.text_content || '数字人视频',
        subject: video.input_type === 'text' ? '文本生成' : '音频生成',
        duration: video.duration || 0,
        status: video.status,
        progress: video.status === 'success' ? 100 : (video.status === 'processing' ? 50 : 0),
        updatedAt: video.create_time ? new Date(video.create_time).toLocaleDateString() : '',
        thumbnail: video.cover_image || getDefaultThumbnail(video.input_type),
        video_url: video.video_url,
        input_type: video.input_type,
        text_content: video.text_content,
        create_time: video.create_time
      }))
    }
  } catch (error) {
    console.error('加载视频数据失败:', error)
    ElMessage.error('加载视频数据失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await videoGenerationApi.getVideoStatistics()
    if (response.code === 200) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 创建新视频课程
const createNewVideoCourse = () => {
  // 跳转到视频生成页面
  router.push('/teacher/video-projects')
}

// 视频操作处理
const handleVideoCommand = async (command, video) => {
  if (command === 'view') {
    if (video.video_url) {
      // 如果有视频URL，可以播放
      playVideo(video)
    } else {
      ElMessage.info(`视频正在生成中，请稍后查看`)
    }
  } else if (command === 'rename') {
    videoForm.value = { id: video.id, title: video.title }
    showRenameVideoDialog.value = true
  } else if (command === 'delete') {
    deleteType.value = 'video'
    deleteTarget.value = video
    showDeleteConfirmDialog.value = true
  }
}



// 重命名视频
const handleRenameVideoConfirm = async () => {
  if (!videoForm.value.title) {
    ElMessage.warning('请输入视频名称')
    return
  }
  
  try {
    const response = await videoGenerationApi.updateGeneratedVideo(videoForm.value.id, {
      name: videoForm.value.title
    })
    
    if (response.code === 200) {
      ElMessage.success('视频重命名成功')
      loadVideos() // 重新加载数据
    } else {
      ElMessage.error(response.message || '重命名失败')
    }
  } catch (error) {
    console.error('重命名视频失败:', error)
    ElMessage.error('重命名失败')
  }
  
  showRenameVideoDialog.value = false
}



// 删除确认
const handleDeleteConfirm = async () => {
  if (deleteType.value === 'video') {
    // 删除视频
    try {
      const response = await videoGenerationApi.deleteGeneratedVideo(deleteTarget.value.id)
      
      if (response.code === 200) {
        ElMessage.success('视频删除成功')
        loadVideos() // 重新加载数据
        loadStatistics() // 重新加载统计数据
      } else {
        ElMessage.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除视频失败:', error)
      ElMessage.error('删除失败')
    }
  }
  
  showDeleteConfirmDialog.value = false
}

// 播放视频
const playVideo = (video) => {
  if (video.status === 'success' && video.video_url) {
    currentVideoUrl.value = video.video_url
    currentVideoTitle.value = video.name || video.title
    showVideoPlayerDialog.value = true
  } else if (video.status === 'processing') {
    ElMessage.info('视频正在生成中，请稍后查看')
  } else {
    ElMessage.error('视频无法播放')
  }
}

const handleVideoPlayerClose = () => {
  currentVideoUrl.value = ''
  currentVideoTitle.value = ''
}

// 视频统计数据计算属性
const totalVideos = computed(() => statistics.value.total)
const processingVideos = computed(() => statistics.value.processing)
const completedVideos = computed(() => statistics.value.completed)
const failedVideos = computed(() => statistics.value.failed)

// 组件挂载时加载数据
onMounted(async () => {
  await Promise.all([
    loadVideos(),
    loadStatistics()
  ])
})

// 监听搜索和筛选变化
import { watch } from 'vue'
watch([searchQuery, statusFilter], () => {
  loadVideos()
})
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-card {
  transition: transform 0.2s;
}

.video-card:hover {
  transform: translateY(-5px);
}



.play-button {
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  transform: scale(1.2);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.25);
  z-index: 2;
  border: 2px solid rgba(255, 255, 255, 0.7);
}

.play-button:hover {
  transform: scale(1.4);
  background: rgba(255, 255, 255, 0.35);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.play-icon {
  font-size: 2.2rem;
  margin-left: 3px;
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.play-button-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button-ring {
  position: absolute;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.6);
  animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

.processing-progress :deep(.el-progress__text) {
  color: white !important;
  font-weight: bold;
}

.processing-icon-container {
  position: relative;
  display: inline-block;
}

.processing-inner-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.processing-gear-icon {
  font-size: 22px;
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  animation: spin 2s linear infinite;
}

.processing-progress :deep(.el-progress-circle__track) {
  stroke: rgba(255, 255, 255, 0.2) !important;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.7;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  70% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(0.8);
    opacity: 0;
  }
}



.responsive-video-grid {
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
}
</style>
