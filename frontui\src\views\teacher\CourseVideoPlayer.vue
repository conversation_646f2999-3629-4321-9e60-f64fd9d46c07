<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :title="course ? course.name : '课程视频'"
    width="80%"
    :before-close="handleClose"
    class="video-player-dialog"
    draggable
    destroy-on-close
    top="5vh"
  >
    <div v-if="course" class="flex">
      <!-- 左侧视频播放区域 -->
      <div class="video-player-container flex-grow w-2/3 pr-4">
        <div v-if="!hasVideo" class="no-video-message">
          <div class="flex flex-col items-center justify-center p-6">
            <span class="material-icons text-5xl text-gray-400 mb-3">videocam_off</span>
            <p class="text-lg text-gray-500">此课程暂无视频</p>
          </div>
        </div>
        <video 
          v-else
          ref="videoPlayer"
          controls
          class="w-full rounded-md"
          style="max-height: 65vh;"
          draggable="false"
        >
          <source 
            :src="videoUrl" 
            type="video/mp4"
          >
          您的浏览器不支持HTML5视频播放
        </video>
        
        <div v-if="hasVideo && currentLesson" class="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 class="text-lg font-medium text-gray-900">{{ currentLesson.title }}</h3>
          <div class="flex items-center text-sm text-gray-500 mt-1">
            <span class="material-icons text-gray-400 text-sm mr-1">account_circle</span>
            <span>{{ course.teacher }}</span>
            <span class="mx-2">|</span>
            <span class="material-icons text-gray-400 text-sm mr-1">school</span>
            <span>{{ course.college }}</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧章节列表 -->
      <div class="chapters-list w-1/3 pl-4 border-l border-gray-200 overflow-y-auto" style="max-height: 65vh;">
        <h3 class="text-lg font-medium mb-4 flex items-center">
          <span class="material-icons mr-2 text-blue-600">video_library</span>
          课程内容
        </h3>
        
        <div v-if="!course.chapters || course.chapters.length === 0" class="text-center py-6 text-gray-500">
          <span class="material-icons text-gray-400 text-4xl mb-2">folder_open</span>
          <p>此课程暂无章节内容</p>
        </div>
        
        <div v-else class="space-y-4">
          <el-card v-for="(chapter, chapterIndex) in course.chapters" :key="`chapter-${chapterIndex}`" class="chapter-card mb-4" shadow="hover">
            <template #header>
              <div class="chapter-header flex items-center justify-between" @click="toggleChapter(chapterIndex)">
                <div class="flex items-center space-x-3">
                  <div class="chapter-index w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center font-medium text-base">
                    {{ chapterIndex + 1 }}
                  </div>
                  <h4 class="font-medium text-sm">{{ chapter.title || `第${chapterIndex + 1}章` }}</h4>
                </div>
                <span class="material-icons text-gray-500 text-sm transform transition-transform duration-200" :class="{'rotate-180': expandedChapters[chapterIndex]}">
                  expand_more
                </span>
              </div>
            </template>
            
            <div v-if="expandedChapters[chapterIndex]" class="lessons-list space-y-2 pt-1">
              <div v-if="!chapter.lessons || chapter.lessons.length === 0" class="text-gray-400 text-center py-4 bg-gray-50 rounded-lg">
                <span class="material-icons text-2xl text-gray-300 mb-1">video_library</span>
                <p class="text-sm">暂无课时</p>
              </div>
              
              <div 
                v-for="(lesson, lessonIndex) in chapter.lessons" 
                :key="`lesson-${chapterIndex}-${lessonIndex}`"
                class="lesson-item p-3 border border-gray-200 rounded-lg flex items-center hover:bg-gray-50 cursor-pointer"
                :class="{'bg-blue-50 border-blue-200': isCurrentLesson(chapter, lesson)}"
                @click="playLesson(chapter, lesson, chapterIndex, lessonIndex)"
              >
                <div class="lesson-index w-6 h-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center text-xs font-medium mr-3"
                  :class="{'bg-blue-100 text-blue-600': isCurrentLesson(chapter, lesson)}">
                  {{ lessonIndex + 1 }}
                </div>
                <div class="flex-1 min-w-0">
                  <div class="lesson-title text-sm font-medium truncate">{{ lesson.title || `课时 ${lessonIndex + 1}` }}</div>
                  <div class="lesson-meta flex items-center text-xs text-gray-500 mt-1">
                    <span v-if="isCurrentLesson(chapter, lesson)" class="flex items-center text-blue-600">
                      <span class="material-icons text-xs mr-1">play_circle</span>
                      <span>正在播放</span>
                    </span>
                    <span v-else-if="lesson.videoFile" class="flex items-center">
                      <span class="material-icons text-xs mr-1">videocam</span>
                      <span>{{ formatDuration(lesson.duration || '00:00') }}</span>
                    </span>
                    <span v-else class="flex items-center text-orange-500">
                      <span class="material-icons text-xs mr-1">warning</span>
                      <span>无视频</span>
                    </span>
                  </div>
                </div>
                <div class="lesson-status ml-2">
                  <span v-if="isCurrentLesson(chapter, lesson)" class="material-icons text-blue-600">play_arrow</span>
                  <span v-else-if="lesson.videoFile" class="material-icons text-gray-400">video_library</span>
                  <span v-else class="material-icons text-gray-300">videocam_off</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted } from 'vue'
import { ElCard } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  course: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'close'])

const videoPlayer = ref(null)
const hasVideo = ref(false)
const videoUrl = ref('')
const expandedChapters = ref({})
const currentChapterIndex = ref(null)
const currentLessonIndex = ref(null)
const currentLesson = ref(null)
const currentChapter = ref(null)

// 初始化章节展开状态
const initExpandedChapters = (course) => {
  if (course && course.chapters) {
    // 默认展开第一个章节
    expandedChapters.value = course.chapters.reduce((acc, _, index) => {
      acc[index] = index === 0 // 只展开第一个章节
      return acc
    }, {})
  }
}

// 切换章节展开/收起状态
const toggleChapter = (chapterIndex) => {
  expandedChapters.value[chapterIndex] = !expandedChapters.value[chapterIndex]
}

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return '00:00'
  return duration
}

// 判断是否为当前播放的课时
const isCurrentLesson = (chapter, lesson) => {
  return currentLesson.value === lesson
}

// 播放指定课时视频
const playLesson = (chapter, lesson, chapterIndex, lessonIndex) => {
  if (!lesson.videoFile) {
    console.log('该课时没有视频')
    return
  }
  
  currentChapterIndex.value = chapterIndex
  currentLessonIndex.value = lessonIndex
  currentLesson.value = lesson
  currentChapter.value = chapter
  
  // 更新视频URL
  if (lesson.videoFile) {
    // 在实际应用中使用真实URL
    // videoUrl.value = URL.createObjectURL(lesson.videoFile)
    
    // 示例视频URL
    //videoUrl.value = "https://www.w3schools.com/html/mov_bbb.mp4"
    videoUrl.value = lesson.video_url
    hasVideo.value = true
    
    // 播放新视频
    setTimeout(() => {
      if (videoPlayer.value) {
        videoPlayer.value.load()
        videoPlayer.value.play().catch(err => {
          console.log('视频自动播放失败，这通常是由于浏览器策略导致的', err)
        })
      }
    }, 100)
  }
}

// 尝试找到第一个有视频的课时并播放
const findAndPlayFirstVideo = (course) => {
  if (!course || !course.chapters) return false
  
  for (let i = 0; i < course.chapters.length; i++) {
    const chapter = course.chapters[i]
    if (!chapter.lessons || chapter.lessons.length === 0) continue
    
    for (let j = 0; j < chapter.lessons.length; j++) {
      const lesson = chapter.lessons[j]
      if (lesson.videoFile) {
        // 找到有视频的课时，播放并展开对应章节
        playLesson(chapter, lesson, i, j)
        expandedChapters.value[i] = true
        return true
      }
    }
  }
  
  return false
}

// 监听课程变化
watch(() => props.course, (newCourse) => {
  console.log('预览课时',newCourse);
  if (newCourse) {
    // 初始化章节展开状态
    initExpandedChapters(newCourse)
    
    // 检查是否有视频
    hasVideo.value = newCourse.hasVideo
    
    if (hasVideo.value) {
      // 尝试找到并播放第一个有视频的课时
      const found = findAndPlayFirstVideo(newCourse)
      
      // 如果没有找到课时，使用默认视频
      if (!found) {
        videoUrl.value = "https://www.w3schools.com/html/mov_bbb.mp4"
        
        // 如果有章节和课时，至少选中第一个作为当前课时（即使没有视频）
        if (newCourse.chapters && 
            newCourse.chapters.length > 0 && 
            newCourse.chapters[0].lessons && 
            newCourse.chapters[0].lessons.length > 0) {
          currentChapter.value = newCourse.chapters[0]
          currentLesson.value = newCourse.chapters[0].lessons[0]
          currentChapterIndex.value = 0
          currentLessonIndex.value = 0
          expandedChapters.value[0] = true
        }
      }
      
      // 在视频加载完成后添加进度事件监听
      setTimeout(() => {
        if (videoPlayer.value) {
          videoPlayer.value.addEventListener('timeupdate', updateProgress)
        }
      }, 500)
    }
  }
}, { immediate: true })

// 更新进度条
const updateProgress = () => {
  if (videoPlayer.value) {
    const currentTime = videoPlayer.value.currentTime
    const duration = videoPlayer.value.duration
    
    // 可以在这里更新课时的学习进度
    if (currentChapter.value && currentLesson.value) {
      // 在实际应用中，这里可以发送请求更新学习进度
      console.log(`章节"${currentChapter.value.title}" 课时"${currentLesson.value.title}" 播放进度: ${Math.floor((currentTime / duration) * 100)}%`)
    }
  }
}

// 关闭对话框
const handleClose = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause()
    // 移除事件监听器
    videoPlayer.value.removeEventListener('timeupdate', updateProgress)
  }
  
  // 重置状态
  currentChapter.value = null
  currentLesson.value = null
  currentChapterIndex.value = null
  currentLessonIndex.value = null
  
  emit('update:modelValue', false)
  emit('close')
}
</script>

<style scoped>
/* 视频播放器样式 */
.video-player-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  cursor: move; /* 提示用户可拖动 */
}

.video-player-dialog :deep(.el-dialog) {
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
  border: 1px solid #e2e8f0;
}

.video-player-dialog :deep(.el-dialog__body) {
  padding: 10px;
}

.video-player-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

.video-player-dialog :deep(.el-dialog__headerbtn) {
  top: 10px;
}

.video-player-container {
  position: relative;
}

.no-video-message {
  background-color: #f1f5f9;
  border-radius: 4px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 章节列表样式 */
.chapter-card {
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.chapter-card :deep(.el-card__header) {
  padding: 10px 16px;
  cursor: pointer;
  background-color: #f8fafc;
}

.chapter-card :deep(.el-card__body) {
  padding: 8px 12px;
}

.chapter-header {
  transition: background-color 0.2s;
}

.lesson-item {
  transition: all 0.2s;
  margin-bottom: 6px;
}

.lesson-item:hover {
  background-color: #f3f4f6;
}

/* 视频播放器控制样式增强 */
video::-webkit-media-controls {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
  padding: 1px;
}

video::-webkit-media-controls-play-button,
video::-webkit-media-controls-timeline,
video::-webkit-media-controls-volume-slider {
  cursor: pointer;
}

video::-webkit-media-controls-timeline {
  height: 6px;
}

/* 调整视频播放器边距 */
video {
  margin: 0;
  border-radius: 4px;
}
</style> 