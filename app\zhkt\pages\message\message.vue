<template>
  <view class="container">
    <!-- 顶部选项卡 -->
    <view class="tabs">
      <view 
        class="tab-item" 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>
    
    <!-- 通讯录列表 -->
    <view class="contacts-list" v-if="currentTab === 0">
      <view class="contact-group" v-for="(group, index) in contactsList" :key="index">
        <view class="group-title">{{ group.title }}</view>
        <view 
          class="contact-item"
          v-for="contact in group.list"
          :key="contact.id"
          @click="navigateToChat(contact)"
        >
          <image class="avatar" :src="contact.avatar" mode="aspectFill"></image>
          <view class="contact-info">
            <text class="name">{{ contact.name }}</text>
            <text class="role">{{ contact.role }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 消息列表 -->
    <view class="message-list" v-else>
      <view 
        class="message-item"
        v-for="message in messageList"
        :key="message.id"
        @click="navigateToChat(message)"
      >
        <image class="avatar" :src="message.avatar" mode="aspectFill"></image>
        <view class="message-content">
          <view class="message-header">
            <text class="name">{{ message.name }}</text>
            <text class="time">{{ message.time }}</text>
          </view>
          <view class="message-body">
            <text class="preview">{{ message.lastMessage }}</text>
            <view class="badge" v-if="message.unread">{{ message.unread }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'

// 选项卡数据
const tabs = ref([
  { name: '通讯录' },
  { name: '消息' }
])
const currentTab = ref(0)

// 通讯录数据
const contactsList = ref([])
// 消息列表数据
const messageList = ref([])

// 切换选项卡
const switchTab = (index) => {
  currentTab.value = index
  // if (index === 0) {
  //   getContacts()
  // } else {
  //   getMessages()
  // }
}

// 获取通讯录列表
const getContacts = async () => {
  try {
    const response = await request({
      url: '/contacts/list/',
      method: 'GET'
    })
    contactsList.value = response
  } catch (error) {
    uni.showToast({
      title: '获取通讯录失败',
      icon: 'none'
    })
  }
}

// 获取消息列表
const getMessages = async () => {
  try {
    const response = await request({
      url: '/message/list/',
      method: 'GET'
    })
    messageList.value = response.results
  } catch (error) {
    uni.showToast({
      title: '获取消息列表失败',
      icon: 'none'
    })
  }
}

// 跳转到聊天页面
const navigateToChat = (item) => {
  uni.navigateTo({
    url: `/pages/message/chat?id=${item.id}&name=${item.name}`
  })
}

onMounted(() => {
  //getContacts()
})
</script>

<style lang="scss">
@import '@/styles/mixins.scss';

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.tabs {
  display: flex;
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  
  .tab-item {
    flex: 1;
    text-align: center;
    font-size: 28rpx;
    color: #666;
    padding: 20rpx 0;
    position: relative;
    
    &.active {
      color: #3cc51f;
      font-weight: bold;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background: #3cc51f;
        border-radius: 2rpx;
      }
    }
  }
}

.contacts-list {
  flex: 1;
  overflow-y: auto;
  
  .contact-group {
    .group-title {
      padding: 20rpx;
      font-size: 24rpx;
      color: #999;
      background: #f5f5f5;
    }
    
    .contact-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background: #fff;
      border-bottom: 1rpx solid #eee;
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      
      .contact-info {
        .name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 6rpx;
        }
        
        .role {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}

.message-list {
  flex: 1;
  overflow-y: auto;
  
  .message-item {
    display: flex;
    padding: 20rpx;
    background: #fff;
    border-bottom: 1rpx solid #eee;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .message-content {
      flex: 1;
      
      .message-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10rpx;
        
        .name {
          font-size: 28rpx;
          color: #333;
        }
        
        .time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .message-body {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .preview {
          flex: 1;
          font-size: 24rpx;
          color: #666;
          @include text-ellipsis(1);
        }
        
        .badge {
          min-width: 32rpx;
          height: 32rpx;
          padding: 0 10rpx;
          background: #ff3b30;
          border-radius: 16rpx;
          color: #fff;
          font-size: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 10rpx;
        }
      }
    }
  }
}
</style> 