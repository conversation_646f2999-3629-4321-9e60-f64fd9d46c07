# -*- coding: utf-8 -*-
# @Time    : 2025/4/26
# <AUTHOR> lhq
# @File    : config.py
# @Description :
import platform
from typing import List
from celery import Celery
from django.utils.translation import gettext_lazy as _

# 语言选项配置 
LANGUAGE_CHOICES = (
    ('zh', _('中文')),
    ('en', _('英文')),
    ('vi', _('越南语')),
    ('id', _('印尼语')),
)

# 默认语言
DEFAULT_LANGUAGE = 'zh'

DEEPSEEK_API_KEY = "***********************************"

TONGYI_API_KEY = "sk-6bb22bf6b8f548629e975dac88c5faba "

RAG_FLOW_API_URL = "http://*************:7001"
RAG_FLOW_API_KEY = "ragflow-Q5MGQ4NjAyMWZlYzExZjA4Mjc4MDI0Mm"

PPT_API_URL = "https://docmee.cn/api/user/createApiToken"
PPT_API_KEY  = "ak_uLoBbb5TFvv35MZe4q"

# 豆包TTS配置
DOUBAO_TTS_APPID = "6535178642"
DOUBAO_TTS_TOKEN = "DMVTssnhj3eodNy7c64ME1raTUlqeZq7"
DOUBAO_TTS_CLUSTER = "volcano_tts"

# Mineru PDF解析API配置
MINERU_API_TOKEN = "eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJqdGkiOiI4NTUwNDAwNSIsInJvbCI6IlJPTEVfUkVHSVNURVIiLCJpc3MiOiJPcGVuWExhYiIsImlhdCI6MTc1MzQzODMwOCwiY2xpZW50SWQiOiJsa3pkeDU3bnZ5MjJqa3BxOXgydyIsInBob25lIjoiIiwib3BlbklkIjpudWxsLCJ1dWlkIjoiM2QwYWEwOWUtNmZhZi00OGI0LWI4NzgtMzljNWJkNGVlZGE0IiwiZW1haWwiOiIiLCJleHAiOjE3NTQ2NDc5MDh9.4Tl96-oJRAXLy2-35blvN-aimhB5V3LuQFtS4U6mXpAI0SDnfuSQowRvPSe1Vmw-FYQDJRuLpeWiR22fFOV8EQ"
MINERU_API_BASE_URL = "https://mineru.net/api/v4"

# LibreOffice路径配置(用于word转pdf)
if platform.system() == 'Windows':
    LibreOffice_path = r"C:\Program Files\LibreOffice\program\soffice.exe"
else:
    LibreOffice_path = "/usr/lib/libreoffice/program/soffice"

# 基础URL配置
BASE_URL: str = "http://localhost:6002"  # 服务基础URL，用于生成完整资源访问路径
# 文件上传配置
UPLOAD_BASE_DIR: str = r"E:\project\digital-resources"  # 文件上传基础目录
# ai-education-tool 项目
AI_EDUCATION_TOOL_URL: str = "http://*************:8001"
# AI_EDUCATION_TOOL_URL: str = "http://127.0.0.1:8001"

# 数字人推理项目
DIGITAL_HUMAN_INFERENCE_URL: str ="http://*************:8002"

# 子目录配置
AUDIO_UPLOAD_DIR: str =  "audio"  # 音频文件目录
VIDEO_UPLOAD_DIR: str = "video"  # 视频文件目录
IMAGE_UPLOAD_DIR: str = "images"  # 图片文件目录
KNOWLEDGE_UPLOAD_DIR: str = "knowledge"  # 知识库文件目录
JSON_UPLOAD_DIR: str = "json"  # json文件目录
TXT_UPLOAD_DIR: str = "txt"  # txt文件目录
HTML_UPLOAD_DIR: str = "html"  # html文件目录


# 文件类型配置
ALLOWED_AUDIO_EXTENSIONS: List[str] = ["mp3", "wav", "m4a", "aac", "flac", "webm"]  # 允许的音频文件后缀
ALLOWED_IMAGE_EXTENSIONS: List[str] = ["jpg", "jpeg", "png", "gif", "webp"]  # 允许的图片文件后缀
ALLOWED_VIDEO_EXTENSIONS: List[str] = ["mp4", "avi", "mov", "wmv", "flv", "mkv"]  # 允许的视频文件后缀
ALLOWED_KNOWLEDGE_EXTENSIONS: List[str] = ["pdf", "doc", "docx", "txt", "xls", "xlsx", "ppt", "pptx"]  # 允许的知识库文件后缀
ALLOWED_JSON_EXTENSIONS: List[str] = ["json"]  # 允许的json文件后缀
ALLOWED_TXT_EXTENSIONS: List[str] = ["txt"]  # 允许的txt文件后缀
ALLOWED_HTML_EXTENSIONS: List[str] = ["html"]  # 允许的html文件后缀

# 文件大小限制 (单位: MB)
MAX_AUDIO_FILE_SIZE: int = 20  # 音频文件最大大小
MAX_IMAGE_FILE_SIZE: int = 10  # 图片文件最大大小
MAX_VIDEO_FILE_SIZE: int = 600  # 视频文件最大大小
MAX_KNOWLEDGE_FILE_SIZE: int = 200  # 知识库文件最大大小
MAX_JSON_FILE_SIZE: int = 10  # json文件最大大小
MAX_TXT_FILE_SIZE: int = 10  # txt文件最大大小
MAX_HTML_FILE_SIZE: int = 10  # html文件最大大小

# OSS配置
OSS_ENABLED: bool = True
OSS_ACCESS_KEY_ID: str = "LTAI5tHDPyGUqRFUR4HBSM9J"
OSS_ACCESS_KEY_SECRET: str = "******************************"
OSS_ENDPOINT: str = "oss-cn-shenzhen.aliyuncs.com"
OSS_BUCKET_NAME: str = "clkj-ai-education"
OSS_BASE_URL: str = "https://clkj-ai-education.oss-cn-shenzhen.aliyuncs.com"


# 创建Celery应用
celery_app = Celery(
    "ai-education",
    broker="redis://localhost:6379/0",
    backend="redis://localhost:6379/0",
)

# 优化Celery配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=False,
    broker_connection_timeout=10,  # Redis连接超时设置
    broker_connection_retry=True,  # 允许重试连接
    broker_connection_max_retries=3,  # 最大重试次数
    broker_pool_limit=10,  # Redis连接池大小限制
    result_expires=3600,  # 结果过期时间1小时
    worker_max_tasks_per_child=200,  # 工作进程处理200个任务后重启
    # 添加错误处理相关的配置
    worker_disable_rate_limits=True,  # 禁用速率限制
    worker_cancel_long_running_tasks_on_connection_loss=False,  # 连接丢失时不取消长时间运行的任务
    task_reject_on_worker_lost=False,  # 工作进程丢失时不拒绝任务
    task_acks_late=True,  # 任务完成后才确认
)

# 自动发现所有app下的tasks.py
celery_app.autodiscover_tasks()