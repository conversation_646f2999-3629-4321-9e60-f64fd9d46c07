<template>
  <AdminLayout 
    pageTitle="系统设置" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <!-- 设置选项卡 -->
        <div class="mt-4 border-b border-gray-200">
          <div class="flex space-x-8">
            <button 
              v-for="tab in tabs" 
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm inline-flex items-center'
              ]"
            >
              <Icon :name="tab.icon" size="sm" :color="activeTab === tab.id ? 'primary' : 'default'" className="mr-2" />
              {{ tab.name }}
            </button>
          </div>
        </div>

        <!-- 基本设置区域 -->
        <div v-if="activeTab === 'basic'" class="mt-6">
          <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">系统信息</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置基本系统信息和显示选项。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
        <form @submit.prevent="saveBasicSettings">
                  <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                      <label for="site-name" class="block text-sm font-medium text-gray-700">系统名称</label>
              <input
                type="text"
                        id="site-name" 
                        v-model="settings.basic.siteName"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              >
            </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="site-url" class="block text-sm font-medium text-gray-700">系统域名</label>
                <input
                        type="text" 
                        id="site-url" 
                        v-model="settings.basic.siteUrl"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                >
              </div>

                    <div class="col-span-6">
                      <label for="site-description" class="block text-sm font-medium text-gray-700">系统描述</label>
              <textarea
                        id="site-description" 
                        v-model="settings.basic.description"
                rows="3"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border border-gray-300 rounded-md"
              ></textarea>
            </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="admin-email" class="block text-sm font-medium text-gray-700">管理员邮箱</label>
              <input
                type="email"
                        id="admin-email" 
                        v-model="settings.basic.adminEmail"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              >
            </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="system-language" class="block text-sm font-medium text-gray-700">系统默认语言</label>
                      <select 
                        id="system-language" 
                        v-model="settings.basic.language"
                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                        <option value="zh-CN">简体中文</option>
                        <option value="en">English</option>
                        <option value="ja">日本語</option>
                      </select>
                    </div>
          </div>
        </form>
              </div>
            </div>
      </div>

          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">功能与服务</h3>
                <p class="mt-1 text-sm text-gray-500">
                  启用或禁用系统功能与服务。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveFeaturesSettings">
                  <fieldset>
                    <legend class="text-base font-medium text-gray-900">功能开关</legend>
                    <div class="mt-4 space-y-4">
                      <div v-for="feature in features" :key="feature.id" class="flex items-start">
                        <div class="flex items-center h-5">
                <input
                            :id="feature.id" 
                            v-model="settings.features[feature.id]"
                  type="checkbox"
                            class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
            </div>
                        <div class="ml-3 text-sm">
                          <label :for="feature.id" class="font-medium text-gray-700">{{ feature.name }}</label>
                          <p class="text-gray-500">{{ feature.description }}</p>
                        </div>
                      </div>
                    </div>
                  </fieldset>

                  <fieldset class="mt-6">
                    <legend class="text-base font-medium text-gray-900">集成服务</legend>
                    <div class="mt-4 space-y-4">
                      <div v-for="service in services" :key="service.id" class="flex items-start">
                        <div class="flex items-center h-5">
                <input
                            :id="service.id" 
                            v-model="settings.services[service.id]"
                  type="checkbox"
                            class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
            </div>
                        <div class="ml-3 text-sm">
                          <label :for="service.id" class="font-medium text-gray-700">{{ service.name }}</label>
                          <p class="text-gray-500">{{ service.description }}</p>
                        </div>
                      </div>
                    </div>
                  </fieldset>
                </form>
              </div>
            </div>
          </div>

          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">自定义配置</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置系统的其他自定义选项。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveCustomSettings">
                  <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                      <label for="page-size" class="block text-sm font-medium text-gray-700">默认分页大小</label>
              <select
                        id="page-size" 
                        v-model="settings.custom.pageSize"
                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                        <option v-for="size in pageSizes" :key="size" :value="size">{{ size }} 条/页</option>
              </select>
            </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="date-format" class="block text-sm font-medium text-gray-700">日期格式</label>
                      <select 
                        id="date-format" 
                        v-model="settings.custom.dateFormat"
                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                        <option v-for="format in dateFormats" :key="format.value" :value="format.value">
                          {{ format.label }}
                        </option>
                      </select>
            </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="cache-time" class="block text-sm font-medium text-gray-700">页面缓存时间 (分钟)</label>
                      <input 
                        type="number" 
                        id="cache-time" 
                        v-model.number="settings.custom.cacheTime"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      >
          </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="session-timeout" class="block text-sm font-medium text-gray-700">会话超时时间 (分钟)</label>
                      <input 
                        type="number" 
                        id="session-timeout" 
                        v-model.number="settings.custom.sessionTimeout"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div class="flex justify-end mt-8">
            <button
              type="button"
              @click="saveAllSettings"
              class="inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="save" size="sm" className="mr-2 text-white" />
              保存设置
            </button>
          </div>
      </div>

        <!-- 其他标签页的内容 -->
        <div v-else-if="activeTab === 'security'" class="mt-6">
          <!-- 密码策略设置 -->
          <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">密码策略</h3>
                <p class="mt-1 text-sm text-gray-500">
                  设置系统密码的安全要求和规则。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveSecuritySettings">
                  <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                      <label for="min-password-length" class="block text-sm font-medium text-gray-700">最小密码长度</label>
              <input
                type="number"
                        id="min-password-length" 
                        v-model.number="settings.security.minPasswordLength"
                        min="6"
                        max="32"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              >
            </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="password-expiry-days" class="block text-sm font-medium text-gray-700">密码有效期（天）</label>
              <input
                type="number"
                        id="password-expiry-days" 
                        v-model.number="settings.security.passwordExpiryDays"
                min="0"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              >
                      <p class="mt-1 text-sm text-gray-500">设置为0表示永不过期</p>
            </div>

                    <div class="col-span-6">
                      <div class="space-y-4">
                        <div class="flex items-start">
                          <div class="flex items-center h-5">
                            <input
                              id="require-uppercase"
                              type="checkbox"
                              v-model="settings.security.requireUppercase"
                              class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                            >
                          </div>
                          <div class="ml-3 text-sm">
                            <label for="require-uppercase" class="font-medium text-gray-700">要求包含大写字母</label>
                          </div>
                        </div>

                        <div class="flex items-start">
                          <div class="flex items-center h-5">
                            <input
                              id="require-numbers"
                              type="checkbox"
                              v-model="settings.security.requireNumbers"
                              class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                            >
                          </div>
                          <div class="ml-3 text-sm">
                            <label for="require-numbers" class="font-medium text-gray-700">要求包含数字</label>
                          </div>
                        </div>

                        <div class="flex items-start">
                          <div class="flex items-center h-5">
                            <input
                              id="require-symbols"
                              type="checkbox"
                              v-model="settings.security.requireSymbols"
                              class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                            >
                          </div>
                          <div class="ml-3 text-sm">
                            <label for="require-symbols" class="font-medium text-gray-700">要求包含特殊字符</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 登录安全设置 -->
          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">登录安全</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置登录相关的安全选项。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveSecuritySettings">
                  <div class="space-y-6">
            <div>
                      <label for="max-login-attempts" class="block text-sm font-medium text-gray-700">最大登录尝试次数</label>
              <input
                type="number"
                        id="max-login-attempts" 
                        v-model.number="settings.security.maxLoginAttempts"
                        min="1"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      >
                      <p class="mt-1 text-sm text-gray-500">超过此次数将暂时锁定账号</p>
            </div>

            <div>
                      <label for="lockout-duration" class="block text-sm font-medium text-gray-700">账号锁定时长（分钟）</label>
              <input
                type="number"
                        id="lockout-duration" 
                        v-model.number="settings.security.lockoutDuration"
                        min="1"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              >
            </div>

                    <div class="space-y-4">
                      <div class="flex items-start">
                        <div class="flex items-center h-5">
                          <input
                            id="enable-2fa"
                            type="checkbox"
                            v-model="settings.security.enable2FA"
                            class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                          >
          </div>
                        <div class="ml-3 text-sm">
                          <label for="enable-2fa" class="font-medium text-gray-700">启用双因素认证</label>
                          <p class="text-gray-500">要求用户使用手机验证码或认证器进行二次验证</p>
                        </div>
                      </div>

                      <div class="flex items-start">
                        <div class="flex items-center h-5">
                          <input
                            id="force-password-change"
                            type="checkbox"
                            v-model="settings.security.forcePasswordChange"
                            class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                          >
                        </div>
                        <div class="ml-3 text-sm">
                          <label for="force-password-change" class="font-medium text-gray-700">首次登录强制修改密码</label>
                          <p class="text-gray-500">新用户首次登录时必须修改默认密码</p>
                        </div>
                      </div>
                    </div>
          </div>
        </form>
              </div>
      </div>
    </div>

          <!-- IP访问控制 -->
          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">访问控制</h3>
                <p class="mt-1 text-sm text-gray-500">
                  设置IP访问白名单和黑名单。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveSecuritySettings">
                  <div class="space-y-6">
        <div>
                      <label class="block text-sm font-medium text-gray-700">IP白名单</label>
                      <div class="mt-1">
                        <textarea
                          v-model="settings.security.ipWhitelist"
                          rows="3"
                          class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          placeholder="每行输入一个IP地址或IP段，例如：&#10;***********&#10;10.0.0.0/24"
                        ></textarea>
                      </div>
                      <p class="mt-1 text-sm text-gray-500">留空表示允许所有IP访问</p>
                    </div>

            <div>
                      <label class="block text-sm font-medium text-gray-700">IP黑名单</label>
                      <div class="mt-1">
                        <textarea
                          v-model="settings.security.ipBlacklist"
                          rows="3"
                          class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          placeholder="每行输入一个IP地址或IP段，例如：&#10;*******&#10;*******/24"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div class="flex justify-end mt-8">
            <button 
              type="button"
              @click="saveSecuritySettings"
              class="inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="save" size="sm" className="mr-2 text-white" />
              保存安全设置
            </button>
          </div>
        </div>
        <div v-else-if="activeTab === 'notification'" class="mt-6">
          <!-- 系统通知设置 -->
          <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">系统通知</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置系统级别的通知选项。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveNotificationSettings">
                  <div class="space-y-4">
                    <div class="flex items-start">
                      <div class="flex items-center h-5">
                <input
                          id="enable-system-notifications"
                  type="checkbox"
                          v-model="settings.notification.enableSystemNotifications"
                          class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                >
            </div>
                      <div class="ml-3 text-sm">
                        <label for="enable-system-notifications" class="font-medium text-gray-700">启用系统通知</label>
                        <p class="text-gray-500">在用户界面显示系统通知消息</p>
                      </div>
                    </div>

                    <div class="flex items-start">
                      <div class="flex items-center h-5">
                        <input
                          id="enable-browser-notifications"
                          type="checkbox"
                          v-model="settings.notification.enableBrowserNotifications"
                          class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                        >
                      </div>
                      <div class="ml-3 text-sm">
                        <label for="enable-browser-notifications" class="font-medium text-gray-700">启用浏览器通知</label>
                        <p class="text-gray-500">允许发送浏览器桌面通知</p>
                      </div>
                    </div>

            <div>
                      <label for="notification-display-time" class="block text-sm font-medium text-gray-700">通知显示时长（秒）</label>
              <input
                type="number"
                        id="notification-display-time" 
                        v-model.number="settings.notification.displayDuration"
                        min="1"
                        max="30"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              >
            </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 邮件通知设置 -->
          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">邮件通知</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置系统邮件通知选项。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveNotificationSettings">
                  <div class="space-y-6">
                    <div class="flex items-start">
                      <div class="flex items-center h-5">
                        <input
                          id="enable-email-notifications"
                          type="checkbox"
                          v-model="settings.notification.enableEmailNotifications"
                          class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                        >
                      </div>
                      <div class="ml-3 text-sm">
                        <label for="enable-email-notifications" class="font-medium text-gray-700">启用邮件通知</label>
                        <p class="text-gray-500">通过邮件发送重要通知</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-6 gap-6">
                      <div class="col-span-6 sm:col-span-3">
                        <label for="smtp-host" class="block text-sm font-medium text-gray-700">SMTP服务器</label>
                        <input 
                          type="text" 
                          id="smtp-host" 
                          v-model="settings.notification.smtpHost"
                          class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          :disabled="!settings.notification.enableEmailNotifications"
                        >
                      </div>

                      <div class="col-span-6 sm:col-span-3">
                        <label for="smtp-port" class="block text-sm font-medium text-gray-700">SMTP端口</label>
                        <input 
                          type="number" 
                          id="smtp-port" 
                          v-model.number="settings.notification.smtpPort"
                          class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          :disabled="!settings.notification.enableEmailNotifications"
                        >
                      </div>

                      <div class="col-span-6 sm:col-span-3">
                        <label for="smtp-username" class="block text-sm font-medium text-gray-700">SMTP用户名</label>
                        <input 
                          type="text" 
                          id="smtp-username" 
                          v-model="settings.notification.smtpUsername"
                          class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          :disabled="!settings.notification.enableEmailNotifications"
                        >
                      </div>

                      <div class="col-span-6 sm:col-span-3">
                        <label for="smtp-password" class="block text-sm font-medium text-gray-700">SMTP密码</label>
                        <input 
                          type="password" 
                          id="smtp-password" 
                          v-model="settings.notification.smtpPassword"
                          class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          :disabled="!settings.notification.enableEmailNotifications"
                        >
                      </div>

                      <div class="col-span-6">
                        <label for="sender-email" class="block text-sm font-medium text-gray-700">发件人邮箱</label>
                        <input 
                          type="email" 
                          id="sender-email" 
                          v-model="settings.notification.senderEmail"
                          class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          :disabled="!settings.notification.enableEmailNotifications"
                        >
                      </div>
                    </div>

                    <div>
            <button
                        type="button"
                        @click="testEmailSettings"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        :disabled="!settings.notification.enableEmailNotifications"
                      >
                        <i class="fas fa-paper-plane mr-2"></i>
                        发送测试邮件
            </button>
                    </div>
                  </div>
                </form>
              </div>
          </div>
        </div>

          <!-- 通知事件设置 -->
          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">通知事件</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置需要发送通知的系统事件。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveNotificationSettings">
          <div class="space-y-4">
                    <div v-for="event in notificationEvents" :key="event.id" class="flex items-start">
                      <div class="flex items-center h-5">
                        <input
                          :id="event.id"
                          type="checkbox"
                          v-model="settings.notification.events[event.id]"
                          class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                        >
                      </div>
                      <div class="ml-3">
                        <div class="flex items-center">
                          <label :for="event.id" class="text-sm font-medium text-gray-700">{{ event.name }}</label>
                          <span :class="['ml-2 px-2 py-1 text-xs rounded-full', event.priority === 'high' ? 'bg-red-100 text-red-800' : event.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800']">
                            {{ event.priority === 'high' ? '高优先级' : event.priority === 'medium' ? '中优先级' : '低优先级' }}
                          </span>
                        </div>
                        <p class="text-sm text-gray-500">{{ event.description }}</p>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div class="flex justify-end mt-8">
            <button 
              type="button"
              @click="saveNotificationSettings"
              class="inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="save" size="sm" className="mr-2 text-white" />
              保存通知设置
            </button>
          </div>
        </div>
        <div v-else-if="activeTab === 'backup'" class="mt-6">
          <!-- 自动备份设置 -->
          <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">自动备份设置</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置系统自动备份的相关选项。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveBackupSettings">
                  <div class="space-y-6">
                    <div class="flex items-start">
                      <div class="flex items-center h-5">
                        <input
                          id="enable-auto-backup"
                          type="checkbox"
                          v-model="settings.backup.enableAutoBackup"
                          class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                        >
                      </div>
                      <div class="ml-3 text-sm">
                        <label for="enable-auto-backup" class="font-medium text-gray-700">启用自动备份</label>
                        <p class="text-gray-500">按照设定的时间间隔自动备份系统数据</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-6 gap-6">
                      <div class="col-span-6 sm:col-span-3">
                        <label for="backup-interval" class="block text-sm font-medium text-gray-700">备份间隔（小时）</label>
                        <input 
                          type="number" 
                          id="backup-interval" 
                          v-model.number="settings.backup.backupInterval"
                          min="1"
                          max="168"
                          class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          :disabled="!settings.backup.enableAutoBackup"
                        >
                      </div>

                      <div class="col-span-6 sm:col-span-3">
                        <label for="retention-days" class="block text-sm font-medium text-gray-700">保留天数</label>
                        <input 
                          type="number" 
                          id="retention-days" 
                          v-model.number="settings.backup.retentionDays"
                          min="1"
                          max="365"
                          class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          :disabled="!settings.backup.enableAutoBackup"
                        >
                      </div>
                    </div>

            <div>
                      <label for="backup-path" class="block text-sm font-medium text-gray-700">备份存储路径</label>
                      <div class="mt-1 flex rounded-md shadow-sm">
                <input
                          type="text" 
                          id="backup-path" 
                          v-model="settings.backup.backupPath"
                          class="flex-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full min-w-0 rounded-md sm:text-sm border-gray-300"
                          :disabled="!settings.backup.enableAutoBackup"
                        >
                      </div>
                      <p class="mt-1 text-sm text-gray-500">指定备份文件的存储位置</p>
                    </div>

                    <div class="space-y-4">
                      <div v-for="type in backupTypes" :key="type.id" class="flex items-start">
                        <div class="flex items-center h-5">
                          <input
                            :id="type.id"
                  type="checkbox"
                            v-model="settings.backup.types[type.id]"
                            class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                            :disabled="!settings.backup.enableAutoBackup"
                >
            </div>
                        <div class="ml-3 text-sm">
                          <label :for="type.id" class="font-medium text-gray-700">{{ type.name }}</label>
                          <p class="text-gray-500">{{ type.description }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 手动备份 -->
          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">手动备份</h3>
                <p class="mt-1 text-sm text-gray-500">
                  立即创建系统数据备份。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <div class="space-y-6">
                  <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
            <div>
                        <h4 class="text-sm font-medium text-gray-900">创建新备份</h4>
                        <p class="text-sm text-gray-500">备份将包含所有选中的数据类型</p>
                      </div>
                      <button
                        type="button"
                        @click="createManualBackup"
                        :disabled="isBackupInProgress"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                      >
                        <i class="fas fa-download mr-2"></i>
                        {{ isBackupInProgress ? '备份进行中...' : '立即备份' }}
                      </button>
                    </div>
                    <div v-if="backupProgress" class="mt-4">
                      <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-700">备份进度</span>
                        <span class="text-sm font-medium text-gray-700">{{ backupProgress }}%</span>
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-indigo-600 h-2 rounded-full" :style="{ width: backupProgress + '%' }"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 备份历史记录 -->
          <div class="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">备份历史</h3>
                <p class="mt-1 text-sm text-gray-500">
                  查看和管理历史备份记录。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <div class="flex flex-col">
                  <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                      <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                          <thead class="bg-gray-50">
                            <tr>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备份时间</th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">大小</th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                              <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">操作</span>
                              </th>
                            </tr>
                          </thead>
                          <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="backup in backupHistory" :key="backup.id">
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ formatDate(backup.createdAt) }}
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ backup.type }}
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ formatSize(backup.size) }}
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <span :class="[
                                  'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                  backup.status === 'success' ? 'bg-green-100 text-green-800' : 
                                  backup.status === 'failed' ? 'bg-red-100 text-red-800' : 
                                  'bg-yellow-100 text-yellow-800'
                                ]">
                                  {{ backup.status === 'success' ? '成功' : 
                                     backup.status === 'failed' ? '失败' : '进行中' }}
                                </span>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button
                                  @click="downloadBackup(backup)"
                                  class="text-indigo-600 hover:text-indigo-900 mr-4"
                                  :disabled="backup.status !== 'success'"
                                >下载</button>
                                <button
                                  @click="deleteBackup(backup)"
                                  class="text-red-600 hover:text-red-900"
                                  :disabled="backup.status === 'in_progress'"
                                >删除</button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div class="flex justify-end mt-8">
            <button 
              type="button"
              @click="saveBackupSettings"
              class="inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="save" size="sm" className="mr-2 text-white" />
              保存备份设置
            </button>
          </div>
        </div>
        <div v-else-if="activeTab === 'logs'" class="mt-6">
          <!-- 日志设置 -->
          <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">日志设置</h3>
                <p class="mt-1 text-sm text-gray-500">
                  配置系统日志的存储和清理策略。
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <form @submit.prevent="saveLogSettings">
                  <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                      <label for="log-retention" class="block text-sm font-medium text-gray-700">日志保留天数</label>
                      <input 
                        type="number" 
                        id="log-retention" 
                        v-model.number="logSettings.retention"
                        min="1"
                        max="365"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="log-level" class="block text-sm font-medium text-gray-700">默认日志级别</label>
              <select
                        id="log-level" 
                        v-model="logSettings.level"
                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                        <option v-for="level in logLevels" :key="level.value" :value="level.value">
                          {{ level.label }}
                        </option>
              </select>
            </div>

                    <div class="col-span-6 sm:col-span-3">
                      <label for="log-max-size" class="block text-sm font-medium text-gray-700">单个日志文件最大大小(MB)</label>
              <input
                type="number"
                        id="log-max-size" 
                        v-model.number="logSettings.maxSize"
                min="1"
                        max="1000"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      >
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 日志查看器 -->
          <div class="mt-6 bg-white shadow sm:rounded-lg">
            <!-- 筛选器 -->
            <div class="px-4 py-5 sm:p-6">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div class="sm:col-span-2">
                  <label for="start-date" class="block text-sm font-medium text-gray-700">开始日期</label>
                  <input 
                    type="date" 
                    id="start-date" 
                    v-model="logFilters.startDate"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
            </div>

                <div class="sm:col-span-2">
                  <label for="end-date" class="block text-sm font-medium text-gray-700">结束日期</label>
                  <input 
                    type="date" 
                    id="end-date" 
                    v-model="logFilters.endDate"
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                </div>

                <div class="sm:col-span-2">
                  <label for="filter-level" class="block text-sm font-medium text-gray-700">日志级别</label>
                  <select 
                    id="filter-level" 
                    v-model="logFilters.level"
                    class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">全部</option>
                    <option v-for="level in logLevels" :key="level.value" :value="level.value">
                      {{ level.label }}
                    </option>
                  </select>
                </div>

                <div class="sm:col-span-2">
                  <label for="filter-module" class="block text-sm font-medium text-gray-700">模块</label>
                  <select 
                    id="filter-module" 
                    v-model="logFilters.module"
                    class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">全部</option>
                    <option v-for="module in logModules" :key="module.value" :value="module.value">
                      {{ module.label }}
                    </option>
                  </select>
                </div>

                <div class="sm:col-span-2">
                  <label for="keyword" class="block text-sm font-medium text-gray-700">关键词</label>
                  <input 
                    type="text" 
                    id="keyword" 
                    v-model="logFilters.keyword"
                    placeholder="搜索日志内容..."
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                </div>

                <div class="sm:col-span-2 flex items-end space-x-4">
            <button
                    type="button"
                    @click="handleFilterChange"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <i class="fas fa-search mr-2"></i>
                    搜索
                  </button>
                  <button
                    type="button"
                    @click="clearFilters"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <i class="fas fa-times mr-2"></i>
                    清除
                  </button>
                  <button
                    type="button"
                    @click="exportLogs"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <i class="fas fa-download mr-2"></i>
                    导出
            </button>
          </div>
        </div>
      </div>

            <!-- 日志列表 -->
            <div class="flex flex-col">
              <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                  <div class="overflow-hidden border-t border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模块</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">消息</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                          <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">操作</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-if="isLoading" class="animate-pulse">
                          <td colspan="6" class="px-6 py-4">
                            <div class="flex items-center justify-center">
                              <i class="fas fa-circle-notch fa-spin mr-2"></i>
                              加载中...
                            </div>
                          </td>
                        </tr>
                        <tr v-else-if="logs.length === 0">
                          <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            没有找到符合条件的日志记录
                          </td>
                        </tr>
                        <tr v-else v-for="log in logs" :key="log.id" class="hover:bg-gray-50">
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ formatDate(log.timestamp) }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span :class="[
                              'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                              `bg-${getLevelColor(log.level)}-100 text-${getLevelColor(log.level)}-800`
                            ]">
                              {{ logLevels.find(l => l.value === log.level)?.label }}
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ logModules.find(m => m.value === log.module)?.label }}
                          </td>
                          <td class="px-6 py-4 text-sm text-gray-900">
                            {{ log.message }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ log.user }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <button
                              type="button"
                              @click="log.showDetails = !log.showDetails"
                              class="text-indigo-600 hover:text-indigo-900"
        >
                              {{ log.showDetails ? '收起' : '详情' }}
        </button>
                          </td>
                        </tr>
                        <!-- 日志详情展开行 -->
                        <tr v-for="log in logs" :key="log.id + '-details'" v-show="log.showDetails">
                          <td colspan="6" class="px-6 py-4 bg-gray-50">
                            <pre class="text-sm text-gray-900 whitespace-pre-wrap">{{ log.details }}</pre>
                          </td>
                        </tr>
                      </tbody>
                    </table>
      </div>
    </div>
  </div>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div class="flex-1 flex justify-between sm:hidden">
                <button
                  @click="handlePageChange(currentPage - 1)"
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  上一页
                </button>
                <button
                  @click="handlePageChange(currentPage + 1)"
                  :disabled="currentPage * pageSize >= totalLogs"
                  class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  下一页
                </button>
              </div>
              <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p class="text-sm text-gray-700">
                    显示第
                    <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span>
                    至
                    <span class="font-medium">{{ Math.min(currentPage * pageSize, totalLogs) }}</span>
                    条，共
                    <span class="font-medium">{{ totalLogs }}</span>
                    条记录
                  </p>
                </div>
                <div>
                  <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      @click="handlePageChange(currentPage - 1)"
                      :disabled="currentPage === 1"
                      class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                    >
                      <span class="sr-only">上一页</span>
                      <i class="fas fa-chevron-left"></i>
                    </button>
                    <!-- 页码按钮 -->
                    <button
                      v-for="page in Math.ceil(totalLogs / pageSize)"
                      :key="page"
                      @click="handlePageChange(page)"
                      :class="[
                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                        page === currentPage
                          ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      ]"
                    >
                      {{ page }}
                    </button>
                    <button
                      @click="handlePageChange(currentPage + 1)"
                      :disabled="currentPage * pageSize >= totalLogs"
                      class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                    >
                      <span class="sr-only">下一页</span>
                      <i class="fas fa-chevron-right"></i>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div class="flex justify-end mt-8">
            <button 
              type="button"
              @click="saveLogSettings"
              class="inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Icon name="save" size="sm" className="mr-2 text-white" />
              保存日志设置
            </button>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import Icon from '@/components/common/Icon.vue'
import { useAdminStore } from '@/stores/admin'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const adminStore = useAdminStore()
const authStore = useAuthStore()

const activeTab = ref('basic')
const tabs = ref([
  { id: 'basic', name: '基本设置', icon: 'settings' },
  { id: 'security', name: '安全设置', icon: 'security' },
  { id: 'notification', name: '通知设置', icon: 'notifications' },
  { id: 'backup', name: '备份设置', icon: 'backup' },
  { id: 'logs', name: '日志设置', icon: 'article' }
])

const settings = ref({
  basic: {
    siteName: '智慧课堂学习系统',
    siteUrl: 'https://smartclass.example.com',
    description: '智慧课堂是一个集教学、学习、管理于一体的综合性教育平台，旨在为师生提供便捷、高效的数字化学习体验。',
    adminEmail: '<EMAIL>',
    language: 'zh-CN'
  },
  features: {
    aiAssistant: true,
    forum: true,
    pointsMall: true,
    dataAnalytics: true
  },
  services: {
    wechatLogin: true,
    cloudStorage: false
  },
  custom: {
    pageSize: 20,
    dateFormat: 'YYYY-MM-DD',
    cacheTime: 30,
    sessionTimeout: 120
  },
  security: {
    minPasswordLength: 8,
    passwordExpiryDays: 90,
    requireUppercase: true,
    requireNumbers: true,
    requireSymbols: true,
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    enable2FA: false,
    forcePasswordChange: true,
    ipWhitelist: '',
    ipBlacklist: ''
  },
  notification: {
    enableSystemNotifications: true,
    enableBrowserNotifications: false,
    displayDuration: 5,
    enableEmailNotifications: false,
    smtpHost: 'smtp.example.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    senderEmail: '<EMAIL>',
    events: {
      userRegistration: true,
      passwordReset: true,
      courseCreation: true,
      assignmentSubmission: false,
      systemMaintenance: true,
      securityAlert: true,
      gradeUpdate: false,
      forumReply: false
    }
  },
  backup: {
    enableAutoBackup: false,
    backupInterval: 24,
    retentionDays: 30,
    backupPath: '/data/backups',
    types: {
      database: true,
      files: true,
      settings: true,
      logs: false
    }
  }
})

const features = ref([
  { id: 'aiAssistant', name: 'AI助手', description: '启用智能AI助手为学生提供学习辅导' },
  { id: 'forum', name: '讨论论坛', description: '允许师生在课程内创建和参与讨论' },
  { id: 'pointsMall', name: '积分商城', description: '允许学生使用积分兑换虚拟物品或特权' },
  { id: 'dataAnalytics', name: '数据分析', description: '启用学习行为和成绩分析功能' }
])

const services = ref([
  { id: 'wechatLogin', name: '微信登录', description: '允许用户使用微信账号登录系统' },
  { id: 'cloudStorage', name: '云存储服务', description: '使用第三方云存储服务存储课程资料和作业' }
])

const pageSizes = ref([10, 20, 50, 100])
const dateFormats = ref([
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' }
])

// 通知事件列表
const notificationEvents = ref([
  {
    id: 'userRegistration',
    name: '用户注册',
    description: '新用户注册成功时发送通知',
    priority: 'medium'
  },
  {
    id: 'passwordReset',
    name: '密码重置',
    description: '用户申请密码重置时发送通知',
    priority: 'high'
  },
  {
    id: 'courseCreation',
    name: '课程创建',
    description: '新课程创建完成时发送通知',
    priority: 'medium'
  },
  {
    id: 'assignmentSubmission',
    name: '作业提交',
    description: '学生提交作业时发送通知',
    priority: 'low'
  },
  {
    id: 'systemMaintenance',
    name: '系统维护',
    description: '系统计划维护时发送通知',
    priority: 'high'
  },
  {
    id: 'securityAlert',
    name: '安全警报',
    description: '检测到安全风险时发送通知',
    priority: 'high'
  },
  {
    id: 'gradeUpdate',
    name: '成绩更新',
    description: '教师更新成绩时发送通知',
    priority: 'medium'
  },
  {
    id: 'forumReply',
    name: '论坛回复',
    description: '用户的帖子收到回复时发送通知',
    priority: 'low'
  }
])

const backupTypes = ref([
  { id: 'database', name: '数据库', description: '包含系统所有数据库内容' },
  { id: 'files', name: '文件', description: '包含上传的文件和资源' },
  { id: 'settings', name: '系统设置', description: '包含所有系统配置和设置' },
  { id: 'logs', name: '系统日志', description: '包含系统操作和错误日志' }
])

const isBackupInProgress = ref(false)
const backupProgress = ref(0)
const backupHistory = ref([
  {
    id: 1,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    type: '自动备份',
    size: 1024 * 1024 * 50, // 50MB
    status: 'success'
  },
  {
    id: 2,
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
    type: '手动备份',
    size: 1024 * 1024 * 75, // 75MB
    status: 'success'
  },
  {
    id: 3,
    createdAt: new Date(),
    type: '自动备份',
    size: 1024 * 1024 * 60, // 60MB
    status: 'in_progress'
  }
])

// 日志管理相关的响应式数据
const logSettings = ref({
  retention: 30, // 日志保留天数
  level: 'info', // 默认日志级别
  maxSize: 100, // 单个日志文件最大大小(MB)
})

const logLevels = ref([
  { value: 'debug', label: '调试', color: 'gray' },
  { value: 'info', label: '信息', color: 'blue' },
  { value: 'warn', label: '警告', color: 'yellow' },
  { value: 'error', label: '错误', color: 'red' }
])

const logFilters = ref({
  startDate: '',
  endDate: '',
  level: '',
  keyword: '',
  module: ''
})

const logModules = ref([
  { value: 'system', label: '系统' },
  { value: 'auth', label: '认证' },
  { value: 'user', label: '用户' },
  { value: 'course', label: '课程' },
  { value: 'assignment', label: '作业' }
])

const logs = ref([])
const totalLogs = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const isLoading = ref(false)

// 权限检查
if (!authStore.isLoggedIn) {
  console.log('Unauthorized access, redirecting to login')
  router.push('/auth/login')
}

async function saveBasicSettings() {
  try {
    await adminStore.dispatch('settings/saveBasicSettings', settings.value.basic)
    adminStore.$message.success('基本设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

async function saveFeaturesSettings() {
  try {
    await adminStore.dispatch('settings/saveFeaturesSettings', {
      features: settings.value.features,
      services: settings.value.services
    })
    adminStore.$message.success('功能设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

async function saveCustomSettings() {
  try {
    await adminStore.dispatch('settings/saveCustomSettings', settings.value.custom)
    adminStore.$message.success('自定义设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

async function saveSecuritySettings() {
  try {
    await adminStore.dispatch('settings/saveSecuritySettings', settings.value.security)
    adminStore.$message.success('安全设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

async function saveNotificationSettings() {
  try {
    await adminStore.dispatch('settings/saveNotificationSettings', settings.value.notification)
    adminStore.$message.success('通知设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

async function testEmailSettings() {
  try {
    // Implementation of testEmailSettings function
  } catch (error) {
    console.error('测试邮件发送失败：', error)
    adminStore.$message.error('测试邮件发送失败')
  }
}

async function saveBackupSettings() {
  try {
    await adminStore.dispatch('settings/saveBackupSettings', settings.value.backup)
    adminStore.$message.success('备份设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

async function createManualBackup() {
  try {
    isBackupInProgress.value = true
    backupProgress.value = 0
    
    // 模拟备份进度
    const interval = setInterval(() => {
      if (backupProgress.value < 100) {
        backupProgress.value += 10
      } else {
        clearInterval(interval)
        isBackupInProgress.value = false
        // 添加新的备份记录
        backupHistory.value.unshift({
          id: Date.now(),
          createdAt: new Date(),
          type: '手动备份',
          size: 1024 * 1024 * Math.floor(Math.random() * 100), // 随机大小
          status: 'success'
        })
        adminStore.$message.success('备份创建成功')
      }
    }, 500)
  } catch (error) {
    isBackupInProgress.value = false
    adminStore.$message.error('备份失败：' + error.message)
  }
}

function formatDate(date) {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatSize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`
}

async function downloadBackup(backup) {
  try {
    // 实现备份下载逻辑
    console.log('Downloading backup:', backup.id)
    adminStore.$message.success('开始下载备份文件')
  } catch (error) {
    adminStore.$message.error('下载失败：' + error.message)
  }
}

async function deleteBackup(backup) {
  try {
    // 实现备份删除逻辑
    backupHistory.value = backupHistory.value.filter(b => b.id !== backup.id)
    adminStore.$message.success('备份删除成功')
  } catch (error) {
    adminStore.$message.error('删除失败：' + error.message)
  }
}

async function saveAllSettings() {
  try {
    await Promise.all([
      saveBasicSettings(),
      saveFeaturesSettings(),
      saveCustomSettings(),
      saveSecuritySettings(),
      saveNotificationSettings(),
      saveBackupSettings()
    ])
    adminStore.$message.success('所有设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

// 日志管理相关的方法
async function saveLogSettings() {
  try {
    await adminStore.dispatch('settings/saveLogSettings', logSettings.value)
    adminStore.$message.success('日志设置保存成功')
  } catch (error) {
    adminStore.$message.error('保存失败：' + error.message)
  }
}

async function fetchLogs() {
  try {
    isLoading.value = true
    // 模拟从后端获取日志数据
    const response = await new Promise(resolve => setTimeout(() => {
      resolve({
        logs: Array.from({ length: pageSize.value }, (_, index) => ({
          id: Date.now() + index,
          timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
          level: logLevels.value[Math.floor(Math.random() * logLevels.value.length)].value,
          module: logModules.value[Math.floor(Math.random() * logModules.value.length)].value,
          message: '这是一条示例日志消息',
          details: '详细的日志信息...',
          user: '系统管理员'
        })),
        total: 156
      })
    }, 500))
    
    logs.value = response.logs
    totalLogs.value = response.total
  } catch (error) {
    adminStore.$message.error('获取日志失败：' + error.message)
  } finally {
    isLoading.value = false
  }
}

function handlePageChange(page) {
  currentPage.value = page
  fetchLogs()
}

function handleFilterChange() {
  currentPage.value = 1
  fetchLogs()
}

function clearFilters() {
  logFilters.value = {
    startDate: '',
    endDate: '',
    level: '',
    keyword: '',
    module: ''
  }
  handleFilterChange()
}

async function exportLogs() {
  try {
    adminStore.$message.success('开始导出日志...')
    // 实现日志导出逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    adminStore.$message.success('日志导出成功')
  } catch (error) {
    adminStore.$message.error('导出失败：' + error.message)
  }
}

function getLevelColor(level) {
  const levelInfo = logLevels.value.find(l => l.value === level)
  return levelInfo ? levelInfo.color : 'gray'
}

// 在组件挂载时获取日志数据
onMounted(() => {
  if (activeTab.value === 'logs') {
    fetchLogs()
  }
})

// 监听标签页切换
watch(activeTab, (newTab) => {
  if (newTab === 'logs') {
    fetchLogs()
  }
})
</script> 

<style scoped>
/* Add your component styles here */
</style> 