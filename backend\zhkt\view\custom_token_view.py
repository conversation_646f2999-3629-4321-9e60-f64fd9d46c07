from rest_framework_simplejwt.views import TokenObtainPairView
from zhkt.serializers import UserSerializer
from zhkt.entitys import User 

# 自定义JWT视图，添加额外的用户信息
class CustomTokenObtainPairView(TokenObtainPairView):
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == 200:
            user = User.objects.get(username=request.data.get('username'))
            serializer = UserSerializer(user)
            response.data['user'] = serializer.data
        return response