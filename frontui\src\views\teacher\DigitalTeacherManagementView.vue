<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="我的数字人"
    activePage="digital-teacher"
    activeSubPage="my-digital-teacher"
  >
    <div class="space-y-6">
      <!-- 功能区 -->
      <div class="flex flex-wrap justify-between items-center gap-4">
        <div class="flex items-center gap-4">
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg flex items-center gap-2 shadow-sm hover:shadow-md transition-all"
            @click="openCreateDigitalTeacherModal"
          >
            <span class="material-icons">add</span>
            创建数字人
          </button>
        </div>
        <div class="flex items-center gap-3">
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery"
              @input="searchDigitalHumans"
              placeholder="搜索数字人..." 
              class="border-gray-300 rounded-lg pl-10 pr-4 py-2 w-64 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative dropdown">
            <button class="flex items-center gap-2 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100 transition">
              <span class="material-icons text-gray-600">sort</span>
              <span>排序</span>
            </button>
            <div class="dropdown-menu absolute right-0 mt-2 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden z-10">
              <div class="py-1">
                <a href="#" @click.prevent="setSortOption('newest')" class="block px-4 py-2 text-sm" :class="sortOption === 'newest' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'">最新创建</a>
                <a href="#" @click.prevent="setSortOption('oldest')" class="block px-4 py-2 text-sm" :class="sortOption === 'oldest' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'">最早创建</a>
                <a href="#" @click.prevent="setSortOption('usage')" class="block px-4 py-2 text-sm" :class="sortOption === 'usage' ? 'bg-gray-100 text-gray-900' : 'text-gray-700'">使用频率</a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数字人模型列表 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-6">
          <!-- 我的数字人区域 -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold text-gray-900 flex items-center mb-4">
              <i class="material-icons mr-3 text-blue-600">person</i>
              我的数字人
            </h3>
            
            <!-- 加载中状态 -->
            <div v-if="loading" class="py-12 text-center text-gray-500">
              <div class="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
                <span class="sr-only">加载中...</span>
              </div>
              <p class="mt-4 text-lg">正在加载数字人列表...</p>
            </div>
            
            <div v-else-if="digitalTeachers.length === 0" class="py-12 text-center text-gray-500 border-2 border-dashed rounded-lg">
              <div class="material-icons text-6xl mb-3 text-gray-400">person_search</div>
              <p class="text-lg font-medium text-gray-700">您还没有创建数字人</p>
              <p class="text-sm text-gray-500 mt-1">点击下方按钮开始创建您的第一个数字人吧！</p>
              <button 
                class="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg flex items-center gap-2 shadow-sm hover:shadow-md transition-all mx-auto"
                @click="openCreateDigitalTeacherModal"
              >
                <span class="material-icons">add</span>
                创建数字人
              </button>
            </div>

            <div v-else class="grid gap-6 responsive-grid-layout">
              <div 
                v-for="digitalTeacher in sortedDigitalTeachers" 
                :key="digitalTeacher.id" 
                class="group relative flex flex-col rounded-xl border bg-white shadow-sm transition-all hover:shadow-xl overflow-hidden"
              >
                <!-- Image -->
                <div class="relative overflow-hidden">
                  <img 
                    :src="digitalTeacher.avatar_url || digitalTeacher.avatar" 
                    alt="数字人形象" 
                    class="h-64 w-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div class="absolute top-2 right-2">
                    <div 
                      class="px-2 py-1 rounded-full text-xs font-semibold text-white"
                      :class="getStatusClass(digitalTeacher.status)"
                    >
                      {{ getStatusText(digitalTeacher.status) }}
                    </div>
                  </div>
                </div>
                
                <!-- Content -->
                <div class="flex flex-1 flex-col p-4">
                  <div class="flex-1">
                    <h4 class="text-base font-semibold text-gray-800 truncate">{{ digitalTeacher.name }}</h4>
                    <div class="mt-2 space-y-1.5 text-sm text-gray-500">
                      <span class="flex items-center">
                        <i class="material-icons text-sm mr-1.5">calendar_today</i>
                        创建于 {{ digitalTeacher.createdAt }}
                      </span>
                      <span class="flex items-center">
                        <i class="material-icons text-sm mr-1.5">visibility</i>
                        {{ digitalTeacher.usageCount || 0 }} 次使用
                      </span>
                    </div>
                  </div>
                  
                  <!-- Actions -->
                  <div class="mt-4 flex items-center justify-between">
                    <button class="text-sm font-medium text-blue-600 hover:text-blue-800 flex items-center gap-1" @click="configDigitalTeacher(digitalTeacher.id)">
                      <i class="material-icons text-base">settings</i>
                      <span>配置模型</span>
                    </button>
                    <div class="dropdown relative">
                      <button class="p-1 text-gray-500 hover:bg-gray-200 rounded-full">
                        <i class="material-icons text-base">more_vert</i>
                      </button>
                      <div class="dropdown-menu absolute right-0 bottom-full mb-2 w-28 origin-bottom-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden">
                        <div class="py-1">
                          <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center" @click="editDigitalTeacher(digitalTeacher)">
                            <i class="material-icons text-sm mr-2">edit</i> 编辑
                          </button>
                          <button class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center" @click="confirmDeleteDigitalTeacher(digitalTeacher)">
                            <i class="material-icons text-sm mr-2">delete</i> 删除
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 系统预设区域 -->
          <!-- <div class="mt-12">
            <h3 class="text-xl font-semibold text-gray-900 flex items-center mb-4">
              <i class="material-icons mr-3 text-purple-600">auto_awesome</i>
              系统预设
            </h3>
            
            <div v-if="systemPresetsLoading" class="py-8 text-center text-gray-500">
              <div class="animate-spin inline-block w-6 h-6 border-2 border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
                <span class="sr-only">加载中...</span>
              </div>
              <span class="ml-2">正在加载系统预设...</span>
            </div>
            
            <div v-else class="grid gap-6 responsive-grid-layout">
              <div 
                v-for="preset in systemPresets" 
                :key="preset.id" 
                class="group relative flex flex-col rounded-xl border bg-white shadow-sm transition-all hover:shadow-xl overflow-hidden cursor-pointer"
                @click="useSystemPreset(preset.id)"
              >
                
                <div class="relative overflow-hidden">
                  <img 
                    :src="preset.avatar" 
                    alt="数字人形象" 
                    class="h-64 w-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div class="absolute top-2 right-2">
                    <div class="px-2 py-1 rounded-full text-xs font-semibold text-white bg-blue-500">
                      系统预设
                    </div>
                  </div>
                </div>
                
                
                <div class="flex flex-1 flex-col p-4">
                  <div class="flex-1">
                    <h4 class="text-base font-semibold text-gray-800 truncate">{{ preset.name }}</h4>
                    <p class="text-sm text-gray-500 mt-1 line-clamp-2">{{ preset.description }}</p>
                  </div>
                  <div class="mt-4">
                     <span class="flex items-center text-sm text-gray-500">
                        <i class="material-icons text-sm mr-1.5">category</i>
                        {{ preset.category }}
                      </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        -->
        </div>
        
        <!-- 分页 -->
        <div class="p-4 border-t flex justify-center">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </div>
    </div>

    <!-- 创建数字人弹窗 -->
    <div v-if="showCreateDigitalTeacherModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">创建新数字人</h3>
        
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">数字人名称</label>
          <input 
            type="text" 
            v-model="newDigitalTeacher.name" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="为您的数字人命名，例如'AI助教'"
          />
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">数字人描述</label>
          <textarea 
            v-model="newDigitalTeacher.description" 
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="描述数字人的特点和用途"
          ></textarea>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">上传训练视频（可选）</label>
          <div 
            class="border-2 border-dashed border-gray-300 rounded-lg p-4 transition-colors duration-200"
            :class="{'border-blue-400 bg-blue-50': isDragging}"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleDrop"
          >
            <div class="flex flex-col items-center justify-center">
              <span class="material-icons text-4xl text-gray-400">video_library</span>
              <p class="text-sm text-gray-600 mt-2">拖拽视频文件到此处或点击上传</p>
              <p class="text-xs text-gray-500 mt-1">仅支持MP4格式, 最大300MB</p>
              <label class="mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md cursor-pointer">
                <span>选择视频文件</span>
                <input type="file" class="hidden" accept="video/mp4" @change="handleVideoUpload" />
              </label>
              <div v-if="videoFile" class="mt-3 text-sm text-green-600 flex items-center">
                <span class="material-icons text-sm mr-1">check_circle</span>
                已选择: {{ videoFile.name }}
              </div>
            </div>
          </div>
          <div class="mt-2 text-sm text-blue-600">
            <span class="material-icons text-sm align-middle">info</span>
            系统将自动从您上传的视频中提取形象和语音特征，创建个性化数字人模型。如果上传视频，将自动从视频第3帧截取头像。
          </div>
        </div>
        
        <div class="flex justify-end gap-3">
          <button 
            @click="closeCreateModal" 
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="creating"
          >
            取消
          </button>
          <button 
            @click="createDigitalTeacher" 
            :disabled="!newDigitalTeacher.name || creating"
            :class="{'bg-blue-600 hover:bg-blue-700': newDigitalTeacher.name && !creating, 'bg-blue-300 cursor-not-allowed': !newDigitalTeacher.name || creating}"
            class="text-white font-medium px-4 py-2 rounded-md flex items-center gap-2"
          >
            <span v-if="creating" class="animate-spin material-icons text-sm">hourglass_empty</span>
            {{ creating ? '创建中...' : '创建数字人' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
        <p class="text-gray-700 mb-4">
          您确定要删除数字人 <span class="font-medium">{{ digitalTeacherToDelete?.name }}</span> 吗？此操作不可恢复。
        </p>
        <div class="flex justify-end gap-3">
          <button 
            @click="showDeleteConfirm = false"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            :disabled="deleting"
          >
            取消
          </button>
          <button 
            @click="deleteDigitalTeacher"
            :disabled="deleting"
            :class="{'bg-red-600 hover:bg-red-700': !deleting, 'bg-red-300 cursor-not-allowed': deleting}"
            class="px-4 py-2 text-white rounded-md text-sm font-medium flex items-center gap-2"
          >
            <span v-if="deleting" class="animate-spin material-icons text-sm">hourglass_empty</span>
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>


  </TeacherLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElPagination } from 'element-plus'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import { digitalHumanApi } from '@/api/digitalHuman'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import digitalTeacher1 from '@/assets/images/avatars/digital-teacher1.png'

// Teacher Data
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 搜索和排序
const searchQuery = ref('')
const sortOption = ref('newest')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalCount.value / pageSize.value)
})

// 状态管理
const loading = ref(false)
const creating = ref(false)
const deleting = ref(false)
const isDragging = ref(false)

// 数据存储
const digitalTeachers = ref([])

// 创建数字人相关
const showCreateDigitalTeacherModal = ref(false)
const newDigitalTeacher = ref({
  name: '',
  description: ''
})
const videoFile = ref(null)

// 删除确认相关
const showDeleteConfirm = ref(false)
const digitalTeacherToDelete = ref(null)



// 状态文字映射
const statusTextMap = {
  'active': '已上线',
  'inactive': '未激活',
  'processing': '处理中'
}

// 获取状态文字
const getStatusText = (status) => {
  return statusTextMap[status] || status
}

// 获取状态样式
const getStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-green-500'
    case 'processing':
      return 'bg-yellow-500'
    case 'inactive':
      return 'bg-gray-500'
    default:
      return 'bg-gray-500'
  }
}

// 处理页码变化
const handleCurrentChange = (page) => {
  loadDigitalHumans(page)
}

// 处理每页显示数量变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadDigitalHumans(1)
}

// 加载数字人列表
const loadDigitalHumans = async (page = 1) => {
  try {
    loading.value = true
    const params = {
      page: page,
      page_size: pageSize.value
    }
    
    if (searchQuery.value.trim()) {
      params.search = searchQuery.value.trim()
    }
    
    const response = await digitalHumanApi.getDigitalHumans(params)
    
    console.log('数字人API响应:', response)
    
    if (response.code === 200) {
      digitalTeachers.value = response.data.digital_humans.map(item => ({
        ...item,
        // 兼容前端字段名
        avatar: item.avatar_url || digitalTeacher1,
        avatar_url: item.avatar_url || digitalTeacher1,
        usageCount: item.usageCount || item.usage_count || 0,
        createdAt: item.createdAt || (item.created_at ? new Date(item.created_at).toISOString().split('T')[0] : '')
      }))
      totalCount.value = response.data.total
      currentPage.value = page
      console.log('处理后的数字人数据:', digitalTeachers.value)
    } else {
      console.error('获取数字人列表失败:', response)
      ElMessage.error(response.message || '获取数字人列表失败')
    }
  } catch (error) {
    console.error('加载数字人列表失败:', error)
    ElMessage.error('加载数字人列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}



// 搜索数字人
const searchDigitalHumans = () => {
  currentPage.value = 1
  loadDigitalHumans(1)
}

// 验证视频文件
const validateVideoFile = (file) => {
  // 检查文件类型
  if (!file.type.includes('mp4') && !file.name.toLowerCase().endsWith('.mp4')) {
    ElMessage.error('仅支持MP4格式的视频文件')
    return false
  }
  
  // 检查文件大小 (300MB)
  if (file.size > 300 * 1024 * 1024) {
    ElMessage.error('视频文件大小不能超过300MB')
    return false
  }
  
  return true
}

// 处理视频上传
const handleVideoUpload = (event) => {
  const file = event.target.files[0]
  if (file && validateVideoFile(file)) {
    videoFile.value = file
  }
}

// 拖拽相关处理函数
const handleDragOver = (event) => {
  isDragging.value = true
}

const handleDragLeave = (event) => {
  // 只有当拖拽离开整个拖拽区域时才设置为false
  if (!event.currentTarget.contains(event.relatedTarget)) {
    isDragging.value = false
  }
}

const handleDrop = (event) => {
  isDragging.value = false
  const files = event.dataTransfer.files
  
  if (files.length > 0) {
    const file = files[0]
    if (validateVideoFile(file)) {
      videoFile.value = file
      ElMessage.success(`已选择视频文件: ${file.name}`)
    }
  }
}

// 打开创建数字人弹窗
const openCreateDigitalTeacherModal = () => {
  showCreateDigitalTeacherModal.value = true
  newDigitalTeacher.value = {
    name: '',
    description: ''
  }
  videoFile.value = null
}

// 关闭创建弹窗
const closeCreateModal = () => {
  showCreateDigitalTeacherModal.value = false
  newDigitalTeacher.value = {
    name: '',
    description: ''
  }
  videoFile.value = null
}

// 创建数字人
const createDigitalTeacher = async () => {
  if (!newDigitalTeacher.value.name.trim()) {
    ElMessage.error('请输入数字人名称')
    return
  }
  
  try {
    creating.value = true
    
    const payload = {
      name: newDigitalTeacher.value.name.trim(),
      description: newDigitalTeacher.value.description?.trim() || ''
    }
    
    // 添加文件
    if (videoFile.value) {
      payload.video = videoFile.value
    }
    
    const response = await digitalHumanApi.createDigitalHuman(payload)
    
    if (response.code === 200) {
      ElMessage.success('数字人创建成功')
      closeCreateModal()
      // 重新加载列表
      await loadDigitalHumans(currentPage.value)
    } else {
      ElMessage.error(response.message || '创建数字人失败')
    }
  } catch (error) {
    console.error('创建数字人失败:', error)
    ElMessage.error('创建数字人失败，请稍后重试')
  } finally {
    creating.value = false
  }
}

// 确认删除数字人
const confirmDeleteDigitalTeacher = (digitalTeacher) => {
  digitalTeacherToDelete.value = digitalTeacher
  showDeleteConfirm.value = true
}

// 删除数字人
const deleteDigitalTeacher = async () => {
  if (!digitalTeacherToDelete.value) return
  
  try {
    deleting.value = true
    
    const response = await digitalHumanApi.deleteDigitalHuman(digitalTeacherToDelete.value.id)
    
    if (response.code === 200) {
      ElMessage.success('删除数字人成功')
      showDeleteConfirm.value = false
      digitalTeacherToDelete.value = null
      // 重新加载列表
      await loadDigitalHumans(currentPage.value)
    } else {
      ElMessage.error(response.message || '删除数字人失败')
    }
  } catch (error) {
    console.error('删除数字人失败:', error)
    ElMessage.error('删除数字人失败，请稍后重试')
  } finally {
    deleting.value = false
  }
}

// 编辑数字人
const editDigitalTeacher = (digitalTeacher) => {
  console.log('编辑数字人:', digitalTeacher.name)
  ElMessage.info('编辑功能开发中...')
}

// 配置数字人
const configDigitalTeacher = (id) => {
  console.log('配置数字人ID:', id)
  ElMessage.info('配置功能开发中...')
}



const setSortOption = (option) => {
  sortOption.value = option
}

const sortedDigitalTeachers = computed(() => {
  const teachers = [...digitalTeachers.value]
  switch (sortOption.value) {
    case 'newest':
      return teachers.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    case 'oldest':
      return teachers.sort((a, b) => new Date(a.created_at) - new Date(b.created_at))
    case 'usage':
      return teachers.sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
    default:
      return teachers
  }
})

// 页面加载时初始化数据
onMounted(async () => {
  await loadDigitalHumans()
})
</script>

<style scoped>
.responsive-grid-layout {
  /* 每个卡片最小 200px，剩余空间自动平均分配 */
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dropdown menu */
.dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-menu {
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 分页组件样式 */
:deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: #fff;
  --el-pagination-text-color: #606266;
  --el-pagination-button-color: #303133;
  --el-pagination-button-disabled-color: #c0c4cc;
  --el-pagination-hover-color: var(--el-color-primary);
  --el-pagination-button-bg-color: #f4f6f8;
}

:deep(.el-pagination .el-pager li.is-active) {
  background-color: #3b82f6;
  color: white;
}
</style> 