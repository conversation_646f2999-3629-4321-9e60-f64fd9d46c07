<template>
  <div class="course-question-bank">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">题库管理</h1>
      <div class="flex gap-3">
        <button 
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
          type="button"
          @click="showAddQuestionDialog"
        >
          <span class="material-icons text-sm">add</span>
          创建题目
        </button>
        <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
          <span class="material-icons text-sm">file_upload</span>
          导入题目
        </button>
        <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
          <span class="material-icons text-sm">file_download</span>
          导出题目
        </button>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow">
      <!-- 搜索和筛选区域 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex gap-3 items-center">
          <div class="relative flex-1">
            <input 
              type="text" 
              v-model="searchQuery"
              @input="handleSearch"
              placeholder="搜索题目..." 
              class="w-full border border-gray-300 rounded-md pl-9 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              type="button"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
              <span class="material-icons text-gray-600">filter_list</span>
              筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <div class="space-y-3">
                <div class="font-medium flex items-center gap-1.5">
                  <span class="material-icons text-base text-gray-600">label</span>
                  题型
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="type in questionTypes" 
                    :key="type.value"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.types.includes(type.value) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('types', type.value)"
                  >
                    <span v-if="selectedFilters.types.includes(type.value)" class="material-icons text-xs">check_circle</span>
                    {{ type.label }}
                  </span>
                </div>
                <div class="font-medium mt-3 flex items-center gap-1.5">
                  <span class="material-icons text-base text-gray-600">speed</span>
                  难度
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="difficulty in difficultyLevels" 
                    :key="difficulty.value"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.difficulties.includes(difficulty.value) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('difficulties', difficulty.value)"
                  >
                    <span v-if="selectedFilters.difficulties.includes(difficulty.value)" class="material-icons text-xs">check_circle</span>
                    {{ difficulty.label }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 题目列表 -->
      <div class="p-4">
        <el-table :data="questions" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="title" label="题目">
            <template #default="{ row }">
              <div class="flex items-start gap-2">
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded mt-0.5">{{ row.question_type_display }}</span>
                <div>
                  <div class="text-gray-900">{{ row.title }}</div>
                  <div class="text-gray-500 text-sm mt-1">{{ row.content }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="难度" width="120">
            <template #default="{ row }">
              <span 
                class="px-2 py-1 text-xs font-medium rounded-md"
                :class="{
                  'bg-green-100 text-green-800': row.difficulty_display === '简单',
                  'bg-yellow-100 text-yellow-800': row.difficulty_display === '中等',
                  'bg-red-100 text-red-800': row.difficulty_display === '困难'
                }"
              >
                {{ row.difficulty_display }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <div class="flex items-center gap-2 whitespace-nowrap">
                <a 
                  @click="previewQuestion(row)"
                  class="text-blue-600 hover:text-blue-800 cursor-pointer"
                >预览</a>
                <a 
                  @click="editQuestion(row)"
                  class="text-green-600 hover:text-green-800 cursor-pointer"
                >编辑</a>
                <a 
                  @click="deleteQuestionHandler(row)"
                  class="text-red-600 hover:text-red-800 cursor-pointer"
                >删除</a>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-end mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 添加/编辑题目对话框 -->
    <el-dialog
      v-model="questionDialogVisible"
      :title="editingQuestion ? '编辑题目' : '添加题目'"
      width="800px"
    >
      <el-form :model="questionForm" label-width="80px">
        <el-form-item label="题目类型">
          <el-select v-model="questionForm.type" placeholder="请选择题目类型">
            <el-option
              v-for="type in questionTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="难度">
          <el-select v-model="questionForm.difficulty" placeholder="请选择难度">
            <el-option
              v-for="difficulty in difficultyLevels"
              :key="difficulty.value"
              :label="difficulty.label"
              :value="difficulty.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题目标题">
          <el-input v-model="questionForm.title" placeholder="请输入题目标题"></el-input>
        </el-form-item>
        <el-form-item label="题目描述">
          <el-input 
            v-model="questionForm.content" 
            type="textarea" 
            :rows="3"
            placeholder="请输入题目描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="分值">
          <el-input-number 
            v-model="questionForm.score" 
            :min="0" 
            :max="100"
            :step="0.5"
            :precision="1"
            placeholder="请输入题目分值"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="答案选项" v-if="questionForm.type === 'single_choice' || questionForm.type === 'multiple_choice'">
          <div class="space-y-2">
            <div v-for="(option, index) in questionForm.options" :key="index" class="flex items-start gap-2">
              <el-input v-model="option.content" placeholder="请输入选项内容" class="flex-1"></el-input>
              <el-checkbox v-model="option.is_correct" label="正确答案" class="mt-2"></el-checkbox>
              <a 
                @click="removeOption(index)"
                class="text-red-600 hover:text-red-800 cursor-pointer whitespace-nowrap mt-2"
              >删除</a>
            </div>
            <div class="mt-4">
              <a 
                @click="addOption"
                class="text-blue-600 hover:text-blue-800 cursor-pointer inline-flex items-center"
              >
                <span class="material-icons text-sm mr-1">add</span>
                添加选项
              </a>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="判断答案" v-else-if="questionForm.type === 'true_false'">
          <el-select v-model="questionForm.answer" placeholder="请选择答案">
            <el-option
              v-for="option in trueFalseOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参考答案" v-else>
          <el-input 
            v-model="questionForm.answer" 
            type="textarea" 
            :rows="3"
            placeholder="请输入参考答案"
          ></el-input>
        </el-form-item>
        <el-form-item label="解析">
          <el-input 
            v-model="questionForm.analysis" 
            type="textarea" 
            :rows="3"
            placeholder="请输入题目解析"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="questionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleQuestionSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预览题目对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="题目预览"
      width="600px"
    >
      <div v-if="previewingQuestion" class="space-y-4">
        <div class="flex items-center gap-2">
          <span class="bg-blue-100 text-blue-800 text-sm px-2 py-0.5 rounded">{{ previewingQuestion.question_type_display }}</span>
          <span 
            class="px-2 py-0.5 text-sm font-medium rounded"
            :class="{
              'bg-green-100 text-green-800': previewingQuestion.difficulty_display === '简单',
              'bg-yellow-100 text-yellow-800': previewingQuestion.difficulty_display === '中等',
              'bg-red-100 text-red-800': previewingQuestion.difficulty_display === '困难'
            }"
          >
            {{ previewingQuestion.difficulty_display }}
          </span>
        </div>
        <div class="text-lg font-medium">{{ previewingQuestion.title }}</div>
        <div class="text-gray-600">{{ previewingQuestion.content }}</div>
        <template v-if="previewingQuestion.question_type === 'single_choice' || previewingQuestion.question_type === 'multiple_choice'">
          <div class="space-y-2">
            <div v-for="(option, index) in previewingQuestion.options" :key="index" class="flex items-center gap-2">
              <span class="w-6 h-6 rounded-full border flex items-center justify-center">{{ String.fromCharCode(65 + index) }}</span>
              <span>{{ option.content }}</span>
              <span v-if="option.is_correct" class="text-green-600 material-icons text-sm">check_circle</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="mt-4">
            <div class="font-medium">参考答案：</div>
            <div class="text-gray-600 mt-1">{{ previewingQuestion.answer }}</div>
          </div>
        </template>
        <div class="mt-4">
          <div class="font-medium">解析：</div>
          <div class="text-gray-600 mt-1">{{ previewingQuestion.analysis }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getQuestions,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  batchCreateQuestions,
  batchDeleteQuestions,
  exportQuestions
} from '@/api/question'

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

const route = useRoute()
const courseId = route.params.courseId

// 搜索和筛选
const searchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  types: [],
  difficulties: []
})

// 处理搜索输入
const handleSearch = debounce(() => {
  currentPage.value = 1 // 重置页码
  fetchQuestions()
}, 500)

// 题目类型和难度级别
const questionTypes = [
  { value: 'single_choice', label: '单选题' },
  { value: 'multiple_choice', label: '多选题' },
  { value: 'true_false', label: '判断题' },
  { value: 'fill_blank', label: '填空题' },
  { value: 'short_answer', label: '简答题' }
]

// 判断题答案选项
const trueFalseOptions = [
  { value: '正确', label: '正确' },
  { value: '错误', label: '错误' }
]

const difficultyLevels = [
  { value: 'easy', label: '简单' },
  { value: 'medium', label: '中等' },
  { value: 'hard', label: '困难' }
]

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 加载状态
const loading = ref(false)

// 题目列表
const questions = ref([])

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 获取题目列表
const fetchQuestions = async () => {
  loading.value = true
  try {
    const params = {
      course_id: courseId,
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value,
      type: selectedFilters.value.types.join(','),
      difficulty: selectedFilters.value.difficulties.join(',')
    }
    const response = await getQuestions(params)
    questions.value = response.results
    total.value = response.count
  } catch (error) {
    ElMessage.error('获取题目列表失败')
  } finally {
    loading.value = false
  }
}

// 题目对话框
const questionDialogVisible = ref(false)
const editingQuestion = ref(null)
const questionForm = ref({
  type: '',
  difficulty: '',
  title: '',
  content: '',
  options: [],
  answer: '',
  analysis: '',
  score: 0
})

// 预览对话框
const previewDialogVisible = ref(false)
const previewingQuestion = ref(null)

// 显示添加题目对话框
const showAddQuestionDialog = () => {
  editingQuestion.value = null
  questionForm.value = {
    type: '',
    difficulty: '',
    title: '',
    content: '',
    options: [],
    answer: '',
    analysis: '',
    score: 0
  }
  questionDialogVisible.value = true
}

// 编辑题目
const editQuestion = (question) => {
  editingQuestion.value = question
  questionForm.value = {
    type: question.question_type,
    difficulty: question.difficulty,
    title: question.title,
    content: question.content,
    options: [...(question.options || [])],
    answer: question.answer,
    analysis: question.analysis,
    score: question.score || 0
  }
  questionDialogVisible.value = true
}

// 预览题目
const previewQuestion = (question) => {
  previewingQuestion.value = question
  previewDialogVisible.value = true
}

// 删除题目
const deleteQuestionHandler = async (question) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除题目 "${question.title}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    await deleteQuestion(question.id)
    ElMessage.success('删除成功')
    fetchQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除题目失败')
    }
  }
}

// 切换筛选条件
const toggleFilter = (type, value) => {
  const index = selectedFilters.value[type].indexOf(value)
  if (index === -1) {
    selectedFilters.value[type].push(value)
  } else {
    selectedFilters.value[type].splice(index, 1)
  }
  fetchQuestions()
}

// 添加选项
const addOption = () => {
  questionForm.value.options.push({
    content: '',
    is_correct: false,
    order: questionForm.value.options.length
  })
}

// 删除选项
const removeOption = (index) => {
  questionForm.value.options.splice(index, 1)
  // 更新剩余选项的顺序
  questionForm.value.options.forEach((option, idx) => {
    option.order = idx
  })
}

// 处理题目提交
const handleQuestionSubmit = async () => {
  if (!questionForm.value.type) {
    ElMessage.warning('请选择题目类型')
    return
  }
  if (!questionForm.value.difficulty) {
    ElMessage.warning('请选择题目难度')
    return
  }
  if (!questionForm.value.title.trim()) {
    ElMessage.warning('请输入题目标题')
    return
  }
  if (questionForm.value.score < 0) {
    ElMessage.warning('分值不能小于0')
    return
  }

  const questionData = {
    course: courseId,
    question_type: questionForm.value.type,
    difficulty: questionForm.value.difficulty,
    title: questionForm.value.title,
    content: questionForm.value.content,
    answer: questionForm.value.answer,
    analysis: questionForm.value.analysis,
    options: questionForm.value.options,
    score: questionForm.value.score
  }

  try {
    if (editingQuestion.value) {
      await updateQuestion(editingQuestion.value.id, questionData)
      ElMessage.success('题目更新成功')
    } else {
      await createQuestion(questionData)
      ElMessage.success('题目添加成功')
    }
    questionDialogVisible.value = false
    fetchQuestions()
  } catch (error) {
    ElMessage.error(editingQuestion.value ? '更新题目失败' : '添加题目失败')
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchQuestions()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchQuestions()
}

// 导出题目
const handleExport = async () => {
  try {
    const response = await exportQuestions({ course_id: courseId })
    // 这里可以处理导出的数据，例如下载为文件
    console.log('导出的题目数据：', response)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出题目失败')
  }
}

// 导入题目
const handleImport = () => {
  // TODO: 实现题目导入功能
  ElMessage.info('题目导入功能开发中')
}

// 初始化加载
onMounted(() => {
  fetchQuestions()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>