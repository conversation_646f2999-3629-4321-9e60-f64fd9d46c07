import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { usersApi } from '@/api/users'
import defaultTeacherAvatar from '@/assets/images/avatars/teacher1.png'

// 定义教师信息存储
export const useTeacherStore = defineStore('teacher', () => {
  // 教师基本数据
  const teacherData = reactive({
    id: '',
    teacherId: '',
    name: '',
    avatar: defaultTeacherAvatar,
    email: '',
    phone: '',
    department: '',
    title: '',
    joinDate: ''
  })

  // 加载状态
  const loading = ref(false)
  // 错误信息
  const error = ref('')

  // 获取当前登录教师信息
  const fetchCurrentTeacher = async () => {
    loading.value = true
    error.value = ''
    try {
      const response = await usersApi.getCurrentTeacherInfo()
      const { user, ...teacherInfo } = response
      // 更新教师信息
      Object.assign(teacherData, {
        id: teacherInfo.id,
        teacherId: teacherInfo.teacher_id,
        name: user.alias,
        //avatar: user.avatar || defaultTeacherAvatar,
        email: user.email,
        phone: user.phone,
        department: teacherInfo.college_name || '',
        title: teacherInfo.title,
        joinDate: user.date_joined
      })
    } catch (err) {
      error.value = '获取教师信息失败：' + (err.message || '未知错误')
      console.error('获取教师信息失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  return {
    teacherData,
    loading,
    error,
    fetchCurrentTeacher,
  }
}) 