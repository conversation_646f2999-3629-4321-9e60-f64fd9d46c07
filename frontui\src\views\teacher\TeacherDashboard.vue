<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="教师首页"
    activePage="dashboard"
  >
    <div>
      <div class="mb-6">
        <h2 class="text-2xl font-semibold text-gray-800">欢迎回来，{{ teacherData.name }}</h2>
        <p class="text-sm text-gray-600">{{ currentDate }}</p>
      </div>

      <!-- Reminder Card -->
      <div class="mb-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md">
        <div class="flex">
            <div class="flex-shrink-0">
                <!-- Note: Using El-Icon instead of Font Awesome -->
                <el-icon class="text-yellow-400"><Bell /></el-icon>
            </div>
            <div class="ml-3">
                <p class="text-sm text-yellow-700">
                    <!-- TODO: Replace with dynamic data -->
                    您有 <strong>{{ teacherStats.pendingAssignments }}</strong> 份作业需要批阅，<strong>0</strong> 个数字人课程需要更新
                </p>
            </div>
        </div>
      </div>
      
      <!-- Dashboard Summary Cards -->
      <div class="flex flex-wrap gap-6 mb-8">
        <!-- Students Card -->
        <div class="bg-white rounded-lg shadow p-6 flex-1 min-w-[250px]">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-700">学生总数</h2>
            <el-icon class="text-2xl text-blue-600"><User /></el-icon>
          </div>
          <div class="flex items-end">
            <span class="text-3xl font-bold text-gray-800">{{ teacherStats.totalStudents }}</span>
            <span class="ml-2 text-sm text-green-600">+{{ teacherStats.newStudentsThisWeek }} 本周</span>
          </div>
        </div>
        
        <!-- Courses Card -->
        <div class="bg-white rounded-lg shadow p-6 flex-1 min-w-[250px]">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-700">课程总数</h2>
            <el-icon class="text-2xl text-purple-600"><Notebook /></el-icon>
          </div>
          <div class="flex items-end">
            <span class="text-3xl font-bold text-gray-800">{{ teacherStats.totalCourses }}</span>
            <span class="ml-2 text-sm text-green-600">+{{ teacherStats.newCoursesThisMonth }} 本月</span>
          </div>
        </div>
        
        <!-- Assignments Card -->
        <div class="bg-white rounded-lg shadow p-6 flex-1 min-w-[250px]">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-700">待批改作业</h2>
            <el-icon class="text-2xl text-orange-600"><Document /></el-icon>
          </div>
          <div class="flex items-end">
            <span class="text-3xl font-bold text-gray-800">{{ teacherStats.pendingAssignments }}</span>
            <span class="ml-2 text-sm text-red-600">需要处理</span>
          </div>
        </div>
      </div>
      
      <!-- Recent Activity and Upcoming Classes -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">最近活动</h2>
            <el-button type="primary">查看全部</el-button>
          </div>
          <div v-if="recentActivities.length > 0" class="space-y-4">
            <div v-for="(activity, index) in recentActivities" :key="index" class="flex items-start pb-4 border-b border-gray-100 last:border-0">
              <div :class="`p-2 rounded-full ${activity.bgColor}`">
                <el-icon class="text-lg"><component :is="activity.icon" /></el-icon>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-800">{{ activity.description }}</p>
                <p class="text-xs text-gray-500">{{ activity.time }}</p>
              </div>
            </div>
          </div>
          <div v-else class="py-12 flex flex-col items-center justify-center text-gray-500">
            <el-icon class="text-4xl mb-2"><Calendar /></el-icon>
            <p>最近没有活动</p>
          </div>
        </div>
        
        <!-- Upcoming Classes -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-800">今日课程</h2>
            <el-button type="primary" @click="openSchedule">查看课表</el-button>
          </div>
          <div v-if="upcomingClasses.length > 0" class="space-y-4">
            <div v-for="(cls, index) in upcomingClasses" :key="index" class="flex p-3 rounded-lg border border-gray-100 hover:bg-gray-50">
              <div class="w-16 flex flex-col items-center justify-center border-r border-gray-100 pr-3">
                <p class="text-sm font-medium text-gray-600">{{ cls.startTime }}</p>
                <p class="text-xs text-gray-500">{{ cls.endTime }}</p>
              </div>
              <div class="ml-4 flex-1">
                <h3 class="text-md font-medium text-gray-800">{{ cls.courseName }}</h3>
                <p class="text-sm text-gray-600">{{ cls.location }} | {{ cls.studentCount }}名学生</p>
              </div>
              <div>
                <el-button type="primary" size="small" @click="startClass(cls)">开始上课</el-button>
              </div>
            </div>
          </div>
          <div v-else class="py-12 flex flex-col items-center justify-center text-gray-500">
            <el-icon class="text-4xl mb-2"><Calendar /></el-icon>
            <p>今天没有课程安排</p>
          </div>
        </div>
      </div>
      
      <!-- Teaching Resources and Quick Links -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-6">教学资源</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div v-for="(resource, index) in teachingResources" :key="index" 
              class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-200 transition-colors cursor-pointer"
              @click="navigateTo(resource.path)">
            <el-icon class="text-3xl text-blue-600 mb-3"><component :is="resource.icon" /></el-icon>
            <h3 class="text-md font-medium text-gray-800 text-center">{{ resource.title }}</h3>
            <p class="text-xs text-gray-500 text-center mt-1">{{ resource.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import {
  House, 
  User,
  Notebook,
  Document,
  Edit,
  DataLine,
  Service,
  Calendar,
  Folder,
  Connection,
  VideoCameraFilled,
  Reading,
  ChatDotRound,
  ArrowRight,
  Bell
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useTeacherStore } from '@/stores/teacher'
import { getTeacherStats } from '@/api/teacher'

// Router
const router = useRouter()

// 使用教师store
const teacherStore = useTeacherStore()

// 获取教师数据
const teacherData = computed(() => teacherStore.teacherData)

// Get current date and format it
const currentDate = computed(() => {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth() + 1;
  const day = today.getDate();
  const options = { weekday: 'long' };
  const weekday = new Intl.DateTimeFormat('zh-CN', options).format(today);
  return `今天是 ${year}年${month}月${day}日 ${weekday}`;
});

// Teacher Stats
const teacherStats = ref({
  totalStudents: 0,
  totalCourses: 0,
  pendingAssignments: 0,
  newStudentsThisWeek: 0,
  newCoursesThisMonth: 0
})

// Recent Activities
const recentActivities = ref([
  // { 
  //   description: '已完成批改《数据结构》课程的期中考试',
  //   time: '昨天 15:30',
  //   icon: 'Document',
  //   bgColor: 'bg-blue-100 text-blue-600'
  // },
  // { 
  //   description: '上传了新课件《算法设计与分析》第5章',
  //   time: '昨天 13:45',
  //   icon: 'Notebook',
  //   bgColor: 'bg-purple-100 text-purple-600'
  // },
  // { 
  //   description: '添加了新学生到《人工智能导论》课程',
  //   time: '2天前',
  //   icon: 'User',
  //   bgColor: 'bg-green-100 text-green-600'
  // },
  // { 
  //   description: '发布了《软件工程》课程的新作业',
  //   time: '3天前',
  //   icon: 'Edit',
  //   bgColor: 'bg-orange-100 text-orange-600'
  // }
])

// Upcoming Classes
const upcomingClasses = ref([
  // {
  //   courseName: '数据结构',
  //   startTime: '09:30',
  //   endTime: '11:10',
  //   location: '教学楼A区302',
  //   studentCount: 45
  // },
  // {
  //   courseName: '算法设计与分析',
  //   startTime: '14:00',
  //   endTime: '15:40',
  //   location: '教学楼B区201',
  //   studentCount: 38
  // }
])

// Teaching Resources
const teachingResources = ref([
  {
    title: '教学内容制作',
    description: '创建课件、讲稿和PPT',
    icon: 'Edit',
    path: '/teacher/lesson-plan-projects'
  },
  {
    title: '教学素材库',
    description: '查找和使用教学素材',
    icon: 'Folder',
    path: '/teacher/knowledge-base'
  },
  {
    title: '数字分身',
    description: '创建和管理您的数字形象',
    icon: 'Service',
    path: '/teacher/digital-teacher-management'
  },
  {
    title: '教学视频录制',
    description: '录制和编辑教学视频',
    icon: 'VideoCameraFilled',
    path: '/teacher/video-course'
  }
])

// Methods
const navigateTo = (path) => {
  router.push(path)
}

const openSchedule = () => {
  ElMessage.info('课表功能即将上线，敬请期待！')
}

const startClass = (cls) => {
  ElMessage.success(`正在准备《${cls.courseName}》的上课环境，即将进入教室...`)
  // Would navigate to virtual classroom in a real app
  // router.push(`/teacher/virtual-classroom/${courseId}`)
}

const loadTeacherStats = async () => {
  try {
    const response = await getTeacherStats()
    teacherStats.value = {
      totalStudents: response.total_students,
      totalCourses: response.total_courses,
      pendingAssignments: response.pending_assignments,
      newStudentsThisWeek: response.new_students_this_week,
      newCoursesThisMonth: response.new_courses_this_month
    }
  } catch (error) {
    console.error('Failed to load teacher stats:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 在组件挂载时获取教师信息和统计数据
onMounted(async () => {
  await teacherStore.fetchCurrentTeacher()
  await loadTeacherStats()
})
</script> 

<style scoped>
/* 覆盖 Element Plus 主要按钮的所有状态颜色 */
:deep(.el-button--primary) {
  --el-button-bg-color: #2563eb !important;
  --el-button-border-color: #2563eb !important;
  --el-button-hover-bg-color: #2563eb !important;
  --el-button-hover-border-color: #2563eb !important;
  --el-button-active-bg-color: #2563eb !important;
  --el-button-active-border-color: #2563eb !important;
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus) {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
  opacity: 0.9;
}

:deep(.el-button--primary:active) {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
  opacity: 0.8;
}
</style>
