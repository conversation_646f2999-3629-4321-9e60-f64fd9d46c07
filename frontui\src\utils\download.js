/**
 * 文件下载工具类
 */
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

export const downloadUtils = {
  /**
   * 下载文件
   * @param {string} url 下载地址
   * @param {string} defaultFilename 默认文件名
   * @param {Object} options 下载选项
   * @param {Function} options.onStart 下载开始回调
   * @param {Function} options.onSuccess 下载成功回调
   * @param {Function} options.onError 下载失败回调
   * @returns {Promise} 下载Promise
   */
  downloadFile(url, defaultFilename, options = {}) {
    const { onStart, onSuccess, onError } = options
    
    // 下载开始回调
    if (typeof onStart === 'function') {
      onStart()
    }
    
    // 获取认证token
    const authStore = useAuthStore()
    const token = authStore.token
    
    return fetch(url, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`)
      }
      
      // 从Content-Disposition中获取文件名，支持UTF-8编码
      let filename = defaultFilename
      const disposition = response.headers.get('Content-Disposition')
      if (disposition) {
        // 优先尝试UTF-8编码的文件名
        const utf8Match = disposition.match(/filename\*=UTF-8''([^;]+)/)
        if (utf8Match && utf8Match[1]) {
          try {
            filename = decodeURIComponent(utf8Match[1])
          } catch (e) {
            console.warn('解码UTF-8文件名失败:', e)
          }
        } else {
          // 回退到常规文件名
          const regularMatch = disposition.match(/filename="([^"]+)"/)
          if (regularMatch && regularMatch[1]) {
            filename = regularMatch[1]
          }
        }
      }
      
      // 获取blob数据
      return response.blob().then(blob => ({
        blob,
        filename
      }))
    })
    .then(({ blob, filename }) => {
      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      
      // 添加到DOM并触发下载
      document.body.appendChild(link)
      link.click()
      
      // 清理
      setTimeout(() => {
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      }, 100)
      
      if (typeof onSuccess === 'function') {
        onSuccess(filename)
      }
      
      return filename
    })
    .catch(error => {
      console.error('下载失败:', error)
      
      if (typeof onError === 'function') {
        onError(error)
      }
      
      throw error
    })
  },
  
  /**
   * 下载文件并显示Element UI消息
   * @param {string} url 下载地址
   * @param {string} defaultFilename 默认文件名
   * @param {Object} messages 消息配置
   */
  downloadWithMessage(url, defaultFilename, messages = {}) {
    const startMsg = messages.start || '开始下载文件...'
    const successMsg = messages.success || '下载完成'
    const errorMsg = messages.error || '下载失败'
    
    return this.downloadFile(url, defaultFilename, {
      onStart: () => ElMessage.info(startMsg),
      onSuccess: (filename) => ElMessage.success(successMsg),
      onError: (error) => ElMessage.error(`${errorMsg}: ${error.message}`)
    })
  }
}

export default downloadUtils 