<template>
  <StudentLayout 
    pageTitle="个人中心" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    
    <div class="space-y-4">
      <!-- 压缩版个人资料卡片 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-4">
          <div class="flex items-start">
            <!-- 头像 -->
            <el-avatar 
              :size="70" 
              :src="studentStore.studentData.avatar"
              class="border-2 border-white shadow-md mr-4" />
            
            <!-- 基本信息 -->
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h2 class="text-xl font-semibold text-gray-800">{{ studentStore.userFullName }}</h2>
              </div>
              
              <!-- 学生信息色块 -->
              <div class="flex flex-wrap gap-2 mb-2">
                <span class="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">学号：{{ studentStore.studentData.studentId }}</span>
                <span class="px-3 py-1.5 bg-green-100 text-green-800 rounded-md text-sm font-medium">{{ academicInfo.college }}</span>
                <span class="px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium">{{ academicInfo.major }}</span>
                <span class="px-3 py-1.5 bg-purple-100 text-purple-800 rounded-md text-sm font-medium">{{ academicInfo.className }}</span>
              </div>
            </div>
          </div>

          <!-- 数据分析指标 - 水平布局 -->
          <div class="flex mt-3 pt-3 border-t gap-3">
            <!-- 学习时长与排名 -->
            <div class="flex-1">
              <div class="bg-blue-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-blue-800 flex items-center mb-1">
                  <el-icon class="text-blue-600 mr-2"><Timer /></el-icon>
                  学习时长
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-blue-900 mb-1">
                    {{ studentAnalytics.totalLearningHours }}小时
                  </div>
                  <div class="text-sm text-blue-700 bg-blue-100 rounded-full px-3 py-0.5 inline-block">
                    排名: {{ studentAnalytics.classRank }}/{{ studentAnalytics.classTotal }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 课程完成情况 -->
            <div class="flex-1">
              <div class="bg-green-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-green-800 flex items-center mb-1">
                  <el-icon class="text-green-600 mr-2"><Notebook /></el-icon>
                  课程完成
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-green-900 mb-1">
                    {{ studentAnalytics.completedCourses }}/{{ studentAnalytics.totalCourses }}
                  </div>
                  <div class="text-sm text-green-700 bg-green-100 rounded-full px-3 py-0.5 inline-block">
                    证书: {{ studentAnalytics.certificatesEarned }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 积分与成就 -->
            <div class="flex-1">
              <div class="bg-purple-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-purple-800 flex items-center mb-1">
                  <el-icon class="text-purple-600 mr-2"><Medal /></el-icon>
                  积分与成就
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-purple-900 mb-1">
                    {{ studentStore.studentData.points.toLocaleString() }}
                  </div>
                  <div class="text-sm text-purple-700 bg-purple-100 rounded-full px-3 py-0.5 inline-block">
                    连续: {{ studentAnalytics.continuousLearningDays }}天
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人信息管理选项卡 -->
      <el-card shadow="hover">
        <el-tabs v-model="activeTab" tab-position="left" style="min-height: 500px">
          <!-- 基本信息管理 -->
          <el-tab-pane label="基本信息" name="basic">
            <h3 class="text-xl font-medium text-gray-800 mb-4">基本信息</h3>
            <el-form 
              ref="basicInfoForm" 
              :model="basicInfo" 
              label-width="120px" 
              :rules="basicInfoRules"
              status-icon>
              <el-form-item label="姓名" prop="name">
                <el-input v-model="basicInfo.name" disabled />
              </el-form-item>
              <el-form-item label="学号" prop="studentId">
                <el-input v-model="basicInfo.studentId" disabled />
              </el-form-item>
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="basicInfo.idCard" disabled />
              </el-form-item>
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="basicInfo.gender">
                  <el-radio label="male">男</el-radio>
                  <el-radio label="female">女</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="出生日期" prop="birthday">
                <el-date-picker 
                  v-model="basicInfo.birthday" 
                  type="date" 
                  placeholder="选择日期" 
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" />
              </el-form-item>
              <el-form-item label="民族" prop="ethnicity">
                <el-select v-model="basicInfo.ethnicity" placeholder="请选择民族" style="width: 100%">
                  <el-option v-for="item in ethnicityOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <el-form-item label="政治面貌" prop="politicalStatus">
                <el-select v-model="basicInfo.politicalStatus" placeholder="请选择政治面貌" style="width: 100%">
                  <el-option v-for="item in politicalStatusOptions" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
              <el-form-item label="联系电话" prop="phone">
                <el-input v-model="basicInfo.phone" placeholder="请输入手机号码" />
              </el-form-item>
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model="basicInfo.email" placeholder="请输入电子邮箱" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveBasicInfo">保存信息</el-button>
                <el-button @click="resetBasicInfo">重置</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 学籍信息 -->
          <el-tab-pane label="学籍信息" name="academic">
            <h3 class="text-xl font-medium text-gray-800 mb-4">学籍信息</h3>
            <el-descriptions border>
              <el-descriptions-item label="所属学院">{{ academicInfo.college }}</el-descriptions-item>
              <el-descriptions-item label="所属专业">{{ academicInfo.major }}</el-descriptions-item>
              <el-descriptions-item label="所属班级">{{ academicInfo.className }}</el-descriptions-item>
              <el-descriptions-item label="学制">{{ academicInfo.educationLength }}年制</el-descriptions-item>
              <el-descriptions-item label="学习形式">{{ academicInfo.studyType }}</el-descriptions-item>
              <el-descriptions-item label="入学日期">{{ academicInfo.enrollmentDate }}</el-descriptions-item>
              <el-descriptions-item label="预计毕业日期">{{ academicInfo.expectedGraduationDate }}</el-descriptions-item>
              <el-descriptions-item label="学籍状态">
                <el-tag type="success">{{ academicInfo.status }}</el-tag>
              </el-descriptions-item>
            </el-descriptions>
            <div class="text-gray-600 text-sm mt-4">
              <el-alert type="info" show-icon>
                学籍信息为只读信息，如有疑问请联系教务管理部门
              </el-alert>
            </div>
          </el-tab-pane>

          <!-- 账号安全管理 -->
          <el-tab-pane label="账号安全" name="security">
            <h3 class="text-xl font-medium text-gray-800 mb-4">账号安全</h3>
            <el-collapse accordion>
              <!-- 密码修改 -->
              <el-collapse-item name="password">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Lock /></el-icon>
                    <span>密码修改</span>
                  </div>
                </template>
                <el-form 
                  ref="passwordForm" 
                  :model="passwordData" 
                  :rules="passwordRules" 
                  label-width="120px">
                  <el-form-item label="当前密码" prop="currentPassword">
                    <el-input v-model="passwordData.currentPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item label="新密码" prop="newPassword">
                    <el-input v-model="passwordData.newPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item label="确认新密码" prop="confirmPassword">
                    <el-input v-model="passwordData.confirmPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="changePassword">修改密码</el-button>
                    <el-button @click="resetPasswordForm">取消</el-button>
                  </el-form-item>
                </el-form>
              </el-collapse-item>

              <!-- 多因素认证 -->
              <el-collapse-item name="mfa">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Key /></el-icon>
                    <span>多因素认证</span>
                  </div>
                </template>
                <div class="p-4">
                  <p class="mb-4">多因素认证可以提高账号安全性，防止未授权访问。</p>
                  <el-switch
                    v-model="securitySettings.mfaEnabled"
                    active-text="已启用"
                    inactive-text="未启用"
                    @change="toggleMFA"
                  />
                  <div v-if="securitySettings.mfaEnabled" class="mt-4 bg-gray-50 p-4 rounded">
                    <p class="mb-2 font-medium">已绑定设备：</p>
                    <el-tag class="mr-2 mb-2">手机验证器</el-tag>
                    <p class="text-sm text-gray-600 mb-2">上次验证时间：2023-09-15 14:30</p>
                    <el-button size="small" type="primary">重新配置</el-button>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 账号绑定 -->
              <el-collapse-item name="binding">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Link /></el-icon>
                    <span>安全验证绑定</span>
                  </div>
                </template>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">手机绑定</h4>
                      <p class="text-sm text-gray-600">当前绑定：{{ securitySettings.phone }}</p>
                    </div>
                    <el-button type="primary" plain size="small">修改</el-button>
                  </div>
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">邮箱绑定</h4>
                      <p class="text-sm text-gray-600">当前绑定：{{ securitySettings.email }}</p>
                    </div>
                    <el-button type="primary" plain size="small">修改</el-button>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-tab-pane>


          <!-- 社交账号绑定 -->
          <el-tab-pane label="社交账号绑定" name="social">
            <h3 class="text-xl font-medium text-gray-800 mb-4">社交账号绑定</h3>
            <el-card shadow="never" class="mb-4">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="text-green-600 text-xl mr-2"><ChatDotSquare /></el-icon>
                  <span>微信账号</span>
                </div>
              </template>
              <div class="flex justify-between items-center">
                <div>
                  <p v-if="socialAccounts.wechat.bound" class="text-gray-600">
                    已绑定账号：{{ socialAccounts.wechat.nickname }}
                  </p>
                  <p v-else class="text-gray-600">未绑定微信账号</p>
                </div>
                <el-button type="success" plain v-if="!socialAccounts.wechat.bound">绑定</el-button>
                <el-button type="info" plain v-else>解绑</el-button>
              </div>
            </el-card>

            <el-card shadow="never">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="text-blue-600 text-xl mr-2"><ChatRound /></el-icon>
                  <span>QQ账号</span>
                </div>
              </template>
              <div class="flex justify-between items-center">
                <div>
                  <p v-if="socialAccounts.qq.bound" class="text-gray-600">
                    已绑定账号：{{ socialAccounts.qq.nickname }}
                  </p>
                  <p v-else class="text-gray-600">未绑定QQ账号</p>
                </div>
                <div v-if="!socialAccounts.qq.bound">
                  <el-button type="primary" class="mr-2" size="small">
                    <el-icon class="mr-1"><Plus /></el-icon>扫码绑定
                  </el-button>
                  <el-popover
                    placement="top"
                    :width="200"
                    trigger="click">
                    <template #reference>
                      <el-button type="info" plain size="small">其他方式</el-button>
                    </template>
                    <div class="p-2">
                      <div class="mb-3 font-medium text-center">选择绑定方式</div>
                      <div class="space-y-2">
                        <el-button type="primary" size="small" block>
                          <el-icon class="mr-1"><Key /></el-icon>账号密码绑定
                        </el-button>
                        <el-button type="success" size="small" block>
                          <el-icon class="mr-1"><Link /></el-icon>手机号关联绑定
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
                <el-button type="info" plain v-else>解绑</el-button>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Lock, 
  Key, 
  Link, 
  EditPen,
  Plus,
  ChatDotSquare,
  ChatRound,
  Edit,
  ArrowRight,
  TrendCharts,
  Timer,
  Notebook,
  Medal
} from '@element-plus/icons-vue'

// 引入学生数据存储
const studentStore = useStudentStore()

// 当前活动选项卡
const activeTab = ref('basic')

// 基本信息表单引用与数据
const basicInfoForm = ref(null)
const basicInfo = reactive({
  name: studentStore.userFullName,
  studentId: studentStore.studentData.student_id,
  idCard: '330************1234',
  gender: 'male',
  birthday: '1998-01-01',
  ethnicity: '汉族',
  politicalStatus: '共青团员',
  phone: '13800138000',
  email: studentStore.studentData.email,
  avatar: studentStore.studentData.avatar
})

// 民族与政治面貌选项
const ethnicityOptions = [
  '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
  '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族', '黎族', '傈僳族',
  '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族', '景颇族', '柯尔克孜族', '土族',
  '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族', '毛南族', '仡佬族', '锡伯族', '阿昌族', '普米族',
  '塔吉克族', '怒族', '乌孜别克族', '俄罗斯族', '鄂温克族', '德昂族', '保安族', '裕固族', '京族', '塔塔尔族',
  '独龙族', '鄂伦春族', '赫哲族', '门巴族', '珞巴族', '基诺族'
]

const politicalStatusOptions = [
  '中共党员', '中共预备党员', '共青团员', '民革党员', '民盟盟员', '民建会员', 
  '民进会员', '农工党党员', '致公党党员', '九三学社社员', '台盟盟员', '无党派人士', '群众'
]

// 表单验证规则
const basicInfoRules = {
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 学籍信息数据
const academicInfo = reactive({
  college: '计算机科学与技术学院',
  major: '计算机科学与技术',
  className: '计算机2023-2班',
  educationLength: 4,
  studyType: '全日制',
  enrollmentDate: '2023-09-01',
  expectedGraduationDate: '2027-07-01',

})

// 安全设置
const passwordForm = ref(null)
const passwordData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能小于8个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, 
      message: '密码必须包含大小写字母、数字和特殊字符', trigger: 'blur' 
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== passwordData.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

const securitySettings = reactive({
  mfaEnabled: true,
  phone: '138****8000',
  email: 's****<EMAIL>'
})

// 社交账号
const socialAccounts = reactive({
  wechat: {
    bound: true,
    nickname: '学习小达人'
  },
  qq: {
    bound: false,
    nickname: ''
  }
})

// 学生数据分析
const studentAnalytics = reactive({
  totalLearningHours: 158.5,
  weeklyLearningHours: 12.5,
  classRank: 3,
  classTotal: 42,
  completedCourses: 5,
  totalCourses: 8,
  certificatesEarned: 3,
  medalLevel: 'silver',
  pointsToNextLevel: 250,
  continuousLearningDays: 15,
  dailyLearningData: [1.2, 0.8, 1.5, 2.0, 0.5, 1.8, 2.5, 1.0, 1.2, 0.0, 2.0, 2.2, 1.6, 2.4]
})

// 方法
const saveBasicInfo = () => {
  basicInfoForm.value.validate((valid) => {
    if (valid) {
      ElMessage.success('基本信息保存成功')
      // 在实际应用中，这里会调用API保存数据
      // studentStore.updateUserData(basicInfo)
    }
  })
}

const resetBasicInfo = () => {
  basicInfoForm.value.resetFields()
}

const changePassword = () => {
  passwordForm.value.validate((valid) => {
    if (valid) {
      ElMessageBox.confirm('确定要修改密码吗？修改后需要重新登录。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 实际应用中这里会调用API修改密码
        ElMessage.success('密码修改成功，请重新登录')
        resetPasswordForm()
      }).catch(() => {
        // 用户取消操作
      })
    }
  })
}

const resetPasswordForm = () => {
  passwordForm.value.resetFields()
}

const toggleMFA = (value) => {
  if (value) {
    ElMessage.success('已成功开启多因素认证')
  } else {
    ElMessageBox.confirm('关闭多因素认证将降低账号安全性，确定要关闭吗？', '安全提示', {
      confirmButtonText: '确定关闭',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      ElMessage.warning('已关闭多因素认证')
    }).catch(() => {
      securitySettings.mfaEnabled = true // 用户取消，恢复开启状态
    })
  }
}

const handleAvatarChange = (file) => {
  // 在真实应用中，这里会处理头像上传逻辑
  const url = URL.createObjectURL(file.raw)
  basicInfo.avatar = url
  ElMessage.success('头像上传成功')
}

onMounted(() => {
  // 在真实应用中，这里会从API加载个人信息数据
  console.log('个人中心页面已加载')
})
</script>

<style scoped>
.el-descriptions :deep(.el-descriptions-item__label) {
  width: 140px;
}
</style> 