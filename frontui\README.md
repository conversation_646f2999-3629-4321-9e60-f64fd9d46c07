# 智慧教育平台

一个现代化的教育管理系统，基于Vue 3开发，为学生、教师和管理员提供全方位功能支持。

## 项目概述

智慧教育平台是一个综合性教育管理系统，集成了学生学习管理、教师教学内容创建、管理员系统监控等多种功能，旨在提供一站式教育信息化解决方案。

## 主要功能模块

### 学生模块
- **学习管理** - 课程学习、作业管理、知识库使用
- **互动辅助** - AI助手、学习社区、学习工具
- **学习分析** - 学习数据分析、学习诊断、错题本管理
- **激励系统** - 积分与成就、积分商城、学习成长路径
- **个人设置** - 个人信息管理、系统配置

### 教师模块
- **教学内容创建** - 知识库管理、教案生成、PPT自动生成
- **课程制作** - 口播脚本与音频制作、数字人与视频制作
- **在线编辑** - 实时编辑功能、协作功能
- **课程管理** - 视频合成、课程发布、学生绑定

### 管理员模块
- **系统管理** - 控制台监控、用户管理、课程管理、机构管理
- **数据分析** - 用户行为分析、报表管理、学习行为监控
- **资源管理** - 积分系统管理、商城管理、内容审核
- **系统运维** - 系统日志与审计、安全管理、系统维护

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **UI框架**: Element Plus
- **路由**: Vue Router
- **网络请求**: Axios
- **CSS预处理器**: SCSS

## 开发环境设置

### 推荐的IDE

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (禁用Vetur)

### 配置自定义

详见 [Vite配置参考](https://vitejs.dev/config/)

## 项目安装与运行

### 安装依赖

```sh
npm install
```

### 开发模式编译与热重载

```sh
npm run dev
```

### 生产环境编译与压缩

```sh
npm run build
```

### 代码规范检查

```sh
npm run lint
```

## 项目结构

```
vue-project/
├── public/              # 静态资源
├── src/
│   ├── assets/          # 项目资源文件
│   ├── components/      # 共用组件
│   ├── router/          # 路由配置
│   ├── stores/          # Pinia状态管理
│   ├── styles/          # 全局样式
│   ├── utils/           # 工具函数
│   ├── views/           # 页面视图
│   │   ├── student/     # 学生相关页面
│   │   ├── teacher/     # 教师相关页面
│   │   └── admin/       # 管理员相关页面
│   ├── App.vue          # 根组件
│   └── main.ts          # 入口文件
├── scripts/             # 项目脚本和PRD文档
└── package.json         # 项目配置
```

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

[MIT](LICENSE)
