from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.conf import settings
from django.core.exceptions import ValidationError
from .base_model_view import BaseModelViewSet
from zhkt.serializers import (
    CollegeSerializer,
    MajorSerializer,
    ClassGroupSerializer,
    StudentSerializer,
)
from zhkt.entitys import (
    College, 
    Major,
    ClassGroup,
    Student,
    Course,
    User,
)
import random
import string

# 组织相关视图集
class CollegeViewSet(BaseModelViewSet):
    queryset = College.objects.all()
    serializer_class = CollegeSerializer
    permission_classes = [permissions.IsAuthenticated]

class MajorViewSet(BaseModelViewSet):
    queryset = Major.objects.all()
    serializer_class = MajorSerializer
    permission_classes = [permissions.IsAuthenticated]

class ClassGroupViewSet(viewsets.ModelViewSet):
    """班级视图集"""
    serializer_class = ClassGroupSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取当前教师管理的班级列表"""
        course_id = self.request.query_params.get('course_id')
        print('course_id', course_id)
        queryset = ClassGroup.objects.filter(
            head_teacher=self.request.user.teacher_profile,
            deleted_at__isnull=True
        )
        if course_id:
            queryset = queryset.filter(courses__id=course_id)
        return queryset.order_by('-created_at')

    def get_current_course(self):
        """获取当前课程"""
        course_id = self.request.data.get('course_id')
        if not course_id:
            return None
        try:
            return Course.objects.get(id=course_id)
        except Course.DoesNotExist:
            return None

    def perform_create(self, serializer):
        """创建班级时设置班主任和关联课程"""
        # 生成唯一的6位邀请码
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            if not ClassGroup.objects.filter(code=code).exists():
                break
        
        # 创建班级
        class_group = serializer.save(head_teacher=self.request.user.teacher_profile, code=code)
        
        # 关联课程
        course = self.get_current_course()
        print('获取当前 course', course)
        if course:
            course.class_groups.add(class_group)

    def perform_destroy(self, instance):
        """移除课程关联关系，如果班级有学生则不允许删除"""
        if instance.students.exists():
            raise ValidationError('班级中还有学生，不能删除')
        
        # 获取当前课程并移除关联
        course = self.get_current_course()
        if course:
            course.class_groups.remove(instance)
            # 如果班级没有关联任何课程，且没有学生，则可以删除
            if not instance.courses.exists() and not instance.students.exists():
                instance.deleted_at = timezone.now()
                instance.save()
        else:
            # 如果没有指定课程，则执行常规删除
            instance.deleted_at = timezone.now()
            instance.save()

    @action(detail=True, methods=['get'])
    def invitation_code(self, request, pk=None):
        """获取班级邀请码"""
        class_group = self.get_object()
        if not class_group.code:
            # 如果没有邀请码，生成一个
            while True:
                code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
                if not ClassGroup.objects.filter(code=code).exists():
                    break
            class_group.code = code
            class_group.save()
        
        return Response({
            'code': class_group.code,
            'type': 'class'
        })

    @action(detail=False, methods=['post'])
    def join_by_code(self, request):
        """通过邀请码加入班级"""
        code = request.data.get('code')
        if not code:
            return Response({'error': '请提供邀请码'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            class_group = ClassGroup.objects.get(code=code, deleted_at__isnull=True)
            user = request.user
            
            # 检查用户是否已经是学生
            student = getattr(user, 'student_profile', None)
            if not student:
                nowdate = timezone.now().date()
                # 如果不是学生，创建学生档案
                student = Student.objects.create(
                    user=user,
                    student_id=f'ST{nowdate.year}{user.id:04d}'
                )
            
            # 将学生添加到班级
            if student not in class_group.students.all():
                class_group.students.add(student)
                return Response({'message': '成功加入班级'}, status=status.HTTP_200_OK)
            else:
                return Response({'error': '您已经在这个班级中了'}, status=status.HTTP_202_ACCEPTED)
            
        except ClassGroup.DoesNotExist:
            return Response({'error': '无效的邀请码'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def add_students(self, request, pk=None):
        """添加学生到班级"""
        class_group = self.get_object()
        user_ids = request.data.get('user_ids', [])
        
        if not user_ids:
            return Response({'error': '请提供用户ID列表'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 获取用户列表
            users = User.objects.filter(id__in=user_ids, deleted_at__isnull=True)
            nowdate = timezone.now().date()
            # 为每个用户创建或获取学生记录
            for user in users:
                student, created = Student.objects.get_or_create(
                    user=user,
                    defaults={
                        'student_id': f'ST{nowdate.year}{user.id:04d}',  # 生成学生ID
                    }
                )
                # 将学生添加到班级
                class_group.students.add(student)
            
            return Response({'message': '学生添加成功'}, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def remove_students(self, request, pk=None):
        """从班级移除学生"""
        class_group = self.get_object()
        student_ids = request.data.get('student_ids', [])
        
        if not student_ids:
            return Response({'error': '请提供学生ID列表'}, status=status.HTTP_400_BAD_REQUEST)
        
        students = Student.objects.filter(id__in=student_ids)
        class_group.students.remove(*students)
        
        return Response({'message': '学生移除成功'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['get'])
    def students(self, request, pk=None):
        """获取班级的所有学生"""
        class_group = self.get_object()
        students = class_group.students.filter(deleted_at__isnull=True)
        serializer = StudentSerializer(students, many=True)
        return Response(serializer.data)