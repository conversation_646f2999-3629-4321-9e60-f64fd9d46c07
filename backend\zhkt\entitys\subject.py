from django.db import models
from django.utils.translation import gettext_lazy as _


class Subject(models.Model):
    """学科模型"""
    name = models.CharField(_('学科名称'), max_length=100)
    code = models.CharField(_('学科代码'), max_length=50, null=True, blank=True)
    description = models.CharField(_('学科描述'), max_length=500, null=True, blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, related_name='children', null=True, blank=True, db_column='parent_id')
    level = models.PositiveSmallIntegerField(_('学科层级'), default=1)
    icon = models.CharField(_('学科图标'), max_length=255, null=True, blank=True)
    sort_order = models.IntegerField(_('排序顺序'), default=0)
    status = models.BooleanField(_('状态'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        db_table = 'zhkt_subject'
        verbose_name = _('学科')
        verbose_name_plural = _('学科')

    def __str__(self):
        return self.name 