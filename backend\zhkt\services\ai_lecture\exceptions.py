# -*- coding: utf-8 -*-
"""
AI讲课服务自定义异常类
提供统一的异常处理和错误信息定义
"""


class AILectureException(Exception):
    """AI讲课服务基础异常类"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class DocumentProcessingException(AILectureException):
    """文档处理异常"""
    def __init__(self, message: str, error_code: str = "DOC_PROCESS_ERROR"):
        super().__init__(message, error_code)


class OutlineGenerationException(AILectureException):  
    """大纲生成异常"""
    def __init__(self, message: str, error_code: str = "OUTLINE_GEN_ERROR"):
        super().__init__(message, error_code)


class ContentCreationException(AILectureException):
    """内容创建异常"""
    def __init__(self, message: str, error_code: str = "CONTENT_CREATE_ERROR"):
        super().__init__(message, error_code)


class SpeechSynthesisException(AILectureException):
    """语音合成异常"""
    def __init__(self, message: str, error_code: str = "SPEECH_SYNTH_ERROR"):
        super().__init__(message, error_code)


class FileOperationException(AILectureException):
    """文件操作异常"""
    def __init__(self, message: str, error_code: str = "FILE_OP_ERROR"):
        super().__init__(message, error_code) 