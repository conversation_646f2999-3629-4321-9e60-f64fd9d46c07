<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="题库管理"
    activePage="question-bank"
  >
    <div class="space-y-6">
      <!-- 功能区 -->
      <div class="flex flex-wrap justify-between items-center gap-4 mb-4">
        <div class="flex gap-3">
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
            @click="addQuestionWithContext"
          >
            <span class="material-icons text-sm">add</span>
            创建题目
          </button>
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_upload</span>
            导入题目
          </button>
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_download</span>
            导出题目
          </button>
        </div>
        <div class="flex gap-3">
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索题目..." 
              class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
              <span class="material-icons text-gray-600">filter_list</span>
              筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <div class="space-y-3">
                <div class="font-medium flex items-center gap-1.5">
                  <span class="material-icons text-base text-gray-600">school</span>
                  课程
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="course in courseOptions" 
                    :key="course"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.courses.includes(course) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('courses', course)"
                  >
                    <span v-if="selectedFilters.courses.includes(course)" class="material-icons text-xs">check_circle</span>
                    {{ course }}
                  </span>
                </div>
                <div class="font-medium mt-3 flex items-center gap-1.5">
                   <span class="material-icons text-base text-gray-600">label</span>
                   题型
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="type in questionTypes" 
                    :key="type"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.types.includes(type) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('types', type)"
                  >
                    <span v-if="selectedFilters.types.includes(type)" class="material-icons text-xs">check_circle</span>
                    {{ type }}
                  </span>
                </div>
                <div class="font-medium mt-3 flex items-center gap-1.5">
                   <span class="material-icons text-base text-gray-600">speed</span>
                   难度
                </div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="difficulty in difficultyLevels" 
                    :key="difficulty"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer flex items-center gap-1"
                    :class="selectedFilters.difficulties.includes(difficulty) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('difficulties', difficulty)"
                  >
                    <span v-if="selectedFilters.difficulties.includes(difficulty)" class="material-icons text-xs">check_circle</span>
                    {{ difficulty }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 知识结构布局 -->
      <div class="grid grid-cols-12 gap-4">
        <!-- 左侧课程-章节树 -->
        <div class="col-span-3 bg-white rounded-lg shadow">
          <div class="p-3 border-b flex items-center justify-between">
            <h3 class="font-medium text-gray-700">知识结构</h3>
            <button class="p-1 rounded-full hover:bg-gray-100" @click="showAddCourseModal = true">
              <span class="material-icons text-gray-500 text-sm">add</span>
            </button>
          </div>
          <!-- 树形结构 -->
          <div class="p-3">
            <div v-for="(course, index) in courses" :key="index" class="mb-2">
              <div 
                class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer"
                @click="toggleCourse(index)"
              >
                <span class="material-icons text-gray-500 text-sm mr-1">
                  {{ course.expanded ? 'expand_more' : 'chevron_right' }}
                </span>
                <span class="material-icons text-blue-500 text-sm mr-1.5">menu_book</span>
                <span class="text-sm">{{ course.name }}</span>
                
                <!-- 课程操作按钮 -->
                <div class="ml-auto flex opacity-0 group-hover:opacity-100" @click.stop>
                  <button class="p-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full" 
                          @click="addChapter(index)">
                    <span class="material-icons text-xs">add</span>
                  </button>
                  <button class="p-1 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-full" 
                          @click="editCourse(index)">
                    <span class="material-icons text-xs">edit</span>
                  </button>
                  <button class="p-1 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-full" 
                          @click="deleteCourse(index)">
                    <span class="material-icons text-xs">delete</span>
                  </button>
                </div>
              </div>
              
              <!-- 章节列表 -->
              <div v-if="course.expanded" class="ml-6">
                <div v-for="(chapter, chapterIndex) in course.chapters" :key="chapterIndex" class="my-1">
                  <div 
                    class="flex items-center p-1.5 hover:bg-gray-50 rounded cursor-pointer group"
                    @click="toggleChapter(index, chapterIndex)"
                  >
                    <span class="material-icons text-gray-500 text-sm mr-1">
                      {{ chapter.expanded ? 'expand_more' : 'chevron_right' }}
                    </span>
                    <span class="material-icons text-green-500 text-sm mr-1.5">folder</span>
                    <span class="text-sm">{{ chapter.name }}</span>
                    
                    <!-- 章节操作按钮 -->
                    <div class="ml-auto flex opacity-0 group-hover:opacity-100" @click.stop>
                      <button class="p-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full" 
                              @click="addSection(index, chapterIndex)">
                        <span class="material-icons text-xs">add</span>
                      </button>
                      <button class="p-1 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-full" 
                              @click="editChapter(index, chapterIndex)">
                        <span class="material-icons text-xs">edit</span>
                      </button>
                      <button class="p-1 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-full" 
                              @click="deleteChapter(index, chapterIndex)">
                        <span class="material-icons text-xs">delete</span>
                      </button>
                    </div>
                  </div>
                  
                  <!-- 小节列表 -->
                  <div v-if="chapter.expanded" class="ml-6">
                    <div 
                      v-for="(section, sectionIndex) in chapter.sections" 
                      :key="sectionIndex"
                      class="flex items-center p-1.5 hover:bg-gray-50 rounded cursor-pointer group"
                      @click="selectSection(index, chapterIndex, sectionIndex)"
                      :class="{'bg-blue-50': isSelectedSection(index, chapterIndex, sectionIndex)}"
                    >
                      <span class="material-icons text-yellow-500 text-sm mr-1.5">article</span>
                      <span class="text-sm">{{ section.name }}</span>
                      
                      <!-- 小节操作按钮 -->
                      <div class="ml-auto flex opacity-0 group-hover:opacity-100" @click.stop>
                        <button class="p-1 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-full" 
                                @click="editSection(index, chapterIndex, sectionIndex)">
                          <span class="material-icons text-xs">edit</span>
                        </button>
                        <button class="p-1 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-full" 
                                @click="deleteSection(index, chapterIndex, sectionIndex)">
                          <span class="material-icons text-xs">delete</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧题目列表 -->
        <div class="col-span-9 bg-white rounded-lg shadow">
          <div class="p-4 border-b">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-800">题目列表</h3>
              <div class="flex gap-2">
                <button 
                  class="px-3 py-1.5 border border-gray-300 rounded text-sm font-medium text-gray-600 hover:bg-gray-50 flex items-center gap-1.5"
                  @click="showBatchOperationMenu = !showBatchOperationMenu"
                >
                  <span class="material-icons text-sm">more_horiz</span>
                  批量操作
                  
                  <!-- 批量操作下拉菜单 -->
                  <div 
                    v-if="showBatchOperationMenu" 
                    class="absolute right-4 mt-28 bg-white shadow-lg rounded-md py-2 z-10 w-36"
                  >
                    <button 
                      class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      @click="exportSelectedQuestions"
                    >
                      <span class="material-icons text-sm">file_download</span>
                      导出所选
                    </button>
                    <button 
                      class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      @click="moveSelectedQuestions"
                    >
                      <span class="material-icons text-sm">drive_file_move</span>
                      移动到
                    </button>
                    <button 
                      class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                      @click="deleteSelectedQuestions"
                    >
                      <span class="material-icons text-sm">delete</span>
                      删除所选
                    </button>
                  </div>
                </button>
                <select 
                  v-model="sortOption" 
                  class="border border-gray-300 rounded px-2 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="newest">最新添加</option>
                  <option value="difficulty-asc">难度由低到高</option>
                  <option value="difficulty-desc">难度由高到低</option>
                  <option value="usage">使用频率</option>
                </select>
              </div>
            </div>
            
            <!-- 题目分类筛选条 -->
            <div class="mt-4 flex flex-wrap gap-2">
              <div>
                <span class="text-sm font-medium text-gray-700 mr-2">类型:</span>
                <select 
                  v-model="selectedQuestionType" 
                  class="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部</option>
                  <option v-for="type in questionTypes" :key="type" :value="type">{{ type }}</option>
                </select>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700 mr-2">难度:</span>
                <select 
                  v-model="selectedDifficulty" 
                  class="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部</option>
                  <option v-for="level in difficultyLevels" :key="level" :value="level">{{ level }}</option>
                </select>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700 mr-2">标签:</span>
                <select 
                  v-model="selectedTag" 
                  class="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部</option>
                  <option v-for="tag in questionTags" :key="tag" :value="tag">{{ tag }}</option>
                </select>
              </div>
              <div class="ml-auto">
                <button 
                  class="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded-md text-sm flex items-center gap-1"
                  @click="addQuestionWithContext"
                >
                  <span class="material-icons text-sm">add</span>
                  添加题目
                </button>
              </div>
            </div>
          </div>
          
          <!-- 题目列表 -->
          <div class="p-4">
            <div v-if="displayQuestions.length === 0" class="py-8 text-center text-gray-500">
              <div class="material-icons text-4xl mb-2">assignment</div>
              <p>暂无符合条件的题目</p>
              <button 
                class="mt-3 text-blue-600 hover:text-blue-800"
                @click="addQuestionWithContext"
              >
                点击创建新题目
              </button>
            </div>
            
            <div 
              v-for="question in displayQuestions" 
              :key="question.id" 
              class="mb-4 p-4 border rounded-md hover:shadow-sm"
            >
              <div class="flex items-start">
                <!-- 选择框 -->
                <div class="pr-3 pt-1">
                  <input 
                    type="checkbox" 
                    :checked="selectedQuestions.includes(question.id)" 
                    @change="toggleQuestionSelection(question.id)"
                    class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                
                <!-- 题目信息 -->
                <div class="flex-1">
                  <div class="flex items-center gap-2 flex-wrap">
                    <span 
                      class="px-2 py-0.5 text-xs rounded-full" 
                      :class="getQuestionTypeClass(question.type)"
                    >
                      {{ question.type }}
                    </span>
                    <span 
                      class="px-2 py-0.5 text-xs rounded-full" 
                      :class="getDifficultyClass(question.difficulty)"
                    >
                      {{ question.difficulty }}
                    </span>
                    <span class="text-xs text-gray-500">{{ question.course }}</span>
                    <span class="text-xs text-gray-500">
                      {{ question.chapter }} - {{ question.section }}
                    </span>
                    <span class="text-xs text-gray-500">{{ question.points }}分</span>
                    <div class="flex gap-1 ml-auto">
                      <span 
                        v-for="tag in (question.tags || [])" 
                        :key="tag" 
                        class="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </div>
                  <div class="mt-2 text-sm font-medium text-gray-900">{{ question.title }}</div>
                  <div class="mt-1 text-xs text-gray-500">
                    创建于 {{ question.createdAt }} | 创建者: {{ question.createdBy }} | 使用次数: {{ question.usageCount }}
                  </div>
                  
                  <!-- 题目内容预览 -->
                  <div class="mt-2 text-sm text-gray-700" v-if="expandedQuestions.includes(question.id)">
                    <!-- 单选题预览 -->
                    <div v-if="question.type === '单选题'" class="mt-2 bg-gray-50 p-3 rounded">
                      <div class="mb-2">{{ question.title }}</div>
                      <div v-for="(option, index) in question.options" :key="index" class="mb-1 flex items-center gap-2">
                        <span 
                          class="h-5 w-5 rounded-full border flex items-center justify-center text-xs"
                          :class="option === question.answer ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
                        >
                          {{ String.fromCharCode(65 + index) }}
                        </span>
                        {{ option }}
                      </div>
                      <div class="mt-2 text-xs text-gray-500">
                        正确答案: {{ question.answer }}
                      </div>
                    </div>
                    
                    <!-- 多选题预览 -->
                    <div v-else-if="question.type === '多选题'" class="mt-2 bg-gray-50 p-3 rounded">
                      <div class="mb-2">{{ question.title }}</div>
                      <div v-for="(option, index) in question.options" :key="index" class="mb-1 flex items-center gap-2">
                        <span 
                          class="h-5 w-5 rounded border flex items-center justify-center text-xs"
                          :class="question.answer.includes(option) ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
                        >
                          {{ String.fromCharCode(65 + index) }}
                        </span>
                        {{ option }}
                      </div>
                      <div class="mt-2 text-xs text-gray-500">
                        正确答案: {{ question.answer.join(', ') }}
                      </div>
                    </div>
                    
                    <!-- 判断题预览 -->
                    <div v-else-if="question.type === '判断题'" class="mt-2 bg-gray-50 p-3 rounded">
                      <div class="mb-2">{{ question.title }}</div>
                      <div class="flex gap-4 mt-2">
                        <div class="flex items-center gap-2">
                          <span 
                            class="h-5 w-5 rounded-full border flex items-center justify-center text-xs"
                            :class="question.answer === true ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
                          >
                            √
                          </span>
                          正确
                        </div>
                        <div class="flex items-center gap-2">
                          <span 
                            class="h-5 w-5 rounded-full border flex items-center justify-center text-xs"
                            :class="question.answer === false ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
                          >
                            ×
                          </span>
                          错误
                        </div>
                      </div>
                      <div class="mt-2 text-xs text-gray-500">
                        正确答案: {{ question.answer ? '正确' : '错误' }}
                      </div>
                    </div>
                    
                    <!-- 填空题预览 -->
                    <div v-else-if="question.type === '填空题'" class="mt-2 bg-gray-50 p-3 rounded">
                      <div class="mb-2 whitespace-pre-line">{{ question.content }}</div>
                      <div class="mt-2 text-xs text-gray-500">
                        答案: <span class="text-blue-600">{{ question.answer }}</span>
                      </div>
                    </div>
                    
                    <!-- 简答题预览 -->
                    <div v-else-if="question.type === '简答题'" class="mt-2 bg-gray-50 p-3 rounded">
                      <div class="mb-2">{{ question.title }}</div>
                      <div class="mt-2 text-xs text-gray-500">
                        参考答案: <span class="text-blue-600">{{ question.answer }}</span>
                      </div>
                    </div>
                    
                    <!-- 编程题预览 -->
                    <div v-else-if="question.type === '编程题'" class="mt-2 bg-gray-50 p-3 rounded">
                      <div class="mb-2">{{ question.title }}</div>
                      <div class="text-xs text-gray-700 mb-2">{{ question.description }}</div>
                      <div v-if="question.testCases && question.testCases.length > 0" class="mb-2">
                        <div class="text-xs font-medium text-gray-600 mb-1">测试用例:</div>
                        <div v-for="(testCase, index) in question.testCases" :key="index" class="text-xs mb-1">
                          输入: <span class="font-mono bg-gray-100 px-1">{{ testCase.input }}</span> → 
                          输出: <span class="font-mono bg-gray-100 px-1">{{ testCase.expectedOutput }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 展开/收起按钮 -->
                  <button 
                    @click="toggleQuestionExpand(question.id)" 
                    class="mt-2 text-xs text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    <span class="material-icons text-xs mr-1">
                      {{ expandedQuestions.includes(question.id) ? 'expand_less' : 'expand_more' }}
                    </span>
                    {{ expandedQuestions.includes(question.id) ? '收起' : '预览题目' }}
                  </button>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex items-start gap-1 ml-2">
                  <button class="p-1.5 text-blue-600 hover:bg-blue-50 rounded" @click.stop="viewQuestion(question)">
                    <span class="material-icons text-sm">visibility</span>
                  </button>
                  <button class="p-1.5 text-green-600 hover:bg-green-50 rounded" @click.stop="editQuestion(question)">
                    <span class="material-icons text-sm">edit</span>
                  </button>
                  <button class="p-1.5 text-red-600 hover:bg-red-50 rounded" @click.stop="deleteQuestion(question)">
                    <span class="material-icons text-sm">delete</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="p-4 border-t flex justify-between items-center">
            <div class="text-sm text-gray-600">
              <span v-if="selectedQuestions.length > 0" class="font-medium text-blue-600 mr-2">
                已选择 {{ selectedQuestions.length }} 个题目
              </span>
              共 <span class="font-medium">{{ totalQuestions }}</span> 个题目
            </div>
            <div class="flex items-center space-x-2">
              <button 
                class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                :disabled="currentPage === 1"
                @click="currentPage--"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
              >
                上一页
              </button>
              
              <span v-for="pageNumber in displayedPageNumbers" :key="pageNumber">
                <button 
                  v-if="pageNumber !== '...'"
                  @click="currentPage = pageNumber"
                  class="px-3 py-1 rounded-md text-sm font-medium"
                  :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'"
                >
                  {{ pageNumber }}
                </button>
                <span v-else class="text-gray-500 px-2">...</span>
              </span>
              
              <button 
                class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>

  <!-- 添加课程模态框 -->
  <div v-if="showAddCourseModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">添加课程</h3>
      <div class="mb-4">
        <label for="courseName" class="block text-sm font-medium text-gray-700 mb-1">课程名称</label>
        <input 
          type="text" 
          id="courseName" 
          v-model="currentEditItem.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="请输入课程名称"
        />
      </div>
      <div class="flex justify-end gap-3">
        <button 
          @click="showAddCourseModal = false"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          @click="submitNewCourse"
          class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          确定
        </button>
      </div>
    </div>
  </div>
  
  <!-- 编辑课程模态框 -->
  <div v-if="showEditCourseModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">编辑课程</h3>
      <div class="mb-4">
        <label for="editCourseName" class="block text-sm font-medium text-gray-700 mb-1">课程名称</label>
        <input 
          type="text" 
          id="editCourseName" 
          v-model="currentEditItem.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div class="flex justify-end gap-3">
        <button 
          @click="showEditCourseModal = false"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          @click="submitCourseEdit"
          class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          确定
        </button>
      </div>
    </div>
  </div>
  
  <!-- 添加章节模态框 -->
  <div v-if="showAddChapterModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">添加章节</h3>
      <div class="mb-2 text-sm text-gray-500">
        课程: {{ currentEditItem.courseIndex !== null ? courses[currentEditItem.courseIndex].name : '' }}
      </div>
      <div class="mb-4">
        <label for="chapterName" class="block text-sm font-medium text-gray-700 mb-1">章节名称</label>
        <input 
          type="text" 
          id="chapterName" 
          v-model="currentEditItem.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="例如: 第一章 Python基础"
        />
      </div>
      <div class="flex justify-end gap-3">
        <button 
          @click="showAddChapterModal = false"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          @click="submitNewChapter"
          class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          确定
        </button>
      </div>
    </div>
  </div>
  
  <!-- 编辑章节模态框 -->
  <div v-if="showEditChapterModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">编辑章节</h3>
      <div class="mb-2 text-sm text-gray-500">
        课程: {{ currentEditItem.courseIndex !== null ? courses[currentEditItem.courseIndex].name : '' }}
      </div>
      <div class="mb-4">
        <label for="editChapterName" class="block text-sm font-medium text-gray-700 mb-1">章节名称</label>
        <input 
          type="text" 
          id="editChapterName" 
          v-model="currentEditItem.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div class="flex justify-end gap-3">
        <button 
          @click="showEditChapterModal = false"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          @click="submitChapterEdit"
          class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          确定
        </button>
      </div>
    </div>
  </div>
  
  <!-- 添加小节模态框 -->
  <div v-if="showAddSectionModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">添加小节</h3>
      <div v-if="currentEditItem.courseIndex !== null && currentEditItem.chapterIndex !== null" class="mb-2 text-sm text-gray-500">
        课程: {{ courses[currentEditItem.courseIndex].name }}<br>
        章节: {{ courses[currentEditItem.courseIndex].chapters[currentEditItem.chapterIndex].name }}
      </div>
      <div class="mb-4">
        <label for="sectionName" class="block text-sm font-medium text-gray-700 mb-1">小节名称</label>
        <input 
          type="text" 
          id="sectionName" 
          v-model="currentEditItem.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="例如: 1.1 Python环境搭建"
        />
      </div>
      <div class="flex justify-end gap-3">
        <button 
          @click="showAddSectionModal = false"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          @click="submitNewSection"
          class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          确定
        </button>
      </div>
    </div>
  </div>
  
  <!-- 编辑小节模态框 -->
  <div v-if="showEditSectionModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">编辑小节</h3>
      <div 
        v-if="currentEditItem.courseIndex !== null && 
             currentEditItem.chapterIndex !== null" 
        class="mb-2 text-sm text-gray-500"
      >
        课程: {{ courses[currentEditItem.courseIndex].name }}<br>
        章节: {{ courses[currentEditItem.courseIndex].chapters[currentEditItem.chapterIndex].name }}
      </div>
      <div class="mb-4">
        <label for="editSectionName" class="block text-sm font-medium text-gray-700 mb-1">小节名称</label>
        <input 
          type="text" 
          id="editSectionName" 
          v-model="currentEditItem.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div class="flex justify-end gap-3">
        <button 
          @click="showEditSectionModal = false"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          @click="submitSectionEdit"
          class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          确定
        </button>
      </div>
    </div>
  </div>
  
  <!-- 删除确认模态框 -->
  <div v-if="showDeleteConfirmModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
      <p class="text-gray-700 mb-4">
        您确定要删除{{ 
          deleteConfirmInfo.type === 'course' ? '课程' : 
          deleteConfirmInfo.type === 'chapter' ? '章节' : '小节' 
        }} <span class="font-medium">{{ deleteConfirmInfo.name }}</span> 吗？
        <template v-if="deleteConfirmInfo.type === 'course'">
          <br>
          <span class="text-red-600">注意：删除课程将同时删除其下所有章节和小节！</span>
        </template>
        <template v-if="deleteConfirmInfo.type === 'chapter'">
          <br>
          <span class="text-red-600">注意：删除章节将同时删除其下所有小节！</span>
        </template>
      </p>
      <div class="flex justify-end gap-3">
        <button 
          @click="showDeleteConfirmModal = false"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button 
          @click="confirmDelete"
          class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700"
        >
          确认删除
        </button>
      </div>
    </div>
  </div>

  <!-- 创建/编辑题目模态框 -->
  <div v-if="showCreateQuestionModal || showEditQuestionModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-3/4 max-h-[90vh] overflow-auto">
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
          {{ showEditQuestionModal ? '编辑题目' : '创建题目' }}
        </h3>
        
        <!-- 基础信息 -->
        <div class="grid grid-cols-3 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">题目类型</label>
            <select 
              v-model="questionForm.type" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option v-for="type in questionTypes" :key="type" :value="type">{{ type }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">难度</label>
            <select 
              v-model="questionForm.difficulty" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option v-for="level in difficultyLevels" :key="level" :value="level">{{ level }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">分值</label>
            <input 
              type="number" 
              v-model="questionForm.points" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        
        <!-- 知识点分类 -->
        <div class="grid grid-cols-3 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">课程</label>
            <select 
              v-model="questionForm.course" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              @change="onCourseChange"
            >
              <option value="">请选择课程</option>
              <option v-for="(course, index) in courses" :key="index" :value="course.name">
                {{ course.name }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">章节</label>
            <select 
              v-model="questionForm.chapter" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              @change="onChapterChange"
              :disabled="!questionForm.course"
            >
              <option value="">请选择章节</option>
              <option v-for="(chapter, index) in availableChapters" :key="index" :value="chapter.name">
                {{ chapter.name }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">小节</label>
            <select 
              v-model="questionForm.section" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              :disabled="!questionForm.chapter"
            >
              <option value="">请选择小节</option>
              <option v-for="(section, index) in availableSections" :key="index" :value="section.name">
                {{ section.name }}
              </option>
            </select>
          </div>
        </div>
        
        <!-- 标签 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">标签</label>
          <div class="flex flex-wrap gap-2">
            <div 
              v-for="tag in questionTags" 
              :key="tag"
              @click="toggleQuestionTag(tag)"
              class="px-3 py-1 rounded-full text-sm cursor-pointer"
              :class="questionForm.tags.includes(tag) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
            >
              {{ tag }}
            </div>
            <div class="relative">
              <input 
                type="text" 
                v-model="newTagInput" 
                @keyup.enter="addNewTag"
                placeholder="+ 添加新标签" 
                class="px-3 py-1 border border-gray-300 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
        
        <!-- 题目内容 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">题目标题</label>
          <input 
            type="text" 
            v-model="questionForm.title" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="输入题目标题"
          />
        </div>
        
        <!-- 题目类型专属字段 -->
        <!-- 选择题选项 -->
        <div v-if="questionForm.type === '单选题' || questionForm.type === '多选题'" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">选项</label>
          <div v-for="(option, index) in questionForm.options" :key="index" class="flex items-center mb-2">
            <span class="mr-2 text-gray-700">{{ String.fromCharCode(65 + index) }}</span>
            <input 
              type="text" 
              v-model="questionForm.options[index]" 
              class="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button 
              @click="removeOption(index)" 
              class="ml-2 text-red-600 hover:text-red-800"
            >
              <span class="material-icons text-sm">delete</span>
            </button>
          </div>
          <button 
            @click="addOption" 
            class="mt-2 text-blue-600 hover:text-blue-800 flex items-center"
          >
            <span class="material-icons text-sm mr-1">add</span> 添加选项
          </button>
        </div>
        
        <!-- 多选题答案 -->
        <div v-if="questionForm.type === '多选题'" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">正确答案（多选）</label>
          <div class="flex flex-wrap gap-3">
            <div 
              v-for="(option, index) in questionForm.options" 
              :key="index"
              @click="toggleAnswerSelection(option)"
              class="px-3 py-1 rounded-md text-sm cursor-pointer flex items-center gap-1"
              :class="isOptionSelected(option) ? 'bg-blue-100 text-blue-700 border border-blue-500' : 'bg-gray-100 text-gray-700 border border-gray-300'"
            >
              <span class="material-icons text-sm" v-if="isOptionSelected(option)">check</span>
              {{ option }}
            </div>
          </div>
        </div>
        
        <!-- 单选题答案 -->
        <div v-if="questionForm.type === '单选题'" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">正确答案</label>
          <div class="flex flex-wrap gap-3">
            <div 
              v-for="(option, index) in questionForm.options" 
              :key="index"
              @click="questionForm.answer = option"
              class="px-3 py-1 rounded-md text-sm cursor-pointer flex items-center gap-1"
              :class="questionForm.answer === option ? 'bg-blue-100 text-blue-700 border border-blue-500' : 'bg-gray-100 text-gray-700 border border-gray-300'"
            >
              <span class="material-icons text-sm" v-if="questionForm.answer === option">check</span>
              {{ option }}
            </div>
          </div>
        </div>
        
        <!-- 判断题答案 -->
        <div v-if="questionForm.type === '判断题'" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">正确答案</label>
          <div class="flex gap-4">
            <div 
              @click="questionForm.answer = true"
              class="px-3 py-1 rounded-md text-sm cursor-pointer flex items-center gap-1"
              :class="questionForm.answer === true ? 'bg-blue-100 text-blue-700 border border-blue-500' : 'bg-gray-100 text-gray-700 border border-gray-300'"
            >
              <span class="material-icons text-sm" v-if="questionForm.answer === true">check</span>
              正确
            </div>
            <div 
              @click="questionForm.answer = false"
              class="px-3 py-1 rounded-md text-sm cursor-pointer flex items-center gap-1"
              :class="questionForm.answer === false ? 'bg-blue-100 text-blue-700 border border-blue-500' : 'bg-gray-100 text-gray-700 border border-gray-300'"
            >
              <span class="material-icons text-sm" v-if="questionForm.answer === false">check</span>
              错误
            </div>
          </div>
        </div>
        
        <!-- 填空题内容 -->
        <div v-if="questionForm.type === '填空题'" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">题目内容（使用下划线"_____"表示填空处）</label>
          <textarea 
            v-model="questionForm.content" 
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="例如：C语言的创始人是_____。"
          ></textarea>
          
          <label class="block text-sm font-medium text-gray-700 mt-4 mb-2">参考答案</label>
          <input 
            type="text" 
            v-model="questionForm.answer" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="输入填空题答案"
          />
        </div>
        
        <!-- 简答题答案 -->
        <div v-if="questionForm.type === '简答题'" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">参考答案</label>
          <textarea 
            v-model="questionForm.answer" 
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="输入参考答案"
          ></textarea>
        </div>
        
        <!-- 编程题 -->
        <div v-if="questionForm.type === '编程题'" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">题目描述</label>
          <textarea 
            v-model="questionForm.description" 
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请详细描述编程题要求..."
          ></textarea>
          
          <!-- 测试用例 -->
          <div class="mt-4">
            <div class="flex items-center justify-between mb-2">
              <label class="text-sm font-medium text-gray-700">测试用例</label>
              <button 
                @click="addTestCase" 
                class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <span class="material-icons text-sm mr-1">add</span> 添加测试用例
              </button>
            </div>
            
            <div v-for="(testCase, index) in questionForm.testCases" :key="index" class="border border-gray-300 rounded-md p-3 mb-3">
              <div class="flex justify-between items-start mb-2">
                <span class="text-sm font-medium text-gray-700">测试用例 #{{ index + 1 }}</span>
                <button 
                  @click="removeTestCase(index)" 
                  class="text-red-600 hover:text-red-800"
                >
                  <span class="material-icons text-sm">delete</span>
                </button>
              </div>
              <div class="grid grid-cols-2 gap-3">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">输入</label>
                  <input 
                    type="text" 
                    v-model="testCase.input" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="测试输入..."
                  />
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">期望输出</label>
                  <input 
                    type="text" 
                    v-model="testCase.expectedOutput" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="期望输出..."
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex justify-end gap-3 mt-6">
          <button 
            @click="closeQuestionModal" 
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            取消
          </button>
          <button 
            @click="submitQuestion" 
            class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
          >
            {{ showEditQuestionModal ? '保存修改' : '创建题目' }}
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 查看题目详情模态框 -->
  <div v-if="showViewQuestionModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full p-6">
      <div class="flex justify-between items-start mb-4">
        <h3 class="text-lg font-medium text-gray-900">题目详情</h3>
        <button @click="showViewQuestionModal = false" class="text-gray-500 hover:text-gray-700">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <!-- 基本信息 -->
      <div class="flex items-center gap-2 flex-wrap mb-4">
        <span 
          class="px-2 py-0.5 text-xs rounded-full" 
          :class="getQuestionTypeClass(currentQuestion.type)"
        >
          {{ currentQuestion.type }}
        </span>
        <span 
          class="px-2 py-0.5 text-xs rounded-full" 
          :class="getDifficultyClass(currentQuestion.difficulty)"
        >
          {{ currentQuestion.difficulty }}
        </span>
        <span class="text-xs text-gray-500">{{ currentQuestion.course }}</span>
        <span class="text-xs text-gray-500">
          {{ currentQuestion.chapter }} - {{ currentQuestion.section }}
        </span>
        <span class="text-xs text-gray-500">{{ currentQuestion.points }}分</span>
        <div class="flex gap-1 ml-auto">
          <span 
            v-for="tag in (currentQuestion.tags || [])" 
            :key="tag" 
            class="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full"
          >
            {{ tag }}
          </span>
        </div>
      </div>
      
      <!-- 题目标题 -->
      <div class="text-lg font-medium text-gray-900 mb-4">{{ currentQuestion.title }}</div>
      
      <!-- 题目内容（根据题型显示不同内容） -->
      <!-- 单选题 -->
      <div v-if="currentQuestion.type === '单选题'" class="mb-6">
        <div class="font-medium text-gray-700 mb-3">选项：</div>
        <div v-for="(option, index) in currentQuestion.options" :key="index" class="mb-2 flex items-center gap-2">
          <span 
            class="h-6 w-6 rounded-full border flex items-center justify-center text-xs"
            :class="option === currentQuestion.answer ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
          >
            {{ String.fromCharCode(65 + index) }}
          </span>
          {{ option }}
        </div>
        <div class="mt-4 text-sm text-gray-700">
          正确答案: <span class="font-medium text-blue-600">{{ currentQuestion.answer }}</span>
        </div>
      </div>
      
      <!-- 多选题 -->
      <div v-if="currentQuestion.type === '多选题'" class="mb-6">
        <div class="font-medium text-gray-700 mb-3">选项：</div>
        <div v-for="(option, index) in currentQuestion.options" :key="index" class="mb-2 flex items-center gap-2">
          <span 
            class="h-6 w-6 rounded border flex items-center justify-center text-xs"
            :class="currentQuestion.answer && currentQuestion.answer.includes(option) ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
          >
            {{ String.fromCharCode(65 + index) }}
          </span>
          {{ option }}
        </div>
        <div class="mt-4 text-sm text-gray-700">
          正确答案: <span class="font-medium text-blue-600">{{ currentQuestion.answer ? currentQuestion.answer.join(', ') : '' }}</span>
        </div>
      </div>
      
      <!-- 判断题 -->
      <div v-if="currentQuestion.type === '判断题'" class="mb-6">
        <div class="flex gap-4 mt-2">
          <div class="flex items-center gap-2">
            <span 
              class="h-6 w-6 rounded-full border flex items-center justify-center text-xs"
              :class="currentQuestion.answer === true ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
            >
              √
            </span>
            正确
          </div>
          <div class="flex items-center gap-2">
            <span 
              class="h-6 w-6 rounded-full border flex items-center justify-center text-xs"
              :class="currentQuestion.answer === false ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300'"
            >
              ×
            </span>
            错误
          </div>
        </div>
        <div class="mt-4 text-sm text-gray-700">
          正确答案: <span class="font-medium text-blue-600">{{ currentQuestion.answer === true ? '正确' : '错误' }}</span>
        </div>
      </div>
      
      <!-- 填空题 -->
      <div v-if="currentQuestion.type === '填空题'" class="mb-6">
        <div class="font-medium text-gray-700 mb-3">题目内容：</div>
        <div class="mb-4 whitespace-pre-line bg-gray-50 p-3 rounded">{{ currentQuestion.content }}</div>
        <div class="text-sm text-gray-700">
          参考答案: <span class="font-medium text-blue-600">{{ currentQuestion.answer }}</span>
        </div>
      </div>
      
      <!-- 简答题 -->
      <div v-if="currentQuestion.type === '简答题'" class="mb-6">
        <div class="mb-4">{{ currentQuestion.title }}</div>
        <div class="font-medium text-gray-700 mb-1">参考答案：</div>
        <div class="bg-gray-50 p-3 rounded whitespace-pre-line">{{ currentQuestion.answer }}</div>
      </div>
      
      <!-- 编程题 -->
      <div v-if="currentQuestion.type === '编程题'" class="mb-6">
        <div class="font-medium text-gray-700 mb-3">题目描述：</div>
        <div class="mb-4 bg-gray-50 p-3 rounded whitespace-pre-line">{{ currentQuestion.description }}</div>
        
        <div v-if="currentQuestion.testCases && currentQuestion.testCases.length > 0">
          <div class="font-medium text-gray-700 mb-2">测试用例：</div>
          <table class="min-w-full border-collapse">
            <thead>
              <tr>
                <th class="py-2 px-4 bg-gray-100 text-left text-xs font-medium text-gray-500">序号</th>
                <th class="py-2 px-4 bg-gray-100 text-left text-xs font-medium text-gray-500">输入</th>
                <th class="py-2 px-4 bg-gray-100 text-left text-xs font-medium text-gray-500">期望输出</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(testCase, index) in currentQuestion.testCases" :key="index" class="border-b">
                <td class="py-2 px-4 text-sm">#{{ index + 1 }}</td>
                <td class="py-2 px-4 text-sm font-mono bg-gray-50">{{ testCase.input }}</td>
                <td class="py-2 px-4 text-sm font-mono bg-gray-50">{{ testCase.expectedOutput }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex justify-end gap-3 mt-6">
        <button 
          @click="showViewQuestionModal = false" 
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          关闭
        </button>
        <button 
          @click="editCurrentQuestion" 
          class="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 flex items-center gap-1"
        >
          <span class="material-icons text-sm">edit</span>
          编辑题目
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

// 教师数据
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 搜索和筛选
const searchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  courses: [],
  types: [],
  difficulties: []
})

// 模态框状态
const showCreateQuestionModal = ref(false)
const showEditQuestionModal = ref(false)
const showViewQuestionModal = ref(false)

// 当前操作的题目
const currentQuestion = ref({})

// 题目表单初始状态
const initialQuestionForm = {
  id: null,
  title: '',
  type: '单选题',
  difficulty: '中等',
  course: '',
  chapter: '',
  section: '',
  points: 2,
  options: ['', '', '', ''],
  answer: '',
  content: '',
  description: '',
  testCases: [],
  tags: []
}

// 题目表单
const questionForm = ref({...initialQuestionForm})

// 新标签输入
const newTagInput = ref('')

// 筛选选项
const courseOptions = ['Python程序设计', '人工智能基础', '数据结构', '计算机网络']
const questionTypes = ['单选题', '多选题', '填空题', '判断题', '简答题', '编程题']
const difficultyLevels = ['简单', '中等', '困难']

// 切换筛选器
const toggleFilter = (category, value) => {
  if (selectedFilters.value[category].includes(value)) {
    selectedFilters.value[category] = selectedFilters.value[category].filter(item => item !== value)
  } else {
    selectedFilters.value[category].push(value)
  }
}

// 课程知识结构数据
const courses = ref([
  {
    name: 'Python程序设计',
    expanded: true,
    chapters: [
      {
        name: '第一章 Python基础',
        expanded: true,
        sections: [
          { name: '1.1 Python环境搭建', id: 'py-1-1' },
          { name: '1.2 变量与数据类型', id: 'py-1-2' },
          { name: '1.3 控制结构', id: 'py-1-3' }
        ]
      },
      {
        name: '第二章 数据结构',
        expanded: false,
        sections: [
          { name: '2.1 列表和元组', id: 'py-2-1' },
          { name: '2.2 字典和集合', id: 'py-2-2' }
        ]
      }
    ]
  },
  {
    name: '人工智能基础',
    expanded: false,
    chapters: [
      {
        name: '第一章 人工智能概述',
        expanded: false,
        sections: [
          { name: '1.1 人工智能历史', id: 'ai-1-1' },
          { name: '1.2 人工智能应用', id: 'ai-1-2' }
        ]
      },
      {
        name: '第二章 机器学习基础',
        expanded: false,
        sections: [
          { name: '2.1 监督学习', id: 'ai-2-1' },
          { name: '2.2 无监督学习', id: 'ai-2-2' }
        ]
      }
    ]
  }
])

// 当前选中的章节
const selectedSection = ref({
  courseIndex: 0,
  chapterIndex: 0,
  sectionIndex: 0
})

// 切换课程展开/收起
const toggleCourse = (courseIndex) => {
  courses.value[courseIndex].expanded = !courses.value[courseIndex].expanded
}

// 切换章节展开/收起
const toggleChapter = (courseIndex, chapterIndex) => {
  courses.value[courseIndex].chapters[chapterIndex].expanded = !courses.value[courseIndex].chapters[chapterIndex].expanded
}

// 选择小节
const selectSection = (courseIndex, chapterIndex, sectionIndex) => {
  selectedSection.value = {
    courseIndex,
    chapterIndex,
    sectionIndex
  }
  // 这里可以加载该小节的题目
}

// 判断是否是当前选中的小节
const isSelectedSection = (courseIndex, chapterIndex, sectionIndex) => {
  return selectedSection.value.courseIndex === courseIndex &&
         selectedSection.value.chapterIndex === chapterIndex &&
         selectedSection.value.sectionIndex === sectionIndex
}

// 排序选项
const sortOption = ref('newest')

// 题目标签
const questionTags = ['基础', '提高', '挑战', '竞赛', '期中', '期末', '考研', 'Python', 'Java', 'C++']

// 题目展开状态
const expandedQuestions = ref([])

// 题目选择状态
const selectedQuestions = ref([])

// 批量操作菜单
const showBatchOperationMenu = ref(false)

// 题目筛选条件
const selectedQuestionType = ref('')
const selectedDifficulty = ref('')
const selectedTag = ref('')

// 添加或移除题目展开状态
const toggleQuestionExpand = (questionId) => {
  if (expandedQuestions.value.includes(questionId)) {
    expandedQuestions.value = expandedQuestions.value.filter(id => id !== questionId)
  } else {
    expandedQuestions.value.push(questionId)
  }
}

// 选择或取消选择题目
const toggleQuestionSelection = (questionId) => {
  if (selectedQuestions.value.includes(questionId)) {
    selectedQuestions.value = selectedQuestions.value.filter(id => id !== questionId)
  } else {
    selectedQuestions.value.push(questionId)
  }
}

// 批量导出选中的题目
const exportSelectedQuestions = () => {
  console.log('导出选中的题目:', selectedQuestions.value)
  showBatchOperationMenu.value = false
  // 这里添加导出逻辑
}

// 批量移动选中的题目
const moveSelectedQuestions = () => {
  console.log('移动选中的题目:', selectedQuestions.value)
  showBatchOperationMenu.value = false
  // 这里添加移动逻辑
}

// 批量删除选中的题目
const deleteSelectedQuestions = () => {
  if (selectedQuestions.value.length === 0) return
  
  deleteConfirmInfo.value = {
    type: 'batch-questions',
    name: `${selectedQuestions.value.length}个题目`,
    data: selectedQuestions.value
  }
  showDeleteConfirmModal.value = true
  showBatchOperationMenu.value = false
}

// 更新删除确认函数以处理批量删除
const confirmDelete = () => {
  const { type, courseIndex, chapterIndex, sectionIndex, data } = deleteConfirmInfo.value
  
  if (type === 'course') {
    courses.value.splice(courseIndex, 1)
  } else if (type === 'chapter') {
    courses.value[courseIndex].chapters.splice(chapterIndex, 1)
  } else if (type === 'section') {
    courses.value[courseIndex].chapters[chapterIndex].sections.splice(sectionIndex, 1)
  } else if (type === 'batch-questions') {
    // 批量删除题目
    questions.value = questions.value.filter(q => !data.includes(q.id))
    selectedQuestions.value = []
  } else if (type === 'single-question') {
    // 删除单个题目
    const index = questions.value.findIndex(q => q.id === data)
    if (index !== -1) {
      questions.value.splice(index, 1)
    }
  }
  
  showDeleteConfirmModal.value = false
}

// 删除单个题目
const deleteQuestion = (question) => {
  deleteConfirmInfo.value = {
    type: 'single-question',
    name: question.title,
    data: question.id
  }
  showDeleteConfirmModal.value = true
}

// 扩展题目数据，添加更多属性和类型
const questions = ref([
  {
    id: 1,
    title: '在Python中，以下哪种数据类型是不可变的？',
    type: '单选题',
    difficulty: '简单',
    course: 'Python程序设计',
    chapter: '第一章 Python基础',
    section: '1.2 变量与数据类型',
    points: 2,
    createdAt: '2023-06-15',
    createdBy: '李教授',
    usageCount: 56,
    options: ['列表', '字典', '集合', '元组'],
    answer: '元组',
    tags: ['基础', 'Python']
  },
  {
    id: 2,
    title: '以下哪些属于Python的基本数据类型？（多选）',
    type: '多选题',
    difficulty: '中等',
    course: 'Python程序设计',
    chapter: '第一章 Python基础',
    section: '1.2 变量与数据类型',
    points: 3,
    createdAt: '2023-06-18',
    createdBy: '李教授',
    usageCount: 42,
    options: ['int', 'str', 'book', 'float', 'char'],
    answer: ['int', 'str', 'float'],
    tags: ['基础', 'Python']
  },
  {
    id: 3,
    title: '编写一个Python函数计算斐波那契数列的第n项',
    type: '编程题',
    difficulty: '困难',
    course: 'Python程序设计',
    chapter: '第二章 数据结构',
    section: '2.1 列表和元组',
    points: 10,
    createdAt: '2023-07-05',
    createdBy: '李教授',
    usageCount: 35,
    description: '编写一个函数，接收一个整数n作为参数，返回斐波那契数列的第n项（从0开始计数）。',
    testCases: [
      { input: '0', expectedOutput: '0' },
      { input: '1', expectedOutput: '1' },
      { input: '10', expectedOutput: '55' }
    ],
    tags: ['提高', 'Python', '算法']
  },
  {
    id: 4,
    title: '填写以下Python代码中的空白，使其能够正确计算1到10的和',
    type: '填空题',
    difficulty: '中等',
    course: 'Python程序设计',
    chapter: '第一章 Python基础',
    section: '1.3 控制结构',
    points: 3,
    createdAt: '2023-07-10',
    createdBy: '李教授',
    usageCount: 65,
    content: 'sum = 0\nfor i in ___:\n    sum += i\nprint(sum)',
    answer: 'range(1, 11)',
    tags: ['基础', 'Python']
  },
  {
    id: 5,
    title: 'Python是一种编译型语言',
    type: '判断题',
    difficulty: '简单',
    course: 'Python程序设计',
    chapter: '第一章 Python基础',
    section: '1.1 Python环境搭建',
    points: 1,
    createdAt: '2023-08-01',
    createdBy: '李教授',
    usageCount: 78,
    answer: false,
    tags: ['基础', 'Python']
  },
  {
    id: 6,
    title: '简述人工智能与机器学习的区别',
    type: '简答题',
    difficulty: '中等',
    course: '人工智能基础',
    chapter: '第一章 人工智能概述',
    section: '1.1 人工智能历史',
    points: 5,
    createdAt: '2023-09-12',
    createdBy: '王教授',
    usageCount: 42,
    answer: '人工智能是研究如何使计算机模拟人类智能的一门学科，而机器学习是实现人工智能的一种方法，通过让机器从数据中学习规律和模式。机器学习是人工智能的子集。',
    tags: ['提高', '期中']
  },
  {
    id: 7,
    title: '以下哪种排序算法的平均时间复杂度是O(n log n)?',
    type: '单选题',
    difficulty: '中等',
    course: '数据结构',
    chapter: '第三章 排序算法',
    section: '3.2 高级排序算法',
    points: 2,
    createdAt: '2023-10-05',
    createdBy: '张教授',
    usageCount: 68,
    options: ['冒泡排序', '插入排序', '选择排序', '快速排序'],
    answer: '快速排序',
    tags: ['提高', '算法']
  }
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalQuestions = computed(() => filteredQuestions.value.length)
const totalPages = computed(() => Math.ceil(totalQuestions.value / pageSize.value))

// 根据过滤条件筛选题目
const filteredQuestions = computed(() => {
  let result = questions.value

  // 按搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(question => 
      question.title.toLowerCase().includes(query) || 
      question.course.toLowerCase().includes(query)
    )
  }

  // 按选中的筛选器筛选
  if (selectedFilters.value.courses.length > 0) {
    result = result.filter(question => selectedFilters.value.courses.includes(question.course))
  }
  
  if (selectedFilters.value.types.length > 0) {
    result = result.filter(question => selectedFilters.value.types.includes(question.type))
  }
  
  if (selectedFilters.value.difficulties.length > 0) {
    result = result.filter(question => selectedFilters.value.difficulties.includes(question.difficulty))
  }
  
  // 根据当前选择的节点筛选
  const { courseIndex, chapterIndex, sectionIndex } = selectedSection.value
  if (courseIndex !== null && chapterIndex !== null && sectionIndex !== null) {
    const course = courses.value[courseIndex]
    const chapter = course.chapters[chapterIndex]
    const section = chapter.sections[sectionIndex]
    
    result = result.filter(question => 
      question.course === course.name && 
      question.chapter === chapter.name && 
      question.section === section.name
    )
  }
  
  // 根据下拉菜单筛选
  if (selectedQuestionType.value) {
    result = result.filter(question => question.type === selectedQuestionType.value)
  }
  
  if (selectedDifficulty.value) {
    result = result.filter(question => question.difficulty === selectedDifficulty.value)
  }
  
  if (selectedTag.value) {
    result = result.filter(question => 
      question.tags && question.tags.includes(selectedTag.value)
    )
  }

  // 排序
  result = [...result].sort((a, b) => {
    switch (sortOption.value) {
      case 'newest':
        return new Date(b.createdAt) - new Date(a.createdAt)
      case 'difficulty-asc':
        return getDifficultyValue(a.difficulty) - getDifficultyValue(b.difficulty)
      case 'difficulty-desc':
        return getDifficultyValue(b.difficulty) - getDifficultyValue(a.difficulty)
      case 'usage':
        return b.usageCount - a.usageCount
      default:
        return 0
    }
  })

  return result
})

// 获取难度的数值表示，用于排序
const getDifficultyValue = (difficulty) => {
  const map = { '简单': 1, '中等': 2, '困难': 3 }
  return map[difficulty] || 0
}

// 分页后的题目列表
const displayQuestions = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredQuestions.value.slice(startIndex, startIndex + pageSize.value)
})

// 显示页码
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 获取题型样式
const getQuestionTypeClass = (type) => {
  switch (type) {
    case '单选题':
      return 'bg-blue-100 text-blue-800'
    case '多选题':
      return 'bg-indigo-100 text-indigo-800'
    case '填空题':
      return 'bg-green-100 text-green-800'
    case '判断题':
      return 'bg-purple-100 text-purple-800'
    case '简答题':
      return 'bg-orange-100 text-orange-800'
    case '编程题':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取难度样式
const getDifficultyClass = (difficulty) => {
  switch (difficulty) {
    case '简单':
      return 'bg-green-100 text-green-800'
    case '中等':
      return 'bg-yellow-100 text-yellow-800'
    case '困难':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 查看题目详情
const viewQuestion = (question) => {
  currentQuestion.value = {...question}
  showViewQuestionModal.value = true
}

// 编辑题目
const editQuestion = (question) => {
  // 深拷贝题目数据
  questionForm.value = JSON.parse(JSON.stringify(question))
  showEditQuestionModal.value = true
}

// 从查看模态框进入编辑模式
const editCurrentQuestion = () => {
  showViewQuestionModal.value = false
  editQuestion(currentQuestion.value)
}

// 知识结构管理模态框状态
const showAddCourseModal = ref(false)
const showEditCourseModal = ref(false)
const showAddChapterModal = ref(false)
const showEditChapterModal = ref(false)
const showAddSectionModal = ref(false)
const showEditSectionModal = ref(false)
const showDeleteConfirmModal = ref(false)

// 当前编辑项
const currentEditItem = ref({
  courseIndex: null,
  chapterIndex: null,
  sectionIndex: null,
  name: ''
})

// 删除确认信息
const deleteConfirmInfo = ref({
  type: '', // 'course', 'chapter', 'section'
  name: '',
  courseIndex: null,
  chapterIndex: null,
  sectionIndex: null
})

// 添加新课程
const addCourse = () => {
  currentEditItem.value = {
    name: '',
    courseIndex: null,
    chapterIndex: null,
    sectionIndex: null
  }
  showAddCourseModal.value = true
}

// 提交新课程
const submitNewCourse = () => {
  if (!currentEditItem.value.name.trim()) return
  
  courses.value.push({
    name: currentEditItem.value.name,
    expanded: false,
    chapters: []
  })
  
  showAddCourseModal.value = false
  currentEditItem.value.name = ''
}

// 编辑课程
const editCourse = (courseIndex) => {
  currentEditItem.value = {
    name: courses.value[courseIndex].name,
    courseIndex,
    chapterIndex: null,
    sectionIndex: null
  }
  showEditCourseModal.value = true
}

// 提交课程编辑
const submitCourseEdit = () => {
  if (!currentEditItem.value.name.trim()) return
  
  const { courseIndex } = currentEditItem.value
  courses.value[courseIndex].name = currentEditItem.value.name
  
  showEditCourseModal.value = false
}

// 删除课程
const deleteCourse = (courseIndex) => {
  deleteConfirmInfo.value = {
    type: 'course',
    name: courses.value[courseIndex].name,
    courseIndex,
    chapterIndex: null,
    sectionIndex: null
  }
  showDeleteConfirmModal.value = true
}

// 添加新章节
const addChapter = (courseIndex) => {
  currentEditItem.value = {
    name: '',
    courseIndex,
    chapterIndex: null,
    sectionIndex: null
  }
  showAddChapterModal.value = true
}

// 提交新章节
const submitNewChapter = () => {
  if (!currentEditItem.value.name.trim()) return
  
  const { courseIndex } = currentEditItem.value
  courses.value[courseIndex].chapters.push({
    name: currentEditItem.value.name,
    expanded: false,
    sections: []
  })
  
  // 确保课程是展开的
  courses.value[courseIndex].expanded = true
  
  showAddChapterModal.value = false
  currentEditItem.value.name = ''
}

// 编辑章节
const editChapter = (courseIndex, chapterIndex) => {
  currentEditItem.value = {
    name: courses.value[courseIndex].chapters[chapterIndex].name,
    courseIndex,
    chapterIndex,
    sectionIndex: null
  }
  showEditChapterModal.value = true
}

// 提交章节编辑
const submitChapterEdit = () => {
  if (!currentEditItem.value.name.trim()) return
  
  const { courseIndex, chapterIndex } = currentEditItem.value
  courses.value[courseIndex].chapters[chapterIndex].name = currentEditItem.value.name
  
  showEditChapterModal.value = false
}

// 删除章节
const deleteChapter = (courseIndex, chapterIndex) => {
  deleteConfirmInfo.value = {
    type: 'chapter',
    name: courses.value[courseIndex].chapters[chapterIndex].name,
    courseIndex,
    chapterIndex,
    sectionIndex: null
  }
  showDeleteConfirmModal.value = true
}

// 添加新小节
const addSection = (courseIndex, chapterIndex) => {
  currentEditItem.value = {
    name: '',
    courseIndex,
    chapterIndex,
    sectionIndex: null
  }
  showAddSectionModal.value = true
}

// 生成唯一ID
const generateSectionId = (courseIndex, chapterIndex, sectionName) => {
  const course = courses.value[courseIndex]
  const courseCode = course.name.substring(0, 2)
  const chapterNumber = chapterIndex + 1
  const sectionCount = course.chapters[chapterIndex].sections.length + 1
  return `${courseCode.toLowerCase()}-${chapterNumber}-${sectionCount}`
}

// 提交新小节
const submitNewSection = () => {
  if (!currentEditItem.value.name.trim()) return
  
  const { courseIndex, chapterIndex } = currentEditItem.value
  const sectionId = generateSectionId(courseIndex, chapterIndex, currentEditItem.value.name)
  
  courses.value[courseIndex].chapters[chapterIndex].sections.push({
    name: currentEditItem.value.name,
    id: sectionId
  })
  
  // 确保章节是展开的
  courses.value[courseIndex].chapters[chapterIndex].expanded = true
  
  showAddSectionModal.value = false
  currentEditItem.value.name = ''
}

// 编辑小节
const editSection = (courseIndex, chapterIndex, sectionIndex) => {
  currentEditItem.value = {
    name: courses.value[courseIndex].chapters[chapterIndex].sections[sectionIndex].name,
    courseIndex,
    chapterIndex,
    sectionIndex
  }
  showEditSectionModal.value = true
}

// 提交小节编辑
const submitSectionEdit = () => {
  if (!currentEditItem.value.name.trim()) return
  
  const { courseIndex, chapterIndex, sectionIndex } = currentEditItem.value
  courses.value[courseIndex].chapters[chapterIndex].sections[sectionIndex].name = currentEditItem.value.name
  
  showEditSectionModal.value = false
}

// 删除小节
const deleteSection = (courseIndex, chapterIndex, sectionIndex) => {
  deleteConfirmInfo.value = {
    type: 'section',
    name: courses.value[courseIndex].chapters[chapterIndex].sections[sectionIndex].name,
    courseIndex,
    chapterIndex,
    sectionIndex
  }
  showDeleteConfirmModal.value = true
}

// 页面加载完成时执行
onMounted(() => {
  // 可以在这里加载初始数据
  console.log('题库管理页面已加载')
})

// 根据课程获取可用章节
const availableChapters = computed(() => {
  if (!questionForm.value.course) return []
  const course = courses.value.find(c => c.name === questionForm.value.course)
  return course ? course.chapters : []
})

// 根据章节获取可用小节
const availableSections = computed(() => {
  if (!questionForm.value.course || !questionForm.value.chapter) return []
  const course = courses.value.find(c => c.name === questionForm.value.course)
  if (!course) return []
  const chapter = course.chapters.find(ch => ch.name === questionForm.value.chapter)
  return chapter ? chapter.sections : []
})

// 课程改变时重置章节和小节
const onCourseChange = () => {
  questionForm.value.chapter = ''
  questionForm.value.section = ''
}

// 章节改变时重置小节
const onChapterChange = () => {
  questionForm.value.section = ''
}

// 添加选项
const addOption = () => {
  questionForm.value.options.push('')
}

// 删除选项
const removeOption = (index) => {
  questionForm.value.options.splice(index, 1)
  
  // 如果删除的是选中的答案选项，重置答案
  if (questionForm.value.type === '单选题') {
    if (questionForm.value.answer === questionForm.value.options[index]) {
      questionForm.value.answer = ''
    }
  } else if (questionForm.value.type === '多选题') {
    questionForm.value.answer = questionForm.value.answer.filter(ans => 
      questionForm.value.options.includes(ans)
    )
  }
}

// 切换选项是否被选中（多选题）
const toggleAnswerSelection = (option) => {
  if (!Array.isArray(questionForm.value.answer)) {
    questionForm.value.answer = []
  }
  
  if (questionForm.value.answer.includes(option)) {
    questionForm.value.answer = questionForm.value.answer.filter(ans => ans !== option)
  } else {
    questionForm.value.answer.push(option)
  }
}

// 检查选项是否被选中
const isOptionSelected = (option) => {
  return Array.isArray(questionForm.value.answer) && questionForm.value.answer.includes(option)
}

// 添加测试用例
const addTestCase = () => {
  if (!Array.isArray(questionForm.value.testCases)) {
    questionForm.value.testCases = []
  }
  questionForm.value.testCases.push({ input: '', expectedOutput: '' })
}

// 删除测试用例
const removeTestCase = (index) => {
  questionForm.value.testCases.splice(index, 1)
}

// 切换题目标签
const toggleQuestionTag = (tag) => {
  if (!Array.isArray(questionForm.value.tags)) {
    questionForm.value.tags = []
  }
  
  if (questionForm.value.tags.includes(tag)) {
    questionForm.value.tags = questionForm.value.tags.filter(t => t !== tag)
  } else {
    questionForm.value.tags.push(tag)
  }
}

// 添加新标签
const addNewTag = () => {
  if (newTagInput.value.trim() && !questionTags.includes(newTagInput.value.trim())) {
    questionTags.push(newTagInput.value.trim())
    questionForm.value.tags.push(newTagInput.value.trim())
    newTagInput.value = ''
  }
}

// 关闭创建/编辑题目模态框
const closeQuestionModal = () => {
  showCreateQuestionModal.value = false
  showEditQuestionModal.value = false
  // 重置表单
  resetQuestionForm()
}

// 重置题目表单
const resetQuestionForm = () => {
  questionForm.value = {...initialQuestionForm}
  
  // 根据题型初始化特定字段
  if (questionForm.value.type === '多选题') {
    questionForm.value.answer = []
  } else if (questionForm.value.type === '判断题') {
    questionForm.value.answer = null
  }
}

// 提交题目表单
const submitQuestion = () => {
  // 表单验证
  if (!validateQuestionForm()) {
    return
  }
  
  const newQuestion = {...questionForm.value}
  
  // 生成ID (仅在创建时)
  if (!newQuestion.id) {
    newQuestion.id = Date.now()
    newQuestion.createdAt = new Date().toISOString().split('T')[0]
    newQuestion.createdBy = teacherData.value.name
    newQuestion.usageCount = 0
  }
  
  if (showEditQuestionModal.value) {
    // 编辑现有题目
    const index = questions.value.findIndex(q => q.id === newQuestion.id)
    if (index !== -1) {
      questions.value[index] = newQuestion
    }
  } else {
    // 创建新题目
    questions.value.push(newQuestion)
  }
  
  // 关闭模态框
  closeQuestionModal()
}

// 验证题目表单
const validateQuestionForm = () => {
  // 基本信息验证
  if (!questionForm.value.title.trim()) {
    alert('请输入题目标题')
    return false
  }
  
  if (!questionForm.value.course) {
    alert('请选择课程')
    return false
  }
  
  if (!questionForm.value.chapter) {
    alert('请选择章节')
    return false
  }
  
  if (!questionForm.value.section) {
    alert('请选择小节')
    return false
  }
  
  // 根据题型验证特定字段
  if (questionForm.value.type === '单选题' || questionForm.value.type === '多选题') {
    // 检查是否有空选项
    if (questionForm.value.options.some(opt => !opt.trim())) {
      alert('选项不能为空')
      return false
    }
    
    // 检查答案
    if (questionForm.value.type === '单选题' && !questionForm.value.answer) {
      alert('请选择正确答案')
      return false
    }
    
    if (questionForm.value.type === '多选题' && 
        (!Array.isArray(questionForm.value.answer) || questionForm.value.answer.length === 0)) {
      alert('请至少选择一个正确答案')
      return false
    }
  }
  
  if (questionForm.value.type === '判断题' && questionForm.value.answer === null) {
    alert('请选择判断题答案')
    return false
  }
  
  if (questionForm.value.type === '填空题' && !questionForm.value.content.trim()) {
    alert('请输入填空题内容')
    return false
  }
  
  if ((questionForm.value.type === '填空题' || questionForm.value.type === '简答题') && 
      !questionForm.value.answer.trim()) {
    alert('请输入参考答案')
    return false
  }
  
  if (questionForm.value.type === '编程题' && !questionForm.value.description.trim()) {
    alert('请输入题目描述')
    return false
  }
  
  return true
}

// 添加带有前置信息的题目
const addQuestionWithContext = () => {
  // 重置表单
  resetQuestionForm()
  
  // 如果已选择章节结构，预填表单
  if (selectedSection.value.courseIndex !== null && 
      selectedSection.value.chapterIndex !== null && 
      selectedSection.value.sectionIndex !== null) {
    
    const course = courses.value[selectedSection.value.courseIndex]
    const chapter = course.chapters[selectedSection.value.chapterIndex]
    const section = chapter.sections[selectedSection.value.sectionIndex]
    
    questionForm.value.course = course.name
    questionForm.value.chapter = chapter.name
    questionForm.value.section = section.name
  }
  
  // 显示创建题目模态框
  showCreateQuestionModal.value = true
}
</script>

<style scoped>
.group:hover .opacity-0 {
  opacity: 1;
}

/* 添加一些工具样式 */
.whitespace-pre-line {
  white-space: pre-line;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
</style> 