# Generated by Django 3.2.20 on 2025-04-24 02:51

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('phone', models.CharField(blank=True, max_length=11, null=True, verbose_name='手机号')),
                ('gender', models.CharField(blank=True, max_length=10, null=True, verbose_name='性别')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='头像')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'ordering': ['id'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='AIChat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='对话标题')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_chats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'AI对话',
                'verbose_name_plural': 'AI对话',
            },
        ),
        migrations.CreateModel(
            name='AIPrompt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='提示词标题')),
                ('content', models.TextField(verbose_name='提示词内容')),
                ('prompt_type', models.CharField(choices=[('GENERAL', '通用'), ('COURSE', '课程'), ('HOMEWORK', '作业'), ('KNOWLEDGE', '知识')], max_length=20, verbose_name='提示词类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'AI提示词',
                'verbose_name_plural': 'AI提示词',
            },
        ),
        migrations.CreateModel(
            name='Chapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='章节标题')),
                ('description', models.TextField(blank=True, verbose_name='章节描述')),
                ('order', models.IntegerField(verbose_name='排序')),
                ('video_url', models.URLField(blank=True, verbose_name='视频URL')),
                ('duration', models.IntegerField(default=0, verbose_name='视频时长(秒)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '章节',
                'verbose_name_plural': '章节',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='ClassGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='班级名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='班级代码')),
                ('grade', models.CharField(max_length=20, verbose_name='年级')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '班级',
                'verbose_name_plural': '班级',
            },
        ),
        migrations.CreateModel(
            name='College',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='学院名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='学院代码')),
                ('description', models.TextField(blank=True, verbose_name='学院描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '学院',
                'verbose_name_plural': '学院',
            },
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='课程名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='课程代码')),
                ('course_type', models.CharField(choices=[('REQUIRED', '必修课'), ('ELECTIVE', '选修课')], max_length=20, verbose_name='课程类型')),
                ('description', models.TextField(verbose_name='课程描述')),
                ('credits', models.DecimalField(decimal_places=1, max_digits=3, verbose_name='学分')),
                ('total_hours', models.IntegerField(verbose_name='总课时')),
                ('cover_image', models.ImageField(blank=True, upload_to='course_covers/', verbose_name='封面图片')),
                ('is_published', models.BooleanField(default=False, verbose_name='是否发布')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '课程',
                'verbose_name_plural': '课程',
            },
        ),
        migrations.CreateModel(
            name='Homework',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='作业标题')),
                ('description', models.TextField(verbose_name='作业描述')),
                ('start_time', models.DateTimeField(verbose_name='开始时间')),
                ('end_time', models.DateTimeField(verbose_name='截止时间')),
                ('total_score', models.IntegerField(verbose_name='总分')),
                ('is_online', models.BooleanField(default=False, verbose_name='是否在线作业')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='homeworks', to='zhkt.course')),
            ],
            options={
                'verbose_name': '作业',
                'verbose_name_plural': '作业',
            },
        ),
        migrations.CreateModel(
            name='KnowledgeBase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='知识库名称')),
                ('description', models.TextField(verbose_name='知识库描述')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_bases', to='zhkt.course')),
            ],
            options={
                'verbose_name': '知识库',
                'verbose_name_plural': '知识库',
            },
        ),
        migrations.CreateModel(
            name='KnowledgePoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='知识点标题')),
                ('content', models.TextField(verbose_name='知识点内容')),
                ('order', models.IntegerField(verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('knowledge_base', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_points', to='zhkt.knowledgebase')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='zhkt.knowledgepoint')),
            ],
            options={
                'verbose_name': '知识点',
                'verbose_name_plural': '知识点',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Major',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='专业名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='专业代码')),
                ('description', models.TextField(blank=True, verbose_name='专业描述')),
                ('duration', models.IntegerField(default=4, verbose_name='学制(年)')),
                ('degree', models.CharField(max_length=50, verbose_name='学位类型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('college', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='majors', to='zhkt.college')),
            ],
            options={
                'verbose_name': '专业',
                'verbose_name_plural': '专业',
            },
        ),
        migrations.CreateModel(
            name='Menu',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='菜单名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='菜单代码')),
                ('path', models.CharField(blank=True, max_length=200, null=True, verbose_name='路由路径')),
                ('component', models.CharField(blank=True, max_length=200, null=True, verbose_name='组件路径')),
                ('icon', models.CharField(blank=True, max_length=50, null=True, verbose_name='图标')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('type', models.CharField(choices=[('M', '目录'), ('C', '菜单'), ('F', '按钮')], max_length=20, verbose_name='类型')),
                ('permission', models.CharField(blank=True, max_length=100, null=True, verbose_name='权限标识')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='zhkt.menu', verbose_name='父级菜单')),
            ],
            options={
                'verbose_name': '菜单',
                'verbose_name_plural': '菜单',
                'ordering': ['order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='商品名称')),
                ('description', models.TextField(verbose_name='商品描述')),
                ('points_price', models.IntegerField(verbose_name='积分价格')),
                ('stock', models.IntegerField(verbose_name='库存数量')),
                ('image', models.ImageField(upload_to='products/', verbose_name='商品图片')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否上架')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '商品',
                'verbose_name_plural': '商品',
            },
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.CharField(max_length=20, unique=True, verbose_name='学号')),
                ('enrollment_date', models.DateField(verbose_name='入学日期')),
                ('expected_graduation_date', models.DateField(verbose_name='预计毕业日期')),
                ('status', models.CharField(max_length=20, verbose_name='学籍状态')),
                ('points', models.IntegerField(default=0, verbose_name='积分')),
                ('class_group', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students', to='zhkt.classgroup')),
                ('college', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students', to='zhkt.college')),
                ('major', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students', to='zhkt.major')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='student_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '学生',
                'verbose_name_plural': '学生',
            },
        ),
        migrations.CreateModel(
            name='Teacher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('teacher_id', models.CharField(max_length=20, unique=True, verbose_name='教师编号')),
                ('title', models.CharField(max_length=50, verbose_name='职称')),
                ('introduction', models.TextField(blank=True, verbose_name='个人简介')),
                ('college', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='teachers', to='zhkt.college')),
                ('majors', models.ManyToManyField(related_name='teachers', to='zhkt.Major')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '教师',
                'verbose_name_plural': '教师',
            },
        ),
        migrations.CreateModel(
            name='Submission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='提交内容')),
                ('file', models.FileField(blank=True, upload_to='homework_submissions/', verbose_name='提交文件')),
                ('status', models.CharField(choices=[('DRAFT', '草稿'), ('SUBMITTED', '已提交'), ('LATE', '迟交')], default='DRAFT', max_length=20, verbose_name='提交状态')),
                ('submitted_at', models.DateTimeField(blank=True, null=True, verbose_name='提交时间')),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='得分')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('homework', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='zhkt.homework')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='zhkt.student')),
            ],
            options={
                'verbose_name': '作业提交',
                'verbose_name_plural': '作业提交',
                'unique_together': {('homework', 'student')},
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='角色名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='角色代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='角色描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('menus', models.ManyToManyField(blank=True, to='zhkt.Menu', verbose_name='菜单权限')),
            ],
            options={
                'verbose_name': '角色',
                'verbose_name_plural': '角色',
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='Resource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='资源标题')),
                ('resource_type', models.CharField(choices=[('DOCUMENT', '文档'), ('VIDEO', '视频'), ('AUDIO', '音频'), ('OTHER', '其他')], max_length=20, verbose_name='资源类型')),
                ('file', models.FileField(upload_to='course_resources/', verbose_name='资源文件')),
                ('description', models.TextField(blank=True, verbose_name='资源描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('chapter', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='zhkt.chapter')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='zhkt.course')),
            ],
            options={
                'verbose_name': '课程资源',
                'verbose_name_plural': '课程资源',
            },
        ),
        migrations.CreateModel(
            name='PointsRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('points', models.IntegerField(verbose_name='积分数量')),
                ('record_type', models.CharField(choices=[('EARN', '获得'), ('SPEND', '消费'), ('ADJUST', '调整')], max_length=20, verbose_name='记录类型')),
                ('description', models.TextField(verbose_name='记录描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='points_records', to='zhkt.student')),
            ],
            options={
                'verbose_name': '积分记录',
                'verbose_name_plural': '积分记录',
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('points_cost', models.IntegerField(verbose_name='消耗积分')),
                ('status', models.CharField(choices=[('PENDING', '待处理'), ('COMPLETED', '已完成'), ('CANCELLED', '已取消')], default='PENDING', max_length=20, verbose_name='订单状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='zhkt.product')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='zhkt.student')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
            },
        ),
        migrations.CreateModel(
            name='Note',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='笔记标题')),
                ('content', models.TextField(verbose_name='笔记内容')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('knowledge_point', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='zhkt.knowledgepoint')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='zhkt.student')),
            ],
            options={
                'verbose_name': '学习笔记',
                'verbose_name_plural': '学习笔记',
            },
        ),
        migrations.AddField(
            model_name='knowledgebase',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_knowledge_bases', to='zhkt.teacher'),
        ),
        migrations.CreateModel(
            name='Feedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='反馈内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('submission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedbacks', to='zhkt.submission')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedbacks', to='zhkt.teacher')),
            ],
            options={
                'verbose_name': '作业反馈',
                'verbose_name_plural': '作业反馈',
            },
        ),
        migrations.CreateModel(
            name='Dept',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='机构名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='机构代码')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('leader', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='邮箱')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='zhkt.dept', verbose_name='父级机构')),
            ],
            options={
                'verbose_name': '机构',
                'verbose_name_plural': '机构',
                'ordering': ['order', 'id'],
            },
        ),
        migrations.AddField(
            model_name='course',
            name='major',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='zhkt.major'),
        ),
        migrations.AddField(
            model_name='course',
            name='teacher',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='zhkt.teacher'),
        ),
        migrations.AddField(
            model_name='classgroup',
            name='head_teacher',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_classes', to='zhkt.teacher'),
        ),
        migrations.AddField(
            model_name='classgroup',
            name='major',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='class_groups', to='zhkt.major'),
        ),
        migrations.AddField(
            model_name='chapter',
            name='course',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chapters', to='zhkt.course'),
        ),
        migrations.CreateModel(
            name='AIChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('USER', '用户'), ('ASSISTANT', '助手'), ('SYSTEM', '系统')], max_length=20, verbose_name='角色')),
                ('content', models.TextField(verbose_name='消息内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('chat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='zhkt.aichat')),
            ],
            options={
                'verbose_name': 'AI对话消息',
                'verbose_name_plural': 'AI对话消息',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='Admin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('admin_id', models.CharField(max_length=20, unique=True, verbose_name='管理员编号')),
                ('department', models.CharField(max_length=50, verbose_name='所属部门')),
                ('role', models.CharField(max_length=50, verbose_name='角色')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='admin_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '管理员',
                'verbose_name_plural': '管理员',
            },
        ),
        migrations.AddField(
            model_name='user',
            name='dept',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='zhkt.dept', verbose_name='所属机构'),
        ),
        migrations.AddField(
            model_name='user',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='zhkt_user_set', to='auth.Group', verbose_name='groups'),
        ),
        migrations.AddField(
            model_name='user',
            name='roles',
            field=models.ManyToManyField(blank=True, to='zhkt.Role', verbose_name='角色'),
        ),
        migrations.AddField(
            model_name='user',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions'),
        ),
        migrations.CreateModel(
            name='CourseEnrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enrolled_at', models.DateTimeField(auto_now_add=True, verbose_name='选课时间')),
                ('status', models.CharField(default='IN_PROGRESS', max_length=20, verbose_name='学习状态')),
                ('progress', models.FloatField(default=0, verbose_name='学习进度')),
                ('last_accessed', models.DateTimeField(auto_now=True, verbose_name='最后访问时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='zhkt.course')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='zhkt.student')),
            ],
            options={
                'verbose_name': '选课记录',
                'verbose_name_plural': '选课记录',
                'unique_together': {('student', 'course')},
            },
        ),
    ]
