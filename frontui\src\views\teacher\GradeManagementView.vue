<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="成绩分析"
    activePage="grade-management"
  >
    <div class="space-y-6">
      <!-- 功能区 -->
      <div class="flex flex-wrap justify-between items-center gap-4 mb-4">
        <div class="flex gap-3">
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
            @click="showAddGradeModal = true"
          >
            <span class="material-icons text-sm">add</span>
            录入成绩
          </button>
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_upload</span>
            批量导入
          </button>
          <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2">
            <span class="material-icons text-sm">file_download</span>
            导出数据
          </button>
          <button 
            class="bg-blue-50 hover:bg-blue-100 text-blue-700 py-2 px-4 rounded-md flex items-center gap-2"
            @click="generateAnalysisReport"
          >
            <span class="material-icons text-sm">analytics</span>
            生成分析报告
          </button>
        </div>
        <div class="flex gap-3">
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索学生或课程..." 
              class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
              <span class="material-icons text-gray-600">filter_list</span>
              筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <div class="space-y-3">
                <div class="font-medium">课程</div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="course in ['数据结构', '计算机网络', '人工智能', '软件工程']" 
                    :key="course"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer"
                    :class="selectedFilters.courses.includes(course) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('courses', course)"
                  >
                    {{ course }}
                  </span>
                </div>
                <div class="font-medium mt-3">考试类型</div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="type in ['期中考试', '期末考试', '平时作业', '实验报告']" 
                    :key="type"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer"
                    :class="selectedFilters.examTypes.includes(type) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('examTypes', type)"
                  >
                    {{ type }}
                  </span>
                </div>
                <div class="font-medium mt-3">学期</div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="term in ['2023春季', '2023秋季', '2024春季']" 
                    :key="term"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer"
                    :class="selectedFilters.terms.includes(term) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('terms', term)"
                  >
                    {{ term }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 整体表现分析卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-6 flex flex-col">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">班级平均分</h3>
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
              <span class="material-icons text-blue-600">school</span>
            </div>
          </div>
          <div class="text-3xl font-bold text-gray-900 mb-2">83.5</div>
          <div class="flex items-center text-sm">
            <span class="material-icons text-green-500 text-sm mr-1">trending_up</span>
            <span class="text-green-500 font-medium">2.3%</span>
            <span class="text-gray-500 ml-1">vs 上学期</span>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-6 flex flex-col">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">优秀人数</h3>
            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
              <span class="material-icons text-green-600">emoji_events</span>
            </div>
          </div>
          <div class="text-3xl font-bold text-gray-900 mb-2">28</div>
          <div class="flex items-center text-sm">
            <span class="text-gray-500">占比 <span class="font-medium text-gray-700">21.5%</span></span>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-6 flex flex-col">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">不及格人数</h3>
            <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
              <span class="material-icons text-red-600">warning</span>
            </div>
          </div>
          <div class="text-3xl font-bold text-gray-900 mb-2">15</div>
          <div class="flex items-center text-sm">
            <span class="material-icons text-red-500 text-sm mr-1">trending_up</span>
            <span class="text-red-500 font-medium">3.1%</span>
            <span class="text-gray-500 ml-1">vs 上学期</span>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 shadow-sm p-6 flex flex-col">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">作业完成率</h3>
            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
              <span class="material-icons text-purple-600">assignment_turned_in</span>
            </div>
          </div>
          <div class="text-3xl font-bold text-gray-900 mb-2">92.3%</div>
          <div class="flex items-center text-sm">
            <span class="material-icons text-green-500 text-sm mr-1">trending_up</span>
            <span class="text-green-500 font-medium">5.7%</span>
            <span class="text-gray-500 ml-1">vs 上学期</span>
          </div>
        </div>
      </div>

      <!-- 分析选项卡 -->
      <div class="flex border-b border-gray-200">
        <button 
          v-for="tab in analysisTabs" 
          :key="tab.value"
          @click="currentAnalysisTab = tab.value"
          class="py-3 px-6 font-medium relative"
          :class="currentAnalysisTab === tab.value ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'"
        >
          {{ tab.label }}
          <span 
            v-if="currentAnalysisTab === tab.value" 
            class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"
          ></span>
        </button>
      </div>

      <!-- 分析内容区 - 将会根据选择的选项卡切换内容 -->
      <div class="bg-white border border-gray-200 rounded-lg">
        <!-- 成绩总览分析 -->
        <div v-if="currentAnalysisTab === 'overview'" class="p-6">
          <h2 class="text-xl font-bold text-gray-800 mb-4">成绩分布</h2>
          
          <!-- 成绩分布图表占位 -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 h-80 mb-6 flex items-center justify-center">
            <div class="text-center">
              <span class="material-icons text-gray-400 text-4xl mb-2">bar_chart</span>
              <p class="text-gray-500">成绩分布图表将显示在这里</p>
            </div>
          </div>

          <!-- 数据卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-blue-50 rounded-lg p-4">
              <h3 class="text-sm font-medium text-gray-700 mb-1">最高分</h3>
              <p class="text-2xl font-bold text-gray-900">96</p>
              <p class="text-sm text-gray-500">张三 - 数据结构</p>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
              <h3 class="text-sm font-medium text-gray-700 mb-1">平均分</h3>
              <p class="text-2xl font-bold text-gray-900">83.5</p>
              <p class="text-sm text-gray-500">全班130人</p>
            </div>
            <div class="bg-yellow-50 rounded-lg p-4">
              <h3 class="text-sm font-medium text-gray-700 mb-1">最低分</h3>
              <p class="text-2xl font-bold text-gray-900">51</p>
              <p class="text-sm text-gray-500">李四 - 数据结构</p>
            </div>
          </div>
        </div>

        <!-- 学生成绩分析 -->
        <div v-if="currentAnalysisTab === 'students'" class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-800">学生表现分析</h2>
            
            <div class="flex gap-3">
              <select v-model="studentAnalysisFilter.courseId" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有课程</option>
                <option v-for="course in courseOptions" :key="course.id" :value="course.id">
                  {{ course.name }}
                </option>
              </select>
              
              <select v-model="studentAnalysisFilter.term" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有学期</option>
                <option v-for="term in termOptions" :key="term" :value="term">
                  {{ term }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- 学生表现搜索和筛选 -->
          <div class="mb-6 flex flex-wrap gap-3 items-center">
            <div class="relative flex-grow max-w-md">
              <input 
                type="text" 
                v-model="studentSearchQuery"
                placeholder="搜索学生姓名或学号..." 
                class="w-full border border-gray-300 rounded-md pl-9 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
            </div>
            
            <div>
              <select v-model="studentSortOption" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                <option value="scoreDesc">按分数从高到低</option>
                <option value="scoreAsc">按分数从低到高</option>
                <option value="nameAsc">按姓名排序</option>
                <option value="idAsc">按学号排序</option>
              </select>
            </div>
            
            <div>
              <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md text-sm flex items-center gap-1" @click="exportStudentPerformance">
                <span class="material-icons text-sm">file_download</span>
                导出分析
              </button>
            </div>
          </div>
          
          <!-- 学生表现表格 -->
          <div class="overflow-x-auto border border-gray-200 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学生</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均成绩</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最高分</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最低分</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作业完成率</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成绩趋势</th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(student, index) in filteredStudentAnalysis" :key="student.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full object-cover" :src="student.avatar" :alt="student.name" />
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ student.name }}</div>
                        <div class="text-xs text-gray-500">{{ student.id }} | {{ student.class }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium" :class="getScoreColorClass(student.averageScore)">
                      {{ student.averageScore }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ student.highestScore }}
                    <span class="text-xs text-gray-500">({{ student.highestScoreCourse }})</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ student.lowestScore }}
                    <span class="text-xs text-gray-500">({{ student.lowestScoreCourse }})</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                      <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${student.assignmentCompletionRate}%` }"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">{{ student.assignmentCompletionRate }}%</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <!-- 简化的趋势图表示（实际项目中可以使用迷你图表） -->
                    <div class="flex items-center gap-1 h-6">
                      <div v-for="(trend, i) in student.performanceTrend" :key="i" 
                           class="w-1.5 rounded-sm" 
                           :class="trend > 0 ? 'bg-green-500' : 'bg-red-500'"
                           :style="{ height: `${Math.abs(trend) * 20}%` }">
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900 mr-3" @click="viewStudentDetail(student)">详情</button>
                    <button class="text-gray-600 hover:text-gray-900" @click="generateStudentReport(student)">报告</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 作业分析 -->
        <div v-if="currentAnalysisTab === 'assignments'" class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-800">作业完成情况分析</h2>
            
            <div class="flex gap-3">
              <select v-model="assignmentAnalysisFilter.courseId" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有课程</option>
                <option v-for="course in courseOptions" :key="course.id" :value="course.id">
                  {{ course.name }}
                </option>
              </select>
              
              <select v-model="assignmentAnalysisFilter.type" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有类型</option>
                <option v-for="type in typeOptions" :key="type" :value="type">
                  {{ type }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- 作业完成率总览图表 -->
          <div class="bg-white border border-gray-200 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-3">作业提交率</h3>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 h-64 flex items-center justify-center">
              <div class="text-center">
                <span class="material-icons text-gray-400 text-4xl mb-2">insights</span>
                <p class="text-gray-500">作业提交率图表将显示在这里</p>
              </div>
            </div>
          </div>
          
          <!-- 作业列表 -->
          <h3 class="text-lg font-medium text-gray-700 mb-3">作业详情</h3>
          <div class="overflow-x-auto border border-gray-200 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作业名称</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">课程</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止日期</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交率</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均分</th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="assignment in filteredAssignmentAnalysis" :key="assignment.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ assignment.title }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" 
                      :class="{
                        'bg-purple-100 text-purple-800': assignment.type === '期末考试',
                        'bg-blue-100 text-blue-800': assignment.type === '期中考试',
                        'bg-green-100 text-green-800': assignment.type === '平时作业',
                        'bg-yellow-100 text-yellow-800': assignment.type === '实验报告'
                      }">
                      {{ assignment.type }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ assignment.course }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ assignment.dueDate }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                      <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${assignment.submissionRate}%` }"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">{{ assignment.submissionRate }}% ({{ assignment.submittedCount }}/{{ assignment.totalStudents }})</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium" :class="getScoreColorClass(assignment.averageScore)">
                      {{ assignment.averageScore }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900 mr-3" @click="viewAssignmentAnalysis(assignment)">分析</button>
                    <button class="text-gray-600 hover:text-gray-900" @click="viewAssignmentSubmissions(assignment)">提交记录</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 知识点分析 -->
        <div v-if="currentAnalysisTab === 'knowledge'" class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-800">知识点掌握情况</h2>
            
            <div class="flex gap-3">
              <select v-model="knowledgeAnalysisFilter.courseId" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有课程</option>
                <option v-for="course in courseOptions" :key="course.id" :value="course.id">
                  {{ course.name }}
                </option>
              </select>
              
              <select v-model="knowledgeAnalysisFilter.chapter" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有章节</option>
                <option v-for="(chapter, index) in availableChapters" :key="index" :value="chapter">
                  {{ chapter }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- 知识点掌握率热力图 -->
          <div class="bg-white border border-gray-200 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-3">班级知识点掌握热力图</h3>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 h-80 flex items-center justify-center">
              <div class="text-center">
                <span class="material-icons text-gray-400 text-4xl mb-2">grid_view</span>
                <p class="text-gray-500">知识点掌握热力图将显示在这里</p>
              </div>
            </div>
          </div>
          
          <!-- 知识点列表 -->
          <h3 class="text-lg font-medium text-gray-700 mb-3">知识点详情</h3>
          <div class="overflow-x-auto border border-gray-200 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">知识点</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属章节</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">课程</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">掌握人数</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">掌握率</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">难度系数</th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="point in filteredKnowledgePoints" :key="point.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ point.name }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ point.chapter }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ point.course }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ point.masteredCount }}/{{ point.totalStudents }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                      <div :class="getMasteryColorClass(point.masteryRate)" class="h-2.5 rounded-full" :style="{ width: `${point.masteryRate}%` }"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">{{ point.masteryRate }}%</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div class="h-full bg-orange-500 rounded-full" :style="{ width: `${point.difficulty * 20}%` }"></div>
                      </div>
                      <span class="ml-2 text-sm text-gray-600">{{ point.difficulty }}/5</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900 mr-3" @click="viewKnowledgePointDetail(point)">详情</button>
                    <button class="text-gray-600 hover:text-gray-900" @click="createReviewMaterial(point)">创建复习</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 题目分析 -->
        <div v-if="currentAnalysisTab === 'questions'" class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-800">题目难度与得分分析</h2>
            
            <div class="flex gap-3">
              <select v-model="questionAnalysisFilter.assignmentId" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有作业</option>
                <option v-for="assignment in assignmentOptions" :key="assignment.id" :value="assignment.id">
                  {{ assignment.title }}
                </option>
              </select>
              
              <select v-model="questionAnalysisFilter.type" class="border border-gray-300 rounded-md px-3 py-1.5 text-sm">
                <option value="">所有题型</option>
                <option v-for="type in questionTypes" :key="type" :value="type">
                  {{ type }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- 题目正确率散点图 -->
          <div class="bg-white border border-gray-200 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-gray-700 mb-3">题目难度与正确率关系</h3>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 h-80 flex items-center justify-center">
              <div class="text-center">
                <span class="material-icons text-gray-400 text-4xl mb-2">scatter_plot</span>
                <p class="text-gray-500">题目难度与正确率散点图将显示在这里</p>
              </div>
            </div>
          </div>
          
          <!-- 题目列表 -->
          <h3 class="text-lg font-medium text-gray-700 mb-3">题目表现详情</h3>
          <div class="overflow-x-auto border border-gray-200 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">题目</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属作业</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">难度</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分值</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">正确率</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">知识点</th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="question in filteredQuestionAnalysis" :key="question.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4">
                    <div class="text-sm font-medium text-gray-900 line-clamp-2 max-w-xs">{{ question.title }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" 
                      :class="getQuestionTypeClass(question.type)">
                      {{ question.type }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ question.assignment }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" 
                      :class="getDifficultyClass(question.difficulty)">
                      {{ question.difficulty }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ question.points }}分
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                      <div :class="getCorrectRateColorClass(question.correctRate)" class="h-2.5 rounded-full" :style="{ width: `${question.correctRate}%` }"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">{{ question.correctRate }}%</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <span 
                        v-for="(tag, i) in question.knowledgePoints" 
                        :key="i" 
                        class="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900 mr-3" @click="viewQuestionDetail(question)">详情</button>
                    <button class="text-gray-600 hover:text-gray-900" @click="viewQuestionAnswers(question)">学生答案</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 添加成绩模态框 -->
      <div v-if="showAddGradeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div class="bg-white rounded-lg max-w-xl w-full max-h-[90vh] overflow-y-auto shadow-xl" @click.stop>
          <!-- 模态框头部 -->
          <div class="bg-blue-50 p-6 border-b border-blue-100 flex justify-between items-center">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="material-icons text-blue-600">add</span>
              </div>
              <h2 class="text-xl font-bold text-gray-800">录入成绩</h2>
            </div>
            <button @click="showAddGradeModal = false" class="text-gray-500 hover:text-gray-700 transition-colors">
              <span class="material-icons text-xl">close</span>
            </button>
          </div>

          <!-- 模态框内容 -->
          <div class="p-6">
            <form @submit.prevent="submitGrade" class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <label class="text-sm font-medium text-gray-700">课程</label>
                  <select v-model="newGrade.courseId" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option v-for="course in courseOptions" :key="course.id" :value="course.id">
                      {{ course.name }}
                    </option>
                  </select>
                </div>
                <div class="space-y-2">
                  <label class="text-sm font-medium text-gray-700">学期</label>
                  <select v-model="newGrade.term" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option v-for="term in termOptions" :key="term" :value="term">
                      {{ term }}
                    </option>
                  </select>
                </div>
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">学生</label>
                <select v-model="newGrade.studentId" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option v-for="student in studentOptions" :key="student.id" :value="student.id">
                    {{ student.name }} ({{ student.id }})
                  </option>
                </select>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <label class="text-sm font-medium text-gray-700">考试类型</label>
                  <select v-model="newGrade.type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option v-for="type in typeOptions" :key="type" :value="type">
                      {{ type }}
                    </option>
                  </select>
                </div>
                <div class="space-y-2">
                  <label class="text-sm font-medium text-gray-700">成绩 (0-100)</label>
                  <input 
                    type="number" 
                    v-model="newGrade.score" 
                    min="0" 
                    max="100"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">评语 (选填)</label>
                <textarea 
                  v-model="newGrade.comments" 
                  rows="3"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="输入对该学生成绩的评语..."
                ></textarea>
              </div>

              <div class="pt-4 flex justify-end space-x-3">
                <button
                  type="button"
                  @click="showAddGradeModal = false"
                  class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  type="submit"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  保存
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'

// Teacher Data
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 显示状态控制
const searchQuery = ref('')
const showFilters = ref(false)
const showAddGradeModal = ref(false)
const currentAnalysisTab = ref('overview')
const currentPage = ref(1)
const pageSize = ref(10)

// 选择控制
const selectedGrades = ref([])
const selectedFilters = ref({
  courses: [],
  examTypes: [],
  terms: []
})

// 学生分析筛选
const studentSearchQuery = ref('')
const studentSortOption = ref('scoreDesc')
const studentAnalysisFilter = ref({
  courseId: '',
  term: ''
})

// 作业分析筛选
const assignmentAnalysisFilter = ref({
  courseId: '',
  type: ''
})

// 知识点分析筛选
const knowledgeAnalysisFilter = ref({
  courseId: '',
  chapter: ''
})

// 题目分析筛选
const questionAnalysisFilter = ref({
  assignmentId: '',
  type: ''
})

// 分析选项卡
const analysisTabs = ref([
  { label: '成绩总览', value: 'overview' },
  { label: '学生分析', value: 'students' },
  { label: '作业分析', value: 'assignments' },
  { label: '知识点分析', value: 'knowledge' },
  { label: '题目分析', value: 'questions' }
])

// 选项数据
const courseOptions = ref([
  { id: 1, name: '数据结构' },
  { id: 2, name: '计算机网络' },
  { id: 3, name: '人工智能' },
  { id: 4, name: '软件工程' }
])

const studentOptions = ref([
  { id: '2020001', name: '张三' },
  { id: '2020002', name: '李四' },
  { id: '2020003', name: '王五' },
  { id: '2020004', name: '赵六' },
])

const termOptions = ref(['2023春季', '2023秋季', '2024春季'])
const typeOptions = ref(['期中考试', '期末考试', '平时作业', '实验报告'])
const questionTypes = ref(['单选题', '多选题', '判断题', '填空题', '简答题', '编程题'])

// 模拟可用章节
const availableChapters = ref(['第一章 基础概念', '第二章 数据类型', '第三章 高级操作', '第四章 算法设计'])

// 新成绩数据
const newGrade = ref({
  courseId: '',
  studentId: '',
  term: '',
  type: '',
  score: '',
  comments: ''
})

// 模拟成绩数据
const grades = ref([
  {
    id: 1,
    student: {
      id: '2020001',
      name: '张三',
      class: '计算机1班',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
    },
    course: '数据结构',
    term: '2023秋季',
    type: '期末考试',
    score: 92,
    totalScore: 100,
    gradeTime: '2023-12-30 15:30'
  },
  {
    id: 2,
    student: {
      id: '2020002',
      name: '李四',
      class: '计算机1班',
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
    },
    course: '数据结构',
    term: '2023秋季',
    type: '期末考试',
    score: 85,
    totalScore: 100,
    gradeTime: '2023-12-30 15:35'
  },
  {
    id: 3,
    student: {
      id: '2020003',
      name: '王五',
      class: '计算机1班',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg'
    },
    course: '数据结构',
    term: '2023秋季',
    type: '期末考试',
    score: 78,
    totalScore: 100,
    gradeTime: '2023-12-30 15:40'
  },
  {
    id: 4,
    student: {
      id: '2020004',
      name: '赵六',
      class: '计算机1班',
      avatar: 'https://randomuser.me/api/portraits/men/4.jpg'
    },
    course: '数据结构',
    term: '2023秋季',
    type: '期末考试',
    score: 65,
    totalScore: 100,
    gradeTime: '2023-12-30 15:45'
  },
  {
    id: 5,
    student: {
      id: '2020005',
      name: '孙七',
      class: '计算机2班',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg'
    },
    course: '计算机网络',
    term: '2023秋季',
    type: '期中考试',
    score: 88,
    totalScore: 100,
    gradeTime: '2023-10-25 14:20'
  },
  {
    id: 6,
    student: {
      id: '2020006',
      name: '周八',
      class: '计算机2班',
      avatar: 'https://randomuser.me/api/portraits/men/6.jpg'
    },
    course: '计算机网络',
    term: '2023秋季',
    type: '期中考试',
    score: 76,
    totalScore: 100,
    gradeTime: '2023-10-25 14:25'
  },
  {
    id: 7,
    student: {
      id: '2020001',
      name: '张三',
      class: '计算机1班',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
    },
    course: '人工智能',
    term: '2024春季',
    type: '平时作业',
    score: 95,
    totalScore: 100,
    gradeTime: '2024-03-15 16:30'
  },
  {
    id: 8,
    student: {
      id: '2020002',
      name: '李四',
      class: '计算机1班',
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg'
    },
    course: '人工智能',
    term: '2024春季',
    type: '平时作业',
    score: 87,
    totalScore: 100,
    gradeTime: '2024-03-15 16:35'
  },
  {
    id: 9,
    student: {
      id: '2020007',
      name: '吴九',
      class: '软件工程1班',
      avatar: 'https://randomuser.me/api/portraits/women/1.jpg'
    },
    course: '软件工程',
    term: '2024春季',
    type: '实验报告',
    score: 92,
    totalScore: 100,
    gradeTime: '2024-04-05 11:30'
  },
  {
    id: 10,
    student: {
      id: '2020008',
      name: '郑十',
      class: '软件工程1班',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg'
    },
    course: '软件工程',
    term: '2024春季',
    type: '实验报告',
    score: 56,
    totalScore: 100,
    gradeTime: '2024-04-05 11:40'
  }
])

// 模拟学生分析数据
const studentAnalysisData = ref([
  {
    id: '2020001',
    name: '张三',
    class: '计算机1班',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    averageScore: 93.5,
    highestScore: 96,
    highestScoreCourse: '数据结构',
    lowestScore: 88,
    lowestScoreCourse: '计算机网络',
    assignmentCompletionRate: 100,
    performanceTrend: [2, 3, 1, -0.5, 1.5] // 正值表示上升，负值表示下降
  },
  {
    id: '2020002',
    name: '李四',
    class: '计算机1班',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    averageScore: 75.2,
    highestScore: 85,
    highestScoreCourse: '人工智能',
    lowestScore: 51,
    lowestScoreCourse: '数据结构',
    assignmentCompletionRate: 87,
    performanceTrend: [-1, -2, 1, 2, 3]
  },
  {
    id: '2020003',
    name: '王五',
    class: '计算机1班',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    averageScore: 82.6,
    highestScore: 90,
    highestScoreCourse: '软件工程',
    lowestScore: 75,
    lowestScoreCourse: '数据结构',
    assignmentCompletionRate: 95,
    performanceTrend: [1, 0.5, 0.5, -1, 2]
  },
  {
    id: '2020004',
    name: '赵六',
    class: '计算机1班',
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    averageScore: 68.3,
    highestScore: 76,
    highestScoreCourse: '人工智能',
    lowestScore: 62,
    lowestScoreCourse: '计算机网络',
    assignmentCompletionRate: 78,
    performanceTrend: [-1, -1.5, -0.5, 1, 2.5]
  }
])

// 模拟作业分析数据
const assignmentAnalysisData = ref([
  {
    id: 1,
    title: '第一章课后习题',
    type: '平时作业',
    course: '数据结构',
    dueDate: '2023-12-10',
    submissionRate: 95,
    submittedCount: 38,
    totalStudents: 40,
    averageScore: 87.5
  },
  {
    id: 2,
    title: '二叉树实验',
    type: '实验报告',
    course: '数据结构',
    dueDate: '2023-12-20',
    submissionRate: 90,
    submittedCount: 36,
    totalStudents: 40,
    averageScore: 82.3
  },
  {
    id: 3,
    title: '期中考试',
    type: '期中考试',
    course: '计算机网络',
    dueDate: '2023-10-25',
    submissionRate: 100,
    submittedCount: 35,
    totalStudents: 35,
    averageScore: 79.6
  },
  {
    id: 4,
    title: 'TCP/IP协议分析',
    type: '平时作业',
    course: '计算机网络',
    dueDate: '2023-11-15',
    submissionRate: 85,
    submittedCount: 30,
    totalStudents: 35,
    averageScore: 75.8
  }
])

// 作业选项
const assignmentOptions = computed(() => {
  return assignmentAnalysisData.value.map(a => ({
    id: a.id,
    title: a.title
  }))
})

// 模拟知识点数据
const knowledgePointsData = ref([
  {
    id: 1,
    name: '二叉树遍历',
    chapter: '第三章 树结构',
    course: '数据结构',
    masteredCount: 32,
    totalStudents: 40,
    masteryRate: 80,
    difficulty: 3.5
  },
  {
    id: 2,
    name: '快速排序算法',
    chapter: '第四章 排序算法',
    course: '数据结构',
    masteredCount: 28,
    totalStudents: 40,
    masteryRate: 70,
    difficulty: 4
  },
  {
    id: 3,
    name: 'TCP三次握手',
    chapter: '第二章 传输层协议',
    course: '计算机网络',
    masteredCount: 25,
    totalStudents: 35,
    masteryRate: 71.4,
    difficulty: 3
  },
  {
    id: 4,
    name: 'IP地址分类',
    chapter: '第三章 网络层',
    course: '计算机网络',
    masteredCount: 30,
    totalStudents: 35,
    masteryRate: 85.7,
    difficulty: 2
  }
])

// 模拟题目分析数据
const questionAnalysisData = ref([
  {
    id: 1,
    title: '完成二叉树的前序遍历算法实现',
    type: '编程题',
    assignment: '第一章课后习题',
    difficulty: '中等',
    points: 10,
    correctRate: 65,
    knowledgePoints: ['二叉树遍历', '递归算法']
  },
  {
    id: 2,
    title: '分析快速排序算法的时间复杂度',
    type: '简答题',
    assignment: '第一章课后习题',
    difficulty: '困难',
    points: 8,
    correctRate: 45,
    knowledgePoints: ['快速排序算法', '算法分析']
  },
  {
    id: 3,
    title: 'TCP和UDP的主要区别是什么？',
    type: '单选题',
    assignment: '期中考试',
    difficulty: '简单',
    points: 5,
    correctRate: 92,
    knowledgePoints: ['传输层协议', 'TCP/UDP']
  },
  {
    id: 4,
    title: '简述TCP三次握手的过程',
    type: '简答题',
    assignment: '期中考试',
    difficulty: '中等',
    points: 8,
    correctRate: 76,
    knowledgePoints: ['TCP三次握手', '连接建立']
  }
])

// 筛选计算属性
const filteredStudentAnalysis = computed(() => {
  let result = [...studentAnalysisData.value]
  
  // 按课程筛选
  if (studentAnalysisFilter.value.courseId) {
    // 实际实现中，这里应该根据选择的课程筛选学生
    console.log('按课程ID筛选:', studentAnalysisFilter.value.courseId)
  }
  
  // 按学期筛选
  if (studentAnalysisFilter.value.term) {
    // 实际实现中，这里应该根据选择的学期筛选学生
    console.log('按学期筛选:', studentAnalysisFilter.value.term)
  }
  
  // 搜索
  if (studentSearchQuery.value) {
    const query = studentSearchQuery.value.toLowerCase()
    result = result.filter(s => 
      s.name.toLowerCase().includes(query) || 
      s.id.toLowerCase().includes(query)
    )
  }
  
  // 排序
  result.sort((a, b) => {
    switch (studentSortOption.value) {
      case 'scoreDesc':
        return b.averageScore - a.averageScore
      case 'scoreAsc':
        return a.averageScore - b.averageScore
      case 'nameAsc':
        return a.name.localeCompare(b.name)
      case 'idAsc':
        return a.id.localeCompare(b.id)
      default:
        return 0
    }
  })
  
  return result
})

const filteredAssignmentAnalysis = computed(() => {
  let result = [...assignmentAnalysisData.value]
  
  // 按课程筛选
  if (assignmentAnalysisFilter.value.courseId) {
    const courseName = courseOptions.value.find(c => c.id === assignmentAnalysisFilter.value.courseId)?.name
    if (courseName) {
      result = result.filter(a => a.course === courseName)
    }
  }
  
  // 按类型筛选
  if (assignmentAnalysisFilter.value.type) {
    result = result.filter(a => a.type === assignmentAnalysisFilter.value.type)
  }
  
  return result
})

const filteredKnowledgePoints = computed(() => {
  let result = [...knowledgePointsData.value]
  
  // 按课程筛选
  if (knowledgeAnalysisFilter.value.courseId) {
    const courseName = courseOptions.value.find(c => c.id === knowledgeAnalysisFilter.value.courseId)?.name
    if (courseName) {
      result = result.filter(p => p.course === courseName)
    }
  }
  
  // 按章节筛选
  if (knowledgeAnalysisFilter.value.chapter) {
    result = result.filter(p => p.chapter === knowledgeAnalysisFilter.value.chapter)
  }
  
  return result
})

const filteredQuestionAnalysis = computed(() => {
  let result = [...questionAnalysisData.value]
  
  // 按作业筛选
  if (questionAnalysisFilter.value.assignmentId) {
    const assignment = assignmentOptions.value.find(a => a.id === questionAnalysisFilter.value.assignmentId)
    if (assignment) {
      result = result.filter(q => q.assignment === assignment.title)
    }
  }
  
  // 按题型筛选
  if (questionAnalysisFilter.value.type) {
    result = result.filter(q => q.type === questionAnalysisFilter.value.type)
  }
  
  return result
})

// 工具方法
const toggleFilter = (filterType, value) => {
  const index = selectedFilters.value[filterType].indexOf(value)
  if (index === -1) {
    selectedFilters.value[filterType].push(value)
  } else {
    selectedFilters.value[filterType].splice(index, 1)
  }
}

const toggleSelectGrade = (gradeId) => {
  const index = selectedGrades.value.indexOf(gradeId)
  if (index === -1) {
    selectedGrades.value.push(gradeId)
  } else {
    selectedGrades.value.splice(index, 1)
  }
}

const submitGrade = () => {
  // 在实际应用中，这里会发送请求到API
  console.log('提交成绩:', newGrade.value)
  
  // 添加到本地数据中进行演示
  const newId = Math.max(...grades.value.map(g => g.id)) + 1
  const course = courseOptions.value.find(c => c.id === newGrade.value.courseId)
  const student = studentOptions.value.find(s => s.id === newGrade.value.studentId)
  
  if (course && student) {
    grades.value.push({
      id: newId,
      student: {
        id: student.id,
        name: student.name,
        class: '计算机1班', // 假设班级
        avatar: `https://randomuser.me/api/portraits/men/${Math.floor(Math.random() * 10)}.jpg`
      },
      course: course.name,
      term: newGrade.value.term,
      type: newGrade.value.type,
      score: parseInt(newGrade.value.score),
      totalScore: 100,
      gradeTime: new Date().toLocaleString('zh-CN', { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '-')
    })
  }
  
  // 重置表单并关闭模态框
  newGrade.value = {
    courseId: '',
    studentId: '',
    term: '',
    type: '',
    score: '',
    comments: ''
  }
  
  showAddGradeModal.value = false
}

// 颜色类方法
const getScoreColorClass = (score) => {
  if (score < 60) return 'text-red-600'
  if (score < 75) return 'text-yellow-600'
  if (score < 90) return 'text-blue-600'
  return 'text-green-600'
}

const getQuestionTypeClass = (type) => {
  switch (type) {
    case '单选题': return 'bg-blue-100 text-blue-800'
    case '多选题': return 'bg-indigo-100 text-indigo-800'
    case '判断题': return 'bg-green-100 text-green-800'
    case '填空题': return 'bg-yellow-100 text-yellow-800'
    case '简答题': return 'bg-orange-100 text-orange-800'
    case '编程题': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getDifficultyClass = (difficulty) => {
  switch (difficulty) {
    case '简单': return 'bg-green-100 text-green-800'
    case '中等': return 'bg-yellow-100 text-yellow-800'
    case '困难': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getMasteryColorClass = (rate) => {
  if (rate < 60) return 'bg-red-500'
  if (rate < 75) return 'bg-yellow-500'
  if (rate < 90) return 'bg-blue-500'
  return 'bg-green-500'
}

const getCorrectRateColorClass = (rate) => {
  if (rate < 40) return 'bg-red-500'
  if (rate < 60) return 'bg-orange-500'
  if (rate < 80) return 'bg-yellow-500'
  return 'bg-green-500'
}

// 功能方法
const generateAnalysisReport = () => {
  console.log('生成分析报告')
  // 实际项目中这里会调用后端API生成报告
}

const exportStudentPerformance = () => {
  console.log('导出学生表现分析数据')
  // 实际项目中这里会执行导出操作
}

const viewStudentDetail = (student) => {
  console.log('查看学生详情', student)
  // 实际项目中这里会打开学生详情页面或模态框
}

const generateStudentReport = (student) => {
  console.log('生成学生个人报告', student)
  // 实际项目中这里会调用后端API生成学生个人报告
}

const viewAssignmentAnalysis = (assignment) => {
  console.log('查看作业分析', assignment)
  // 实际项目中这里会打开作业分析详情页面或模态框
}

const viewAssignmentSubmissions = (assignment) => {
  console.log('查看作业提交记录', assignment)
  // 实际项目中这里会打开作业提交记录页面或模态框
}

const viewKnowledgePointDetail = (point) => {
  console.log('查看知识点详情', point)
  // 实际项目中这里会打开知识点详情页面或模态框
}

const createReviewMaterial = (point) => {
  console.log('创建复习材料', point)
  // 实际项目中这里会打开创建复习材料的页面或模态框
}

const viewQuestionDetail = (question) => {
  console.log('查看题目详情', question)
  // 实际项目中这里会打开题目详情页面或模态框
}

const viewQuestionAnswers = (question) => {
  console.log('查看学生答案', question)
  // 实际项目中这里会打开学生答案页面或模态框
}

// 页面加载时执行
onMounted(() => {
  console.log('成绩分析页面已加载')
})
</script> 