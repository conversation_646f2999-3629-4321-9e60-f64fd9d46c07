from django.db import models
from django.utils.translation import gettext_lazy as _

from .subject import Subject


class PPTProject(models.Model):
    """PPT项目模型"""
    ppt_id = models.CharField(_('文多多PPT ID'), max_length=64)
    title = models.CharField(_('PPT标题'), max_length=255)
    description = models.TextField(_('PPT描述'), blank=True, null=True)
    subject = models.ForeignKey('Subject', on_delete=models.RESTRICT, related_name='ppt_projects', db_column='subject_id')
    slides_count = models.IntegerField(_('幻灯片数量'), default=0)
    user = models.ForeignKey('User', on_delete=models.CASCADE, related_name='ppt_projects')
    create_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    update_at = models.DateTimeField(_('更新时间'), auto_now=True)
    delete_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        db_table = 'zhkt_ppt_projects'
        verbose_name = _('PPT项目')
        verbose_name_plural = _('PPT项目')

    def __str__(self):
        return self.title

