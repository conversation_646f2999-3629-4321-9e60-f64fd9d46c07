<template>
  <div class="h-full pt-0 mt-0 flex flex-col bg-gray-800 border-r border-gray-700 w-64 transition-all duration-300" :class="{ 'collapsed': isCollapsed }">
    <!-- Logo and App Title -->
    <div class="flex items-center justify-center h-16 px-4 bg-gray-900">
      <router-link to="/admin/dashboard" class="flex items-center gap-2">
        <div class="flex items-center justify-center h-8 w-8 rounded-full bg-white text-gray-900">
          <el-icon><Monitor /></el-icon>
        </div>
        <span class="font-bold text-xl text-white" :class="{ 'hidden': isCollapsed }">智慧课堂管理系统</span>
      </router-link>
    </div>

    <!-- Navigation Links -->
    <div class="pt-0 mt-0 flex-1 overflow-y-auto">
      <nav class="px-2 py-4 space-y-1">
        <router-link 
          v-for="link in navLinks" 
          :key="link.id" 
          :to="link.path" 
          class="flex items-center px-2 py-2 text-sm font-medium rounded-md" 
          :class="currentPage === link.id ? 'text-white bg-gray-900' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
        >
          <el-icon :class="`text-lg ${isCollapsed ? 'mx-auto' : 'mr-3'}`"><component :is="link.icon" /></el-icon>
          <span :class="{ 'hidden': isCollapsed }">{{ link.text }}</span>
        </router-link>
      </nav>
    </div>

    <!-- Toggle Button -->
    <div class="p-2 border-t border-gray-700">
      <button class="w-full text-gray-300 hover:text-white text-sm py-1" @click="toggleSidebar">
        <template v-if="!isCollapsed">
          <el-icon class="mr-2"><ArrowLeft /></el-icon>收起菜单
        </template>
        <template v-else>
          <el-icon class="mx-auto"><ArrowRight /></el-icon>
        </template>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Monitor,
  User,
  Notebook,
  DataAnalysis,
  Setting,
  Bell,
  ArrowLeft,
  ArrowRight,
  Shop,
  Coin,
  Document
} from '@element-plus/icons-vue'

// Navigation configuration
const navLinks = [
  { id: 'dashboard', path: '/admin/dashboard', icon: 'Monitor', text: '控制台(测试)' },
  { id: 'users', path: '/admin/users', icon: 'User', text: '用户管理' },
  { id: 'courses', path: '/admin/courses', icon: 'Notebook', text: '课程管理(测试)' },
  { id: 'store', path: '/admin/store', icon: 'Shop', text: '商城管理' },
  { id: 'points', path: '/admin/points', icon: 'Coin', text: '积分管理(测试)' },
  { id: 'analytics', path: '/admin/analytics', icon: 'DataAnalysis', text: '数据分析(测试)' },
  { id: 'notifications', path: '/admin/notifications', icon: 'Bell', text: '通知管理(测试)' },
  { id: 'audit', path: '/admin/audit', icon: 'Document', text: '日志审计(测试)' },
  { id: 'settings', path: '/admin/settings', icon: 'Setting', text: '系统设置(测试)' }
]

const route = useRoute()
const isCollapsed = ref(false)

// Get the current page ID from the route
const currentPage = computed(() => {
  const path = route.path
  const segments = path.split('/')
  return segments[segments.length - 1]
})

// Toggle sidebar collapsed state
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}
</script>

<style scoped>
.collapsed {
  width: 64px !important; /* 4rem = 64px */
}
</style> 