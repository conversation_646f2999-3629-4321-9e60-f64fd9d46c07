<template>
  <view class="loading-container" v-if="visible">
    <view class="loading-mask"></view>
    <view class="loading-content">
      <view class="loading-spinner">
        <view class="loading-dot" v-for="i in 12" :key="i" :style="{ transform: `rotate(${i * 30}deg)` }">
          <view class="dot"></view>
        </view>
      </view>
      <text class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: '加载中...'
    }
  }
}
</script>

<style lang="scss">
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  
  .loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
  }
  
  .loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10rpx;
    padding: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .loading-spinner {
    position: relative;
    width: 60rpx;
    height: 60rpx;
    
    .loading-dot {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      
      .dot {
        width: 6rpx;
        height: 6rpx;
        background: #fff;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 50%;
        margin-left: -3rpx;
        opacity: 0;
        animation: loading 1.2s linear infinite;
      }
      
      @for $i from 1 through 12 {
        &:nth-child(#{$i}) .dot {
          animation-delay: #{($i - 1) * 0.1}s;
        }
      }
    }
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #fff;
    margin-top: 20rpx;
  }
}

@keyframes loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style> 