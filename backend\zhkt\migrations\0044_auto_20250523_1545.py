# Generated by Django 3.2.20 on 2025-05-23 15:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0043_ailecturedocument_cover_image_path'),
    ]

    operations = [
        migrations.CreateModel(
            name='AILectureSpeechStyle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('style_code', models.CharField(help_text='如classroom, grumpy, storytelling等', max_length=50, unique=True, verbose_name='风格代码')),
                ('style_name', models.CharField(max_length=100, verbose_name='风格名称')),
                ('description', models.CharField(max_length=255, verbose_name='风格描述')),
                ('prompt_template', models.TextField(help_text='提示词模板，使用{chapter_title}, {points}, {chapter_text}作为占位符', verbose_name='提示词模板')),
                ('is_system', models.BooleanField(default=False, help_text='是否为系统预设风格，系统预设风格不可删除', verbose_name='是系统预设')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'AI讲课语音风格',
                'verbose_name_plural': 'AI讲课语音风格',
                'db_table': 'zhkt_ai_lecture_speech_style',
            },
        ),
        migrations.AddField(
            model_name='ailecturedocument',
            name='speech_style',
            field=models.CharField(default='classroom', help_text='可选值: classroom, grumpy, storytelling, novel, memorial', max_length=50, verbose_name='语音风格'),
        ),
    ]
