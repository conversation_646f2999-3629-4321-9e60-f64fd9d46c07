from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from django.utils import timezone
from .base_model_view import BaseModelViewSet
from zhkt.serializers import (
    QuestionSerializer,
)
from zhkt.entitys import (
    Question, 
)

class QuestionViewSet(BaseModelViewSet):
    """题目视图集"""
    queryset = Question.objects.all()
    serializer_class = QuestionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)
            
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(tags__icontains=search)
            )
            
        # 题目类型筛选
        question_type = self.request.query_params.get('type')
        if question_type:
            queryset = queryset.filter(question_type=question_type)
            
        # 难度筛选
        difficulty = self.request.query_params.get('difficulty')
        if difficulty:
            queryset = queryset.filter(difficulty=difficulty)
            
        return queryset.select_related('course').prefetch_related('options')
    
    def create(self, request, *args, **kwargs):
        # 从请求数据中分离选项数据
        options_data = request.data.pop('options', [])
        serializer = self.get_serializer(
            data=request.data,
            context={'options': options_data}
        )
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    def update(self, request, *args, **kwargs):
        # 从请求数据中分离选项数据
        options_data = request.data.pop('options', [])
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(
            instance,
            data=request.data,
            context={'options': options_data},
            partial=partial
        )
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def batch_create(self, request):
        """批量创建题目"""
        questions_data = request.data.get('questions', [])
        created_questions = []
        
        for question_data in questions_data:
            options_data = question_data.pop('options', [])
            serializer = self.get_serializer(
                data=question_data,
                context={'options': options_data}
            )
            try:
                serializer.is_valid(raise_exception=True)
                question = serializer.save()
                created_questions.append(self.get_serializer(question).data)
            except Exception as e:
                return Response(
                    {'error': f'创建题目失败: {str(e)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        return Response(created_questions, status=status.HTTP_201_CREATED)
    
    @action(detail=False, methods=['post'])
    def batch_delete(self, request):
        """批量删除题目"""
        question_ids = request.data.get('question_ids', [])
        if not question_ids:
            return Response(
                {'error': '请提供要删除的题目ID列表'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            questions = Question.objects.filter(id__in=question_ids)
            questions.update(deleted_at=timezone.now())
            return Response({'message': f'成功删除{len(question_ids)}个题目'})
        except Exception as e:
            return Response(
                {'error': f'删除题目失败: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def export(self, request):
        """导出题目"""
        course_id = request.query_params.get('course_id')
        if not course_id:
            return Response(
                {'error': '请提供课程ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        questions = self.get_queryset().filter(course_id=course_id)
        serializer = self.get_serializer(questions, many=True)
        
        return Response({
            'course_id': course_id,
            'questions': serializer.data
        })