import request from '@/utils/request'

// 获取会话历史
export const getChatSession = (params) => {
  return request({
    url: '/ai-chats/',
    method: 'GET',
    data: params
  })
}

// 获取聊天历史
export const getChatMessage = (params) => {
  return request({
    url: '/ai-chat-messages/?chat=' + params.chat,
    method: 'GET'
  })
}

// 发送聊天消息
export const sendMessage = (data) => {
  return request({
    url: '/ai-chat-messages/',
    method: 'POST',
    data
  })
}

// 删除聊天记录
export const deleteChat = (data) => {
  return request({
    url: '/ai-chats/delete',
    method: 'POST',
    data
  })
}

// 语音识别
export const recognizeVoice = (data) => {
  return request({
    url: '/ai-chats/voice/recognize',
    method: 'POST',
    data
  })
} 