# Generated by Django 3.2.20 on 2025-06-18 18:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0065_product_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='DigitalHuman',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='数字人名称')),
                ('avatar_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='数字人头像路径')),
                ('description', models.TextField(blank=True, null=True, verbose_name='数字人描述')),
                ('status', models.CharField(choices=[('active', '已激活'), ('inactive', '未激活'), ('processing', '处理中')], default='inactive', max_length=20, verbose_name='数字人状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='digital_humans', to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '数字人',
                'verbose_name_plural': '数字人',
                'db_table': 'zhkt_digital_human',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Avatar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='化身名称')),
                ('avatar_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='化身图像路径')),
                ('video_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='视频文件路径')),
                ('description', models.TextField(blank=True, null=True, verbose_name='化身描述')),
                ('status', models.CharField(choices=[('active', '激活'), ('inactive', '未激活')], default='active', max_length=20, verbose_name='化身状态')),
                ('duration', models.IntegerField(default=0, verbose_name='视频时长(秒)')),
                ('usage_count', models.IntegerField(default=0, verbose_name='使用次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('digital_human', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='avatars', to='zhkt.digitalhuman', verbose_name='关联的数字人')),
            ],
            options={
                'verbose_name': '数字人化身',
                'verbose_name_plural': '数字人化身',
                'db_table': 'zhkt_avatar',
                'ordering': ['-created_at'],
            },
        ),
    ]
