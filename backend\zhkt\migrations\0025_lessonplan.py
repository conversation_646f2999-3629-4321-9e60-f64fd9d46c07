# Generated by Django 3.2.20 on 2025-05-08 14:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0024_knowledgedocument_file_path'),
    ]

    operations = [
        migrations.CreateModel(
            name='LessonPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='教学设计标题')),
                ('content_file_path', models.CharField(blank=True, max_length=255, null=True, verbose_name='教学设计内容文件路径')),
                ('lesson_template', models.CharField(default='标准教学模板', max_length=50, verbose_name='教案模板')),
                ('teaching_style', models.Char<PERSON>ield(default='平衡型（理论与实践并重）', max_length=50, verbose_name='教学风格')),
                ('specific_requirements', models.TextField(blank=True, null=True, verbose_name='具体要求')),
                ('material_file_path', models.CharField(blank=True, max_length=255, null=True, verbose_name='上传的教材文件路径')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_plans', to='zhkt.subject', verbose_name='关联学科')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_plans', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '教学设计',
                'verbose_name_plural': '教学设计',
                'db_table': 'zhkt_lesson_plan',
            },
        ),
    ]
