# Generated by Django 3.2.20 on 2025-05-06 07:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0018_lesson_subtitle_data'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='knowledgecategory',
            options={'ordering': ['sort_order'], 'verbose_name': '知识库主分类', 'verbose_name_plural': '知识库主分类'},
        ),
        migrations.AlterModelOptions(
            name='knowledgedataset',
            options={'verbose_name': '知识库子分类', 'verbose_name_plural': '知识库子分类'},
        ),
        migrations.AddField(
            model_name='knowledgecategory',
            name='category_type',
            field=models.CharField(choices=[('personal', '个人文档'), ('knowledge', '知识库')], default='knowledge', max_length=20, verbose_name='分类类型'),
        ),
        migrations.AddField(
            model_name='knowledgedataset',
            name='user_id',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='所属用户ID'),
        ),
        migrations.AddField(
            model_name='knowledgedocument',
            name='is_public',
            field=models.BooleanField(default=False, help_text='对于知识库文档，应设置为True；对于个人文档，应设置为False', verbose_name='是否公开文档'),
        ),
        migrations.AddField(
            model_name='knowledgedocument',
            name='user_id',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='文档所有者ID'),
        ),
        migrations.AlterField(
            model_name='knowledgecategory',
            name='name',
            field=models.CharField(max_length=255, verbose_name='大分类名称'),
        ),
        migrations.AlterField(
            model_name='knowledgedataset',
            name='embedding_model',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='嵌入模型名称'),
        ),
        migrations.AlterField(
            model_name='knowledgedataset',
            name='name',
            field=models.CharField(max_length=255, verbose_name='数据集名称'),
        ),
        migrations.AlterUniqueTogether(
            name='knowledgecategory',
            unique_together={('name', 'category_type')},
        ),
        migrations.AlterUniqueTogether(
            name='knowledgedataset',
            unique_together={('user_id', 'name', 'category')},
        ),
        migrations.CreateModel(
            name='KnowledgeFavorite',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='收藏ID')),
                ('user_id', models.CharField(max_length=64, verbose_name='用户ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='收藏时间')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to='zhkt.knowledgedocument', verbose_name='收藏的文档')),
            ],
            options={
                'verbose_name': '知识库收藏',
                'verbose_name_plural': '知识库收藏',
                'db_table': 'zhkt_kb_favorites',
                'unique_together': {('user_id', 'document')},
            },
        ),
    ]
