# Generated by Django 3.2.20 on 2025-04-28 08:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0007_auto_20250428_0917'),
    ]

    operations = [
        migrations.CreateModel(
            name='Lesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='课时标题')),
                ('description', models.TextField(blank=True, verbose_name='课时描述')),
                ('order', models.IntegerField(verbose_name='排序')),
                ('video_url', models.URLField(blank=True, verbose_name='视频URL')),
                ('duration', models.IntegerField(default=0, verbose_name='视频时长(秒)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('chapter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lessons', to='zhkt.chapter')),
            ],
            options={
                'verbose_name': '课时',
                'verbose_name_plural': '课时',
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='resource',
            name='lesson',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='zhkt.lesson'),
        ),
    ]
