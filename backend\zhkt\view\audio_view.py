import traceback
import requests

from rest_framework.decorators import action
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from django.http import HttpResponse, StreamingHttpResponse

from ..services.audio_service import AudioService
from ..utils.response import ResponseResult
from ..utils.file_utils import FileUtils


class AudioViewSet(ViewSet):
    """音频服务视图集"""
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    @action(detail=False, methods=['post'], url_path='upload')
    def upload_audio_file(self, request):
        """
        上传音频文件
        
        参数:
        - file: 音频文件
        - name: 音频名称（可选，默认使用文件名）
        - description: 音频描述（可选）
        
        返回:
        - 上传成功的音频信息
        """
        try:
            # 获取请求中的文件和数据
            file = request.FILES.get('file')
            if not file:
                return ResponseResult.error(
                    code=400,
                    message="未提供音频文件"
                )
            
            # 检查文件是否为音频
            if not file.content_type.startswith('audio/'):
                return ResponseResult.error(
                    code=400,
                    message="只能上传音频文件"
                )
            
            name = request.POST.get('name', file.name)
            description = request.POST.get('description', '')
            
            # 调用服务层处理上传
            audio = AudioService.upload_audio_file(
                user_id=request.user.id,
                file=file,
                name=name,
                description=description
            )
            
            return ResponseResult.success(
                data={
                    "id": audio.id,
                    "name": audio.name,
                    "audio_url": audio.audio_url,
                    "status": audio.status
                },
                message="音频上传成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"上传音频失败: {str(e)}"
            )

    @action(detail=False, methods=['post'], url_path='clone')
    def create_audio_clone(self, request):
        """
        创建声音克隆
        
        参数:
        - name: 声音名称
        - description: 声音描述（可选）
        - audio_file: 音频文件
        - avatar: 头像图片（可选）
        """
        try:
            # 获取请求中的文件和数据
            audio_file = request.FILES.get('audio_file')
            if not audio_file:
                return ResponseResult.error(
                    code=400,
                    message="未提供音频文件"
                )
            
            # 检查文件是否为音频
            if not audio_file.content_type.startswith('audio/'):
                return ResponseResult.error(
                    code=400,
                    message=f"只能上传音频文件，当前文件类型: {audio_file.content_type}"
                )
            
            name = request.POST.get('name')
            if not name:
                return ResponseResult.error(
                    code=400,
                    message="未提供声音名称"
                )
                
            description = request.POST.get('description')
            avatar = request.FILES.get('avatar')
            
            # 如果有头像，检查类型
            if avatar and not avatar.content_type.startswith('image/'):
                return ResponseResult.error(
                    code=400,
                    message="头像必须是图片文件"
                )
            
            # 调用服务层创建声音克隆
            voice_model = AudioService.create_audio_clone(
                user_id=request.user.id,
                name=name,
                description=description,
                audio_file=audio_file,
                avatar=avatar
            )
            
            return ResponseResult.success(
                data={
                    "id": voice_model.id,
                    "reference_id": voice_model.reference_id
                },
                message="声音克隆创建成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"创建声音克隆失败: {str(e)}"
            )

    @action(detail=False, methods=['get'], url_path='clones')
    def get_audio_clones(self, request):
        """
        获取用户的声音克隆列表
        
        查询参数:
        - page: 页码 (默认1)
        - page_size: 每页数量 (默认10)
        - search: 搜索关键词（可选）
        """
        try:
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            search = request.query_params.get('search')
            
            # calculate skip and limit from page and page_size
            skip = (page - 1) * page_size
            limit = page_size
            
            total, clones = AudioService.get_user_audio_clones(
                user_id=request.user.id,
                skip=skip,
                limit=limit,
                search=search
            )
            
            return ResponseResult.success(
                data={
                    "count": total,
                    "results": clones
                },
                message="获取声音克隆列表成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"获取声音克隆列表失败: {str(e)}"
            )

    @action(detail=False, methods=['get'], url_path='system-presets')
    def get_system_voice_presets(self, request):
        """
        获取系统预设声音列表
        """
        try:
            presets = AudioService.get_system_voice_presets()
            
            return ResponseResult.success(
                data=presets,
                message="获取系统预设声音列表成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"获取系统预设声音列表失败: {str(e)}"
            )

    @action(detail=True, methods=['put'], url_path='update')
    def update_audio_clone(self, request, pk=None):
        """
        更新声音克隆
        
        参数:
        - name: 声音名称（可选）
        - description: 声音描述（可选）
        - avatar: 头像图片（可选）
        """
        try:
            name = request.POST.get('name')
            description = request.POST.get('description')
            avatar = request.FILES.get('avatar')
            
            # 如果有头像，检查类型
            if avatar and not avatar.content_type.startswith('image/'):
                return ResponseResult.error(
                    code=400,
                    message="头像必须是图片文件"
                )
            
            # 调用服务层更新声音克隆
            updated = AudioService.update_audio_clone(
                clone_id=pk,
                user_id=request.user.id,
                name=name,
                description=description,
                avatar=avatar
            )
            
            if not updated:
                return ResponseResult.error(
                    code=404,
                    message="声音克隆不存在或无权限修改"
                )
            
            return ResponseResult.success(
                message="更新声音克隆成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"更新声音克隆失败: {str(e)}"
            )

    @action(detail=True, methods=['delete'], url_path='delete')
    def delete_audio_clone(self, request, pk=None):
        """删除声音克隆"""
        try:
            deleted = AudioService.delete_audio_clone(
                clone_id=pk,
                user_id=request.user.id
            )
            
            if not deleted:
                return ResponseResult.error(
                    code=404,
                    message="声音克隆不存在或无权限删除"
                )
            
            return ResponseResult.success(
                message="删除声音克隆成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"删除声音克隆失败: {str(e)}"
            )

    @action(detail=False, methods=['post'], url_path='generate')
    def generate_audio(self, request):
        """
        生成音频
        
        参数:
        - title: 音频标题
        - text_content: 文本内容
        - audio_clone_id: 使用的声音克隆ID
        - speed: 语速倍率（默认1.0）
        - model: 语音模型（默认speech-1.6）
        - copywriting_id: 关联的文案ID（可选）
        """
        try:
            title = request.POST.get('title')
            if not title:
                return ResponseResult.error(
                    code=400,
                    message="未提供音频标题"
                )
                
            text_content = request.POST.get('text_content')
            if not text_content:
                return ResponseResult.error(
                    code=400,
                    message="未提供文本内容"
                )
                
            audio_clone_id = request.POST.get('audio_clone_id')
            if not audio_clone_id:
                return ResponseResult.error(
                    code=400,
                    message="未提供声音克隆ID"
                )
                
            try:
                audio_clone_id = int(audio_clone_id)
            except ValueError:
                return ResponseResult.error(
                    code=400,
                    message="声音克隆ID必须是整数"
                )
                
            speed = request.POST.get('speed', 1.0)
            try:
                speed = float(speed)
            except ValueError:
                speed = 1.0
                
            model = request.POST.get('model', 'speech-1.6')
            if model not in ['speech-1.5', 'speech-1.6', 's1']:
                model = 'speech-1.6'  # 默认值
                
            copywriting_id = request.POST.get('copywriting_id')
            if copywriting_id:
                try:
                    copywriting_id = int(copywriting_id)
                except ValueError:
                    copywriting_id = None
            
            # 调用服务层生成音频
            audio = AudioService.generate_audio(
                user_id=request.user.id,
                title=title,
                text_content=text_content,
                audio_clone_id=audio_clone_id,
                speed=speed,
                model=model,
                copywriting_id=copywriting_id
            )
            
            return ResponseResult.success(
                data={
                    "audio_id": audio.id
                },
                message="音频生成成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"生成音频失败: {str(e)}"
            )

    @action(detail=False, methods=['get'], url_path='generated')
    def get_generated_audios(self, request):
        """
        获取用户生成的音频列表
        
        查询参数:
        - page: 页码 (默认1)
        - page_size: 每页数量 (默认10)
        """
        try:
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            skip = (page - 1) * page_size
            limit = page_size
            
            total, audios = AudioService.get_user_generated_audios(
                user_id=request.user.id,
                skip=skip,
                limit=limit
            )
            
            # 将音频对象转换为字典列表
            audio_list = []
            for audio in audios:
                audio_list.append({
                    'id': audio.id,
                    'name': audio.name,
                    'text_content': audio.text_content,
                    # 前端字段别名
                    'text': audio.text_content,
                    'audio_url': FileUtils.get_file_url(audio.audio_url),
                    'status': audio.status,
                    'speed': audio.speed,
                    'duration': audio.duration,
                    'created_at': audio.created_at,
                    'createdAt': audio.created_at,
                    'updated_at': audio.updated_at,
                    'voice_model_id': audio.voice_model_id,
                    'voice_model_name': audio.voice_model.name if audio.voice_model else None,
                    'model': audio.voice_model.name if audio.voice_model else None
                })
            
            return ResponseResult.success(
                data={
                    "total": total,
                    "count": total,  # 兼容前端字段名
                    "audios": audio_list,
                    "results": audio_list,  # 兼容前端字段名
                    "page": page,
                    "page_size": page_size
                },
                message="获取音频列表成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"获取音频列表失败: {str(e)}"
            )

    @action(detail=True, methods=['delete'], url_path='delete-generated')
    def delete_generated_audio(self, request, pk=None):
        """删除生成的音频"""
        try:
            deleted = AudioService.delete_generated_audio(
                audio_id=pk,
                user_id=request.user.id
            )
            
            if not deleted:
                return ResponseResult.error(
                    code=404,
                    message="音频不存在或无权限删除"
                )
            
            return ResponseResult.success(
                message="删除音频成功"
            )
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"删除音频失败: {str(e)}"
            )

    @action(detail=True, methods=['get'], url_path='download')
    def download_audio(self, request, pk=None):
        """
        下载音频文件（流式下载）
        
        参数:
        - pk: 音频ID
        
        返回:
        - 音频文件流用于下载
        """
        try:
            # 获取音频记录
            audio = AudioService.get_audio_by_id(pk, request.user.id)
            if not audio:
                return ResponseResult.error(
                    code=404,
                    message="音频不存在或无权限下载"
                )
            
            # 检查音频状态
            if audio.status != 'success':
                return ResponseResult.error(
                    code=400,
                    message="音频未成功生成，无法下载"
                )
            
            # 获取音频完整URL
            audio_url = FileUtils.get_file_url(audio.audio_url)
            if not audio_url:
                return ResponseResult.error(
                    code=404,
                    message="音频文件不存在"
                )
            
            # 处理文件名，确保有正确的扩展名和编码
            file_name = audio.name
            if not file_name.lower().endswith(('.mp3', '.wav', '.ogg', '.m4a')):
                # 从URL获取扩展名
                import os
                _, ext = os.path.splitext(audio.audio_url)
                if ext:
                    file_name += ext
                else:
                    file_name += '.mp3'  # 默认扩展名
            
            # 创建流式响应，直接从阿里云OSS获取并转发，不需要先下载到服务器
            try:
                # 创建流式请求
                r = requests.get(audio_url, stream=True, timeout=30)
                r.raise_for_status()
                
                # 创建流式响应
                response = StreamingHttpResponse(
                    streaming_content=(chunk for chunk in r.iter_content(chunk_size=8192)),
                    content_type=r.headers.get('Content-Type', 'audio/mpeg')
                )
                
                # 正确编码文件名，处理中文字符
                import urllib.parse
                encoded_filename = urllib.parse.quote(file_name.encode('utf-8'))
                
                # 设置必要的响应头，同时支持中文文件名
                response['Content-Disposition'] = f'attachment; filename="{file_name}"; filename*=UTF-8\'\'{encoded_filename}'
                
                # 如果原始响应包含Content-Length，也添加到我们的响应中
                if 'Content-Length' in r.headers:
                    response['Content-Length'] = r.headers['Content-Length']
                
                # 允许前端访问这些头信息
                response['Access-Control-Expose-Headers'] = 'Content-Disposition, Content-Length'
                
                return response
                
            except requests.exceptions.RequestException as e:
                traceback.print_exc()
                return ResponseResult.error(
                    code=500,
                    message=f"获取音频流失败: {str(e)}"
                )
            except Exception as e:
                traceback.print_exc()
                return ResponseResult.error(
                    code=500,
                    message=f"处理音频下载失败: {str(e)}"
                )
            
        except Exception as e:
            traceback.print_exc()
            return ResponseResult.error(
                code=500,
                message=f"下载音频失败: {str(e)}"
            ) 