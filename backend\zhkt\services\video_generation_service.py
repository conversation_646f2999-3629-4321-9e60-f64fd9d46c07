import os
import traceback
import uuid
from typing import Optional, List, Tuple

from django.conf import settings
from django.core.files.uploadedfile import UploadedFile
from django.utils import timezone
from django.db.models import Q

from ..entitys.digital_human import DigitalHuman
from ..entitys.generated_video import GeneratedVideo
from ..utils.file_utils import FileUtils
from ..utils.temp_file_utils import get_temp_filepath, clean_temp_file
from .audio_service import AudioService


class VideoGenerationService:
    """视频生成服务类"""

    @staticmethod
    def generate_video(
        user_id: int,
        name: str,
        digital_human_id: int,
        input_type: str,
        text_content: Optional[str] = None,
        audio_file: Optional[UploadedFile] = None,
        voice_id: Optional[int] = None,
        speed: float = 1.0,
        pitch: int = 0,
        folder_id: Optional[int] = None
    ) -> GeneratedVideo:
        """
        生成数字人视频
        
        Args:
            user_id: 用户ID
            name: 视频名称
            digital_human_id: 数字人ID
            input_type: 输入类型 ('text' 或 'audio')
            text_content: 文本内容（text类型时必填）
            audio_file: 音频文件（audio类型时必填）
            voice_id: 语音模型ID（text类型时必填）
            speed: 语速（text类型时有效）
            pitch: 音调（text类型时有效）
            folder_id: 文件夹ID（可选）
            
        Returns:
            创建的生成视频对象
        """
        try:
            # 验证数字人存在
            digital_human = DigitalHuman.objects.filter(
                id=digital_human_id,
                deleted_at__isnull=True
            ).first()
            
            if not digital_human:
                raise Exception("数字人不存在")
            
            # 验证用户权限（如果是用户创建的数字人，需要验证所有权）
            if digital_human.type == 'real' and digital_human.user_id != user_id:
                raise Exception("无权限使用此数字人")
            
            generated_audio_id = None
            uploaded_audio_url = None
            
            if input_type == 'text':
                # 文本输入：先生成音频
                if not text_content or not voice_id:
                    raise Exception("文本输入时，文本内容和语音模型ID不能为空")
                
                # 调用音频服务生成音频
                audio_generation = AudioService.generate_audio(
                    user_id=user_id,
                    title=f"{name}_音频",
                    text_content=text_content,
                    audio_clone_id=voice_id,
                    speed=speed
                )
                generated_audio_id = audio_generation.id
                
            elif input_type == 'audio':
                # 音频上传：保存音频文件
                if not audio_file:
                    raise Exception("音频上传时，音频文件不能为空")
                
                # 保存上传的音频文件
                audio_ext = os.path.splitext(getattr(audio_file, 'name', ''))[1]
                temp_audio_path = get_temp_filepath(suffix=audio_ext)
                with open(temp_audio_path, 'wb') as tmp_f:
                    for chunk in audio_file.chunks():
                        tmp_f.write(chunk)
                try:
                    uploaded_audio_url = FileUtils.save_audio_file(temp_audio_path)
                finally:
                    clean_temp_file(temp_audio_path)
            else:
                raise Exception("不支持的输入类型")
            
            # 创建生成视频记录
            generated_video = GeneratedVideo.objects.create(
                user_id=user_id,
                name=name,
                digital_human_id=digital_human_id,
                input_type=input_type,
                audio_id=generated_audio_id,
                uploaded_audio_url=uploaded_audio_url,
                text_content=text_content if input_type == 'text' else None,
                status='processing',
                tags=str(folder_id) if folder_id else None  # 将文件夹ID存储在tags字段中
            )
            
            # 更新数字人使用次数
            from django.db import models
            DigitalHuman.objects.filter(
                id=digital_human_id,
                deleted_at__isnull=True
            ).update(usage_count=models.F('usage_count') + 1)
            
            # 异步启动LatentSync视频生成任务
            VideoGenerationService._start_latent_sync_task(generated_video)
            
            return generated_video
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"生成视频失败: {str(e)}")
    
    @staticmethod
    def get_user_generated_videos(
        user_id: int,
        skip: int = 0,
        limit: int = 10,
        search: Optional[str] = None,
        folder_id: Optional[int] = None,
        status_filter: Optional[str] = None
    ) -> Tuple[int, List[GeneratedVideo]]:
        """
        获取用户生成的视频列表
        
        Args:
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数
            search: 搜索关键词，可选
            folder_id: 文件夹ID，可选
            status_filter: 状态筛选，可选
            
        Returns:
            (总数, 视频列表)
        """
        try:
            # 创建基础查询
            base_query = GeneratedVideo.objects.filter(
                user_id=user_id,
                deleted_at__isnull=True
            )
            
            # 添加文件夹筛选
            if folder_id is not None:
                base_query = base_query.filter(tags=str(folder_id))
            else:
                # 如果folder_id为None，筛选出tags为空或None的记录（根目录）
                base_query = base_query.filter(Q(tags__isnull=True) | Q(tags=''))
            
            # 添加搜索条件
            if search:
                base_query = base_query.filter(name__icontains=search)
            
            # 添加状态筛选
            if status_filter and status_filter != 'all':
                status_map = {
                    'completed': 'success',
                    'processing': 'processing',
                    'failed': 'failed'
                }
                actual_status = status_map.get(status_filter, status_filter)
                base_query = base_query.filter(status=actual_status)
            
            # 获取总数
            total = base_query.count()
            
            # 获取分页数据
            videos = base_query.order_by('-create_time')[skip:skip+limit]
            
            return total, videos
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取生成视频列表失败: {str(e)}")
    
    @staticmethod
    def update_generated_video(
        user_id: int,
        video_id: int,
        name: Optional[str] = None,
        folder_id: Optional[int] = None
    ) -> Optional[GeneratedVideo]:
        """
        更新生成的视频信息
        
        Args:
            user_id: 用户ID
            video_id: 视频ID
            name: 新的视频名称，可选
            folder_id: 新的文件夹ID，可选
            
        Returns:
            更新后的视频对象或None
        """
        try:
            video = GeneratedVideo.objects.filter(
                id=video_id,
                user_id=user_id,
                deleted_at__isnull=True
            ).first()
            
            if not video:
                return None
            
            # 更新名称
            if name is not None:
                video.name = name
            
            # 更新文件夹
            if folder_id is not None:
                video.tags = str(folder_id)
            elif folder_id is None and 'folder_id' in locals():
                # 如果明确传入None，清空文件夹
                video.tags = None
            
            video.save()
            return video
            
        except Exception as e:
            traceback.print_exc()
            return None
    
    @staticmethod
    def delete_generated_video(user_id: int, video_id: int) -> bool:
        """
        删除生成的视频（软删除）
        
        Args:
            user_id: 用户ID
            video_id: 视频ID
            
        Returns:
            是否删除成功
        """
        try:
            video = GeneratedVideo.objects.filter(
                id=video_id,
                user_id=user_id,
                deleted_at__isnull=True
            ).first()
            
            if not video:
                return False
            
            # 删除相关文件
            if video.uploaded_audio_url:
                FileUtils.delete_file(video.uploaded_audio_url)
            if video.video_url:
                FileUtils.delete_file(video.video_url)
            if video.cover_image:
                FileUtils.delete_file(video.cover_image)
            
            # 软删除
            video.deleted_at = timezone.now()
            video.save()
            
            return True
            
        except Exception as e:
            traceback.print_exc()
            return False
    
    @staticmethod
    def get_video_statistics(user_id: int) -> dict:
        """
        获取用户视频统计数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            统计数据字典
        """
        try:
            base_query = GeneratedVideo.objects.filter(
                user_id=user_id,
                deleted_at__isnull=True
            )
            
            total = base_query.count()
            processing = base_query.filter(status='processing').count()
            completed = base_query.filter(status='success').count()
            failed = base_query.filter(status='failed').count()
            
            return {
                'total': total,
                'processing': processing,
                'completed': completed,
                'failed': failed
            }
            
        except Exception as e:
            traceback.print_exc()
            return {
                'total': 0,
                'processing': 0,
                'completed': 0,
                'failed': 0
            }
    
    @staticmethod
    def _start_latent_sync_task(generated_video: GeneratedVideo) -> None:
        """
        启动LatentSync视频生成任务
        
        Args:
            generated_video: 生成视频对象
        """
        try:
            from ..tasks.latent_sync_task import process_latent_sync_video_task
            from ..entitys.digital_human import DigitalHuman
            from .audio_service import AudioService
            
            # 获取数字人信息
            digital_human = DigitalHuman.objects.filter(
                id=generated_video.digital_human_id,
                deleted_at__isnull=True
            ).first()
            
            if not digital_human or not digital_human.video_url:
                raise Exception("数字人视频文件不存在")
            
            # 获取音频URL
            audio_url = None
            if generated_video.input_type == 'text' and generated_video.audio_id:
                # 从音频生成记录获取音频文件
                from ..entitys.audio_model import AudioGenerationHistory
                try:
                    audio_generation = AudioGenerationHistory.objects.get(
                        id=generated_video.audio_id,
                        deleted_at__isnull=True
                    )
                    if audio_generation and audio_generation.audio_url:
                        audio_url = audio_generation.audio_url
                    else:
                        raise Exception("音频记录不包含有效的音频URL")
                except AudioGenerationHistory.DoesNotExist:
                    raise Exception(f"音频记录不存在，ID: {generated_video.audio_id}")
            elif generated_video.input_type == 'audio' and generated_video.uploaded_audio_url:
                # 直接使用上传的音频文件
                audio_url = generated_video.uploaded_audio_url
            
            if not audio_url:
                raise Exception("音频URL不存在")
            
            # 获取数字人视频URL
            video_url = digital_human.video_url
            if not video_url:
                raise Exception("数字人视频URL不存在")
            
            # 异步启动LatentSync任务
            process_latent_sync_video_task.delay(
                generated_video_id=generated_video.id,
                video_url=video_url,
                audio_url=audio_url,
                guidance_scale=2.0,  # 默认引导比例
                inference_steps=20   # 默认推理步数
            )
            
        except Exception as e:
            # 如果启动任务失败，更新视频状态为失败
            try:
                generated_video.status = 'failed'
                generated_video.save()
            except:
                pass
            raise Exception(f"启动LatentSync任务失败: {str(e)}") 