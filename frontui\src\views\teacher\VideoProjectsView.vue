<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="AI视频合成"
    activePage="content-creation"
    activeSubPage="video"
  >
    <div class="p-4 bg-gray-50 min-h-full">
      <div class="max-w-screen-2xl mx-auto">
        <!-- 主要工作区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
          
          <!-- 左侧：数字人与内容配置 -->
          <div class="lg:col-span-2 space-y-4">
            <!-- 1. 选择数字人 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
              <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-800 flex items-center">
                  <span class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-3">1</span>
                  选择数字人
                </h3>
              </div>
              
              <div class="p-4">
                <div v-if="!selectedDigitalTeacher" class="flex flex-col items-center justify-center py-10 text-center">
                  <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="material-icons text-4xl text-gray-400">person_add</i>
                  </div>
                  <h4 class="text-base font-medium text-gray-700 mb-2">请选择一个数字人</h4>
                  <p class="text-sm text-gray-500 mb-6">只有已激活状态的数字人才能用于视频生成</p>
                  <button 
                    class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-5 rounded-lg font-medium flex items-center gap-2 text-sm"
                    @click="showDigitalTeacherSelector = true"
                  >
                    <i class="material-icons text-sm">smart_toy</i>
                    立即选择
                  </button>
                </div>
                
                <div v-else class="space-y-4">
                  <div class="relative group">
                    <div class="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden shadow-inner">
                      <img 
                        :src="selectedDigitalTeacher.avatar" 
                        alt="数字人预览" 
                        class="w-full h-full object-contain transition-transform duration-300"
                      />
                      <div v-if="isGenerating" class="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center backdrop-blur-sm">
                        <div class="text-white text-center">
                          <div class="animate-spin w-12 h-12 border-4 border-white border-t-transparent rounded-full mx-auto mb-4"></div>
                          <p class="font-medium text-lg mb-2">视频生成中...</p>
                          <p class="text-sm opacity-80">请稍候，正在处理您的数字人视频</p>
                        </div>
                      </div>
                    </div>
                    <button 
                      @click="showDigitalTeacherSelector = true"
                      class="absolute top-3 right-3 bg-white bg-opacity-90 hover:bg-opacity-100 text-blue-600 p-2.5 rounded-full transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-110"
                      title="更换数字人"
                    >
                      <i class="material-icons text-lg">edit</i>
                    </button>
                  </div>
                  
                  <div class="text-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                    <h4 class="font-semibold text-lg text-gray-800 mb-1">{{ selectedDigitalTeacher.name }}</h4>
                    <div class="flex items-center justify-center gap-4 text-sm text-gray-600">
                      <span class="flex items-center">
                        <i class="material-icons text-sm mr-1 text-blue-500">category</i>
                        {{ selectedDigitalTeacher.category || '自定义数字人' }}
                      </span>
                      <span class="flex items-center">
                        <i class="material-icons text-sm mr-1 text-green-500">check_circle</i>
                        已激活可用
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 2. 内容配置 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
              <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-800 flex items-center">
                  <span class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-3">2</span>
                  内容配置
                </h3>
              </div>
              <div class="p-4 space-y-4">
                   <!-- 输入方式选择 -->
                   <div class="flex justify-start">
                     <div class="bg-gray-100 p-1 rounded-xl inline-flex">
                       <button
                         @click="contentInputType = 'text'"
                         :class="[
                           'px-5 py-2 rounded-lg text-sm font-medium flex items-center gap-2',
                           contentInputType === 'text'
                             ? 'bg-white text-blue-600 shadow-sm'
                             : 'text-gray-600 hover:text-gray-800'
                         ]"
                       >
                         <i class="material-icons text-base">text_fields</i>
                         文本输入
                       </button>
                       <button
                         @click="contentInputType = 'audio'"
                         :class="[
                           'px-5 py-2 rounded-lg text-sm font-medium flex items-center gap-2',
                           contentInputType === 'audio'
                             ? 'bg-white text-blue-600 shadow-sm'
                             : 'text-gray-600 hover:text-gray-800'
                         ]"
                       >
                         <i class="material-icons text-base">upload_file</i>
                         上传音频
                       </button>
                     </div>
                   </div>

                  <!-- 文本输入区域 -->
                  <div v-if="contentInputType === 'text'" class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">输入或粘贴文本</label>
                      <div class="relative">
                        <textarea 
                          v-model="textContent"
                          rows="8"
                          class="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none text-sm"
                          placeholder="请输入要生成视频的文本内容..."
                        ></textarea>
                        <div class="absolute bottom-3 right-3 flex items-center space-x-4 text-xs text-gray-500 bg-white/70 backdrop-blur-sm px-2 py-1 rounded-lg">
                          <span>{{ textContent.length }} 字符</span>
                          <span>预计 {{ estimatedDuration }} 秒</span>
                        </div>
                      </div>
                    </div>

                    <!-- 高级设置 -->
                    <div class="bg-gray-100 rounded-xl p-3">
                      <details class="group">
                        <summary class="flex items-center justify-between cursor-pointer">
                          <span class="text-sm font-medium text-gray-700">高级音频设置</span>
                           <i class="material-icons transition-transform duration-300 group-open:rotate-180">expand_more</i>
                        </summary>
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">语速调节</label>
                            <div class="flex items-center space-x-2">
                              <input 
                                type="range" 
                                min="0.5" 
                                max="2" 
                                step="0.1" 
                                v-model="advancedSettings.speed" 
                                class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer range-thumb-blue"
                              />
                              <span class="text-xs font-medium text-gray-700 min-w-[40px] text-center">{{ advancedSettings.speed }}×</span>
                            </div>
                          </div>
                          <!-- <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">音调调节</label>
                            <div class="flex items-center space-x-2">
                              <input 
                                type="range" 
                                min="-10" 
                                max="10" 
                                v-model="advancedSettings.pitch" 
                                class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer range-thumb-blue"
                              />
                              <span class="text-xs font-medium text-gray-700 min-w-[40px] text-center">{{ advancedSettings.pitch }}</span>
                            </div>
                          </div> -->
                        </div>
                      </details>
                    </div>
                  </div>

                  <!-- 音频上传区域 -->
                  <div v-else class="space-y-4">
                    <label class="block text-sm font-medium text-gray-700">上传音频驱动</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-2xl p-6 text-center hover:border-blue-400 transition-colors">
                      <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="material-icons text-blue-500 text-3xl">audiotrack</i>
                      </div>
                      <p class="text-sm font-medium text-gray-700 mb-1">将文件拖放到此处或点击上传</p>
                      <p class="text-xs text-gray-500 mb-3">支持MP3, WAV, AAC格式, 最大50MB</p>
                      <label class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg cursor-pointer inline-flex items-center gap-2 text-sm">
                        <i class="material-icons text-sm">upload</i>
                        <span>选择文件</span>
                        <input type="file" class="hidden" accept="audio/*" @change="handleAudioUpload" />
                      </label>
                      <div v-if="uploadedAudio" class="mt-3 inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-lg text-sm">
                        <i class="material-icons text-sm mr-2">check_circle</i>
                        已选择: {{ uploadedAudio.name }}
                      </div>
                    </div>
                  </div>
              </div>
              <!-- 底部操作区 -->
              <div class="p-4 bg-gray-50 border-t border-gray-200">
                <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div class="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-600">
                    <div class="flex items-center">
                      <i class="material-icons mr-1.5 text-blue-500 text-base">smart_toy</i>
                      <span>{{ selectedDigitalTeacher?.name || '未选数字人' }}</span>
                    </div>
                    <div class="flex items-center">
                      <i class="material-icons mr-1.5 text-blue-500 text-base">record_voice_over</i>
                      <span>{{ selectedVoice?.name || '未选语音' }}</span>
                    </div>
                    <div class="flex items-center">
                      <i class="material-icons mr-1.5 text-blue-500 text-base">description</i>
                      <span>{{ contentInputType === 'text' ? (textContent ? `${textContent.length}字符` : '未输入内容') : (uploadedAudio ? '已上传音频' : '未上传') }}</span>
                    </div>
                  </div>
                  
                  <button 
                    @click="generateVideo"
                    :disabled="!canGenerate"
                    :class="[
                      'w-full sm:w-auto px-6 py-2.5 rounded-lg font-medium flex items-center justify-center gap-2 transition-all duration-200 transform text-sm',
                      canGenerate 
                        ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg hover:scale-105'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    ]"
                  >
                    <i class="material-icons text-base">smart_display</i>
                    <span>{{ isGenerating ? '生成中...' : '开始生成视频' }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：语音选择 (粘性定位) -->
          <div class="lg:col-span-1 lg:sticky lg:top-0 h-fit">
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col">
              <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-800 flex items-center">
                  <span class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-3">3</span>
                  选择语音
                </h3>
              </div>
              <div class="p-4">
                <div class="space-y-4">
                  <!-- 我的语音 -->
                  <div>
                    <h4 class="text-base font-medium text-gray-700 mb-3 flex items-center">
                      <i class="material-icons mr-2 text-blue-500 text-sm">person</i>
                      我的语音模型
                    </h4>
                    <div class="min-h-[600px]">
                      <div v-if="voicePagination.loading" class="text-center py-24 text-gray-500">
                        <div class="flex flex-col items-center justify-center">
                          <div class="loader">
                            <svg class="circular" viewBox="25 25 50 50">
                              <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="4" stroke-miterlimit="10"/>
                            </svg>
                          </div>
                          <p class="text-sm mt-5 text-blue-600 font-medium">正在加载语音模型...</p>
                        </div>
                      </div>
                      <div v-else-if="myVoices.length === 0" class="text-center py-16 text-gray-500">
                        <i class="material-icons text-4xl mb-3">mic_off</i>
                      <p class="text-sm">暂无自定义语音模型</p>
                    </div>
                      <div v-else>
                        <div class="grid grid-cols-2 sm:grid-cols-2 gap-3">
                      <div 
                        v-for="voice in myVoices" 
                        :key="voice.id"
                        @click="selectVoice(voice)"
                        :class="[
                          'border-2 rounded-xl p-3 cursor-pointer transition-all duration-200 text-center',
                          selectedVoice?.id === voice.id
                            ? 'border-blue-600 bg-blue-200 shadow-lg'
                            : 'border-gray-200 hover:border-blue-400 hover:bg-blue-50'
                        ]"
                      >
                        <i class="material-icons text-3xl text-blue-500 mb-2">mic</i>
                        <p class="font-medium text-sm text-gray-800 mb-1">{{ voice.name }}</p>
                        <p class="text-xs text-gray-500 mb-2">{{ voice.type === 'custom' ? '自定义' : '系统' }}</p>
                        <button 
                          @click.stop="playVoicePreview(voice.id)"
                          class="text-blue-600 hover:text-blue-800 text-xs flex items-center justify-center gap-1 mx-auto"
                          title="试听"
                          :disabled="!voice.clone_audio_url"
                        >
                          <i class="material-icons text-base">
                            {{ currentlyPlaying === voice.id ? 'pause_circle_filled' : 'play_circle_filled' }}
                          </i>
                          {{ currentlyPlaying === voice.id ? '暂停' : '试听' }}
                        </button>
                          </div>
                      </div>
                      </div>
                    </div>
                      
                    <!-- 分页控件 -->
                    <div v-if="totalVoicePages > 1" class="flex justify-center items-center mt-4 space-x-2">
                      <button
                        @click="handleVoicePageChange(voicePagination.currentPage - 1)"
                        :disabled="voicePagination.currentPage === 1"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                      >
                        上一页
                      </button>
                      <span class="text-sm text-gray-700">第 {{ voicePagination.currentPage }} 页 / 共 {{ totalVoicePages }} 页</span>
                      <button
                        @click="handleVoicePageChange(voicePagination.currentPage + 1)"
                        :disabled="voicePagination.currentPage >= totalVoicePages"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                      >
                        下一页
                      </button>
                    </div>
                  </div>

                  <!-- 系统语音 -->
                  <div>
                    <h4 class="text-base font-medium text-gray-700 mb-3 flex items-center">
                       <i class="material-icons mr-2 text-blue-500 text-sm">auto_awesome</i>
                      系统预设语音
                    </h4>
                    <div v-if="systemVoices.length === 0" class="text-center py-4 text-gray-500">
                      <i class="material-icons text-2xl mb-2">speaker_notes_off</i>
                      <p class="text-sm">系统预设语音加载中...</p>
                    </div>
                    <div v-else class="grid grid-cols-2 sm:grid-cols-2 gap-3">
                      <div 
                        v-for="voice in systemVoices" 
                        :key="voice.id"
                        @click="selectVoice(voice)"
                        :class="[
                          'border-2 rounded-xl p-3 cursor-pointer transition-all duration-200 text-center',
                          selectedVoice?.id === voice.id
                            ? 'border-blue-600 bg-blue-200 shadow-lg'
                            : 'border-gray-200 hover:border-blue-400 hover:bg-blue-50'
                        ]"
                      >
                        <i class="material-icons text-3xl text-blue-500 mb-2">spatial_audio_off</i>
                        <p class="font-medium text-sm text-gray-800 mb-1">{{ voice.name }}</p>
                        <p class="text-xs text-gray-500 mb-2">{{ voice.category || '系统' }}</p>
                        <button 
                          @click.stop="playVoicePreview(voice.id)"
                          class="text-blue-600 hover:text-blue-800 text-xs flex items-center justify-center gap-1 mx-auto"
                          title="试听"
                          :disabled="!voice.clone_audio_url"
                        >
                          <i class="material-icons text-base">
                            {{ currentlyPlaying === voice.id ? 'pause_circle_filled' : 'play_circle_filled' }}
                          </i>
                          {{ currentlyPlaying === voice.id ? '暂停' : '试听' }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数字人选择弹窗 -->
    <div v-if="showDigitalTeacherSelector" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl shadow-2xl max-w-5xl w-full max-h-[90vh] flex flex-col">
        <div class="p-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">选择一个数字人</h3>
            <button @click="showDigitalTeacherSelector = false" class="text-gray-500 hover:text-gray-700 p-2 hover:bg-gray-100 rounded-full transition-colors">
              <i class="material-icons">close</i>
            </button>
          </div>
        </div>

        <div class="p-4 overflow-y-auto">
          <!-- 加载状态 -->
          <div v-if="loadingDigitalTeachers" class="py-8 text-center text-gray-500">
            <div class="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
              <span class="sr-only">加载中...</span>
            </div>
            <p class="mt-4 text-lg">正在加载数字人列表...</p>
          </div>
          
          <div v-else>
            <!-- 我的数字人 -->
            <div class="mb-6">
              <h4 class="text-base font-medium text-gray-700 mb-3 flex items-center">
                <i class="material-icons mr-2 text-blue-500 text-sm">person</i>
                我的数字人
              </h4>
              
              <div v-if="availableDigitalTeachers.length === 0" class="py-6 text-center text-gray-500 border-2 border-dashed border-gray-200 rounded-xl">
                <i class="material-icons text-4xl mb-2 text-gray-400">person_search</i>
                <p class="text-sm">暂无可用数字人</p>
                <p class="text-xs text-gray-400 mt-1">只有已激活状态的数字人才能用于视频生成</p>
                <p class="text-xs text-gray-400 mt-1">请先创建并激活数字人模型</p>
              </div>
              
              <div v-else class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                <div 
                  v-for="teacher in availableDigitalTeachers" 
                  :key="teacher.id"
                  @click="selectDigitalTeacher(teacher)"
                  class="group relative flex flex-col rounded-xl border-2 border-gray-200 bg-white shadow-sm transition-all duration-200 cursor-pointer hover:border-blue-500 hover:shadow-lg overflow-hidden"
                >
                  <!-- 图片容器 -->
                  <div class="relative overflow-hidden bg-gray-100">
                    <img 
                      :src="teacher.avatar" 
                      alt="数字人" 
                      class="w-full h-40 object-cover transition-opacity duration-300" 
                    />
                    <!-- 状态标签 -->
                    <div class="absolute top-2 right-2">
                      <span class="px-2 py-1 bg-green-500 text-white text-xs font-medium rounded-full">
                        已激活
                      </span>
                    </div>
                  </div>
                  
                  <!-- 信息区域 -->
                  <div class="p-3 flex-1 flex flex-col">
                    <h5 class="font-semibold text-sm text-gray-800 truncate mb-1">{{ teacher.name }}</h5>
                    <p class="text-xs text-gray-500 flex items-center">
                      <i class="material-icons text-xs mr-1">person</i>
                      自定义数字人
                    </p>
                  </div>
                  
                  <!-- 选择指示器 -->
                  <div class="absolute inset-0 bg-blue-600 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center">
                    <div class="bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg">
                      <i class="material-icons text-blue-600">check</i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import { voiceApi } from '@/api/voice'
import { digitalHumanApi } from '@/api/digitalHuman'
import { videoGenerationApi } from '@/api/videoGeneration'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import digitalTeacher1 from '@/assets/images/avatars/digital-teacher1.png'

// Teacher Data
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// 界面状态
const contentInputType = ref('text')
const showAdvancedSettings = ref(false)
const showDigitalTeacherSelector = ref(false)
const isGenerating = ref(false)
const currentlyPlaying = ref(null)
const loadingDigitalTeachers = ref(false)

// 选择的内容
const selectedDigitalTeacher = ref(null)
const selectedVoice = ref(null)
const textContent = ref('欢迎来到人工智能基础课程，今天我们将学习神经网络的基本原理。神经网络是一种模拟人脑神经元的数学模型，它能够通过训练来识别模式和做出预测。')
const uploadedAudio = ref(null)

// 高级设置
const advancedSettings = ref({
  speed: 1.0,
  pitch: 0
})

// 我的语音模型
const myVoices = ref([])
// 我的语音分页数据
const voicePagination = ref({
  currentPage: 1,
  pageSize: 8,
  total: 0,
  loading: false
})

// 计算总页数
const totalVoicePages = computed(() => Math.ceil(voicePagination.value.total / voicePagination.value.pageSize))

// 系统语音模型
const systemVoices = ref([])

// 音频播放器
const audioPlayer = ref(null)

// 可用的数字人 - 改为动态数据
const availableDigitalTeachers = ref([])

// 计算属性
const estimatedDuration = computed(() => {
  if (!textContent.value) return 0
  // 简单估算：平均每分钟200字
  return Math.ceil(textContent.value.length / 200 * 60)
})

const canGenerate = computed(() => {
  return selectedDigitalTeacher.value && selectedVoice.value && 
         ((contentInputType.value === 'text' && textContent.value.trim()) || 
          (contentInputType.value === 'audio' && uploadedAudio.value)) && 
         !isGenerating.value
})

// 方法
const selectDigitalTeacher = (teacher) => {
  selectedDigitalTeacher.value = teacher
  showDigitalTeacherSelector.value = false
}

const selectVoice = (voice) => {
  selectedVoice.value = voice
}

const loadDigitalTeachers = async () => {
  try {
    loadingDigitalTeachers.value = true
    
    // 获取用户创建的可用数字人（仅限已激活状态）
    const userDigitalHumansRes = await digitalHumanApi.getActiveDigitalHumans({
      skip: 0,
      limit: 100 // 获取全部可用用户数字人
    })
    
    if (userDigitalHumansRes.code === 200) {
      availableDigitalTeachers.value = userDigitalHumansRes.data.digital_humans.map(item => ({
        id: item.id,
        name: item.name,
        avatar: item.avatar_url || digitalTeacher1,
        status: getStatusText(item.status),
        type: 'user'
      }))
    }
    
  } catch (error) {
    console.error('加载数字人列表失败:', error)
    ElMessage.error('加载数字人列表失败，请稍后重试')
  } finally {
    loadingDigitalTeachers.value = false
  }
}

// 状态文字映射
const getStatusText = (status) => {
  const statusTextMap = {
    'active': '已上线',
    'inactive': '未激活',
    'processing': '处理中'
  }
  return statusTextMap[status] || status
}

// 加载语音数据
const loadVoiceData = async (page = 1) => {
  try {
    voicePagination.value.loading = true
    voicePagination.value.currentPage = page
    
    // 加载用户自定义语音模型
    const userRes = await voiceApi.getVoiceModels({
      page: page,
      page_size: voicePagination.value.pageSize
    })
    
    // 更新数据之前稍微延迟一下，确保加载状态能显示
    setTimeout(() => {
      myVoices.value = userRes.data?.results || [] // 修改为results，与后端返回结构对应
      voicePagination.value.total = userRes.data?.count || 0
      
      // 默认选择第一个语音
      if (myVoices.value.length > 0 && !selectedVoice.value) {
        selectedVoice.value = myVoices.value[0]
      } 
      
      voicePagination.value.loading = false
    }, 300)
    
    // 加载系统预设声音
    const systemRes = await voiceApi.getSystemVoicePresets()
    systemVoices.value = systemRes.data || []
    
    // 如果没有用户语音但有系统语音，默认选择第一个系统语音
    if (myVoices.value.length === 0 && systemVoices.value.length > 0 && !selectedVoice.value) {
      selectedVoice.value = systemVoices.value[0]
    }
  } catch (e) {
    console.error('加载语音数据失败', e)
    voicePagination.value.loading = false
  }
}

// 翻页处理函数
const handleVoicePageChange = (newPage) => {
  loadVoiceData(newPage)
}

const playVoicePreview = (voiceId) => {
  // 若再次点击同一条 -> 暂停
  if (currentlyPlaying.value === voiceId) {
    if (audioPlayer.value) audioPlayer.value.pause()
    currentlyPlaying.value = null
    return
  }

  // 找到语音模型（自定义或系统）
  const voice = [...myVoices.value, ...systemVoices.value].find(v => v.id === voiceId)
  if (!voice || !voice.clone_audio_url) return

  // 停止之前播放
  if (audioPlayer.value) audioPlayer.value.pause()

  // 播放语音样本
  audioPlayer.value = new Audio(voice.clone_audio_url)
  audioPlayer.value.play().catch(err => console.error('播放失败', err))

  currentlyPlaying.value = voiceId
  audioPlayer.value.onended = () => {
    if (currentlyPlaying.value === voiceId) currentlyPlaying.value = null
  }
}

const handleAudioUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    uploadedAudio.value = {
      name: file.name,
      file: file
    }
  }
}

const generateVideo = async () => {
  if (!canGenerate.value) return
  
  isGenerating.value = true
  
  try {
    // 准备请求数据
    const payload = {
      name: `${selectedDigitalTeacher.value.name}_${Date.now()}`,
      digital_human_id: selectedDigitalTeacher.value.id,
      input_type: contentInputType.value,
      voice_id: selectedVoice.value.id,
      speed: advancedSettings.value.speed,
      pitch: advancedSettings.value.pitch
    }
    
    if (contentInputType.value === 'text') {
      payload.text_content = textContent.value
    } else if (contentInputType.value === 'audio') {
      payload.audio = uploadedAudio.value.file
    }
    
    // 调用API生成视频
    const response = await videoGenerationApi.generateVideo(payload)
    
    if (response.code === 200) {
      ElMessage.success('视频生成任务已提交，请稍后查看结果')
      console.log('视频生成成功:', response.data)
      
      // 可以在这里重置表单或跳转到视频列表页面
      // resetForm()
    } else {
      ElMessage.error(response.message || '视频生成失败')
    }
    
  } catch (error) {
    console.error('视频生成失败:', error)
    ElMessage.error('视频生成失败，请检查网络连接或联系管理员')
  } finally {
    isGenerating.value = false
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await Promise.all([
    loadVoiceData(),
    loadDigitalTeachers()
  ])
})
</script>

<style scoped>
/* 自定义滑块样式 */
.range-thumb-blue::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  margin-top: -6px;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #dbeafe;
}

.range-thumb-blue::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #dbeafe;
}

/* 加载动画样式 */
.loader {
  position: relative;
  margin: 0 auto;
  width: 60px;
  height: 60px;
}

.circular {
  animation: rotate 2s linear infinite;
  height: 100%;
  transform-origin: center center;
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

.path {
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
  stroke-linecap: round;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}

@keyframes color {
  0%, 100% {
    stroke: #3b82f6;
  }
  40% {
    stroke: #60a5fa;
  }
  66% {
    stroke: #2563eb;
  }
  80%, 90% {
    stroke: #1d4ed8;
  }
}
</style>

