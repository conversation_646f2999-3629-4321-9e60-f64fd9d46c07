from rest_framework.decorators import action
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from ..services.ppt_service import PPTService
from ..utils.response import ResponseResult


class PPTViewSet(ViewSet):
    """PPT生成相关接口视图集"""
    permission_classes = [IsAuthenticated]
    parser_classes = [JSONParser, MultiPartParser, FormParser]

    @action(detail=False, methods=['get'], url_path='token')
    def get_token(self, request):
        """
        获取文多多API token
        
        Returns:
            Response: 包含token的响应
        """
        try:
            # 获取当前用户ID
            user_id = request.user.id
            
            # 调用服务获取token
            token_result = PPTService.get_docmee_token(user_id=user_id)
            
            return ResponseResult.success(
                data={"token": token_result["token"]},
                message="获取Token成功"
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f"获取Token失败: {str(e)}"
            )
            
    @action(detail=False, methods=['get'], url_path='projects')
    def get_projects(self, request):
        """
        获取PPT项目列表
        
        Query参数:
            search: 搜索关键词
            subject_id: 学科ID
            sort: 排序选项（recent, oldest, az, za）
            page: 当前页码，默认1
            page_size: 每页数量，默认10
            
        Returns:
            Response: 包含项目列表和分页信息
        """
        try:
            # 获取查询参数
            search_query = request.query_params.get('search', '')
            subject_id = request.query_params.get('subject_id', 'all')
            sort_option = request.query_params.get('sort', 'recent')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            
            # 获取当前用户ID
            user_id = request.user.id
            
            # 调用服务获取项目查询集
            project_queryset = PPTService.get_ppt_projects(
                user_id=user_id,
                subject_id=subject_id,
                search_query=search_query,
                sort_option=sort_option
            )
            
            # 定义项目格式化函数
            def format_project(project):
                return {
                    'id': project.id,
                    'ppt_id': project.ppt_id,
                    'title': project.title,
                    'description': project.description or '',
                    'subject': {
                        'id': project.subject.id,
                        'name': project.subject.name,
                        'code': project.subject.code,
                        'icon': project.subject.icon
                    },
                    'slides': project.slides_count,
                    'user': {
                        'id': project.user.id,
                        'name': project.user.username
                    },
                    'createdAt': project.create_at.strftime('%Y-%m-%d'),
                    'updatedAt': project.update_at.strftime('%Y-%m-%d')
                }
            
            # 使用ResponseResult.paginate方法，提供格式化函数
            return ResponseResult.paginate(
                queryset=project_queryset,
                page=page,
                page_size=page_size,
                message='获取项目列表成功',
                item_formatter=format_project
            )
                
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'获取项目列表失败: {str(e)}'
            )
            
    @action(detail=False, methods=['post'], url_path='generate')
    def generate_ppt(self, request):
        """
        根据教案内容直接生成PPT，返回流式生成任务ID
        
        Request Body:
            content: 教案文本内容 (二选一)
            file: 教案文件 (二选一)
            teaching_duration: 教学时长(分钟)，默认45
            teaching_style: 教学风格，默认balanced
            specific_requirements: 具体需求说明
            document_ids: 知识库文档ID列表(可选)
        
        Returns:
            Response: 包含任务ID
        """
        try:
            # 获取请求参数
            content = request.data.get('content', '')
            teaching_duration = request.data.get('teaching_duration', '45')
            teaching_style = request.data.get('teaching_style', 'balanced')
            specific_requirements = request.data.get('specific_requirements', '')
            document_ids = request.data.get('document_ids', [])
            
            # 如果document_ids是JSON字符串，则解析成列表
            if isinstance(document_ids, str):
                try:
                    import json
                    document_ids = json.loads(document_ids)
                except json.JSONDecodeError:
                    document_ids = []

            # 如果上传了文件，优先处理文件
            if 'file' in request.FILES:
                file = request.FILES['file']
                result = PPTService.generate_from_file(
                    file=file,
                    teaching_duration=teaching_duration,
                    teaching_style=teaching_style,
                    specific_requirements=specific_requirements,
                    document_ids=document_ids
                )
            # 否则使用传入的文本内容
            elif content:
                result = PPTService.generate_from_text(
                    content=content,
                    teaching_duration=teaching_duration,
                    teaching_style=teaching_style,
                    specific_requirements=specific_requirements,
                    document_ids=document_ids
                )
            else:
                return ResponseResult.error(
                    code=400,
                    message='请提供教案内容或上传教案文件'
                )

            return ResponseResult.success(
                data=result,
                message='PPT生成任务已创建，请使用流式接口获取生成结果'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'创建PPT生成任务失败: {str(e)}'
            )

    @action(detail=False, methods=['get'], url_path='stream/(?P<task_id>[^/.]+)')
    def stream_ppt(self, request, task_id):
        """
        流式生成PPT并返回
        
        URL参数:
            task_id: 任务ID
            
        Returns:
            StreamingHttpResponse: 流式响应，包含SSE格式的生成内容
        """
        try:
            from django.http import StreamingHttpResponse
            return StreamingHttpResponse(
                PPTService.generate_ppt_stream(task_id),
                content_type='text/event-stream'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'启动流式生成失败: {str(e)}'
            )

    @action(detail=False, methods=['post'], url_path='save-ppt-id')
    def save_ppt_id(self, request):
        """
        保存文多多生成的PPT ID，并尝试获取幻灯片数量
        
        Request Body:
            ppt_id: 文多多PPT ID
            title: PPT标题
            subject_id: 学科ID
            description: PPT描述(可选)
        
        Returns:
            Response: 包含保存结果和幻灯片数量
        """
        try:
            # 获取请求参数
            ppt_id = request.data.get('ppt_id')
            title = request.data.get('title')
            subject_id = request.data.get('subject_id')
            description = request.data.get('description', '')
            
            # 参数验证
            if not ppt_id or not title or not subject_id:
                return ResponseResult.error(
                    code=400,
                    message='缺少必要参数'
                )
            
            # 获取当前用户ID
            user_id = request.user.id
            
            # 调用服务保存PPT ID
            result = PPTService.save_ppt_id(
                ppt_id=ppt_id,
                title=title,
                subject_id=subject_id,
                description=description,
                user_id=user_id
            )
            
            return ResponseResult.success(
                data=result,
                message='PPT保存成功'
            )
        except Exception as e:
            return ResponseResult.error(
                code=500,
                message=f'保存PPT失败: {str(e)}'
            )
