<script setup>
import { RouterView } from 'vue-router'
import { useAuthStore } from './stores/auth'
import { useI18n } from 'vue-i18n'
import { onMounted } from 'vue'
import axios from 'axios'

const authStore = useAuthStore()
const { locale } = useI18n()

// 初始化语言设置和身份验证
onMounted(() => {
  // 检查登录状态
  authStore.checkAuth()
  
  // 初始化语言设置
  const savedLang = localStorage.getItem('userLanguage') || 'zh'
  locale.value = savedLang
  
  // 设置API请求的语言头
  axios.defaults.headers.common['Accept-Language'] = savedLang
})
</script>

<template>
  <RouterView />
</template>

<style scoped>
/* 全局样式可以保留 */
</style>
