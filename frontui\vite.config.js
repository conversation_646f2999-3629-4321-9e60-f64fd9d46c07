import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),
      vueDevTools(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    server: {
      host: '0.0.0.0',
      port: 5173,
      proxy: {
        // 使用环境变量中的 API 路径
        [env.VITE_APP_BASE_API || '/api']: {
          target: 'http://127.0.0.1:8000',
          changeOrigin: true
        }
      }
    },
    build: {
      chunkSizeWarningLimit: 15000, // 单位：KB
      // 生产环境打包配置
      outDir: 'dist',
      assetsDir: 'assets',
      // 确保环境变量在构建时被正确替换
      rollupOptions: {
        output: {
          manualChunks: {
            'vendor': ['vue']
          }
        }
      }
    },
    // 定义全局常量替换
    define: {
      __APP_ENV__: JSON.stringify(env.ENV),
    }

  }
})
