import request from '@/utils/request'

/**
 * 视频生成相关 API
 * 对应后端 VideoGenerationViewSet 中的接口
 */
export const videoGenerationApi = {
  /**
   * 生成数字人视频
   * @param {Object} payload { name, digital_human_id, input_type, text_content, voice_id, speed, pitch, audio, folder_id }
   */
  generateVideo(payload) {
    const formData = new FormData()
    
    // 添加基本字段
    Object.entries(payload).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        formData.append(key, value)
      }
    })
    
    return request({
      url: '/video-generation/generate/',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取生成的视频列表
   * @param {Object} params { skip, limit, search, folder_id, status }
   */
  getGeneratedVideos(params = {}) {
    return request({
      url: '/video-generation/list/',
      method: 'get',
      params
    })
  },

  /**
   * 更新生成的视频信息
   * @param {Number|String} id 视频ID
   * @param {Object} payload { name, folder_id }
   */
  updateGeneratedVideo(id, payload) {
    return request({
      url: `/video-generation/${id}/update/`,
      method: 'put',
      data: payload
    })
  },

  /**
   * 删除生成的视频
   * @param {Number|String} id 视频ID
   */
  deleteGeneratedVideo(id) {
    return request({
      url: `/video-generation/${id}/delete/`,
      method: 'delete'
    })
  },

  /**
   * 获取用户视频统计数据
   */
  getVideoStatistics() {
    return request({
      url: '/video-generation/statistics/',
      method: 'get'
    })
  }
} 