<template>
  <AdminLayout 
    pageTitle="积分管理" 
    :userName="adminStore.adminData.name"
    :userAvatar="adminStore.adminData.avatar">
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <!-- 积分统计 -->
        <el-card class="mb-4 stats-card">
          <template #header>
            <div class="flex items-center">
              <el-icon class="mr-2"><TrendCharts /></el-icon>
              <span class="text-lg">积分统计</span>
            </div>
          </template>
          <div class="p-4">
            <div class="flex items-center">
              <el-icon class="text-blue-500 mr-3 text-xl"><Medal /></el-icon>
              <div class="text-lg font-medium">本月发放积分：</div>
              <div class="text-2xl font-bold text-blue-600 ml-2">{{ monthlyTotalPoints }}</div>
            </div>
          </div>
        </el-card>

        <!-- 积分规则设置 -->
        <el-card class="mb-4 rules-card" :body-style="{ padding: '1rem' }">
          <template #header>
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <el-icon class="mr-2"><Setting /></el-icon>
                <span class="text-lg">积分规则设置</span>
              </div>
              <el-button type="primary" @click="handleEditRules">
                <el-icon class="mr-1"><Edit /></el-icon>
                编辑规则
              </el-button>
            </div>
          </template>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="rule in pointRules" 
                 :key="rule.id" 
                 class="p-4 border rounded-lg hover:shadow-md transition-shadow duration-300 bg-white">
              <div class="flex items-center mb-3">
                <el-icon class="mr-2 star-icon"><Star /></el-icon>
                <h3 class="font-medium text-lg">{{ rule.name }}</h3>
              </div>
              <p class="text-blue-600 font-semibold mb-2">
                <el-icon class="mr-1 plus-icon"><Plus /></el-icon>
                获得积分：{{ rule.points }}
              </p>
              <p class="text-gray-600">
                <el-icon class="mr-1 info-icon"><InfoFilled /></el-icon>
                {{ rule.description }}
              </p>
            </div>
          </div>
        </el-card>

        <!-- 编辑规则对话框 -->
        <el-dialog
          v-model="editRulesDialogVisible"
          width="50%"
          :close-on-click-modal="false"
          @close="cancelEdit"
        >
          <template #header>
            <div class="flex items-center">
              <el-icon class="mr-2 text-xl text-blue-500"><Setting /></el-icon>
              <span class="text-xl font-medium">编辑积分规则</span>
            </div>
          </template>

          <el-form :model="editingRules" label-width="100px">
            <div v-for="(rule, index) in editingRules" :key="rule.id" class="mb-6 pb-6 border-b last:border-b-0">
              <div class="flex justify-between items-center mb-4">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-blue-500"><Star /></el-icon>
                  <h3 class="text-lg font-medium">规则 {{ index + 1 }}</h3>
                </div>
                <el-button 
                  type="danger" 
                  :icon="Delete"
                  size="small"
                  @click="removeRule(index)"
                  v-if="editingRules.length > 1"
                >
                  删除规则
                </el-button>
              </div>
              
              <el-form-item>
                <template #label>
                  <div class="flex items-center">
                    <el-icon class="mr-1"><Edit /></el-icon>
                    规则名称
                  </div>
                </template>
                <el-input v-model="rule.name" placeholder="请输入规则名称" />
              </el-form-item>
              
              <el-form-item>
                <template #label>
                  <div class="flex items-center">
                    <el-icon class="mr-1"><Medal /></el-icon>
                    积分值
                  </div>
                </template>
                <el-input-number 
                  v-model="rule.points" 
                  :min="1"
                  :max="100"
                  placeholder="请输入积分值"
                  class="w-full"
                />
              </el-form-item>
              
              <el-form-item>
                <template #label>
                  <div class="flex items-center">
                    <el-icon class="mr-1"><InfoFilled /></el-icon>
                    规则描述
                  </div>
                </template>
                <el-input
                  v-model="rule.description"
                  type="textarea"
                  rows="2"
                  placeholder="请输入规则描述"
                />
              </el-form-item>
            </div>
            
            <div class="flex justify-center mt-4">
              <el-button 
                type="primary" 
                plain
                @click="addNewRule"
                class="add-rule-btn"
              >
                <el-icon class="mr-1"><CirclePlus /></el-icon>
                <span class="text-white">添加新规则</span>
              </el-button>
            </div>
          </el-form>
          
          <template #footer>
            <div class="flex justify-end gap-2">
              <el-button @click="cancelEdit">
                <el-icon class="mr-1"><Close /></el-icon>
                取消
              </el-button>
              <el-button type="primary" @click="saveRules">
                <el-icon class="mr-1"><Check /></el-icon>
                保存
              </el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 积分记录 -->
        <el-card class="records-card">
          <template #header>
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <el-icon class="mr-2"><List /></el-icon>
                <span class="text-lg">积分记录</span>
              </div>
              <div class="flex items-center">
                <el-icon class="mr-2"><Search /></el-icon>
                <el-input
                  v-model="search"
                  placeholder="搜索用户"
                  style="width: 200px"
                >
                  <template #prefix>
                    <el-icon><User /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>
          </template>
          <el-table :data="pointRecords" style="width: 100%" :stripe="true" :border="true">
            <el-table-column prop="userId" label="用户ID" width="100">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="mr-1"><UserFilled /></el-icon>
                  用户ID
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="username" label="用户名" width="120">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="mr-1"><User /></el-icon>
                  用户名
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="action" label="操作类型" width="120">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="mr-1"><Operation /></el-icon>
                  操作类型
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="积分变动">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="mr-1"><DataLine /></el-icon>
                  积分变动
                </div>
              </template>
              <template #default="{ row }">
                <span :class="[
                  row.points > 0 ? 'text-green-600' : 'text-red-600',
                  'flex items-center'
                ]">
                  <el-icon class="mr-1">
                    <component :is="row.points > 0 ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                  {{ row.points > 0 ? '+' : '' }}{{ row.points }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="balance" label="当前积分" width="120">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="mr-1"><Wallet /></el-icon>
                  当前积分
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="time" label="时间" width="180">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="mr-1"><Timer /></el-icon>
                  时间
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="mr-1"><Document /></el-icon>
                  备注
                </div>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="flex justify-center mt-4">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
            />
          </div>
        </el-card>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import { useAdminStore } from '@/stores/admin'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  Setting,
  Edit,
  Star,
  Plus,
  InfoFilled,
  List,
  Search,
  User,
  UserFilled,
  Operation,
  DataLine,
  ArrowUp,
  ArrowDown,
  Wallet,
  Timer,
  Document,
  Medal,
  Delete,
  CirclePlus,
  Check,
  Close
} from '@element-plus/icons-vue'

const router = useRouter()
const adminStore = useAdminStore()
const authStore = useAuthStore()

// 本月总积分统计
const monthlyTotalPoints = ref(0)

// 获取本月发放的总积分
const fetchMonthlyTotalPoints = () => {
  // TODO: 替换为实际的API调用
  // 这里暂时使用模拟数据
  monthlyTotalPoints.value = 1250
}

onMounted(() => {
  fetchMonthlyTotalPoints()
})

// 积分规则
const pointRules = ref([
  {
    id: 1,
    name: '每日签到',
    points: 5,
    description: '每日首次登录系统可获得积分'
  },
  {
    id: 2,
    name: '完成课程',
    points: 20,
    description: '完成一门课程学习可获得积分'
  },
  {
    id: 3,
    name: '参与讨论',
    points: 2,
    description: '在课程讨论区发表评论可获得积分'
  },
  {
    id: 4,
    name: '优秀作业',
    points: 10,
    description: '作业被评为优秀可获得额外积分'
  }
])

// 积分记录
const search = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

const pointRecords = ref([
  {
    userId: '1001',
    username: '张三',
    action: '签到',
    points: 5,
    balance: 125,
    time: '2024-03-15 09:00:00',
    remark: '每日签到奖励'
  },
  {
    userId: '1002',
    username: '李四',
    action: '购物',
    points: -50,
    balance: 75,
    time: '2024-03-15 10:30:00',
    remark: '购买课程笔记本'
  }
])

// 编辑规则相关
const editRulesDialogVisible = ref(false)
const editingRules = ref([])

// 打开编辑规则对话框
const handleEditRules = () => {
  editingRules.value = JSON.parse(JSON.stringify(pointRules.value))
  editRulesDialogVisible.value = true
}

// 添加新规则
const addNewRule = () => {
  const newId = Math.max(...editingRules.value.map(rule => rule.id), 0) + 1
  editingRules.value.push({
    id: newId,
    name: '',
    points: 1,
    description: ''
  })
}

// 删除规则
const removeRule = (index) => {
  editingRules.value.splice(index, 1)
}

// 取消编辑
const cancelEdit = () => {
  editRulesDialogVisible.value = false
  editingRules.value = []
}

// 保存规则
const saveRules = () => {
  // 验证规则
  if (!validateRules()) {
    return
  }
  
  // 更新规则
  pointRules.value = JSON.parse(JSON.stringify(editingRules.value))
  
  // TODO: 调用后端API保存规则
  // await saveRulesToBackend(pointRules.value)
  
  ElMessage({
    type: 'success',
    message: '规则保存成功'
  })
  
  editRulesDialogVisible.value = false
}

// 验证规则
const validateRules = () => {
  for (const rule of editingRules.value) {
    if (!rule.name.trim()) {
      ElMessage({
        type: 'error',
        message: '规则名称不能为空'
      })
      return false
    }
    if (!rule.points || rule.points < 1) {
      ElMessage({
        type: 'error',
        message: '积分值必须大于0'
      })
      return false
    }
    if (!rule.description.trim()) {
      ElMessage({
        type: 'error',
        message: '规则描述不能为空'
      })
      return false
    }
  }
  return true
}
</script>

<style scoped>
.stats-card :deep(.el-card__header),
.rules-card :deep(.el-card__header),
.records-card :deep(.el-card__header) {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.stats-card :deep(.el-card__header) .el-icon {
  color: #6366f1;  /* Indigo-500 */
  font-size: 1.25rem;
}

.stats-card .text-blue-500 {
  color: #3b82f6;  /* Blue-500 */
  font-size: 1.5rem;
}

.rules-card :deep(.el-card__header) .el-icon {
  color: #8b5cf6;  /* Purple-500 */
  font-size: 1.25rem;
}

.rules-card .el-icon.text-blue-500 {
  color: #6366f1;  /* Indigo-500 */
}

.records-card :deep(.el-card__header) .el-icon {
  color: #10b981;  /* Emerald-500 */
  font-size: 1.25rem;
}

/* Table header icons */
.el-table :deep(th) .el-icon {
  color: #6b7280;  /* Gray-500 */
}

/* Points change icons */
.text-green-600 .el-icon {
  color: #059669;  /* Emerald-600 */
}

.text-red-600 .el-icon {
  color: #dc2626;  /* Red-600 */
}

/* Rule card icons */
.rules-card .el-icon {
  font-size: 1.1rem;
}

.rules-card .star-icon {
  color: #f59e0b;  /* Amber-500 */
}

.rules-card .plus-icon {
  color: #10b981;  /* Emerald-500 */
}

.rules-card .info-icon {
  color: #6b7280;  /* Gray-500 */
}

/* Search input icon */
.el-input :deep(.el-input__prefix) .el-icon {
  color: #6b7280;  /* Gray-500 */
}

.el-card {
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.el-table {
  --el-table-border-color: #e5e7eb;
  --el-table-header-bg-color: #f8fafc;
}

.el-icon {
  vertical-align: middle;
}

/* 编辑规则对话框样式 */
.el-dialog :deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  margin-right: 0;
  padding: 20px 24px;
}

.el-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.el-dialog :deep(.el-dialog__footer) {
  border-top: 1px solid #e5e7eb;
  padding: 16px 24px;
}

.el-form-item :deep(.el-form-item__label) {
  font-weight: 500;
}

.el-input-number.w-full :deep(.el-input__wrapper) {
  width: 100%;
}

.el-dialog :deep(.el-dialog__body) {
  padding-top: 10px;
}

.el-button>span {
  color: #fff;
}

/* 添加新规则按钮的图标颜色 */
.add-rule-btn :deep(.el-icon) {
  color: #fff !important;
}
</style> 