<template>
  <div class="course-overview">
    <div class="overview-section">
      <div v-if="!isEditing">
        <div class="overview-block">
          <h3 class="block-title">{{ t('courseOverview.keyPoints') }}</h3>
          <div class="content-block" v-html="overview?.key_points || t('courseOverview.noContent')"></div>
        </div>
        <div class="overview-block">
          <h3 class="block-title">{{ t('courseOverview.importantPoints') }}</h3>
          <div class="content-block" v-html="overview?.important_points || t('courseOverview.noContent')"></div>
        </div>
        <div class="overview-block">
          <h3 class="block-title">{{ t('courseOverview.notes') }}</h3>
          <div class="content-block" v-html="overview?.notes || t('courseOverview.noContent')"></div>
        </div>
        <div v-if="canEdit" class="edit-section">
          <el-button @click="startEdit">{{ t('courseOverview.edit') }}</el-button>
        </div>
      </div>
      <div v-else>
        <form @submit.prevent="saveOverview" class="edit-form">
          <div class="form-block">
            <el-form-item :label="t('courseOverview.keyPoints')">
              <el-input
                type="textarea"
                v-model="editForm.key_points"
                :rows="4"
                :placeholder="t('courseOverview.enterKeyPoints')"
              />
            </el-form-item>
          </div>
          <div class="form-block">
            <el-form-item :label="t('courseOverview.importantPoints')">
              <el-input
                type="textarea"
                v-model="editForm.important_points"
                :rows="4"
                :placeholder="t('courseOverview.enterImportantPoints')"
              />
            </el-form-item>
          </div>
          <div class="form-block">
            <el-form-item :label="t('courseOverview.notes')">
              <el-input
                type="textarea"
                v-model="editForm.notes"
                :rows="4"
                :placeholder="t('courseOverview.enterNotes')"
              />
            </el-form-item>
          </div>
          <div class="button-group">
            <el-button type="primary" native-type="submit" :loading="saving">{{ t('general.save') }}</el-button>
            <el-button @click="cancelEdit">{{ t('general.cancel') }}</el-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'
import { ElButton, ElFormItem, ElInput, ElMessage } from 'element-plus'
import { courseApi } from '@/api/course'
import { useI18n } from 'vue-i18n' // 添加i18n支持

const { t } = useI18n() // 初始化i18n

const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  },
  overview: {
    type: Object,
    default: () => ({
      key_points: '',
      important_points: '',
      notes: ''
    })
  },
  canEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:overview'])

const isEditing = ref(false)
const saving = ref(false)

const editForm = ref({
  key_points: '',
  important_points: '',
  notes: ''
})

// Debug logs
onMounted(() => {
  console.log('CourseOverview mounted:', {
    courseId: props.courseId,
    overview: props.overview,
    canEdit: props.canEdit
  })
})

watch(() => props.overview, (newVal) => {
  console.log('Overview updated:', newVal)
}, { deep: true })

const startEdit = () => {
  editForm.value = {
    key_points: props.overview?.key_points || '',
    important_points: props.overview?.important_points || '',
    notes: props.overview?.notes || ''
  }
  isEditing.value = true
}

const cancelEdit = () => {
  isEditing.value = false
  editForm.value = {
    key_points: '',
    important_points: '',
    notes: ''
  }
}

const saveOverview = async () => {
  try {
    saving.value = true
    const response = await courseApi.updateCourseOverview(props.courseId, editForm.value)
    emit('update:overview', response)
    ElMessage.success(t('courseOverview.updateSuccess'))
    isEditing.value = false
  } catch (error) {
    ElMessage.error(error.message || t('courseOverview.saveFailed'))
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.course-overview {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.overview-section {
  margin-bottom: 24px;
}

.overview-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
}

.overview-block {
  margin-bottom: 20px;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.block-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #409EFF;
}

.content-block {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap;
}

.edit-section {
  margin-top: 16px;
}

.edit-form {
  margin-top: 16px;
}

.form-block {
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style> 