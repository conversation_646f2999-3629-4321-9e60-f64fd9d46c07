<template>
  <span
    :class="[
      'material-icons',
      sizeClass,
      colorClass,
      className
    ]"
  >{{ name }}</span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  size: {
    type: String,
    default: 'md'
  },
  color: {
    type: String,
    default: 'default'
  },
  className: {
    type: String,
    default: ''
  }
})

const sizeClass = computed(() => {
  const sizes = {
    'xs': 'text-xs',
    'sm': 'text-sm',
    'md': 'text-base',
    'lg': 'text-lg',
    'xl': 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
    '5xl': 'text-5xl'
  }
  return sizes[props.size] || sizes.md
})

const colorClass = computed(() => {
  const colors = {
    'default': 'text-gray-600',
    'primary': 'text-indigo-600',
    'success': 'text-green-600',
    'warning': 'text-yellow-600',
    'danger': 'text-red-600',
    'info': 'text-blue-600',
    'light': 'text-gray-400',
    'dark': 'text-gray-800'
  }
  return colors[props.color] || ''
})
</script> 