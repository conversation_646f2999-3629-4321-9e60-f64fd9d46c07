<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    pageTitle="创建新PPT"
    activePage="content-creation"
    activeSubPage="ppt"
  >
    <div class="flex-1 p-6">
      <!-- 面包屑导航 -->
      <nav class="flex mb-5" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <router-link to="/teacher/dashboard" class="text-gray-700 hover:text-blue-600">
              <el-icon class="mr-2"><HomeFilled /></el-icon>首页
            </router-link>
          </li>
          <li>
            <div class="flex items-center">
              <el-icon class="text-gray-400 mx-2"><ArrowRight /></el-icon>
              <router-link to="/teacher/content-creation" class="text-gray-700 hover:text-blue-600">教学内容制作</router-link>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <el-icon class="text-gray-400 mx-2"><ArrowRight /></el-icon>
              <router-link to="/teacher/content-ppt-projects" class="text-gray-700 hover:text-blue-600">PPT项目管理</router-link>
            </div>
          </li>
          <li aria-current="page">
            <div class="flex items-center">
              <el-icon class="text-gray-400 mx-2"><ArrowRight /></el-icon>
              <span class="text-gray-500">PPT内容设计</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- 正在分析教案提示 -->
      <div v-if="isAnalyzing && !showSuccessNotice" class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-md">
        <div class="flex">
          <div class="flex-shrink-0">
            <span class="material-icons animate-spin text-blue-400">sync</span>
          </div>
          <div class="ml-3 flex-1">
            <p class="text-sm text-blue-700">
              <span class="font-bold">AI正在分析教案:</span> {{ progressMessage }}
            </p>
            <!-- 进度条 -->
            <div class="mt-2 w-full bg-gray-200 rounded-full h-2.5">
              <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${progressPercentage}%` }"></div>
            </div>
            <div class="mt-1 text-xs text-blue-600 flex justify-between">
              <span>{{ progressPercentage }}%</span>
              <span>{{ currentSlideInfo }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- PPT生成成功提示 -->
      <div v-if="showSuccessNotice" class="bg-green-50 border-l-4 border-green-400 p-4 mb-6 rounded-md">
        <div class="flex">
          <div class="flex-shrink-0">
            <el-icon class="text-green-400"><Check /></el-icon>
          </div>
          <div class="ml-3">
            <p class="text-sm text-green-700">
              <span class="font-bold">PPT内容生成完成:</span> 已为您创建了 {{ slides.length }} 个主题的幻灯片，可以直接编辑或进行下一步选择模板。
            </p>
          </div>
        </div>
      </div>

      <div class="gap-6">
        <div class="w-full">
          <div class="bg-white shadow rounded-lg p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">PPT内容生成控制</h3>
            
            <div class="space-y-4">
              <!-- 教案内容导入 -->
              <div>
                <div class="text-xs text-gray-500 mb-2">上传教学设计文件</div>
                
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors"
                     @dragover.prevent="dragover = true"
                     @dragleave.prevent="dragover = false"
                     @drop.prevent="onFileDrop"
                     :class="{ 'bg-blue-50 border-blue-300': dragover }">
                  <input type="file" ref="uploadInput" class="hidden" accept=".pdf,.docx,.doc,.pptx,.ppt,.md,.txt" @change="handleFileUpload" />
                  <label for="upload-lesson-file" class="cursor-pointer" @click="$refs.uploadInput.click()">
                    <div class="mb-2">
                      <el-icon class="text-blue-500 text-2xl"><Upload /></el-icon>
                    </div>
                    <div class="text-sm text-gray-600 mb-1">点击上传或拖拽文件</div>
                    <div class="text-xs text-gray-500">支持PDF、Word、PPT、MD等格式</div>
                  </label>
                </div>
              </div>

              <!-- 知识库文档选择、教学时长、教学风格 横向排列 -->
              <div class="flex flex-col md:flex-row gap-4">
                <!-- 关联知识库文档 -->
                <div class="flex-1 min-w-0">
                  <label class="block text-sm font-medium text-gray-700 mb-1">关联知识库文档</label>
                  <div class="mt-2">
                    <button @click="showKnowledgeModal = true" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      <el-icon class="mr-2"><FolderOpened /></el-icon>
                      选择知识库文档
                    </button>
                    <!-- 显示已选择的项目 -->
                    <div v-if="selectedDocuments.length > 0" class="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                      <div class="text-xs text-gray-500 mb-1">已选择:</div>
                      <div class="flex flex-wrap gap-1">
                        <span class="text-xs font-medium text-gray-500">文档 ({{ selectedDocuments.length }}):</span>
                        <span v-for="id in selectedDocuments" :key="id" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                          {{ getDocumentName(id) }}
                          <button @click.stop="toggleDocument(id)" class="ml-2 text-green-500 hover:text-green-700">
                            <el-icon><Close /></el-icon>
                          </button>
                        </span>
                      </div>
                    </div>
                    <div v-else class="mt-2 text-xs text-gray-500">
                      未选择任何文档
                    </div>
                  </div>
                </div>
                <!-- 学科选择 -->
                <div class="flex-1 min-w-0">
                  <label class="block text-sm font-medium text-gray-700 mb-[8px]">选择学科</label>
                  <select v-model="selectedSubject" class="mt-0 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <option v-for="subject in subjectList" :key="subject.id" :value="subject.id">{{ subject.name }}</option>
                  </select>
                </div>
                <!-- 教学时长 -->
                <div class="flex-1 min-w-0">
                  <label class="block text-sm font-medium text-gray-700 mb-[8px]">教学时长</label>
                  <select v-model="selectedTeachingDuration" class="mt-0 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <option value="30">30分钟</option>
                    <option value="45">45分钟</option>
                    <option value="60">60分钟</option>
                    <option value="90">90分钟</option>
                    <option value="120">120分钟</option>
                  </select>
                </div>
                <!-- 教学风格 -->
                <div class="flex-1 min-w-0">
                  <label class="block text-sm font-medium text-gray-700 mb-[8px]">教学风格</label>
                  <select v-model="selectedTeachingStyle" class="mt-0 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                    <option value="balanced">平衡型（理论与实践并重）</option>
                    <option value="theory">深度理论型（注重概念讲解）</option>
                    <option value="practice">实践应用型（注重实例展示）</option>
                    <option value="interactive">互动探究型（引导自主思考）</option>
                  </select>
                </div>
              </div>

              <!-- 具体要求输入框 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">具体要求</label>
                <textarea v-model="specificRequirements" rows="4" class="mt-1 block w-full px-3 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md" placeholder="例如：重点讲解Python的数据类型和控制结构，增加更多实际编程案例，使用简单明了的语言..."></textarea>
              </div>

              <!-- 生成按钮 -->
              <button @click="generateWithAI" :disabled="isGenerating" class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" :class="{ 'opacity-50 cursor-not-allowed': isGenerating }">
                <template v-if="isGenerating">
                  <span class="material-icons animate-spin mr-2">sync</span>
                  正在生成PPT内容...
                </template>
                <template v-else>
                  <el-icon class="mr-2"><Edit /></el-icon>
                  生成PPT内容
                </template>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导入教案弹窗 -->
    <ImportLessonPlanModal 
      v-if="showImportModal"
      @close="showImportModal = false"
      @import="handleLessonPlanImport"
    />

    <!-- 成功通知 -->
    <div v-if="showNotification" class="success-notification" :class="{ 'show-notification': showNotification }">
      <el-icon class="mr-2"><Check /></el-icon>
      <span>{{ notificationMessage }}</span>
    </div>

    <!-- 添加一个用于显示错误消息的组件 -->
    <div v-if="errorMessage" class="mt-4 bg-red-50 border-l-4 border-red-400 p-4 rounded-md">
      <div class="flex">
        <div class="flex-shrink-0">
          <el-icon class="text-red-400"><WarningFilled /></el-icon>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">{{ errorMessage }}</p>
        </div>
      </div>
    </div>

    <!-- 添加知识库选择弹窗 -->
    <el-dialog
      v-model="showKnowledgeModal"
      title="选择知识库文档"
      width="80%"
      :destroy-on-close="false"
      :close-on-click-modal="false"
    >
      <div class="knowledge-selector">
        <!-- 选项卡导航 -->
        <div class="flex border-b border-gray-200 mb-4">
          <button 
            v-for="tab in ['categories', 'datasets', 'documents']" 
            :key="tab"
            @click="knowledgeTab = tab"
            class="py-3 px-6 font-medium relative"
            :class="knowledgeTab === tab ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'"
          >
            {{ tab === 'categories' ? '知识库分类' : tab === 'datasets' ? '数据集' : '文档' }}
            <span 
              v-if="knowledgeTab === tab" 
              class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"
            ></span>
          </button>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="mb-6 flex space-x-4">
          <div class="flex-1 relative">
            <input 
              v-model="searchQuery" 
              type="text" 
              placeholder="搜索..." 
              class="w-full pl-10 pr-4 py-2 border rounded-md"
            />
            <el-icon class="absolute left-3 top-2.5 text-gray-400"><Search /></el-icon>
          </div>
          
        </div>
        
        <!-- 内容区域 -->
        <div class="modal-content-container" style="height: 50vh; overflow-y: auto;">
          <!-- 知识分类列表 -->
          <div v-if="knowledgeTab === 'categories'" class="grid grid-cols-4 gap-4">
            <div v-if="loadingCategories" class="col-span-4 text-center py-8">
              <span class="material-icons animate-spin text-blue-500 text-2xl">sync</span>
              <p class="mt-2 text-gray-500">加载分类中...</p>
            </div>
            <div v-else-if="categories.length === 0" class="col-span-4 text-center py-8">
              <el-icon class="text-gray-400 text-4xl mb-2"><InfoFilled /></el-icon>
              <p class="text-gray-500">暂无知识分类</p>
            </div>
            <div 
              v-for="category in categories" 
              :key="category.id"
              @click="selectCategory(category.id)"
              :class="['border rounded-md p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200', selectedCategory === category.id ? 'border-blue-500 bg-blue-50' : '']"
            >
              <div class="font-medium mb-1">{{ category.name }}</div>
              <div class="text-sm text-gray-500">{{ category.description || '暂无描述' }}</div>
            </div>
          </div>
          
          <!-- 数据集列表 -->
          <div v-else-if="knowledgeTab === 'datasets'" class="grid grid-cols-4 gap-4">
            <div v-if="loadingDatasets" class="col-span-4 text-center py-8">
              <span class="material-icons animate-spin text-blue-500 text-2xl">sync</span>
              <p class="mt-2 text-gray-500">加载数据集中...</p>
            </div>
            <div v-else-if="datasets.length === 0" class="col-span-4 text-center py-8">
              <el-icon class="text-gray-400 text-4xl mb-2"><InfoFilled /></el-icon>
              <p class="text-gray-500">暂无数据集</p>
            </div>
            <div 
              v-for="dataset in datasets" 
              :key="dataset.id"
              @click="loadDocuments(dataset.id)"
              class="border rounded-md p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200"
            >
              <div class="font-medium mb-1">{{ dataset.name }}</div>
              <div class="text-sm text-gray-500">{{ dataset.description || '暂无描述' }}</div>
              <div class="text-xs text-gray-400 mt-2">文档数: {{ dataset.count || 0 }}</div>
            </div>
          </div>
          
          <!-- 文档列表 -->
          <div v-else-if="knowledgeTab === 'documents'" class="grid grid-cols-4 gap-4">
            <div v-if="loadingDocuments" class="col-span-4 text-center py-8">
              <span class="material-icons animate-spin text-blue-500 text-2xl">sync</span>
              <p class="mt-2 text-gray-500">加载文档中...</p>
            </div>
            <div v-else-if="documents.length === 0" class="col-span-4 text-center py-8">
              <el-icon class="text-gray-400 text-4xl mb-2"><InfoFilled /></el-icon>
              <p class="text-gray-500">暂无文档</p>
            </div>
            <div 
              v-for="doc in documents" 
              :key="doc.id"
              @click="toggleDocument(doc.id)"
              :class="['border rounded-md p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200', selectedDocuments.includes(doc.id) ? 'border-blue-500 bg-blue-50' : '']"
            >
              <div class="font-medium truncate mb-1">{{ doc.name }}</div>
              <div class="flex justify-between text-xs text-gray-400 mt-2">
                <span>{{ doc.type || '未知类型' }}</span>
                <span>{{ formatFileSize(doc.size) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 知识库错误提示 -->
        <div v-if="knowledgeError" class="mt-4 p-3 bg-red-50 border-l-2 border-red-400 text-red-700 text-sm">
          <span class="flex items-center"><el-icon class="mr-2"><WarningFilled /></el-icon> {{ knowledgeError }}</span>
        </div>
        
        <!-- 选择统计 -->
        <div class="p-4 bg-gray-50 border-t border-gray-200 mt-4">
          <div v-if="selectedDocuments.length > 0" class="mb-2">
            <div class="text-sm font-medium mb-2">已选择:</div>
            <div class="flex flex-wrap gap-2">
              <span class="text-sm font-medium text-gray-500">文档 ({{ selectedDocuments.length }}):</span>
              <span v-for="id in selectedDocuments" :key="id" class="inline-flex items-center px-3 py-1 rounded text-sm font-medium bg-green-100 text-green-800">
                {{ getDocumentName(id) }}
                <button @click.stop="toggleDocument(id)" class="ml-2 text-green-500 hover:text-green-700">
                  <el-icon><Close /></el-icon>
                </button>
              </span>
            </div>
          </div>
          <div v-else class="text-center text-gray-500">
            未选择任何文档
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button @click="showKnowledgeModal = false">取消</el-button>
          <el-button type="primary" @click="showKnowledgeModal = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import ImportLessonPlanModal from '@/components/modals/ImportLessonPlanModal.vue'
import axios from 'axios'
import { pptApi } from '@/api/ppt'
import { knowledgeApi } from '@/api/knowledge'

// 导入 Element Plus 图标
import {
  HomeFilled,
  Document,
  Plus,
  Delete,
  ArrowUp,
  ArrowDown,
  Upload,
  Search,
  Close,
  Check,
  Refresh,
  Download,
  ArrowRight,
  Edit,
  DocumentAdd,
  FolderOpened,
  InfoFilled,
  Connection,
  WarningFilled,
  Picture,
  SetUp
} from '@element-plus/icons-vue'

// Router
const router = useRouter()

// Teacher Data
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// Form data
const pptTitle = ref('')
const pptDescription = ref('')
const pptSubject = ref('')
const pptGrade = ref('')
const aiPrompt = ref('')
const isGenerating = ref(false)
const newTag = ref('')
const selectedTags = ref([])
const slides = ref([])

// 任务状态相关
const currentTaskId = ref(null)
const progressPercentage = ref(0)
const progressMessage = ref('正在初始化...')
const currentSlideInfo = ref('')
const statusCheckInterval = ref(null)

// Computed properties
const isFormValid = computed(() => {
  return pptTitle.value.trim() !== '' && 
         pptSubject.value !== '' && 
         slides.value.length > 0
})

// Methods

const addTag = () => {
  if (newTag.value.trim() && !selectedTags.value.includes(newTag.value.trim())) {
    selectedTags.value.push(newTag.value.trim())
    newTag.value = ''
  }
}

const removeTag = (index) => {
  selectedTags.value.splice(index, 1)
}

const addNewSlide = () => {
  slides.value.push({
    title: `幻灯片 ${slides.value.length + 1}`,
    sections: [] // 空的sections数组，结构与后端返回的一致
  })
}

const removeSlide = (index) => {
  slides.value.splice(index, 1)
}

const moveSlide = (oldIndex, newIndex) => {
  if (newIndex >= 0 && newIndex < slides.value.length) {
    const slide = slides.value.splice(oldIndex, 1)[0]
    slides.value.splice(newIndex, 0, slide)
  }
}

const saveDraft = async () => {
  if (!pptTitle.value.trim()) {
    showNotificationMessage('请输入PPT标题')
    return
  }

  if (slides.value.length === 0) {
    showNotificationMessage('请至少添加一张幻灯片')
    return
  }

  try {
    const response = await pptApi.saveDraft({
      title: pptTitle.value,
      slides: slides.value,
      settings: {
        teaching_duration: selectedTeachingDuration.value,
        teaching_style: selectedTeachingStyle.value,
        specific_requirements: specificRequirements.value
      }
    })

    if (response && response.code === 0) {
      showNotificationMessage('PPT草稿保存成功')
    } else {
      throw new Error(response?.message || '保存失败')
    }
  } catch (error) {
    console.error('保存PPT草稿失败:', error)
    showNotificationMessage('保存PPT草稿失败: ' + (error.message || '未知错误'))
  }
}

const previewPPT = () => {
  // 预览PPT逻辑
  alert('PPT预览功能待实现')
}

// 添加错误状态变量
const errorMessage = ref('')

// 优化generateWithAI方法
const generateWithAI = async () => {
  // 重置错误信息
  errorMessage.value = ''
  
  if (!importedLessonPlan.value && !specificRequirements.value.trim()) {
    errorMessage.value = '请提供教案内容或具体要求'
    showNotificationMessage('请提供教案内容或具体要求')
    return
  }
  
  isGenerating.value = true
  isAnalyzing.value = true
  showSuccessNotice.value = false
  
  try {
    // 准备API请求参数
    const formData = new FormData()
    
    // 如果有导入的教案文件
    if (importedLessonPlan.value && importedLessonPlan.value.file) {
      formData.append('file', importedLessonPlan.value.file)
    } else {
      // 否则使用文本内容
      formData.append('content', specificRequirements.value)
    }
    
    // 添加其他参数
    formData.append('teaching_duration', selectedTeachingDuration.value)
    formData.append('teaching_style', selectedTeachingStyle.value)
    formData.append('specific_requirements', specificRequirements.value)
    
    // 添加选中的文档ID列表
    if (selectedDocuments.value.length > 0) {
      // 将文档ID数组转为JSON字符串传递
      formData.append('document_ids', JSON.stringify(selectedDocuments.value))
      console.log('发送选中文档IDs:', selectedDocuments.value)
    }
    
    // 调用后端API
    console.log('正在调用生成PPT API...')
    const response = await pptApi.generatePPT(formData)
    
    console.log('API返回结果:', response)
    
    // 处理API响应 - 修改为支持code为200的情况
    if (response && (response.code === 0 || response.code === 200)) {
      const result = response.data
      
      // 保存任务ID，开始轮询任务状态
      if (result.task_id) {
        console.log('获取到任务ID，开始轮询任务状态:', result.task_id)
        currentTaskId.value = result.task_id
        startStatusCheck()
      } else {
        console.error('未获取到有效的任务ID')
        errorMessage.value = '未获取到有效的任务ID'
      }
    } else {
      throw new Error(response?.message || '生成PPT失败')
    }
  } catch (error) {
    console.error('生成PPT失败:', error)
    errorMessage.value = '生成PPT失败: ' + (error.message || '未知错误')
    showNotificationMessage('生成PPT失败: ' + (error.message || '未知错误'))
    isGenerating.value = false
    isAnalyzing.value = false
  }
}

// Import Lesson Plan
const showImportModal = ref(false)
const importedLessonPlan = ref(null)

const handleLessonPlanImport = (lessonPlan) => {
  // Handle lesson plan import
  importedLessonPlan.value = lessonPlan
}

const removeImportedPlan = () => {
  // Remove imported lesson plan
  importedLessonPlan.value = null
}

// Upload file
const uploadInput = ref(null)
const selectedTeachingDuration = ref('45')
const selectedTeachingStyle = ref('balanced')
const specificRequirements = ref('')

const showNotificationMessage = (message) => {
  notificationMessage.value = message
  showNotification.value = true
  setTimeout(() => {
    showNotification.value = false
  }, 3000)
}

const handleFileUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    alert('文件大小不能超过10MB')
    return
  }
  
  // 将文件信息存储到导入的教案中
  importedLessonPlan.value = {
    name: file.name,
    points: Math.floor(Math.random() * 20) + 30, // 随机生成30-50个知识点，实际项目中应该计算实际知识点
    size: file.size,
    file: file // 保存文件对象
  }
  
  showNotificationMessage('教案文件上传成功')
  
  // 清空文件输入框
  if (uploadInput.value) {
    uploadInput.value.value = ''
  }
}

// 生命周期钩子
onMounted(() => {
  // 加载知识库分类
  loadKnowledgeCategories()
  
  // 加载学科列表
  fetchSubjects()
  
  // 尝试恢复之前的编辑状态
  try {
    const savedData = localStorage.getItem('pptEditorData')
    if (savedData) {
      const data = JSON.parse(savedData)
      slides.value = data.slides || []
      importedLessonPlan.value = data.importedLessonPlan
      if (data.settings) {
        selectedTeachingDuration.value = data.settings.teachingDuration
        selectedTeachingStyle.value = data.settings.teachingStyle
        specificRequirements.value = data.settings.specificRequirements
      }
    }
  } catch (error) {
    console.error('恢复编辑状态失败:', error)
  }
})

// Notification
const showNotification = ref(false)
const notificationMessage = ref('')

const showSuccessNotice = ref(false)
const isAnalyzing = ref(false)

// 定义轮询间隔时间（毫秒）
const POLLING_INTERVAL = 8000 // 8秒



// 添加新的副标题和内容组合
const addSection = (index) => {
  slides.value[index].sections.push({
    subtitle: '', // 与后端返回的字段保持一致
    content: ''   // 与后端返回的字段保持一致
  })
}

// 删除副标题和内容组合
const removeSection = (slideIndex, sectionIndex) => {
  slides.value[slideIndex].sections.splice(sectionIndex, 1)
}

// 拖拽文件上传逻辑
const dragover = ref(false)
const onFileDrop = (event) => {
  event.preventDefault()
  const files = event.dataTransfer.files
  handleFileUpload({ target: { files } })
}

// 开始轮询检查任务状态
const startStatusCheck = () => {
  // 清除可能存在的旧定时器
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }
  
  console.log('开始轮询任务状态...')
  // 设置新的定时器，每8秒检查一次任务状态
  statusCheckInterval.value = setInterval(checkTaskStatus, POLLING_INTERVAL)
}

// 检查任务状态
const checkTaskStatus = async () => {
  if (!currentTaskId.value) return
  
  try {
    console.log('正在检查任务状态:', currentTaskId.value)
    const response = await pptApi.getTaskStatus(currentTaskId.value)
    console.log('任务状态响应:', response)
    
    // 修改为支持code为200的情况
    if (response && (response.code === 0 || response.code === 200)) {
      const taskStatus = response.data
      
      // 更新进度信息
      progressPercentage.value = taskStatus.progress || 0
      progressMessage.value = taskStatus.message || '正在处理...'
      
      // 更新任务数据
      if (taskStatus.data) {
        // 如果有markdown数据，直接存储
        if (taskStatus.data.markdown) {
          taskMarkdown.value = taskStatus.data.markdown
          console.log('获取到Markdown数据')
          
          // 从markdown提取标题，如果还没有设置标题
          if (!pptTitle.value && taskMarkdown.value) {
            const titleMatch = taskMarkdown.value.match(/^# (.+)$/m);
            if (titleMatch && titleMatch[1]) {
              pptTitle.value = titleMatch[1];
            }
          }
          
          // 从markdown解析slides信息，用于显示进度
          updateSlidesFromMarkdown(taskMarkdown.value);
        }
      }
      
      // 检查任务是否完成
      if (taskStatus.status === 'completed') {
        console.log('任务已完成')
        clearInterval(statusCheckInterval.value)
        showSuccessNotice.value = true
        isGenerating.value = false
        isAnalyzing.value = false
        showNotificationMessage('PPT内容已成功生成')
        
        // 自动打开文多多编辑器
        nextTick(() => {
          goToDocmeeEditor()
        })
      } else if (taskStatus.status === 'failed') {
        console.log('任务失败')
        clearInterval(statusCheckInterval.value)
        errorMessage.value = `生成PPT失败: ${taskStatus.error || '未知错误'}`
        isGenerating.value = false
        isAnalyzing.value = false
      }
    }
  } catch (error) {
    console.error('获取任务状态失败:', error)
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }
})

// 修改pptApi对象，添加获取任务状态的方法
// 需要在src/api/ppt.js文件中添加getTaskStatus方法
if (!pptApi.getTaskStatus) {
  pptApi.getTaskStatus = async (taskId) => {
    try {
      return await axios.get(`/api/ppt/task/${taskId}`)
    } catch (error) {
      console.error('获取任务状态失败:', error)
      throw error
    }
  }
}

// 知识库文档相关
const knowledgeTab = ref('categories')
const searchQuery = ref('')
const filterType = ref('')
const filterSubject = ref('')
const selectedDatasets = ref([])
const selectedDocuments = ref([])
const loadingCategories = ref(false)
const loadingDatasets = ref(false)
const loadingDocuments = ref(false)
const knowledgeError = ref('')

// 知识库数据
const categories = ref([])
const datasets = ref([])
const documents = ref([])
const selectedCategory = ref(null)

// 学科相关
const subjectList = ref([])
const selectedSubject = ref(null)

// 获取学科列表
const fetchSubjects = async () => {
  try {
    const response = await pptApi.getSubjects()
    if (response.code === 200 || response.code === 0) {
      subjectList.value = response.data || []
      // 默认选择第一个学科
      if (subjectList.value.length > 0 && !selectedSubject.value) {
        selectedSubject.value = subjectList.value[0].id
      }
    } else {
      console.error('获取学科列表失败:', response.message)
    }
  } catch (error) {
    console.error('获取学科列表失败:', error)
  }
}

// 修改loadKnowledgeCategories函数以确保实际调用API
const loadKnowledgeCategories = async () => {
  loadingCategories.value = true
  knowledgeError.value = ''
  try {
    console.log('正在获取知识库分类数据...')
    const response = await knowledgeApi.getAllCategories()
    console.log('知识库分类数据:', response)
    if (response.code === 0 || response.code === 200) {
      categories.value = response.data
    } else {
      knowledgeError.value = `获取知识库分类失败: ${response.message || '未知错误'}`
      console.error(knowledgeError.value)
    }
  } catch (error) {
    knowledgeError.value = `获取知识库分类出错: ${error.message || '未知错误'}`
    console.error('获取知识库分类出错:', error)
  } finally {
    loadingCategories.value = false
  }
}

// 修改selectCategory函数
const selectCategory = async (categoryId) => {
  if (selectedCategory.value === categoryId) {
    selectedCategory.value = null
  } else {
    selectedCategory.value = categoryId
    await loadDatasets(categoryId)
  }
}

// 修改loadDatasets函数确保实际调用API
const loadDatasets = async (categoryId) => {
  if (!categoryId) return
  loadingDatasets.value = true
  knowledgeError.value = ''
  datasets.value = []
  try {
    console.log(`正在获取分类ID ${categoryId} 的数据集...`)
    const response = await knowledgeApi.getDatasetsByCategory(categoryId)
    console.log('数据集数据:', response)
    if (response.code === 0 || response.code === 200) {
      datasets.value = response.data
      knowledgeTab.value = 'datasets'
    } else {
      knowledgeError.value = `获取知识库数据集失败: ${response.message || '未知错误'}`
      console.error(knowledgeError.value)
    }
  } catch (error) {
    knowledgeError.value = `获取知识库数据集出错: ${error.message || '未知错误'}`
    console.error('获取知识库数据集出错:', error)
  } finally {
    loadingDatasets.value = false
  }
}

// 修改loadDocuments函数确保实际调用API
const loadDocuments = async (datasetId) => {
  if (!datasetId) return
  loadingDocuments.value = true
  knowledgeError.value = ''
  try {
    console.log(`正在获取数据集ID ${datasetId} 的文档...`)
    const response = await knowledgeApi.getDocumentsByDataset(datasetId)
    console.log('文档数据:', response)
    if (response.code === 0 || response.code === 200) {
      // 直接替换文档列表，而不是添加
      documents.value = response.data || []
      knowledgeTab.value = 'documents'
    } else {
      knowledgeError.value = `获取知识库文档失败: ${response.message || '未知错误'}`
      console.error(knowledgeError.value)
    }
  } catch (error) {
    knowledgeError.value = `获取知识库文档出错: ${error.message || '未知错误'}`
    console.error('获取知识库文档出错:', error)
  } finally {
    loadingDocuments.value = false
  }
}

// 切换选择文档
const toggleDocument = (docId) => {
  const index = selectedDocuments.value.indexOf(docId)
  if (index === -1) {
    selectedDocuments.value.push(docId)
  } else {
    selectedDocuments.value.splice(index, 1)
  }
}

// 获取数据集名称
const getDatasetName = (id) => {
  const dataset = datasets.value.find(d => d.id === id)
  return dataset ? dataset.name : id
}

// 获取文档名称
const getDocumentName = (id) => {
  const doc = documents.value.find(d => d.id === id)
  return doc ? doc.name : id
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听知识库选项卡变化
watch(knowledgeTab, (newTab) => {
  // 切换到分类tab时，重新加载分类数据
  if (newTab === 'categories' && categories.value.length === 0) {
    loadKnowledgeCategories()
  }
})

// 监听选中的数据集变化
watch(selectedDatasets, (newDatasets) => {
  // 当新选择数据集时，自动加载其文档
  if (newDatasets.length > 0) {
    const latestDatasetId = newDatasets[newDatasets.length - 1]
    loadDocuments(latestDatasetId)
  }
})

// 控制知识库选择弹窗的显示
const showKnowledgeModal = ref(false)

// 定义编辑状态对象
const editingStates = ref({})

// 设置编辑状态
const setEditingState = (id, isEditing) => {
  if (isGenerating.value) return
  editingStates.value[id] = isEditing
}

// 文多多AiPPT相关
const showDocmeeModal = ref(false)
const docmeeEditorRef = ref(null)
const exportedMarkdown = ref('')
const docmeeEditMode = ref(false)
const docmeePptId = ref('')

// 处理文多多AiPPT编辑器模态框的成功事件
const handleDocmeeSuccess = (message) => {
  console.log('DocmeePPT事件成功:', message)
  showNotificationMessage('PPT内容生成成功')
}

// 处理文多多AiPPT编辑器模态框的错误事件
const handleDocmeeError = (errorMsg) => {
  console.error('DocmeePPT错误:', errorMsg)
  errorMessage.value = errorMsg
}

// 处理文多多AiPPT编辑器模态框的关闭事件
const handleDocmeeClose = () => {
  console.log('DocmeePPT模态框已关闭')
}

// 处理文多多AiPPT生成完毕扣费事件
const handleDocmeeCharge = async (data) => {
  try {
    console.log('处理PPT生成扣费事件:', data)
    if (data && data.pptId) {
      // 保存生成的PPT ID，以便后续编辑
      docmeePptId.value = data.pptId
      
      // 获取当前选择的学科ID，默认使用第一个可用学科
      const subjectId = selectedSubject.value
      
      if (!subjectId) {
        console.error('保存PPT ID失败: 未选择学科')
        errorMessage.value = '保存PPT失败: 未选择学科'
        return
      }
      
      // 保存PPT ID到数据库
      const response = await pptApi.savePptId({
        ppt_id: data.pptId,
        title: pptTitle.value || '新建PPT',
        subject_id: subjectId,
        description: specificRequirements.value || null
      })
      
      if (response && response.code === 200) {
        console.log('PPT ID保存成功:', response.data)
        showNotificationMessage('PPT保存成功')
        
        // 切换到编辑模式
        docmeeEditMode.value = true
      } else {
        console.error('PPT ID保存失败:', response.message)
        errorMessage.value = `保存PPT失败: ${response.message || '未知错误'}`
      }
    }
  } catch (error) {
    console.error('保存PPT ID失败:', error)
    errorMessage.value = `保存PPT失败: ${error.message || '未知错误'}`
  }
}

// 添加用于存储任务返回的markdown的响应式变量
const taskMarkdown = ref('')

// 添加goToDocmeeEditor方法
const goToDocmeeEditor = async (pptId) => {
  if (pptId) {
    // 编辑模式
    // 跳转到编辑器页面，携带必要的查询参数
    router.push({
      path: '/teacher/ppt/editor',
      query: {
        editMode: 'true',
        pptId: pptId,
        title: pptTitle.value,
        subjectId: selectedSubject.value
      }
    })
    return
  }
  
  // 创建模式
  if (!pptTitle.value.trim()) {
    // 如果没有标题，使用当前时间作为默认标题
    pptTitle.value = '电商市场数据分析' + new Date().toLocaleString()
  }
  
  // 直接使用taskMarkdown，不做任何转换
  const markdownContent = taskMarkdown.value || ''
  
  console.log('准备跳转到编辑器，标题:', pptTitle.value)
  console.log('Markdown内容长度:', markdownContent.length)
  
  // 跳转到编辑器页面
  router.push({
    path: '/teacher/ppt/editor',
    query: {
      editMode: 'false',
      title: pptTitle.value,
      subjectId: selectedSubject.value,
      description: specificRequirements.value,
      markdown: encodeURIComponent(markdownContent)
    }
  })
}

// 添加方法解析大纲并更新slides数组
const updateSlidesFromMarkdown = (content) => {
  try {
    if (!content) return;
    
    // 解析简单大纲格式（每行一个标题）
    const lines = content.split('\n').filter(line => line.trim());
    const newSlides = [];
    
    let currentSlide = null;
    
    for (const line of lines) {
      // 检查是否为主标题行（格式如 "1. 标题"）
      const mainTitleMatch = line.match(/^(\d+)\.\s+(.+)$/);
      if (mainTitleMatch) {
        // 如果已有当前slide，先添加到数组
        if (currentSlide) {
          newSlides.push(currentSlide);
        }
        
        // 创建新的slide
        currentSlide = {
          title: mainTitleMatch[2],
          sections: []
        };
        continue;
      }
      
      // 检查是否为子标题行（格式如 "   1.1 子标题"）
      const subTitleMatch = line.match(/^\s+\d+\.\d+\s+(.+)$/);
      if (subTitleMatch && currentSlide) {
        currentSlide.sections.push({
          subtitle: subTitleMatch[1],
          content: ''
        });
      }
    }
    
    // 添加最后一个slide
    if (currentSlide) {
      newSlides.push(currentSlide);
    }
    
    // 更新slides
    if (newSlides.length > 0) {
      slides.value = newSlides;
      // 更新当前幻灯片信息
      currentSlideInfo.value = `已生成 ${slides.value.length} 张幻灯片`;
    }
  } catch (error) {
    console.error('解析内容失败:', error);
  }
}

</script>

<style scoped>
.success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #34D399;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1100;
  transform: translateY(-100px);
  opacity: 0;
  transition: all 0.5s ease;
}

.show-notification {
  transform: translateY(0);
  opacity: 1;
}

.slide-container {
  transition: all 0.3s ease;
}

.slide-container:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.slide-content {
  min-height: 140px;
  resize: vertical;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 拖拽上传区域样式 */
.border-dashed {
  border-style: dashed;
}

.border-dashed:hover {
  border-color: #3B82F6;
}

/* 内联编辑样式 */
input.border-dashed, textarea.border-dashed {
  transition: all 0.2s ease;
}

input:hover.border-dashed, textarea:hover.border-dashed {
  border-bottom-color: #e5e7eb;
}

input:focus.border-dashed, textarea:focus.border-dashed {
  border-bottom-color: #3B82F6;
}

textarea {
  resize: vertical;
  min-height: 60px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style> 