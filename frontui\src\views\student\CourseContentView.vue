<template>
  <div class="course-content-view">
    <!-- 顶部导航区域 -->
    <div class="top-navigation">
      <div class="nav-left">
        <router-link :to="{ name: 'student-courses' }" class="back-to-courses">
          <div class="back-icon">
            <i class="material-icons">arrow_back</i>
          </div>
        </router-link>
        <h1 class="course-title">{{ course ? course.name : '' }} - {{ currentChapter ? currentChapter.title : '' }}</h1>
      </div>
    </div>

    <!-- 主内容区域 -->
      <div class="main-content">
      <!-- 左侧内容区域 -->
      <div class="content-left">
        <!-- 视频播放器区域 -->
        <div class="video-player-container">
          <div class="video-player">
            <!-- HTML5视频播放器 -->
            <div class="video-wrapper">
              <video
                ref="videoPlayer"
                class="html5-video-player"
                controls
                @loadedmetadata="onVideoLoaded"
                @timeupdate="onVideoTimeUpdate"
                @play="onVideoPlay"
                @pause="onVideoPause"
                @ended="onVideoEnded"
                @error="onVideoError"
              >
                {{ t('courseContent.videoPlayerNotSupported') }}
              </video>
            </div>
          </div>
        </div>

        <!-- 标签导航栏 -->
        <div class="tab-navigation">
          <div class="tab" :class="{ active: activeTab === 'overview' }" @click="activeTab = 'overview'">
            {{ t('courseContent.tabs.overview') }}
          </div>
          <div class="tab" :class="{ active: activeTab === 'discussion' }" @click="activeTab = 'discussion'">
            {{ t('courseContent.tabs.discussion') }}
            <span class="comment-count" v-if="totalCommentCount > 0">({{ totalCommentCount }})</span>
          </div>
          <div class="video-controls">
            <button class="video-control-btn" @click="openNoteModal" title="{{ t('courseContent.controls.notes') }}">
              <el-icon><EditPen /></el-icon>
              <span>{{ t('courseContent.controls.notes') }}</span>
            </button>
            <button class="video-control-btn" @click="openVideoNav" title="{{ t('courseContent.controls.navigation') }}">
              <el-icon><Location /></el-icon>
              <span>{{ t('courseContent.controls.navigation') }}</span>
            </button>
            <button class="video-control-btn" @click="openRatingDialog" title="{{ t('courseContent.controls.rating') }}">
              <el-icon><Star /></el-icon>
              <span>{{ t('courseContent.controls.rating') }}</span>
            </button>
            </div>
          </div>
          
        <!-- 标签内容区域 -->
        <div class="tab-content">
          <!-- 课程概述内容 -->
          <div v-if="activeTab === 'overview'" class="overview-content">
            <!-- 课程数据信息 -->
            <div class="course-stats" v-if="course">
              <div class="stat-card">
                <i class="material-icons stat-icon">timer</i>
                <div class="stat-info">
                  <span class="stat-value">{{ course?.total_hours || 0 }}{{ t('courseContent.stats.hour') }}</span>
                  <span class="stat-label">{{ t('courseContent.stats.totalHours') }}</span>
                </div>
              </div>
              <div class="stat-card">
                <i class="material-icons stat-icon">group</i>
                <div class="stat-info">
                  <span class="stat-value">{{ course?.student_count || 0 }}</span>
                  <span class="stat-label">{{ t('courseContent.stats.studentsCount') }}</span>
                </div>
              </div>
              <div class="stat-card">
                <i class="material-icons stat-icon">star</i>
                <div class="stat-info">
                  <div class="flex items-center">
                    <el-rate
                      :model-value="course?.average_rating || 0"
                      disabled
                      show-score
                      text-color="#ff9900"
                      score-template="{value}"
                    />
                  </div>
                  <span class="stat-label">{{ course?.rating_count || 0 }}{{ t('courseContent.stats.ratingCount') }}</span>
                </div>
              </div>
            </div>
            
            <!-- 课程概述 -->
            <div v-if="course" class="course-overview-container">
              <CourseOverview
                :course-id="course.id"
                :overview="course.overview"
                :can-edit="false"
              />
            </div>
            
          </div>

          <!-- 评论讨论内容 -->
          <div v-if="activeTab === 'discussion'" class="discussion-content">
            <!-- 评论输入框 -->
            <div class="comment-input-container">
              <textarea 
                class="comment-input" 
                :placeholder="t('courseContent.comments.placeholder')" 
                v-model="newComment"
                rows="3"
              ></textarea>
              <button class="comment-submit" @click="addComment(newComment)">{{ t('courseContent.comments.submit') }}</button>
                      </div>
            
            <!-- 评论列表 -->
            <div class="comments-list">
              <div v-for="comment in comments" :key="comment.id" class="comment-item">
                <!-- 评论主体 -->
                <div class="comment-main">
                  <div class="comment-avatar">
                    <img :src="comment.user.avatar" :alt="comment.user" />
                    </div>
                  <div class="comment-content">
                    <div class="comment-header">
                      <span class="comment-username">
                        {{ comment.user.name }}
                        <span v-if="comment.user.isInstructor" class="instructor-badge">{{ t('courseContent.comments.instructor') }}</span>
                      </span>
                      <span class="comment-time">{{ comment.time }}</span>
                      </div>
                    <div class="comment-text">{{ comment.content }}</div>
                    <!-- 评论操作按钮 -->
                    <div class="comment-actions">
                      <button class="action-btn like-btn" @click="likeComment(comment)">
                        <i class="material-icons">star</i> {{ comment.likes }}
                      </button>
                      <button class="action-btn reply-btn" @click="comment.showReplyInput = !comment.showReplyInput">
                        <el-icon><ChatDotRound /></el-icon> {{ t('courseContent.comments.reply') }}
                      </button>
                    </div>
                    
                    <!-- 回复输入框 -->
                    <div v-if="comment.showReplyInput" class="reply-input-container">
                      <textarea 
                        class="reply-input" 
                        :placeholder="t('courseContent.comments.replyPlaceholder')" 
                        v-model="comment.replyText"
                        rows="2"
                      ></textarea>
                      <button class="reply-submit" @click="replyToComment(comment, comment.replyText); comment.replyText = ''; comment.showReplyInput = false;">{{ t('courseContent.comments.submitReply') }}</button>
                      </div>
                    </div>
                  </div>
                  
                <!-- 回复列表 -->
                <div v-if="comment.replies && comment.replies.length > 0" class="replies-list">
                  <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
                    <div class="reply-avatar">
                      <img :src="reply.user.avatar" :alt="reply.user.name" />
                  </div>
                    <div class="reply-content">
                      <div class="reply-header">
                        <span class="reply-username">
                          {{ reply.user.name }}
                          <span v-if="reply.user.isInstructor" class="instructor-badge">讲师</span>
                        </span>
                        <span class="reply-time">{{ reply.time }}</span>
                  </div>
                      <div class="reply-text">{{ reply.content }}</div>
                      <!-- 回复点赞按钮 -->
                      <div class="reply-actions">
                        <button class="action-btn like-btn" @click="reply.likes++">
                          <el-icon><Star /></el-icon> {{ reply.likes }}
                        </button>
                  </div>
                </div>
              </div>
            </div>
                </div>
                      </div>
          </div>
                    </div>
                  </div>
                  
      <!-- 右侧课程目录 -->
      <div class="content-right">
        <!-- 课程信息头部 -->
        <div class="course-sidebar-header">
          <h2 class="sidebar-title">{{ courseInfo.title }}</h2>
          <div class="course-meta">
            <div class="instructor">
              <i class="material-icons">person</i> {{ teacherInfo.teacher_name }} 
              <!-- <span v-if="teacherInfo.title" class="teacher-title">({{ teacherInfo.teacher_name }})</span> -->
            </div>
            <div class="lessons-count">
              <i class="material-icons">menu_book</i> {{ courseInfo.totalLessons }}{{ t('courseContent.sidebar.lessonsUnit') }}
            </div>
          </div>
          <div class="sidebar-progress">
            <div class="progress-text">{{ t('courseContent.sidebar.progress') }}: {{ courseInfo.totalProgress }}%</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: courseInfo.totalProgress + '%' }"></div>
            </div>
          </div>
        </div>

        <!-- 章节列表 -->
        <div class="course-chapters">
          <div v-for="(chapter, chapterIndex) in chapters" :key="chapter.id" class="chapter-item">
            <div class="chapter-header" @click="toggleChapter(chapterIndex)">
              <div class="chapter-title">{{ chapter.title }}</div>
              <div class="chapter-toggle">
                <i class="material-icons">{{ expandedChapters[chapterIndex] ? 'expand_less' : 'expand_more' }}</i>
              </div>
            </div>
            <div v-if="expandedChapters[chapterIndex]" class="chapter-lessons">
              <div 
                v-for="lesson in chapter.lessons" 
                :key="lesson.id" 
                class="lesson-item"
                :class="{ 'active': currentLesson && currentLesson.id === lesson.id }"
                @click="selectLesson(chapter, lesson)"
              >
                <div class="lesson-title">{{ lesson.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- AI学伴悬浮按钮 -->
    <div class="ai-companion-floating" @click="toggleAICompanion">
      <img src="@/assets/images/avatars/course-detai-1.gif" style="height:300px" alt="AI学伴" />
    </div>
    
    <!-- 笔记模态框 -->
    <div v-if="showNoteModal" class="modal-overlay">
      <div class="note-modal">
        <div class="modal-header">
          <div class="flex items-center">
            <i class="material-icons text-blue-600 text-lg mr-2">edit_note</i>
            <h3>{{ t('courseContent.noteModal.title') }}</h3>
          </div>
          <el-button class="modal-close" circle plain size="small" @click="closeNoteModal">
            <i class="material-icons">close</i>
          </el-button>
        </div>
        
        <div class="note-container">
          <!-- 左侧笔记列表 -->
          <div class="notes-sidebar">
            <div class="notes-search">
              <div class="search-input-wrapper">
                <input type="text" :placeholder="t('courseContent.noteModal.search')" v-model="noteSearchQuery" />
                <i class="material-icons search-icon">search</i>
              </div>
            </div>
            <div class="notes-list">
              <div v-for="note in filteredNotes" :key="note.id" class="note-item" @click="editNote(note)">
                <div class="note-header">
                  <h3 class="note-title">{{ note.title }}</h3>
                  <span class="note-timestamp">{{ note.timestamp }}</span>
                </div>
                <div class="note-content">{{ note.content }}</div>
                <div class="note-footer">
                  <span class="note-date">{{ formatDate(note.updated_at) }}</span>
                </div>
              </div>
            </div>
            
            <button class="new-note-btn" @click="createNewNote">
              <el-icon><Plus /></el-icon> {{ t('courseContent.noteModal.new') }}
            </button>
          </div>
      
          <!-- 右侧笔记编辑区 -->
          <div class="note-editor">
            <div class="note-editor-header">
              <input 
                type="text" 
                class="note-title-input" 
                :placeholder="t('courseContent.noteModal.titlePlaceholder')" 
                v-model="currentNote.title"
              />
              <div class="note-meta-controls">
                <div class="note-meta-info">
                  <div class="note-meta-item">
                    <label>{{ t('courseContent.noteModal.timePoint') }}:</label>
                    <input type="text" class="timestamp-input" v-model="currentNote.timestamp" placeholder="00:00" />
                  </div>
                </div>
                <div class="note-actions">
                  <el-button v-if="currentNote.id" class="delete-note-btn" circle type="danger" size="small" @click="confirmDeleteNote" :title="t('courseContent.noteModal.delete')">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          
            <!-- 编辑器工具栏 -->
            <div class="editor-toolbar">
              <div class="toolbar-group">
                <el-tooltip content="粗体" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertTextFormat('**', '**')">
                    <i class="material-icons">format_bold</i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="斜体" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertTextFormat('*', '*')">
                    <i class="material-icons">format_italic</i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="下划线" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertTextFormat('<u>', '</u>')">
                    <i class="material-icons">format_underlined</i>
                  </el-button>
                </el-tooltip>
              </div>
              
              <div class="toolbar-divider"></div>
              
              <div class="toolbar-group">
                <el-tooltip content="有序列表" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertOrderedList()">
                    <i class="material-icons">format_list_numbered</i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="无序列表" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertUnorderedList()">
                    <i class="material-icons">format_list_bulleted</i>
                  </el-button>
                </el-tooltip>
              </div>
              
              <div class="toolbar-divider"></div>
              
              <div class="toolbar-group">
                <el-tooltip content="标题" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertTextFormat('## ', '')">
                    <i class="material-icons">title</i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="代码块" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertTextFormat('```\n', '\n```')">
                    <i class="material-icons">code</i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="链接" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertLink()">
                    <i class="material-icons">link</i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="图片" placement="top">
                  <el-button class="toolbar-btn" type="text" @click="insertImage()">
                    <i class="material-icons">image</i>
                  </el-button>
                </el-tooltip>
              </div>
              
              <div class="toolbar-divider"></div>
              
              <div class="toolbar-group">
                <el-tooltip content="预览" placement="top">
                  <el-button class="toolbar-btn" type="text">
                    <i class="material-icons">visibility</i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="AI辅助" placement="top">
                  <el-button class="toolbar-btn" type="text">
                    <i class="material-icons">auto_fix_high</i>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
            <textarea 
              ref="noteContentEditor"
              class="note-content-editor" 
              :placeholder="t('courseContent.noteModal.contentPlaceholder')" 
              v-model="currentNote.content"
              @keydown.tab.prevent="handleTabKey"
            ></textarea>
            
            <div class="editor-footer">
              <div class="editor-info">
                <span>{{ t('courseContent.noteModal.markdownSupport') }}</span>
                <span class="char-count">{{ t('courseContent.noteModal.charCount', { count: currentNote.content.length }) }}</span>
              </div>
              <div class="editor-actions">
                <button class="cancel-btn" @click="closeNoteModal">{{ t('courseContent.noteModal.cancel') }}</button>
                <button class="save-note-btn" @click="saveNote">
                  <el-icon><Check /></el-icon> {{ t('courseContent.noteModal.save') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- AI视频导航模态框 -->
    <div v-if="showVideoNavModal" class="modal-overlay">
      <div class="video-nav-modal">
        <div class="modal-header">
          <h3>{{ t('courseContent.videoNavModal.title') }}</h3>
          <el-button class="modal-close" circle plain size="small" @click="closeVideoNav">
            <i class="material-icons">close</i>
          </el-button>
        </div>
        
        <div class="video-nav-content">
          <div class="video-summary">
            <h4>{{ t('courseContent.videoNavModal.summary') }}</h4>
            <p>{{ navigationData?.summary || videoNavigation?.summary || t('courseContent.videoNavModal.noSummary') }}</p>
          </div>
          
          <!-- 关键点导航 -->
          <div class="key-points-section">
            <h4>{{ t('courseContent.videoNavModal.keyPoints') }}</h4>
            <div class="key-points-list">
              <div v-if="!navigationData?.key_points || navigationData.key_points.length === 0" class="text-center text-muted my-3">
                {{ t('courseContent.videoNavModal.noKeyPoints') }}
              </div>
              <div 
                v-for="(keyPoint, index) in navigationData?.key_points || []" 
                :key="index"
                class="key-point-item"
                @click="jumpToKeyPoint(keyPoint.time)"
              >
                <div class="key-point-time">{{ keyPoint.time }}</div>
                <div class="key-point-content">{{ keyPoint.content }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- AI学伴面板 -->
    <div v-if="showAICompanion" class="ai-companion-panel">
      <div class="ai-panel-header">
        <div class="ai-panel-title">
          <!-- <div class="ai-avatar">
            <img src="@/assets/images/avatars/course-detai-1.gif" alt="AI学伴" />
          </div> -->
          <div class="ai-info">
            <h3>{{ t('courseContent.aiCompanion.title') }}</h3>
            <span class="ai-subtitle">{{ t('courseContent.aiCompanion.subtitle') }}</span>
          </div>
        </div>
        <div class="ai-panel-actions">
          <el-tooltip :content="t('courseContent.aiCompanion.selectCompanion')" placement="top">
            <el-button size="small" class="ai-action-btn companion-btn" @click="openCompanionDialog">
              <el-icon><UserFilled /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip :content="t('courseContent.aiCompanion.close')" placement="top">
            <el-button circle size="small" class="ai-action-btn close-btn" @click="toggleAICompanion">
              <el-icon><Close /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
      
      <div class="ai-messages-container">
        <div 
          v-for="(message, index) in aiCompanionMessages" 
          :key="index"
          class="ai-message"
          :class="message.sender"
        >
          <div class="message-avatar" v-if="message.sender !== 'user'">
            <img :src="getMessageAvatar(message)" :alt="message.role || 'AI'" />
          </div>
          <div class="message-wrapper">
            <div class="message-sender" v-if="message.sender !== 'user' && message.role">{{ getRoleName(message.role) }}</div>
            <div class="message-content" v-html="formatMessage(message.content)"></div>
            <div v-if="message.isStreaming" class="message-typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <div class="message-avatar user-avatar" v-if="message.sender === 'user'">
            <img :src="getMessageAvatar(message)" alt="用户" />
          </div>
        </div>
      </div>
      
      <div class="ai-input-container">
        <el-input
          type="textarea"
          class="ai-input"
          :placeholder="t('courseContent.aiCompanion.inputPlaceholder')"
          v-model="aiInputMessage"
          @keydown.enter.prevent="sendToAI(aiInputMessage); aiInputMessage = ''"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
        />
        <!-- 删除发送按钮 -->
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
    
    <!-- 学习伙伴选择对话框 -->
    <el-dialog
      v-model="showCompanionDialog"
      width="550px"
      class="companion-dialog"
      destroy-on-close
      :show-close="true"
      :title="t('courseContent.aiCompanionDialog.title')"
    >
      <div class="companion-dialog-content">
        <h4 class="companion-section-title">{{ t('courseContent.aiCompanionDialog.requiredRoles') }} <span class="required-roles-note">{{ t('courseContent.aiCompanionDialog.requiredRolesNote') }}</span></h4>
        <div class="companion-options-row">
          <div class="companion-option option-selected">
            <el-checkbox v-model="selectedTeacherRoles.assistant" class="companion-checkbox" disabled checked>
              <div class="companion-option-content">
                <div class="companion-avatar assistant">
                  <img src="@/assets/images/avatars/ai_assistant.png" alt="助教" />
                </div>
                <div class="companion-info">
                  <div class="companion-name">{{ t('courseContent.aiCompanionDialog.roles.teacher.name') }}</div>
                  <div class="companion-desc" :data-tooltip="t('courseContent.aiCompanionDialog.roles.teacher.desc')">{{ t('courseContent.aiCompanionDialog.roles.teacher.desc') }}</div>
                </div>
              </div>
            </el-checkbox>
          </div>
          <!-- 添加一个空的div来平衡布局 -->
          <div class="companion-option invisible" style="visibility: hidden;">
          </div>
        </div>
        
        <h4 class="companion-section-title">{{ t('courseContent.aiCompanionDialog.companionRoles') }} <span class="optional-roles-note">{{ t('courseContent.aiCompanionDialog.optionalRolesNote') }}</span></h4>
        <div class="companion-options-grid">
          <div class="companion-option" :class="{ 'option-selected': selectedStudentRoles.funnyKid }">
            <el-checkbox v-model="selectedStudentRoles.funnyKid" class="companion-checkbox">
              <div class="companion-option-content">
                <div class="companion-avatar funnyKid">
                  <img src="@/assets/images/avatars/comedian.png" alt="小明" />
                </div>
                <div class="companion-info">
                  <div class="companion-name">{{ t('courseContent.aiCompanionDialog.roles.funnyKid.name') }}</div>
                  <div class="companion-desc" :data-tooltip="t('courseContent.aiCompanionDialog.roles.funnyKid.desc')">{{ t('courseContent.aiCompanionDialog.roles.funnyKid.desc') }}</div>
                </div>
              </div>
            </el-checkbox>
          </div>
          
          <div class="companion-option" :class="{ 'option-selected': selectedStudentRoles.thinker }">
            <el-checkbox v-model="selectedStudentRoles.thinker" class="companion-checkbox">
              <div class="companion-option-content">
                <div class="companion-avatar thinker">
                  <img src="@/assets/images/avatars/genius.png" alt="李华" />
                </div>
                <div class="companion-info">
                  <div class="companion-name">{{ t('courseContent.aiCompanionDialog.roles.thinker.name') }}</div>
                  <div class="companion-desc" :data-tooltip="t('courseContent.aiCompanionDialog.roles.thinker.desc')">{{ t('courseContent.aiCompanionDialog.roles.thinker.desc') }}</div>
                </div>
              </div>
            </el-checkbox>
          </div>
          
          <div class="companion-option" :class="{ 'option-selected': selectedStudentRoles.curious }">
            <el-checkbox v-model="selectedStudentRoles.curious" class="companion-checkbox">
              <div class="companion-option-content">
                <div class="companion-avatar curious">
                  <img src="@/assets/images/avatars/student1.png" alt="张颖" />
                </div>
                <div class="companion-info">
                  <div class="companion-name">{{ t('courseContent.aiCompanionDialog.roles.curious.name') }}</div>
                  <div class="companion-desc" :data-tooltip="t('courseContent.aiCompanionDialog.roles.curious.desc')">{{ t('courseContent.aiCompanionDialog.roles.curious.desc') }}</div>
                </div>
              </div>
            </el-checkbox>
          </div>
          
          <div class="companion-option" :class="{ 'option-selected': selectedStudentRoles.topStudent }">
            <el-checkbox v-model="selectedStudentRoles.topStudent" class="companion-checkbox">
              <div class="companion-option-content">
                <div class="companion-avatar topStudent">
                  <img src="@/assets/images/avatars/partner.png" alt="赵阳" />
                </div>
                <div class="companion-info">
                  <div class="companion-name">{{ t('courseContent.aiCompanionDialog.roles.topStudent.name') }}</div>
                  <div class="companion-desc" :data-tooltip="t('courseContent.aiCompanionDialog.roles.topStudent.desc')">{{ t('courseContent.aiCompanionDialog.roles.topStudent.desc') }}</div>
                </div>
              </div>
            </el-checkbox>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCompanionDialog = false">{{ t('general.cancel') }}</el-button>
          <el-button type="primary" @click="confirmCompanionSelection">{{ t('courseContent.aiCompanionDialog.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 测试对话面板 -->
    <el-dialog
      v-model="showTestDialoguePanel"
      title="测试预设对话"
      width="380px"
    >
      <div class="test-dialogue-panel">
        <h4>选择要触发的对话</h4>
        <div class="preset-dialogues-list">
          <div 
            v-for="(dialogue, index) in testDialogues" 
            :key="dialogue.id || index"
            class="test-dialogue-item"
            @click="triggerTestDialogue(dialogue)"
          >
            <div class="test-dialogue-title">
              <span>{{ dialogue.title || `对话 ${index + 1}` }}</span>
              <span v-if="dialogue.triggerTime" class="dialogue-time">{{ dialogue.triggerTime }}</span>
            </div>
            <div class="test-dialogue-preview">
              {{ getDialoguePreview(dialogue) }}
            </div>
          </div>
          
          <div class="no-dialogues" v-if="testDialogues.length === 0">
            没有可用的预设对话。请尝试加载课程或刷新页面。
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 评分弹层 -->
    <el-dialog
      v-model="showRatingDialog"
      :title="t('courseContent.ratingDialog.title')"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="rating-dialog-content">
        <div class="rating-stars">
          <el-rate
            v-model="userRating"
            allow-half
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRatingDialog = false">{{ t('courseContent.ratingDialog.cancel') }}</el-button>
          <el-button type="primary" @click="submitRating">{{ t('courseContent.ratingDialog.submit') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { User, Reading, EditPen, Location, Star, StarFilled, Collection, CollectionTag, ChatDotRound, Document, UserFilled, Delete, QuestionFilled, Close, Position, DocumentCopy, School, Service, Cpu, Notebook,
    Message, Avatar, Timer, Warning, Plus, Search, Edit, Promotion, Check, View, List, Link, Picture } from '@element-plus/icons-vue'
import { ref, onMounted, computed, watch, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n'; // 添加 i18n 支持
import { 
  getVideoDialogues, 
  getDialoguesAtTimestamp, 
  getNewDialoguesAtTimestamp,
  resetSession,
  generateSessionId,
  getVideoNavigation,
  getVideoTeacherInfo,
  chatWithAIStream
} from '@/api/videoAssistant';
import { courseApi } from '@/api/course';
import { lessonApi } from '@/api/lesson';
import { getNotes, createNote, updateNote, deleteNote } from '@/api/note';
import { commentApi } from '@/api/comment'
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import axios from 'axios';

// 导入所有需要的头像图片
import userAvatar from '@/assets/images/avatars/student1.png';
import teacherAvatar from '@/assets/images/avatars/teacher.png';
import assistantAvatar from '@/assets/images/avatars/ai_assistant.png';
import thinkerAvatar from '@/assets/images/avatars/genius.png';
import questionerAvatar from '@/assets/images/avatars/student1.png';
import notetakerAvatar from '@/assets/images/avatars/partner.png';
import challengerAvatar from '@/assets/images/avatars/comedian.png';
import digitalTeacher from '@/assets/images/digital_teachers/63b10801.gif';
import student2Avatar from '@/assets/images/avatars/student2.png';
// 导入新的角色头像
import coderAvatar from '@/assets/images/avatars/teacher1.png'; // 暂时使用已有的图片替代
import mentorAvatar from '@/assets/images/avatars/student2.png'; // 暂时使用已有的图片替代

// 导入外部 CSS 文件
import '../../css/CourseContentView.css';

import CourseOverview from '@/components/course/CourseOverview.vue'

export default {
  name: 'CourseContentView',
  components: {
    CourseOverview,
    User,
    Reading,
    EditPen,
    Location,
    Star,
    StarFilled,
    Collection,
    CollectionTag,
    ChatDotRound,
    Document,
    UserFilled,
    Delete,
    QuestionFilled,
    Close,
    Position,
    DocumentCopy,
    School,
    Service,
    Cpu,
    Notebook,
    Message,
    Avatar,
    Timer,
    Warning,
    Plus,
    Search,
    Edit,
    Promotion,
    Check,
    View,
    List,
    Link,
    Picture
  },
  setup() {
    const { t } = useI18n(); // 初始化 i18n
    return { t };
  },
  data() {
    return {
      // UI 状态
      activeTab: 'overview',
      isLiked: false,
      isFavorited: false,
      
      // 模态框状态
      showNoteModal: false,
      showVideoNavModal: false,
      showAICompanion: false,
      showCompanionDialog: false,
      
      // 输入数据
      aiInputMessage: '',
      noteSearchQuery: '',
      newComment: '',
      
      // 课程数据
      courseInfo: {
        title: 'Python编程',
        chapter: '第3章：函数与模块',
        currentSection: '3.2 参数传递',
        instructor: '刘教授',
        totalLessons: 36,
        totalProgress: 60,
        duration: '15:20',
        studentsCount: 3254,
        rating: 4.5,
        ratingCount: 128
      },
      
      // 新增数据
      course: null,
      chapters: [],
      currentChapter: null,
      currentLesson: null,
      teacherInfo: {
        teacher_name: '',
        introduction: '',
        avatar: '',
        college: ''
      },
      
      
      
      // 章节数据 - 已删除
      chapters: [
      ],
      
      // 学习要点
      keyPoints: [
        '理解位置参数的传递机制',
        '掌握关键字参数的使用方法',
        '学习默认参数的定义与应用',
        '理解可变参数(*args)和关键字可变参数(**kwargs)',
        '参数传递中的常见错误与规避方法'
      ],
      
      // 评论数据
      comments: [],
      commentCount: 0,
      
      // AI视频导航数据
      videoNavigation: {
        summary: '本视频详细讲解了Python函数的参数传递机制，包括位置参数、关键字参数、默认参数和可变参数等内容，并通过实际案例演示了各种参数的使用方法和注意事项。'
      },
      
      // 笔记数据
      notes: [
        /*
        { id: 1, title: '函数参数类型总结', chapter: '3.2', timestamp: '05:20', content: '# 参数类型总结\n\n## 位置参数\n- 调用时必须按顺序提供\n- 例：`def func(a, b): ...`\n\n## 关键字参数\n- 调用时使用参数名\n- 例：`func(b=5, a=3)`\n\n## 默认参数\n- 定义时提供默认值\n- 例：`def func(a, b=5): ...`' },
        { id: 2, title: '可变参数笔记', chapter: '3.2', timestamp: '09:45', content: '# 可变参数用法\n\n## *args\n- 接收任意数量的位置参数\n- 在函数内部表现为元组\n\n## **kwargs\n- 接收任意数量的关键字参数\n- 在函数内部表现为字典\n\n## 混合使用示例\n```python\ndef example(a, b=5, *args, **kwargs):\n    print(a, b, args, kwargs)\n```' }
        */
      ],
      
      // 当前编辑的笔记
      currentNote: {
        id: null,
        title: '',
        chapter: '3.2',
        timestamp: '00:00',
        content: ''
      },
      
      // 通知系统
      notification: {
        show: false,
        type: 'info',
        message: '',
        timeout: null
      },
      
      // AI学伴对话记录
      aiCompanionMessages: [],
      
      // AI聊天历史 - 用于与DeepSeek API通信
      aiChatHistory: [],
      
      // AI学伴设置
      aiCompanionSettings: {
        interactiveMode: true
      },
      
      // 学习伙伴选择
      selectedTeacherRoles: {
        assistant: true
      },
      selectedStudentRoles: {
        funnyKid: true,
        thinker: true,
        curious: true,
        topStudent: true
      },
      
      // 新增AI角色
      aiRoles: {
        assistant: {
          name: '王老师',
          description: '讲师角色，负责解释概念、回答问题',
          avatar: assistantAvatar,
          personality: '专业、有耐心、知识渊博，语气专业且耐心',
          // 多语言名称
          multiLangNames: {
            zh: '王老师',
            en: 'Prof. Wang',
            vi: 'GS. Wang',
            id: 'Prof. Wang'
          }
        },
        funnyKid: {
          name: '小明(班宠萌娃)',
          description: '活跃开朗的学生，经常开玩笑活跃课堂气氛',
          avatar: challengerAvatar,
          personality: '幽默、活泼、语言轻松幽默，偶尔带有emoji表情',
          multiLangNames: {
            zh: '小明(班宠萌娃)',
            en: 'Xiao Ming (Class Pet)',
            vi: 'Tiểu Minh (Cưng của lớp)',
            id: 'Xiao Ming (Kesayangan Kelas)'
          }
        },
        thinker: {
          name: '李华(沉思者)',
          description: '提供深入讨论的学生',
          avatar: thinkerAvatar,
          personality: '善于思考问题的本质和提出有深度的见解，语言表达严谨',
          multiLangNames: {
            zh: '李华(沉思者)',
            en: 'Li Hua (Deep Thinker)',
            vi: 'Lý Hoa (Người suy tư)',
            id: 'Li Hua (Pemikir Mendalam)'
          }
        },
        curious: {
          name: '张颖(好奇宝宝)',
          description: '积极提出问题的学生',
          avatar: questionerAvatar,
          personality: '不懂就问，问题简单直接，有助于引出重要概念的解释',
          multiLangNames: {
            zh: '张颖(好奇宝宝)',
            en: 'Zhang Ying (Curious Student)',
            vi: 'Trương Dĩnh (Học sinh tò mò)',
            id: 'Zhang Ying (Siswa Penasaran)'
          }
        },
        topStudent: {
          name: '赵阳(卷王学霸)',
          description: '整理学习要点的学生',
          avatar: notetakerAvatar,
          personality: '善于用符号标记（如⭐、📌）和序号梳理关键内容，语言简洁有力',
          multiLangNames: {
            zh: '赵阳(卷王学霸)',
            en: 'Zhao Yang (Top Student)',
            vi: 'Triệu Dương (Học sinh giỏi)',
            id: 'Zhao Yang (Siswa Terbaik)'
          }
        }
      },
      
      // 视频播放器相关
      videoPlayer: null,
      videoId: '', // 当前视频ID，将根据当前课时动态更新
      currentVideoTime: '00:00', // 当前视频时间
      videoTimeUpdateInterval: null, // 定时更新视频时间的interval
      lastCheckedTime: -1, // 上次检查对话的时间点（秒）
      aiDialoguesLoaded: false, // 是否已加载AI对话
      presetDialogues: [], // 预设的AI对话
      triggeredDialogueIds: new Set(), // 已触发的对话ID集合
      skippedDialogueIds: new Set(), // 已跳过的对话ID集合
      lastAIResponseTime: 0, // AI最后回复的时间戳
      dialogueCooldownPeriod: 5000, // 对话冷却期(毫秒)
      isAIResponding: false, // 标记AI是否正在回复用户消息
      videoSessionId: '', // 视频会话ID，用于跟踪对话显示状态
      bilibiliPlayer: null, // B站播放器对象
      usingBackendAPI: true, // 是否使用后端API
      videoDuration: 0, // 视频总长度(秒)
      lastProgressPercentage: 0, // 上次检查的进度百分比
      showTestDialoguePanel: false,
      testDialogues: [], // 可测试的对话列表
      isVideoPlaying: false, // 视频是否正在播放
      localVideoSource: 'https://clkj-ai-education.oss-cn-shenzhen.aliyuncs.com/19_1745734279.mp4', // 本地视频源
      
      // 添加关键点导航数据
      navigationData: {
          key_points: [],
          summary: ''
      },
      
      // 章节展开状态
      expandedChapters: {},

      // AI回答异常时的后备回复列表
      fallbackResponses: [
        "我刚才好像走神了，能请您再说一遍您的问题吗？",
        "关于这个问题，我需要思考一下。基于视频内容，我认为...",
        "这是一个很好的问题！让我根据视频内容来解答...",
        "您的问题很有深度。根据我们学习的内容，我认为...",
        "这个问题很有意思，根据视频中提到的内容，我们可以这样理解..."
      ],
      
      // 评分相关
      userRating: 0,
      originalRating: 0,
      showRatingDialog: false,
    };
  },
  
  computed: {
    // 根据搜索条件筛选笔记
    filteredNotes() {
      if (!this.noteSearchQuery) return this.notes;
      const query = this.noteSearchQuery.toLowerCase();
      return this.notes.filter(note => 
        note.title.toLowerCase().includes(query) || 
        note.content.toLowerCase().includes(query)
      );
    },
    
    // 获取当前激活的角色
    activeAIRoles() {
      const roles = [];
      
      // 添加必选角色
      if (this.selectedTeacherRoles.assistant) roles.push('assistant');
      
      // 添加可选学生角色
      Object.entries(this.selectedStudentRoles).forEach(([role, isActive]) => {
        if (isActive) roles.push(role);
      });
      
      return roles;
    },
    
    hasRatingChanged() {
      return this.userRating !== this.originalRating
    },
    totalCommentCount() {
      return this.comments.reduce((total, comment) => {
        // 计算主评论和回复的总数
        return total + 1 + (comment.replies ? comment.replies.length : 0)
      }, 0)
    },

    // 获取本地化的AI角色信息
    localizedAIRoles() {
      const currentLang = this.$i18n.locale || 'zh'
      const localizedRoles = {}

      Object.keys(this.aiRoles).forEach(roleKey => {
        const role = this.aiRoles[roleKey]
        localizedRoles[roleKey] = {
          ...role,
          name: role.multiLangNames?.[currentLang] || role.multiLangNames?.zh || role.name
        }
      })

      return localizedRoles
    }
  },

  async mounted() {
    // 初始化视频交互
    await this.initVideoInteraction();

    // 加载视频导航数据
    this.loadVideoNavigation();

    // 加载课程数据
    await this.loadCourseData();

    // 加载笔记
    await this.loadNotes();

    // 使用事件委托添加时间跳转链接的点击处理
    document.addEventListener('click', this.handleTimeJumpClick);

    // 加载评论
    await this.loadComments()
  },

  watch: {
    // 监听语言变化
    '$i18n.locale': {
      handler: async function(newLang) {
        console.log('课程详情页语言切换到:', newLang)
        // 重新加载课程数据
        await this.loadCourseData()
      },
      immediate: false
    }
  },

  beforeUnmount() {
    // 清除定时器
    if (this.videoTimeUpdateInterval) {
      clearInterval(this.videoTimeUpdateInterval);
    }

    // 移除B站播放器事件监听
    if (window.bilibiliEventsHandler) {
      window.removeEventListener('message', window.bilibiliEventsHandler);
      window.bilibiliEventsHandler = null;
    }

    // 移除时间跳转点击事件监听
    document.removeEventListener('click', this.handleTimeJumpClick);
  },

  methods: {
    // 获取多语言欢迎消息
    getWelcomeMessages() {
      const currentLang = this.$i18n.locale || 'zh'
      const teacherName = this.localizedAIRoles.assistant?.name || '老师'

      const welcomeMessages = {
        zh: [
          `欢迎来到智慧课堂！我是${teacherName}，您的AI学习助手，随时为您解答问题。`,
          '您可以向我咨询课程内容，提出学习疑问，或者讨论相关知识点。'
        ],
        en: [
          `Welcome to Smart Classroom! I'm ${teacherName}, your AI learning assistant, ready to answer your questions anytime.`,
          'You can ask me about course content, raise learning questions, or discuss related knowledge points.'
        ],
        vi: [
          `Chào mừng đến với Lớp học Thông minh! Tôi là ${teacherName}, trợ lý học tập AI của bạn, sẵn sàng trả lời câu hỏi của bạn bất cứ lúc nào.`,
          'Bạn có thể hỏi tôi về nội dung khóa học, đặt câu hỏi học tập, hoặc thảo luận về các điểm kiến thức liên quan.'
        ],
        id: [
          `Selamat datang di Kelas Pintar! Saya ${teacherName}, asisten belajar AI Anda, siap menjawab pertanyaan Anda kapan saja.`,
          'Anda dapat bertanya kepada saya tentang konten kursus, mengajukan pertanyaan belajar, atau mendiskusikan poin pengetahuan terkait.'
        ]
      }

      return welcomeMessages[currentLang] || welcomeMessages.zh
    },

    // 添加新方法初始化会话ID
    async initSessionId() {
      // 直接在前端生成会话ID
      this.videoSessionId = `session_${Date.now()}_${Math.random().toString(36).slice(2, 9)}`;
    },
    
    
    // 视频交互功能
    openNoteModal() {
      // 获取当前视频时间
      const video = this.$refs.videoPlayer;
      if (video) {
        const currentTime = Math.floor(video.currentTime);
        const minutes = Math.floor(currentTime / 60);
        const seconds = currentTime % 60;
        this.currentNote.timestamp = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      }

      // 重置当前笔记为新笔记
      this.currentNote = {
        id: null,
        title: '',
        chapter: this.courseInfo.chapter,
        timestamp: this.currentNote.timestamp || '00:00',
        content: ''
      };
      this.showNoteModal = true;
    },
    openVideoNav() {
      this.showVideoNavModal = true;
    },
    toggleLike() {
      this.isLiked = !this.isLiked;
      // 向后端更新点赞状态
    },
    toggleFavorite() {
      this.isFavorited = !this.isFavorited;
      // 向后端更新收藏状态
    },
    
    // 笔记功能
    closeNoteModal() {
      this.showNoteModal = false;
    },
    editNote(note) {
      this.currentNote = { ...note };
      this.showNoteModal = true;
    },
    // 保存笔记
    async saveNote() {
      if (!this.currentNote.title.trim()) {
        this.showNotification('请输入笔记标题', 'error');
        return;
      }

      try {
        const noteData = {
          title: this.currentNote.title,
          content: this.currentNote.content,
          course: this.course.id,
          timestamp: this.currentNote.timestamp || '00:00'  // 确保有时间戳
        };

        if (this.currentNote.id) {
          // 更新笔记
          await updateNote(this.currentNote.id, noteData);
          this.showNotification('笔记更新成功', 'success');
        } else {
          // 创建新笔记
          await createNote(noteData);
          this.showNotification('笔记创建成功', 'success');
        }

        // 重新加载笔记列表
        await this.loadNotes();
        this.closeNoteModal();
      } catch (error) {
        this.showNotification(error.message || '保存笔记失败', 'error');
      }
    },
    
    // AI视频导航
    closeVideoNav() {
      this.showVideoNavModal = false;
    },
    jumpToVideoTime(time) {
      // 从时间格式(MM:SS)转换为秒
      let seconds = 0;
      if (typeof time === 'string') {
        const parts = time.split(':').map(Number);
        if (parts.length === 2) {
          seconds = parts[0] * 60 + parts[1];
        }
      } else if (typeof time === 'number') {
        seconds = time;
      }
      
      // 跳转到指定时间
      this.seekVideo(seconds);
      this.showNotification(`已跳转到 ${time}`, 'info');
    },
    
    // AI学伴功能
    toggleAICompanion() {
      this.showAICompanion = !this.showAICompanion;
      
      // 如果是首次打开，展示欢迎消息
      if (this.showAICompanion && this.aiCompanionMessages.length === 0) {
        const welcomeMessages = this.getWelcomeMessages()

        welcomeMessages.forEach(message => {
          this.aiCompanionMessages.push({
            sender: 'ai',
            role: 'assistant',
            content: message
          })
        })
      }
    },
    async sendToAI(message) {
      if (!message.trim()) return;
      
      // 立即设置AI正在响应标志，禁止预设对话触发
      this.isAIResponding = true;
      // 立即更新最后响应时间，阻止预设对话
      this.lastAIResponseTime = Date.now();
      console.log('AI响应开始，暂停预设对话触发');
      
      // 设置请求超时
      const apiTimeoutMs = 30000; // 30秒超时
      let apiTimeout = setTimeout(() => {
        // 超时处理
        console.error('API请求超时，30秒无响应');
        this.showNotification('AI响应超时，使用后备回复', 'warning');
        
        // 使用随机后备响应
        const fallbackResponse = this.getRandomFallbackResponse();
        
        // 如果已经有一个临时消息，更新它
        const tempMessageIndex = this.aiCompanionMessages.findIndex(
          msg => msg.sender === 'ai' && msg.isStreaming === true
        );
        
        if (tempMessageIndex !== -1) {
          // 更新现有消息
          this.aiCompanionMessages[tempMessageIndex].content = fallbackResponse;
          this.aiCompanionMessages[tempMessageIndex].isStreaming = false;
        } else {
          // 添加新消息
          this.aiCompanionMessages.push({
            sender: 'ai',
            role: 'assistant',
            content: fallbackResponse,
            isStreaming: false
          });
        }
        
        // 添加到聊天历史
        this.aiChatHistory.push({
          role: 'assistant',
          content: fallbackResponse
        });
        
        // 重置状态
        this.isAIResponding = false;
        this.lastAIResponseTime = Date.now();
      }, apiTimeoutMs);
      
      // 添加用户消息
      this.aiCompanionMessages.push({
        sender: 'user',
        content: message
      });
      
      // 添加到聊天历史
      this.aiChatHistory.push({
        role: 'user',
        content: message
      });
      
      // 自动滚动到底部
      this.$nextTick(() => {
        const container = document.querySelector('.ai-messages-container');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
      
      // 显示思考状态
      this.showNotification('AI学伴正在思考...', 'info');
      
      try {
        // 创建一个临时消息对象用于流式显示
        const tempMessageIndex = this.aiCompanionMessages.push({
          sender: 'ai',
          role: 'assistant',
          content: '',
          isStreaming: true
        }) - 1;
        
        // 调用流式API，传递当前视频ID和当前视频播放时间
        const response = await chatWithAIStream(
          message, 
          this.aiChatHistory, 
          this.videoId,
          this.currentVideoTime
        );
        
        // 清除超时定时器
        clearTimeout(apiTimeout);
        
        
        // 检查响应状态
        if (!response.ok) {
          throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
        }
        
        // 读取流式数据
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let completeContent = '';
        
        // 处理流式数据
        while (true) {
          try {
            const { done, value } = await reader.read();
            if (done) break;
            
            // 解析接收到的数据
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n\n');
            
            // 处理每一行数据
            for (const line of lines) {
              if (line.startsWith('data:')) {
                try {
                  const data = JSON.parse(line.slice(5).trim());
                  
                  // 处理内容更新
                  if (data.content) {
                    // 更新临时消息的内容
                    completeContent += data.content;
                    this.aiCompanionMessages[tempMessageIndex].content = completeContent;
                    
                    // 在每次内容更新后滚动到底部
                    this.$nextTick(() => {
                      const container = document.querySelector('.ai-messages-container');
                      if (container) {
                        container.scrollTop = container.scrollHeight;
                      }
                    });
                  }
                  
                  // 处理错误
                  if (data.error) {
                    this.showNotification('AI回复出错，使用后备回复', 'warning');
                    
                    // 终止流式读取，转为使用后备响应
                    throw new Error('AI回复出错，使用后备响应: ' + data.error);
                  }
                } catch (err) {
                  // 记录JSON解析错误
                  console.error('数据解析错误:', err, 'raw line:', line);
                }
              }
            }
          } catch (streamError) {
            // 捕获流式读取错误
            console.error('流式数据读取错误:', streamError);
            throw streamError;
          }
        }
        
        // 移除流式标记
        this.aiCompanionMessages[tempMessageIndex].isStreaming = false;
        
        // 添加到聊天历史
        this.aiChatHistory.push({
          role: 'assistant',
          content: completeContent
        });
        
        // 标记AI响应完成，并重新开始冷却期计时
        this.isAIResponding = false;
        this.lastAIResponseTime = Date.now(); // 重新设置时间戳，开始冷却期
        console.log('AI响应完成，设置5秒冷却期');
        
        // 流式响应结束后再次滚动到底部
        this.$nextTick(() => {
          const container = document.querySelector('.ai-messages-container');
          if (container) {
            container.scrollTop = container.scrollHeight;
          }
        });
        
        // 控制历史长度，防止过长
        if (this.aiChatHistory.length > 20) {
          // 保留前2条和后18条，删除中间内容
          this.aiChatHistory = [
            ...this.aiChatHistory.slice(0, 2),
            ...this.aiChatHistory.slice(-18)
          ];
        }
      } catch (error) {
        console.error('AI对话异常:', error);
        this.showNotification('AI对话异常，请稍后重试', 'error');
        
        // 清除超时定时器
        clearTimeout(apiTimeout);
        
        // 使用随机后备响应
        const fallbackResponse = this.getRandomFallbackResponse();
        
        // 如果已经有一个临时消息，更新它
        const tempMessageIndex = this.aiCompanionMessages.findIndex(
          msg => msg.sender === 'ai' && msg.isStreaming === true
        );
        
        if (tempMessageIndex !== -1) {
          // 更新现有消息
          this.aiCompanionMessages[tempMessageIndex].content = fallbackResponse;
          this.aiCompanionMessages[tempMessageIndex].isStreaming = false;
        } else {
          // 添加新消息
          this.aiCompanionMessages.push({
            sender: 'ai',
            role: 'assistant',
            content: fallbackResponse,
            isStreaming: false
          });
        }
        
        // 添加到聊天历史
        this.aiChatHistory.push({
          role: 'assistant',
          content: fallbackResponse
        });
        
        // 确保即使出错也重置AI响应状态
        this.isAIResponding = false;
        this.lastAIResponseTime = Date.now(); // 设置冷却期开始时间
      }
    },
    
    // 模拟多角色响应 - 保留现有实现，但不再使用
    simulateMultiRoleResponse(userMessage, activeRoles) {
      // ... 保留原始方法，但在使用新的AI接口后不再调用 ...
    },
    
    // 添加AI响应
    addAIResponse(role, content) {
      this.aiCompanionMessages.push({
        sender: 'ai',
        role: role,
        content: content
      });
      
      // 自动滚动到底部
      this.$nextTick(() => {
        const container = document.querySelector('.ai-messages-container');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },
    
    // 根据角色生成回复内容
    generateRoleResponse(role, userMessage) {
      // 提取用户消息中的关键词
      const keywords = this.extractKeywords(userMessage);
      
      // 基于角色和用户消息的关键词生成回复
      const pythonContent = this.generatePythonContent(role, keywords);
      
      // 根据角色特质定制回复风格
      switch (role) {
        case 'assistant':
          return `你好同学，关于"${this.truncateMessage(userMessage, 30)}"的问题，让我给你详细解答：\n\n${pythonContent}`;
        case 'thinker':
          return `这个问题值得深入思考。\n\n${pythonContent}\n\n从更深层次来看，我们还可以思考这个概念在不同编程范式中的应用...`;
        case 'curious':
          return `我对这个也很好奇！${pythonContent}\n\n不过，你有没有想过：\n- 这在实际项目中如何运用？\n- 有没有更高效的方法？\n- Python 3.10的新特性能否优化这个过程？`;
        case 'topStudent':
          return `📝 **学习要点总结**\n\n${pythonContent}\n\n**关键概念：**\n1. 语法正确性\n2. 代码效率\n3. 最佳实践`;
        case 'funnyKid':
          return `哈！这个问题有点意思。${pythonContent}\n\n不过我要挑战你：试试用Python一行代码解决这个问题？不行的话，我也不会告诉你答案哦😉`;
        default:
          return `关于"${userMessage}"，这是一个很好的问题。\n\n${pythonContent}`;
      }
    },
    
    // 生成角色之间的互动回复
    generateInteractionResponse(mainRole, respondingRole, userMessage) {
      const mainRoleName = this.localizedAIRoles[mainRole]?.name || this.aiRoles[mainRole]?.name;
      const respondingRoleName = this.localizedAIRoles[respondingRole]?.name || this.aiRoles[respondingRole]?.name;
      
      const interactions = [
        `${respondingRoleName}提出了一个很好的角度。我想补充的是，在实际开发中，我们还需要考虑代码的可维护性和团队协作。`,
        `我同意${respondingRoleName}的观点，但还想强调一点：Python的设计哲学是"优雅胜于丑陋，明确胜于隐晦"，所以代码的可读性也很重要。`,
        `${respondingRoleName}说得对！另外，对于初学者来说，我建议先掌握基础概念，再探索更复杂的用法。`,
        `听完${respondingRoleName}的解释，我想提醒大家，在Python中，"显式优于隐式"是一个重要原则，所以尽量写清晰的代码。`
      ];
      
      return interactions[Math.floor(Math.random() * interactions.length)];
    },
    
    // 提取用户消息中的关键词
    extractKeywords(message) {
      const pythonKeywords = ['函数', '模块', '类', '对象', '变量', '循环', '条件', '异常', '列表', '字典', '元组', '集合', 
                             '装饰器', '迭代器', '生成器', '参数', '返回值', 'self', 'import', 'def', 'class', 'for', 'while', 
                             'if', 'else', 'try', 'except', 'finally', 'with', 'as', 'lambda', 'return'];
      
      // 将消息转为小写并分词
      const words = message.toLowerCase().split(/\s+/);
      
      // 找出匹配的关键词
      return pythonKeywords.filter(keyword => 
        words.some(word => word.includes(keyword.toLowerCase())) || 
        message.toLowerCase().includes(keyword.toLowerCase())
      );
    },
    
    // 基于关键词生成Python相关内容
    generatePythonContent(role, keywords) {
      // 如果没有检测到Python相关关键词，返回通用回复
      if (keywords.length === 0) {
        const generalResponses = [
          "Python是一种易于学习的高级编程语言，它强调代码的可读性和简洁的语法，使开发者能够用更少的代码表达想法。",
          "学习Python最好的方式是通过实践，尝试编写小项目并逐渐提高复杂度。",
          "Python广泛应用于数据分析、机器学习、网站开发、自动化脚本等领域。",
          "掌握Python的基础语法后，建议深入学习常用库如NumPy、Pandas、Flask或Django等。"
        ];
        return generalResponses[Math.floor(Math.random() * generalResponses.length)];
      }
      
      // 基于角色和关键词定制内容
      const keywordResponses = {
        '函数': [
          "Python函数使用`def`关键字定义，可以接收参数并返回值。函数是Python中重用代码的主要方式。",
          "函数可以有默认参数、可变参数和关键字参数，增加了函数调用的灵活性。",
          "在Python中，函数是一等公民，意味着它们可以作为参数传递、作为返回值，甚至赋值给变量。"
        ],
        '模块': [
          "Python模块是包含函数、变量和类的文件，通过`import`语句引入。它们帮助组织和重用代码。",
          "标准库中有很多有用的模块，如`os`、`sys`、`datetime`等，可以简化常见任务。",
          "你可以创建自己的模块，只需编写Python文件并在其他文件中导入它。"
        ],
        '类': [
          "Python中的类使用`class`关键字定义，它们是创建对象的蓝图。",
          "类支持继承、多态和封装等面向对象编程的核心概念。",
          "通过`__init__`方法，我们可以初始化新创建的对象的属性。"
        ],
        '装饰器': [
          "装饰器是Python中的一种强大工具，允许修改函数或方法的行为而不改变其代码。",
          "常见用途包括日志记录、性能测量、访问控制和缓存等。",
          "装饰器实际上是接收函数并返回新函数的高阶函数。"
        ]
      };
      
      // 检查关键词并随机选择相应回复
      for (const keyword of keywords) {
        if (keywordResponses[keyword]) {
          const responses = keywordResponses[keyword];
          return responses[Math.floor(Math.random() * responses.length)];
        }
      }
      
      // 如果没有匹配的特定关键词回复，使用更通用的回复
      const pythonResponses = [
        "Python的设计哲学强调代码的可读性和简洁性，使它成为一个优秀的编程入门语言。",
        "Python的优势在于丰富的库和活跃的社区支持，使得各种应用开发变得更加高效。",
        "学习Python时，理解数据结构、控制流和函数概念是建立良好基础的关键。",
        "Python解释器使用缩进来定义代码块，这促使开发者编写整洁一致的代码。"
      ];
      
      return pythonResponses[Math.floor(Math.random() * pythonResponses.length)];
    },
    
    // 生成示例代码
    generateSampleCode(keywords) {
      // 基于关键词生成相关代码示例
      if (keywords.includes('函数') || keywords.includes('def')) {
        return `def greet(name, greeting="Hello"):
    """简单的问候函数"""
    return f"{greeting}, {name}!"

# 使用示例
print(greet("Alice"))  # 输出: Hello, Alice!
print(greet("Bob", "Hi"))  # 输出: Hi, Bob!`;
      }
      
      if (keywords.includes('类') || keywords.includes('class')) {
        return `class Person:
    """人物类示例"""
    def __init__(self, name, age):
        self.name = name
        self.age = age
        
    def introduce(self):
        return f"我叫{self.name}，今年{self.age}岁。"
        
# 使用示例
alice = Person("Alice", 25)
print(alice.introduce())  # 输出: 我叫Alice，今年25岁。`;
      }
      
      if (keywords.includes('装饰器') || keywords.includes('decorator')) {
        return `def timer(func):
    """计时装饰器"""
    import time
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__}执行时间: {end - start:.4f}秒")
        return result
    return wrapper
    
@timer
def slow_function():
    import time
    time.sleep(1)
    return "完成"
    
print(slow_function())  # 输出执行时间和"完成"`;
      }
      
      // 默认代码示例
      return `# Python基本语法示例
name = "World"
print(f"Hello, {name}!")  # 使用f-strings格式化

# 列表示例
numbers = [1, 2, 3, 4, 5]
squared = [x**2 for x in numbers]  # 列表推导式
print(squared)  # 输出: [1, 4, 9, 16, 25]`;
    },
    
    // 截断消息到指定长度，避免太长
    truncateMessage(message, maxLength = 30) {
      if (message.length <= maxLength) return message;
      return message.substring(0, maxLength) + '...';
    },
  
    // 通知系统
    showNotification(message, type = 'info') {
      // 清除之前的定时器
      if (this.notification.timeout) {
        clearTimeout(this.notification.timeout);
      }
      
      this.notification = {
        show: true,
        type,
        message,
        timeout: null
      };
      
      // 设置通知自动消失
      this.notification.timeout = setTimeout(() => {
        this.notification.show = false;
      }, 3000);
    },
    
    // 学习伙伴选择
    confirmCompanionSelection() {
      // 获取选择的角色
      const selectedRoles = [];
      
      if (this.selectedTeacherRoles.assistant) selectedRoles.push('助教');
      
      Object.entries(this.selectedStudentRoles).forEach(([role, isActive]) => {
        if (isActive && this.localizedAIRoles[role]) {
          selectedRoles.push(this.localizedAIRoles[role].name);
        }
      });
      
      // 合成角色名字字符串
      let roleNames = selectedRoles.join('、');
      
      this.showCompanionDialog = false;
      this.showNotification(`已选择${roleNames}作为你的学习伙伴，对话将立即按照你的选择过滤`, 'success');
      
      
    },
    openCompanionDialog() {
      // 确保两个老师角色都被选中
      this.selectedTeacherRoles.assistant = true;
      this.showCompanionDialog = true;
    },
    // 获取消息头像
    getMessageAvatar(message) {
      if (message.sender === 'user') {
        return userAvatar;
      }
      
      // 依据agentId映射角色到头像
      const agentRoleMapping = {
        'teacher': 'assistant',
        'assistant': 'assistant',
        'curious': 'curious',
        'thinker': 'thinker',
        'topStudent': 'topStudent',
        'funnyKid': 'funnyKid'
      };
      
      // 根据角色返回不同头像
      const role = message.role;
      
      // 如果消息中有agentId，根据映射关系获取对应角色
      if (message.agentId && agentRoleMapping[message.agentId]) {
        const mappedRole = agentRoleMapping[message.agentId];
        if (this.aiRoles[mappedRole]) {
          return this.aiRoles[mappedRole].avatar;
        }
      }
      
      // 否则根据role获取头像
      if (this.aiRoles[role]) {
        return this.aiRoles[role].avatar;
      }
      
      // 默认头像
      return assistantAvatar;
    },
    // 获取角色名称
    getRoleName(role) {
      // 检查消息中是否有自定义agentName
      const foundMessages = this.aiCompanionMessages.filter(msg => msg.role === role && msg.agentName);
      if (foundMessages.length > 0) {
        return foundMessages[foundMessages.length - 1].agentName;
      }

      // 否则使用本地化的角色名称
      if (this.localizedAIRoles[role]) {
        return this.localizedAIRoles[role].name;
      }
      return 'AI学习助手';
    },
    // 将消息内容格式化为HTML
    formatMessage(content) {
      if (!content) return '';
      
      // 替换换行符为<br>
      let formatted = content.replace(/\n/g, '<br>');
      
      // 处理代码块 - 这里简化起见，不使用```标记，而是直接添加样式
      formatted = formatted.replace(/```([^`]+)```/g, '<pre class="code-block">$1</pre>');
      
      // 特殊处理行内代码
      formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');
      
      // 处理视频时间跳转，格式为[跳转至MM:SS]
      formatted = formatted.replace(/\[跳转至\s*(\d{1,2}:\d{2})\]/g, 
        '<a href="javascript:void(0)" class="time-jump-link" data-time="$1" style="color: #409EFF; text-decoration: underline; cursor: pointer;">跳转至 $1</a>');
      
      return formatted;
    },
    
    // 视频播放器初始化
    async initVideoInteraction() {
      try {
        // 加载所有预设对话，如果当前有有效的视频ID
        if (this.videoId) {
          await this.loadVideoDialogues();
        }
      } catch (error) {
        // 不使用后备数据，而是创建空对话列表
        console.error('初始化视频交互失败:', error);
        this.presetDialogues = [];
        this.aiDialoguesLoaded = true;
        this.showNotification('无法加载视频交互数据', 'warning');
      }
    },
    
    // 设置备选的时间跟踪
    setupFallbackTimeTracking() {
      this.videoTimeUpdateInterval = setInterval(() => {
        // 使用模拟时间
        const now = new Date();
        const seconds = now.getSeconds();
        const minutes = now.getMinutes() % 10; // 限制在10分钟内循环
        this.currentVideoTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // 检查是否应该触发对话
        this.checkForDialogueTriggers();
      }, 1000);
    },
    
    // 检查是否应该触发对话
    async checkForDialogueTriggers() {
      // 如果对话数据未加载，则尝试加载
      if (!this.aiDialoguesLoaded && this.presetDialogues.length === 0) {
        // 加载预设对话
        await this.loadPresetDialogues();
        return;
      }
      
      // 如果AI正在响应用户消息，不触发对话
      if (this.isAIResponding) {
        return;
      }
      
      // 检查是否在冷却期内（AI回答后的5秒内）
      const currentTime = Date.now();
      const timeSinceLastResponse = currentTime - this.lastAIResponseTime;
      if (this.lastAIResponseTime > 0 && timeSinceLastResponse < this.dialogueCooldownPeriod) {
        // 如果在冷却期内，不触发对话
        return;
      }
      
      // 将当前时间转换为秒
      const currentTimeInSeconds = this.timeToSeconds(this.currentVideoTime);
      
      // 查找应该在当前时间触发的对话
      this.presetDialogues.forEach(dialogue => {
        if (!dialogue.triggerTime) return;
        
        const triggerTimeInSeconds = this.timeToSeconds(dialogue.triggerTime);
        
        // 如果当前时间已经超过对话触发时间的窗口上限，且未被触发，则将其标记为已跳过
        if (currentTimeInSeconds > triggerTimeInSeconds + 5 && 
            !this.triggeredDialogueIds.has(dialogue.triggerTime) && 
            !this.skippedDialogueIds.has(dialogue.triggerTime)) {
          this.skippedDialogueIds.add(dialogue.triggerTime);
          console.log(`对话 ${dialogue.triggerTime} 被跳过`);
          return;
        }
        
        // 修改为前后10秒的时间窗口：只要当前时间在触发时间前后10秒范围内，且未被触发过，就触发对话
        // 并添加条件：该对话不能在跳过列表中
        if (currentTimeInSeconds >= triggerTimeInSeconds - 5 && 
            currentTimeInSeconds <= triggerTimeInSeconds + 5 && 
            !this.triggeredDialogueIds.has(dialogue.triggerTime) &&
            !this.skippedDialogueIds.has(dialogue.triggerTime)) {
          
          // 将该对话标记为已触发
          this.triggeredDialogueIds.add(dialogue.triggerTime);
          
          // 显示AI对话
          this.showPresetDialogue(dialogue);
        }
      });
    },
    
    // 显示预设对话
    showPresetDialogue(dialogue) {
      // 确保AI助手面板是打开的
      if (!this.showAICompanion) {
        this.toggleAICompanion();
      }
      
      // 如果对话为空，不显示
      if (!dialogue || !dialogue.messages || dialogue.messages.length === 0) {
        return;
      }
      
      // 标记该对话为已触发
      if (dialogue.triggerTime) {
        this.triggeredDialogueIds.add(dialogue.triggerTime);
      }
      
      // 添加对话消息
      let delay = 0;
      dialogue.messages.forEach((message, index) => {
        delay += 800 + (message.content.length / 20 * 100); // 根据消息长度调整延迟
        setTimeout(() => {
          this.addPresetAgentMessage(message);
        }, delay);
      });
      
      // 显示通知
      ElMessage({
        message: '有新的AI助手消息',
        type: 'info',
        duration: 2000
      });
    },
    
    // 添加预设角色消息
    addPresetAgentMessage(message) {
      if (!message || !message.content) {
        return;
      }
      
      // 映射agentId到现有角色
      const roleMapping = {
        'teacher': 'assistant',
        'assistant': 'assistant',
        'curious': 'curious',
        'thinker': 'thinker',
        'topStudent': 'topStudent',
        'funnyKid': 'funnyKid'
      };
      
      // 获取对应的角色
      const role = message.agentId && roleMapping[message.agentId] 
        ? roleMapping[message.agentId] 
        : 'teacher';
      
      // 检查角色是否被启用
      // 助教（assistant）角色始终显示，其他角色根据用户设置决定是否显示
      if (role !== 'assistant' && role !== 'teacher') {
        // 检查该角色是否被用户关闭
        if (!this.selectedStudentRoles[role]) {
          console.log(`角色 ${role} 已被用户关闭，不显示其消息`);
          return; // 如果角色被关闭，不显示该消息
        }
      }
      
      // 从本地化aiRoles中获取角色名称，如果不存在则使用消息中的agentName
      const roleName = this.localizedAIRoles[role] ? this.localizedAIRoles[role].name : message.agentName;
      
      // 添加消息到对话列表(用于UI显示)
      this.aiCompanionMessages.push({
        sender: 'ai',
        role: role,
        content: message.content,
        agentId: message.agentId,
        agentName: roleName // 使用从aiRoles中获取的名称
      });
      
      // 将预设对话也添加到聊天历史中，作为上下文传递给AI
      // 教师和助教角色的消息映射为assistant角色，其他角色映射为user角色
      const aiRole = (role === 'teacher' || role === 'assistant') ? 'assistant' : 'user';
      
      // 构建带角色前缀的内容，使AI能理解不同角色的发言
      const prefixedContent = roleName 
        ? `${roleName}: ${message.content}` 
        : message.content;
      
      // 添加到聊天历史
      this.aiChatHistory.push({
        role: aiRole,
        content: prefixedContent
      });
      
      // 控制历史长度，防止过长
      if (this.aiChatHistory.length > 30) {
        // 保留前2条和后28条，删除中间内容
        this.aiChatHistory = [
          ...this.aiChatHistory.slice(0, 2),
          ...this.aiChatHistory.slice(-28)
        ];
      }
      
      // 自动滚动到底部
      this.$nextTick(() => {
        const container = document.querySelector('.ai-messages-container');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },
    
    // 时间字符串转秒数
    timeToSeconds(timeStr) {
      try {
        const [minutes, seconds] = timeStr.split(':').map(Number);
        return minutes * 60 + seconds;
      } catch (e) {
        return 0;
      }
    },
    // 获取特定时间点的对话
    async fetchDialoguesAtCurrentTime() {
      if (!this.videoId || !this.currentVideoTime) return;
      
      // 确保sessionId已准备好
      if (!this.videoSessionId || typeof this.videoSessionId === 'object') {
        // 直接在前端生成会话ID
        this.videoSessionId = `session_${Date.now()}_${Math.random().toString(36).slice(2, 9)}`;
      }
      
      console.log(`尝试获取时间点[${this.currentVideoTime}]的对话，视频ID：${this.videoId}，会话ID：${this.videoSessionId}`);
      
      getDialoguesAtTimestamp(this.videoId, this.currentVideoTime, this.videoSessionId)
        .then(response => {
          // 调试日志
          console.log(`时间点[${this.currentVideoTime}]响应:`, response);
          
          if (response && response.status === 200) {
            const responseData = response.data || {};
            
            if (responseData.status === 'success') {
              let dialogues = [];
              
              // 处理不同的数据结构
              if (responseData.data && Array.isArray(responseData.data.dialogues)) {
                dialogues = responseData.data.dialogues;
              } else if (responseData.data && typeof responseData.data === 'object') {
                if (Array.isArray(responseData.data)) {
                  dialogues = responseData.data;
                } else if (responseData.data.dialogues) {
                  dialogues = responseData.data.dialogues;
                }
              }
              
              console.log(`找到${dialogues.length}个对话`);
              
              // 过滤出未触发过的对话
              const newDialogues = dialogues.filter(dialogue => 
                !this.triggeredDialogueIds.has(dialogue.triggerTime)
              );
              
              // 显示新对话
              newDialogues.forEach(dialogue => {
                this.triggeredDialogueIds.add(dialogue.triggerTime);
                this.showPresetDialogue(dialogue);
              });
            } else {
              console.error('获取时间点对话API响应状态错误:', responseData.message || '未知错误');
            }
          } else {
            console.error('获取时间点对话API失败:', response?.statusText || '无响应数据', response);
          }
        })
        .catch(error => {
          console.error('获取时间点对话异常:', error);
          if (error.response) {
            console.error('错误响应数据:', error.response.data);
            console.error('错误状态码:', error.response.status);
          } else if (error.request) {
            console.error('未收到响应，请检查网络或API服务是否可用');
          } else {
            console.error('请求配置错误:', error.message);
          }
        });
    },
    // 加载预设对话
    async loadPresetDialogues() {
      await this.loadVideoDialogues();
    },
    // 添加后备方案，使用硬编码的预设对话数据
    useFallbackDialogues() {
      console.log('使用后备预设对话数据');
      // 使用硬编码的对话数据作为后备
      this.presetDialogues = [
        {
          "triggerTime": "00:10",
          "messages": [
            {
              "agentId": "assistant",
              "agentName": this.localizedAIRoles.assistant.name,
              "content": "同学们好！我们今天要讲的是数据分析指标体系，这是企业经营分析的基础框架。"
            }
          ]
        },
        {
          "triggerTime": "00:30",
          "messages": [
            {
              "agentId": "curious",
              "agentName": "张颖",
              "content": "老师，什么是数据分析指标体系啊？为什么我们需要建立这样的体系？"
            },
            {
              "agentId": "assistant",
              "agentName": this.localizedAIRoles.assistant.name,
              "content": "好问题！数据分析指标体系是对企业运营过程中各个环节数据的系统化整理，帮助我们全面了解业务状况，做出更准确的决策。"
            }
          ]
        },
        {
          "triggerTime": "01:20",
          "messages": [
            {
              "agentId": "topStudent",
              "agentName": "赵阳",
              "content": "⭐重点⭐ 三大数据指标体系：\n1️⃣ 市场数据（行业数据、竞争数据）\n2️⃣ 运营数据（客户、推广、服务、供应链）\n3️⃣ 产品数据（行业表现、企业表现）"
            }
          ]
        }
      ];
      this.aiDialoguesLoaded = true;
      this.usingBackendAPI = false; // 标记为使用本地数据，不使用后端API
    },
    // 视频重置/初始化
    async resetVideoSession() {
      console.log(`正在重置视频会话状态，当前视频ID: ${this.videoId}`);
      
      // 直接在前端生成会话ID
      this.videoSessionId = `session_${Date.now()}_${Math.random().toString(36).slice(2, 9)}`;
      
      // 重置已触发对话记录
      this.triggeredDialogueIds = new Set();
      // 重置已跳过对话记录
      this.skippedDialogueIds = new Set();
      // 重置上次检查时间和最后AI响应时间
      this.lastCheckedTime = -1;
      this.lastAIResponseTime = 0;
      this.isAIResponding = false; // 重置AI响应状态
      
      console.log('视频会话已重置, 新会话ID:', this.videoSessionId);
      
      // 如果之前显示了AI助手，关闭它
      if (this.showAICompanion) {
        this.toggleAICompanion();
      }
      
      // 清空AI对话
      this.aiCompanionMessages = [];
      
      // 重新加载预设对话
      this.loadPresetDialogues();
    },
    
    // 准备测试对话数据
    prepareTestDialogues() {
      // 首先添加已加载的预设对话
      this.testDialogues = [...this.presetDialogues].map((dialogue, index) => ({
        ...dialogue,
        title: `时间点对话 ${index + 1}`,
        id: dialogue.id || `time_dialogue_${index}`
      }));
      
      // 添加进度百分比对话
      const progressDialogues = [
        { 
          id: 'progress_25', 
          title: '进度 25%', 
          triggerTime: '25%',
          messages: [
            { agentId: 'teacher', agentName: '老师', content: '你已经完成了四分之一的学习，做得很好！继续保持。' }
          ]
        },
        { 
          id: 'progress_50', 
          title: '进度 50%', 
          triggerTime: '50%', 
          messages: [
            { agentId: 'assistant', agentName: '助教', content: '已经到达课程中点了！有什么问题想问吗？' }
          ]
        },
        { 
          id: 'progress_75', 
          title: '进度 75%', 
          triggerTime: '75%', 
          messages: [
            { agentId: 'thinker', agentName: '思考者', content: '你已经学习了大部分内容，不妨思考一下这些知识如何应用到实际项目中？' }
          ]
        },
        { 
          id: 'progress_95', 
          title: '进度 95%', 
          triggerTime: '95%', 
          messages: [
            { agentId: 'teacher', agentName: '老师', content: '课程即将结束，让我们一起回顾一下今天学习的重点内容。' }
          ]
        }
      ];
      
      // 添加自定义示例对话
      const exampleDialogues = [
        {
          id: 'example_1',
          title: '知识点讲解',
          messages: [
            { agentId: 'teacher', agentName: '王老师', content: '接下来我们将学习数据分析中的重要概念：相关性分析。' },
            { agentId: 'curious', agentName: '好奇学生', content: '老师，什么是相关性分析？它有什么用？' },
            { agentId: 'teacher', agentName: '王老师', content: '相关性分析是研究两个或多个变量之间关联程度的统计方法。通过它，我们可以了解数据间的关系强度和方向。' }
          ]
        },
        {
          id: 'example_2',
          title: '学习经验分享',
          messages: [
            { agentId: 'notetaker', agentName: '学霸同学', content: '学习数据分析，我的经验是：先掌握基本统计概念，然后学习数据可视化，最后才是高级建模技术。' },
            { agentId: 'teacher', agentName: '王老师', content: '非常好的建议！打好基础非常重要，这样才能理解更复杂的分析方法。' }
          ]
        }
      ];
      
      // 合并所有对话
      this.testDialogues = [
        ...this.testDialogues, 
        ...progressDialogues,
        ...exampleDialogues
      ];
    },
    
    // 触发测试对话
    triggerTestDialogue(dialogue) {
      if (!dialogue || !dialogue.messages) {
        return;
      }
      
      // 显示对话
      this.showPresetDialogue(dialogue);
      
      // 关闭测试面板
      this.showTestDialoguePanel = false;
      
      // 显示提示
      this.showNotification('已触发测试对话', 'success');
    },
    
    // 获取对话预览
    getDialoguePreview(dialogue) {
      if (!dialogue || !dialogue.messages || dialogue.messages.length === 0) {
        return '空对话';
      }
      
      // 获取第一条消息的内容，并截断
      const firstMessage = dialogue.messages[0].content || '';
      return firstMessage.length > 30 ? firstMessage.substring(0, 30) + '...' : firstMessage;
    },
    
    // HTML5视频播放器事件处理
    async onVideoTimeUpdate(event) {
      const video = this.$refs.videoPlayer;
      if (!video) return;
      
      // 获取当前时间（秒）
      const currentSeconds = Math.floor(video.currentTime);
      
      // 检测视频是否大幅跳转（快进或后退5秒以上）
      const timeJump = Math.abs(currentSeconds - this.lastCheckedTime);
      if (this.lastCheckedTime > 0 && timeJump > 5) {
        console.log(`检测到视频时间跳转: ${this.lastCheckedTime} -> ${currentSeconds}, 差值: ${timeJump}秒`);
        
        // 如果是后退（当前时间比上次检查时间早）
        if (currentSeconds < this.lastCheckedTime - 5) {
          // 不再重置已触发对话记录，确保已触发的对话不会再次显示
          console.log('视频后退，保持已触发对话记录，避免重复显示对话');
        } 
        // 如果是快进，则将中间的对话标记为已跳过
        else if (currentSeconds > this.lastCheckedTime + 5) {
          // 将快进跳过的时间段内的对话标记为已跳过
          this.markSkippedDialogues(this.lastCheckedTime, currentSeconds);
          console.log(`视频快进，已将 ${this.lastCheckedTime} - ${currentSeconds} 秒之间的对话标记为已跳过`);
        }
        
        // 无论是快进还是后退，都立即检查当前时间点是否有需要触发的对话
        this.checkForDialogueTriggers();
      }
      
      // 更新最后检查时间
      this.lastCheckedTime = currentSeconds;
      
      // 转换为MM:SS格式
      const minutes = Math.floor(currentSeconds / 60);
      const seconds = currentSeconds % 60;
      this.currentVideoTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      
      // 正常的对话触发检查
      this.checkForDialogueTriggers();
    },
    
    // 新增方法：标记被跳过的对话
    markSkippedDialogues(startTimeSeconds, endTimeSeconds) {
      if (!this.presetDialogues || this.presetDialogues.length === 0) {
        return;
      }
      
      this.presetDialogues.forEach(dialogue => {
        if (!dialogue.triggerTime) return;
        
        const triggerTimeInSeconds = this.timeToSeconds(dialogue.triggerTime);
        
        // 如果对话的触发时间在起始时间和结束时间之间，且未被触发过，则标记为已跳过
        if (triggerTimeInSeconds > startTimeSeconds && 
            triggerTimeInSeconds < endTimeSeconds && 
            !this.triggeredDialogueIds.has(dialogue.triggerTime) &&
            !this.skippedDialogueIds.has(dialogue.triggerTime)) {
          
          this.skippedDialogueIds.add(dialogue.triggerTime);
          console.log(`对话 ${dialogue.triggerTime} 因视频快进被标记为已跳过`);
        }
      });
    },
    
    onVideoLoaded(event) {
      const video = this.$refs.videoPlayer;
      if (!video) return;
      
      // 获取视频总长度
      this.videoDuration = Math.floor(video.duration);
      
      // 初始化视频播放器引用
      this.videoPlayer = video;
    },
    
    onVideoPlay(event) {
      this.isVideoPlaying = true;
    },
    
    onVideoPause(event) {
      this.isVideoPlaying = false;
    },
    
    onVideoEnded(event) {
      this.isVideoPlaying = false;
      
      // 可以在视频结束时显示特定对话
      const endDialogue = {
        id: 'video_end',
        triggerTime: 'end',
        messages: [
          { 
            agentId: 'teacher', 
            agentName: '老师', 
            content: '恭喜你完成了本节课程！希望今天的内容对你有所帮助。如果有任何问题，随时提问。' 
          }
        ]
      };
      
      this.showPresetDialogue(endDialogue);
    },
    
    // 视频播放控制
    playVideo() {
      if (this.videoPlayer) {
        this.videoPlayer.play();
      }
    },
    
    pauseVideo() {
      if (this.videoPlayer) {
        this.videoPlayer.pause();
      }
    },
    
    seekVideo(timeInSeconds) {
      if (this.videoPlayer) {
        this.videoPlayer.currentTime = timeInSeconds;
      }
    },
    
    // 更新视频跳转方法
    jumpToVideoTime(time) {
      // 从时间格式(MM:SS)转换为秒
      let seconds = 0;
      if (typeof time === 'string') {
        const parts = time.split(':').map(Number);
        if (parts.length === 2) {
          seconds = parts[0] * 60 + parts[1];
        }
      } else if (typeof time === 'number') {
        seconds = time;
      }
      
      // 跳转到指定时间
      this.seekVideo(seconds);
      this.showNotification(`已跳转到 ${time}`, 'info');
    },
    
    // 选择预设视频
    selectPresetVideo(url) {
      if (!url) return;
      
      // 更新视频源
      this.localVideoSource = url;
      
      // 重置会话
      this.resetVideoSession();
      
      // 显示通知
      this.showNotification('已切换到预设视频', 'success');
    },
    
    // 加载视频导航数据
    async loadVideoNavigation() {
      if (!this.videoId) {
        return;
      }
      
      try {
        const response = await getVideoNavigation(this.videoId);
        
        if (response && response.code === 200 && response.data) {
          this.navigationData = response.data;
        } else {
          // 如果响应不成功，保持初始状态
          this.navigationData = {
            key_points: [],
            summary: ''
          };
        }
      } catch (error) {
        console.error('获取视频导航数据失败:', error);
        // 发生错误时，保持初始状态
        this.navigationData = {
          key_points: [],
          summary: ''
        };
      }
    },
    
    // 时间字符串转换为秒数
    timeStringToSeconds(timeStr) {
      try {
        const [minutes, seconds] = timeStr.split(':').map(Number);
        return minutes * 60 + seconds;
      } catch (e) {
        return 0;
      }
    },
    
    // 跳转到指定时间点
    jumpToKeyPoint(timeStr) {
      const seconds = this.timeStringToSeconds(timeStr);
      
      if (this.videoPlayer) {
        this.videoPlayer.currentTime = seconds;
        this.showNotification(`已跳转到 ${timeStr}`, 'info');
      } else if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.currentTime = seconds;
        this.showNotification(`已跳转到 ${timeStr}`, 'info');
      }
    },
    
    // 加载课程数据
    async loadCourseData() {
      try {
        const route = this.$route;
        const courseId = route.params.id;
        
        if (!courseId) {
          this.showNotification('课程ID无效', 'error');
          return;
        }
        
        // 获取课程详情
        const courseData = await courseApi.getCourseDetail(courseId);
        
        // 获取课程概述
        // try {
        //   const overviewData = await courseApi.getCourseOverview(courseId);
        //   console.log('Course overview data:', overviewData);
        //   if (overviewData && typeof overviewData === 'object') {
        //     courseData.overview = overviewData;
        //     console.log('Updated course data with overview:', courseData);
        //   }
        // } catch (error) {
        //   console.error('Failed to load course overview:', error);
        //   courseData.overview = {
        //     key_points: '',
        //     important_points: '',
        //     notes: ''
        //   };
        // }
        
        this.course = courseData;
        console.log('设置后的课程数据:', this.course);
        
        // 更新课程信息
        this.courseInfo = {
          ...this.courseInfo,
          title: courseData.name,
          instructor: courseData.teacher ? courseData.teacher.name : '未知',
          totalLessons: 0, // 将在加载章节时更新
          rating: courseData.average_rating,
          ratingCount: courseData.rating_count
        };

        // 设置用户评分
        if (courseData.user_rating) {
          this.userRating = courseData.user_rating.rating;
          this.originalRating = courseData.user_rating.rating;
        }
        
        // 获取章节列表
        const chaptersResponse = await courseApi.getChaptersByCourse(courseId);
        const chaptersData = chaptersResponse.results || [];
        
        // 初始化章节展开状态
        chaptersData.forEach((_, index) => {
          this.expandedChapters[index] = false;
        });
        
        // 默认展开第一个章节
        if (chaptersData.length > 0) {
          this.expandedChapters[0] = true;
        }
        
        // 获取每个章节的课时
        let totalLessons = 0;
        const chaptersWithLessons = await Promise.all(
          chaptersData.map(async (chapter) => {
            const lessonsResponse = await lessonApi.getLessons(chapter.id);
            const lessons = lessonsResponse.results || [];
            totalLessons += lessons.length;
            
            return {
              ...chapter,
              lessons: lessons
            };
          })
        );
        
        this.chapters = chaptersWithLessons;
        this.courseInfo.totalLessons = totalLessons;
        
        // 自动选择第一个章节和课时
        this.findLastAccessedContent();
      } catch (error) {
        console.error('加载课程数据失败:', error);
        this.showNotification('加载课程数据失败', 'error');
      }
    },
    
    findLastAccessedContent() {
      // 始终从第一章第一节开始，无论之前的进度如何
      if (this.chapters.length > 0) {
        // 选择第一个章节
        this.currentChapter = this.chapters[0];
        this.courseInfo.chapter = this.currentChapter.title;
        
        // 选择第一个课时
        if (this.currentChapter.lessons && this.currentChapter.lessons.length > 0) {
          this.currentLesson = this.currentChapter.lessons[0];
          this.courseInfo.currentSection = this.currentLesson.title;
          
          // 加载课时内容
          this.loadLessonContent(this.currentLesson);
        }
        
        // 展开第一个章节
        this.expandedChapters[0] = true;
      }
    },
    
    loadLessonContent(lesson) {
      if (!lesson) {
        return;
      }
      
      // 更新当前视频ID为课时ID
      if (lesson.id) {
        // 保存新的视频ID
        this.videoId = lesson.id.toString();
        console.log(`切换视频至: ID=${this.videoId}, 标题=${lesson.title || '未知'}`);
        
        // 重置视频会话状态，防止重复触发对话
        this.resetVideoSession();
        
        // 加载视频导航数据和教师信息
        this.loadVideoNavigation();
        this.loadTeacherInfo();
      }
      
      if (lesson && lesson.video_url) {
        // 直接使用API返回的video_url
        this.localVideoSource = lesson.video_url;
        
        // 强制重新加载视频
        setTimeout(() => {
          const video = this.$refs.videoPlayer;
          if (video) {
            video.load();
            video.src = lesson.video_url;
          }
        }, 100);
      } else {
        // 设置默认视频
        this.localVideoSource = "https://clkj-ai-education.oss-cn-shenzhen.aliyuncs.com/video/202504/92a3a0d7-410c-4740-997d-3d4f54376cfd.mp4";
        setTimeout(() => {
          const video = this.$refs.videoPlayer;
          if (video) {
            video.load();
            video.src = this.localVideoSource;
          }
        }, 100);
      }
    },

    onVideoError(event) {
      const video = this.$refs.videoPlayer;
      
      if (video && video.error) {
        let errorMessage = '';
        switch (video.error.code) {
          case 1: errorMessage = '加载中断'; break;
          case 2: errorMessage = '网络错误'; break;
          case 3: errorMessage = '解码错误'; break;
          case 4: errorMessage = '视频格式不支持'; break;
          default: errorMessage = '未知错误';
        }
        this.showNotification(`视频加载失败: ${errorMessage}`, 'error');
        
        // 强制重新加载视频
        setTimeout(() => {
          if (video) {
            video.load();
          }
        }, 100);
      }
    },
    
    async loadVideoDialogues() {
      if (!this.videoId) {
        return;
      }
      
      try {
        const response = await getVideoDialogues(this.videoId);
        
        if (response && response.code === 200) {
          this.presetDialogues = response.data || [];
          this.aiDialoguesLoaded = true;
          
          // 重置已触发对话记录
          this.triggeredDialogueIds = new Set();
        }
      } catch (error) {
        // 不使用后备数据，而是创建空对话列表并显示错误通知
        console.error('获取视频对话数据失败:', error);
        this.presetDialogues = [];
        this.aiDialoguesLoaded = true;
        this.showNotification('无法加载对话数据', 'warning');
      }
    },
    
    // 切换章节展开/折叠状态
    toggleChapter(chapterIndex) {
      // Vue 3中直接修改响应式对象即可
      this.expandedChapters[chapterIndex] = !this.expandedChapters[chapterIndex];
    },
    
    // 选择课时
    selectLesson(chapter, lesson) {
      this.currentChapter = chapter;
      this.currentLesson = lesson;
      this.courseInfo.chapter = chapter.title;
      this.courseInfo.currentSection = lesson.title;
      
      // 加载课时内容
      this.loadLessonContent(lesson);
      
      // 显示通知
      this.showNotification(`正在播放: ${lesson.title}`, 'info');
    },
    
    // 加载教师信息
    async loadTeacherInfo() {
      if (!this.videoId) {
        return;
      }
      
      try {
        const response = await getVideoTeacherInfo(this.videoId);
        
        if (response && response.code === 200 && response.data && response.data.teacher) {
          this.teacherInfo = response.data.teacher;
        }
      } catch (error) {
        console.error('获取教师信息失败:', error);
      }
    },
    // 处理时间跳转链接点击
    handleTimeJumpClick(event) {
      // 检查是否点击了时间跳转链接
      let target = event.target;
      while (target && target !== document) {
        if (target.classList && target.classList.contains('time-jump-link')) {
          const timeValue = target.getAttribute('data-time');
          if (timeValue) {
            event.preventDefault();
            this.jumpToVideoTime(timeValue);
          }
          break;
        }
        target = target.parentNode;
      }
    },
    // 获取随机后备响应
    getRandomFallbackResponse() {
      if (!this.fallbackResponses || this.fallbackResponses.length === 0) {
        return "抱歉，我暂时无法回答这个问题。请稍后再试。";
      }
      
      const randomIndex = Math.floor(Math.random() * this.fallbackResponses.length);
      return this.fallbackResponses[randomIndex];
    },
    // 加载笔记列表
    async loadNotes() {
      try {
        if (!this.course?.id) {
          this.showNotification('无法获取课程信息', 'error');
          return;
        }

        const response = await getNotes(this.course.id);
        this.notes = response.results;
      } catch (error) {
        this.showNotification('加载笔记失败', 'error');
      }
    },

    // 删除笔记
    async confirmDeleteNote() {
      try {
        await ElMessageBox.confirm('确定要删除这条笔记吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        await deleteNote(this.currentNote.id);
        this.showNotification('笔记删除成功', 'success');
        await this.loadNotes();
        this.closeNoteModal();
      } catch (error) {
        if (error !== 'cancel') {
          this.showNotification('删除笔记失败', 'error');
        }
      }
    },

    // 编辑笔记
    editNote(note) {
      this.currentNote = { ...note };
      this.showNoteModal = true;
    },

    // 创建新笔记
    createNewNote() {
      this.currentNote = {
        id: null,
        title: '',
        content: '',
        timestamp: this.currentVideoTime || '00:00'
      };
      this.showNoteModal = true;
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    setCurrentTime() {
      const minutes = Math.floor(this.currentVideoTime / 60);
      const seconds = Math.floor(this.currentVideoTime % 60);
      this.currentNote.timestamp = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },
    
    closeNoteModal() {
      this.showNoteModal = false;
      this.currentNote = {
        id: null,
        title: '',
        content: '',
        timestamp: ''
      };
    },
    
    async handleRating(value) {
      this.userRating = value;
    },

    async submitRating() {
      try {
        const response = await courseApi.rateCourse(this.course.id, {
          rating: this.userRating
        });

        // 更新本地数据
        this.originalRating = this.userRating;
        this.courseInfo.rating = this.course.average_rating;
        this.courseInfo.ratingCount = this.course.rating_count;

        this.$message.success('评分成功');
      } catch (error) {
        console.error('评分失败:', error);
        this.$message.error('评分失败，请重试');
      }
    },
    openRatingDialog() {
      this.showRatingDialog = true;
    },
    // 加载评论列表
    async loadComments() {
      try {
        const response = await commentApi.getComments(this.course.id)
        this.comments = response.results || []
        this.comments.forEach(comment => {
          comment.user = {
            id: comment.user.id,
            avatar: userAvatar,
          }
          comment.replies = comment.replies.map(reply => ({
            id: reply.id,
            content: reply.content,
            created_at: reply.created_at,
            user: {
              id: reply.user.id,
              avatar: teacherAvatar
            }
          }))
        })
        console.log('comments', this.comments)
      } catch (error) {
        console.error('加载评论失败:', error)
        this.$message.error('加载评论失败')
      }
    },

    // 添加评论
    async addComment(content) {
      if (!content.trim()) return

      try {
        const data = {
          content: content.trim(),
          course: this.course.id
        }
        
        const response = await commentApi.createComment(data)
        
        // 将新评论添加到列表开头
        this.comments.unshift(response)
        
        // 清空输入框
        this.newComment = ''
        
        // 显示成功提示
        this.$message.success('评论发表成功')
      } catch (error) {
        console.error('发表评论失败:', error)
        this.$message.error('发表评论失败，请重试')
      }
    },

    // 回复评论
    async replyToComment(comment, content) {
      if (!content.trim()) return

      try {
        const response = await commentApi.replyToComment(comment.id, {
          content: content
        })

        // 如果评论没有replies数组，创建一个
        if (!comment.replies) {
          comment.replies = []
        }

        // 将新回复添加到回复列表
        comment.replies.push(response)

        comment.replies = comment.replies.map(reply => ({
            id: reply.id,
            content: reply.content,
            created_at: reply.created_at,
            user: {
              id: reply.user.id,
              avatar: teacherAvatar
            }
        }))

        comment.showReplyInput = false
        this.showNotification('回复发表成功', 'success')
      } catch (error) {
        this.showNotification('发表回复失败', 'error')
      }
    },

    // 点赞评论
    async likeComment(comment) {
      try {
        const response = await commentApi.likeComment(comment.id)
        comment.likes = response.likes
        comment.has_liked = !comment.has_liked
        this.showNotification(response.detail, 'success')
      } catch (error) {
        this.showNotification('操作失败', 'error')
      }
    },

    // 删除评论
    async deleteComment(comment) {
      try {
        await this.$confirm('确定要删除这条评论吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await commentApi.deleteComment(comment.id)
        
        // 从列表中移除评论
        const index = this.comments.findIndex(c => c.id === comment.id)
        if (index > -1) {
          this.comments.splice(index, 1)
        }
        
        this.showNotification('评论已删除', 'success')
      } catch (error) {
        if (error !== 'cancel') {
          this.showNotification('删除评论失败', 'error')
        }
      }
    }
  }
};
</script>

<style>
/* 已有样式 */

/* 视频播放器样式 */
.video-wrapper {
  position: relative;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 返回按钮样式调整 */
.back-to-courses {
  display: flex;
  align-items: center;
  margin-right: 20px;
  color: #606266;
  text-decoration: none;
  transition: all 0.3s ease;
}

.back-to-courses:hover {
  color: #409EFF;
}

.back-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f0f9ff;
  margin-right: 20px;
  transition: all 0.3s ease;
}

.back-to-courses:hover .back-icon {
  background-color: #e6f1fc;
  transform: translateX(-3px);
}

/* 教师信息样式 */
.teacher-title {
  font-size: 0.9em;
  color: #606266;
  margin-left: 4px;
}

.instructor {
  display: flex;
  align-items: center;
}

.instructor .material-icons {
  margin-right: 5px;
  color: #409EFF;
}

/* 测试对话面板样式 */
.test-dialogue-panel h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #409EFF;
  font-size: 16px;
}

.preset-dialogues-list {
  max-height: 400px;
  overflow-y: auto;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
}

.test-dialogue-item {
  padding: 12px 16px;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-dialogue-item:hover {
  background-color: #F5F7FA;
}

.test-dialogue-item:last-child {
  border-bottom: none;
}

.test-dialogue-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-weight: 500;
  color: #303133;
}

.dialogue-time {
  font-size: 12px;
  color: #909399;
  background-color: #F0F2F5;
  padding: 2px 6px;
  border-radius: 4px;
}

.test-dialogue-preview {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-dialogues {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.key-points-section {
      margin-top: 20px;
    }
    
    .key-points-list {
      max-height: 250px;
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 4px;
    }
    
    .key-point-item {
      display: flex;
      padding: 10px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }
    
    .key-point-item:hover {
      background-color: #f5f7fa;
    }
    
    .key-point-item:last-child {
      border-bottom: none;
    }
    
    .key-point-time {
      flex: 0 0 60px;
      padding: 2px 6px;
      background-color: #409EFF;
      color: white;
      border-radius: 4px;
      text-align: center;
      font-size: 14px;
      margin-right: 10px;
    }
    
    .key-point-content {
      flex: 1;
      font-size: 14px;
    }

.course-chapters {
  margin-top: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.chapter-item {
  border-bottom: 1px solid #eee;
}

.chapter-item:last-child {
  border-bottom: none;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chapter-header:hover {
  background-color: #f0f7ff;
}

.chapter-title {
  font-weight: 500;
  color: #303133;
}

.chapter-toggle {
  color: #909399;
}

.chapter-lessons {
  background-color: #fff;
}

.lesson-item {
  padding: 10px 16px 10px 32px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.lesson-item:last-child {
  border-bottom: none;
}

.lesson-item:hover {
  background-color: #f5f7fa;
}

.lesson-item.active {
  background-color: #ecf5ff;
  color: #409EFF;
  font-weight: 500;
}

.lesson-title {
  font-size: 14px;
}

/* 添加消息输入指示器样式 */
.message-typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-top: 4px;
}

.message-typing-indicator span {
  height: 6px;
  width: 6px;
  background-color: #5a67d8;
  border-radius: 50%;
  display: inline-block;
  margin: 0 1px;
  animation: typing-indicator 1.4s infinite ease-in-out both;
}

.message-typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}
.message-typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}
.message-typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-indicator {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 时间跳转链接样式 */
.time-jump-link {
  display: inline-block;
  color: #409EFF !important;
  text-decoration: none;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  padding: 2px 6px;
  margin: 0 2px;
  transition: all 0.3s;
  font-weight: 500;
}

.time-jump-link:hover {
  background-color: rgba(64, 158, 255, 0.2);
  color: #1875e5 !important;
  text-decoration: none !important;
}

.time-jump-link::before {
  content: '⏱️';
  margin-right: 4px;
  font-size: 14px;
}
.message-wrapper {
  max-width: 100% !important;
}
.ai-action-btn.companion-btn::before {
  background: linear-gradient(135deg, #44a0ff 0%, #9accff 100%) !important;
}
.message-avatar img {
  padding: 2px !important;
}

.notes-list {
  padding: 20px;
}

.note-item {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.note-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.note-title {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.note-timestamp {
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.note-content {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-footer {
  display: flex;
  justify-content: flex-end;
  font-size: 12px;
  color: #999;
}

.note-date {
  font-size: 12px;
  color: #999;
}

.note-form {
  padding: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.timestamp-input {
  width: 200px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-input-wrapper input {
  width: 100%;
  padding: 8px 35px 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  background-color: #fff;
  transition: border-color 0.2s;
}

.search-input-wrapper input:focus {
  outline: none;
  border-color: #409eff;
}

.search-input-wrapper .search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #909399;
  font-size: 20px;
  cursor: pointer;
}

.rating-dialog-content {
  padding: 20px;
}

.rating-stars {
  text-align: center;
  margin-bottom: 20px;
}

.rating-stars .el-rate {
  font-size: 24px;
}

.comment-count {
  font-size: 14px;
  color: #666;
  margin-left: 4px;
}
</style>



