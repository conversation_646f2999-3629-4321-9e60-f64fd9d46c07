import request from '@/utils/request'
import { useAuthStore } from '@/stores/auth' // Import auth store for token

/**
 * 讲稿设计服务 - PPT解析与图片处理
 */
export default {
  /**
   * 解析PPT文件
   * @param {File} file PPT文件对象
   * @returns {Promise} 解析结果
   */
  parsePPT(file) {
    console.log('PPT解析服务接收到文件:', file);
    
    // 确保参数是有效的File对象
    if (!file || !(file instanceof File)) {
      console.error('传入的文件对象无效:', file);
      return Promise.reject(new Error('无效的文件对象'));
    }
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);
    
    console.log('FormData创建完成，文件名:', file.name);
    
    return request({
      url: '/speech-design/parse/',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.lengthComputable) {
          const percentComplete = (progressEvent.loaded / progressEvent.total) * 100;
          console.log(`上传进度: ${percentComplete.toFixed(2)}%`);
        }
      }
    });
  },
  
  /**
   * 从PPT中提取图片
   * @param {File} file PPT文件对象
   * @returns {Promise} 图片提取结果
   */
  extractPPTImages(file) {
    console.log('PPT图片提取服务接收到文件:', file);
    
    // 确保参数是有效的File对象
    if (!file || !(file instanceof File)) {
      console.error('传入的文件对象无效:', file);
      return Promise.reject(new Error('无效的文件对象'));
    }
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);
    
    console.log('FormData创建完成，文件名:', file.name);
    
    return request({
      url: '/speech-design/extract-images/',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.lengthComputable) {
          const percentComplete = (progressEvent.loaded / progressEvent.total) * 100;
          console.log(`图片提取上传进度: ${percentComplete.toFixed(2)}%`);
        }
      }
    });
  },
  
  /**
   * 生成单个幻灯片的讲稿内容
   * @param {Object} data 包含当前幻灯片和上下文信息的数据
   * @returns {Promise} 生成的讲稿内容
   */
  generateSlideScript(data) {
    console.log('开始生成单个幻灯片讲稿:', data.current_slide.index + 1);
    
    return request({
      url: '/speech-design/generate-slide-script/',
      method: 'post',
      data
    });
  },

  /**
   * 生成单个幻灯片的讲稿内容 (流式版)
   * @param {Object} data 包含当前幻灯片和上下文信息的数据
   * @returns {Promise<Response>} fetch API响应对象，用于流式处理
   */
  generateSlideScriptStream(data) {
    const authStore = useAuthStore()
    const token = authStore.token
    const url = `/api/speech-design/generate-slide-script/` // Ensure your API base is prefixed if needed

    console.log('开始流式生成单个幻灯片讲稿, URL:', url, 'Payload:', data);

    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': '*/*'
      },
      body: JSON.stringify(data),
      cache: 'no-cache',
      credentials: 'same-origin', // or 'include' if your API requires cookies across origins
      mode: 'cors'
    })
    .then(response => {
      if (!response.ok) {
        // Attempt to read error from stream or response body
        return response.text().then(text => {
          console.error('Streaming API error response text:', text);
          let errorMsg = `HTTP error! status: ${response.status}`;
          try {
            const errJson = JSON.parse(text);
            errorMsg = errJson.message || errJson.detail || errorMsg;
          } catch (e) {
            // not a json error, use text if not empty
            if (text) errorMsg = text;
          }
          throw new Error(errorMsg);
        });
      }
      return response;
    })
    .catch(error => {
      console.error('流式生成讲稿请求失败:', error);
      throw error; // Re-throw to be caught by the calling function
    });
  }
} 