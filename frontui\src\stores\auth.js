import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'
import { useRouter } from 'vue-router'

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()
  // 状态
  const user = ref(null)
  const token = ref(null)
  const refreshToken = ref(null)
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => user.value?.roles || null)

  // 从localStorage加载状态
  function loadState() {
    const savedUser = localStorage.getItem('user')
    const savedToken = localStorage.getItem('token')
    const savedRefreshToken = localStorage.getItem('refreshToken')
    
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (e) {
        user.value = null
      }
    }
    
    if (savedToken) token.value = savedToken
    if (savedRefreshToken) refreshToken.value = savedRefreshToken
  }

  // 保存状态到localStorage
  function saveState() {
    if (user.value) {
      localStorage.setItem('user', JSON.stringify(user.value))
    }
    if (token.value) {
      localStorage.setItem('token', token.value)
    }
    if (refreshToken.value) {
      localStorage.setItem('refreshToken', refreshToken.value)
    }
  }

  // 清除状态
  function clearState() {
    user.value = null
    token.value = null
    refreshToken.value = null
    localStorage.removeItem('user')
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
  }

  // 登录
  async function login(username, password) {
    try {
      const response = await authApi.login(username, password)
      user.value = response.user
      token.value = response.access
      refreshToken.value = response.refresh
      saveState()
      return true
    } catch (error) {
      console.error('登录失败:', error)
      return false
    }
  }

  // 注销
  async function logout() {
    clearState()
    router.push('/auth/login')
  }

  // 检查认证状态
  function checkAuth() {
    loadState()
    return isLoggedIn.value
  }

  // 初始化时加载状态
  loadState()

  return {
    user,
    token,
    refreshToken,
    isLoggedIn,
    userRole,
    login,
    logout,
    checkAuth
  }
}) 