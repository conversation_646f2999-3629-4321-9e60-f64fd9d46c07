import request from '@/utils/request'

// 获取商品列表
export function getProducts(params) {
  return request({
    url: '/products/',
    method: 'get',
    params
  })
}

// 获取热门商品
export function getHotProducts() {
  return request({
    url: '/products/hot/',
    method: 'get'
  })
}

// 兑换商品
export function exchangeProduct(data) {
  return request({
    url: '/orders/',
    method: 'post',
    data
  })
}

// 获取兑换记录
export function getOrders(params) {
  return request({
    url: '/orders/',
    method: 'get',
    params
  })
}

// 获取用户积分记录
export function getPointsRecords(params) {
  return request({
    url: '/points-records/',
    method: 'get',
    params
  })
}

// 管理员 - 创建商品
export function createProduct(data) {
  return request({
    url: '/products/',
    method: 'post',
    data
  })
}

// 管理员 - 更新商品
export function updateProduct(id, data) {
  return request({
    url: `/products/${id}/`,
    method: 'put',
    data
  })
}

// 管理员 - 删除商品
export function deleteProduct(id) {
  return request({
    url: `/products/${id}/`,
    method: 'delete'
  })
}

// 管理员 - 修改商品状态
export function updateProductStatus(id, status) {
  return request({
    url: `/products/${id}/`,
    method: 'patch',
    data: { is_active: status }
  })
} 