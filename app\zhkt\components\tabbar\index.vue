<template>
  <view class="tabbar">
    <view 
      class="tabbar-item"
      v-for="(item, index) in items"
      :key="index"
      @tap="switchTab(index)"
    >
      <view class="icon-wrapper">
        <uni-icons 
          :type="currentTab === index ? item.selectedIcon : item.icon"
          :color="currentTab === index ? activeColor : '#999'"
          size="24"
        ></uni-icons>
        <view class="badge" v-if="item.badge">{{ item.badge }}</view>
      </view>
      <text 
        class="text"
        :class="{ active: currentTab === index }"
      >{{ item.text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TabBar',
  
  props: {
    // 当前选中的标签页索引
    value: {
      type: Number,
      default: 0
    },
    // 标签页配置项
    items: {
      type: Array,
      default: () => []
    },
    // 选中时的颜色
    activeColor: {
      type: String,
      default: '#007AFF'
    }
  },

  data() {
    return {
      currentTab: this.value
    }
  },

  watch: {
    value: {
      handler(newVal) {
        this.currentTab = newVal
      },
      immediate: true
    }
  },

  methods: {
    // 切换标签页
    switchTab(index) {
      if (this.currentTab === index) return
      this.currentTab = index
      this.$emit('input', index)
      this.$emit('change', index)
      
      // 如果有配置路径，则进行跳转
      const item = this.items[index]
      if (item.pagePath) {
        uni.switchTab({
          url: item.pagePath
        })
      }
    }
  }
}
</script>

<style lang="scss">
.tabbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);

  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx 0;

    .icon-wrapper {
      position: relative;
      margin-bottom: 6rpx;

      .badge {
        position: absolute;
        top: -10rpx;
        right: -16rpx;
        background: #ff5a5f;
        color: #fff;
        font-size: 20rpx;
        padding: 2rpx 10rpx;
        border-radius: 20rpx;
        min-width: 30rpx;
        text-align: center;
      }
    }

    .text {
      font-size: 24rpx;
      color: #999;
      transition: color 0.3s;

      &.active {
        color: var(--active-color);
        font-weight: 500;
      }
    }
  }
}
</style> 