# -*- coding: utf-8 -*-
# @Time    : 2024/12/24
# <AUTHOR> lhq
# @File    : model_utils.py
# @Description :
import json
from tenacity import retry, stop_after_attempt, wait_fixed
import requests

proxies = {
    'http': 'http://127.0.0.1:10809',
    'https': 'http://127.0.0.1:10809'
}


def keya(content):
    headers = {
        'Authorization': 'Bearer sk-kC9pKG6Pej9ws7iggOFuCanTreSTbsnXX2mJgtDFz0mcqFU0',
    }
    json_data = {
        # 'model': 'gpt-4o-mini',
        'model': 'deepseek-v3',
        'messages': [
            {
                'role': 'user',
                'content': content,
            },
        ],
        'stream': False,
        'temperature': 0.7,
    }

    response = requests.post('https://api.keya.pw/v1/chat/completions', headers=headers, json=json_data,
                             proxies=proxies)
    gpt_content = response.json().get('choices')[0].get('message').get('content')
    return gpt_content


def xedu(content):
    headers = {
        'Authorization': 'Bearer sk-My3rx7aTEY5ERFWFPfwac9Yz3ksBqG53IH2oQV0fx4Scstuj',
    }
    json_data = {
        # 'model': 'gpt-4o',
        'model': 'deepseek-chat',
        # 'model': 'deepseek-reasoner',
        'messages': [
            {
                'role': 'user',
                'content': content,
            },
        ],
        'stream': False,
        'temperature': 0.7,
    }

    response = requests.post('https://fast.xeduapi.com/v1/chat/completions', headers=headers, json=json_data)
    # response = requests.post('https://api.xeduapi.com/v1/chat/completions', headers=headers, json=json_data)
    gpt_content = response.json().get('choices')[0].get('message').get('content')
    return gpt_content


@retry(stop=stop_after_attempt(3), wait=wait_fixed(1))
def poloai(messages):
    headers = {
        'Authorization': 'Bearer sk-x0cKGp1IykbDDpdFlQT1MNEBRqCTk9DnwFI2uHF6q6Axyoz8',
    }
    json_data = {
        'model': 'claude-4-sonnet',
        # 'model': 'claude-3-7-sonnet-20250219-thinking',
        # 'model': 'claude-3-7-sonnet-20250219',
        # 'model': 'claude-3-5-sonnet-20241022',
        'messages': messages,
        'stream': False,
        'temperature': 0.7,
    }

    response = requests.post('https://poloai.top/v1/chat/completions', headers=headers, json=json_data)
    response.raise_for_status()
    gpt_content = response.json().get('choices')[0].get('message').get('content')
    return gpt_content


def qwen(content):
    headers = {
        'Authorization': f"Bearer sk-6bb22bf6b8f548629e975dac88c5faba",
    }

    json_data = {
        # 'model': 'qwen-plus',
        'model': 'deepseek-v3',
        'messages': [
            {
                'role': 'user',
                'content': content,
            },
        ],
    }

    response = requests.post('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', headers=headers,
                             json=json_data)
    gpt_content = response.json().get('choices')[0].get('message').get('content')
    return gpt_content


def llama(content):
    # llama
    port = '26928'
    token = 'B1se2vgH8F'
    # url = f"https://hz-jcy-1.matpool.com:{port}/api/chat?token={token}"
    url = "http://192.168.1.123:11434/api/chat"
    payload = json.dumps({
        # "model": "qwen2.5:7b",
        "model": "qwen:7b",
        # "model": "wangshenzhi/llama3-8b-chinese-chat-ollama-q8",
        # "model": "llama3.2-vision",
        "messages": [
            {
                "role": "user",
                "content": content,
            }
        ]
    })
    headers = {
        'Content-Type': 'application/json'
    }
    # 使用流式请求
    response = requests.request("POST", url, headers=headers, data=payload)
    # 检查响应状态
    gpt_content = ""
    if response.status_code == 200:
        # 按块读取响应流
        for chunk in response.iter_lines(decode_unicode=True):
            if chunk:  # 检查非空块
                llama_json = json.loads(chunk.decode('utf-8'))
                gpt_content += llama_json.get('message').get('content')
    return gpt_content


