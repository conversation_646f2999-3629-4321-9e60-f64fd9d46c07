# Generated by Django 3.2.20 on 2025-06-19 14:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0067_auto_20250618_1840'),
    ]

    operations = [
        migrations.CreateModel(
            name='GeneratedVideo',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='生成视频ID')),
                ('user_id', models.BigIntegerField(db_index=True, verbose_name='用户ID')),
                ('name', models.CharField(max_length=100, verbose_name='视频名称')),
                ('digital_human_id', models.BigIntegerField(blank=True, null=True, verbose_name='使用的数字人ID')),
                ('input_type', models.CharField(choices=[('text', '文本输入'), ('audio', '音频上传')], default='text', max_length=10, verbose_name='输入类型')),
                ('audio_id', models.BigIntegerField(blank=True, help_text='文本输入时生成的音频ID', null=True, verbose_name='生成的音频ID')),
                ('uploaded_audio_url', models.CharField(blank=True, help_text='用户上传的音频文件路径', max_length=255, null=True, verbose_name='上传的音频URL')),
                ('text_content', models.TextField(blank=True, help_text='文本输入时的原始内容', null=True, verbose_name='文本内容')),
                ('duration', models.CharField(blank=True, max_length=10, null=True, verbose_name='视频时长')),
                ('video_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='视频URL')),
                ('cover_image', models.CharField(blank=True, max_length=255, null=True, verbose_name='封面图片路径')),
                ('status', models.CharField(choices=[('processing', '处理中'), ('success', '成功'), ('failed', '失败')], default='processing', max_length=20, verbose_name='处理状态')),
                ('power_consumed', models.IntegerField(default=0, verbose_name='消耗的算力')),
                ('tags', models.TextField(blank=True, help_text='以逗号分隔的标签ID', null=True, verbose_name='视频标签')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
            ],
            options={
                'verbose_name': '生成视频',
                'verbose_name_plural': '生成视频',
                'db_table': 'zhkt_generated_videos',
                'ordering': ['-create_time'],
            },
        ),
        migrations.AddIndex(
            model_name='generatedvideo',
            index=models.Index(fields=['user_id'], name='zhkt_genera_user_id_165bd5_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedvideo',
            index=models.Index(fields=['status'], name='zhkt_genera_status_8fb2c1_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedvideo',
            index=models.Index(fields=['input_type'], name='zhkt_genera_input_t_1cf1fd_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedvideo',
            index=models.Index(fields=['create_time'], name='zhkt_genera_create__85ece5_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedvideo',
            index=models.Index(fields=['digital_human_id'], name='zhkt_genera_digital_200661_idx'),
        ),
    ]
