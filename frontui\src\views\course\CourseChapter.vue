<template>
  <div class="course-chapter">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">章节管理</h1>
      <button 
        class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
        @click="showAddChapterDialog"
      >
        <i class="fas fa-plus text-sm"></i>
        添加章节
      </button>
    </div>

    <div class="bg-white p-6 rounded-lg shadow">
      <!-- 章节树形结构 -->
      <el-tree
        ref="treeRef"
        :data="chapters"
        :props="defaultProps"
        node-key="id"
        default-expand-all
        v-loading="loading"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node flex items-center justify-between w-full pr-4">
            <div class="flex items-center">
              <span class="material-icons text-gray-400 mr-2">
                {{ data.type === 'chapter' ? 'folder' : 'description' }}
              </span>
              <span>{{ node.label }}</span>
            </div>
            <div class="flex gap-2">
              <button 
                v-if="data.type === 'chapter'"
                @click.stop="showAddLessonDialog(data)"
                class="text-blue-600 hover:text-blue-800"
              >
                添加课时
              </button>
              <template v-else>
                <div v-if="data.video_url" class="flex gap-2">
                  <button 
                    @click.stop="playVideo(data)"
                    class="text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    <span class="material-icons text-sm mr-1">play_circle</span>
                    播放
                  </button>
                  <button 
                    @click.stop="deleteVideo(data)"
                    class="text-red-600 hover:text-red-800 flex items-center"
                  >
                    <span class="material-icons text-sm mr-1">delete</span>
                    删除视频
                  </button>
                </div>
                <button 
                  v-else
                  @click.stop="showUploadVideo(data)"
                  class="text-orange-600 hover:text-orange-800 flex items-center"
                >
                  <span class="material-icons text-sm mr-1">upload</span>
                  上传视频
                </button>
              </template>
              <button 
                @click.stop="showEditDialog(data)"
                class="text-green-600 hover:text-green-800"
              >
                编辑
              </button>
              <button 
                @click.stop="confirmDelete(data)"
                class="text-red-600 hover:text-red-800"
              >
                删除
              </button>
            </div>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 添加/编辑章节对话框 -->
    <el-dialog
      v-model="chapterDialogVisible"
      :title="editingChapter ? '编辑章节' : '添加章节'"
      width="500px"
    >
      <el-form :model="chapterForm" label-width="80px">
        <el-form-item label="章节名称" required>
          <el-input v-model="chapterForm.name" placeholder="请输入章节名称"></el-input>
        </el-form-item>
        <el-form-item label="章节描述">
          <el-input 
            v-model="chapterForm.description" 
            type="textarea" 
            placeholder="请输入章节描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="chapterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleChapterSubmit" :loading="submitting">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加/编辑课时对话框 -->
    <el-dialog
      v-model="lessonDialogVisible"
      :title="editingLesson ? '编辑课时' : '添加课时'"
      width="500px"
    >
      <el-form :model="lessonForm" label-width="80px">
        <el-form-item label="课时名称" required>
          <el-input v-model="lessonForm.name" placeholder="请输入课时名称"></el-input>
        </el-form-item>
        <el-form-item label="课时描述">
          <el-input 
            v-model="lessonForm.description" 
            type="textarea" 
            placeholder="请输入课时描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="lessonDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleLessonSubmit" :loading="submitting">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 视频播放器 -->
    <el-dialog
      v-model="showVideoModal"
      :title="currentLesson?.title || '视频播放'"
      width="60%"
      :before-close="closeVideoPlayer"
      class="video-player-dialog"
      top="5vh"
    >
      <div class="video-player-container">
        <div v-if="!currentLesson?.video_url" class="no-video-message">
          <div class="flex flex-col items-center justify-center p-6">
            <span class="material-icons text-5xl text-gray-400 mb-3">videocam_off</span>
            <p class="text-lg text-gray-500">此课时暂无视频</p>
          </div>
        </div>
        <video 
          v-else
          ref="videoPlayer"
          controls
          class="w-full rounded-md"
          style="max-height: 65vh;"
        >
          <source 
            :src="currentLesson?.video_url" 
            type="video/mp4"
          >
          您的浏览器不支持HTML5视频播放
        </video>
        
        <div v-if="currentLesson?.video_url" class="mt-4 p-4 bg-gray-50 rounded-lg">
          <div class="text-sm text-gray-500 mt-1">
            {{ currentLesson.description? currentLesson.description : '暂无描述' }}
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 视频上传对话框 -->
    <el-dialog
      v-model="showVideoUploadDialog"
      :title="`上传视频 - ${uploadingLesson?.title}`"
      width="600px"
      destroy-on-close
    >
      <div class="p-4">
        <div v-if="!videoFile" class="upload-area">
          <el-upload
            class="upload-dragger"
            drag
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :accept="uploadProps.accept"
            :on-change="handleVideoFileSelect"
          >
            <div class="flex flex-col items-center justify-center p-8">
              <span class="material-icons text-5xl text-gray-400 mb-4">cloud_upload</span>
              <div class="el-upload__text">
                <span class="text-base">将文件拖到此处或<em>点击上传</em></span>
                <p class="text-sm text-gray-500 mt-2">支持 MP4、MOV 格式视频，最大 500MB</p>
              </div>
            </div>
          </el-upload>
        </div>

        <div v-else class="selected-video mt-4">
          <div class="flex items-start gap-4 bg-gray-50 p-4 rounded-lg">
            <div class="video-info flex-grow">
              <div class="flex items-center mb-2">
                <span class="material-icons text-blue-600 mr-2">video_file</span>
                <span class="font-medium">{{ videoFile.name }}</span>
              </div>
              <div class="text-sm text-gray-500">
                <p>大小：{{ formatFileSize(videoFile.size) }}</p>
                <p>类型：{{ videoFile.type }}</p>
              </div>
            </div>
            <button 
              @click="videoFile = null"
              class="text-gray-500 hover:text-gray-700"
            >
              <span class="material-icons">close</span>
            </button>
          </div>

          <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>上传进度</span>
              <!-- <span>{{ uploadProgress }}%</span> -->
            </div>
            <el-progress :percentage="uploadProgress"></el-progress>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showVideoUploadDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleVideoUpload" 
            :loading="submitting"
            :disabled="!videoFile"
          >
            开始上传
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { courseApi } from '@/api/course'
import { lessonApi } from '@/api/lesson'
import { resourceApi } from '@/api/resource'

const route = useRoute()
const courseId = ref(route.params.courseId)

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 视频播放相关
const showVideoModal = ref(false)
const currentLesson = ref(null)
const videoPlayer = ref(null)

// 视频上传相关
const uploadProps = {
  accept: 'video/mp4,video/mov'
}
const showVideoUploadDialog = ref(false)
const uploadingLesson = ref(null)
const videoFile = ref(null)
const uploadProgress = ref(0)

// 树形结构配置
const treeRef = ref(null)
const defaultProps = {
  children: 'children',
  label: 'title'
}

// 章节数据
const chapters = ref([])

// 章节对话框
const chapterDialogVisible = ref(false)
const editingChapter = ref(null)
const chapterForm = ref({
  name: '',
  description: ''
})

// 课时对话框
const lessonDialogVisible = ref(false)
const editingLesson = ref(null)
const currentChapter = ref(null)
const lessonForm = ref({
  name: '',
  description: ''
})

// 转换章节数据结构
const transformChapterData = (chapters) => {
  return chapters.map(chapter => ({
    ...chapter,
    type: 'chapter',
    children: [] // 预留给课时数据
  }))
}

// 加载课时数据
const loadLessons = async (chapter) => {
  try {
    const response = await lessonApi.getLessons(chapter.id)
    const lessons = response.results
    
    // 获取每个课时的视频资源
    const lessonsWithVideo = await Promise.all(lessons.map(async (lesson) => {
      try {
        const resourceResponse = await resourceApi.getResourcesByLesson({
          lesson_id: lesson.id,
          resource_type: 'VIDEO'
        })
        const videoResource = resourceResponse.results?.[0]
        if (videoResource) {
          lesson.video_url = "https://clkj-ai-education.oss-cn-shenzhen.aliyuncs.com/" + videoResource.file
          lesson.videoFile = {
            id: videoResource.id,
            name: videoResource.title || '视频文件',
            size: videoResource.size || 0,
            type: 'video/mp4'
          }
        }
      } catch (error) {
        console.error('获取课时视频资源失败:', error)
      }
      return {
        ...lesson,
        type: 'lesson',
        chapter: chapter.id
      }
    }))
    
    return lessonsWithVideo
  } catch (error) {
    console.error('加载课时数据失败:', error)
    return []
  }
}

// 监听路由参数变化
watch(() => route.params.courseId, (newId) => {
  console.log('Route courseId changed:', newId)
  if (newId) {
    courseId.value = newId
    loadChapters()
  }
})

// 加载章节数据
const loadChapters = async () => {
  console.log('Loading chapters for courseId:', courseId.value)
  if (!courseId.value) {
    console.warn('No courseId available')
    return
  }

  try {
    loading.value = true
    const response = await courseApi.getChaptersByCourse(courseId.value)
    console.log('Chapters loaded:', response)
    
    // 转换章节数据
    const transformedChapters = transformChapterData(response.results)
    
    // 加载每个章节的课时
    for (const chapter of transformedChapters) {
      chapter.children = await loadLessons(chapter)
    }
    
    chapters.value = transformedChapters
  } catch (error) {
    console.error('Failed to load chapters:', error)
    ElMessage.error('加载章节数据失败')
  } finally {
    loading.value = false
  }
}

// 显示视频上传对话框
const showUploadVideo = (lesson) => {
  uploadingLesson.value = lesson
  videoFile.value = null
  uploadProgress.value = 0
  showVideoUploadDialog.value = true
}

// 处理视频文件选择
const handleVideoFileSelect = (uploadFile) => {
  const file = uploadFile.raw
  if (!file) return false
  
  // 验证文件大小（最大500MB）
  const maxSize = 500 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('视频文件大小不能超过500MB')
    return false
  }
  
  videoFile.value = file
  return false // 阻止自动上传
}

// 删除视频
const deleteVideo = async (lesson) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该课时的视频吗？此操作无法撤销。',
      '删除视频',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (lesson.videoFile?.id) {
      await resourceApi.deleteResource(lesson.videoFile.id)
      
      // 重新加载章节数据以更新视频状态
      await loadChapters()
      ElMessage.success('视频删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除视频失败:', error)
      ElMessage.error('删除视频失败')
    }
  }
}

// 播放视频
const playVideo = (lesson) => {
  if (!lesson.video_url) {
    ElMessage.warning('该课时暂无视频')
    return
  }
  currentLesson.value = lesson
  showVideoModal.value = true
}

// 关闭视频播放器
const closeVideoPlayer = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause()
  }
  showVideoModal.value = false
  currentLesson.value = null
}

// 显示添加章节对话框
const showAddChapterDialog = () => {
  editingChapter.value = null
  chapterForm.value = {
    name: '',
    description: ''
  }
  chapterDialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (data) => {
  console.log('showEditDialog',data);
  
  if (data.type === 'chapter') {
    editingChapter.value = data
    chapterForm.value = {
      name: data.title,
      description: data.description || ''
    }
    chapterDialogVisible.value = true
  } else {
    currentChapter.value = chapters.value.find(chapter => chapter.id === data.chapter)
    editingLesson.value = data
    lessonForm.value = {
      name: data.title,
      description: data.description || ''
    }
    lessonDialogVisible.value = true
  }
}

// 显示添加课时对话框
const showAddLessonDialog = (chapter) => {
  editingLesson.value = null
  currentChapter.value = chapter
  lessonForm.value = {
    name: '',
    description: ''
  }
  lessonDialogVisible.value = true
}

// 处理章节提交
const handleChapterSubmit = async () => {
  if (!chapterForm.value.name.trim()) {
    ElMessage.warning('请输入章节名称')
    return
  }

  try {
    submitting.value = true
    const data = {
      course: courseId.value,
      title: chapterForm.value.name,
      description: chapterForm.value.description,
      order: chapters.value.length + 1
    }

    if (editingChapter.value) {
      // 编辑现有章节
      await courseApi.updateChapter(editingChapter.value.id, data)
      ElMessage.success('章节编辑成功')
    } else {
      // 添加新章节
      await courseApi.createChapter(data)
      ElMessage.success('章节添加成功')
    }

    // 重新加载章节数据
    await loadChapters()
    chapterDialogVisible.value = false
  } catch (error) {
    ElMessage.error(editingChapter.value ? '章节编辑失败' : '章节添加失败')
    console.error('章节操作失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理课时提交
const handleLessonSubmit = async () => {
  if (!lessonForm.value.name.trim()) {
    ElMessage.warning('请输入课时名称')
    return
  }

  try {
    submitting.value = true
    const data = {
      chapter: currentChapter.value.id,
      title: lessonForm.value.name,
      description: lessonForm.value.description,
      order: currentChapter.value.children.length + 1
    }

    if (editingLesson.value) {
      // 编辑现有课时
      await lessonApi.updateLesson(editingLesson.value.id, data)
      ElMessage.success('课时编辑成功')
    } else {
      // 添加新课时
      await lessonApi.createLesson(data)
      ElMessage.success('课时添加成功')
    }

    // 重新加载章节数据
    await loadChapters()
    lessonDialogVisible.value = false
  } catch (error) {
    ElMessage.error(editingLesson.value ? '课时编辑失败' : '课时添加失败')
    console.error('课时操作失败:', error)
  } finally {
    submitting.value = false
  }
}

// 确认删除
const confirmDelete = (data) => {
  ElMessageBox.confirm(
    `确定要删除${data.type === 'chapter' ? '章节' : '课时'} "${data.title}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await courseApi.deleteChapter(data.id)
      await loadChapters()
      ElMessage.success('删除成功')
    } catch (error) {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    }
  }).catch(() => {})
}

// 处理视频上传
const handleVideoUpload = async () => {
  if (!videoFile.value || !uploadingLesson.value) return
  
  try {
    submitting.value = true
    
    // 模拟上传进度
    const updateProgress = () => {
      if (uploadProgress.value < 90) {
        uploadProgress.value = Math.floor(uploadProgress.value + Math.random() * 30)
        setTimeout(updateProgress, 500)
      }
    }
    updateProgress()
    
    // 上传视频
    const response = await lessonApi.uploadLessonVideo(uploadingLesson.value.id, videoFile.value)
    
    if (response) {
      uploadingLesson.value.videoFile = {
        name: response.title,
        size: 0,
        type: 'video/mp4'
      }
      uploadingLesson.value.video_url = response.url
      
      uploadProgress.value = 100
      ElMessage.success('视频上传成功')
      
      // 重新加载章节数据以更新视频状态
      await loadChapters()
      
      // 关闭对话框
      showVideoUploadDialog.value = false
    }
  } catch (error) {
    console.error('视频上传失败:', error)
    ElMessage.error('视频上传失败：' + (error.response?.data?.error || error.message))
  } finally {
    submitting.value = false
    uploadProgress.value = 0
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
}

// 初始化加载
onMounted(() => {
  console.log('Component mounted, courseId:', courseId.value)
  if (courseId.value) {
    loadChapters()
  } else {
    console.warn('CourseChapter mounted without courseId')
  }
})

</script> 

<style scoped>
.custom-tree-node {
  flex: 1;
  padding: 8px 0;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

:deep(.el-tree-node__children) {
  padding-left: 24px;
}

/* 视频播放器样式 */
.video-player-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.video-player-dialog :deep(.el-dialog) {
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
  border: 1px solid #e2e8f0;
}

.video-player-dialog :deep(.el-dialog__body) {
  padding: 10px;
}

.video-player-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

.video-player-dialog :deep(.el-dialog__headerbtn) {
  top: 10px;
}

.video-player-container {
  position: relative;
}

.no-video-message {
  background-color: #f1f5f9;
  border-radius: 4px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 章节列表样式 */
.chapter-card {
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.chapter-card :deep(.el-card__header) {
  padding: 10px 16px;
  background-color: #f8fafc;
}

.chapter-card :deep(.el-card__body) {
  padding: 8px 12px;
}

.chapter-header {
  transition: background-color 0.2s;
}

.lesson-item {
  transition: all 0.2s;
  margin-bottom: 6px;
}

.lesson-item:hover {
  background-color: #f3f4f6;
}

/* 视频播放器控制样式增强 */
video::-webkit-media-controls-play-button,
video::-webkit-media-controls-timeline,
video::-webkit-media-controls-volume-slider {
  cursor: pointer;
}

video::-webkit-media-controls-timeline {
  height: 6px;
}

/* 调整视频播放器边距 */
video {
  margin: 0;
  border-radius: 4px;
}
</style> 