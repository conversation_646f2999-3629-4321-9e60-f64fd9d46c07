# -*- coding: utf-8 -*-
"""
Google Gemini TTS 简单工具类
"""

import os
import struct
from datetime import datetime

from google import genai
from google.genai import types


class _GeminiClientBase:
    """基础客户端类，用于初始化Gemini客户端"""

    def __init__(self):
        """初始化"""
        # proxy_user_pass = "lhq888888:lhq888888"
        # proxy_url = f'http://{proxy_user_pass}@47.242.215.121:10809'
        # os.environ['HTTPS_PROXY'] = proxy_url
        # os.environ['HTTP_PROXY'] = proxy_url

        # 写死的API Key
        api_key = "AIzaSyC-ibugmJLJik8xyztgiWM9_OFJKTUyBl8"
        self.pro_client = genai.Client(
            api_key=api_key,
        )
        """
        自己
        AIzaSyD5Dn7OMdlyWM9vtwJ6t829XenEgUIDA7Q
        买的
        AIzaSyBOAQ8gmEDwFNohBkLr61In2MVFj35cWuI
        AIzaSyB9RxpHii0ty37nJoVPLAkn9RTijwsAgjU
        """
        self.flash_client = genai.Client(
            api_key="AIzaSyB9RxpHii0ty37nJoVPLAkn9RTijwsAgjU",
        )


class GeminiDialogueGenerator(_GeminiClientBase):
    """Gemini 对话生成器 (通用)"""

    def generate_pro_content(self, prompt: str):
        """根据给定的提示词生成内容"""

        response = self.pro_client.models.generate_content(
            model="gemini-2.5-pro-preview-06-05",
            contents=prompt
        )

        return response.text

    def generate_flash_content(self, prompt: str):
        """根据给定的提示词生成内容"""

        response = self.flash_client.models.generate_content(
            model="gemini-2.5-flash-preview-05-20",
            contents=prompt
        )

        return response.text


class GeminiAudioGenerator(_GeminiClientBase):
    """Gemini 音频生成器"""

    def create_wav_header(self, data_size: int, sample_rate: int = 24000) -> bytes:
        """创建WAV文件头"""
        header = struct.pack('<4sI4s', b'RIFF', 36 + data_size, b'WAVE')
        fmt_chunk = struct.pack('<4sIHHIIHH',
                                b'fmt ', 16, 1, 1, sample_rate,
                                sample_rate * 2, 2, 16)
        data_header = struct.pack('<4sI', b'data', data_size)
        return header + fmt_chunk + data_header

    def convert_l16_to_wav(self, pcm_data: bytes, sample_rate: int = 24000) -> bytes:
        """将L16 PCM数据转换为WAV格式"""
        wav_header = self.create_wav_header(len(pcm_data), sample_rate)
        return wav_header + pcm_data

    def generate_audio(self, script_text: str):
        """生成音频"""
        config = types.GenerateContentConfig(
            temperature=1.2,
            response_modalities=["AUDIO"],
            speech_config=types.SpeechConfig(
                multi_speaker_voice_config=types.MultiSpeakerVoiceConfig(
                    speaker_voice_configs=[
                        types.SpeakerVoiceConfig(
                            speaker='Anya',
                            voice_config=types.VoiceConfig(
                                prebuilt_voice_config=types.PrebuiltVoiceConfig(
                                    voice_name='Charon'
                                )
                            )
                        ),
                        types.SpeakerVoiceConfig(
                            speaker='Liam',
                            voice_config=types.VoiceConfig(
                                prebuilt_voice_config=types.PrebuiltVoiceConfig(
                                    voice_name='Zephyr'
                                )
                            )
                        ),
                    ]
                )
            )
        )

        response = self.pro_client.models.generate_content(
            model="gemini-2.5-pro-preview-tts",
            contents=script_text,
            config=config
        )

        return response

    def save_audio(self, response, output_dir: str = "output_audio", timestamp: str = None):
        """保存音频"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        ts = timestamp if timestamp else datetime.now().strftime("%Y%m%d_%H%M%S")

        for candidate in response.candidates:
            if candidate.content and candidate.content.parts:
                for part in candidate.content.parts:
                    if part.inline_data and part.inline_data.data:
                        audio_data = part.inline_data.data
                        mime_type = getattr(part.inline_data, 'mime_type', '')

                        # 处理L16 PCM格式
                        if 'audio/L16' in mime_type:
                            sample_rate = 24000
                            if 'rate=' in mime_type:
                                rate_part = mime_type.split('rate=')[1].split(';')[0]
                                sample_rate = int(rate_part)

                            final_audio_data = self.convert_l16_to_wav(audio_data, sample_rate)
                        else:
                            final_audio_data = audio_data

                        # 保存音频文件
                        audio_filename = f"{output_dir}/dialogue_{ts}.wav"
                        with open(audio_filename, "wb") as f:
                            f.write(final_audio_data)

                        return audio_filename

        raise Exception("未找到音频数据")


def generate_dialogue_and_audio(content_text: str, output_dir: str):
    """
    一个辅助函数，封装了生成对话脚本和音频的完整流程。
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 构造对话脚本的提示词
    prompt = f"""
    {content_text}
    
    生成真实的人物对话播客音频内容
    主持人名字是Anya和Liam
    输出要求:
    - 直接输出人物对话的讲稿内容
    - 不要有"以下是讲稿"、"根据文档内容"等开头语
    - 不要提起对方名字
    输出格式：
    Anya: xxxx
    Liam: xxxx
    
    请直接生成中文播客内容
    """

    dialogue_generator = GeminiDialogueGenerator()
    audio_generator = GeminiAudioGenerator()

    # 1. 生成脚本
    script_text = dialogue_generator.generate_pro_content(prompt)

    # 准备时间戳和文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 2. 保存脚本
    script_filename = f"{output_dir}/dialogue_{timestamp}.txt"
    with open(script_filename, "w", encoding="utf-8") as f:
        f.write(script_text)

    # 3. 生成音频
    audio_response = audio_generator.generate_audio(script_text)

    # 4. 保存音频
    audio_filename = audio_generator.save_audio(audio_response, output_dir, timestamp)

    return {
        'audio_file': audio_filename,
        'script_file': script_filename
    }

if __name__ == '__main__':
    generate_dialogue_and_audio("你好，我是Anya，今天我们聊一下关于AI的话题。", "output_audio")

    # print(GeminiDialogueGenerator().generate_flash_content("1+1"))