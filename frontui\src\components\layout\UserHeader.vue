<template>
  <div class="sticky top-0 z-10 flex h-16 bg-white border-b shadow-sm">
    <!-- Mobile menu button -->
    <button class="px-4 my-auto text-gray-700 border-r border-gray-200 hover:bg-gray-100" @click="toggleMobileMenu">
      <el-icon class="text-lg"><Menu /></el-icon>
    </button>
    
    <!-- Breadcrumb navigation -->
    <nav class="flex items-center ml-4 flex-1" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-2">
        <li class="inline-flex items-center">
          <router-link :to="isAdmin ? '/admin/dashboard' : isTeacher ? '/teacher/dashboard' : '/student/dashboard'" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
            <el-icon class="mr-2"><House /></el-icon>
            {{ t('general.home') }}
          </router-link>
        </li>
        <li v-for="(crumb, index) in breadcrumbs" :key="index">
          <div class="flex items-center">
            <el-icon class="text-gray-400 mx-1"><ArrowRight /></el-icon>
            <router-link 
              :to="crumb.path" 
              class="text-sm font-medium text-gray-500 hover:text-blue-600"
              :class="{ 'text-blue-600': index === breadcrumbs.length - 1 }"
            >
              {{ crumb.name }}
            </router-link>
          </div>
        </li>
      </ol>
      <!-- <h2 class="text-xl font-semibold text-gray-800">{{ pageTitle }}</h2> -->
    </nav>
    
    <!-- Right side user actions area -->
    <div class="flex items-center">
      <!-- Date display -->
      <div class="hidden md:block mr-4 text-sm text-gray-500">
        {{ formattedDate }}
      </div>
      
      <!-- Points display for student role -->
      <div v-if="!isAdmin && !isTeacher" :class="`hidden sm:flex items-center ${pointsBgClass} px-3 py-1 rounded-full mr-4`">
        <el-icon class="mr-1"><Coin /></el-icon>
        <span id="user-points" class="user-points">{{ formattedPoints }} {{ t('general.points') }}</span>
      </div>

      <!-- Role badge for admin -->
      <div v-if="isAdmin" class="hidden sm:flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full mr-4">
        <el-icon class="mr-1"><School /></el-icon>
        <span class="font-medium text-sm">{{ t('role.admin') }}</span>
      </div>

      <!-- Role badge for teacher -->
      <div v-if="!isAdmin && isTeacher" class="hidden sm:flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full mr-4">
        <el-icon class="mr-1"><School /></el-icon>
        <span class="font-medium text-sm">{{ t('role.teacher') }}</span>
      </div>

      <!-- Role badge for student -->
      <div v-if="!isAdmin && !isTeacher" class="hidden sm:flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full mr-4">
        <el-icon class="mr-1"><School /></el-icon>
        <span class="font-medium text-sm">{{ t('role.student') }}</span>
      </div>
      
      <!-- 语言切换 -->
      <div class="relative mr-4">
        <LanguageSwitcher />
      </div>
      
      <!-- Notifications -->
      <div class="relative mr-4">
        <button class="relative p-2 text-gray-500 focus:outline-none hover:text-gray-700 hover:bg-gray-100 rounded-full">
          <el-icon><Bell /></el-icon>
          <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
        </button>
      </div>
      
      <!-- User menu -->
      <div class="relative mr-4">
        <button id="user-menu-button" class="flex items-center max-w-xs text-sm bg-white rounded-full focus:outline-none focus:ring-offset-2 focus:ring-blue-500" @click="toggleUserMenu">
          <img class="user-avatar w-8 h-8 rounded-full" :src="userAvatar" alt="用户头像">
          <span class="ml-2 user-name hidden sm:inline">{{ userData.name }}</span>
        </button>
        
        <!-- Dropdown menu -->
        <div id="user-menu" 
            class="absolute right-0 w-48 py-1 mt-2 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-20"
            :class="{ 'hidden': !userMenuOpen }"
            role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
          <!-- 根据角色不同路由不同 -->
          <router-link :to="isTeacher ? '/teacher/profile' : '/profile'" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">{{ t('userHeader.profile') }}</router-link>
          <router-link :to="isTeacher ? '/teacher/settings' : '/settings'" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">{{ t('userHeader.settings') }}</router-link>
          <button @click="showVerificationModal = true" class="block w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50" role="menuitem" tabindex="-1">
            <span class="flex items-center">
              <el-icon class="mr-1"><Check /></el-icon>
              {{ t('userHeader.activate') }}
            </span>
          </button>
          <div class="border-t border-gray-100 my-1"></div>
          <button @click="handleLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">{{ t('userHeader.logout') }}</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 激活认证模态框 -->
  <el-dialog
    v-model="showVerificationModal"
    :title="t('userHeader.verification.title')"
    width="500px"
    :before-close="handleCloseVerificationModal"
    custom-class="verification-dialog"
  >
    <div class="verification-content">
      <el-alert
        v-if="!isVerified"
        type="info"
        show-icon
        :title="t('userHeader.verification.alertTitle')"
        :description="isTeacher ? t('userHeader.verification.teacherDescription') : t('userHeader.verification.studentDescription')"
        class="mb-4"
      />
      <el-alert
        v-else
        type="success"
        show-icon
        :title="t('userHeader.verification.successTitle')"
        :description="isTeacher ? t('userHeader.verification.teacherSuccess') : t('userHeader.verification.studentSuccess')"
        class="mb-4"
      />
      
      <el-steps :active="activeStep" finish-status="success" simple class="mb-4">
        <el-step :title="t('userHeader.verification.step1')" />
        <el-step :title="t('userHeader.verification.step2')" />
        <el-step :title="t('userHeader.verification.step3')" />
      </el-steps>
      
      <el-form
        v-if="activeStep === 1"
        ref="verificationFormRef"
        :model="verificationForm"
        :rules="verificationRules"
        label-width="80px"
        class="mt-4"
      >
        <el-form-item :label="t('userHeader.verification.name')" prop="name">
          <el-input v-model="verificationForm.name" :placeholder="t('userHeader.verification.namePlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('userHeader.verification.idCard')" prop="idCard">
          <el-input v-model="verificationForm.idCard" :placeholder="t('userHeader.verification.idCardPlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('userHeader.verification.phone')" prop="phone">
          <el-input v-model="verificationForm.phone" :placeholder="t('userHeader.verification.phonePlaceholder')" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="goToNextStep">{{ t('userHeader.verification.next') }}</el-button>
          <el-button @click="handleCloseVerificationModal">{{ t('general.cancel') }}</el-button>
        </el-form-item>
      </el-form>
      
      <el-form
        v-if="activeStep === 2"
        ref="verificationCodeFormRef"
        :model="verificationForm"
        :rules="verificationCodeRules"
        label-width="80px"
        class="mt-4"
      >
        <el-form-item :label="t('userHeader.verification.phone')">
          <div class="flex items-center">
            <span>{{ verificationForm.phone }}</span>
            <el-button type="text" class="ml-4" @click="activeStep = 1">{{ t('userHeader.verification.modify') }}</el-button>
          </div>
        </el-form-item>
        <el-form-item :label="t('userHeader.verification.code')" prop="verificationCode">
          <div class="flex">
            <el-input v-model="verificationForm.verificationCode" :placeholder="t('userHeader.verification.codePlaceholder')" class="flex-1 mr-2" />
            <el-button 
              :disabled="countdown > 0" 
              @click="sendVerificationCode"
              :loading="sendingCode"
            >
              {{ countdown > 0 ? t('userHeader.verification.resend', { seconds: countdown }) : t('userHeader.verification.getCode') }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="verifyCode">{{ t('userHeader.verification.verify') }}</el-button>
          <el-button @click="activeStep = 1">{{ t('userHeader.verification.previous') }}</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="activeStep === 3" class="verification-success text-center py-6">
        <el-icon class="text-green-500 text-6xl mb-4"><CircleCheckFilled /></el-icon>
        <h3 class="text-xl font-bold mb-2">{{ t('userHeader.verification.successTitle') }}</h3>
        <p class="text-gray-600 mb-4">
          {{ isTeacher ? t('userHeader.verification.teacherSuccess') : t('userHeader.verification.studentSuccess') }}
        </p>
        <el-button type="primary" @click="handleCloseVerificationModal">{{ t('userHeader.verification.complete') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useI18n } from 'vue-i18n'
import LanguageSwitcher from '@/components/common/LanguageSwitcher.vue'
import { 
  Menu, 
  House, 
  ArrowRight, 
  Coin, 
  Bell,
  School,
  Check,
  CircleCheckFilled
} from '@element-plus/icons-vue'
import student1Avatar from '@/assets/images/avatars/student1.png'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import adminAvatar from '@/assets/images/avatars/user_avatar.jpg'
import { ElMessage } from 'element-plus'

const { t, locale } = useI18n()

// Props
const props = defineProps({
  pageTitle: {
    type: String,
    default: '智慧课堂'
  },
  userName: {
    type: String,
    required: true
  },
  userAvatar: {
    type: String,
    required: true
  },
  userPoints: {
    type: [Number, String],
    default: 0
  },
  userRole: {
    type: String,
    default: 'student' // 'student' or 'teacher'
  }
})

// Router and route
const route = useRoute()
const authStore = useAuthStore()
const roleList = authStore.userRole.map(role => role.code)

// Check if user is teacher
const isTeacher = computed(() => roleList.includes('teacher'))

// Check if user is admin
const isAdmin = computed(() => roleList.includes('admin'))

// User data
const userData = computed(() => ({
  name: props.userName,
  avatar: props.userAvatar,
  points: props.userPoints || 0
}))

// Use imported avatar or fall back to the appropriate default
const userAvatar = computed(() => {
  // If the path not starts with 'src/' or '/src/', use the imported avatar
  if (!props.userAvatar.includes('/src/assets/') && !props.userAvatar.startsWith('https:')) {
    if(isAdmin.value){  
      return adminAvatar
    }else{
      return isTeacher.value ? teacherAvatar : student1Avatar
    }
  }
  // Otherwise, use the avatar path from props
  return props.userAvatar
})

// Emits
const emit = defineEmits(['toggleSidebar', 'toggleCollapse'])

// State
const userMenuOpen = ref(false)
const showVerificationModal = ref(false)
const activeStep = ref(1)
const verificationForm = ref({
  name: '',
  idCard: '',
  phone: '',
  verificationCode: ''
})
const verificationFormRef = ref(null)
const verificationCodeFormRef = ref(null)
const verificationRules = {
  name: [
    { required: true, message: t('userHeader.verification.nameRequired'), trigger: 'blur' },
    { min: 2, max: 20, message: t('userHeader.verification.nameLength'), trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: t('userHeader.verification.idCardRequired'), trigger: 'blur' },
    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: t('userHeader.verification.idCardFormat'), trigger: 'blur' }
  ],
  phone: [
    { required: true, message: t('userHeader.verification.phoneRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('userHeader.verification.phoneFormat'), trigger: 'blur' }
  ]
}
const verificationCodeRules = {
  verificationCode: [
    { required: true, message: t('userHeader.verification.codeRequired'), trigger: 'blur' },
    { pattern: /^\d{6}$/, message: t('userHeader.verification.codeFormat'), trigger: 'blur' }
  ]
}
const countdown = ref(0)
const sendingCode = ref(false)
const isVerified = ref(false)
const countdownTimer = ref(null)

// Breadcrumb navigation based on current route
const breadcrumbs = computed(() => {
  const path = route.path
  const segments = path.split('/').filter(segment => segment)
  
  const crumbs = []
  let currentPath = ''
  
  // Handle routes with dynamic segments or query parameters by checking route.name
  const routeName = route.name
  const routeMeta = route.meta || {}
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    let name = segment
    
    // Format the segment for display
    switch(segment) {
      case 'student':
      case 'teacher':
      case 'admin':
        // Skip role segment in breadcrumbs
        return // 使用return跳过此次循环，不添加到crumbs中
      case 'dashboard':
        name = isAdmin.value ? t('breadcrumb.adminDashboard') : (isTeacher.value ? t('breadcrumb.teacherDashboard') : t('breadcrumb.studentDashboard'))
        break
      case 'courses':
        name = t('breadcrumb.myCourses')
        break
      case 'course':
        name = t('breadcrumb.courseContent')
        break
      case 'assignments':
        name = t('breadcrumb.myAssignments')
        break
      case 'assignment-management':
        name = t('breadcrumb.assignmentManagement')
        break
      case 'course-management':
        name = t('breadcrumb.courseManagement')
        break
      case 'student-management':
        name = t('breadcrumb.studentManagement')
        break
      case 'class-list':
        name = t('breadcrumb.classManagement')
        break
      case 'grade-management':
        name = t('breadcrumb.gradeManagement')
        break
      case 'question-bank':
        name = t('breadcrumb.questionBank')
        break
      case 'textbook-projects':
        name = t('breadcrumb.textbookProjects')
        break
      case 'content-creation':
        name = t('breadcrumb.contentCreation')
        break
      case 'lesson-plan-projects':
        name = t('breadcrumb.lessonPlanProjects')
        break
      case 'content-ppt-projects':
        name = t('breadcrumb.contentPptProjects')
        break
      case 'content-script-projects':
        name = t('breadcrumb.contentScriptProjects')
        break
      case 'content-video-projects':
        name = t('breadcrumb.contentVideoProjects')
        break
      case 'video-course':
        name = t('breadcrumb.videoManagement')
        break
      case 'digital-teacher-management':
        name = t('breadcrumb.digitalTeacher')
        break
      case 'voice-management':
        name = t('breadcrumb.aiVoice')
        break
      case 'today-learn':
        name = t('breadcrumb.aiLecture')
        break
      case 'ai-learning':
        name = t('breadcrumb.aiLearning')
        break
      case 'bookshelf':
        name = t('breadcrumb.bookshelf')
        break
      case 'admin':
        name = t('breadcrumb.systemManagement')
        break
      case 'users':
        name = t('breadcrumb.userManagement')
        break
      case 'store':
        name = t('breadcrumb.storeManagement')
        break
      case 'points':
        name = t('breadcrumb.pointsManagement')
        break
      case 'notifications':
        name = t('breadcrumb.notificationManagement')
        break
      case 'audit':
        name = t('breadcrumb.auditLogs')
        break
      case 'knowledge-base':
        name = t('breadcrumb.knowledgeBase')
        break
      case 'digital-teacher':
        name = t('breadcrumb.digitalTeacher')
        break
      case 'personal-center':
        name = t('breadcrumb.personalCenter')
        break
      case 'profile':
        name = t('breadcrumb.profile')
        break
      case 'ai-assistant':
        name = t('breadcrumb.aiAssistant')
        break
      case 'points-mall':
        name = t('breadcrumb.pointsMall')
        break
      case 'settings':
        name = t('breadcrumb.settings')
        break
      case 'chapter':
        name = t('breadcrumb.chapter')
        break
      case 'questionbank':
        name = t('breadcrumb.questionBank')
        break
      case 'class':
        name = t('breadcrumb.class')
        break
      case 'homework':
        name = t('breadcrumb.homework')
        break
      case 'grades':
        name = t('breadcrumb.grades')
        break
      case 'dograde':
        name = t('breadcrumb.dograde')
        break
      case 'grading':
        name = t('breadcrumb.grading')
        break
      default:
        // 检查路由元数据中是否有自定义名称
        if (routeMeta.breadcrumbName) {
          name = routeMeta.breadcrumbName
        } else {
          // Capitalize first letter and replace hyphens with spaces
          name = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ')
        }
        // 判断是否为数字，如果是数字则不显示
        if (!isNaN(name)) {
          // 如果是纯数字（比如ID），跳过该路由片段，不添加到面包屑中
          return
        }
    }
    
    crumbs.push({ path: currentPath, name })
  })
  
  // Handle special dynamic routes with query parameters
  if (routeMeta.parentPath && routeMeta.breadcrumbName && crumbs.length > 0) {
    // Replace the last crumb name with the one from metadata
    crumbs[crumbs.length - 1].name = routeMeta.breadcrumbName
  }
  
  return crumbs
})

// Current date
const formattedDate = computed(() => {
  const now = new Date()
  const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' }
  return now.toLocaleDateString(locale.value, options)
})

// Format points with comma separator
const formattedPoints = computed(() => {
  return userData.value.points.toLocaleString()
})

// Determine points display class
const pointsClass = computed(() => {
  if (userData.value.points >= 2000) return 'text-purple-800'
  if (userData.value.points >= 1000) return 'text-blue-800'
  return 'text-green-800'
})

// Determine points background class
const pointsBgClass = computed(() => {
  if (userData.value.points >= 2000) return 'bg-purple-100 text-purple-800'
  if (userData.value.points >= 1000) return 'bg-blue-100 text-blue-800'
  return 'bg-green-100 text-green-800'
})

// Toggle user menu dropdown
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

// Toggle sidebar collapse (统一收缩行为)
const toggleMobileMenu = () => {
  emit('toggleCollapse')
}

// Close menu when clicking outside
const handleOutsideClick = (event) => {
  const menuButton = document.getElementById('user-menu-button')
  const menu = document.getElementById('user-menu')
  
  if (userMenuOpen.value && 
      menuButton && 
      menu && 
      !menuButton.contains(event.target) && 
      !menu.contains(event.target)) {
    userMenuOpen.value = false
  }
}

// Setup document click listener
onMounted(() => {
  document.addEventListener('click', handleOutsideClick)
})

// Cleanup listeners
onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick)
})

// 处理退出登录
const handleLogout = async () => {
  try {
    await authStore.logout()
    // 关闭用户菜单
    userMenuOpen.value = false
  } catch (error) {
    console.error(t('userHeader.logoutFailed'), error)
  }
}

// Verification logic
const goToNextStep = () => {
  verificationFormRef.value.validate((valid) => {
    if (valid) {
      activeStep.value = 2
    } else {
      ElMessage.error(t('userHeader.verification.fillRequiredFields'))
    }
  })
}

const handleCloseVerificationModal = () => {
  showVerificationModal.value = false
  activeStep.value = 1
  verificationForm.value = {
    name: '',
    idCard: '',
    phone: '',
    verificationCode: ''
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdown.value = 0
  }
}

const startCountdown = () => {
  countdown.value = 60
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value)
    }
  }, 1000)
}

const sendVerificationCode = async () => {
  if (countdown.value > 0) return
  
  sendingCode.value = true
  try {
    // 模拟发送验证码
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(t('userHeader.verification.codeSentSuccess', { phone: verificationForm.value.phone }))
    startCountdown()
    
    // 在实际应用中，这里应该调用API发送验证码
    console.log(t('userHeader.verification.sendCodeTo'), verificationForm.value.phone)
  } catch (error) {
    ElMessage.error(t('userHeader.verification.codeSendFailed'))
  } finally {
    sendingCode.value = false
  }
}

const verifyCode = () => {
  verificationCodeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 模拟验证过程
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // 假设验证成功
        isVerified.value = true
        activeStep.value = 3
        
        // 在实际应用中，这里应该调用API验证验证码
        console.log(`${t('userHeader.verification.verifying')}${isTeacher.value ? t('role.teacher') : t('role.student')}${t('userHeader.verification.threeElements')}:`, {
          name: verificationForm.value.name,
          idCard: verificationForm.value.idCard,
          phone: verificationForm.value.phone,
          code: verificationForm.value.verificationCode,
          role: isTeacher.value ? 'teacher' : 'student'
        })
      } catch (error) {
        ElMessage.error(t('userHeader.verification.verificationFailed'))
      }
    } else {
      ElMessage.error(t('userHeader.verification.enterCorrectCode'))
    }
  })
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
/* Add your custom styles here */
ol li {
  list-style: none;
}
</style> 