import request from '@/utils/request'

// 获取作业列表
export function getHomeworkList(courseId) {
  return request({
    url: '/homeworks/',
    method: 'get',
    params: { course_id: courseId }
  })
}

// 获取课程题库题目
export function getCourseQuestions(courseId) {
  return request({
    url: '/homeworks/course_questions/',
    method: 'get',
    params: { course_id: courseId }
  })
}

// 创建作业
export function createHomework(data) {
  return request({
    url: '/homeworks/',
    method: 'post',
    data
  })
}

// 更新作业
export function updateHomework(id, data) {
  return request({
    url: `/homeworks/${id}/`,
    method: 'put',
    data
  })
}

// 删除作业
export function deleteHomework(id) {
  return request({
    url: `/homeworks/${id}/`,
    method: 'delete'
  })
}

// 获取作业统计信息
export function getHomeworkStatistics(id) {
  return request({
    url: `/homeworks/${id}/statistics/`,
    method: 'get'
  })
}

// 获取课程作业统计信息
export function getCourseHomeworkStats(courseId) {
  return request({
    url: '/homeworks/course_stats/',
    method: 'get',
    params: { course_id: courseId }
  })
}

// 发布作业
export function publishHomework(id) {
  return request({
    url: `/homeworks/${id}/publish/`,
    method: 'post'
  })
}

// 撤回作业
export function unpublishHomework(id) {
  return request({
    url: `/homeworks/${id}/unpublish/`,
    method: 'post'
  })
}

/**
 * 获取学生作业列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getStudentAssignments(params) {
  return request({
    url: '/homeworks/student_assignments/',
    method: 'get',
    params
  })
}

/**
 * 获取学生作业统计数据
 * @returns {Promise}
 */
export function getStudentStats() {
  return request({
    url: '/homeworks/student_stats/',
    method: 'get'
  })
}

/**
 * 获取作业详情
 * @param {string|number} id 作业ID
 * @returns {Promise}
 */
export function getHomeworkDetail(id) {
  return request({
    url: `/homeworks/${id}/`,
    method: 'get'
  })
}

/**
 * 获取学生作业提交状态和答案
 * @param {string|number} id 作业ID
 * @returns {Promise}
 */
export function getStudentStatus(id) {
  return request({
    url: `/homeworks/${id}/student_status/`,
    method: 'get'
  })
}

/**
 * 提交作业
 * @param {string|number} id 作业ID
 * @param {Object} data 提交数据
 * @returns {Promise}
 */
export function submitHomework(data) {
  return request({
    url: `/submissions/submit/`,
    method: 'post',
    data
  })
}

/**
 * 获取作业批改信息
 * @param {string|number} courseId 课程ID
 * @param {string|number} homeworkId 作业ID
 * @returns {Promise}
 */
export function getHomeworkGradingInfo(courseId, homeworkId) {
  return request({
    url: `/homeworks/${homeworkId}/grading_info/`,
    method: 'get',
    params: { course_id: courseId }
  })
}

/**
 * 获取作业提交记录列表
 * @param {string|number} courseId 课程ID
 * @param {string|number} homeworkId 作业ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getHomeworkSubmissions(courseId, homeworkId, params) {
  return request({
    url: `/homeworks/${homeworkId}/submissions/`,
    method: 'get',
    params: { course_id: courseId, ...params }
  })
}

/**
 * 保存作业批改结果
 * @param {string|number} courseId 课程ID
 * @param {string|number} homeworkId 作业ID
 * @param {string|number} submissionId 提交ID
 * @param {Object} data 批改数据
 * @returns {Promise}
 */
export function saveGradingResult(courseId, homeworkId, submissionId, data) {
  return request({
    url: `/homeworks/${homeworkId}/grade/`,
    method: 'post',
    data: { course_id: courseId, submission_id:submissionId, ...data }
  })
}

/**
 * 批量保存作业批改结果
 * @param {string|number} courseId 课程ID
 * @param {string|number} homeworkId 作业ID
 * @param {Array} data 批量批改数据
 * @returns {Promise}
 */
export function saveAllGradingResults(courseId, homeworkId, data) {
  return request({
    url: `/homeworks/${homeworkId}/batch_grade/`,
    method: 'post',
    data: { course_id: courseId, submissions: data }
  })
}

/**
 * 发布作业成绩
 * @param {string|number} courseId 课程ID
 * @param {string|number} homeworkId 作业ID
 * @returns {Promise}
 */
export function publishHomeworkGrades(courseId, homeworkId) {
  return request({
    url: `/homeworks/${homeworkId}/publish_grades/`,
    method: 'post',
    data: { course_id: courseId }
  })
}

/**
 * 获取学生作业反馈
 * @param {string|number} homeworkId 作业ID
 * @returns {Promise}
 */
export function getStudentFeedback(homeworkId) {
  return request({
    url: `/homeworks/${homeworkId}/student_feedback/`,
    method: 'get'
  })
}

// 获取作业的成绩列表
export function getHomeworkGrades(homeworkId) {
  return request({
    url: `/homeworks/${homeworkId}/homework_grades/`,
    method: 'get'
  })
}