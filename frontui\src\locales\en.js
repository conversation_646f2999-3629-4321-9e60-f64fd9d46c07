export default {
  system: {
    title: 'Smart Classroom',
    slogan: 'Knowledge changes destiny, wisdom illuminates the future',
    copyright: '© 2025 Smart Classroom. All rights reserved.'
  },
  login: {
    title: 'User Login',
    username: 'Please enter username',
    password: 'Please enter password',
    remember: 'Remember password',
    forgot: 'Forgot password?',
    button: 'Login',
    otherMethods: 'Other login methods',
    noAccount: 'No account yet?',
    register: 'Register now'
  },
  role: {
    student: 'Student',
    teacher: 'Teacher',
    admin: 'Administrator'
  },
  errors: {
    loginFailed: 'Login failed, please try again!',
    wrongCredentials: 'Incorrect username or password, please try again!',
    roleError: 'User role error, please try again!',
    unknown: 'Unknown error, please try again later!',
    loadCoursesListFailed: 'Failed to load courses list'
  },
  dashboard: {
    student: {
      title: 'Student Dashboard',
      welcome: 'Welcome back, {name}',
      today: 'Today is {date}',
      learningStatus: {
        continuousLearning: '{days} days learning streak',
        weeklyLearning: '{hours} hours this week'
      },
      learningGoal: {
        title: 'Learning Goal',
        remainingCourses: 'Need to complete {count} more courses to reach next level'
      },
      stats: {
        courseCompletion: {
          title: 'Course Completion',
          totalCourses: 'Completed {completed} of {total} courses',
          monthlyChange: '↑{percent}% from last month'
        },
        homeworkCompletion: {
          title: 'Assignment Completion',
          totalHomeworks: 'Completed {completed} of {total} assignments',
          averageScore: 'Average score: {score}'
        },
        learningHours: {
          title: 'Learning Hours',
          hours: '{hours} hours',
          increase: '↑ {hours} hours',
          weekday: {
            monday: 'Mon',
            sunday: 'Sun'
          }
        },
        points: {
          title: 'Points & Achievements',
          points: '{points} points',
          increase: '↑ {points}',
          medalLevel: {
            bronze: 'Bronze Student',
            silver: 'Silver Student',
            gold: 'Gold Student'
          },
          nextLevel: '{points} points to {level}'
        }
      },
      recommendations: {
        title: 'Personalized Learning Recommendations',
        efficiency: 'Learning Efficiency',
        reinforcement: 'Knowledge Reinforcement',
        habit: 'Study Habits'
      }
    }
  },
  courses: {
    title: 'My Courses',
    joinClass: 'Join Class',
    points: 'Points',
    
    courseStats: {
      total: 'All Courses',
      completed: 'Completed',
      inProgress: 'In Progress',
      weeklyHours: 'Weekly Study'
    },
    
    joinClassModal: {
      title: 'Join Class',
      enterCode: 'Please enter class invitation code',
      codePlaceholder: 'Enter 6-digit invitation code',
      joining: 'Joining...',
      confirm: 'Confirm Join',
      success: 'Successfully joined the class',
      failed: 'Failed to join class',
      failedWithError: 'Failed to join class: {error}'
    },
    
    coursesList: {
      title: 'All Courses',
      teacher: 'Instructor',
      hours: 'Hours',
      students: '{count} students enrolled',
      continueLearning: 'Continue Learning →',
      review: 'Review →'
    },
    
    pagination: {
      previous: 'Previous',
      next: 'Next'
    },
    
    notes: {
      button: 'Notes',
      title: '{course} - Notes',
      listTitle: 'Notes List',
      count: '{count} notes',
      searchPlaceholder: 'Search notes...',
      loading: 'Loading notes list...',
      loadingContent: 'Loading note content...',
      empty: 'No notes available',
      untitled: 'Untitled Note',
      createdAt: 'Created at',
      loadFailed: 'Failed to load notes list',
      loadDetailFailed: 'Failed to load note details'
    }
  },
  
  courseContent: {
    videoPlayerNotSupported: 'Your browser does not support HTML5 video player',
    
    tabs: {
      overview: 'Course Overview',
      discussion: 'Discussion'
    },
    
    controls: {
      notes: 'Notes',
      navigation: 'Navigation',
      rating: 'Rating'
    },
    
    stats: {
      totalHours: 'Total Hours',
      hour: 'hours',
      studentsCount: 'Students',
      rating: 'Rating',
      ratingCount: 'ratings'
    },
    
    comments: {
      title: 'Discussion',
      placeholder: 'Share your thoughts...',
      submit: 'Post Comment',
      reply: 'Reply',
      replyPlaceholder: 'Reply...',
      submitReply: 'Post Reply',
      instructor: 'Instructor'
    },
    
    sidebar: {
      progress: 'Progress',
      lessons: 'Lessons',
      lessonsUnit: 'lessons'
    },
    
    aiCompanion: {
      title: 'AI Learning Assistant',
      subtitle: 'Here to answer your questions',
      inputPlaceholder: 'Type your question here...',
      selectCompanion: 'Choose Companion',
      close: 'Close'
    },
    
    aiCompanionDialog: {
      title: 'Choose Your Learning Partner',
      requiredRoles: 'Required Roles',
      requiredRolesNote: '(Teacher Wang is selected automatically)',
      companionRoles: 'Companion Roles',
      optionalRolesNote: '(Free to choose)',
      confirm: 'Confirm Selection',
      roles: {
        teacher: {
          name: 'Teacher Wang',
          desc: 'Teaching support assistant'
        },
        funnyKid: {
          name: 'Xiao Ming (Class Jester)',
          desc: 'Lively and humorous, enjoys jokes'
        },
        thinker: {
          name: 'Li Hua (Deep Thinker)',
          desc: 'Thoughtful and analytical'
        },
        curious: {
          name: 'Zhang Ying (Curious Mind)',
          desc: 'Asks questions, highlights concepts'
        },
        topStudent: {
          name: 'Zhao Yang (Top Student)',
          desc: 'Organizes key points concisely'
        }
      }
    },
    
    noteModal: {
      title: 'Course Notes',
      search: 'Search notes...',
      new: 'New Note',
      titlePlaceholder: 'Note Title',
      contentPlaceholder: 'Enter note content here...',
      timePoint: 'Timestamp',
      save: 'Save Note',
      cancel: 'Cancel',
      delete: 'Delete Note',
      markdownSupport: 'Markdown supported',
      charCount: '{count} characters',
      confirmDelete: 'Are you sure you want to delete this note?',
      // Notification messages
      titleRequired: 'Please enter note title',
      updateSuccess: 'Note updated successfully',
      createSuccess: 'Note created successfully',
      saveFailed: 'Failed to save note',
      loadFailed: 'Failed to load notes',
      deleteSuccess: 'Note deleted successfully',
      deleteFailed: 'Failed to delete note',
      courseInfoError: 'Unable to get course information'
    },
    
    videoNavModal: {
      title: 'AI Video Navigation',
      summary: 'Video Content Summary',
      noSummary: 'No video summary available',
      keyPoints: 'Key Points Navigation',
      noKeyPoints: 'No key points data available'
    },
    
    ratingDialog: {
      title: 'Course Rating',
      submit: 'Submit Rating',
      cancel: 'Cancel'
    }
  },
  
  courseOverview: {
    keyPoints: 'Key Points',
    importantPoints: 'Important Points',
    notes: 'Notes',
    noContent: 'No content available',
    edit: 'Edit Course Overview',
    enterKeyPoints: 'Enter key points',
    enterImportantPoints: 'Enter important points',
    enterNotes: 'Enter notes',
    updateSuccess: 'Course overview updated',
    saveFailed: 'Save failed'
  },
  general: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    confirmation: 'Confirmation',
    error: 'An error occurred',
    uploadFailed: 'Upload failed',
    pleaseWait: 'Please wait...',
    defaultStudentName: 'Student',
    home: 'Home',
    points: 'Points',
    save: 'Save'
  },
  
  userHeader: {
    profile: 'Profile',
    settings: 'Account Settings',
    activate: 'Activate Account',
    logout: 'Sign Out',
    logoutFailed: 'Sign out failed',
    verification: {
      title: 'Account Activation',
      alertTitle: 'Please Complete Verification',
      successTitle: 'Verification Completed',
      teacherDescription: 'As a teacher, activation enables more features. Please enter your real information for verification.',
      studentDescription: 'Account activation enables more features. Please enter your real information for verification.',
      teacherSuccess: 'Your teacher account has been verified and activated',
      studentSuccess: 'Your account has been verified and activated',
      step1: 'Enter Information',
      step2: 'Verify Phone',
      step3: 'Complete',
      name: 'Name',
      idCard: 'ID Card',
      phone: 'Phone',
      code: 'Code',
      namePlaceholder: 'Enter your real name',
      idCardPlaceholder: 'Enter your ID card number',
      phonePlaceholder: 'Enter your phone number',
      codePlaceholder: 'Enter verification code',
      nameRequired: 'Please enter your real name',
      nameLength: 'Name must be 2-20 characters',
      idCardRequired: 'Please enter your ID card number',
      idCardFormat: 'Please enter a valid ID card number',
      phoneRequired: 'Please enter your phone number',
      phoneFormat: 'Please enter a valid phone number',
      codeRequired: 'Please enter the verification code',
      codeFormat: 'Code must be 6 digits',
      next: 'Next',
      previous: 'Previous',
      modify: 'Edit',
      getCode: 'Get Code',
      resend: 'Resend in {seconds}s',
      verify: 'Verify',
      complete: 'Complete',
      codeSentSuccess: 'Code sent to {phone}',
      codeSendFailed: 'Failed to send code, please try again later',
      verificationFailed: 'Verification failed, please check your information',
      enterCorrectCode: 'Please enter the correct code',
      fillRequiredFields: 'Please fill in all required fields correctly',
      verifying: 'Verifying ',
      threeElements: ' information and code',
      sendCodeTo: 'Sending code to'
    }
  },

  breadcrumb: {
    adminDashboard: 'Dashboard',
    teacherDashboard: 'Teacher Dashboard',
    studentDashboard: 'Student Dashboard',
    myCourses: 'My Courses',
    courseContent: 'Course Content',
    myAssignments: 'My Assignments',
    assignmentManagement: 'Assignment Management',
    courseManagement: 'Course Management',
    studentManagement: 'Student Management',
    classManagement: 'Class Management',
    gradeManagement: 'Grade Management',
    questionBank: 'Question Bank',
    textbookProjects: 'Textbook Projects',
    contentCreation: 'Tools',
    lessonPlanProjects: 'Lesson Plans',
    contentPptProjects: 'PPT Creation',
    contentScriptProjects: 'Script Content',
    contentVideoProjects: 'Video Synthesis',
    videoManagement: 'Video Management',
    digitalTeacher: 'Digital Teacher',
    aiVoice: 'AI Voice',
    aiLecture: 'AI Lecture',
    aiLearning: 'AI Learning',
    bookshelf: 'Bookshelf',
    systemManagement: 'System Management',
    userManagement: 'User Management',
    storeManagement: 'Store Management',
    pointsManagement: 'Points Management',
    notificationManagement: 'Notification Management',
    auditLogs: 'Audit Logs',
    knowledgeBase: 'Knowledge Base',
    personalCenter: 'Personal Center',
    profile: 'Profile',
    aiAssistant: 'AI Assistant',
    pointsMall: 'Points Mall',
    settings: 'Settings',
    chapter: 'Chapter',
    class: 'Class',
    homework: 'Homework',
    grades: 'Grades',
    dograde: 'Grade Assignments',
    grading: 'Grading Details'
  },
  
  sidebar: {
    home: 'Home',
    aiLecture: 'AI Lecture',
    bookshelf: 'Bookshelf',
    courses: 'My Courses',
    assignments: 'My Assignments',
    knowledgeBase: 'Knowledge Base',
    aiAssistant: 'AI Assistant',
    pointsMall: 'Points Mall',
    personalCenter: 'Personal Center',
    collapse: 'Collapse Menu'
  },
  
  todayLearn: {
    title: 'AI Lecture',
    upload: {
      area: 'Drag files here, or',
      button: 'Click to upload',
      supportTypes: 'Supported file types: .doc, .docx, .txt, .pdf',
      dropHint: 'Release to upload file',
      uploading: 'Uploading...'
    },
    styleDialog: {
      title: 'Choose Lecture Style',
      description: 'Please select a lecture style for your uploaded document:',
      cancel: 'Cancel Upload',
      confirm: 'Confirm and Upload'
    }
  },
  
  bookshelf: {
    title: 'Bookshelf',
    stats: '{count} Documents',
    slogan: 'Reinvent your reading experience',
    search: 'Search documents...',
    generating: '{count} documents generating',
    batchOperation: 'Batch Operations',
    exitBatch: 'Exit Batch',
    selectAll: 'Select All',
    cancelSelect: 'Cancel Selection',
    delete: 'Delete',
    outlineCompleted: 'Outline generation completed',
    processingTime: 'Est. 10-15 minutes',
    document: 'Document',
    
    addDocument: {
      title: 'Add Document',
      dropHint: 'Drop files here',
      supportDrag: 'Supports drag & drop'
    },
    
    upload: {
      uploading: 'Uploading...',
      deleting: 'Deleting...',
      completed: 'Completed',
      pleaseWait: 'Please wait...',
      progress: 'Completed {completed} / {total}'
    },
    
    documentCard: {
      editName: 'Edit Name',
      delete: 'Delete',
      moreActions: 'More Actions'
    },
    
    editNameDialog: {
      title: 'Edit Document Name',
      placeholder: 'Enter document name',
      confirm: 'Confirm',
      cancel: 'Cancel'
    },
    
    styleDialog: {
      title: 'Choose Lecture Style',
      description: 'Please select the style you want AI to use for this document',
      selectStyle: 'Select lecture style:',
      confirm: 'Confirm',
      cancel: 'Cancel',
      cancelUpload: 'Cancel Upload',
      confirmAndUpload: 'Confirm & Upload'
    },
    
    preview: {
      generating: 'Outline is being generated, please try again later',
      failed: 'Failed to load preview'
    },
    
    notifications: {
      nameUpdated: 'Document name updated',
      deleteConfirm: 'Are you sure you want to delete {name}?',
      deleteFailed: 'Delete failed',
      fileTypeError: 'Unsupported file type',
      uploadSuccess: '{name} uploaded successfully',
      generatingOutline: 'Generating outline, this may take a few minutes',
      selectDocumentsToDelete: 'Please select documents to delete',
      batchDeleteConfirm: 'Are you sure you want to delete {count} selected documents?',
      batchDeleteSuccess: 'Successfully deleted {count} documents',
      batchDeleteFailed: 'Batch deletion failed',
      confirmDelete: 'Are you sure you want to delete {name}?',
      deleteConfirmTitle: 'Confirm Delete',
      confirmButtonText: 'Confirm',
      cancelButtonText: 'Cancel',
      deleteSuccess: 'Delete successful',
      fileTypesHint: 'Only PDF and DOCX files are supported',
      confirmBatchDelete: 'Are you sure you want to delete {count} selected documents?',
      batchDeleteConfirmTitle: 'Confirm Batch Delete'
    }
  },
  
  htmlPreview: {
    actions: {
      chapterList: 'Chapter List',
      switchStyle: 'Switch Style',
      back: 'Back to Bookshelf'
    },
    chapterDialog: {
      title: 'Chapter List',
      noChapters: 'No chapters available',
      page: 'p. {start}-{end}'
    },
    styleDialog: {
      title: 'Choose Lecture Style',
      currentStyle: 'Current Style:',
      loading: 'Loading style list...',
      cancel: 'Cancel',
      confirm: 'Confirm Change',
      switching: 'Switching...'
    },
    status: {
      loading: 'Loading...',
      generating: 'Generating chapter content...',
      pointsGenerated: 'Generated {count} key points',
      completed: 'Generation complete!',
      notGenerated: 'Chapter content not yet generated',
      startGenerate: 'Start Generating',
      loadFailed: 'Failed to load'
    },
    controls: {
      play: 'Play',
      pause: 'Pause',
      noAudio: 'No Audio',
      generating: 'Generating audio...',
      prev: 'Previous',
      next: 'Next',
      page: 'Page {current} / {total}',
      generating: 'Content generating...'
    },
    notifications: {
      styleChanged: 'Style changed successfully',
      styleChangedDetail: 'Switched to {style}, page will refresh, audio will be generated in background',
      styleChangeFailed: 'Style change failed',
      retryLater: 'Please try again later',
      audioUpdated: 'Audio updated',
      audioUpdateDetail: 'Audio for the new style has been generated',
      generationStarted: 'Generation started',
      generationDetail: 'Chapter content generation has started, please wait...',
      generationFailed: 'Generation failed',
      triggerFailed: 'Failed to trigger chapter generation',
      autoPaging: 'Auto-paging',
      pagingMessage: 'Turning to page {page} and continuing playback...',
      playCompleted: 'Playback complete',
      playCompletedDetail: 'All content has been played',
      firstPointGenerated: 'First key point generated',
      firstPointDetail: 'Preview now available, more content will be generated in the background',
      generationCompleted: 'Generation complete',
      allContentGenerated: 'All chapter content has been generated',
      outlineCompleted: '{names} outline generation completed!'
    },
    status: {
      notGenerated: 'Not generated',
      generating: 'Generating',
      completed: 'Completed'
    }
  }
} 