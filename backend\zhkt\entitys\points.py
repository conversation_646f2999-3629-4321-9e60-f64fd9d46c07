from django.db import models
from django.utils.translation import gettext_lazy as _

class PointsRecord(models.Model):
    """积分记录模型"""
    RECORD_TYPE_CHOICES = (
        ('EARN', '获得'),
        ('SPEND', '消费'),
        ('ADJUST', '调整'),
    )
    
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='points_records')
    points = models.IntegerField(_('积分数量'))
    record_type = models.CharField(_('记录类型'), max_length=20, choices=RECORD_TYPE_CHOICES)
    description = models.TextField(_('记录描述'))
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('积分记录')
        verbose_name_plural = _('积分记录')

    def __str__(self):
        return f"{self.student.user.username} - {self.record_type} - {self.points}"

class Product(models.Model):
    """商城商品模型"""
    CATEGORY_CHOICES = (
        ('physical', '实物商品'),
        ('virtual', '虚拟商品'),
        ('learning', '学习资源'),
    )

    name = models.CharField(_('商品名称'), max_length=200)
    description = models.TextField(_('商品描述'))
    points_price = models.IntegerField(_('积分价格'))
    stock = models.IntegerField(_('库存数量'))
    image = models.CharField(_('商品图片'), max_length=500, blank=True)
    category = models.CharField(_('商品类别'), max_length=20, choices=CATEGORY_CHOICES, default='physical')
    is_active = models.BooleanField(_('是否上架'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('商品')
        verbose_name_plural = _('商品')

    def __str__(self):
        return self.name

class Order(models.Model):
    """订单模型"""
    STATUS_CHOICES = (
        ('PENDING', '待处理'),
        ('COMPLETED', '已完成'),
        ('CANCELLED', '已取消'),
    )
    
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='orders')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='orders')
    points_cost = models.IntegerField(_('消耗积分'))
    status = models.CharField(_('订单状态'), max_length=20, choices=STATUS_CHOICES, default='PENDING')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('订单')
        verbose_name_plural = _('订单')

    def __str__(self):
        return f"{self.student.user.username} - {self.product.name}" 