<template>
  <TeacherLayout 
    pageTitle="账号设置" 
    :userName="teacherStore.teacherData.name"
    :userAvatar="teacherStore.teacherData.avatar"
    activePage="settings">
    
    <div class="space-y-4">
      <!-- 压缩版个人资料卡片 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-4">
          <div class="flex items-start">
            <!-- 头像 -->
            <el-avatar 
              :size="70" 
              :src="teacherStore.teacherData.avatar"
              class="border-2 border-white shadow-md mr-4" />
            
            <!-- 基本信息 -->
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h2 class="text-xl font-semibold text-gray-800">{{ teacherStore.teacherData.name }}</h2>
              </div>
              
              <!-- 教师信息色块 -->
              <div class="flex flex-wrap gap-2 mb-2">
                <span class="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">工号：{{ teacherStore.teacherData.teacherId }}</span>
                <span class="px-3 py-1.5 bg-green-100 text-green-800 rounded-md text-sm font-medium">{{ academicInfo.department }}</span>
                <span class="px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium">{{ academicInfo.title }}</span>
              </div>
            </div>
          </div>

          <!-- 数据分析指标 - 水平布局 -->
          <div class="flex mt-3 pt-3 border-t gap-3">
            <!-- 教学时长 -->
            <div class="flex-1">
              <div class="bg-blue-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-blue-800 flex items-center mb-1">
                  <el-icon class="text-blue-600 mr-2"><Timer /></el-icon>
                  教学时长
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-blue-900 mb-1">
                    {{ teacherAnalytics.totalTeachingHours }}小时
                  </div>
                  <div class="text-sm text-blue-700 bg-blue-100 rounded-full px-3 py-0.5 inline-block">
                    近30天: {{ teacherAnalytics.last30DaysHours }}小时
                  </div>
                </div>
              </div>
            </div>

            <!-- 课程情况 -->
            <div class="flex-1">
              <div class="bg-green-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-green-800 flex items-center mb-1">
                  <el-icon class="text-green-600 mr-2"><Notebook /></el-icon>
                  课程情况
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-green-900 mb-1">
                    {{ teacherAnalytics.activeCourses }}门
                  </div>
                  <div class="text-sm text-green-700 bg-green-100 rounded-full px-3 py-0.5 inline-block">
                    累计: {{ teacherAnalytics.totalCourses }}门
                  </div>
                </div>
              </div>
            </div>

            <!-- 学生情况 -->
            <div class="flex-1">
              <div class="bg-purple-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-purple-800 flex items-center mb-1">
                  <el-icon class="text-purple-600 mr-2"><User /></el-icon>
                  学生情况
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-purple-900 mb-1">
                    {{ teacherAnalytics.currentStudents }}
                  </div>
                  <div class="text-sm text-purple-700 bg-purple-100 rounded-full px-3 py-0.5 inline-block">
                    累计: {{ teacherAnalytics.totalStudents }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人信息管理选项卡 -->
      <el-card shadow="hover">
        <el-tabs v-model="activeTab" tab-position="left" style="min-height: 500px">
          <!-- 账号安全管理 -->
          <el-tab-pane label="账号安全" name="security">
            <h3 class="text-xl font-medium text-gray-800 mb-4">账号安全</h3>
            <el-collapse accordion>
              <!-- 密码修改 -->
              <el-collapse-item name="password">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Lock /></el-icon>
                    <span>密码修改</span>
                  </div>
                </template>
                <el-form 
                  ref="passwordForm" 
                  :model="passwordData" 
                  :rules="passwordRules" 
                  label-width="120px">
                  <el-form-item label="当前密码" prop="currentPassword">
                    <el-input v-model="passwordData.currentPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item label="新密码" prop="newPassword">
                    <el-input v-model="passwordData.newPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item label="确认新密码" prop="confirmPassword">
                    <el-input v-model="passwordData.confirmPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="changePassword">修改密码</el-button>
                    <el-button @click="resetPasswordForm">取消</el-button>
                  </el-form-item>
                </el-form>
              </el-collapse-item>

              <!-- 多因素认证 -->
              <el-collapse-item name="mfa">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Key /></el-icon>
                    <span>多因素认证</span>
                  </div>
                </template>
                <div class="p-4">
                  <p class="mb-4">多因素认证可以提高账号安全性，防止未授权访问。</p>
                  <el-switch
                    v-model="securitySettings.mfaEnabled"
                    active-text="已启用"
                    inactive-text="未启用"
                    @change="toggleMFA"
                  />
                  <div v-if="securitySettings.mfaEnabled" class="mt-4 bg-gray-50 p-4 rounded">
                    <p class="mb-2 font-medium">已绑定设备：</p>
                    <el-tag class="mr-2 mb-2">手机验证器</el-tag>
                    <p class="text-sm text-gray-600 mb-2">上次验证时间：2023-09-15 14:30</p>
                    <el-button size="small" type="primary">重新配置</el-button>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 账号绑定 -->
              <el-collapse-item name="binding">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Link /></el-icon>
                    <span>安全验证绑定</span>
                  </div>
                </template>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">手机绑定</h4>
                      <p class="text-sm text-gray-600">当前绑定：{{ securitySettings.phone }}</p>
                    </div>
                    <el-button type="primary" plain size="small">修改</el-button>
                  </div>
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">邮箱绑定</h4>
                      <p class="text-sm text-gray-600">当前绑定：{{ securitySettings.email }}</p>
                    </div>
                    <el-button type="primary" plain size="small">修改</el-button>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-tab-pane>

          <!-- 社交账号绑定 -->
          <el-tab-pane label="社交账号绑定" name="social">
            <h3 class="text-xl font-medium text-gray-800 mb-4">社交账号绑定</h3>
            <el-card shadow="never" class="mb-4">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="text-green-600 text-xl mr-2"><ChatDotSquare /></el-icon>
                  <span>微信账号</span>
                </div>
              </template>
              <div class="flex justify-between items-center">
                <div>
                  <p v-if="socialAccounts.wechat.bound" class="text-gray-600">
                    已绑定账号：{{ socialAccounts.wechat.nickname }}
                  </p>
                  <p v-else class="text-gray-600">未绑定微信账号</p>
                </div>
                <el-button type="success" plain v-if="!socialAccounts.wechat.bound">绑定</el-button>
                <el-button type="danger" plain v-else>解绑</el-button>
              </div>
            </el-card>

            <el-card shadow="never" class="mb-4">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="text-blue-600 text-xl mr-2"><ChatDotRound /></el-icon>
                  <span>QQ账号</span>
                </div>
              </template>
              <div class="flex justify-between items-center">
                <div>
                  <p v-if="socialAccounts.qq.bound" class="text-gray-600">
                    已绑定账号：{{ socialAccounts.qq.nickname }}
                  </p>
                  <p v-else class="text-gray-600">未绑定QQ账号</p>
                </div>
                <el-button type="primary" plain v-if="!socialAccounts.qq.bound">绑定</el-button>
                <el-button type="danger" plain v-else>解绑</el-button>
              </div>
            </el-card>
          </el-tab-pane>

          <!-- 通知设置 -->
          <el-tab-pane label="通知设置" name="notifications">
            <h3 class="text-xl font-medium text-gray-800 mb-4">通知设置</h3>
            <div class="space-y-6">
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-lg mb-3">系统通知</h4>
                <el-form>
                  <el-form-item label="课程通知">
                    <el-switch v-model="notificationSettings.course" />
                    <span class="ml-2 text-sm text-gray-600">接收课程相关更新、变更等通知</span>
                  </el-form-item>
                  <el-form-item label="教学通知">
                    <el-switch v-model="notificationSettings.teaching" />
                    <span class="ml-2 text-sm text-gray-600">接收教学安排、会议等通知</span>
                  </el-form-item>
                  <el-form-item label="学生互动">
                    <el-switch v-model="notificationSettings.student" />
                    <span class="ml-2 text-sm text-gray-600">接收学生提问、作业提交等通知</span>
                  </el-form-item>
                  <el-form-item label="系统更新">
                    <el-switch v-model="notificationSettings.system" />
                    <span class="ml-2 text-sm text-gray-600">接收平台更新、维护等通知</span>
                  </el-form-item>
                  <el-form-item label="账号安全">
                    <el-switch v-model="notificationSettings.security" disabled />
                    <span class="ml-2 text-sm text-gray-600">接收账号安全相关通知（不可关闭）</span>
                  </el-form-item>
                </el-form>
              </div>

              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-lg mb-3">通知方式</h4>
                <el-form>
                  <el-form-item label="站内通知">
                    <el-switch v-model="notificationMethods.inApp" disabled />
                    <span class="ml-2 text-sm text-gray-600">接收平台内通知（不可关闭）</span>
                  </el-form-item>
                  <el-form-item label="邮件通知">
                    <el-switch v-model="notificationMethods.email" />
                    <span class="ml-2 text-sm text-gray-600">通过邮件接收通知</span>
                  </el-form-item>
                  <el-form-item label="短信通知">
                    <el-switch v-model="notificationMethods.sms" />
                    <span class="ml-2 text-sm text-gray-600">通过短信接收重要通知</span>
                  </el-form-item>
                </el-form>
              </div>

              <el-button type="primary" @click="saveNotificationSettings">保存通知设置</el-button>
            </div>
          </el-tab-pane>

          <!-- 账号管理 -->
          <el-tab-pane label="账号管理" name="account">
            <h3 class="text-xl font-medium text-gray-800 mb-4">账号管理</h3>
            
            <!-- 头像设置 -->
            <div class="bg-gray-50 p-4 rounded-lg mb-4">
              <h4 class="font-medium text-lg mb-3">头像设置</h4>
              <div class="flex items-center space-x-4">
                <el-avatar 
                  :size="100" 
                  :src="teacherStore.teacherData.avatar" 
                  class="border-2 border-white shadow-md" />
                <div>
                  <el-upload
                    action="/api/upload"
                    :show-file-list="false"
                    :before-upload="beforeAvatarUpload"
                    :on-success="handleAvatarSuccess"
                  >
                    <el-button type="primary">更换头像</el-button>
                  </el-upload>
                  <p class="text-sm text-gray-600 mt-2">支持 JPG, PNG 格式，文件大小不超过 2MB</p>
                </div>
              </div>
            </div>

            <!-- 账号注销 -->
            <div class="bg-red-50 p-4 rounded-lg mb-4">
              <h4 class="font-medium text-lg mb-3 text-red-800">账号注销</h4>
              <p class="text-red-600 text-sm mb-3">
                注销账号将永久删除您的所有个人数据，此操作不可逆，请谨慎操作！
              </p>
              <el-button type="danger" @click="showDeactivateDialog = true">申请注销账号</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 账号注销确认对话框 -->
    <el-dialog
      v-model="showDeactivateDialog"
      title="账号注销确认"
      width="500px"
    >
      <div class="text-center">
        <el-icon class="text-red-500 text-5xl"><WarningFilled /></el-icon>
        <h3 class="text-xl font-bold mt-4">确认要注销您的账号吗？</h3>
        <p class="text-gray-600 mt-2">注销后，您的所有个人数据将被永久删除，且无法恢复。</p>
      </div>
      <div class="mt-4">
        <el-checkbox v-model="deactivateConfirm">
          我已知晓账号注销的所有后果，并确认注销账号
        </el-checkbox>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDeactivateDialog = false">取消</el-button>
          <el-button
            type="danger"
            :disabled="!deactivateConfirm"
            @click="deactivateAccount"
          >
            确认注销
          </el-button>
        </span>
      </template>
    </el-dialog>
  </TeacherLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useTeacherStore } from '@/stores/teacher'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Timer,
  Notebook,
  User,
  Lock,
  Key,
  Link,
  ChatDotSquare,
  ChatDotRound,
  WarningFilled
} from '@element-plus/icons-vue'

// 引入教师数据存储
const teacherStore = useTeacherStore()

// 当前活动选项卡
const activeTab = ref('security')

// 教师基本信息
const academicInfo = reactive({
  department: teacherStore.teacherData.department,
  title: teacherStore.teacherData.title,
  specialization: '人工智能',
  hireDate: teacherStore.teacherData.joinDate,
  teacherType: '专任教师',
  status: '在职'
})

// 教师统计数据
const teacherAnalytics = reactive({
  totalTeachingHours: 1420,
  last30DaysHours: 48,
  activeCourses: 3,
  totalCourses: 15,
  currentStudents: 126,
  totalStudents: 568
})

// 密码修改表单
const passwordForm = ref(null)
const passwordData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能小于8个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordData.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 安全设置
const securitySettings = reactive({
  mfaEnabled: false,
  phone: teacherStore.teacherData.phone,
  email: teacherStore.teacherData.email
})

// 社交账号绑定
const socialAccounts = reactive({
  wechat: {
    bound: true,
    nickname: teacherStore.teacherData.name
  },
  qq: {
    bound: false,
    nickname: ''
  }
})

// 通知设置
const notificationSettings = reactive({
  course: true,
  teaching: true,
  student: true,
  system: false,
  security: true
})

// 通知方式
const notificationMethods = reactive({
  inApp: true,
  email: true,
  sms: false
})

// 账号注销对话框
const showDeactivateDialog = ref(false)
const deactivateConfirm = ref(false)

// 修改密码
const changePassword = () => {
  passwordForm.value.validate((valid) => {
    if (valid) {
      // 模拟API调用
      setTimeout(() => {
        ElMessage.success('密码修改成功，请使用新密码重新登录')
        resetPasswordForm()
      }, 1000)
    } else {
      return false
    }
  })
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.value.resetFields()
}

// 切换多因素验证
const toggleMFA = (value) => {
  if (value) {
    ElMessage.success('已启用多因素认证')
  } else {
    ElMessageBox.confirm(
      '关闭多因素认证将降低您账号的安全性，确定要关闭吗？',
      '安全提示',
      {
        confirmButtonText: '确认关闭',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(() => {
        ElMessage.warning('已关闭多因素认证')
      })
      .catch(() => {
        securitySettings.mfaEnabled = true
      })
  }
}

// 保存通知设置
const saveNotificationSettings = () => {
  // 模拟API调用
  setTimeout(() => {
    ElMessage.success('通知设置保存成功')
  }, 1000)
}

// 头像上传前校验
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG 或 PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 头像上传成功回调
const handleAvatarSuccess = (res, file) => {
  // 实际项目中，这里应该使用返回的URL更新头像
  ElMessage.success('头像上传成功')
}

// 注销账号
const deactivateAccount = () => {
  // 模拟API调用
  setTimeout(() => {
    showDeactivateDialog.value = false
    deactivateConfirm.value = false
    ElMessage.success('账号注销申请已提交，将在3个工作日内处理')
  }, 1000)
}

// 组件挂载时
onMounted(() => {
  // 可以在这里从API获取用户设置信息
  console.log('TeacherSettings component mounted')
})
</script>

<style scoped>
/* 自定义样式 */
</style> 