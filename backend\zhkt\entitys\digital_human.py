from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class DigitalHuman(models.Model):
    """数字人模型（合并数字人和化身功能）"""
    
    STATUS_CHOICES = [
        ('active', '已激活'),
        ('inactive', '未激活'),
        ('processing', '处理中'),
    ]
    
    TYPE_CHOICES = [
        ('real', '真人'),
        ('system', '系统预设'),
    ]
    
    # 基本信息
    name = models.CharField(_('数字人名称'), max_length=100)
    description = models.TextField(_('数字人描述'), null=True, blank=True)
    avatar_url = models.CharField(_('头像图像路径'), max_length=255, null=True, blank=True)
    video_url = models.CharField(_('训练视频路径'), max_length=255, null=True, blank=True)
    status = models.Char<PERSON>ield(_('状态'), max_length=20, choices=STATUS_CHOICES, default='inactive')
    type = models.CharField(_('类型'), max_length=20, choices=TYPE_CHOICES, default='real')
    duration = models.IntegerField(_('视频时长(秒)'), default=0)
    usage_count = models.IntegerField(_('使用次数'), default=0)
    category = models.CharField(_('预设分类'), max_length=50, null=True, blank=True, 
                               help_text=_('仅系统预设使用'))
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)
    
    # 关联用户ID（系统预设可以为空）
    user_id = models.IntegerField(_('用户ID'), null=True, blank=True)
    
    class Meta:
        db_table = 'zhkt_digital_human'
        verbose_name = _('数字人')
        verbose_name_plural = _('数字人')
        ordering = ['-created_at']
        
    def __str__(self):
        return self.name
        
    @property
    def is_system_preset(self):
        """判断是否为系统预设"""
        return self.type == 'system'
        
    @property
    def avatar_file_url(self):
        """获取头像文件完整URL"""
        if self.avatar_url:
            # 这里可以根据需要添加域名前缀
            return self.avatar_url
        return None
        
    @property
    def video_file_url(self):
        """获取视频文件完整URL"""
        if self.video_url:
            # 这里可以根据需要添加域名前缀
            return self.video_url
        return None 