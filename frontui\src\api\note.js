import request from '@/utils/request'

// 获取笔记列表
export function getNotes(courseId) {
  return request({
    url: '/notes/',
    method: 'get',
    params: { course: courseId }
  })
}

// 创建新笔记
export function createNote(data) {
  return request({
    url: '/notes/',
    method: 'post',
    data
  })
}

// 更新笔记
export function updateNote(id, data) {
  return request({
    url: `/notes/${id}/`,
    method: 'put',
    data
  })
}

// 删除笔记
export function deleteNote(id) {
  return request({
    url: `/notes/${id}/`,
    method: 'delete'
  })
}

// 获取单个笔记详情
export function getNoteDetail(id) {
  return request({
    url: `/notes/${id}/`,
    method: 'get'
  })
} 