import json
from typing import List, Dict, Any, Optional, Union, Generator, Callable

import requests
import sseclient

from .. import config


class TongyiAPI:
    """通义千问 API 封装类，支持 OpenAI 兼容方式调用"""

    def __init__(
        self, 
    ):
        """
        初始化通义千问 API 客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL，默认为 https://dashscope.aliyuncs.com/compatible-mode/v1
        """
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.api_key =  config.TONGYI_API_KEY

        # HTTP 方式的请求头
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def chat_http(
        self,
        messages: List[Dict[str, Any]],
        model: str = "qwen-plus",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        top_p: float = 0.8,
        presence_penalty: float = 0.0,
        enable_thinking: bool = False,
        **kwargs
    ) -> str:
        """
        使用HTTP方式创建聊天完成请求（一次性返回完整响应）
        
        Args:
            messages: 对话的消息列表
            model: 使用的模型，默认为 qwen-plus
            temperature: 采样温度，介于0和2之间
            max_tokens: 生成的最大token数
            top_p: 核采样概率阈值
            presence_penalty: 控制重复度的参数
            enable_thinking: 是否开启思考模式(对Qwen3模型有效)
            **kwargs: 其他参数
        
        Returns:
            API响应字典
        """
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "top_p": top_p,
            "presence_penalty": presence_penalty,
        }
        
        if max_tokens is not None:
            data["max_tokens"] = max_tokens
            
        if enable_thinking:
            data["enable_thinking"] = enable_thinking
            
        # 添加其他参数
        data.update(kwargs)
        
        response = requests.post(url, headers=self.headers, json=data)
        return response.json().get("choices")[0].get("message").get("content")
    
    def chat_http_stream(
        self,
        messages: List[Dict[str, Any]],
        model: str = "qwen-plus",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        top_p: float = 0.8,
        presence_penalty: float = 0.0,
        enable_thinking: bool = False,
        stream_callback: Optional[Callable[[str], None]] = None,
        **kwargs
    ) -> Generator[Dict[str, Any], None, None]:
        """
        使用HTTP方式创建流式聊天完成请求
        
        Args:
            messages: 对话的消息列表
            model: 使用的模型，默认为 qwen-plus
            temperature: 采样温度，介于0和2之间
            max_tokens: 生成的最大token数
            top_p: 核采样概率阈值
            presence_penalty: 控制重复度的参数
            enable_thinking: 是否开启思考模式(对Qwen3模型有效)
            stream_callback: 流式内容回调函数
            **kwargs: 其他参数
        
        Returns:
            Generator: 生成器，每次产生一个响应块
        """
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "top_p": top_p,
            "presence_penalty": presence_penalty,
            "stream": True
        }
        
        if max_tokens is not None:
            data["max_tokens"] = max_tokens
            
        if enable_thinking:
            data["enable_thinking"] = enable_thinking
            
        # 添加其他参数
        data.update(kwargs)
        
        response = requests.post(url, headers=self.headers, json=data, stream=True)
        client = sseclient.SSEClient(response)
        
        full_content = ""
        
        for event in client.events():
            if event.data == "[DONE]":
                break
            
            try:
                chunk = json.loads(event.data)
                
                if chunk.get("choices") and chunk["choices"][0].get("delta"):
                    delta = chunk["choices"][0]["delta"]
                    content = delta.get("content", "")
                    full_content += content
                    
                    if stream_callback and content:
                        stream_callback(content)
                
                yield chunk
            except json.JSONDecodeError:
                pass
    
    @staticmethod
    def create_system_message(content: str) -> Dict[str, str]:
        """创建system角色的消息"""
        return {"role": "system", "content": content}
    
    @staticmethod
    def create_user_message(content: str) -> Dict[str, str]:
        """创建user角色的消息"""
        return {"role": "user", "content": content}
    
    @staticmethod
    def create_user_message_with_images(
        content: str, 
        images: List[Union[str, Dict[str, str]]]
    ) -> Dict[str, Any]:
        """
        创建包含图片的user角色的消息
        
        Args:
            content: 文本内容
            images: 图片列表，可以是图片的base64编码字符串或包含图片信息的字典
                  字典格式: {"image_data": "base64_string", "image_format": "jpeg/png"}
        
        Returns:
            Dict: 包含文本和图片的用户消息
        """
        image_content = []
        
        for img in images:
            try:
                if isinstance(img, str):
                    # 如果直接是base64字符串
                    img_data = img.strip()  # 移除可能的空白字符
                    image_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{img_data}"
                        }
                    })
                elif isinstance(img, dict) and "image_data" in img:
                    # 如果是字典格式
                    img_data = img["image_data"].strip()  # 移除可能的空白字符
                    image_format = img.get("image_format", "jpeg").lower()
                    # 确保格式是jpeg或png
                    if image_format not in ["jpeg", "png"]:
                        image_format = "jpeg"
                    
                    image_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/{image_format};base64,{img_data}"
                        }
                    })
            except Exception as e:
                # 如果处理某张图片出错，记录错误但继续处理其他图片
                print(f"处理图片时出错: {str(e)}")
                continue
        
        # 组合文本和图片
        content_list = [{"type": "text", "text": content}]
        content_list.extend(image_content)
        
        return {
            "role": "user",
            "content": content_list
        }
    
    @staticmethod
    def create_assistant_message(content: str) -> Dict[str, str]:
        """创建assistant角色的消息"""
        return {"role": "assistant", "content": content}
    
    @staticmethod
    def create_user_message_with_image_urls(
        content: str, 
        image_urls: List[str]
    ) -> Dict[str, Any]:
        """
        创建包含图片URL的user角色的消息
        
        Args:
            content: 文本内容
            image_urls: 图片URL列表，这些URL应该是可公开访问的
        
        Returns:
            Dict: 包含文本和图片URL的用户消息
        """
        image_content = []
        
        for url in image_urls:
            try:
                if url and isinstance(url, str):
                    image_content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": url
                        }
                    })
            except Exception as e:
                # 如果处理某个URL出错，记录错误但继续处理其他URL
                print(f"处理图片URL时出错: {str(e)}")
                continue
        
        # 组合文本和图片
        content_list = [{"type": "text", "text": content}]
        content_list.extend(image_content)
        
        return {
            "role": "user",
            "content": content_list
        } 