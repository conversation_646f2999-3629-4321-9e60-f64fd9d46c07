# Generated by Django 3.2.20 on 2025-06-17 11:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0062_create_voice_model'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='VoiceModel',
            new_name='AudioModel',
        ),
        migrations.AlterModelTable(
            name='audiomodel',
            table='zhkt_audio_model',
        ),

        # VoiceGenerationHistory -> AudioGenerationHistory 及表名调整
        migrations.RenameModel(
            old_name='VoiceGenerationHistory',
            new_name='AudioGenerationHistory',
        ),
        migrations.AlterModelTable(
            name='audiogenerationhistory',
            table='zhkt_audio_generation_history',
        ),

        # 字段 text -> text_content
        migrations.RenameField(
            model_name='audiogenerationhistory',
            old_name='text',
            new_name='text_content',
        ),

        # 新增扩展字段
        migrations.AddField(
            model_name='audiogenerationhistory',
            name='duration',
            field=models.IntegerField(blank=True, null=True, verbose_name='音频时长(秒)'),
        ),
        migrations.AddField(
            model_name='audiogenerationhistory',
            name='speed',
            field=models.FloatField(default=1.0, verbose_name='语速'),
        ),
        migrations.AddField(
            model_name='audiogenerationhistory',
            name='pitch',
            field=models.IntegerField(default=0, verbose_name='音调'),
        ),
        migrations.AddField(
            model_name='audiogenerationhistory',
            name='power_consumed',
            field=models.IntegerField(default=0, verbose_name='消耗算力'),
        ),
    ]
