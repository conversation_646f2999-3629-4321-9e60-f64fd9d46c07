import request from '@/utils/request'

// 用户登录
export const login = (data) => {
  return request({
    url: '/users/login',
    method: 'POST',
    data
  })
}

// 获取用户信息
export const getUserInfo = () => {
  return request({
    url: '/users/info',
    method: 'GET'
  })
}

// 更新用户信息
export const updateUserInfo = (data) => {
  return request({
    url: '/users/info',
    method: 'PUT',
    data
  })
}

// 修改密码
export const changePassword = (data) => {
  return request({
    url: '/users/password',
    method: 'PUT',
    data
  })
} 