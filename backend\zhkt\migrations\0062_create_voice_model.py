# Generated by Django 3.2.20 on 2025-06-17 10:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0061_remove_ailecturesubtitle_zhkt_ailect_speech__c77fd7_idx'),
    ]

    operations = [
        migrations.CreateModel(
            name='VoiceModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模型名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='模型描述')),
                ('type', models.CharField(choices=[('custom', '自定义'), ('system', '系统')], default='custom', max_length=20, verbose_name='模型类型')),
                ('category', models.CharField(blank=True, max_length=50, null=True, verbose_name='模型分类')),
                ('reference_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='引用ID')),
                ('sample_audio_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='样本音频URL')),
                ('status', models.CharField(choices=[('processing', '处理中'), ('success', '成功'), ('failed', '失败')], default='processing', max_length=20, verbose_name='状态')),
                ('usage_count', models.IntegerField(default=0, verbose_name='使用次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='voice_models', to='zhkt.user', verbose_name='所属用户')),
            ],
            options={
                'verbose_name': '语音模型',
                'verbose_name_plural': '语音模型',
                'db_table': 'zhkt_voice_model',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VoiceGenerationHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='生成名称')),
                ('text', models.TextField(verbose_name='生成文本')),
                ('audio_url', models.CharField(blank=True, max_length=255, null=True, verbose_name='音频URL')),
                ('status', models.CharField(choices=[('processing', '处理中'), ('success', '成功'), ('failed', '失败')], default='processing', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('voice_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generation_histories', to='zhkt.voicemodel', verbose_name='语音模型')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='voice_generations', to='zhkt.user', verbose_name='所属用户')),
            ],
            options={
                'verbose_name': '语音生成历史',
                'verbose_name_plural': '语音生成历史',
                'db_table': 'zhkt_voice_generation_history',
                'ordering': ['-created_at'],
            },
        ),
    ]
