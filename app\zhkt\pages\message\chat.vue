<template>
  <view class="container">
    <!-- 消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y 
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      @scrolltoupper="loadMore"
    >
      <!-- 加载更多 -->
      <uni-load-more :status="loadMoreStatus"></uni-load-more>
      
      <!-- 消息内容 -->
      <view class="message-wrapper" v-for="message in messages" :key="message.id">
        <view class="time" v-if="message.showTime">{{ message.time }}</view>
        <view :class="['message', message.isSelf ? 'self' : 'other']">
          <image 
            class="avatar" 
            :src="message.avatar" 
            mode="aspectFill"
            v-if="!message.isSelf"
          ></image>
          <view class="content">
            <text class="name" v-if="!message.isSelf">{{ message.name }}</text>
            <view class="bubble">
              <!-- 文本消息 -->
              <text v-if="message.type === 'text'">{{ message.content }}</text>
              
              <!-- 图片消息 -->
              <image 
                v-if="message.type === 'image'" 
                :src="message.content" 
                mode="widthFix" 
                @tap="previewImage(message.content)"
              ></image>
              
              <!-- 语音消息 -->
              <view 
                v-if="message.type === 'voice'" 
                class="voice-message"
                @tap="playVoice(message)"
              >
                <uni-icons 
                  :type="message.isPlaying ? 'sound-filled' : 'sound'" 
                  size="20" 
                  color="#666"
                ></uni-icons>
                <text>{{ message.duration }}''</text>
              </view>
            </view>
            <text class="status" v-if="message.isSelf">{{ message.status }}</text>
          </view>
          <image 
            class="avatar" 
            :src="message.avatar" 
            mode="aspectFill"
            v-if="message.isSelf"
          ></image>
        </view>
      </view>
    </scroll-view>
    
    <!-- 输入区域 -->
    <view class="input-area">
      <!-- 语音/文字切换 -->
      <view class="voice-btn" @tap="toggleVoiceInput">
        <uni-icons :type="isVoiceInput ? 'keyboard' : 'mic'" size="24" color="#666"></uni-icons>
      </view>
      
      <!-- 文字输入 -->
      <input 
        v-if="!isVoiceInput"
        type="text"
        v-model="inputContent"
        :focus="inputFocus"
        @focus="onInputFocus"
        @blur="onInputBlur"
        placeholder="请输入消息"
      />
      
      <!-- 语音输入 -->
      <view 
        v-else
        class="voice-input"
        @touchstart="startRecording"
        @touchend="stopRecording"
        @touchmove="cancelRecording"
      >
        按住说话
      </view>
      
      <!-- 表情按钮 -->
      <view class="emoji-btn" @tap="toggleEmojiPanel">
        <uni-icons type="emotion" size="24" color="#666"></uni-icons>
      </view>
      
      <!-- 更多功能按钮 -->
      <view class="more-btn" @tap="toggleMorePanel">
        <uni-icons type="plusempty" size="24" color="#666"></uni-icons>
      </view>
      
      <!-- 发送按钮 -->
      <button 
        class="send-btn" 
        :class="{ active: inputContent }"
        @tap="sendMessage"
        v-if="!isVoiceInput"
      >发送</button>
    </view>
    
    <!-- 表情面板 -->
    <view class="emoji-panel" v-if="showEmojiPanel">
      <scroll-view class="emoji-list" scroll-y>
        <view 
          class="emoji-item"
          v-for="emoji in emojis"
          :key="emoji"
          @tap="insertEmoji(emoji)"
        >
          {{ emoji }}
        </view>
      </scroll-view>
    </view>
    
    <!-- 更多功能面板 -->
    <view class="more-panel" v-if="showMorePanel">
      <view class="action-list">
        <view class="action-item" @tap="chooseImage">
          <view class="icon-wrapper">
            <uni-icons type="image" size="24" color="#666"></uni-icons>
          </view>
          <text>图片</text>
        </view>
        <view class="action-item" @tap="takePhoto">
          <view class="icon-wrapper">
            <uni-icons type="camera" size="24" color="#666"></uni-icons>
          </view>
          <text>拍照</text>
        </view>
        <view class="action-item" @tap="chooseFile">
          <view class="icon-wrapper">
            <uni-icons type="folder" size="24" color="#666"></uni-icons>
          </view>
          <text>文件</text>
        </view>
      </view>
    </view>
    
    <!-- 录音提示 -->
    <view class="recording-tip" v-if="isRecording">
      <view class="tip-content">
        <uni-icons :type="recordingCanceled ? 'closeempty' : 'mic-filled'" size="40" color="#fff"></uni-icons>
        <text>{{ recordingCanceled ? '松开手指，取消发送' : '手指上滑，取消发送' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import request from '@/utils/request'

// 消息列表
const messages = ref([])
const page = ref(1)
const loadMoreStatus = ref('more')
const scrollTop = ref(0)

// 输入相关
const inputContent = ref('')
const inputFocus = ref(false)
const isVoiceInput = ref(false)
const isRecording = ref(false)
const recordingCanceled = ref(false)

// 面板显示
const showEmojiPanel = ref(false)
const showMorePanel = ref(false)

// 表情列表
const emojis = ref(['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇'])

// 获取消息列表
const getMessages = async (refresh = false) => {
  if (refresh) {
    page.value = 1
    messages.value = []
  }
  
  try {
    loadMoreStatus.value = 'loading'
    const response = await request({
      url: '/message/list/',
      method: 'GET',
      data: {
        page: page.value,
        to_user: getQueryParam('userId')
      }
    })
    
    const { results, has_next } = response
    messages.value = [...messages.value, ...results]
    loadMoreStatus.value = has_next ? 'more' : 'noMore'
    
    // 滚动到底部
    if (refresh) {
      scrollToBottom()
    }
  } catch (error) {
    loadMoreStatus.value = 'more'
    uni.showToast({
      title: '获取消息失败',
      icon: 'none'
    })
  }
}

// 加载更多
const loadMore = () => {
  if (loadMoreStatus.value === 'loading' || loadMoreStatus.value === 'noMore') return
  page.value++
  getMessages()
}

// 滚动到底部
const scrollToBottom = () => {
  setTimeout(() => {
    const query = uni.createSelectorQuery()
    query.select('.message-list').boundingClientRect()
    query.exec((res) => {
      if (res[0]) {
        scrollTop.value = res[0].height
      }
    })
  }, 100)
}

// 发送消息
const sendMessage = async () => {
  if (!inputContent.value.trim()) return
  
  try {
    const message = {
      type: 'text',
      content: inputContent.value,
      to_user: getQueryParam('userId')
    }
    
    await request({
      url: '/message/send/',
      method: 'POST',
      data: message
    })
    
    inputContent.value = ''
    getMessages(true)
  } catch (error) {
    uni.showToast({
      title: '发送失败',
      icon: 'none'
    })
  }
}

// 切换语音输入
const toggleVoiceInput = () => {
  isVoiceInput.value = !isVoiceInput.value
  showEmojiPanel.value = false
  showMorePanel.value = false
}

// 开始录音
const startRecording = () => {
  isRecording.value = true
  recordingCanceled.value = false
  
  uni.startRecord({
    success: () => {
      console.log('开始录音')
    }
  })
}

// 停止录音
const stopRecording = () => {
  if (recordingCanceled.value) {
    uni.stopRecord()
    isRecording.value = false
    return
  }
  
  uni.stopRecord({
    success: (res) => {
      console.log('录音文件：', res.tempFilePath)
      uploadVoice(res.tempFilePath)
    }
  })
  
  isRecording.value = false
}

// 取消录音
const cancelRecording = (e) => {
  const { touches } = e
  if (touches[0].pageY < 400) { // 根据实际情况调整判断条件
    recordingCanceled.value = true
  } else {
    recordingCanceled.value = false
  }
}

// 上传语音文件
const uploadVoice = async (filePath) => {
  try {
    const response = await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: '/api/upload/',
        filePath: filePath,
        name: 'file',
        success: (res) => {
          resolve(JSON.parse(res.data))
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
    
    const message = {
      type: 'voice',
      content: response.url,
      duration: response.duration,
      to_user: getQueryParam('userId')
    }
    
    await request({
      url: '/message/send/',
      method: 'POST',
      data: message
    })
    
    getMessages(true)
  } catch (error) {
    uni.showToast({
      title: '发送失败',
      icon: 'none'
    })
  }
}

// 播放语音
const playVoice = (message) => {
  const innerAudioContext = uni.createInnerAudioContext()
  innerAudioContext.src = message.content
  innerAudioContext.play()
  
  message.isPlaying = true
  innerAudioContext.onEnded(() => {
    message.isPlaying = false
  })
}

// 切换表情面板
const toggleEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value
  showMorePanel.value = false
  if (showEmojiPanel.value) {
    inputFocus.value = false
  }
}

// 插入表情
const insertEmoji = (emoji) => {
  inputContent.value += emoji
}

// 切换更多面板
const toggleMorePanel = () => {
  showMorePanel.value = !showMorePanel.value
  showEmojiPanel.value = false
  if (showMorePanel.value) {
    inputFocus.value = false
  }
}

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      uploadImage(tempFilePath)
    }
  })
}

// 拍照
const takePhoto = () => {
  uni.chooseImage({
    count: 1,
    sourceType: ['camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      uploadImage(tempFilePath)
    }
  })
}

// 上传图片
const uploadImage = async (filePath) => {
  try {
    const response = await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: '/api/upload/',
        filePath: filePath,
        name: 'file',
        success: (res) => {
          resolve(JSON.parse(res.data))
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
    
    const message = {
      type: 'image',
      content: response.url,
      to_user: getQueryParam('userId')
    }
    
    await request({
      url: '/message/send/',
      method: 'POST',
      data: message
    })
    
    getMessages(true)
  } catch (error) {
    uni.showToast({
      title: '发送失败',
      icon: 'none'
    })
  }
}

// 选择文件
const chooseFile = () => {
  uni.chooseFile({
    count: 1,
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      uploadFile(tempFilePath)
    }
  })
}

// 上传文件
const uploadFile = async (filePath) => {
  try {
    const response = await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: '/api/upload/',
        filePath: filePath,
        name: 'file',
        success: (res) => {
          resolve(JSON.parse(res.data))
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
    
    const message = {
      type: 'file',
      content: response.url,
      fileName: response.name,
      fileSize: response.size,
      to_user: getQueryParam('userId')
    }
    
    await request({
      url: '/message/send/',
      method: 'POST',
      data: message
    })
    
    getMessages(true)
  } catch (error) {
    uni.showToast({
      title: '发送失败',
      icon: 'none'
    })
  }
}

// 预览图片
const previewImage = (url) => {
  uni.previewImage({
    urls: [url]
  })
}

// 输入框获得焦点
const onInputFocus = () => {
  inputFocus.value = true
  showEmojiPanel.value = false
  showMorePanel.value = false
}

// 输入框失去焦点
const onInputBlur = () => {
  inputFocus.value = false
}

// 获取URL参数
const getQueryParam = (name) => {
  const query = window.location.search.substring(1)
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (pair[0] === name) {
      return pair[1]
    }
  }
  return null
}

onMounted(() => {
  //getMessages(true)
})

onUnmounted(() => {
  // 清理录音等资源
  uni.stopRecord()
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.message-list {
  flex: 1;
  padding: 20rpx;
  
  .message-wrapper {
    margin-bottom: 30rpx;
    
    .time {
      text-align: center;
      font-size: 24rpx;
      color: #999;
      margin-bottom: 20rpx;
    }
    
    .message {
      display: flex;
      align-items: flex-start;
      
      &.self {
        flex-direction: row-reverse;
        
        .content {
          align-items: flex-end;
          margin-right: 20rpx;
          
          .bubble {
            background: #3cc51f;
            color: #fff;
            
            &::after {
              right: -20rpx;
              border-left-color: #3cc51f;
            }
          }
        }
      }
      
      &.other {
        .content {
          margin-left: 20rpx;
          
          .bubble {
            background: #fff;
            
            &::after {
              left: -20rpx;
              border-right-color: #fff;
            }
          }
        }
      }
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
      
      .content {
        display: flex;
        flex-direction: column;
        
        .name {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 10rpx;
        }
        
        .bubble {
          position: relative;
          padding: 20rpx;
          border-radius: 8rpx;
          max-width: 60%;
          word-break: break-all;
          
          &::after {
            content: '';
            position: absolute;
            top: 20rpx;
            border: 10rpx solid transparent;
          }
          
          text {
            font-size: 28rpx;
            line-height: 1.4;
          }
          
          image {
            max-width: 100%;
            border-radius: 8rpx;
          }
          
          .voice-message {
            display: flex;
            align-items: center;
            
            text {
              margin-left: 10rpx;
            }
          }
        }
        
        .status {
          font-size: 24rpx;
          color: #999;
          margin-top: 10rpx;
        }
      }
    }
  }
}

.input-area {
  background: #fff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #eee;
  
  .voice-btn, .emoji-btn, .more-btn {
    padding: 10rpx;
  }
  
  input {
    flex: 1;
    height: 72rpx;
    background: #f5f5f5;
    border-radius: 36rpx;
    padding: 0 30rpx;
    margin: 0 20rpx;
    font-size: 28rpx;
  }
  
  .voice-input {
    flex: 1;
    height: 72rpx;
    background: #f5f5f5;
    border-radius: 36rpx;
    margin: 0 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #666;
  }
  
  .send-btn {
    width: 120rpx;
    height: 72rpx;
    background: #ddd;
    color: #fff;
    font-size: 28rpx;
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &.active {
      background: #3cc51f;
    }
  }
}

.emoji-panel {
  height: 400rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  
  .emoji-list {
    height: 100%;
    padding: 20rpx;
    display: flex;
    flex-wrap: wrap;
    
    .emoji-item {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40rpx;
    }
  }
}

.more-panel {
  height: 400rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  
  .action-list {
    display: flex;
    flex-wrap: wrap;
    padding: 40rpx;
    
    .action-item {
      width: 160rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 40rpx;
      margin-bottom: 40rpx;
      
      .icon-wrapper {
        width: 100rpx;
        height: 100rpx;
        background: #f5f5f5;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20rpx;
      }
      
      text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.recording-tip {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .tip-content {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20rpx;
    padding: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    text {
      font-size: 28rpx;
      color: #fff;
      margin-top: 20rpx;
    }
  }
}
</style> 