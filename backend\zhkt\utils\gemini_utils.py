# -*- coding: utf-8 -*-
"""
Google Gemini API 调用工具类 - 通过ai-education-tool API代理
"""

import os
import logging
import traceback
from datetime import datetime
from typing import Dict, Any

import requests
from tenacity import retry, stop_after_attempt, wait_fixed

from ..config import AI_EDUCATION_TOOL_URL
from ..utils.temp_file_utils import create_temp_subdir, clean_temp_file, clean_temp_dir

logger = logging.getLogger(__name__)

class _GeminiClientBase:
    """基础客户端类，用于API调用准备"""
    
    def __init__(self):
        """初始化"""
        self.api_base_url = AI_EDUCATION_TOOL_URL


class GeminiDialogueGenerator(_GeminiClientBase):
    """Gemini 对话生成器 (通用)"""

    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
    def generate_pro_content(self, prompt: str) -> str:
        """根据给定的提示词生成内容 - Pro模型"""
        try:
            response = requests.post(
                f"{self.api_base_url}/api/gemini/generate_pro_content",
                data={"prompt": prompt}
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("status") == "success":
                return result.get("content", "")
            else:
                raise Exception(result.get("message", "未知错误"))
        except Exception as e:
            logger.error(f"生成Pro内容失败: {str(e)}")
            raise

    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
    def generate_flash_content(self, prompt: str) -> str:
        """根据给定的提示词生成内容 - Flash模型"""
        try:
            response = requests.post(
                f"{self.api_base_url}/api/gemini/generate_flash_content",
                data={"prompt": prompt}
            )
            response.raise_for_status()
            result = response.json()
            
            if result.get("status") == "success":
                return result.get("content", "")
            else:
                raise Exception(result.get("message", "未知错误"))
        except Exception as e:
            logger.error(f"生成Flash内容失败: {str(e)}")
            raise


class GeminiAudioGenerator(_GeminiClientBase):
    """Gemini 音频生成器"""

    @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
    def generate_audio(self, script_text: str):
        """生成音频 - 调用API"""
        try:
            response = requests.post(
                f"{self.api_base_url}/api/gemini/generate_audio",
                data={"content_text": script_text}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"生成音频失败: {str(e)}")
            raise

    def save_audio(self, response_data: Dict[str, Any], output_dir: str = None, timestamp: str = None) -> str:
        """保存API返回的音频数据到文件"""
        temp_dir = None
        audio_filename = None
        
        try:
            # 创建临时目录
            if output_dir:
                # 如果提供了输出目录，确保它存在
                os.makedirs(output_dir, exist_ok=True)
                temp_dir = output_dir
            else:
                # 否则创建临时目录
                temp_dir = create_temp_subdir("gemini_audio")
            
            ts = timestamp if timestamp else datetime.now().strftime("%Y%m%d_%H%M%S")
            audio_filename = os.path.join(temp_dir, f"dialogue_{ts}.wav")
            
            if "audio_base64" in response_data:
                # 如果API返回base64编码的音频数据
                import base64
                audio_bytes = base64.b64decode(response_data["audio_base64"])
                with open(audio_filename, "wb") as f:
                    f.write(audio_bytes)
                logger.info(f"已保存音频文件: {audio_filename}")
                return audio_filename
            else:
                raise Exception("API响应中未找到音频数据")
        except Exception as e:
            logger.error(f"保存音频文件失败: {str(e)}")
            # 如果是我们创建的临时目录，则清理它
            if temp_dir and not output_dir:
                clean_temp_dir(temp_dir)
            raise


@retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
def generate_dialogue_and_audio(content_text: str, output_dir: str = None, language: str = 'zh', custom_prompt: str = None):
    """
    一个辅助函数，封装了通过API生成对话脚本和音频的完整流程。
    
    Args:
        content_text: 内容文本
        output_dir: 输出目录，如果为None则创建临时目录
        language: 语言代码，支持 'zh', 'en', 'vi', 'id'
        custom_prompt: 自定义提示词，完整的提示词模板，已经包含内容文本
                      如果提供则直接使用，无需FastAPI端构建提示词
        
    Returns:
        dict: 包含音频文件路径和脚本文本的字典
        
    Raises:
        Exception: 生成失败时抛出
        
    注意:
        1. 如果提供了custom_prompt，则会直接将其传递给FastAPI端，用于生成脚本
        2. 如果没有提供custom_prompt，则会由FastAPI端根据language参数选择适当的提示词模板
        3. 自定义提示词的内容应当包含完整的指令，包括脚本格式、输出要求等
    """
    temp_dir = None
    audio_filename = None
    
    try:
        # 验证语言参数
        if language not in ['zh', 'en', 'vi', 'id']:
            logger.warning(f"不支持的语言: {language}，使用默认中文")
            language = 'zh'  # 默认使用中文
            
        # 创建临时目录
        if output_dir:
            # 如果提供了输出目录，确保它存在
            os.makedirs(output_dir, exist_ok=True)
            temp_dir = output_dir
        else:
            # 否则创建临时目录
            temp_dir = create_temp_subdir("gemini_dialogue")
        
        # 准备API请求数据
        form_data = {
            "content_text": content_text,
            "language": language  # 确保语言参数被传递
        }
        
        # 如果提供了自定义提示词，添加到请求中
        if custom_prompt:
            form_data["custom_prompt"] = custom_prompt
        
        # 调用API生成对话和音频
        response = requests.post(
            f"{AI_EDUCATION_TOOL_URL}/api/gemini/generate_audio",
            data=form_data
        )
        response.raise_for_status()
        result = response.json()
        
        if "error" in result:
            raise Exception(result.get("error", "API调用失败"))
        
        # 准备时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 获取脚本文本
        script_text = result.get("text", "")
        
        # 保存音频
        if "audio_base64" in result:
            # 如果API返回base64编码的音频数据
            import base64
            audio_filename = os.path.join(temp_dir, f"dialogue_{timestamp}_{language}.wav")
            audio_bytes = base64.b64decode(result["audio_base64"])
            with open(audio_filename, "wb") as f:
                f.write(audio_bytes)
        
        logger.info(f"对话和音频生成成功: 音频={audio_filename}, 语言={language}, 自定义提示词={'是' if custom_prompt else '否'}")
        return {
            'audio_file': audio_filename,
            'script_text': script_text,  # 直接返回脚本文本而不是文件路径
            'language': language,  # 返回语言信息
            'used_custom_prompt': bool(custom_prompt)  # 返回是否使用了自定义提示词
        }
    except Exception as e:
        traceback.print_exc()
        logger.error(f"生成对话和音频失败 (语言={language}): {str(e)}")
        # 只有当我们创建了临时目录时才清理
        if temp_dir and not output_dir:
            if audio_filename and os.path.exists(audio_filename):
                clean_temp_file(audio_filename)
            clean_temp_dir(temp_dir)
        raise


if __name__ == '__main__':
    generate_dialogue_and_audio("你好，我是Anya，今天我们聊一下关于AI的话题。", "output_audio")
    
    # print(GeminiDialogueGenerator().generate_flash_content("1+1"))