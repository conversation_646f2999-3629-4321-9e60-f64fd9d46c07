<template>
  <view class="container">
    <!-- 视频播放器 -->
    <view class="video-player-section">
      <video
        v-if="currentVideo"
        :src="currentVideo"
        class="video-player"
        controls
		autoplay
        @error="handleVideoError"
      ></video>
      <view v-else class="no-video-placeholder">
        <image :src="courseInfo.cover_image" mode="aspectFill" class="cover-image"></image>
        <view class="course-info">
          <text class="course-name">{{ courseInfo.name }}</text>
          <text class="teacher-name">{{ courseInfo.teacher.title }}</text>
        </view>
      </view>
    </view>
    
    <!-- 标签页 -->
    <view class="tabs-section">
      <view class="tabs-header">
        <view 
          class="tab-item" 
          :class="{ 'active': activeTab === 'intro' }"
          @click="activeTab = 'intro'"
        >简介</view>
        <view 
          class="tab-item" 
          :class="{ 'active': activeTab === 'comments' }"
          @click="activeTab = 'comments'"
        >评论<span class="comment-count" v-if="commentCount > 0">({{ commentCount }})</span></view>
      </view>
      
      <!-- 标签页内容 -->
      <view class="tabs-content">
        <!-- 简介标签页 -->
        <view v-if="activeTab === 'intro'" class="tab-pane">
          <!-- 课程描述 -->
          <view class="course-description">
            <text class="description-title">课程简介</text>
            <text class="description-content">{{ courseInfo.description || '暂无课程简介' }}</text>
          </view>
          
          <!-- 课程评分 -->
          <view class="course-rating">
            <view class="rating-score">
              <text class="score">{{ courseInfo.rating || '5.0' }}</text>
              <view class="stars">
                <uni-icons v-for="i in 5" :key="i" type="star-filled" 
                  :color="i <= Math.floor(courseInfo.rating || 5) ? '#ff9900' : '#ddd'" 
                  size="16"
                ></uni-icons>
              </view>
            </view>
            <text class="rating-count">{{ courseInfo.rating_count || 0 }}人评分</text>
          </view>
          
          <!-- 学习进度 -->
          <view class="progress-section">
            <view class="progress-info">
              <text class="progress-text">学习进度：{{ courseInfo.progress }}%</text>
              <text class="chapter-info">已学习 {{ courseInfo.learnedChapters }}/{{ courseInfo.totalChapters }} 章</text>
            </view>
            <view class="progress-bar">
              <view class="progress" :style="{ width: courseInfo.progress + '%' }"></view>
            </view>
          </view>
          
          <!-- 章节列表 -->
          <view class="chapter-list">
            <view class="chapter-item" v-for="chapter in chapters" :key="chapter.id">
              <view class="chapter-header" @click="toggleChapter(chapter)">
                <view class="chapter-title">
                  <uni-icons :type="chapter.isExpanded ? 'bottom' : 'right'" size="16" color="#666"></uni-icons>
                  <text class="chapter-name">{{ chapter.title }}</text>
                </view>
              </view>
              <!-- 课时列表 -->
              <view class="lesson-list" v-if="chapter.isExpanded">
                <view 
                  class="lesson-item" 
                  v-for="lesson in chapter.lessons" 
                  :key="lesson.id" 
                  @click="playLesson(lesson)"
                  :class="{ 'lesson-item-active': currentLessonId === lesson.id }"
                >
                  <view class="lesson-info">
                    <view class="lesson-title">
                      <uni-icons 
                        :type="currentLessonId === lesson.id ? 'videocam-filled' : 'videocam'" 
                        size="16" 
                        :color="currentLessonId === lesson.id ? '#2979ff' : '#666'"
                      ></uni-icons>
                      <text 
                        class="lesson-name"
                        :class="{ 'lesson-name-active': currentLessonId === lesson.id }"
                      >{{ lesson.title }}</text>
                    </view>
                    <text class="duration">{{ lesson.duration }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 评论标签页 -->
        <view v-else class="tab-pane">
          <view class="comments-list">
            <view v-if="comments.length === 0" class="no-comments">
              <text>暂无评论</text>
            </view>
            <view v-else v-for="comment in comments" :key="comment.id" class="comment-item">
              <view class="comment-header">
                <image class="avatar" :src="comment.user_avatar" mode="aspectFill"></image>
                <view class="comment-info">
                  <text class="username">{{ comment.user_name }}</text>
                  <text class="time">{{ getFriendlyDate(comment.created_at) }}</text>
                </view>
              </view>
              <view class="comment-content">
                <text>{{ comment.content }}</text>
                <view class="comment-actions">
                  <text class="action-btn" @click="showReplyInput(comment)">回复</text>
                </view>
              </view>
              <!-- 回复输入框 -->
              <view v-if="currentReplyTo && currentReplyTo.id === comment.id" class="reply-input-wrapper">
                <textarea
                  v-model="replyContent"
                  class="reply-textarea"
                  placeholder="回复评论..."
                  :maxlength="200"
                  auto-height
                ></textarea>
                <view class="reply-footer">
                  <text class="word-count">{{ replyContent.length }}/200</text>
                  <view class="btn-group">
                    <button 
                      class="cancel-btn" 
                      @click="cancelReply"
                    >取消</button>
                    <button 
                      class="submit-btn" 
                      :class="{ 'disabled': !replyContent.trim() }"
                      @click="submitReply"
                    >回复</button>
                  </view>
                </view>
              </view>
              <!-- 回复列表 -->
              <view v-if="comment.replies && comment.replies.length > 0" class="replies-list">
                <view v-for="reply in comment.replies" :key="reply.id" class="reply-item">
                  <view class="reply-header">
                    <image class="avatar" :src="reply.user_avatar" mode="aspectFill"></image>
                    <view class="reply-info">
                      <text class="username">{{ reply.user_name }}</text>
                      <text class="time">{{ getFriendlyDate(reply.created_at) }}</text>
                    </view>
                  </view>
                  <view class="reply-content">
                    <text>{{ reply.content }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 评论输入框 -->
          <view class="comment-input-section">
            <view class="input-wrapper">
              <textarea
                v-model="newComment"
                class="comment-textarea"
                placeholder="写下你的评论..."
                :maxlength="200"
                auto-height
              ></textarea>
              <view class="input-footer">
                <text class="word-count">{{ newComment.length }}/200</text>
                <button 
                  class="submit-btn" 
                  :class="{ 'disabled': !newComment.trim() }"
                  @click="submitComment"
                >发表评论</button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import request from '@/utils/request'
import { getFriendlyDate } from '@/utils/date'

// 课程信息
const courseInfo = ref({
  id: '',
  name: '',
  teacher: '',
  cover: '',
  progress: 0,
  learnedChapters: 0,
  totalChapters: 0,
})

// 章节列表
const chapters = ref([])

// 存储课程ID
const courseId = ref('')

// 当前播放的视频URL
const currentVideo = ref('')

// 当前播放的课时ID
const currentLessonId = ref('')

// 标签页状态
const activeTab = ref('intro')

// 评论数据
const comments = ref([])

const commentCount = ref(0)

// 新评论内容
const newComment = ref('')

// 回复相关的状态
const currentReplyTo = ref(null)
const replyContent = ref('')

// 获取课程详情
const getCourseDetail = async () => {
  try {
    const response = await request({
      url: `/courses/${courseId.value}/`,
      method: 'GET'
    })
    courseInfo.value = response
    courseInfo.value.learnedChapters = 1
    courseInfo.value.totalChapters = 2
	courseInfo.value.progress = 50
  } catch (error) {
    console.log('获取课程详情失败',error)
    uni.showToast({
      title: '获取课程详情失败',
      icon: 'none'
    })
  }
}

const loadLessons = (chapterId) => {
	return request({
		url: `/lessons/?chapter_id=${chapterId}`,
		method: 'GET'
	})
}

// 获取章节列表
const getChapterList = async () => {
  try {
    const response = await request({
      url: `/chapters/?course_id=${courseId.value}`,
      method: 'GET'
    })
	// 加载每个章节的课时
	for (const chapter of response.results) {
	  const lesson = await loadLessons(chapter.id)
	  chapter.lessons = lesson.results
	}
    // 为每个章节添加展开状态控制
    chapters.value = response.results.map(chapter => ({
      ...chapter,
      isExpanded: false
    }))
  } catch (error) {
    uni.showToast({
      title: '获取章节列表失败',
      icon: 'none'
    })
  }
}

// 切换章节展开状态
const toggleChapter = (chapter) => {
  chapter.isExpanded = !chapter.isExpanded
}

// 播放课时视频
const playLesson = (lesson) => {
  console.log('视频地址',lesson.video_url)
  if (lesson.video_url) {
    currentVideo.value = lesson.video_url
    currentLessonId.value = lesson.id
  } else {
    uni.showToast({
      title: '该课时暂无视频',
      icon: 'none'
    })
  }
}

// 处理视频播放错误
const handleVideoError = () => {
  uni.showToast({
    title: '视频播放失败',
    icon: 'none'
  })
  currentVideo.value = ''
}

// 获取评论列表
const getComments = async () => {
  try {
    const response = await request({
      url: `/comments/?course_id=${courseId.value}`,
      method: 'GET'
    })
    comments.value = response.results || []
	// 计算主评论和回复的总数
	commentCount.value = response.results.reduce((total, comment) => {
        return total + 1 + (comment.replies ? comment.replies.length : 0)
    }, 0)
  } catch (error) {
    console.error('获取评论失败', error)
    uni.showToast({
      title: '获取评论失败',
      icon: 'none'
    })
  }
}

// 提交评论
const submitComment = async () => {
  if (!newComment.value.trim()) {
    return
  }
  
  try {
    const response = await request({
      url: '/comments/',
      method: 'POST',
      data: {
        course: courseId.value,
        content: newComment.value.trim()
      }
    })
    
    // 清空输入框
    newComment.value = ''
    
    // 重新获取评论列表
    getComments()
    
    uni.showToast({
      title: '评论成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('提交评论失败', error)
    uni.showToast({
      title: '评论失败',
      icon: 'none'
    })
  }
}

// 显示回复输入框
const showReplyInput = (comment) => {
  currentReplyTo.value = comment
  replyContent.value = ''
}

// 取消回复
const cancelReply = () => {
  currentReplyTo.value = null
  replyContent.value = ''
}

// 提交回复
const submitReply = async () => {
  if (!replyContent.value.trim() || !currentReplyTo.value) {
    return
  }
  
  try {
    const response = await request({
      url: `/comments/${currentReplyTo.value.id}/reply/`,
      method: 'POST',
      data: {
        content: replyContent.value.trim()
      }
    })
    
    // 清空输入框并隐藏
    replyContent.value = ''
    currentReplyTo.value = null
    
    // 重新获取评论列表
    getComments()
    
    uni.showToast({
      title: '回复成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('提交回复失败', error)
    uni.showToast({
      title: '回复失败',
      icon: 'none'
    })
  }
}

// 页面加载时获取参数
onLoad((option) => {
  courseId.value = option.id
  if (!courseId.value) {
    uni.showToast({
      title: '课程ID不能为空',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    return
  }
  getCourseDetail()
  getChapterList()
  getComments()
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.video-player-section {
  position: relative;
  height: 400rpx;
  background: #000;
  
  .video-player {
    width: 100%;
    height: 100%;
  }
  
  .no-video-placeholder {
    position: relative;
    height: 100%;
    
    .cover-image {
      width: 100%;
      height: 100%;
    }
    
    .course-info {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 30rpx;
      background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
      
      .course-name {
        font-size: 36rpx;
        color: #fff;
        font-weight: bold;
        margin-bottom: 10rpx;
      }
      
      .teacher-name {
        font-size: 28rpx;
        color: rgba(255,255,255,0.8);
      }
    }
  }
}

.tabs-section {
  background: #fff;
  margin-bottom: 20rpx;
  
  .tabs-header {
    display: flex;
    border-bottom: 1rpx solid #eee;
    
    .tab-item {
      flex: 1;
      text-align: center;
      padding: 24rpx 0;
      font-size: 28rpx;
      color: #666;
      position: relative;
      
      &.active {
        color: #2979ff;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background: #2979ff;
          border-radius: 2rpx;
        }
      }
    }
  }
  
  .tabs-content {
    .tab-pane {
      padding: 30rpx;
    }
  }
}

.course-rating {
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .rating-score {
    display: flex;
    align-items: center;
    gap: 10rpx;
    
    .score {
      font-size: 40rpx;
      font-weight: bold;
      color: #ff9900;
    }
    
    .stars {
      display: flex;
      gap: 4rpx;
    }
  }
  
  .rating-count {
    font-size: 24rpx;
    color: #999;
  }
}

.course-description {
  margin-top: 30rpx;
  
  .description-title {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }
  
  .description-content {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
  }
}

.comments-list {
  .no-comments {
    text-align: center;
    padding: 60rpx 0;
    color: #999;
    font-size: 28rpx;
  }
  
  .comment-item {
    padding: 30rpx 0;
    border-bottom: 1rpx solid #eee;
    
    &:last-child {
      border-bottom: none;
    }
    
    .comment-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 16rpx;
      
      .avatar {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
      }
      
      .comment-info {
        .username {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          display: block;
        }
        
        .time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .comment-content {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      padding-left: 80rpx;
      
      .comment-actions {
        margin-top: 16rpx;
        
        .action-btn {
          font-size: 24rpx;
          color: #2979ff;
          padding: 4rpx 0;
          
          &:active {
            opacity: 0.8;
          }
        }
      }
    }
  }
}

.replies-list {
  margin-top: 20rpx;
  padding-left: 80rpx;
  
  .reply-item {
    background: #f5f5f5;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .reply-header {
      display: flex;
      align-items: center;
      gap: 12rpx;
      margin-bottom: 12rpx;
      
      .avatar {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
      }
      
      .reply-info {
        .username {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }
        
        .time {
          font-size: 22rpx;
          color: #999;
          margin-left: 12rpx;
        }
      }
    }
    
    .reply-content {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
    }
  }
}

.progress-section {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 30rpx;
  
  .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    
    .progress-text {
      font-size: 28rpx;
      color: #333;
    }
    
    .chapter-info {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .progress-bar {
    height: 6rpx;
    background: #eee;
    border-radius: 3rpx;
    
    .progress {
      height: 100%;
      background: #3cc51f;
      border-radius: 3rpx;
      transition: width 0.3s;
    }
  }
}

.chapter-list {
  background: #fff;
  padding: 0 30rpx;
  
  .chapter-item {
    border-bottom: 1rpx solid #eee;
    
    &:last-child {
      border-bottom: none;
    }
    
    .chapter-header {
      padding: 30rpx 0;
      
      .chapter-title {
        display: flex;
        align-items: center;
        
        .chapter-name {
          font-size: 28rpx;
          color: #333;
          margin-left: 10rpx;
        }
        
        .duration {
          margin-left: 5px;
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}

.lesson-list {
  padding-left: 40rpx;
  
  .lesson-item {
    padding: 20rpx 0;
    margin-left: 26rpx;
    border-left: 1rpx solid #eee;
    transition: all 0.3s ease;
    
    &.lesson-item-active {
      background-color: rgba(41, 121, 255, 0.1);
      border-left-color: #2979ff;
    }
    
    .lesson-info {
      display: flex;
      align-items: center;
      padding-left: 30rpx;
      position: relative;
      justify-content: space-between;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        width: 20rpx;
        height: 1rpx;
        background: #eee;
      }
      
      .lesson-title {
        display: flex;
        align-items: center;
        gap: 10rpx;
      }
      
      .lesson-name {
        font-size: 26rpx;
        color: #666;
        transition: color 0.3s ease;
        
        &.lesson-name-active {
          color: #2979ff;
          font-weight: 500;
        }
      }
      
      .duration {
        margin-left: 10rpx;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.comment-input-section {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx;
  border-top: 1rpx solid #eee;
  
  .input-wrapper {
    background: #f5f5f5;
    border-radius: 12rpx;
    padding: 20rpx;
    
    .comment-textarea {
      width: 100%;
      min-height: 80rpx;
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
    
    .input-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;
      
      .word-count {
        font-size: 24rpx;
        color: #999;
      }
      
      .submit-btn {
        margin: 0;
        padding: 0 32rpx;
        height: 64rpx;
        line-height: 64rpx;
        font-size: 28rpx;
        color: #fff;
        background: #2979ff;
        border-radius: 32rpx;
        
        &.disabled {
          opacity: 0.6;
          background: #999;
        }
      }
    }
  }
}

// 调整评论列表的底部边距，为输入框留出空间
.comments-list {
  padding-bottom: 180rpx;
  
  .comment-item {
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }
}

.reply-input-wrapper {
  margin: 20rpx 0 20rpx 80rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx;
  
  .reply-textarea {
    width: 100%;
    min-height: 60rpx;
    font-size: 26rpx;
    color: #333;
    line-height: 1.5;
  }
  
  .reply-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16rpx;
    
    .word-count {
      font-size: 24rpx;
      color: #999;
    }
    
    .btn-group {
      display: flex;
      gap: 16rpx;
      
      button {
        margin: 0;
        padding: 0 24rpx;
        height: 56rpx;
        line-height: 56rpx;
        font-size: 26rpx;
        border-radius: 28rpx;
      }
      
      .cancel-btn {
        color: #666;
        background: #eee;
      }
      
      .submit-btn {
        color: #fff;
        background: #2979ff;
        
        &.disabled {
          opacity: 0.6;
          background: #999;
        }
      }
    }
  }
}
</style> 