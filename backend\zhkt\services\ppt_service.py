# -*- coding: utf-8 -*-
import json
import os
import time
import traceback
import uuid
from typing import Dict, List, Any, Generator

import requests
from django.db import models  # 添加这一行导入

from ..config import PPT_API_URL, PPT_API_KEY
from ..entitys.ppt import PPTProject
from ..utils.deepseek_api import DeepSeekAPI
from ..utils.knowledge_utils import KnowledgeBaseAPI
from ..utils.temp_file_utils import clean_temp_file
# 导入通用临时文件工具
from ..utils.temp_file_utils import get_temp_filepath
from ..utils.document_utils import DocumentReader


class PPTService:
    """PPT生成服务类，提供PPT生成相关的业务逻辑"""
    
    # 存储生成任务基本信息的字典（简化版）
    _tasks = {}
    
    @classmethod
    def get_docmee_token(cls, user_id=None) -> Dict[str, Any]:
        """
        获取文多多API Token
        
        Args:
            user_id: 用户ID，可用于个性化token
            
        Returns:
            Dict[str, Any]: 包含token的字典
        """
        try:
            # 如果缓存中没有，调用API获取新token
            # 使用配置文件中的API地址和API密钥
            api_url = PPT_API_URL
            api_key = PPT_API_KEY
            
            # 重试机制
            max_retries = 3
            for retry_count in range(max_retries):
                try:
                    response = requests.post(
                        api_url,
                        headers={
                            'Api-Key': api_key,
                            'Content-Type': 'application/json'
                        },
                        json={
                            'uid': user_id,
                            'limit': 100  # 设置使用次数限制
                        },
                        timeout=8  # 设置超时时间
                    )
                    
                    if response.status_code != 200:
                        raise Exception(f"服务器响应错误: {response.status_code} {response.reason}")
                    
                    data = response.json()
                    
                    if data and data.get('code') == 0 and data.get('data') and data.get('data').get('token'):
                        token = data['data']['token']

                        return {
                            "token": token
                        }
                    else:
                        raise Exception(f"获取Token失败: {data.get('message', '未知错误')}")
                except Exception as e:
                    if retry_count >= max_retries - 1:
                        raise Exception(f"获取API Token失败，超出重试次数: {str(e)}")
                    
                    # 等待一段时间再重试
                    time.sleep(0.5)
            
            
                
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取文多多API Token失败: {str(e)}")
    
    @classmethod
    def get_ppt_projects(cls, user_id=None, subject_id=None, search_query=None, sort_option=None):
        """
        获取PPT项目列表
        
        Args:
            user_id: 用户ID，不提供则查询所有用户的项目
            subject_id: 学科ID，不提供则查询所有学科的项目
            search_query: 搜索关键词，标题和描述
            sort_option: 排序选项，可选值：recent(最近更新)、oldest(最早创建)、az(名称A-Z)、za(名称Z-A)
            
        Returns:
            QuerySet: PPT项目查询集
        """
        try:
            # 构建基础查询
            query = PPTProject.objects.filter(delete_at__isnull=True)
            
            # 按用户ID筛选
            if user_id:
                query = query.filter(user_id=user_id)
                
            # 按学科ID筛选
            if subject_id and subject_id != 'all':
                query = query.filter(subject_id=subject_id)
                
            # 搜索关键词
            if search_query:
                query = query.filter(
                    models.Q(title__icontains=search_query) | 
                    models.Q(description__icontains=search_query)
                )
                
            # 排序
            if sort_option == 'recent':
                query = query.order_by('-update_at')
            elif sort_option == 'oldest':
                query = query.order_by('create_at')
            elif sort_option == 'az':
                query = query.order_by('title')
            elif sort_option == 'za':
                query = query.order_by('-title')
            else:
                # 默认按更新时间降序
                query = query.order_by('-update_at')
            
            # 返回查询集，让视图层处理分页和数据转换    
            return query.select_related('subject', 'user')
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取PPT项目列表失败: {str(e)}")
    
    @classmethod
    def generate_async(cls, content=None, file=None, teaching_duration='45', 
                      teaching_style='balanced', specific_requirements='',
                      document_ids=None) -> str:
        """
        创建生成任务，返回任务ID
        
        Args:
            content: 教案文本内容
            file: 教案文件
            teaching_duration: 教学时长（分钟）
            teaching_style: 教学风格
            specific_requirements: 具体需求说明
            document_ids: 知识库文档ID列表
            
        Returns:
            str: 任务ID
        """
        # 生成唯一任务ID
        task_id = str(uuid.uuid4())
        
        # 初始化任务信息（简化版，只保存必要信息）
        cls._tasks[task_id] = {
            'content': content,
            'teaching_duration': teaching_duration,
            'teaching_style': teaching_style,
            'specific_requirements': specific_requirements,
            'document_ids': document_ids,
            'created_at': time.time()
        }
        
        # 如果是文件，需要提前读取文件内容
        if file:
            # 在主线程中读取文件内容，防止在工作线程中访问已关闭的文件
            file_content_text, file_name, file_extension = cls._read_file_content(file)
            cls._tasks[task_id]['file_data'] = {
                'content': file_content_text,
                'name': file_name,
                'extension': file_extension
            }
        
        return task_id
    
    @classmethod
    def _read_file_content(cls, file):
        """
        读取文件内容到内存中
        
        Args:
            file: 上传的文件对象
            
        Returns:
            tuple: (文件文本内容, 文件名, 文件扩展名)
        """
        # 获取文件名和扩展名
        file_name = file.name
        file_extension = os.path.splitext(file_name)[1].lower()
        
        # 使用通用方法获取临时文件路径
        temp_file_path = get_temp_filepath(file_extension)
        
        try:
            # 保存上传的文件到临时位置
            with open(temp_file_path, 'wb') as temp_f:
                for chunk in file.chunks():
                    temp_f.write(chunk)
            
            # 使用DocumentReader读取文件内容
            file_content_text = DocumentReader.read_document(temp_file_path)
            
        finally:
            # 确保临时文件被删除
            clean_temp_file(temp_file_path)
            
        return file_content_text, file_name, file_extension
    
    @classmethod
    def _convert_to_markdown(cls, title, slides):
        """
        将PPT内容转换为Markdown格式
        
        Args:
            title: PPT标题
            slides: 幻灯片内容列表
            
        Returns:
            str: Markdown格式的PPT内容
        """
        markdown = f"# {title}\n\n"
        
        for slide in slides:
            # 注意这里要添加两级标题，与前端格式保持一致
            markdown += f"## {slide['title']}\n\n"
            markdown += f"### {slide['title']}\n\n"
            
            for section in slide['sections']:
                markdown += f"#### {section['subtitle']}\n\n"
                if section['content']:
                    markdown += f"- {section['content']}\n\n"
                
        return markdown

    @classmethod
    def generate_from_text(cls, content: str, teaching_duration: str = '45', 
                           teaching_style: str = 'balanced', specific_requirements: str = '',
                           document_ids: List[str] = None) -> Dict[str, Any]:
        """
        根据教案文本内容创建PPT生成任务
        
        Args:
            content: 教案文本内容
            teaching_duration: 教学时长（分钟）
            teaching_style: 教学风格，可选值：balanced(平衡型)、theory(理论型)、practice(实践型)、interactive(互动型)
            specific_requirements: 具体需求说明
            document_ids: 知识库文档ID列表
            
        Returns:
            Dict[str, Any]: 包含任务ID
        """
        try:
            # 创建任务
            task_id = cls.generate_async(
                content=content,
                teaching_duration=teaching_duration,
                teaching_style=teaching_style,
                specific_requirements=specific_requirements,
                document_ids=document_ids
            )
            
            # 返回任务ID
            return {
                'task_id': task_id
            }
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"生成PPT失败: {str(e)}")
    
    @classmethod
    def generate_from_file(cls, file, teaching_duration: str = '45', 
                          teaching_style: str = 'balanced', specific_requirements: str = '',
                          document_ids: List[str] = None) -> Dict[str, Any]:
        """
        根据上传的教案文件创建PPT生成任务
        
        Args:
            file: 上传的教案文件
            teaching_duration: 教学时长（分钟）
            teaching_style: 教学风格
            specific_requirements: 具体需求说明
            document_ids: 知识库文档ID列表
            
        Returns:
            Dict[str, Any]: 包含任务ID
        """
        try:
            # 创建任务
            task_id = cls.generate_async(
                file=file,
                teaching_duration=teaching_duration,
                teaching_style=teaching_style,
                specific_requirements=specific_requirements,
                document_ids=document_ids
            )
            
            # 返回任务ID
            return {
                'task_id': task_id
            }
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"处理文件失败: {str(e)}")

    @classmethod
    def get_ppt_slides_count(cls, ppt_id, user_id=None):
        """
        获取文多多PPT的幻灯片数量
        
        Args:
            ppt_id: 文多多PPT ID
            user_id: 用户ID，用于获取token
            
        Returns:
            int: 幻灯片数量
        """
        try:
            # 获取文多多API Token
            token_data = cls.get_docmee_token(user_id=user_id)
            token = token_data.get('token')
            
            if not token:
                raise Exception("获取文多多API Token失败")
            
            # 调用文多多API下载PPT，通过响应获取幻灯片数量
            api_url = "https://open.docmee.cn/api/ppt/downloadPptx"
            
            # 请求参数
            request_data = {
                "id": ppt_id,
                "refresh": False
            }
            
            # 发送请求
            response = requests.post(
                api_url,
                headers={
                    'token': token,
                    'Content-Type': 'application/json'
                },
                json=request_data,
                timeout=30  # 设置超时时间
            )
            
            if response.status_code != 200:
                raise Exception(f"文多多API响应错误: {response.status_code} {response.reason}")
            
            data = response.json()
            
            if data and data.get('code') == 0 and data.get('data'):
                result = data['data']
                # 访问PPT文件下载链接，解析获取幻灯片数量
                file_url = result.get('fileUrl')
                
                if not file_url:
                    raise Exception("无法获取PPT文件链接")
                    
                # 请求文件链接以获取内容
                file_response = requests.get(file_url, timeout=30)
                
                if file_response.status_code != 200:
                    raise Exception(f"下载PPT文件失败: {file_response.status_code}")
                
                # 使用通用方法获取临时文件路径
                temp_file_path = get_temp_filepath('.pptx')
                slides_count = 0
                
                try:
                    with open(temp_file_path, 'wb') as temp:
                        temp.write(file_response.content)
                    
                    # 使用python-pptx库解析文件并获取幻灯片数量
                    from pptx import Presentation
                    prs = Presentation(temp_file_path)
                    slides_count = len(prs.slides)
                    
                    return slides_count
                finally:
                    # 确保临时文件被删除
                    clean_temp_file(temp_file_path)
            else:
                raise Exception(f"获取PPT信息失败: {data.get('message', '未知错误')}")
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取PPT幻灯片数量失败: {str(e)}")
    
    @classmethod
    def generate_ppt_stream(cls, task_id: str) -> Generator[str, None, None]:
        """
        流式生成PPT
        
        Args:
            task_id: 任务ID
            
        Returns:
            Generator: 生成内容的流，实时返回生成内容和最终markdown
        """
        # 检查任务是否存在
        if task_id not in cls._tasks:
            yield f"data: {json.dumps({'error': '任务不存在'})}\n\n"
            return
        
        try:
            # 初始化PPT内容生成器
            ppt_generator = PPTContentStreamGenerator(task_id, cls._tasks[task_id])
            
            # 开始流式生成
            for chunk in ppt_generator.stream_generate_ppt_content():
                yield chunk
                
            # 任务完成后清理任务数据
            if task_id in cls._tasks:
                del cls._tasks[task_id]
                
        except Exception as e:
            traceback.print_exc()
            # 返回错误消息
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
            
            # 错误情况下也应清理任务数据
            if task_id in cls._tasks:
                del cls._tasks[task_id]


class PPTContentStreamGenerator:
    """PPT内容流式生成器 - 负责具体的PPT生成逻辑"""
    
    def __init__(self, task_id: str, task_config: dict):
        self.task_id = task_id
        self.task_config = task_config
        self.ai_chat_api = DeepSeekAPI()
        self.knowledge_retrieval_api = KnowledgeBaseAPI()
        
        # 教学参数配置
        self.lesson_duration_minutes = task_config.get('teaching_duration', '45')
        self.teaching_style_preference = task_config.get('teaching_style', 'balanced')
        self.custom_requirements = task_config.get('specific_requirements', '')
        self.knowledge_document_ids = task_config.get('document_ids', [])
        
        # 获取教学内容
        self.teaching_content = self._extract_teaching_content_from_task()
        
    def _extract_teaching_content_from_task(self) -> str:
        """从任务中提取教学文本内容"""
        if 'file_data' in self.task_config:
            return self.task_config['file_data']['content']
        else:
            return self.task_config.get('content', '')
    
    def _resolve_knowledge_dataset_ids(self) -> List[str]:
        """解析获取知识库数据集ID列表"""
        if not self.knowledge_document_ids:
            return []
            
        try:
            from ..services.knowledge_service import KnowledgeService
            resolved_dataset_ids = KnowledgeService.get_datasets_by_documents(self.knowledge_document_ids)
            return resolved_dataset_ids if resolved_dataset_ids else []
        except Exception as e:
            raise Exception(f'解析知识库数据集ID失败: {str(e)}')
    
    def stream_generate_ppt_content(self) -> Generator[str, None, None]:
        """主要的PPT内容流式生成流程"""
        try:
            # 验证教学内容是否存在
            if not self.teaching_content:
                yield f"data: {json.dumps({'error': '无有效教学内容可处理'})}\n\n"
                return
            
            # 获取知识库数据集ID
            knowledge_dataset_ids = self._resolve_knowledge_dataset_ids()
            
            # 第一步：生成PPT章节大纲
            yield f"data: {json.dumps({'chunk': '开始生成PPT大纲...'})}\n\n"
            ppt_outline_structure = self._generate_ppt_outline_structure()
            
            if not ppt_outline_structure:
                yield f"data: {json.dumps({'error': '大纲生成失败'})}\n\n"
                return
            
            # 如果没有知识库，直接返回大纲markdown
            if not knowledge_dataset_ids:
                outline_markdown = self._convert_outline_structure_to_markdown(ppt_outline_structure)
                yield f"data: {json.dumps({'markdown': outline_markdown})}\n\n"
                return
            
            # 第二步：基于知识库生成PPT详细内容
            yield f"data: {json.dumps({'chunk': '开始生成PPT内容...'})}\n\n"
            complete_ppt_slides = []
            
            # 逐个处理每张幻灯片
            for slide_index, slide_outline in enumerate(ppt_outline_structure.get("ppt_outline", [])):
                slide_title = slide_outline["title"]
                slide_sections = slide_outline["sections"]
                
                # 处理单张幻灯片的内容生成
                slide_generation_result = self._generate_single_slide_content(
                    slide_title, slide_sections, slide_index, knowledge_dataset_ids
                )
                
                # 输出生成过程中的流式内容
                for stream_chunk in slide_generation_result['stream_chunks']:
                    yield stream_chunk
                
                complete_ppt_slides.append(slide_generation_result['slide_content'])
            
            # 第三步：生成最终的PPT markdown格式
            yield f"data: {json.dumps({'chunk': '正在生成最终PPT内容...'})}\n\n"
            ppt_main_title = ppt_outline_structure["ppt_outline"][0]["title"] if ppt_outline_structure["ppt_outline"] else "新建PPT"
            final_ppt_markdown = PPTService._convert_to_markdown(ppt_main_title, complete_ppt_slides)
            
            yield f"data: {json.dumps({'markdown': final_ppt_markdown})}\n\n"
            
        except Exception as e:
            raise Exception(f"PPT内容生成失败: {str(e)}")
    
    def _generate_ppt_outline_structure(self) -> dict:
        """生成PPT的章节大纲结构"""
        try:
            system_message = "你是一名专业的PPT大纲生成助手，擅长将复杂内容转化为简洁清晰的章节标题结构。"
            
            requirements_description = f"- 特定需求：{self.custom_requirements}" if self.custom_requirements else ''
            
            user_prompt = f"""
            根据用户提供的教案素材，生成逻辑清晰的PPT大纲标题；
            保持学术性与实用性的平衡；
            每个主标题配3-5个子章节；
            所有标题为短语形式，内容不能太泛性，需要精准信息（≤10字）；
            子章节的内容需要简单概括主要内容，内容不能太泛性，需要精准信息（≤30字）；
            保持学术严谨性；
            
            【教学参数】
            - 教学时长：{self.lesson_duration_minutes}分钟
            - 教学风格：{self.teaching_style_preference}
            {requirements_description}
            
            【教案内容】
            {self.teaching_content}
            
            请以JSON的格式返回，格式如下：
            {{
              "ppt_outline": [
                {{
                  "title": "主标题",
                  "sections": [
                    "子章节1",
                    "子章节2",
                    "子章节3"
                  ]
                }}
              ]
            }}
            """
            
            messages = [
                self.ai_chat_api.create_system_message(system_message),
                self.ai_chat_api.create_user_message(user_prompt)
            ]
            
            # 获取大纲生成的流式内容
            raw_outline_content = ""
            for chunk in self.ai_chat_api.chat_stream(messages):
                if chunk:
                    raw_outline_content += chunk.get('content', '')
            
            # 解析生成的JSON结构
            return self._parse_outline_json_structure(raw_outline_content)
            
        except Exception as e:
            raise Exception(f"生成PPT大纲结构失败: {str(e)}")
    
    def _parse_outline_json_structure(self, raw_content: str) -> dict:
        """解析大纲的JSON结构数据"""
        try:
            json_start_index = raw_content.find('{')
            json_end_index = raw_content.rfind('}') + 1
            if json_start_index >= 0 and json_end_index > json_start_index:
                json_string = raw_content[json_start_index:json_end_index]
                return json.loads(json_string)
            else:
                return {"ppt_outline": []}
        except json.JSONDecodeError:
            return {"ppt_outline": []}
    
    def _convert_outline_structure_to_markdown(self, outline_structure: dict) -> str:
        """将大纲结构转换为markdown格式"""
        markdown_content = ""
        for slide in outline_structure.get("ppt_outline", []):
            markdown_content += f"## {slide['title']}\n\n"
            for section in slide['sections']:
                markdown_content += f"### {section}\n\n"
        return markdown_content
    
    def _generate_single_slide_content(self, slide_title: str, slide_sections: List[str], 
                                     slide_index: int, knowledge_dataset_ids: List[str]) -> dict:
        """生成单张幻灯片的详细内容"""
        slide_content_structure = {
            "title": slide_title,
            "sections": []
        }
        
        stream_output_chunks = []
        
        try:
            # 处理幻灯片中的每个小节
            for section_index, section_title in enumerate(slide_sections):
                section_generation_result = self._generate_single_section_content(
                    slide_title, section_title, slide_index, section_index, knowledge_dataset_ids
                )
                
                slide_content_structure["sections"].append(section_generation_result['section_content'])
                stream_output_chunks.extend(section_generation_result['stream_chunks'])
            
            # 发送整张幻灯片完成的消息
            stream_output_chunks.append(f"data: {json.dumps({'slide_complete': {'slide': slide_index+1, 'title': slide_title}})}\n\n")
            
            return {
                'slide_content': slide_content_structure,
                'stream_chunks': stream_output_chunks
            }
            
        except Exception as e:
            error_message = f"生成幻灯片 {slide_title} 内容时出错: {str(e)}"
            stream_output_chunks.append(f"data: {json.dumps({'error': error_message, 'slide': slide_index+1})}\n\n")
            return {
                'slide_content': slide_content_structure,
                'stream_chunks': stream_output_chunks
            }
    
    def _generate_single_section_content(self, slide_title: str, section_title: str, slide_index: int, 
                                       section_index: int, knowledge_dataset_ids: List[str]) -> dict:
        """生成单个小节的详细内容"""
        stream_output_chunks = []
        
        try:
            # 从知识库检索相关内容
            knowledge_retrieval_results = self.knowledge_retrieval_api.retrieve(
                question=section_title,
                dataset_ids=knowledge_dataset_ids,
                document_ids=self.knowledge_document_ids,
                similarity_threshold=0.5
            )
            
            # 提取检索到的知识内容
            retrieved_knowledge_contents = []
            if "data" in knowledge_retrieval_results and "chunks" in knowledge_retrieval_results["data"]:
                for knowledge_item in knowledge_retrieval_results["data"]["chunks"]:
                    if "content" in knowledge_item:
                        retrieved_knowledge_contents.append(knowledge_item["content"])
            
            # 基于检索到的知识生成小节内容
            detailed_section_content = self._generate_detailed_section_text(
                slide_title, section_title, retrieved_knowledge_contents, 
                stream_output_chunks, slide_index, section_index
            )
            
            # 发送小节完成的消息
            stream_output_chunks.append(f"data: {json.dumps({'section_complete': {'slide': slide_index+1, 'section': section_index+1, 'title': slide_title, 'subtitle': section_title}})}\n\n")
            
            return {
                'section_content': {
                    "subtitle": section_title,
                    "content": detailed_section_content.strip()
                },
                'stream_chunks': stream_output_chunks
            }
            
        except Exception as e:
            error_message = f"生成小节 {section_title} 内容时出错: {str(e)}"
            stream_output_chunks.append(f"data: {json.dumps({'error': error_message, 'slide': slide_index+1, 'section': section_index+1})}\n\n")
            return {
                'section_content': {
                    "subtitle": section_title,
                    "content": ""
                },
                'stream_chunks': stream_output_chunks
            }
    
    def _generate_detailed_section_text(self, slide_title: str, section_title: str, 
                                      knowledge_contents: List[str], stream_chunks: List[str], 
                                      slide_index: int, section_index: int) -> str:
        """基于知识库内容生成详细的小节文本"""
        content_generation_prompt = f"""
        请基于以下资料，生成"{slide_title}-{section_title}"的PPT一页的内容。
        内容要求：
        1. 简明扼要，突出重点
        2. 学术性与实用性并重
        3. 符合教学情境
        4. 要点不超过200字
        5. 我只需要一句话不需要分点
        6. 不能给我其他无关的内容,比如注释、提示等
        
        【教学参数】
        - 教学风格：{self.teaching_style_preference}
        
        参考资料：
        {' '.join(knowledge_contents[:8])}
        """
        
        messages = [
            self.ai_chat_api.create_system_message("你是一名专业的知识要点提取助手，擅长提炼核心内容并组织成简洁有力的要点。"),
            self.ai_chat_api.create_user_message(content_generation_prompt)
        ]
        
        # 获取流式响应并生成内容
        generated_section_text = ""
        for chunk in self.ai_chat_api.chat_stream(messages):
            if chunk:
                chunk_content = chunk.get('content', '')
                generated_section_text += chunk_content
                # 发送流式内容给前端
                stream_chunks.append(f"data: {json.dumps({'chunk': chunk_content, 'slide': slide_index+1, 'section': section_index+1})}\n\n")
        
        return generated_section_text
