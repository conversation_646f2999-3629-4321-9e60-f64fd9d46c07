<template>
  <div class="course-dograde p-6">
    <!-- 作业信息 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="flex justify-between items-start">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ homework.title }}</h1>
          <p class="text-gray-600">{{ homework.description }}</p>
        </div>
        <div class="flex gap-3">
          <button 
            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center"
            @click="publishGrades"
          >
            <span class="material-icons mr-2">publish</span>
            发布成绩
          </button>
        </div>
      </div>

      <div class="grid grid-cols-4 gap-4 mt-6">
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="text-sm text-gray-500">总提交数</div>
          <div class="text-xl font-bold text-gray-800">{{ stats.totalSubmissions }}</div>
        </div>
        <div class="bg-green-50 rounded-lg p-4">
          <div class="text-sm text-gray-500">已批改</div>
          <div class="text-xl font-bold text-gray-800">{{ stats.gradedCount }}</div>
        </div>
        <div class="bg-yellow-50 rounded-lg p-4">
          <div class="text-sm text-gray-500">待批改</div>
          <div class="text-xl font-bold text-gray-800">{{ stats.pendingCount }}</div>
        </div>
        <div class="bg-purple-50 rounded-lg p-4">
          <div class="text-sm text-gray-500">平均分</div>
          <div class="text-xl font-bold text-gray-800">{{ stats.averageScore }}</div>
        </div>
      </div>
    </div>

    <!-- 提交列表 -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="p-6 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-medium text-gray-900">提交记录</h2>
          <div class="flex gap-3">
            <div class="relative">
              <input 
                type="text" 
                v-model="searchQuery"
                placeholder="搜索学生..." 
                class="w-64 border border-gray-300 rounded-md pl-9 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
            </div>
            <select 
              v-model="filterStatus"
              class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部状态</option>
              <option value="not_graded">未批改</option>
              <option value="graded">已批改</option>
            </select>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学生信息
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                提交时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                得分
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="submission in filteredSubmissions" :key="submission.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img class="h-10 w-10 rounded-full" :src="submission.student.avatar || defaultUserAvatar" :alt="submission.student.name">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ submission.student.name }}</div>
                    <div class="text-sm text-gray-500">{{ submission.student.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDate(submission.submitted_at) }}</div>
                <div class="text-xs text-gray-500">{{ getSubmissionStatus(submission) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="getStatusClass(submission.status)">
                  {{ getStatusText(submission.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm" :class="getScoreClass(submission.score)">
                  {{ submission.score || '待批改' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  v-if="submission.status !== 'graded'" 
                  @click="openGradeDialog(submission)"
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  批改
                </button>
                <button 
                  v-if="submission.status === 'graded'"
                  @click="viewGradeDetail(submission)"
                  class="text-gray-600 hover:text-gray-900"
                >
                  查看
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-700">
            共 <span class="font-medium">{{ total }}</span> 条记录
          </div>
          <div class="flex gap-2">
            <button 
              v-for="page in totalPages" 
              :key="page"
              @click="currentPage = page"
              class="px-3 py-1 rounded-md"
              :class="currentPage === page ? 'bg-blue-600 text-white' : 'text-gray-700 hover:bg-gray-100'"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批改对话框 -->
    <el-dialog 
      v-model="showGradeDialog" 
      title="作业批改" 
      width="80%"
      :close-on-click-modal="false"
      class="grade-dialog"
    >
      <div v-if="currentSubmission" class="space-y-6">
        <!-- 学生信息 -->
        <div class="flex items-center justify-between pb-4 border-b">
          <div class="flex items-center">
            <img 
              :src="currentSubmission.student.avatar || defaultUserAvatar" 
              :alt="currentSubmission.student.name"
              class="w-12 h-12 rounded-full mr-4"
            >
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ currentSubmission.student.name }}</h3>
              <p class="text-sm text-gray-500">{{ currentSubmission.student.email }}</p>
            </div>
          </div>
          <div class="text-sm text-gray-500">
            提交时间：{{ formatDate(currentSubmission.submitted_at) }}
          </div>
        </div>

        <!-- 题目列表 -->
        <div v-for="question in homework.questions" :key="question.id" class="border rounded-lg p-4">
          <div class="flex justify-between items-start mb-4">
            <div>
              <h4 class="text-base font-medium text-gray-900">{{ question.question_detail.title }}</h4>
              <div class="flex gap-2 mt-1">
                <span class="px-2 py-0.5 rounded-full text-xs"
                  :class="getQuestionTypeClass(question.question_detail.question_type)">
                  {{ getQuestionTypeText(question.question_detail.question_type) }}
                </span>
                <span class="text-sm text-gray-500">{{ question.score }}分</span>
              </div>
            </div>
          </div>

          <!-- 题目选项（仅选择题显示） -->
          <div v-if="['single_choice', 'multiple_choice'].includes(question.question_detail.question_type)" class="mb-4">
            <div class="text-sm font-medium text-gray-700 mb-2">选项：</div>
            <div class="space-y-2">
              <div v-for="option in question.question_detail.options" :key="option.id" 
                class="flex items-center p-2 rounded"
                :class="{
                  'bg-green-50': option.is_correct,
                  'bg-gray-50': !option.is_correct
                }"
              >
                <span class="text-sm text-gray-600">{{ option.content }}</span>
                <span v-if="option.is_correct" class="ml-2 text-xs text-green-600">(正确答案)</span>
              </div>
            </div>
          </div>

          <!-- 参考答案（非选择题） -->
          <div v-if="!['single_choice', 'multiple_choice'].includes(question.question_detail.question_type) && question.question_detail.answer" class="mb-4">
            <div class="text-sm font-medium text-gray-700 mb-2">参考答案：</div>
            <div class="bg-green-50 p-3 rounded">
              <div class="text-sm text-gray-700">{{ question.question_detail.answer }}</div>
            </div>
          </div>

          <!-- 学生答案 -->
          <div class="bg-gray-50 rounded p-4 mb-4">
            <div class="text-sm text-gray-700">学生答案：</div>
            <div class="mt-2" v-html="formatAnswer(question.id)"></div>
          </div>

          <!-- 评分区域 -->
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
              <label class="text-sm font-medium text-gray-700">得分：</label>
              <input 
                type="number" 
                v-model="questionScores[question.id]"
                :max="question.score"
                min="0"
                step="0.5"
                :disabled="isViewMode"
                class="w-24 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                :class="{ 'bg-gray-100 cursor-not-allowed': isViewMode }"
              >
              <span class="text-sm text-gray-500">/ {{ question.score }}分</span>
            </div>
          </div>
        </div>

        <!-- 总评 -->
        <div class="border rounded-lg p-4">
          <h4 class="text-base font-medium text-gray-900 mb-4">总评</h4>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">总分</label>
              <div class="text-2xl font-bold text-blue-600">{{ totalScore }}</div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">总评语</label>
              <textarea 
                v-model="overallComment"
                rows="3"
                :disabled="isViewMode"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                :class="{ 'bg-gray-100 cursor-not-allowed': isViewMode }"
                placeholder="输入总评语（选填）"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <button 
            @click="showGradeDialog = false"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            关闭
          </button>
          <button 
            v-if="!isViewMode"
            @click="saveGrade"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            保存
          </button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/date'
import defaultUserAvatar from '@/assets/images/avatars/user_avatar.jpg'
import { 
  getHomeworkGradingInfo, 
  getHomeworkSubmissions, 
  saveGradingResult, 
  publishHomeworkGrades 
} from '@/api/homework'

const props = defineProps({
  courseId: {
    type: String,
    required: true
  },
  homeworkId: {
    type: String,
    required: true
  }
})

// 作业数据
const homework = ref({
  id: '',
  title: '',
  description: '',
  questions: []
})

// 统计数据
const stats = ref({
  totalSubmissions: 0,
  gradedCount: 0,
  pendingCount: 0,
  averageScore: 0
})

// 提交记录
const submissions = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')
const filterStatus = ref('')

// 批改对话框
const showGradeDialog = ref(false)
const currentSubmission = ref(null)
const questionScores = ref({})
const overallComment = ref('')
const isViewMode = ref(false)

// 计算属性
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
const filteredSubmissions = computed(() => {
  let result = [...submissions.value]
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(s => 
      s.student.name.toLowerCase().includes(query) ||
      s.student.id.toLowerCase().includes(query)
    )
  }
  
  if (filterStatus.value) {
    result = result.filter(s => s.status === filterStatus.value)
  }
  
  return result
})

const totalScore = computed(() => {
  return Object.values(questionScores.value).reduce((sum, score) => sum + (Number(score) || 0), 0)
})

// 获取作业信息和提交记录
const fetchData = async () => {
  try {
    // 获取作业批改信息
    const gradingInfo = await getHomeworkGradingInfo(props.courseId, props.homeworkId)
    homework.value = gradingInfo.homework
    stats.value = gradingInfo.stats

    // 获取提交记录
    const submissionsData = await getHomeworkSubmissions(props.courseId, props.homeworkId, {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value,
      status: filterStatus.value
    })
    submissions.value = submissionsData.results
    total.value = submissionsData.total
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 打开批改对话框
const openGradeDialog = (submission, viewMode = false) => {
  currentSubmission.value = submission
  isViewMode.value = viewMode
  // 初始化评分数据
  questionScores.value = {}
  overallComment.value = ''
  
  if (submission.status === 'graded') {
    // 如果已批改，加载已有的评分数据
    const feedback = JSON.parse(submission.feedback || '{}')
    questionScores.value = feedback.scores || {}
    overallComment.value = feedback.overall || ''
  } else {
    // 如果是新批改，对选择题和判断题进行自动评分
    const answers = JSON.parse(submission.answers).answers
    homework.value.questions.forEach(question => {
      const questionType = question.question_detail.question_type
      const studentAnswer = answers.find(a => a.question_id === question.id)?.answer
      
      if (['single_choice', 'multiple_choice', 'true_false'].includes(questionType)) {
        let isCorrect = false
        
        if (questionType === 'true_false') {
          // 判断题：直接比较答案
          isCorrect = studentAnswer === question.question_detail.answer
        } else {
          // 选择题：从选项中找出正确答案
          const correctOptions = question.question_detail.options
            .filter(opt => opt.is_correct)
            .map(opt => opt.id)
          
          if (questionType === 'multiple_choice') {
            // 多选题：检查答案数组是否完全相同（顺序无关）
            isCorrect = Array.isArray(studentAnswer) && 
                       Array.isArray(correctOptions) &&
                       studentAnswer.length === correctOptions.length &&
                       studentAnswer.every(id => correctOptions.includes(id))
          } else {
            // 单选题：直接比较答案
            isCorrect = studentAnswer === correctOptions[0]
          }
        }
        
        // 如果答案正确，给满分
        questionScores.value[question.id] = isCorrect ? question.score : 0
      }
    })
  }
  
  showGradeDialog.value = true
}

// 查看批改详情
const viewGradeDetail = (submission) => {
  openGradeDialog(submission, true)
}

// 保存批改结果
const saveGrade = async () => {
  try {
    const feedback = {
      scores: questionScores.value,
      overall: overallComment.value
    }
    
    await saveGradingResult(
      props.courseId,
      props.homeworkId,
      currentSubmission.value.id,
      feedback
    )
    
    showGradeDialog.value = false
    ElMessage.success('保存成功')
    await fetchData() // 刷新数据
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 发布成绩
const publishGrades = async () => {
  try {
    await publishHomeworkGrades(props.courseId, props.homeworkId)
    ElMessage.success('发布成功')
    await fetchData() // 刷新数据
  } catch (error) {
    console.error('发布失败:', error)
    ElMessage.error('发布失败')
  }
}

// 工具方法
const getStatusClass = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'submitted':
      return 'bg-blue-100 text-blue-800'
    case 'late':
      return 'bg-red-100 text-red-800'
    case 'graded':
      return 'bg-green-100 text-green-800'
    case 'overdue':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待提交'
    case 'submitted':
      return '已提交'
    case 'late':
      return '迟交'
    case 'graded':
      return '已批改'
    case 'overdue':
      return '已逾期'
    default:
      return status
  }
}

const getScoreClass = (score) => {
  if (score === null || score === undefined) return 'text-gray-500'
  if (score >= 90) return 'text-green-600 font-semibold'
  if (score >= 60) return 'text-blue-600'
  return 'text-red-600'
}

const getQuestionTypeClass = (type) => {
  switch (type) {
    case 'single_choice':
      return 'bg-blue-100 text-blue-800'
    case 'multiple_choice':
      return 'bg-indigo-100 text-indigo-800'
    case 'true_false':
      return 'bg-green-100 text-green-800'
    case 'fill_blank':
      return 'bg-yellow-100 text-yellow-800'
    case 'short_answer':
      return 'bg-orange-100 text-orange-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getQuestionTypeText = (type) => {
  switch (type) {
    case 'single_choice':
      return '单选题'
    case 'multiple_choice':
      return '多选题'
    case 'true_false':
      return '判断题'
    case 'fill_blank':
      return '填空题'
    case 'short_answer':
      return '简答题'
    default:
      return type
  }
}

const formatAnswer = (questionId) => {
  const questions = homework.value.questions
  const answers = currentSubmission.value.answers
  const studentAnswers = JSON.parse(answers).answers
  if (Array.isArray(studentAnswers)) {
    const findList = studentAnswers.filter((item) => { return item.question_id == questionId })
    if (findList.length > 0) {
      const question = questions.find(q => q.id === questionId)
      const questionType = question.question_detail.question_type
      
      // 如果是选择题，需要转换选项ID为选项内容
      if (['single_choice', 'multiple_choice'].includes(questionType)) {
        const studentAnswer = findList[0].answer
        const options = question.question_detail.options
        
        // 如果是多选题，答案可能是数组
        if (Array.isArray(studentAnswer)) {
          return studentAnswer.map(answerId => {
            const option = options.find(opt => opt.id === answerId)
            return option ? option.content : answerId
          }).join('、')
        } else {
          // 单选题
          const option = options.find(opt => opt.id === studentAnswer)
          return option ? option.content : studentAnswer
        }
      }
      
      // 非选择题直接返回答案
      return findList[0].answer
    }
  }
  return ''
}

const getSubmissionStatus = (submission) => {
  if (submission.status === 'late') return '迟交'
  return '按时提交'
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script> 

<style scoped>
.grade-dialog :deep(.el-dialog__body) {
  max-height: calc(90vh - 150px);
  overflow-y: auto;
}
</style> 