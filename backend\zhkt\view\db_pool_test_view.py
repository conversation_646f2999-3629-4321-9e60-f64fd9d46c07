from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from django.db import connection
from zhkt.db_pool import get_pool_stats

class DBPoolTestView(APIView):
    """数据库连接池测试视图"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        """获取数据库连接池状态"""
        try:
            # 获取连接池统计信息
            stats = get_pool_stats()
            
            # 执行一个简单的测试查询
            with connection.cursor() as cursor:
                cursor.execute('SELECT 1')
                test_result = cursor.fetchone()[0]
            
            return Response({
                'status': 'success',
                'message': '数据库连接测试成功',
                'test_query_result': test_result,
                'pool_stats': stats
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'数据库连接测试失败: {str(e)}',
                'pool_stats': get_pool_stats()  # 即使测试查询失败，也尝试获取连接池状态
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 