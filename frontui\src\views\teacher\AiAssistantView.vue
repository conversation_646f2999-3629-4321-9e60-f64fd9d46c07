<template>
  <TeacherLayout
    pageTitle="AI问答"
    :userName="teacherStore.teacherData.name"
    :userAvatar="teacherStore.teacherData.avatar"
    :userPoints="teacherStore.teacherData.points"
  >
    <div class="grid grid-cols-12 gap-4">
      <!-- 左侧：对话历史 -->
      <el-card class="col-span-3 left-panel-card">
        <template #header>
          <div class="flex justify-between items-center">
            <button class="start-conversation-btn" @click="createNewConversation">
              <span class="mr-1" style="display:inline-flex;align-items:center; margin-right: 9px;">
                <svg width="22" height="22" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.10999 27C8.92999 27 8.76001 26.96 8.60001 26.9C8.43001 26.83 8.29 26.74 8.16 26.61C8.03 26.49 7.94 26.3499 7.87 26.1899C7.79999 26.0299 7.76001 25.8599 7.76001 25.6899L7.73001 23.04C7.34001 22.98 6.95001 22.8799 6.57001 22.7599C6.19001 22.6299 5.83001 22.48 5.48001 22.29C5.13001 22.1 4.79999 21.88 4.48999 21.63C4.17999 21.39 3.89 21.1199 3.63 20.82C3.37 20.52 3.13999 20.21 2.92999 19.87C2.72999 19.53 2.56001 19.18 2.42001 18.82C2.28001 18.45 2.17001 18.07 2.10001 17.69C2.03001 17.3 2 16.92 2 16.53V9.46995C2 9.03995 2.04 8.61995 2.12 8.19995C2.21 7.77995 2.34 7.36995 2.5 6.96995C2.67 6.57995 2.88 6.19995 3.12 5.84995C3.36 5.48995 3.64001 5.15995 3.95001 4.85995C4.26001 4.55995 4.59999 4.28995 4.95999 4.04995C5.32999 3.80995 5.70999 3.60995 6.10999 3.44995C6.51999 3.27995 6.94 3.15995 7.37 3.07995C7.79999 2.98995 8.23001 2.94995 8.67001 2.94995H13.3C13.46 2.94995 13.61 2.97995 13.76 3.03995C13.9 3.09995 14.03 3.17995 14.14 3.28995C14.25 3.39995 14.33 3.51995 14.39 3.65995C14.45 3.79995 14.48 3.94995 14.48 4.09995C14.48 4.25995 14.45 4.39995 14.39 4.54995C14.33 4.68995 14.25 4.80995 14.14 4.91995C14.03 5.02995 13.9 5.10995 13.76 5.16995C13.61 5.22995 13.46 5.25995 13.3 5.25995H8.67001C8.38001 5.25995 8.09999 5.27995 7.82999 5.33995C7.54999 5.38995 7.27999 5.46995 7.01999 5.57995C6.75999 5.67995 6.50999 5.80995 6.26999 5.96995C6.03999 6.11995 5.82 6.29995 5.62 6.48995C5.42 6.68995 5.23999 6.89995 5.07999 7.12995C4.92999 7.35995 4.78999 7.59995 4.67999 7.85995C4.57999 8.10995 4.49 8.37995 4.44 8.64995C4.38 8.91995 4.35999 9.18995 4.35999 9.46995V16.53C4.35999 16.81 4.38 17.08 4.44 17.36C4.5 17.63 4.58 17.9 4.69 18.16C4.8 18.42 4.93 18.67 5.09 18.9C5.25 19.13 5.43001 19.3499 5.64001 19.5499C5.84001 19.75 6.05999 19.92 6.29999 20.08C6.53999 20.24 6.79 20.37 7.06 20.47C7.32 20.58 7.6 20.66 7.88 20.72C8.16001 20.77 8.44001 20.7999 8.73001 20.7999C8.91001 20.7999 9.08 20.83 9.25 20.9C9.41 20.97 9.55999 21.0599 9.67999 21.18C9.80999 21.3099 9.91001 21.45 9.98001 21.61C10.05 21.77 10.08 21.94 10.09 22.11L10.1 23.74L13.08 21.61C13.84 21.07 14.69 20.7999 15.63 20.7999H19.32C19.61 20.7999 19.89 20.77 20.16 20.72C20.44 20.67 20.71 20.59 20.97 20.4799C21.23 20.3699 21.48 20.24 21.72 20.09C21.95 19.94 22.17 19.76 22.37 19.57C22.57 19.3699 22.75 19.16 22.91 18.93C23.07 18.7 23.2 18.46 23.31 18.2C23.41 17.95 23.5 17.68 23.55 17.41C23.61 17.14 23.63 16.87 23.63 16.59V12.94C23.63 12.79 23.66 12.64 23.72 12.5C23.78 12.36 23.87 12.23 23.98 12.13C24.09 12.02 24.22 11.93 24.36 11.88C24.51 11.82 24.66 11.79 24.82 11.79C24.97 11.79 25.12 11.82 25.27 11.88C25.41 11.93 25.54 12.02 25.65 12.13C25.76 12.23 25.85 12.36 25.91 12.5C25.97 12.64 26 12.79 26 12.94V16.59C26 17.02 25.95 17.44 25.87 17.86C25.78 18.28 25.66 18.69 25.49 19.08C25.32 19.48 25.11 19.8499 24.87 20.2099C24.63 20.57 24.35 20.9 24.04 21.2C23.73 21.5 23.39 21.7699 23.03 22.0099C22.67 22.2499 22.28 22.45 21.88 22.61C21.47 22.77 21.06 22.9 20.63 22.9799C20.2 23.07 19.76 23.11 19.32 23.11H16.4C15.47 23.11 14.62 23.3799 13.86 23.9199L9.91 26.74C9.67 26.91 9.39999 27 9.10999 27Z" fill="currentColor"></path><path d="M24.6805 5.14453H18.1874C17.5505 5.14453 17.0342 5.66086 17.0342 6.29778C17.0342 6.9347 17.5505 7.45102 18.1874 7.45102H24.6805C25.3175 7.45102 25.8338 6.9347 25.8338 6.29778C25.8338 5.66086 25.3175 5.14453 24.6805 5.14453Z" fill="currentColor"></path><path d="M22.6137 3.1804C22.6137 2.52848 22.0852 2 21.4333 2C20.7814 2 20.2529 2.52848 20.2529 3.1804V9.4168C20.2529 10.0687 20.7814 10.5972 21.4333 10.5972C22.0852 10.5972 22.6137 10.0687 22.6137 9.4168V3.1804Z" fill="currentColor"></path></svg>
              </span>
              开启新对话
            </button>
          </div>
        </template>
        
        <div class="conversation-history h-[calc(100vh-16rem)] overflow-y-auto">
          <template v-for="(list, group) in groupedConversations" :key="group">
            <div v-if="list.length" class="conversation-group">
              <div class="group-title text-xs text-gray-400 px-3 py-2">{{ group }}</div>
              <div v-for="(conversation, index) in list" :key="conversation.id"
                   class="conversation-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
                   :class="{ 'active': currentConversationId === conversation.id }"
                   @click="selectConversation(conversation)">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-800 conversation-title-ellipsis">
                    {{ (conversation.firstUserMessage && conversation.firstUserMessage.length > 0) ? conversation.firstUserMessage : '新对话' }}
                  </span>
                  <el-dropdown trigger="click" @command="handleCommand($event, conversation)">
                    <el-button type="default" circle class="more-btn" size="small">
                      <el-icon><More /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="edit">
                          <el-icon class="mr-2"><Edit /></el-icon>编辑标题
                        </el-dropdown-item>
                        <el-dropdown-item command="delete" divided>
                          <el-icon class="mr-2"><Delete /></el-icon>删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </template>
        </div>
      </el-card>

      <!-- 右侧：对话框 -->
       <div class="col-span-9">
        <el-card class="col-span-9 right-panel-card">
          <!-- 标题 header slot，只有有消息时才显示 -->
          <template #header>
            <div v-if="currentMessages.length > 0" class="flex justify-between items-center">
              <div style="text-align: center; width: 100%; margin-bottom: 30px;">
                <span class="font-medium" style="font-weight: bold; font-size: 16px;">
                  {{ (currentMessages.length > 0 && currentMessages[0].type === 'user') ? currentMessages[0].content : '新对话' }}
                </span>
              </div>
            </div>
          </template>
          <!-- 对话内容区，只有有消息时才显示 -->
          <div v-if="currentMessages.length > 0" class="conversation-content bg-gray-50 p-4 space-y-4" ref="messageContainer" style="margin-bottom: 180px;">
            <div v-for="(message, index) in currentMessages" :key="index" 
                class="flex items-start"
                :class="{ 'justify-end': message.type === 'user' }">
              <!-- AI头像 -->
              <div v-if="message.type === 'ai'" class="avatar-ai-center flex-shrink-0 mr-3">
                <svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 1.8rem; height: 1.8rem; vertical-align: middle;">
                  <path id="path" d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12.3018 21.623 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z" fill-rule="nonzero" fill="#4D6BFE"></path>
                </svg>
              </div>
              
              <!-- 消息内容 -->
              <div :class="[
                'rounded-lg p-3 shadow-sm relative',
                message.type === 'ai' ? 'bg-white' : 'bg-blue-100'
              ]">
                <!-- 新增思考过程区域 -->
                <div v-if="message.type === 'ai' && message.thinkingProcess" class="thinking-process">
                  <div class="thinking-header" @click="toggleThinkingProcess(message)">
                    <span>思考过程</span>
                    <el-icon><ArrowDown v-if="!message.isThinkingExpanded" /><ArrowUp v-else /></el-icon>
                  </div>
                  <div v-show="message.isThinkingExpanded" class="thinking-content">
                    <div v-html="md.render(message.thinkingProcess)" class="markdown-content"></div>
                    <span v-if="message.isGenerating" class="typing-dots">
                      <!-- <span class="dot"></span>
                      <span class="dot"></span>
                      <span class="dot"></span> -->
                    </span>
                  </div>
                </div>

                <div v-if="message.type === 'ai'" class="absolute right-2 bottom-2">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="copyToClipboard(message.content)"
                    class="copy-btn"
                  >
                    <el-icon style="margin-right: 4px;"><CopyDocument /></el-icon>
                    复制
                  </el-button>
                </div>
                <p :class="[
                  'text-sm',
                  message.type === 'ai' ? 'text-gray-800' : 'text-gray-800'
                ]">
                  <template v-if="message.type === 'ai' && message.isGenerating">
                    <div v-html="md.render(message.displayContent)" class="markdown-content"></div>
                    <span class="typing-dots">
                      <span class="dot"></span>
                      <span class="dot"></span>
                      <span class="dot"></span>
                    </span>
                  </template>
                  <template v-else>
                    <div v-html="md.render(message.content)" class="markdown-content"></div>
                  </template>
                </p>
              </div>
              
              <!-- 用户头像 -->
              <div v-if="message.type === 'user'" class="flex-shrink-0 ml-3">
                <el-avatar :size="40" :src="teacherStore.teacherData.avatar" />
              </div>
            </div>
          </div>
          <!-- 输入区域始终显示 -->
          <div v-if="currentMessages.length === 0" class="ai-welcome-box" style="text-align:center; margin: 150px 0 0 0;">
            <div class="ai-welcome-title" style="font-size: 1.6rem; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; color: #000;">
              <svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 3.5rem; height: 3.5rem; margin-right: 0.7rem; vertical-align: middle;">
                <path id="path" d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12.3018 21.623 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z" fill-rule="nonzero" fill="#4D6BFE"></path>
              </svg>
              我是 DeepSeek，很高兴见到你！
            </div>
            <div class="ai-welcome-desc" style="color: #333; font-size: 0.9rem; margin-bottom: 45px;">
              我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~
            </div>
          </div>
          <div 
            class="input-area-container"
            :class="{ 'input-area-new-chat': currentMessages.length === 0 }"
          >
          <!-- <div class="downBox" v-if="currentMessages.length > 0">
            <ArrowDown 
              @click="scrollToBottom"
              style="width: 2em; height: 2em; color: #262626; border: 1px solid #e5e7eb; border-radius: 50%; padding: 5px; cursor: pointer;"
            />
          </div> -->
            <div class="input-area-box">
              <span class="input-placeholder" v-if="!inputMessage">给 DeepSeek 发送消息</span>
              <textarea
                v-model="inputMessage"
                class="input-textarea"
                rows="1"
                @keyup.enter="handleSendClick"
                placeholder=""
              ></textarea>
            </div>
            <div class="input-bottom-row">
              <div class="input-functions">
                <el-button
                  v-for="func in functions"
                  :key="func.type"
                  :type="selectedFunctions.includes(func.type) ? 'primary' : 'default'"
                  size="small"
                  @click="toggleFunction(func.type)"
                  class="min-w-fit"
                >
                  <span v-if="func.type === 'deep'" class="mr-1" style="display:inline-flex;align-items:center;">
                    <svg width="17" height="17" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.656 17.344c-1.016-1.015-1.15-2.75-.313-4.925.325-.825.73-1.617 1.205-2.365L3.582 10l-.033-.054c-.5-.799-.91-1.596-1.206-2.365-.836-2.175-.703-3.91.313-4.926.56-.56 1.364-.86 2.335-.86 1.425 0 3.168.636 4.957 1.756l.053.034.053-.034c1.79-1.12 3.532-1.757 4.957-1.757.972 0 1.776.3 2.335.86 1.014 1.015 1.148 2.752.312 4.926a13.892 13.892 0 0 1-1.206 2.365l-.034.054.034.053c.5.8.91 1.596 1.205 2.365.837 2.175.704 3.911-.311 4.926-.56.56-1.364.861-2.335.861-1.425 0-3.168-.637-4.957-1.757L10 16.415l-.053.033c-1.79 1.12-3.532 1.757-4.957 1.757-.972 0-1.776-.3-2.335-.86zm13.631-4.399c-.187-.488-.429-.988-.71-1.492l-.075-.132-.092.12a22.075 22.075 0 0 1-3.968 3.968l-.12.093.132.074c1.308.734 2.559 1.162 3.556 1.162.563 0 1.006-.138 1.298-.43.3-.3.436-.774.428-1.346-.008-.575-.159-1.264-.449-2.017zm-6.345 1.65l.058.042.058-.042a19.881 19.881 0 0 0 4.551-4.537l.043-.058-.043-.058a20.123 20.123 0 0 0-2.093-2.458 19.732 19.732 0 0 0-2.458-2.08L10 5.364l-.058.042A19.883 19.883 0 0 0 5.39 9.942L5.348 10l.042.059c.631.874 1.332 1.695 2.094 2.457a19.74 19.74 0 0 0 2.458 2.08zm6.366-10.902c-.293-.293-.736-.431-1.298-.431-.998 0-2.248.429-3.556 1.163l-.132.074.12.092a21.938 21.938 0 0 1 3.968 3.968l.092.12.074-.132c.282-.504.524-1.004.711-1.492.29-.753.442-1.442.45-2.017.007-.572-.129-1.045-.429-1.345zM3.712 7.055c.202.514.44 1.013.712 1.493l.074.13.092-.119a21.94 21.94 0 0 1 3.968-3.968l.12-.092-.132-.074C7.238 3.69 5.987 3.262 4.99 3.262c-.563 0-1.006.138-1.298.43-.3.301-.436.774-.428 1.346.007.575.159 1.264.448 2.017zm0 5.89c-.29.753-.44 1.442-.448 2.017-.008.572.127 1.045.428 1.345.293.293.736.431 1.298.431.997 0 2.247-.428 3.556-1.162l.131-.074-.12-.093a21.94 21.94 0 0 1-3.967-3.968l-.093-.12-.074.132a11.712 11.712 0 0 0-.71 1.492z" :fill="selectedFunctions.includes(func.type) ? '#4d6bfe' : '#58aaff'" :stroke="selectedFunctions.includes(func.type) ? '#4d6bfe' : '#58aaff'" stroke-width=".1"></path><path d="M10.706 11.704A1.843 1.843 0 0 1 8.155 10a1.845 1.845 0 1 1 2.551 1.704z" :fill="selectedFunctions.includes(func.type) ? '#4d6bfe' : '#58aaff'" :stroke="selectedFunctions.includes(func.type) ? '#4d6bfe' : '#58aaff'" stroke-width=".2"></path></svg>
                  </span>
                  <span v-else-if="func.type === 'web'" class="mr-1" style="display:inline-flex;align-items:center;">
                    <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="10" cy="10" r="9" :stroke="selectedFunctions.includes(func.type) ? '#4d6bfe' : '#58aaff'" stroke-width="1.8"></circle><path d="M10 1c1.657 0 3 4.03 3 9s-1.343 9-3 9M10 19c-1.657 0-3-4.03-3-9s1.343-9 3-9M1 10h18" :stroke="selectedFunctions.includes(func.type) ? '#4d6bfe' : '#58aaff'" stroke-width="1.8"></path></svg>
                  </span>
                  <el-icon v-else class="mr-1" style="font-size:18px;"><component :is="func.icon" /></el-icon>
                  {{ func.label }}
                </el-button>
              </div>
              <div>
                <button class="input-btn attachment-btn" @click="handleAttachmentClick">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#444" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                    <g transform="rotate(-45 12 12)">
                      <path d="M21.44 11.05l-8.49 8.49a5.5 5.5 0 0 1-7.78-7.78l9.19-9.19a3.5 3.5 0 0 1 4.95 4.95l-9.19 9.19a1.5 1.5 0 0 1-2.12-2.12l8.49-8.49" />
                    </g>
                  </svg>
                </button>
                <template v-if="isAIThinking">
                  <button class="input-btn send-btn custom-send-btn" @click="stopAIThinking">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="12" fill="#4D6BFE"/>
                      <rect x="8" y="8" width="8" height="8" rx="2" fill="#fff"/>
                    </svg>
                  </button>
                </template>
                <template v-else>
                  <el-tooltip
                    :content="!inputMessage.trim() ? '请输入你的问题' : ''"
                    placement="top"
                    :disabled="!!inputMessage.trim()"
                  >
                    <button 
                      class="input-btn send-btn custom-send-btn"
                      :class="{ 'disabled-send-btn': !inputMessage.trim() }"
                      @click="handleSendClick"
                      :disabled="!inputMessage.trim()"
                    >
                      <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="12" fill="#4D6BFE"/>
                        <g transform="rotate(-90 12 12)">
                          <path d="M8 12h8M14 8l4 4-4 4" stroke="#fff" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                      </svg>
                    </button>
                  </el-tooltip>
                </template>
              </div>
            </div>
          </div>
          <div class="footai">内容由 AI 生成，请仔细甄别</div>
        </el-card>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useTeacherStore } from '@/stores/teacher'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import {
  Plus,
  Check,
  Share,
  More,
  Avatar,
  Paperclip,
  Position,
  Star,
  Lightning,
  Operation,
  Monitor,
  Connection,
  Reading,
  DataLine,
  ShoppingCart,
  Cpu,
  Delete,
  Edit,
  CopyDocument,
  ArrowDown,
  ArrowUp
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getChatList,
  createChat,
  getChatMessages,
  sendMessage,
  getAIResponse,
  deleteChat,
  updateChatTitle
} from '@/api/aiassist'
import { differenceInDays } from 'date-fns'

// 引入学生数据存储
const teacherStore = useTeacherStore()

onMounted(() => {
  setTimeout(() => {
    const btn = document.querySelector('.sidebar-toggle-btn')
    if (btn) btn.click()
  }, 100)
})

// 功能列表
const functions = [
  { type: 'deep', label: '深度思考', icon: 'Cpu' },
  { type: 'web', label: '联网搜索', icon: 'Search' },
  { type: 'knowledge', label: '知识库', icon: 'Reading' },
  // { type: 'python', label: 'Python课程', icon: 'DataLine' },
  // { type: 'ecommerce', label: '电商课程', icon: 'ShoppingCart' },
  // { type: 'ai', label: 'AI课程', icon: 'Cpu' }
]

// 选中的功能
const selectedFunctions = ref(['deep'])
// const selectedFunctions = ref([])

// 添加模型选项
const modelOptions = [
  { value: 'deepseek', label: 'DeepSeek' },
  { value: 'gpt4', label: 'GPT-4' },
  { value: 'claude', label: 'Claude' },
  { value: 'gemini', label: 'Gemini' }
]

// 选中的模型
const selectedModel = ref('deepseek')

// 对话列表
const conversations = ref([])

// 当前对话ID
const currentConversationId = ref(null)
// 当前对话
const currentConversation = ref(null)
// 当前消息列表
const currentMessages = ref([])
// 输入消息
const inputMessage = ref('')

// AI流式回复的reader对象（用于中断）
const aiStreamReader = ref(null)

// AI流式回复的中断标志
let aiShouldStop = false

// 消息容器引用
const messageContainer = ref(null)

// 初始化 markdown-it
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {}
    }
    return '' // 使用默认的转义
  }
})

// 滚动到底部
const scrollToBottom = () => {
  if (messageContainer.value) {
    messageContainer.value.scrollTop = messageContainer.value.scrollHeight
  }
}

// 获取对话列表
const fetchConversations = async () => {
  try {
    let allConvs = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      const data = await getChatList(page);
      if (!data.results || data.results.length === 0) {
        hasMore = false;
        break;
      }

      // 获取当前页的会话基本信息
      const currentPageConvs = data.results.map(chat => ({
        id: chat.id,
        title: chat.title || '新对话',
        time: new Date(chat.created_at).toLocaleString(),
        messageCount: chat.message_count,
        firstUserMessage: '', // 先占位，后面补充
        allUserMessages: []   // 新增：所有用户消息内容
      }));

      allConvs = [...allConvs, ...currentPageConvs];
      
      // 检查是否还有下一页
      hasMore = data.next !== null && data.next !== undefined;
      page++;
    }

    // 并发获取每个会话的所有用户消息
    await Promise.all(allConvs.map(async (conv) => {
      try {
        const msgs = await getChatMessages(conv.id);
        const msgList = msgs.results ? msgs.results : msgs;
        const userMsgs = msgList.filter(m => m.role === 'USER');
        conv.firstUserMessage = userMsgs.length > 0 ? userMsgs[0].content : '';
        conv.allUserMessages = userMsgs.map(m => m.content);
      } catch (e) {
        conv.firstUserMessage = '';
        conv.allUserMessages = [];
      }
    }));

    // 只保留有用户消息的会话
    conversations.value = allConvs.filter(conv => conv.firstUserMessage && conv.firstUserMessage.length > 0);
  } catch (error) {
    ElMessage.error('获取对话列表失败');
    console.error(error);
  }
}

// 获取对话消息
const fetchMessages = async (chatId) => {
  try {
    const data = await getChatMessages(chatId)
    const convert_data = data.results?data.results:data
    currentMessages.value = convert_data.map(msg => ({
      id: msg.id,
      type: msg.role === 'USER' ? 'user' : 'ai',
      content: msg.content,
      timestamp: msg.created_at
    }))
  } catch (error) {
    ElMessage.error('获取消息失败')
    console.error(error);
  }
}

// 创建新对话
const createNewConversation = async () => {
  // 清空当前对话和消息
  currentConversationId.value = null;
  currentConversation.value = null;
  currentMessages.value = [];
  inputMessage.value = '';
}

// 选择对话
const selectConversation = async (conversation) => {
  currentConversationId.value = conversation.id
  currentConversation.value = conversation
  await fetchMessages(conversation.id)
}

// 发送消息
const sendAIMessage = async () => {
  aiShouldStop = false;
  if (!inputMessage.value.trim()) return;
  
  let currentChat = null;
  if (!currentConversationId.value) {
    // 只在实际发送消息时创建新会话
    try {
      currentChat = await createChat({
        title: '新对话',
        status: 'active'
      });
      currentConversationId.value = currentChat.id;
      currentConversation.value = currentChat;
      conversations.value.unshift({
        id: currentChat.id,
        title: currentChat.title,
        time: new Date(currentChat.created_at).toLocaleString(),
        messageCount: 0,
        firstUserMessage: inputMessage.value
      });
    } catch (error) {
      ElMessage.error('创建对话失败');
      return;
    }
  }

  try {
    // 添加用户消息到界面
    const userMessage = {
      type: 'user',
      content: inputMessage.value
    };
    currentMessages.value.push(userMessage);

    // 清空输入框
    inputMessage.value = '';

    // 立即滚动到底部
    await nextTick()
    scrollToBottom()

    // 创建AI消息占位
    const aiMessage = {
      type: 'ai',
      content: '',
      displayContent: '',
      isGenerating: true,
      thinkingProcess: '', // 新增思考过程字段
      isThinkingExpanded: true // 新增思考过程展开状态
    }
    currentMessages.value.push(aiMessage)

    // 再次滚动到底部
    await nextTick()
    scrollToBottom()

    // 获取流式AI回复
    const response = await getAIResponse({
      chat: currentConversationId.value,
      content: userMessage.content,
      model: selectedFunctions.value.includes('deep') ? 'deepseek-reasoner' : 'deepseek-chat',
      selectedFuncValues: selectedFunctions.value
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    if (!response.body) {
      throw new Error('Response body is not available')
    }

    const reader = response.body.getReader()
    aiStreamReader.value = reader // 赋值ref
    const decoder = new TextDecoder()

    // 设置超时时间（180秒）
    const timeout = setTimeout(() => {
      reader.cancel()
      ElMessage.error('请求超时，请重试')
    }, 180000)

    try {
      let firstResponse = true
      while (true) {
        if (aiShouldStop) break;
        const { value, done } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)

        // 处理SSE格式的响应
        const lines = chunk.split('\n')
        for (const line of lines) {
          if (aiShouldStop) break;
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6) // 移除 "data: " 前缀
              const data = JSON.parse(jsonStr)
              
              // 处理思考过程
              if (data.reasoning) {
                // 逐个字符显示思考过程
                const chars = data.reasoning.split('')
                for (const char of chars) {
                  if (aiShouldStop) break;
                  await new Promise(resolve => setTimeout(resolve, 5)) // 每个字符延迟5ms
                  aiMessage.thinkingProcess = (aiMessage.thinkingProcess || '') + char
                  // 强制更新视图
                  currentMessages.value = [...currentMessages.value]
                }
              }
              
              // 处理内容更新
              if (data.content) {
                // 使用Vue的响应式更新
                aiMessage.content = aiMessage.content + data.content
                // 逐个字符显示
                const chars = data.content.split('')
                for (const char of chars) {
                  if (aiShouldStop) break;
                  await new Promise(resolve => setTimeout(resolve, 5)) // 每个字符延迟5ms
                  aiMessage.displayContent = aiMessage.displayContent + char
                  // 强制更新视图
                  currentMessages.value = [...currentMessages.value]
                  // 滚动到底部
                  await nextTick()
                  scrollToBottom()

                  // 如果是第一条回复，且内容长度达到20个字符，更新会话标题
                  if (firstResponse && aiMessage.displayContent.length >= 20) {
                    const title = aiMessage.displayContent.slice(0, 20) + '...'
                    try {
                      await updateChatTitle(currentConversationId.value, title)
                      // 更新本地会话列表
                      const index = conversations.value.findIndex(c => c.id === currentConversationId.value)
                      if (index !== -1) {
                        conversations.value[index].title = title
                        // 同步更新当前会话的标题
                        currentConversation.value = {
                          ...currentConversation.value,
                          title: title
                        }
                      }
                      firstResponse = false
                    } catch (error) {
                      console.error('更新会话标题失败:', error)
                    }
                  }
                }
              } else if (data.error) {
                ElMessage.error(data.error)
                break
              }
            } catch (e) {
              console.error('解析响应数据失败:', e, '原始数据:', line)
            }
          }
        }
      }
    } catch (error) {
      console.error('处理AI响应时出错:', error)
      ElMessage.error('处理AI响应时出错')
    } finally {
      clearTimeout(timeout)
      if (aiStreamReader.value === reader) {
        aiStreamReader.value = null
      }
      // 生成完成后移除生成状态
      aiMessage.isGenerating = false
      // 强制更新视图
      currentMessages.value = [...currentMessages.value]
      // 最后一次滚动到底部
      await nextTick()
      scrollToBottom()

      // 更新会话列表
      await fetchConversations()
    }

  } catch (error) {
    if (aiStreamReader.value && aiStreamReader.value === reader) {
      aiStreamReader.value = null
    }
    ElMessage.error('发送消息失败')
    console.error('发送消息失败:', error)
    // 如果AI消息还没有内容，从列表中移除
    if (currentMessages.value.length > 0 && currentMessages.value[currentMessages.value.length - 1].content === '') {
      currentMessages.value.pop()
    }
  }
}

// 切换功能
const toggleFunction = (type) => {
  const index = selectedFunctions.value.indexOf(type)
  if (index === -1) {
    selectedFunctions.value.push(type)
  } else {
    selectedFunctions.value.splice(index, 1)
  }
}

// 生成代码
const generateCode = () => {
  ElMessage.info('代码生成功能开发中')
}

// 显示推荐问题
const showRecommendedQuestions = () => {
  ElMessage.info('推荐问题功能开发中')
}

// 显示快捷键
const showShortcuts = () => {
  ElMessage.info('快捷键功能开发中')
}

// 删除对话
const handleDeleteChat = async (conversation) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个对话吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'custom-message-box'
      }
    )

    await deleteChat(conversation.id)
    ElMessage.success('删除成功')

    // 如果删除的是当前对话，清空当前对话
    if (currentConversationId.value === conversation.id) {
      currentConversationId.value = null
      currentConversation.value = null
      currentMessages.value = []
    }

    // 重新获取对话列表
    await fetchConversations()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.log(error)
    }
  }
}

// 处理下拉菜单命令
const handleCommand = async (command, conversation) => {
  switch (command) {
    case 'edit':
      handleEditTitle(conversation)
      break
    case 'delete':
      handleDeleteChat(conversation)
      break
  }
}

// 编辑标题
const handleEditTitle = async (conversation) => {
  try {
    const { value: newTitle } = await ElMessageBox.prompt('请输入新的标题', '编辑标题', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: conversation.title,
      customClass: 'custom-message-box',
      inputValidator: (value) => {
        if (!value.trim()) {
          return '标题不能为空'
        }
        return true
      }
    })

    if (newTitle && newTitle.trim()) {
      await updateChatTitle(conversation.id, newTitle.trim())
      ElMessage.success('修改成功')
      // 更新本地会话列表
      const index = conversations.value.findIndex(c => c.id === conversation.id)
      if (index !== -1) {
        conversations.value[index].title = newTitle.trim()
        // 如果是当前会话，同步更新右侧标题
        if (currentConversationId.value === conversation.id) {
          currentConversation.value = {
            ...currentConversation.value,
            title: newTitle.trim()
          }
        }
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('修改失败')
      console.log(error)
    }
  }
}

// 添加复制功能
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (err) {
    ElMessage.error('复制失败')
    console.error('复制失败:', err)
  }
}

const handleAttachmentClick = () => {
  ElMessage.info('附件功能开发中')
}

const handleSendClick = () => {
  if (isAIThinking.value) {
    ElMessage.warning('AI正在思考中，请等待回答或点击停止');
    return;
  }
  if (!inputMessage.value.trim()) {
    ElMessage.warning('请输入您的问题');
    return;
  }
  sendAIMessage();
}

// 停止AI思考
const stopAIThinking = async () => {
  aiShouldStop = true;
  if (aiStreamReader.value) {
    try {
      await aiStreamReader.value.cancel();
      aiStreamReader.value = null;
    } catch (e) {
      // ignore
    }
  }
  // 强制让最后一条AI消息停止生成
  if (
    currentMessages.value.length > 0 &&
    currentMessages.value[currentMessages.value.length - 1].type === 'ai' &&
    currentMessages.value[currentMessages.value.length - 1].isGenerating
  ) {
    currentMessages.value[currentMessages.value.length - 1].isGenerating = false;
    currentMessages.value = [...currentMessages.value];
  }
  ElMessage.success('已停止AI思考');
}

// 页面加载时获取对话列表
onMounted(() => {
  fetchConversations()
})

// 对话分组逻辑
const groupedConversations = computed(() => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today)
  yesterday.setDate(today.getDate() - 1)
  const groups = {
    '今天': [],
    '昨天': [],
    '7天内': [],
    '30天内': [],
    '更早': []
  }
  conversations.value.forEach(conv => {
    const convDate = new Date(conv.time)
    const convDay = new Date(convDate.getFullYear(), convDate.getMonth(), convDate.getDate())
    const days = differenceInDays(now, convDate)
    if (convDay.getTime() === today.getTime()) {
      groups['今天'].push(conv)
    } else if (convDay.getTime() === yesterday.getTime()) {
      groups['昨天'].push(conv)
    } else if (days <= 7) {
      groups['7天内'].push(conv)
    } else if (days <= 30) {
      groups['30天内'].push(conv)
    } else {
      groups['更早'].push(conv)
    }
  })
  return groups
})

// AI是否正在思考/生成回答
const isAIThinking = computed(() => {
  if (currentMessages.value.length === 0) return false;
  const lastMsg = currentMessages.value[currentMessages.value.length - 1];
  return lastMsg.type === 'ai' && lastMsg.isGenerating === true;
});

// 在 script 部分添加新的方法
const toggleThinkingProcess = (message) => {
  message.isThinkingExpanded = !message.isThinkingExpanded;
}

</script>

<style scoped>
/* 美化主要按钮样式 */
:deep(.el-button--primary) {
  background-color: #dbeafe !important;
  border-color: #bad9fe !important;
  color: #4d6bfe;
  border-radius: 4px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  height: 32px !important;
  padding: 0 12px !important;
  box-shadow: none !important;
}

:deep(.el-button--primary:hover), :deep(.el-button--primary:focus) {
  background-color: #dbeafe !important;
  border-color: #bad9fe !important;
}

:deep(.el-button--primary:active),
:deep(.el-button--primary:focus:active) {
  background-color: #dbeafe !important;
  border-color: #dbeafe !important;
  color: #4d6bfe;
  box-shadow: none !important;
}

/* 美化链接按钮样式 */
:deep(.el-button--primary.is-link) {
  color: #707280 !important;
  font-weight: 500 !important;
  /* transition: all 0.3s ease !important; */
  border-radius: 4px !important;
  height: 32px !important;
  padding: 0 12px !important;
  background-color: white !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.el-button--primary.is-link:hover) {
  color: #707280 !important;
  background-color: #f3f4f6 !important;
}

:deep(.el-button--primary.is-link:active),
:deep(.el-button--primary.is-link:focus:active) {
  background-color: #dbeafe !important;
  border-color: #dbeafe !important;
  box-shadow: none !important;
}

/* 美化功能按钮样式 */
:deep(.el-button--default) {
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
  background-color: white !important;
  color: #374151 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  height: 32px !important;
  padding: 0 12px !important;
  transform: none !important;
}

:deep(.el-button--default:hover) {
  border-color: #2680EB !important;
  color: #2680EB !important;
  background-color: #f0f7ff !important;
  transform: none !important;
}

:deep(.el-button) {
  --el-transition-duration: 0.3s !important;
  transform: none !important;
}

:deep(.el-button:hover) {
  transform: none !important;
}

/* 美化发送按钮样式 */
:deep(.el-button--primary.send-btn) {
  padding: 0 16px !important;
  height: 36px !important;
  font-size: 14px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
}

/* 美化附件按钮样式 */
:deep(.attachment-btn) {
  height: 40px !important;
  width: 40px !important;
  border: none !important;
  transition: all 0.3s ease !important;
  color: #2680EB !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  box-shadow: none !important;
}

:deep(.attachment-btn:hover) {
  color: #1a6bca !important;
}

:deep(.attachment-btn:active),
:deep(.attachment-btn:focus) {
  background-color: #dbeafe !important;
  border-color: #dbeafe !important;
  box-shadow: none !important;
}

:deep(.attachment-btn .el-icon) {
  font-size: 20px !important;
}

:deep(.attachment-btn.el-button.is-link) {
  box-shadow: none !important;
  background: none !important;
}

:deep(.attachment-btn.el-button.is-link:hover),
:deep(.attachment-btn.el-button.is-link:focus),
:deep(.attachment-btn.el-button.is-link:active) {
  box-shadow: none !important;
  background: none !important;
}

/* 美化标签样式 */
:deep(.el-tag--primary) {
  background-color: #f0f7ff !important;
  border: 1px solid #2680EB !important;
  color: #2680EB !important;
  border-radius: 4px !important;
  padding: 4px 8px !important;
  font-weight: 500 !important;
}

/* 美化输入框样式 */
:deep(.custom-textarea .el-textarea__inner) {
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
  transition: all 0.3s ease !important;
  padding-left: 52px !important;
  padding-right: 100px !important;
  resize: none !important;
  min-height: 80px !important;
  max-height: 200px !important;
  line-height: 1.5 !important;
  background: white !important;
}

:deep(.custom-textarea .el-textarea__wrapper) {
  box-shadow: none !important;
  padding: 0 !important;
  background: none !important;
  border: none !important;
}

:deep(.custom-textarea) {
  --el-input-border-color: none !important;
  --el-input-hover-border-color: none !important;
  --el-input-focus-border-color: none !important;
}

:deep(.custom-textarea .el-textarea__inner:hover) {
  border-color: #2680EB !important;
}

:deep(.custom-textarea .el-textarea__inner:focus) {
  border-color: #2680EB !important;
}

/* 美化下拉框样式 */
:deep(.el-select .el-input__wrapper) {
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 0 0 1px #e5e7eb inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #2680EB inset !important;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #2680EB inset !important;
}

:deep(.el-select-dropdown__item.selected) {
  color: #2680EB !important;
  font-weight: 500 !important;
}

:deep(.el-select) {
  --el-select-input-focus-border-color: #2680EB !important;
  --el-select-border-color-hover: #2680EB !important;
  --el-select-border-color: #2680EB !important;
  --el-select-text-color: #2680EB !important;
  --el-select-option-selected-text-color: #2680EB !important;
}

:deep(.el-select:hover .el-input__wrapper) {
  box-shadow: 0 0 0 1px #2680EB inset !important;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #2680EB inset !important;
}

:deep(.el-popper.is-light) {
  border: 1px solid #2680EB !important;
}

/* 滚动条样式 */
.conversation-history,
.conversation-content {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E0 #EDF2F7;
  scroll-behavior: smooth;
}

.conversation-history::-webkit-scrollbar,
.conversation-content::-webkit-scrollbar {
  width: 6px;
}

.conversation-history::-webkit-scrollbar-track,
.conversation-content::-webkit-scrollbar-track {
  background: #EDF2F7;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .col-span-3,
  .col-span-9 {
    grid-column: span 12;
  }
  
  .conversation-history,
  .conversation-content {
    height: 400px;
  }
}

/* 添加下拉框样式 */
:deep(.el-select) {
  --el-select-input-focus-border-color: var(--el-color-primary);
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

/* 调整功能按钮样式 */
.gap-3 {
  gap: 0.75rem;
}

:deep(.el-button) {
  white-space: nowrap;
}

:deep(.el-button.is-link) {
  min-width: fit-content;
}

/* 调整链接按钮的hover效果 */
:deep(.el-button.is-link:hover) {
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
}

/* 调整下拉框样式 */
.modelSelectWidth {
  width: 120px !important;
}

:deep(.modelSelectWidth .el-input__wrapper) {
  padding: 0 8px;
}

:deep(.modelSelectWidth .el-input__inner) {
  text-align: center;
}

/* 移除之前的响应式布局样式 */
@media (max-width: 768px) {
  .flex-wrap {
    gap: 0.75rem;
  }
}

/* 自定义文本域样式 */
:deep(.custom-textarea .el-textarea__inner) {
  padding-left: 44px;
  padding-right: 80px;
  resize: none;
  min-height: 80px;
  max-height: 200px;
  line-height: 1.5;
}

:deep(.custom-textarea .el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

/* 调整按钮样式 */
:deep(.el-button.is-link) {
  height: 28px;
  width: 28px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-button.is-link:hover) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

/* 调整发送按钮样式 */
:deep(.el-button--primary) {
  height: 32px;
  padding: 0 12px;
  transition: all 0.2s;
}

/* 调整附件按钮样式 */
:deep(.attachment-btn) {
  height: 32px;
  width: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

:deep(.attachment-btn:hover) {
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
}

/* 调整删除按钮样式 */
:deep(.el-button.is-link.is-danger) {
  padding: 4px;
  height: 24px;
  width: 24px;
}

:deep(.el-button.is-link.is-danger:hover) {
  background-color: var(--el-color-danger-light-9);
  border-radius: 4px;
}

/* 调整下拉菜单样式 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 8px;
}

:deep(.el-dropdown-menu__item.is-disabled) {
  color: var(--el-text-color-disabled);
}

/* 添加动态省略号样式 */
.typing-dots {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
}

.typing-dots .dot {
  width: 4px;
  height: 4px;
  margin: 0 1px;
  background-color: currentColor;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* 添加打字机效果样式 */
.char {
  display: inline-block;
  animation: fadeIn 0.1s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加 Markdown 样式 */
.markdown-content {
  line-height: 1.6;
  font-size: 16px;
}

.markdown-content :deep(pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin: 8px 0;
}

.markdown-content :deep(code) {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}

.markdown-content :deep(pre code) {
  padding: 0;
  background-color: transparent;
}

.markdown-content :deep(p) {
  margin: 8px 0;
}

.markdown-content :deep(ul), .markdown-content :deep(ol) {
  padding-left: 2em;
  margin: 8px 0;
}

.markdown-content :deep(li) {
  margin: 4px 0;
}

.markdown-content :deep(h1), 
.markdown-content :deep(h2), 
.markdown-content :deep(h3), 
.markdown-content :deep(h4), 
.markdown-content :deep(h5), 
.markdown-content :deep(h6) {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content :deep(h1) { font-size: 2em; }
.markdown-content :deep(h2) { font-size: 1.5em; }
.markdown-content :deep(h3) { font-size: 1.25em; }
.markdown-content :deep(h4) { font-size: 1em; }
.markdown-content :deep(h5) { font-size: 0.875em; }
.markdown-content :deep(h6) { font-size: 0.85em; }

.markdown-content :deep(blockquote) {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 8px 0;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.markdown-content :deep(th), 
.markdown-content :deep(td) {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-content :deep(tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

.markdown-content :deep(img) {
  max-width: 100%;
  box-sizing: border-box;
}

.markdown-content :deep(a) {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

/* 调整复制按钮样式 */
.copy-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 4px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  border: none !important;
  box-shadow: none !important;
}

.rounded-lg:hover .copy-btn {
  opacity: 1;
}

.copy-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* 调整消息内容区域的内边距，为复制按钮留出空间 */
.rounded-lg {
  padding-bottom: 0.5rem;
}

.start-conversation-btn {
  display: flex;
  align-items: center;
  background: #dbeafe;
  color: #4d6bfe;
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 500;
  padding: 10px 22px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  box-shadow: none;
  outline: none;
}
.start-conversation-btn .el-icon {
  font-size: 22px;
  margin-right: 6px;
}
.start-conversation-btn:hover {
  background: #c6dcf8;
  color: #4d6bfe;
}
.more-btn {
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  color: #707280 !important;
  border: none !important;
  transition: background 0.2s;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: transparent !important;
}
.more-btn:hover {
  color: #2680EB !important;
  background-color: transparent !important;
}
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 2px !important;
  font-size: 15px;
  padding: 8px 20px;
}
:deep(.el-dropdown-menu__item:hover) {
  background: #f0f7ff !important;
  color: #2680EB !important;
}
.left-panel-card {
  background-color: #f9fbff !important;
  width: 290px;
  box-shadow: none !important;
  transition: none !important;
  transform: none !important;
  position: fixed !important;
  top: 100px !important;
  height: calc(100vh - 120px) !important;
  overflow-y: auto !important;
}
.left-panel-card:hover, .left-panel-card:active, .left-panel-card:focus {
  transition: none !important;
  transform: none !important;
  box-shadow: none !important;
  top: 100px !important;
}
.conversation-group + .conversation-group {
  margin-top: 8px;
}
.group-title {
  background: transparent;
  color: #555;
  font-weight: 700;
  letter-spacing: 1px;
  font-size: 15px;
  margin-top: 20px;
}
.conversation-item {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
  border-bottom: none !important;
}
.conversation-item .text-sm {
  line-height: 1.2 !important;
  font-size: 15px;
  color: #444;
}
.conversation-item.active {
  background-color: #dbeafe !important;
  border-radius: 6px;
}
.conversation-item.conversation-item:hover {
  background-color: #eef6ff !important;
  border-radius: 6px;
}
.conversation-item .more-btn {
  opacity: 0;
  transition: opacity 0.2s;
}
.conversation-item:hover .more-btn,
.conversation-item.active .more-btn {
  opacity: 1;
}
.conversation-title-ellipsis {
  display: inline-block;
  max-width: 220px;
  white-space: nowrap;
  overflow: hidden;
}
.right-panel-card {
  width: 900px;
  margin: 0;
  margin-left: 400px;
  background: transparent !important;
  box-shadow: none !important;
  transition: none !important;
  transform: none !important;
}
.input-area-container {
  width: 900px;
  background-color: #f3f4f6;
  border-radius: 25px;
  position: absolute;
  bottom: 25px;
}

.input-area-container.input-area-new-chat {
  position: relative;
}
.input-area-box {
  position: relative;
  background: #f3f4f6;
  border-radius: 24px;
  min-height: 72px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: none;
  border: none;
}
.input-textarea {
  flex: 1;
  border: none;
  background: #f3f4f6;
  outline: none;
  resize: none;
  font-size: 18px;
  color: #555;
  padding: 24px 0 24px 0;
  min-height: 48px;
  max-height: 120px;
  line-height: 1.5;
}
.input-placeholder {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #bfc2c9;
  font-size: 17px;
  pointer-events: none;
}
.input-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: #bfc2c9;
  margin-left: 8px;
  transition: color 0.2s;
}
.input-btn:hover {
  color: #2680EB;
}
.attachment-btn {
  margin-right: 4px;
}
.send-btn {
  background: #e5e8ef;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  color: #bfc2c9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  transition: background 0.2s, color 0.2s;
}
.send-btn:hover {
  background: #2680EB;
  color: #fff;
}
.input-functions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  padding-left: 8px;
}
.input-bottom-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px 8px 8px;
}
.input-bottom-row > div:last-child {
  display: flex;
  align-items: center;
  gap: 2px !important;
}
.input-bottom-row > div:last-child > button {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 18px !important;
  background: none;
  border: none;
}
.input-bottom-row > div:last-child > button svg {
  width: 32px;
  height: 32px;
  display: block;
}
.custom-send-btn {
  background: none;
  border: none;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.input-bottom-row > div:last-child > .attachment-btn {
  width: 36px;
  height: 36px;
}
.input-bottom-row > div:last-child > .attachment-btn svg {
  width: 24px;
  height: 24px;
}
.input-functions .el-button {
  border-radius: 18px !important;
  margin-right: -10px !important;
}
.input-bottom-row > div:last-child > button:active,
.input-functions .el-button:active,
.custom-send-btn:active {
  background-color: #dbeafe !important;
}
.custom-send-btn:active svg circle,
.custom-send-btn:focus svg circle {
  fill: #dbeafe !important;
}
:deep(.el-button .el-button__ripple) {
  background-color: transparent !important;
}
.conversation-content {
  background: transparent !important;
}
.shadow-sm {
  box-shadow: none !important;
  padding: 0 20px;
  background-color: #eff6ff;
  margin-bottom: 20px;
}
.bg-blue-100 {
  /* max-width: 400px; */
}
/* 消息气泡背景色去除 */
.bg-white {
  background: transparent !important;
}
.disabled-send-btn {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: #e5e8ef !important;
  color: #bfc2c9 !important;
  pointer-events: auto !important;
}
.avatar-ai-center {
  border: 1px solid #d5e4ff;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}
:deep(.el-card__body) {
  padding: 0px !important;
}
:deep(.el-card__header) {
  border-bottom: none !important;
  padding: 0px !important;
}
:deep(.p-6) {
  padding-bottom: 0px !important;
}
.footai {
  position: absolute; 
  bottom: 0; 
  text-align: center; 
  width: 900px;
  font-size: 12px; 
  color: #a3a3a3;
  height: 25px;
  line-height: 25px;
  background-color: #fff;
}

/* 自定义确认框按钮样式 */
:global(.custom-message-box .el-button--primary) {
  background-color: #4D6BFE !important;
  border-color: #4D6BFE !important;
  color: white !important;
}

:global(.custom-message-box .el-button--primary:hover) {
  background-color: #3d5be8 !important;
  border-color: #3d5be8 !important;
}

:global(.custom-message-box .el-button--primary:active) {
  background-color: #2d4bd2 !important;
  border-color: #2d4bd2 !important;
}

/* 自定义消息框输入框样式 */
:global(.custom-message-box .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:global(.custom-message-box .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #4D6BFE inset !important;
}

:global(.custom-message-box .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #4D6BFE inset !important;
}
:deep(.bg-gray-50) {
  background-color: white !important;
}
.downBox {
  width: 100%;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: -50px;
  transform: translateX(-10px);
}

/* Add new style for the arrow wrapper */
.downBox > svg {
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  padding: 5px;
}

/* 新增思考过程样式 */
.thinking-process {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.thinking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  color: #666;
  font-size: 14px;
}

.thinking-header:hover {
  background-color: #f0f0f0;
  border-radius: 4px;
}

.thinking-content {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #eee;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.thinking-actions {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style>