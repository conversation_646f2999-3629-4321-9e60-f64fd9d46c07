# -*- coding: utf-8 -*-

import os
import pandas as pd
from docx import Document
import PyPDF2
import logging
from typing import Optional, List, Dict, Any, Union

logger = logging.getLogger(__name__)

class DocumentReader:
    """
    文档读取工具类，用于读取Excel、Word和PDF文件并转换为文本
    """
    
    @staticmethod
    def read_excel(file_path: str, sheet_name: Optional[Union[str, int, List[Union[int, str]]]] = 0) -> str:
        """
        读取Excel文件并转换为文本
        
        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称或索引，默认为第一个工作表
            
        Returns:
            提取的文本内容
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return ""
                
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 将DataFrame转换为字符串
            text_content = df.to_string(index=False)
            return text_content
        except Exception as e:
            logger.error(f"读取Excel文件失败: {str(e)}")
            return ""
    
    @staticmethod
    def read_word(file_path: str) -> str:
        """
        读取Word文档并转换为文本
        
        Args:
            file_path: Word文档路径
            
        Returns:
            提取的文本内容
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return ""
                
            doc = Document(file_path)
            
            # 提取段落文本
            paragraphs = [para.text for para in doc.paragraphs]
            
            # 提取表格文本
            tables = []
            for table in doc.tables:
                for row in table.rows:
                    row_text = ' | '.join([cell.text for cell in row.cells])
                    if row_text.strip():
                        tables.append(row_text)
            
            # 合并所有文本
            all_text = '\n'.join(paragraphs)
            if tables:
                all_text += '\n\n表格内容:\n' + '\n'.join(tables)
                
            return all_text
        except Exception as e:
            logger.error(f"读取Word文档失败: {str(e)}")
            return ""
    
    @staticmethod
    def read_pdf(file_path: str) -> str:
        """
        读取PDF文件并转换为文本
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            提取的文本内容
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return ""
                
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text_content = []
                
                # 遍历所有页面并提取文本
                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text_content.append(page.extract_text())
                
                return '\n\n'.join(text_content)
        except Exception as e:
            logger.error(f"读取PDF文件失败: {str(e)}")
            return ""
    
    @staticmethod
    def read_txt(file_path: str, encoding: str = 'utf-8') -> str:
        """
        读取TXT文本文件
        
        Args:
            file_path: TXT文件路径
            encoding: 文件编码，默认为utf-8
            
        Returns:
            提取的文本内容
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return ""
            
            # 尝试使用指定编码读取文件
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    return file.read()
            except UnicodeDecodeError:
                # 如果utf-8解码失败，尝试使用gbk编码
                logger.warning(f"使用{encoding}编码读取失败，尝试使用gbk编码")
                try:
                    with open(file_path, 'r', encoding='gbk') as file:
                        return file.read()
                except UnicodeDecodeError:
                    # 如果gbk也失败，尝试使用latin-1编码（几乎不会失败）
                    logger.warning("使用gbk编码读取失败，尝试使用latin-1编码")
                    with open(file_path, 'r', encoding='latin-1') as file:
                        return file.read()
        except Exception as e:
            logger.error(f"读取TXT文件失败: {str(e)}")
            return ""
    
    @staticmethod
    def read_document(file_path: str, **kwargs) -> str:
        """
        根据文件扩展名自动选择合适的方法读取文档
        
        Args:
            file_path: 文件路径
            **kwargs: 其他参数，如sheet_name等
            
        Returns:
            提取的文本内容
        """
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return ""
            
        file_ext = os.path.splitext(file_path.lower())[1]
        
        if file_ext in ['.xlsx', '.xls']:
            sheet_name = kwargs.get('sheet_name', 0)
            return DocumentReader.read_excel(file_path, sheet_name=sheet_name)
        elif file_ext in ['.txt']:
            return DocumentReader.read_txt(file_path)
        elif file_ext in ['.docx', '.doc']:
            return DocumentReader.read_word(file_path)
        elif file_ext == '.pdf':
            return DocumentReader.read_pdf(file_path)
        else:
            logger.error(f"不支持的文件格式: {file_ext}")
            return ""

if __name__ == '__main__':
    print(DocumentReader.read_document(r'E:\金融学校-RAGflow\1--5\电子商务基础主编白东蕊\电子商务基础主编白东蕊_1-100.docx'))