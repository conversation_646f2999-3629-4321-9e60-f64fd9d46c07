# Generated by Django 3.2.20 on 2025-05-07 03:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0021_knowledgecategory_category_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='knowledgecategory',
            name='created_by',
            field=models.IntegerField(blank=True, null=True, verbose_name='创建者ID'),
        ),
        migrations.AlterField(
            model_name='knowledgedataset',
            name='created_by',
            field=models.IntegerField(blank=True, null=True, verbose_name='创建者ID'),
        ),
        migrations.AlterField(
            model_name='knowledgedataset',
            name='user_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='所属用户ID'),
        ),
        migrations.AlterField(
            model_name='knowledgedocument',
            name='user_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='文档所有者ID'),
        ),
        migrations.AlterField(
            model_name='knowledgefavorite',
            name='user_id',
            field=models.Integer<PERSON>ield(verbose_name='用户ID'),
        ),
    ]
