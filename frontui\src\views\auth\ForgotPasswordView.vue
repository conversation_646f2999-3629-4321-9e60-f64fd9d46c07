<template>
  <div class="bg-gray-50 flex items-center justify-center min-h-screen">
    <div class="max-w-md w-full mx-auto">
      <el-card class="box-card">
        <!-- 头部 -->
        <div class="p-6 bg-blue-600 text-center">
          <h1 class="text-2xl font-bold text-white">智慧课堂</h1>
          <p class="text-blue-100 mt-2">知识改变命运，智慧点亮未来</p>
        </div>
        
        <!-- 找回密码表单 -->
        <div class="p-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-6 text-center">找回密码</h2>
          
          <el-form @submit.native.prevent="handleForgotPassword" :model="formData" :rules="rules" ref="ruleFormRef">
            <!-- 邮箱/手机号 -->
            <el-form-item prop="email">
              <el-input 
                v-model="formData.email" 
                placeholder="请输入已注册的邮箱或手机号"
                prefix-icon="Message"
              />
            </el-form-item>
            
            <!-- 验证码 -->
            <el-form-item prop="code">
              <div class="flex gap-2">
                <el-input 
                  v-model="formData.code" 
                  placeholder="请输入验证码"
                  prefix-icon="Key"
                  class="flex-1"
                />
                <el-button 
                  type="primary" 
                  @click="sendCode" 
                  :disabled="countdown > 0"
                  class="main-color-btn"
                >
                  {{ countdown > 0 ? `重新发送(${countdown}s)` : '获取验证码' }}
                </el-button>
              </div>
            </el-form-item>
            
            <!-- 提交按钮 -->
            <el-button type="primary" @click="submitForm(ruleFormRef)" class="w-full main-color-btn">
              <el-icon class="mr-2"><Right /></el-icon> 下一步
            </el-button>
          </el-form>
          
          <!-- 返回登录 -->
          <div class="mt-6 text-center">
            <router-link to="/auth/login" class="font-medium text-blue-600 hover:text-blue-500 flex items-center justify-center">
              <el-icon class="mr-1"><Back /></el-icon> 返回登录
            </router-link>
          </div>
        </div>
      </el-card>
      
      <!-- 页脚 -->
      <div class="mt-4 text-center">
        <p class="text-xs text-gray-500">
          © 2025 智慧课堂. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message, Key, Right, Back } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const countdown = ref(0)
const timer = ref(null)
const ruleFormRef = ref()

// 表单数据
const formData = reactive({
  email: '',
  code: ''
})

// 表单验证规则
const validateEmail = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入邮箱或手机号'))
  } else {
    const isEmail = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(value)
    const isPhone = /^1[3-9]\d{9}$/.test(value)
    
    if (!isEmail && !isPhone) {
      callback(new Error('请输入正确的邮箱或手机号'))
    } else {
      callback()
    }
  }
}

const rules = reactive({
  email: [
    { validator: validateEmail, trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度在 4 到 6 个字符', trigger: 'blur' }
  ]
})

// 表单提交
const submitForm = async (formEl) => {
  if (!formEl) return
  
  await formEl.validate((valid, fields) => {
    if (valid) {
      handleForgotPassword()
    } else {
      ElMessage.error('表单验证失败，请检查输入')
    }
  })
}

// 发送验证码
const sendCode = () => {
  if (!formData.email) {
    ElMessage.warning('请输入邮箱或手机号')
    return
  }
  
  if (countdown.value > 0) return
  
  countdown.value = 60
  
  // 显示倒计时
  timer.value = setInterval(() => {
    countdown.value--
    
    if (countdown.value <= 0) {
      clearInterval(timer.value)
    }
  }, 1000)
  
  // 模拟发送验证码
  ElMessage.success(`验证码已发送到：${formData.email}`)
}

// 处理找回密码
const handleForgotPassword = () => {
  // 演示用，简单验证
  ElMessage.success('验证成功，请重置您的密码')
  
  // 跳转到重置密码页面
  router.push('/auth/reset-password')
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<style scoped>
/* 可以添加组件特定的样式 */
.main-color-btn {
  background-color: #2680eb !important;
  border-color: #2680eb !important;
}
.main-color-btn:hover,
.main-color-btn:focus {
  background-color: #1a6bca !important;
  border-color: #1a6bca !important;
}
.main-color-btn:active {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}
</style> 