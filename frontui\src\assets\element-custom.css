/* Element Plus 主题定制
 * 这是项目的主要UI框架样式
 * 这些样式应当拥有最高优先级
 */

:root {
  /* Primary Colors - Using a blue tone that matches educational platforms */
  --el-color-primary: #5669FF;
  --el-color-primary-light-1: #6B7DFF;
  --el-color-primary-light-2: #8191FF;
  --el-color-primary-light-3: #97A5FF;
  --el-color-primary-light-4: #ADB9FF;
  --el-color-primary-light-5: #C3CDFF;
  --el-color-primary-light-6: #D9E1FF;
  --el-color-primary-light-7: #EFF5FF;
  --el-color-primary-light-8: #F5F9FF;
  --el-color-primary-light-9: #FBFDFF;
  --el-color-primary-dark-1: #1a25e8;
  --el-color-primary-dark-2: #3B42D1;
  
  /* Text Colors */
  --el-text-color-primary: #333333;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #C0C4CC;
  
  /* Border Colors */
  --el-border-color-base: #DCDFE6;
  --el-border-color-light: #E4E7ED;
  --el-border-color-lighter: #EBEEF5;
  --el-border-color-extra-light: #F2F6FC;
  
  /* Font */
  --el-font-size-base: 14px;
  --el-font-weight-primary: 500;

  /* Success, Warning, Danger, Info Colors */
  --el-color-success: #4CAF50;
  --el-color-warning: #FF9800;
  --el-color-danger: #F44336;
  --el-color-info: #2196F3;
  
  /* Background Colors */
  --el-bg-color: #FFFFFF;
  --el-bg-color-overlay: #FFFFFF;
  
  /* Border Radius */
  --el-border-radius-base: 8px;
  --el-border-radius-small: 4px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;
  
  /* 暗黑模式变量 - 添加暗黑模式支持 */
  --el-bg-color-dark: #141414;
  --el-bg-color-overlay-dark: #1d1e1f;
  --el-text-color-primary-dark: #E5EAF3;
  --el-text-color-regular-dark: #CFD3DC;
  --el-text-color-secondary-dark: #A3A6AD;
  --el-border-color-dark: #373737;
}

/* 全局组件样式增强，确保较高CSS优先级 */

/* Tab customization */
.el-tabs__item {
  padding: 0 20px;
  height: 48px;
  box-sizing: border-box;
  line-height: 48px;
  font-size: 16px;
  color: var(--el-text-color-secondary);
  transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.el-tabs__item.is-active {
  color: var(--el-color-primary);
  font-weight: 600;
}

.el-tabs__active-bar {
  height: 3px;
  border-radius: 3px;
}

/* Button customization */
.el-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s;
}

.el-button--primary {
  background-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(86, 105, 255, 0.25);
}

.el-button--primary:hover {
  background-color: var(--el-color-primary-light-1);
  box-shadow: 0 6px 16px rgba(86, 105, 255, 0.35);
  transform: translateY(-1px);
}

.el-button--primary:active {
  background-color: var(--el-color-primary-dark-1);
  box-shadow: 0 2px 8px rgba(86, 105, 255, 0.2);
  transform: translateY(0);
}

/* Card customization */
.el-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.el-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-weight: 600;
}

/* Input customization */
.el-input__inner, .el-textarea__inner {
  border-radius: 8px;
  padding: 12px 15px;
  transition: all 0.3s;
}

.el-input__inner:focus, .el-textarea__inner:focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(86, 105, 255, 0.15);
}

/* Avatar customization */
.el-avatar {
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Badge customization */
.el-badge__content {
  border: none;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Comment section customization */
.el-comment {
  margin-bottom: 24px;
}

.el-comment__header {
  margin-bottom: 8px;
}

.el-comment__author {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.el-comment__content {
  padding: 12px 16px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

/* Instructor badge for comments */
.instructor-badge {
  display: inline-block;
  background-color: var(--el-color-success);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  font-weight: 600;
}

/* 表格增强 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.el-table th.el-table__cell {
  background-color: rgba(86, 105, 255, 0.05);
  font-weight: 600;
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: rgba(86, 105, 255, 0.02);
}

.el-table .el-table__row:hover td.el-table__cell {
  background-color: rgba(86, 105, 255, 0.08);
}

/* 表单项增强 */
.el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.el-form-item.is-error .el-input__inner {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1);
}

/* 弹窗增强 */
.el-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.el-dialog__header {
  padding: 20px 24px;
  background-color: var(--el-bg-color);
  margin-right: 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.el-dialog__title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.el-dialog__body {
  padding: 24px;
  color: var(--el-text-color-regular);
}

.el-dialog__footer {
  padding: 16px 24px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 下拉菜单增强 */
.el-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
}

.el-dropdown-menu__item {
  padding: 8px 16px;
  font-size: 14px;
  transition: all 0.2s;
}

.el-dropdown-menu__item:hover {
  background-color: rgba(86, 105, 255, 0.05);
}

.el-dropdown-menu__item.is-disabled {
  opacity: 0.6;
}

/* 分页组件增强 */
.el-pagination {
  padding: 16px 0;
  font-weight: 500;
}

.el-pagination .el-pagination__total,
.el-pagination .el-pagination__jump {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.el-pagination button {
  border-radius: 4px;
  transition: all 0.2s;
}

.el-pagination button:hover {
  background-color: rgba(86, 105, 255, 0.05);
}

.el-pagination .el-pagination__sizes .el-select .el-input {
  margin: 0 8px;
}

.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
  border-radius: 4px;
  transition: all 0.2s;
}

/* 选择器增强 */
.el-select-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.el-select-dropdown__item {
  padding: 8px 16px;
  height: 40px;
  line-height: 24px;
  transition: all 0.2s;
}

.el-select-dropdown__item.selected {
  color: var(--el-color-primary);
  font-weight: 600;
}

.el-select-dropdown__item.hover, 
.el-select-dropdown__item:hover {
  background-color: rgba(86, 105, 255, 0.05);
}

/* 日期选择器增强 */
.el-date-picker {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.el-date-table td.today .el-date-table-cell__text {
  color: var(--el-color-primary);
  font-weight: 600;
}

.el-date-table td.current .el-date-table-cell__text {
  background-color: var(--el-color-primary);
}

/* 菜单增强 */
.el-menu {
  border-right: none;
}

.el-menu-item {
  height: 50px;
  line-height: 50px;
  transition: all 0.3s;
}

.el-menu-item.is-active {
  background-color: rgba(86, 105, 255, 0.08);
  color: var(--el-color-primary);
  font-weight: 600;
}

.el-menu-item:hover {
  background-color: rgba(86, 105, 255, 0.05);
}

.el-submenu__title {
  height: 50px;
  line-height: 50px;
}

.el-submenu.is-active .el-submenu__title {
  color: var(--el-color-primary);
  font-weight: 600;
}

/* 开关增强 */
.el-switch {
  --el-switch-on-color: var(--el-color-primary);
}

.el-switch.is-checked .el-switch__core {
  box-shadow: 0 2px 6px rgba(86, 105, 255, 0.25);
}

/* 分割线增强 */
.el-divider {
  margin: 24px 0;
}

.el-divider__text {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
}

/* 滑块增强 */
.el-slider__runway {
  height: 6px;
  border-radius: 3px;
}

.el-slider__bar {
  height: 6px;
  border-radius: 3px;
  background-color: var(--el-color-primary);
}

.el-slider__button {
  width: 16px;
  height: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 加载中增强 */
.el-loading-spinner .circular {
  width: 32px;
  height: 32px;
}

.el-loading-spinner .path {
  stroke: var(--el-color-primary);
  stroke-width: 3;
}

/* 上传组件增强 */
.el-upload-dragger {
  border-radius: 8px;
  padding: 24px;
  transition: all 0.3s;
}

.el-upload-dragger:hover {
  border-color: var(--el-color-primary);
  background-color: rgba(86, 105, 255, 0.02);
}

.el-upload-list__item {
  transition: all 0.3s;
}

/* 标签增强 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 步骤条增强 */
.el-steps {
  margin: 20px 0;
}

.el-step__head.is-process, 
.el-step__head.is-finish {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.el-step__title.is-process, 
.el-step__title.is-finish {
  font-weight: 600;
}

.el-step__head.is-process .el-step__icon {
  background-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(86, 105, 255, 0.25);
}

/* 暗黑模式样式适配 */
.dark-mode .el-card,
.dark-mode .el-dialog,
.dark-mode .el-table,
.dark-mode .el-dropdown-menu,
.dark-mode .el-select-dropdown,
.dark-mode .el-date-picker,
.dark-mode .el-pagination,
.dark-mode .el-menu {
  background-color: var(--el-bg-color-overlay-dark);
  color: var(--el-text-color-primary-dark);
  border-color: var(--el-border-color-dark);
}

.dark-mode .el-button--default,
.dark-mode .el-input__inner,
.dark-mode .el-textarea__inner {
  background-color: var(--el-bg-color-dark);
  border-color: var(--el-border-color-dark);
  color: var(--el-text-color-regular-dark);
}

.dark-mode .el-tag {
  background-color: rgba(255, 255, 255, 0.08);
  color: var(--el-text-color-regular-dark);
}

.dark-mode .el-divider__text {
  background-color: var(--el-bg-color-dark);
}

/* 确保Element样式最高优先级 */
.el-message,
.el-notification,
.el-loading-mask,
.el-message-box,
.el-drawer,
.el-popper,
.el-color-picker__panel,
.el-cascader__dropdown,
.el-tooltip__popper {
  z-index: 9999 !important;
} 