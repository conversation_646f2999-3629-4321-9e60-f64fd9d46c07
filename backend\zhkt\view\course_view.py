from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
import os, datetime
from django.utils import timezone
from zhkt.utils.file_utils import FileUtils
from django.conf import settings
from django.core.exceptions import ValidationError
from .base_model_view import BaseModelViewSet
from zhkt.config import DEFAULT_LANGUAGE
from zhkt.serializers import (
    CourseSerializer,
    CourseRatingSerializer,
    CourseOverviewSerializer,
    ChapterSerializer,
    ResourceSerializer,
    LessonSerializer,
    NoteSerializer,
    CommentSerializer,
)
from zhkt.entitys import (
    Course, 
    CourseRating,
    CourseOverview,
    Chapter,
    Resource,
    Lesson,
    Note,
    Comment,
    Student,
)

class CourseViewSet(BaseModelViewSet):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 获取查询参数中的teacher_id
        teacher_id = self.request.query_params.get('teacher_id')

        # 获取语言参数
        language = self.request.query_params.get('language') or self.request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language

        # 如果提供了teacher_id参数，按teacher_id筛选
        if teacher_id:
            queryset = queryset.filter(teacher_id=teacher_id)
        # 否则，如果当前用户是教师，只返回该教师的课程
        elif hasattr(self.request.user, 'teacher_profile'):
            queryset = queryset.filter(teacher=self.request.user.teacher_profile)

        # 按语言过滤
        queryset = queryset.filter(language_code=language)

        return queryset

    @action(detail=True, methods=['get', 'put', 'post'])
    def overview(self, request, pk=None):
        """获取或更新课程概述"""
        course = self.get_object()
        
        if request.method == 'GET':
            overview = CourseOverview.objects.filter(course=course, deleted_at__isnull=True).first()
            if overview:
                serializer = CourseOverviewSerializer(overview)
                return Response(serializer.data)
            return Response({})
        
        elif request.method in ['PUT', 'POST']:
            overview = CourseOverview.objects.filter(course=course, deleted_at__isnull=True).first()
            if overview:
                serializer = CourseOverviewSerializer(overview, data=request.data, partial=True)
            else:
                data = request.data.copy()
                data['course'] = course.id
                serializer = CourseOverviewSerializer(data=data)
            
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def upload_cover(self, request, pk=None):
        """上传课程封面"""
        try:
            file = request.FILES.get('file')
            
            if not file:
                return Response({'error': '未找到文件'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证文件类型
            if not file.content_type.startswith('image/'):
                return Response({'error': '只支持图片文件'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证文件大小（最大2MB）
            if file.size > 2 * 1024 * 1024:
                return Response({'error': '文件大小不能超过2MB'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 保存文件到临时目录
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'course_covers')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 生成临时文件路径
            temp_file_path = os.path.join(temp_dir, file.name)
            
            # 保存上传的文件到临时目录
            with open(temp_file_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)
            
            try:
                # 使用FileUtils保存图片文件到OSS
                image_path = FileUtils.save_image_file(temp_file_path, 'course_covers')
                
                # 获取课程对象
                course = self.get_object()
                
                # 更新课程的封面图片URL
                course.cover_image = FileUtils.get_file_url(image_path)
                course.save()
                
                return Response({
                    'url': FileUtils.get_file_url(image_path),
                    'path': image_path
                })
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def rate(self, request, pk=None):
        """为课程评分"""
        try:
            course = self.get_object()
            student = request.user.student_profile

            # 验证评分数据
            rating_value = request.data.get('rating')
            if not rating_value or not isinstance(rating_value, (int, float)):
                return Response({'error': '评分无效'}, status=status.HTTP_400_BAD_REQUEST)
            
            if not (0 <= rating_value <= 5):
                return Response({'error': '评分必须在0-5之间'}, status=status.HTTP_400_BAD_REQUEST)

            # 检查是否已经评分过
            existing_rating = CourseRating.objects.filter(
                course=course,
                student=student,
                deleted_at__isnull=True
            ).first()

            if existing_rating:
                # 更新已有评分
                existing_rating.rating = rating_value
                existing_rating.save()
                serializer = CourseRatingSerializer(existing_rating)
                return Response(serializer.data)
            else:
                # 创建新评分
                rating = CourseRating.objects.create(
                    course=course,
                    student=student,
                    rating=rating_value
                )
                serializer = CourseRatingSerializer(rating)
                return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def ratings(self, request, pk=None):
        """获取课程的所有评分"""
        course = self.get_object()
        ratings = course.ratings.filter(deleted_at__isnull=True)
        page = self.paginate_queryset(ratings)
        if page is not None:
            serializer = CourseRatingSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = CourseRatingSerializer(ratings, many=True)
        return Response(serializer.data)
    
# 课程相关视图集
class CourseOverviewViewSet(BaseModelViewSet):
    """课程概述视图集"""
    queryset = CourseOverview.objects.all()
    serializer_class = CourseOverviewSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = self.queryset.filter(deleted_at__isnull=True)

        # 获取语言参数
        language = self.request.query_params.get('language') or self.request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language

        # 按语言过滤
        queryset = queryset.filter(language_code=language)

        return queryset
    
class ChapterViewSet(BaseModelViewSet):
    queryset = Chapter.objects.all()
    serializer_class = ChapterSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        course_id = self.request.query_params.get('course_id')

        # 获取语言参数
        language = self.request.query_params.get('language') or self.request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language

        if course_id:
            queryset = queryset.filter(course_id=course_id)

        # 通过关联的课程按语言过滤
        queryset = queryset.filter(course__language_code=language)

        return queryset

class ResourceViewSet(BaseModelViewSet):
    queryset = Resource.objects.all()
    serializer_class = ResourceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Resource.objects.all()
        lesson_id = self.request.query_params.get('lesson_id', None)
        resource_type = self.request.query_params.get('resource_type', None)

        # 获取语言参数
        language = self.request.query_params.get('language') or self.request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language

        if lesson_id:
            queryset = queryset.filter(lesson_id=lesson_id)
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        queryset = queryset.filter(deleted_at__isnull=True)

        # 通过关联的课程按语言过滤
        queryset = queryset.filter(course__language_code=language)

        return queryset
    
    @action(detail=False, methods=['post'])
    def getResourceByChapterAndLesson(self, request):
        chapter_id = request.query_params.get('chapter_id')
        lesson_id = request.query_params.get('lesson_id')
        
        if not chapter_id or not lesson_id:
            return Response(
                {"error": "chapter_id 和 lesson_id 参数都是必需的"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            resource = Resource.objects.get(chapter_id=chapter_id, lesson_id=lesson_id)
            serializer = self.get_serializer(resource)
            return Response(serializer.data)
        except Resource.DoesNotExist:
            return Response(
                {"error": "未找到对应的资源"},
                status=status.HTTP_404_NOT_FOUND
            )

class LessonViewSet(BaseModelViewSet):
    queryset = Lesson.objects.all()
    serializer_class = LessonSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        chapter_id = self.request.query_params.get('chapter_id')

        # 获取语言参数
        language = self.request.query_params.get('language') or self.request.headers.get('Accept-Language') or DEFAULT_LANGUAGE
        # 简化语言代码，如zh-CN -> zh
        language = language.split('-')[0] if '-' in language else language

        if chapter_id:
            queryset = queryset.filter(chapter_id=chapter_id)

        # 通过关联的课程按语言过滤
        queryset = queryset.filter(chapter__course__language_code=language)

        return queryset

    @action(detail=True, methods=['post'])
    def upload_video(self, request, pk=None):
        """上传课时视频"""
        try:
            file = request.FILES.get('file')
            
            if not file:
                return Response({'error': '未找到文件'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证文件类型
            if not file.content_type.startswith('video/'):
                return Response({'error': '只支持视频文件'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证文件大小（最大500MB）
            if file.size > 500 * 1024 * 1024:
                return Response({'error': '文件大小不能超过500MB'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 保存文件到临时目录
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'lesson_videos')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 生成临时文件路径
            temp_file_path = os.path.join(temp_dir, file.name)
            
            # 保存上传的文件到临时目录
            with open(temp_file_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)
            
            try:
                # 使用FileUtils保存视频文件
                video_path = FileUtils.save_video_file(temp_file_path)
                
                # 获取课时对象
                lesson = self.get_object()
                
                # 创建资源记录
                resource = Resource.objects.create(
                    course=lesson.chapter.course,
                    chapter=lesson.chapter,
                    lesson=lesson,
                    title=f'视频 {datetime.datetime.now().strftime("%Y%m%d%H%M%S")}',
                    resource_type='VIDEO',
                    file=video_path
                )
                
                # 更新课时的视频URL
                lesson.video_url = FileUtils.get_file_url(video_path)
                lesson.save()
                
                return Response({
                    'url': lesson.video_url,
                    'title': resource.title,
                    'resource_id': resource.id
                })
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class NoteViewSet(viewsets.ModelViewSet):
    """笔记视图集"""
    serializer_class = NoteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取笔记列表或单个笔记"""
        student = Student.objects.get(user=self.request.user)
        queryset = Note.objects.filter(
            student_id=student.id,
            deleted_at__isnull=True
        )
        
        # 如果是列表查询，且提供了course_id参数，则按课程过滤
        course_id = self.request.query_params.get('course', None)
        if self.action == 'list' and course_id:
            queryset = queryset.filter(course_id=course_id)

        # 如果是列表查询，且提供了classes参数，则按班级过滤
        classes = self.request.query_params.get('classes', None)
        if self.action == 'list' and classes:
            # 将逗号分隔的字符串转换为整数列表
            try:
                class_ids = [int(class_id.strip()) for class_id in classes.split(',') if class_id.strip()]
                if class_ids:
                    queryset = queryset.filter(course__class_groups__id__in=class_ids)
            except (ValueError, AttributeError):
                pass  # 如果转换失败，忽略classes过滤
            
        return queryset.order_by('timestamp')

    def perform_create(self, serializer):
        """创建笔记时自动设置学生"""
        try:
            student = Student.objects.get(user=self.request.user)
            serializer.save(student=student)
        except Student.DoesNotExist:
            raise ValidationError({"error": "当前用户不是学生"})

    def perform_update(self, serializer):
        """更新笔记时自动设置更新时间"""
        serializer.save()

    def perform_destroy(self, instance):
        """软删除笔记"""
        instance.deleted_at = timezone.now()
        instance.save()

class CommentViewSet(BaseModelViewSet):
    """评论视图集"""
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        course_id = self.request.query_params.get('course_id', None)
        if course_id:
            # 只返回父评论（不是回复的评论）
            queryset = queryset.filter(course_id=course_id, parent__isnull=True)
        return queryset

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def like(self, request, pk=None):
        """点赞评论"""
        comment = self.get_object()
        user = request.user

        if comment.liked_by.filter(id=user.id).exists():
            # 如果已经点赞，则取消点赞
            comment.liked_by.remove(user)
            comment.likes -= 1
            comment.save()
            return Response({'detail': '已取消点赞', 'likes': comment.likes})
        else:
            # 如果未点赞，则添加点赞
            comment.liked_by.add(user)
            comment.likes += 1
            comment.save()
            return Response({'detail': '点赞成功', 'likes': comment.likes})

    @action(detail=True, methods=['post'])
    def reply(self, request, pk=None):
        """回复评论"""
        parent_comment = self.get_object()
        serializer = self.get_serializer(data={
            'content': request.data.get('content'),
            'course': parent_comment.course.id,
            'parent': parent_comment.id
        })
        serializer.is_valid(raise_exception=True)
        serializer.save(user=request.user)
        return Response(serializer.data)