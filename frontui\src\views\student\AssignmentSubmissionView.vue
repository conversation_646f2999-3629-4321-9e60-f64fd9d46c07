<template>
  <StudentLayout 
    pageTitle="作业详情" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <router-link to="/student/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
            <span class="material-icons mr-2">home</span>
            首页
          </router-link>
        </li>
        <li>
          <div class="flex items-center">
            <span class="material-icons text-gray-400 mx-2">chevron_right</span>
            <router-link to="/student/assignments" class="text-sm font-medium text-gray-700 hover:text-blue-600">作业管理</router-link>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <span class="material-icons text-gray-400 mx-2">chevron_right</span>
            <span class="text-sm font-medium text-gray-500">{{ assignment.title }}</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- 作业信息卡片 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="flex-col-md-row">
        <div>
          <h1 class="text-xl font-bold text-gray-900 mb-2">{{ assignment.title }}</h1>
          <p class="text-sm text-gray-600">作业ID: {{ assignment.id }}</p>
        </div>
        <div class="mt-4 md:mt-0">
          <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
            已提交
          </span>
        </div>
      </div>
      
      <div class="flex-col-md-row" style="width: 75%; margin: 15px 0;">
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">shopping_cart</span>
          <div>
            <p class="text-xs text-gray-500">所属课程</p>
            <p class="text-sm font-medium">{{ assignment.courseName }}</p>
          </div>
        </div>
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">person</span>
          <div>
            <p class="text-xs text-gray-500">授课教师</p>
            <p class="text-sm font-medium">{{ assignment.teacherName }}</p>
          </div>
        </div>
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">event</span>
          <div>
            <p class="text-xs text-gray-500">截止时间</p>
            <p class="text-sm font-medium">{{ formatDate(assignment.dueDate) }}</p>
          </div>
        </div>
      </div>
      
      <div class="border-t border-gray-200 pt-4">
        <h2 class="text-lg font-medium text-gray-900 mb-3">作业说明</h2>
        <div class="prose max-w-none text-gray-700 mb-4">
          <p>{{ assignment.description }}</p>
        </div>
      </div>
    </div>

    <!-- 当前提交状态 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
        <span class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full mr-2">
          <span class="material-icons">assignment</span>
        </span>
        当前提交
      </h2>
      
      <div class="bg-blue-50 p-4 rounded-lg mb-6 flex items-center">
        <span class="material-icons text-blue-500 mr-2">info</span>
        <p class="text-blue-700">您已成功提交作业，等待教师批改。您可以在截止日期前重新提交以更新作业内容。</p>
      </div>
      
      <div class="border rounded-lg overflow-hidden mb-6">
        <div class="bg-gray-50 px-4 py-3 border-b">
          <h3 class="text-sm font-medium text-gray-700">提交详情</h3>
        </div>
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <p class="text-xs text-gray-500 mb-1">提交时间</p>
              <p class="text-sm font-medium">{{ formatDate(submission.submitTime) }}</p>
            </div>
            <div>
              <p class="text-xs text-gray-500 mb-1">提交状态</p>
              <p class="text-sm font-medium text-blue-600">已提交，待批改</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
            <span class="material-icons text-red-500 text-2xl">picture_as_pdf</span>
            <div class="flex-1">
              <p class="text-sm font-medium">{{ submission.fileName }}</p>
              <p class="text-xs text-gray-500">{{ submission.fileSize }}  |  上传于 {{ formatDate(submission.submitTime) }}</p>
            </div>
            <a href="#" class="text-blue-600 hover:text-blue-800" @click.prevent="downloadFile">
              <span class="material-icons">download</span>
            </a>
          </div>
        </div>
      </div>
      
      <div class="border rounded-lg overflow-hidden mb-6">
        <div class="bg-gray-50 px-4 py-3 border-b">
          <h3 class="text-sm font-medium text-gray-700">提交说明</h3>
        </div>
        <div class="p-4">
          <p class="text-sm text-gray-700">{{ submission.note }}</p>
        </div>
      </div>
    </div>
    
    <!-- 重新提交区域 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">重新提交</h2>
      
      <!-- 文件上传区域 -->
      <div 
        class="upload-area flex flex-col items-center justify-center p-8 rounded-lg mb-6 cursor-pointer"
        @click="triggerFileInput"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
        :class="{ 'bg-blue-50': isDragging }"
      >
        <template v-if="!selectedFile">
          <span class="material-icons text-blue-500 text-3xl mb-2">cloud_upload</span>
          <p class="text-sm font-medium text-gray-700 mb-1">拖放文件到此处或点击上传</p>
          <p class="text-xs text-gray-500">支持 .doc, .docx, .pdf 格式，最大 20MB</p>
        </template>
        <template v-else>
          <div class="flex items-center w-full">
            <span class="material-icons text-blue-500 text-2xl mr-3">description</span>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-800">{{ selectedFile.name }}</p>
              <p class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</p>
            </div>
            <button class="text-gray-500 hover:text-red-500" @click.stop="resetFileUpload">
              <span class="material-icons">close</span>
            </button>
          </div>
        </template>
        <input 
          type="file" 
          ref="fileInput"
          class="hidden" 
          accept=".doc,.docx,.pdf"
          @change="handleFileChange"
        >
      </div>
      
      <!-- 提交说明 -->
      <div class="mb-6">
        <label for="submission-note" class="block text-sm font-medium text-gray-700 mb-2">提交说明（可选）</label>
        <div class="editor-toolbar bg-gray-50 p-2 flex items-center space-x-2">
          <button class="p-1 rounded hover:bg-gray-200"><span class="material-icons">format_bold</span></button>
          <button class="p-1 rounded hover:bg-gray-200"><span class="material-icons">format_italic</span></button>
          <button class="p-1 rounded hover:bg-gray-200"><span class="material-icons">format_underlined</span></button>
          <span class="border-r border-gray-300 h-6"></span>
          <button class="p-1 rounded hover:bg-gray-200"><span class="material-icons">format_list_bulleted</span></button>
          <button class="p-1 rounded hover:bg-gray-200"><span class="material-icons">format_list_numbered</span></button>
          <span class="border-r border-gray-300 h-6"></span>
          <button class="p-1 rounded hover:bg-gray-200"><span class="material-icons">link</span></button>
        </div>
        <div 
          class="editor-content p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
          contenteditable="true" 
          id="submission-note"
          @input="updateSubmissionNote"
        >
          请输入提交说明...
        </div>
      </div>
      
      <!-- 按钮区域 -->
      <div class="flex justify-end space-x-3">
        <router-link 
          to="/student/assignments" 
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none"
        >
          取消
        </router-link>
        <button 
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          @click="resubmitAssignment"
        >
          重新提交
        </button>
      </div>
    </div>
  </StudentLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { formatDate } from '@/utils/date.js'
import { useRoute } from 'vue-router'

const studentStore = useStudentStore()
const route = useRoute()

// 文件上传相关
const fileInput = ref<HTMLInputElement | null>(null)
const selectedFile = ref<File | null>(null)
const isDragging = ref(false)
const submissionNote = ref('')

// 作业数据
const assignment = ref({
  id: 'EC20250315002',
  title: '电子商务市场调研报告',
  courseName: '电子商务',
  teacherName: '李老师',
  dueDate: '2025-03-30 23:59',
  description: `请针对一个您感兴趣的电子商务平台或行业，撰写一份详细的市场调研报告。报告应包含以下内容：

- 市场概况与发展趋势
- 目标用户群体分析
- 竞争对手分析（至少3家）
- SWOT分析
- 营销策略建议

提交要求：
- Word文档或PDF格式
- 报告正文不少于3000字
- 可包含适当的图表、数据和图片，但须注明来源
- 按照标准学术格式引用参考文献`
})

// 提交数据
const submission = ref({
  submitTime: '2025-03-25 14:32',
  fileName: '电子商务市场调研报告_张三.pdf',
  fileSize: '2.4 MB',
  note: '我选择了对中国生鲜电商平台进行市场调研分析，重点研究了当前行业竞争格局和未来发展趋势。报告中包含了详细的用户画像分析和四家主要竞争对手的优劣势对比。通过SWOT分析，我提出了几点创新营销策略建议，希望老师给予指导和建议。'
})

// 文件上传相关方法
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement
  if (input.files && input.files[0]) {
    selectedFile.value = input.files[0]
  }
}

const handleDragOver = () => {
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (event: DragEvent) => {
  isDragging.value = false
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    selectedFile.value = event.dataTransfer.files[0]
  }
}

const resetFileUpload = () => {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const formatFileSize = (bytes: number) => {
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(1)} MB`
}

const updateSubmissionNote = (event: Event) => {
  const target = event.target as HTMLElement
  submissionNote.value = target.innerText
}

// 下载文件
const downloadFile = () => {
  // 实现文件下载逻辑
  console.log('下载文件:', submission.value.fileName)
}

// 重新提交作业
const resubmitAssignment = () => {
  if (!selectedFile.value) {
    alert('请选择要上传的文件')
    return
  }
  
  // 实现重新提交逻辑
  console.log('重新提交作业:', {
    file: selectedFile.value,
    note: submissionNote.value
  })
}

// 页面加载时获取作业数据
onMounted(() => {
  const assignmentId = route.params.id
  // 根据作业ID获取作业详情
  console.log('获取作业详情:', assignmentId)
})
</script>

<style scoped>
.upload-area {
  border: 2px dashed #d1d5db;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #3b82f6;
  background-color: #f0f9ff;
}

.editor-toolbar {
  border: 1px solid #e5e7eb;
  border-bottom: none;
  border-radius: 0.375rem 0.375rem 0 0;
}

.editor-content {
  border: 1px solid #e5e7eb;
  border-radius: 0 0 0.375rem 0.375rem;
  min-height: 200px;
}

.flex-col-md-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 