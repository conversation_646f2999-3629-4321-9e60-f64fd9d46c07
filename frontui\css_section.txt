<style scoped>
.course-create-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  background-color: #ebf5ff;
  border-bottom: 1px solid #e0e7ff;
}

.course-create-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.compact-steps :deep(.el-step__main) {
  padding-top: 4px;
  padding-bottom: 4px;
  min-height: auto;
}

.compact-steps :deep(.el-step__title) {
  font-size: 14px;
  line-height: 1.2;
}

.compact-steps :deep(.el-step__description) {
  font-size: 12px;
  padding-bottom: 0;
  margin-top: 2px;
}

.compact-steps :deep(.el-step) {
  padding-bottom: 0px;
}

.form-content :deep(.el-form-item__label) {
  padding-bottom: 4px;
}

.avatar-uploader :deep(.el-upload),
.course-video-upload :deep(.el-upload) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  text-align: center;
  width: 100%;
}

.avatar-uploader :deep(.el-upload:hover),
.course-video-upload :deep(.el-upload:hover) {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  gap: 8px;
}

.avatar {
  max-height: 160px;
  display: block;
  margin: 0 auto;
  border-radius: 6px;
}

.video-preview {
  position: relative;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  background-color: #e0e7ff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.video-play-icon {
  font-size: 30px;
  color: #3b82f6;
}

.video-info {
  display: flex;
  flex-direction: column;
  text-align: left;
  flex-grow: 1;
  min-width: 0;
}

.video-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-size {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.video-remove-btn {
  background-color: #fee2e2;
  color: #ef4444;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.video-remove-btn:hover {
  background-color: #fecaca;
}

.video-remove-btn .material-icons {
  font-size: 18px;
}

/* Custom styling for Element Plus steps */
:deep(.el-steps) {
  margin-bottom: 24px;
}

:deep(.el-step__title) {
  font-weight: 500;
}

:deep(.el-step__head.is-process) {
  color: #2563eb;
  border-color: #2563eb;
}

:deep(.el-step__title.is-process) {
  color: #2563eb;
}

/* Fix alignment of form fields */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  height: 40px;
  line-height: 40px;
}

/* Make select component match input height */
:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input) {
  height: 40px;
}

/* 视频播放器样式 */
.video-player-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 16px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.video-player-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.video-player-container {
  position: relative;
}

.no-video-message {
  background-color: #f1f5f9;
  border-radius: 8px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-lesson-btn {
  font-weight: 500 !important;
  font-size: 0.95rem !important;
}

.add-lesson-btn :deep(.material-icons) {
  font-size: 1.1rem !important;
}

.add-lesson-btn, .add-button {
  font-weight: 500 !important;
}

.add-lesson-btn :deep(.material-icons),
.add-button :deep(.material-icons) {
  font-size: 1.1rem !important;
}

.el-button.add-button {
  padding: 10px 16px;
}

.chapter-actions .el-button,
.lesson-actions .el-button {
  font-size: 14px;
}
</style> 
