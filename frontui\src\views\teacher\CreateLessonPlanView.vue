<template>
  <TeacherLayout
    :userName="teacherData.name"
    :userAvatar="teacherData.avatar"
    :pageTitle="pageTitle"
    activePage="content-creation"
    activeSubPage="lesson-plan"
  >
    <div class="space-y-6">
      

      <!-- 顶部栏：通知和操作按钮 -->
      <div class="flex justify-between items-center mb-6">
        <!-- 正在分析教材提示 - 仅创建模式显示 -->
        <div v-if="!isGenerating && mode === 'create'" class="bg-blue-100 border-l-4 border-blue-500 p-4 rounded-md max-w-4xl w-3/4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="material-icons text-blue-400">info</i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-700">
                <span class="font-bold">AI分析完成:</span> 已成功从教材中提取了<strong>42</strong>个知识点和<strong>8</strong>个核心概念，适合制作约<strong>45</strong>分钟的教学内容。
              </p>
            </div>
          </div>
        </div>

        <!-- 编辑模式提示 -->
        <div v-if="mode === 'edit' && !isGenerating" class="bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded-md max-w-4xl w-3/4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="material-icons text-yellow-400">edit</i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                <span class="font-bold">编辑模式:</span> 您可以修改内容并保存更改。
              </p>
            </div>
          </div>
        </div>

        <!-- 查看模式提示 -->
        <div v-if="mode === 'view' && !isGenerating" class="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-md max-w-4xl w-3/4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="material-icons text-blue-400">visibility</i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-700">
                <span class="font-bold">查看模式:</span> 您正在查看此教学设计，内容不可编辑。
              </p>
            </div>
          </div>
        </div>
        
        <!-- 正在生成教学设计提示 -->
        <div v-if="isGenerating" class="bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded-md max-w-4xl w-3/4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="material-icons text-yellow-500 animate-spin">autorenew</i>
            </div>
            <div class="ml-3 w-full">
              <p class="text-sm text-yellow-700">
                <span class="font-bold">AI正在生成教学设计</span> 
                <span class="ml-2 text-xs">{{ generationProgress }}%</span>
              </p>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div class="bg-yellow-500 h-2 rounded-full transition-all duration-300" :style="`width: ${generationProgress}%`"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex gap-2">
          <button 
            class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md flex items-center gap-2 text-sm shadow-sm"
            @click="router.push('/teacher/lesson-plan-projects')"
          >
            <i class="material-icons text-sm">arrow_back</i>
            返回列表
          </button>

          <button 
            v-if="mode !== 'view'"
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2 text-sm shadow-sm"
            @click="saveAndReturn"
            :disabled="isGenerating || (mode === 'create' && (!currentTaskId || !generatedContent))"
          >
            <i class="material-icons text-sm">save</i>
            {{ mode === 'edit' ? '保存修改' : '保存并返回' }}
          </button>

          <button 
            v-if="mode === 'view'"
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2 text-sm shadow-sm"
            @click="router.push(`/teacher/lesson-plan/${lessonPlanId}/edit`)"
          >
            <i class="material-icons text-sm">edit</i>
            编辑
          </button>
        </div>
      </div>

      <!-- AI 助手聊天界面 -->
      <div class="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white cursor-pointer shadow-lg z-50" 
           @click="toggleAIChat">
        <i class="material-icons text-2xl">smart_toy</i>
      </div>

      <div class="fixed bottom-6 right-6 w-[350px] bg-white rounded-lg shadow-lg z-40 flex flex-col overflow-hidden max-h-[500px] transition-all duration-300"
           :class="aiChatVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-20 pointer-events-none'">
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 flex justify-between items-center cursor-pointer"
             @click="minimizeAIChat">
          <div class="flex items-center">
            <i class="material-icons mr-2">smart_toy</i>
            <span>AI 教案助手</span>
          </div>
          <i class="material-icons">remove</i>
        </div>
        <div class="p-2 bg-gray-100 border-t border-b border-gray-200 flex items-center">
          <span class="text-sm text-gray-600 mr-2">选择模型:</span>
          <select class="text-sm border border-gray-300 rounded px-2 py-1">
            <option>GPT-4 Turbo</option>
            <option>Claude 3</option>
            <option>Gemini Pro</option>
            <option>本地大模型</option>
          </select>
        </div>
        <div class="p-3 flex-1 overflow-y-auto bg-gray-50">
          <div class="bg-blue-50 border border-blue-100 text-sm p-3 rounded-lg rounded-bl-sm mb-3 max-w-[80%]">
            您好！我是您的AI教案助手。我已分析了教材内容，提取了核心知识点和教学目标。有什么可以帮您优化教案的吗？
          </div>
        </div>
        <div class="flex p-2 border-t border-gray-200">
          <input type="text" placeholder="输入您的问题或指令..." 
                 class="flex-1 border border-gray-300 rounded-full px-4 py-2 text-sm mr-2" />
          <button class="bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center">
            <i class="material-icons text-sm">send</i>
          </button>
        </div>
      </div>

      <!-- 两栏布局 -->
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- 左侧：教案编辑器 -->
        <div class="w-full lg:w-2/3">
          <div class="bg-white shadow rounded-lg mb-6">
            <!-- 教案标题栏 -->
            <div class="border-b border-gray-200 px-6 py-4">
              <div class="flex justify-between items-center">
                <h3 
                  class="text-lg font-medium text-gray-900" 
                  :contenteditable="isEditable"
                  @blur="updateTitle"
                  ref="titleElement"
                >{{ documentTitle }}</h3>
                <div class="flex space-x-2">
                  <button @click="showKnowledgeMap = true" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                    <i class="material-icons text-sm mr-1">account_tree</i>
                    知识图谱
                  </button>
                  <button @click="showSkillMap = true" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                    <i class="material-icons text-sm mr-1">hub</i>
                    能力图谱
                  </button>
                  <button v-if="isEditable" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                    <i class="material-icons text-sm mr-1">undo</i>
                    撤销
                  </button>
                  <button v-if="isEditable" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                    <i class="material-icons text-sm mr-1">redo</i>
                    重做
                  </button>
                  <button v-if="isEditable" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded text-white bg-blue-600 hover:bg-blue-700">
                    <i class="material-icons text-sm mr-1">save</i>
                    保存
                  </button>
                </div>
              </div>
            </div>

            <!-- 教案编辑工具栏 - 仅在可编辑状态显示 -->
            <div v-if="isEditable" class="border-b border-gray-200 px-4 py-2 bg-gray-50">
              <div class="flex flex-wrap items-center gap-1">
                <div class="flex border-r border-gray-300 pr-1 mr-1">
                  <button class="editor-btn" title="加粗" @click="execCommand('bold')">
                    <i class="material-icons text-sm">format_bold</i>
                  </button>
                  <button class="editor-btn" title="斜体" @click="execCommand('italic')">
                    <i class="material-icons text-sm">format_italic</i>
                  </button>
                  <button class="editor-btn" title="下划线" @click="execCommand('underline')">
                    <i class="material-icons text-sm">format_underlined</i>
                  </button>
                  <button class="editor-btn" title="删除线" @click="execCommand('strikeThrough')">
                    <i class="material-icons text-sm">strikethrough_s</i>
                  </button>
                </div>
                
                <div class="flex border-r border-gray-300 pr-1 mr-1">
                  <button class="editor-btn" title="标题1" @click="execCommand('formatBlock', 'h1')">
                    <span class="font-bold text-xs">H1</span>
                  </button>
                  <button class="editor-btn" title="标题2" @click="execCommand('formatBlock', 'h2')">
                    <span class="font-bold text-xs">H2</span>
                  </button>
                  <button class="editor-btn" title="标题3" @click="execCommand('formatBlock', 'h3')">
                    <span class="font-bold text-xs">H3</span>
                  </button>
                  <button class="editor-btn" title="段落" @click="execCommand('formatBlock', 'p')">
                    <i class="material-icons text-sm">notes</i>
                  </button>
                </div>
                
                <div class="flex border-r border-gray-300 pr-1 mr-1">
                  <button class="editor-btn" title="左对齐" @click="execCommand('justifyLeft')">
                    <i class="material-icons text-sm">format_align_left</i>
                  </button>
                  <button class="editor-btn" title="居中对齐" @click="execCommand('justifyCenter')">
                    <i class="material-icons text-sm">format_align_center</i>
                  </button>
                  <button class="editor-btn" title="右对齐" @click="execCommand('justifyRight')">
                    <i class="material-icons text-sm">format_align_right</i>
                  </button>
                  <button class="editor-btn" title="两端对齐" @click="execCommand('justifyFull')">
                    <i class="material-icons text-sm">format_align_justify</i>
                  </button>
                </div>
                
                <div class="flex border-r border-gray-300 pr-1 mr-1">
                  <button class="editor-btn" title="无序列表" @click="execCommand('insertUnorderedList')">
                    <i class="material-icons text-sm">format_list_bulleted</i>
                  </button>
                  <button class="editor-btn" title="有序列表" @click="execCommand('insertOrderedList')">
                    <i class="material-icons text-sm">format_list_numbered</i>
                  </button>
                  <button class="editor-btn" title="减少缩进" @click="execCommand('outdent')">
                    <i class="material-icons text-sm">format_indent_decrease</i>
                  </button>
                  <button class="editor-btn" title="增加缩进" @click="execCommand('indent')">
                    <i class="material-icons text-sm">format_indent_increase</i>
                  </button>
                </div>
                
                <div class="flex">
                  <button class="editor-btn" title="撤销" @click="execCommand('undo')">
                    <i class="material-icons text-sm">undo</i>
                  </button>
                  <button class="editor-btn" title="重做" @click="execCommand('redo')">
                    <i class="material-icons text-sm">redo</i>
                  </button>
                  <button class="editor-btn" title="清除格式" @click="execCommand('removeFormat')">
                    <i class="material-icons text-sm">format_clear</i>
                  </button>
                </div>
              </div>
            </div>

            <!-- 教案编辑区 -->
            <div class="p-6" id="editor-container">
              <div id="editor-content" ref="editorContent" class="outline-none min-h-[500px] markdown-content" :contenteditable="isEditable">
                <!-- Content will be rendered here by markdown-it -->
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：控制面板 -->
        <div class="w-full lg:w-1/3">
          <!-- 面板标签栏 -->
          <div class="bg-white shadow rounded-lg mb-6">
            <div class="border-b border-gray-200">
              <nav class="-mb-px flex" aria-label="Tabs">
                <button 
                  @click="activeTab = 'materialInfo'" 
                  class="w-full py-4 px-1 text-center border-b-2 border-blue-500 font-medium text-sm text-blue-600"
                >
                  <i class="material-icons text-sm mr-2">menu_book</i>{{ mode === 'create' ? '教材信息' : '教案信息' }}
                </button>
              </nav>
            </div>

            <!-- 教材信息面板内容不同 -->
            <div class="p-6">
              <div class="space-y-4">
                
                <!-- 创建模式才显示文件上传 -->
                <div v-if="mode === 'create'" class="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-6 text-center bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div class="mb-4">
                    <i class="material-icons text-5xl text-blue-500">upload_file</i>
                  </div>
                  <h4 class="text-gray-700 font-medium text-lg mb-2">拖拽或点击上传教材文件</h4>
                  <p class="text-sm text-gray-500 mb-4">支持Word格式</p>
                  <label class="inline-flex items-center px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer">
                    <i class="material-icons text-sm mr-2">description</i>
                    选择文件
                    <input type="file" class="hidden" @change="handleFileSelect" accept=".pdf,.doc,.docx,.ppt,.pptx,.md,.txt">
                  </label>
                  <div v-if="selectedFile" class="mt-4 p-2 bg-blue-50 border border-blue-100 rounded-md inline-flex items-center">
                    <i class="material-icons text-blue-500 mr-2">description</i>
                    <span class="text-sm text-blue-700">{{ selectedFile.name }}</span>
                  </div>
                </div>

                <!-- 编辑/查看模式显示已上传的文件信息 -->
                <div v-if="mode !== 'create' && selectedFile" class="mb-6">
                  <h4 class="text-sm font-medium text-gray-700 mb-2">已上传的教材</h4>
                  <div class="p-2 bg-blue-50 border border-blue-100 rounded-md flex items-center">
                    <i class="material-icons text-blue-500 mr-2">description</i>
                    <span class="text-sm text-blue-700">{{ selectedFile.name }}</span>
                  </div>
                </div>

                <!-- 添加学科选择 -->
                <div>
                  <label for="subject-select" class="block text-sm font-medium text-gray-700 mb-1">关联学科</label>
                  <select 
                    id="subject-select" 
                    v-model="subjectId" 
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    :disabled="mode === 'view'"
                  >
                    <option :value="null" disabled>请选择学科</option>
                    <option v-for="subject in subjects" :key="subject.id" :value="subject.id">
                      {{ subject.name }}
                    </option>
                  </select>
                </div>

                <div>
                  <label for="lesson-template" class="block text-sm font-medium text-gray-700 mb-1">教案模板</label>
                  <select 
                    id="lesson-template" 
                    v-model="lessonTemplate" 
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    :disabled="mode === 'view'"
                  >
                    <option selected>标准教学模板</option>
                    <option>互动式教学模板</option>
                    <option>探究式教学模板</option>
                    <option>案例教学模板</option>
                    <option>PBL问题导向模板</option>
                  </select>
                </div>
              
                
                <!-- 教学风格 -->
                <div>
                  <label for="teaching-style" class="block text-sm font-medium text-gray-700 mb-1">教学风格</label>
                  <select 
                    id="teaching-style" 
                    v-model="teachingStyle" 
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    :disabled="mode === 'view'"
                  >
                    <option selected>平衡型（理论与实践并重）</option>
                    <option>理论深入型（注重概念理解）</option>
                    <option>实践应用型（注重实际应用）</option>
                    <option>探究发现型（引导自主思考）</option>
                  </select>
                </div>
                
                
                
                <!-- 具体要求输入框 -->
                <div>
                  <label for="specific-requirements" class="block text-sm font-medium text-gray-700 mb-1">输入你描述的具体要求</label>
                  <div class="relative">
                    <textarea 
                      id="specific-requirements" 
                      v-model="specificRequirements" 
                      rows="6" 
                      class="mt-1 block w-full px-3 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md" 
                      placeholder="例如：面向大学一年级学生，重点讲解Python基础语法和数据结构，增加更多实际应用案例..."
                      :disabled="mode === 'view'"
                    ></textarea>
                    <button 
                      v-if="mode !== 'view'"
                      id="intelligent-expansion" 
                      class="absolute right-2 bottom-2 inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <i class="material-icons text-xs mr-1">auto_fix_high</i>
                      智能扩写
                    </button>
                  </div>
                </div>

                <!-- 根据模式显示不同的按钮 -->
                <button 
                  v-if="mode === 'create'"
                  @click="confirmUpload" 
                  :disabled="isGenerating || !selectedFile || !subjectId" 
                  class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  <i class="material-icons text-sm mr-2">{{ isGenerating ? 'autorenew' : 'auto_fix_high' }}</i>
                  {{ isGenerating ? '正在生成中...' : '生成教案' }}
                </button>

                <button 
                  v-if="mode === 'edit'"
                  @click="updateLessonPlan" 
                  class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <i class="material-icons text-sm mr-2">save</i>
                  保存修改
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 知识图谱弹窗 -->
    <div v-if="showKnowledgeMap" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 max-h-screen overflow-y-auto">
        <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">Python编程基础 - 知识图谱</h3>
          <button @click="showKnowledgeMap = false" class="text-gray-400 hover:text-gray-500">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="p-6">
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
            <p class="text-sm text-gray-700">知识图谱展示了本课程的核心概念及其关联关系，有助于理解知识结构和学习路径。</p>
          </div>
          
          <div class="h-[400px] border border-gray-200 rounded-lg shadow-sm p-4 flex items-center justify-center mb-6 bg-blue-50 overflow-hidden">
            <!-- 知识图谱SVG可视化 -->
            <svg width="800" height="400" class="max-w-full max-h-full">
              <!-- 背景网格 -->
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
              
              <!-- 中心节点 - Python基础 -->
              <g>
                <circle cx="400" cy="200" r="50" fill="#4F46E5" />
                <text x="400" y="200" text-anchor="middle" fill="white" dy=".3em" font-weight="bold">Python基础</text>
                
                <!-- 连接线和节点 - 基础语法 -->
                <line x1="400" y1="150" x2="250" y2="100" stroke="#9EA3AF" stroke-width="2" />
                <circle cx="250" cy="100" r="40" fill="#3B82F6" />
                <text x="250" y="100" text-anchor="middle" fill="white" dy=".3em" font-weight="bold">基础语法</text>
                
                <!-- 基础语法子节点 -->
                <line x1="250" y1="100" x2="150" y2="80" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="150" cy="80" r="25" fill="#60A5FA" />
                <text x="150" y="80" text-anchor="middle" fill="white" dy=".3em" font-size="10">变量</text>
                
                <line x1="250" y1="100" x2="200" y2="40" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="200" cy="40" r="25" fill="#60A5FA" />
                <text x="200" y="40" text-anchor="middle" fill="white" dy=".3em" font-size="10">运算符</text>
                
                <line x1="250" y1="100" x2="300" y2="40" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="300" cy="40" r="25" fill="#60A5FA" />
                <text x="300" y="40" text-anchor="middle" fill="white" dy=".3em" font-size="10">控制语句</text>
                
                <!-- 连接线和节点 - 数据结构 -->
                <line x1="450" y1="200" x2="600" y2="200" stroke="#9EA3AF" stroke-width="2" />
                <circle cx="600" cy="200" r="40" fill="#10B981" />
                <text x="600" y="200" text-anchor="middle" fill="white" dy=".3em" font-weight="bold">数据结构</text>
                
                <!-- 数据结构子节点 -->
                <line x1="600" y1="200" x2="650" y2="120" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="650" cy="120" r="25" fill="#34D399" />
                <text x="650" y="120" text-anchor="middle" fill="white" dy=".3em" font-size="10">列表</text>
                
                <line x1="600" y1="200" x2="700" y2="170" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="700" cy="170" r="25" fill="#34D399" />
                <text x="700" y="170" text-anchor="middle" fill="white" dy=".3em" font-size="10">元组</text>
                
                <line x1="600" y1="200" x2="700" y2="230" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="700" cy="230" r="25" fill="#34D399" />
                <text x="700" y="230" text-anchor="middle" fill="white" dy=".3em" font-size="10">字典</text>
                
                <line x1="600" y1="200" x2="650" y2="280" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="650" cy="280" r="25" fill="#34D399" />
                <text x="650" y="280" text-anchor="middle" fill="white" dy=".3em" font-size="10">集合</text>
                
                <!-- 连接线和节点 - 函数 -->
                <line x1="400" y1="250" x2="400" y2="350" stroke="#9EA3AF" stroke-width="2" />
                <circle cx="400" cy="350" r="40" fill="#8B5CF6" />
                <text x="400" y="350" text-anchor="middle" fill="white" dy=".3em" font-weight="bold">函数</text>
                
                <!-- 函数子节点 -->
                <line x1="400" y1="350" x2="300" y2="380" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="300" cy="380" r="25" fill="#A78BFA" />
                <text x="300" y="380" text-anchor="middle" fill="white" dy=".3em" font-size="10">函数定义</text>
                
                <line x1="400" y1="350" x2="350" y2="320" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="350" cy="320" r="25" fill="#A78BFA" />
                <text x="350" y="320" text-anchor="middle" fill="white" dy=".3em" font-size="10">参数传递</text>
                
                <line x1="400" y1="350" x2="450" y2="320" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="450" cy="320" r="25" fill="#A78BFA" />
                <text x="450" y="320" text-anchor="middle" fill="white" dy=".3em" font-size="10">匿名函数</text>
                
                <line x1="400" y1="350" x2="500" y2="380" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="500" cy="380" r="25" fill="#A78BFA" />
                <text x="500" y="380" text-anchor="middle" fill="white" dy=".3em" font-size="10">作用域</text>
                
                <!-- 连接线和节点 - 模块和包 -->
                <line x1="350" y1="200" x2="200" y2="250" stroke="#9EA3AF" stroke-width="2" />
                <circle cx="200" cy="250" r="40" fill="#EC4899" />
                <text x="200" y="250" text-anchor="middle" fill="white" dy=".3em" font-weight="bold">模块和包</text>
                
                <!-- 模块和包子节点 -->
                <line x1="200" y1="250" x2="150" y2="200" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="150" cy="200" r="25" fill="#F472B6" />
                <text x="150" y="200" text-anchor="middle" fill="white" dy=".3em" font-size="10">导入模块</text>
                
                <line x1="200" y1="250" x2="130" y2="250" stroke="#9EA3AF" stroke-width="1.5" />
                <circle cx="130" cy="250" r="25" fill="#F472B6" />
                <text x="130" y="250" text-anchor="middle" fill="white" dy=".3em" font-size="10">标准库</text>
              </g>
            </svg>
          </div>
          
          <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 rounded-lg p-3 border border-blue-100">
              <h4 class="font-semibold text-blue-700 mb-2 flex items-center">
                <i class="material-icons mr-2">code</i>
                基础语法
              </h4>
              <ul class="text-sm space-y-1">
                <li>• 变量和数据类型</li>
                <li>• 运算符和表达式</li>
                <li>• 控制流程语句</li>
              </ul>
            </div>
            <div class="bg-green-50 rounded-lg p-3 border border-green-100">
              <h4 class="font-semibold text-green-700 mb-2 flex items-center">
                <i class="material-icons mr-2">view_in_ar</i>
                数据结构
              </h4>
              <ul class="text-sm space-y-1">
                <li>• 列表与元组</li>
                <li>• 字典与集合</li>
                <li>• 列表推导式</li>
              </ul>
            </div>
            <div class="bg-purple-50 rounded-lg p-3 border border-purple-100">
              <h4 class="font-semibold text-purple-700 mb-2 flex items-center">
                <i class="material-icons mr-2">settings</i>
                函数
              </h4>
              <ul class="text-sm space-y-1">
                <li>• 函数定义与调用</li>
                <li>• 参数传递</li>
                <li>• 匿名函数</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="border-t border-gray-200 px-6 py-4 flex justify-end">
          <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
            <i class="material-icons text-sm mr-2">download</i>
            下载知识图谱
          </button>
        </div>
      </div>
    </div>

    <!-- 能力图谱弹窗 -->
    <div v-if="showSkillMap" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 max-h-screen overflow-y-auto">
        <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">Python编程基础 - 能力图谱</h3>
          <button @click="showSkillMap = false" class="text-gray-400 hover:text-gray-500">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="p-6">
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
            <p class="text-sm text-gray-700">能力图谱展示了本课程将培养的核心能力和技能水平，帮助学生明确学习目标和进度。</p>
          </div>
          
          <div class="h-[400px] border border-gray-200 rounded-lg shadow-sm p-4 flex items-center justify-center mb-6 bg-blue-50 overflow-hidden">
            <!-- 能力雷达图SVG可视化 -->
            <svg width="600" height="400" class="max-w-full max-h-full">
              <!-- 背景和坐标系统 -->
              <g transform="translate(300, 200)">
                <!-- 背景圆网格 -->
                <circle cx="0" cy="0" r="150" fill="none" stroke="#e5e7eb" stroke-width="1" />
                <circle cx="0" cy="0" r="120" fill="none" stroke="#e5e7eb" stroke-width="1" />
                <circle cx="0" cy="0" r="90" fill="none" stroke="#e5e7eb" stroke-width="1" />
                <circle cx="0" cy="0" r="60" fill="none" stroke="#e5e7eb" stroke-width="1" />
                <circle cx="0" cy="0" r="30" fill="none" stroke="#e5e7eb" stroke-width="1" />
                
                <!-- 坐标轴线 -->
                <line x1="-150" y1="0" x2="150" y2="0" stroke="#d1d5db" stroke-width="1" />
                <line x1="0" y1="-150" x2="0" y2="150" stroke="#d1d5db" stroke-width="1" />
                <line x1="-106" y1="-106" x2="106" y2="106" stroke="#d1d5db" stroke-width="1" />
                <line x1="106" y1="-106" x2="-106" y2="106" stroke="#d1d5db" stroke-width="1" />
                
                <!-- 坐标轴标签 -->
                <text x="0" y="-160" text-anchor="middle" fill="#4B5563" font-size="12">团队协作能力</text>
                <text x="115" y="-115" text-anchor="start" fill="#4B5563" font-size="12">算法设计能力</text>
                <text x="160" y="0" text-anchor="start" fill="#4B5563" font-size="12">问题分析能力</text>
                <text x="0" y="160" text-anchor="middle" fill="#4B5563" font-size="12">代码实现能力</text>
                <text x="-160" y="0" text-anchor="end" fill="#4B5563" font-size="12">调试优化能力</text>
                <text x="-115" y="-115" text-anchor="end" fill="#4B5563" font-size="12">学习迁移能力</text>
                
                <!-- 学生平均能力多边形 -->
                <path d="M 0,-90 L 75,-75 L 105,30 L 0,120 L -75,0 L -60,-60 Z" 
                      fill="rgba(37, 99, 235, 0.2)" 
                      stroke="#2563EB" 
                      stroke-width="2" />
                
                <!-- 能力点 -->
                <circle cx="0" cy="-90" r="5" fill="#2563EB" />
                <circle cx="75" cy="-75" r="5" fill="#2563EB" />
                <circle cx="105" cy="30" r="5" fill="#2563EB" />
                <circle cx="0" cy="120" r="5" fill="#2563EB" />
                <circle cx="-75" cy="0" r="5" fill="#2563EB" />
                <circle cx="-60" cy="-60" r="5" fill="#2563EB" />
                
                <!-- 期望水平多边形 -->
                <path d="M 0,-120 L 90,-90 L 120,45 L 0,140 L -90,0 L -75,-75 Z" 
                      fill="none" 
                      stroke="#F59E0B" 
                      stroke-width="2" 
                      stroke-dasharray="5,5" />
                
                <!-- 图例 -->
                <rect x="-145" y="-180" width="15" height="15" fill="rgba(37, 99, 235, 0.2)" stroke="#2563EB" stroke-width="2" />
                <text x="-125" y="-168" text-anchor="start" fill="#4B5563" font-size="11">学生平均水平</text>
                
                <rect x="15" y="-180" width="15" height="15" fill="none" stroke="#F59E0B" stroke-width="2" stroke-dasharray="5,5" />
                <text x="35" y="-168" text-anchor="start" fill="#4B5563" font-size="11">期望达到水平</text>
              </g>
            </svg>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border border-gray-200 rounded-lg overflow-hidden">
              <div class="bg-indigo-50 px-4 py-3">
                <h4 class="font-semibold text-indigo-700 flex items-center">
                  <i class="material-icons mr-2">laptop_chromebook</i>
                  技术能力
                </h4>
              </div>
              <div class="p-4">
                <div class="mb-4">
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">基础语法掌握</span>
                    <span class="text-sm text-gray-600">90%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-indigo-600 h-2 rounded-full" style="width: 90%"></div>
                  </div>
                </div>
                <div class="mb-4">
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">数据结构应用</span>
                    <span class="text-sm text-gray-600">75%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-indigo-600 h-2 rounded-full" style="width: 75%"></div>
                  </div>
                </div>
                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">函数编程能力</span>
                    <span class="text-sm text-gray-600">65%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-indigo-600 h-2 rounded-full" style="width: 65%"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="border border-gray-200 rounded-lg overflow-hidden">
              <div class="bg-orange-50 px-4 py-3">
                <h4 class="font-semibold text-orange-700 flex items-center">
                  <i class="material-icons mr-2">psychology</i>
                  实践能力
                </h4>
              </div>
              <div class="p-4">
                <div class="mb-4">
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">问题分析能力</span>
                    <span class="text-sm text-gray-600">70%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 70%"></div>
                  </div>
                </div>
                <div class="mb-4">
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">算法设计能力</span>
                    <span class="text-sm text-gray-600">60%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 60%"></div>
                  </div>
                </div>
                <div>
                  <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">调试与优化能力</span>
                    <span class="text-sm text-gray-600">55%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 55%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="border-t border-gray-200 px-6 py-4 flex justify-end">
          <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
            <i class="material-icons text-sm mr-2">download</i>
            下载能力图谱
          </button>
        </div>
      </div>
    </div>

   

    <!-- 成功通知 -->
    <div 
      class="fixed top-5 right-5 bg-green-500 text-white px-4 py-3 rounded-md shadow-lg transition-all duration-300 transform z-[100]"
      :class="notification.show ? 'translate-y-0 opacity-100' : '-translate-y-10 opacity-0 pointer-events-none'"
    >
      <div class="flex items-center">
        <i class="material-icons mr-2">check_circle</i>
        <span>{{ notification.message }}</span>
      </div>
    </div>
  </TeacherLayout>
</template>

<script setup>
import { ref, onMounted, reactive, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { lessonPlanApi } from '@/api/lessonPlan'
import { subjectApi } from '@/api/subject'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// Router
const router = useRouter()
const route = useRoute()

// 确定当前模式：创建、编辑或查看
const mode = computed(() => {
  // 检查路由路径
  if (route.path.includes('/edit')) {
    return 'edit'
  } else if (route.path.includes('/detail')) {
    return 'view'
  } else {
    return 'create'
  }
})

// 根据模式设置页面标题
const pageTitle = computed(() => {
  switch(mode.value) {
    case 'edit': return '编辑教学设计'
    case 'view': return '查看教学设计'
    default: return '创建教学设计'
  }
})

// 从路由参数获取教学设计ID
const lessonPlanId = computed(() => {
  return route.params.id
})

// 是否允许编辑（创建和编辑模式允许，查看模式不允许）
const isEditable = computed(() => {
  return mode.value !== 'view'
})

// Initialize markdown-it
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {}
    }
    return '' // use default escaping
  }
})

// Teacher Data - In a real app, this would come from API
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// Tab management
const activeTab = ref('materialInfo')
const uploadTab = ref('upload')

// AI Chat visibility
const aiChatVisible = ref(false)

// Modal states
const showUploadModal = ref(false)
const showKnowledgeMap = ref(false)
const showSkillMap = ref(false)

// Selected file
const selectedFile = ref(null)

// Form data
const lessonTemplate = ref('标准教学模板')
const teachingStyle = ref('平衡型（理论与实践并重）')
const specificRequirements = ref('')

// Loading state
const isLoading = ref(false)

// Notification system
const notification = reactive({
  show: false,
  message: ''
})

// Editor ref
const editorContent = ref(null)

// 添加状态变量
const isGenerating = ref(false)
const generatedContent = ref('')
const generationProgress = ref(0)
const currentTaskId = ref(null)
const eventSource = ref(null)
const subjectId = ref(null)

// 初始化学科列表
const subjects = ref([])

// 文档标题
const documentTitle = ref('新建教学设计')
const titleElement = ref(null)

// 更新标题
const updateTitle = () => {
  if (titleElement.value) {
    documentTitle.value = titleElement.value.textContent
  }
}

// 从文件名生成标题
const generateTitleFromFile = (fileName) => {
  if (!fileName) return '新建教学设计'
  
  // 移除扩展名
  const nameWithoutExt = fileName.split('.').slice(0, -1).join('.')
  
  // 生成标题
  return `教学设计-${nameWithoutExt}`
}

// Toggle AI Chat visibility
const toggleAIChat = () => {
  aiChatVisible.value = !aiChatVisible.value
}

// Minimize AI Chat
const minimizeAIChat = () => {
  aiChatVisible.value = false
}

// Execute document command for rich text editing
const execCommand = (command, value = null) => {
  document.execCommand(command, false, value)
}

// Show success notification
const showNotification = (message) => {
  notification.message = message
  notification.show = true
  
  setTimeout(() => {
    notification.show = false
  }, 3000)
}

// Navigation methods
const goBack = () => {
  router.push('/teacher/lesson-plan-projects')
}

// Save the lesson plan to database
const saveLessonPlan = async () => {
  if (!currentTaskId.value) {
    showNotification('请先上传教材并生成教学设计')
    return
  }
  
  try {
    isLoading.value = true
    
    // 检查任务状态，确保内容已生成完成
    const statusResponse = await lessonPlanApi.getTaskStatus(currentTaskId.value)
    const taskStatus = statusResponse.data.status
    
    if (taskStatus !== 'completed') {
      showNotification('教学设计内容尚未生成完成，请等待生成完毕')
      isLoading.value = false
      return
    }
    
    // 使用文档中的标题
    const title = documentTitle.value
    
    // 获取选择的学科ID
    const subject = subjectId.value
    
    if (!subject) {
      showNotification('请选择关联学科')
      isLoading.value = false
      return
    }
    
    // 调用API保存教学设计
    const response = await lessonPlanApi.saveLessonPlan({
      taskId: currentTaskId.value,
      title: title,
      subjectId: subject
    })
    console.log('保存教学设计返回数据:', response)
    
    if (response.code === 200) {
      showNotification(response.message)
      // 可以跳转到教学设计列表页面
      setTimeout(() => {
        router.push('/teacher/lesson-plan-projects')
      }, 1500)
    } else {
      showNotification(`保存失败: ${response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('保存教学设计失败:', error)
    showNotification(`保存失败: ${error.message || '未知错误'}`)
  } finally {
    isLoading.value = false
  }
}

// Save and return to project list
const saveAndReturn = async () => {
  if (mode.value === 'edit') {
    await updateLessonPlan()
  } else {
    await saveLessonPlan()
  }
}

// Save as draft
const saveAsDraft = () => {
  console.log('保存为草稿')
  // Here we would implement draft saving logic
  
  // Show success notification
  showNotification('已保存为草稿')
  
  // Navigate back to list
  router.push('/teacher/lesson-plan-projects')
}

// 选择文件处理函数
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    selectedFile.value = file
    // 从文件名生成标题
    documentTitle.value = generateTitleFromFile(file.name)
    showNotification(`已选择文件: ${file.name}`)
  }
}

// 添加处理流式返回的函数
const handleStreamResponse = async (taskId) => {
  // 保存当前任务ID
  currentTaskId.value = taskId
  
  // 清空之前的内容
  generatedContent.value = ''
  isGenerating.value = true
  
  // 如果标题是默认的，则根据文件名生成标题
  if (documentTitle.value === '新建教学设计' && selectedFile.value) {
    documentTitle.value = generateTitleFromFile(selectedFile.value.name)
  }
  
  try {
    // 使用API方法获取流式响应
    const response = await lessonPlanApi.getLessonPlanStream(taskId)
    
    // 创建Reader
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    
    // 处理流式响应
    while (true) {
      const { done, value } = await reader.read()
      
      if (done) {
        console.log('Stream complete')
        isGenerating.value = false
        showNotification('教学设计生成完成')
        break
      }
      
      // 解码二进制数据
      const chunk = decoder.decode(value, { stream: true })
      
      // 处理SSE格式的数据
      chunk.split('\n\n').forEach(line => {
        if (line.startsWith('data: ')) {
          try {
            const dataStr = line.substring(6) // 去掉 'data: ' 前缀
            
            if (dataStr === '[DONE]') {
              isGenerating.value = false
              return  // 流结束标记
            }
            
            const data = JSON.parse(dataStr)
            
            // 处理内容更新
            if (data.content) {
              // 添加文本内容
              generatedContent.value += data.content
              
              // 使用markdown-it渲染内容
              if (editorContent.value) {
                editorContent.value.innerHTML = md.render(generatedContent.value)
              }
              
              // 滚动编辑器到底部
              scrollEditorToBottom()
              
              // 更新进度条（根据内容长度估算）
              generationProgress.value = Math.min(95, 30 + Math.floor(generatedContent.value.length / 50))
            }
            
            // 检查生成是否完成
            if (data.status === 'completed') {
              isGenerating.value = false
              generationProgress.value = 100
              showNotification('教学设计生成完成')
            }
            
            // 处理错误信息
            if (data.error) {
              showNotification(`生成失败: ${data.error}`)
              isGenerating.value = false
            }
          } catch (e) {
            console.error('Error parsing event data:', e)
          }
        }
      })
    }
  } catch (error) {
    console.error('流式请求失败:', error)
    showNotification(`连接失败: ${error.message || '未知错误'}`)
    isGenerating.value = false
  }
}

// 滚动编辑器到底部，实现自动滚动效果
const scrollEditorToBottom = () => {
  if (editorContent.value) {
    editorContent.value.scrollTop = editorContent.value.scrollHeight
  }
}

// 关闭流式连接的函数
const closeEventSource = () => {
  if (eventSource.value) {
    eventSource.value.close()
    eventSource.value = null
  }
}

// 更新编辑器内容的函数
const updateEditorContent = (content) => {
  if (editorContent.value) {
    // 使用markdown-it渲染内容
    editorContent.value.innerHTML = md.render(content)
  }
}

// 修改确认上传函数，支持流式生成
const confirmUpload = async () => {
  if (mode.value !== 'create' && !selectedFile.value) {
    // 编辑模式，使用已有的内容进行更新
    return updateLessonPlan()
  }
  
  // 原有的创建逻辑 
  if (!selectedFile.value) {
    showNotification('请先选择文件')
    return
  }
  
  try {
    isLoading.value = true
    
    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('lessonTemplate', lessonTemplate.value)
    formData.append('teachingStyle', teachingStyle.value)
    
    if (specificRequirements.value) {
      formData.append('specificRequirements', specificRequirements.value)
    }
    
    // 调用API
    const response = await lessonPlanApi.createLessonPlan(formData)
    
    // 关闭弹窗
    showUploadModal.value = false
    
    // 显示成功通知
    showNotification('教材文件已成功导入，开始生成教学设计')
    
    // 获取任务ID
    const taskId = response.data.task_id
    
    // 开始流式生成
    handleStreamResponse(taskId)
    
  } catch (error) {
    console.error('上传失败:', error)
    showNotification(`上传失败: ${error.message || '未知错误'}`)
  } finally {
    isLoading.value = false
  }
}

// 获取当前教学设计内容，用于编辑
const getCurrentContent = () => {
  return generatedContent.value || ''
}

// 用户手动编辑教学设计内容
const updateContent = (content) => {
  generatedContent.value = content
  if (editorContent.value) {
    editorContent.value.innerHTML = md.render(content)
  }
}

// 组件销毁时关闭EventSource
onUnmounted(() => {
  closeEventSource()
})

// Initialize editor when component is mounted
onMounted(() => {
  if (editorContent.value) {
    editorContent.value.focus()
  }
  
  // 加载学科列表
  loadSubjects()
  
  // 如果是编辑或查看模式，获取现有数据
  if (mode.value !== 'create') {
    fetchLessonPlanData()
  }
  
  // Add event listeners for editor buttons state
  document.addEventListener('selectionchange', updateButtonStates)
})

// Update button states based on current selection
const updateButtonStates = () => {
  // This would check the current format states and update the editor button active states
  console.log('Selection changed, updating button states')
}

// 获取学科列表
const loadSubjects = async () => {
  try {
    const response = await subjectApi.getSubjects()
    console.log('获取学科列表返回数据:', response)
    
    if (response.code === 200 && response.data) {
      subjects.value = response.data
    } else {
      console.warn('未获取到学科数据:', response.message)
    }
  } catch (error) {
    console.error('获取学科列表失败:', error)
  }
}

// 添加初始化数据获取
const fetchLessonPlanData = async () => {
  if (mode.value === 'create') return
  
  try {
    isLoading.value = true
    // 获取教学设计详情
    const response = await lessonPlanApi.getLessonPlan(lessonPlanId.value)
    console.log('获取教学设计详情返回数据:', response)
    
    if (response.code === 200 && response.data) {
      const planData = response.data
      
      // 设置标题
      documentTitle.value = planData.title || '未命名教学设计'
      
      // 填充表单数据
      subjectId.value = planData.subject?.id
      lessonTemplate.value = planData.lesson_template || '标准教学模板'
      teachingStyle.value = planData.teaching_style || '平衡型（理论与实践并重）'
      specificRequirements.value = planData.specific_requirements || ''
      
      // 设置内容
      generatedContent.value = planData.content || ''
      
      // 使用markdown-it渲染内容
      if (editorContent.value) {
        editorContent.value.innerHTML = md.render(generatedContent.value)
        
        // 在查看模式下禁用编辑
        if (mode.value === 'view') {
          editorContent.value.contentEditable = false
          editorContent.value.classList.add('cursor-default')
        }
      }
      
      // 假装已经上传了文件（仅用于UI展示）
      if (planData.material_file_path) {
        selectedFile.value = { name: planData.material_file_path.split('/').pop() || '已上传文件' }
      }
    } else {
      showNotification(`获取失败: ${response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('获取教学设计失败:', error)
    showNotification(`获取失败: ${error.message || '未知错误'}`)
  } finally {
    isLoading.value = false
  }
}

// 添加更新教学设计方法
const updateLessonPlan = async () => {
  try {
    isLoading.value = true
    
    // 构建更新数据
    const updateData = {
      title: documentTitle.value,
      subject_id: subjectId.value,
      lesson_template: lessonTemplate.value,
      teaching_style: teachingStyle.value,
      specific_requirements: specificRequirements.value,
      content: generatedContent.value || editorContent.value?.innerHTML
    }
    
    // 调用API更新
    const response = await lessonPlanApi.updateLessonPlan(lessonPlanId.value, updateData)
    console.log('更新教学设计返回数据:', response)
    
    // 后端返回code: 200代表成功
    if (response.code === 200) {
      showNotification(response.message)
      setTimeout(() => {
        router.push('/teacher/lesson-plan-projects')
      }, 1500)
    } else {
      console.error('更新教学设计返回错误:', response)
      showNotification(`更新失败: ${response.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('更新教学设计失败:', error)
    showNotification(`更新失败: ${error.message || '未知错误'}`)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* Editor button styles */
.editor-btn {
  @apply inline-flex items-center justify-center w-8 h-8 rounded hover:bg-blue-50 hover:text-blue-600 transition-colors;
}
.editor-btn.active {
  @apply bg-blue-100 text-blue-600;
}

/* Switch styles for toggles */
.switch input:checked + .slider {
  @apply bg-blue-500;
}
.switch input:checked + .slider:before {
  transform: translateX(1.5rem);
}

/* Editor content styles */
#editor-content:focus {
  @apply outline-none;
}
#editor-content[contenteditable="true"] {
  @apply border border-transparent min-h-[500px];
}
#editor-content[contenteditable="true"]:focus {
  @apply border-gray-200;
}

/* Animation for AI chat */
.scale-20 {
  transform: scale(0.2);
}

/* Markdown Styles */
:deep(.markdown-content) {
  line-height: 1.6;
  font-size: 16px;
}

:deep(.markdown-content pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin: 8px 0;
}

:deep(.markdown-content code) {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}

:deep(.markdown-content pre code) {
  padding: 0;
  background-color: transparent;
}

:deep(.markdown-content p) {
  margin: 8px 0;
}

:deep(.markdown-content ul), :deep(.markdown-content ol) {
  padding-left: 2em;
  margin: 8px 0;
}

:deep(.markdown-content li) {
  margin: 4px 0;
}

:deep(.markdown-content h1) { 
  font-size: 2.5em; 
  font-weight: 700;
  margin-top: 1.2em;
  margin-bottom: 0.6em;
  color: #1a202c;
}

:deep(.markdown-content h2) { 
  font-size: 2em;
  font-weight: 600;
  margin-top: 1em;
  margin-bottom: 0.5em;
  color: #2d3748;
}

:deep(.markdown-content h3) { 
  font-size: 1.75em;
  font-weight: 600;
  margin-top: 0.8em;
  margin-bottom: 0.4em;
  color: #4a5568;
}

:deep(.markdown-content h4) { 
  font-size: 1.5em;
  font-weight: 500;
  margin-top: 0.6em;
  margin-bottom: 0.3em;
  color: #4a5568;
}

:deep(.markdown-content h5) { 
  font-size: 1.25em;
  font-weight: 500;
  margin-top: 0.4em;
  margin-bottom: 0.2em;
  color: #4a5568;
}

:deep(.markdown-content h6) { 
  font-size: 1.1em;
  font-weight: 500;
  margin-top: 0.2em;
  margin-bottom: 0.1em;
  color: #4a5568;
}

:deep(.markdown-content blockquote) {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 8px 0;
}

:deep(.markdown-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

:deep(.markdown-content th), 
:deep(.markdown-content td) {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

:deep(.markdown-content tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

:deep(.markdown-content img) {
  max-width: 100%;
  box-sizing: border-box;
}

:deep(.markdown-content a) {
  color: #0366d6;
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}
</style> 