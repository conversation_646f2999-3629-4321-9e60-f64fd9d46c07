#!/bin/bash

# 检查并终止可能存在的旧进程
screen -S ai-education -X quit
screen -S celery-ai-education -X quit

# 短暂等待，确保旧进程已退出
sleep 1

# 启动 main.py，激活 conda 环境
screen -dmS ai-education bash -c "source /root/miniconda3/bin/activate ai-education && python manage.py runserver ; exec bash"

# 启动 celery_main.py，激活 conda 环境
screen -dmS celery-ai-education bash -c "source /root/miniconda3/bin/activate ai-education && python3 celery_worker.py; exec bash"

echo "程序已在后台 screen 会话中启动！"
