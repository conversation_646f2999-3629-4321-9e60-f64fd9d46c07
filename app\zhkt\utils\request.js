import { useUserStore } from '@/store/modules/user'

// baseURL 部署正式环境请修改
const baseURL = 'https://6e51-113-16-138-78.ngrok-free.app/api'

// 请求拦截器
const requestInterceptor = (config) => {
  const userStore = useUserStore()
  if (userStore.token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${userStore.token}`,
	  'ngrok-skip-browser-warning': '69420'
    }
  }
  return config
}

// 响应拦截器
const responseInterceptor = (response) => {
  const { statusCode, data } = response
  
  if (statusCode >= 200 && statusCode < 300) {
    return data
  }
  
  // 处理401未授权的情况
  if (statusCode === 401) {
    const userStore = useUserStore()
    // 清除用户信息
    userStore.logout()
    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }
  
  // 处理错误
  const error = new Error(data.message || '请求失败')
  error.response = response
  throw error
}

// 请求函数
const request = (options) => {
  const { url, method = 'GET', data, header = {} } = options
  
  const config = {
    url: `${baseURL}${url}`,
    method,
    data,
    header
  }
  
  const finalConfig = requestInterceptor(config)
  
  return new Promise((resolve, reject) => {
    uni.request({
      ...finalConfig,
      success: (response) => {
        try {
          const result = responseInterceptor(response)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

export default request 