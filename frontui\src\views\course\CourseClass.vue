<template>
  <div>
    <div class="flex h-full">
      <!-- 左侧班级列表 -->
      <div class="w-64 bg-gray-50 border-r border-gray-200 p-4 flex-shrink-0 overflow-y-auto">
        <div class="mb-4 flex items-center justify-between">
          <span class="font-bold text-lg text-gray-700">班级列表</span>
        </div>
        <div class="mb-4 flex gap-2">
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded flex items-center gap-1 text-sm" @click="onCreateClass">
            <span class="material-icons" style="font-size: 14px;">add</span> 新建班级
          </button>
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded flex items-center gap-1 text-sm" @click="onManageClass">
            <span class="material-icons" style="font-size: 14px;">settings</span> 班级管理
          </button>
        </div>
        <div class="mb-4 flex items-center justify-between">
          <input type="text" v-model="classSearchQuery" placeholder="搜索班级" class="border border-gray-300 rounded-md px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
        </div>
        <ul>
          <li
            v-for="cls in filteredClassList"
            :key="cls.id"
            @click="selectClass(cls)"
            :class="[
              'px-4 py-2 rounded cursor-pointer mb-1',
              selectedClass && selectedClass.id === cls.id ? 'bg-blue-100 text-blue-700 font-semibold' : 'hover:bg-blue-50 text-gray-700'
            ]"
          >
            {{ cls.name }}
            <span class="text-xs text-gray-400 ml-2">({{ getStudentCountByClass(cls.id) }})</span>
          </li>
        </ul>
      </div>
      <!-- 右侧学生管理区 -->
      <div class="flex-1 p-8 overflow-y-auto">
        <div v-if="selectedClass" class="mb-6">
          <div class="flex items-center gap-4 mb-2">
            <h2 class="text-2xl font-bold text-gray-800">{{ selectedClass.name }}</h2>
            <span class="text-gray-500 mt-1">共 {{ filteredStudents.length }} 人</span>
            <button class="ml-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded flex items-center gap-1 text-sm mt-1" @click="onClassSettings">
              <span class="material-icons" style="font-size: 14px;">settings</span> 设置
            </button>
          </div>
          <div class="text-gray-500 text-sm mb-4">班级简介：{{ selectedClass.desc || '暂无简介' }}</div>
        </div>
        <!-- 右侧原有功能区和表格 -->
        <div class="space-y-6">
          <!-- 功能区 -->
          <div class="flex flex-wrap justify-between items-center gap-4 mb-4">
            <div class="flex gap-3">
              <button 
                class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
                @click="showAddStudentModal = true"
              >
                <span class="material-icons text-sm">person_add</span>
                添加学生
              </button>
              <button 
                class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
                @click="showInvitationCodeModal = true"
              >
                <span class="material-icons text-sm">qr_code</span>
                邀请码
              </button>
              <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2" @click="exportStudentList">
                <span class="material-icons text-sm">file_download</span>
                导出名单
              </button>
            </div>
            <div class="flex gap-3">
              <div class="relative">
                <input 
                  type="text" 
                  v-model="studentSearchQuery"
                  placeholder="搜索学生..." 
                  class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
              </div>
              <div class="relative">
                <button 
                  @click="showFilters = !showFilters"
                  class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
                >
                  <span class="material-icons text-gray-600">filter_list</span>
                  筛选
                </button>
                <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
                  <div class="space-y-3">
                    <div class="font-medium">班级</div>
                    <div class="flex flex-wrap gap-2">
                      <span 
                        v-for="cls in classList" 
                        :key="cls.id"
                        class="px-3 py-1 rounded-full text-sm cursor-pointer"
                        :class="selectedFilters.classes.includes(cls.name) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                        @click="toggleFilter('classes', cls.name)"
                      >
                        {{ cls.name }}
                      </span>
                    </div>
                    <div class="font-medium mt-3">状态</div>
                    <div class="flex flex-wrap gap-2">
                      <span 
                        v-for="status in ['活跃', '较少活动', '未激活']" 
                        :key="status"
                        class="px-3 py-1 rounded-full text-sm cursor-pointer"
                        :class="selectedFilters.statuses.includes(status) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                        @click="toggleFilter('statuses', status)"
                      >
                        {{ status }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分类筛选区 -->
          <div class="flex border-b border-gray-200">
            <button 
              v-for="tab in tabs" 
              :key="tab.value"
              @click="currentTab = tab.value"
              class="py-3 px-6 font-medium relative"
              :class="currentTab === tab.value ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'"
            >
              {{ tab.label }}
              <span 
                v-if="currentTab === tab.value" 
                class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"
              ></span>
            </button>
          </div>

          <!-- 表格控制区 -->
          <div class="flex justify-between items-center mb-4 mt-6">
            <div class="flex items-center">
              <input 
                type="checkbox" 
                id="select-all"
                class="rounded text-blue-600 focus:ring-blue-500"
                @change="toggleSelectAll"
                :checked="selectedStudents.length === filteredStudents.length && filteredStudents.length > 0"
              />
              <label for="select-all" class="ml-2 text-sm text-gray-600">全选</label>
              
              <div class="ml-4 flex items-center" v-if="selectedStudents.length > 0">
                <span class="text-sm text-gray-600">已选择 {{ selectedStudents.length }} 名学生</span>
                <button class="ml-3 text-sm text-red-600 hover:text-red-800" >
                  批量操作
                </button>
              </div>
            </div>
            
            <div class="flex items-center gap-4">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 mr-2">每页显示：</span>
                <select 
                  v-model="pageSize" 
                  class="border border-gray-300 rounded-md px-2 py-1 text-sm"
                >
                  <option :value="20">20条</option>
                  <option :value="50">50条</option>
                  <option :value="100">100条</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 学生表格 -->
          <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="w-12 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    选择
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学生信息
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学号
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    班级/专业
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学习状态
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    平均成绩
                  </th>
                  <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="student in paginatedStudents" :key="student.id" class="hover:bg-gray-50 cursor-pointer" @click="viewStudentDetails(student)">
                  <td class="px-2 py-4 whitespace-nowrap" @click.stop>
                    <input 
                      type="checkbox" 
                      :checked="selectedStudents.includes(student.id)"
                      @change="toggleStudentSelection(student.id)"
                      class="rounded text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <img class="h-8 w-8 rounded-full object-cover" :src="student.avatarUrl" :alt="student.name" />
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">{{ student.name }}</div>
                        <div class="text-sm text-gray-500">{{ student.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ student.studentId }}</div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ student.className }}</div>
                    <div class="text-sm text-gray-500">{{ student.major }}</div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" 
                      :class="{
                        'bg-green-100 text-green-800': student.status === 'active',
                        'bg-yellow-100 text-yellow-800': student.status === 'less-active',
                        'bg-gray-100 text-gray-800': student.status === 'inactive'
                      }">
                      {{ getStatusLabel(student.status) }}
                    </span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div v-if="student.averageGrade" class="text-sm text-gray-900 flex items-center">
                      <span class="mr-1">{{ student.averageGrade }}</span>
                      <span class="material-icons text-yellow-400 text-xs" v-if="student.averageGrade >= 90">star</span>
                    </div>
                    <div v-else class="text-sm text-gray-400">暂无评分</div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium" @click.stop>
                    <button class="text-blue-600 hover:text-blue-900 mr-2" @click="viewStudentDetails(student)">详情</button>
                    <button class="text-red-600 hover:text-red-900" @click="showRemoveStudentModal(student)">迁出</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页控制 -->
          <div class="mt-5 flex justify-between items-center">
            <div class="text-sm text-gray-700">
              共 <span class="font-medium">{{ students.length }}</span> 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button 
                class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                :disabled="currentPage === 1"
                @click="currentPage--"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
              >
                上一页
              </button>
              
              <div v-for="pageNumber in displayedPageNumbers" :key="pageNumber">
                <button 
                  v-if="pageNumber !== '...'"
                  @click="currentPage = pageNumber"
                  class="px-3 py-1 rounded-md text-sm font-medium"
                  :class="pageNumber === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'"
                >
                  {{ pageNumber }}
                </button>
                <span v-else class="text-gray-500 px-2">...</span>
              </div>
              
              <button 
                class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                :disabled="currentPage === totalPages"
                @click="currentPage++"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
              >
                下一页
              </button>
              
              <div class="flex items-center ml-2">
                <span class="text-sm text-gray-700 mr-2">前往</span>
                <input 
                  type="number" 
                  v-model.number="goToPage" 
                  min="1" 
                  :max="totalPages"
                  class="w-12 border border-gray-300 rounded-md px-2 py-1 text-sm"
                />
                <span class="text-sm text-gray-700 mx-2">页</span>
                <button 
                  @click="jumpToPage"
                  class="border border-gray-300 rounded-md px-3 py-1 bg-white text-sm font-medium text-blue-700 hover:bg-blue-50"
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加学生模态框 -->
    <div v-if="showAddStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-xl" @click.stop>
        <!-- 模态框头部 -->
        <div class="bg-blue-50 p-6 border-b border-blue-100 flex justify-between items-center">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="material-icons text-blue-600">person_add</span>
            </div>
            <h2 class="text-xl font-bold text-gray-800">添加学生</h2>
          </div>
          <button @click="showAddStudentModal = false" class="text-gray-500 hover:text-gray-700 transition-colors">
            <span class="material-icons text-xl">close</span>
          </button>
        </div>
        
        <div class="p-6">
          <!-- 搜索区域 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">搜索学生</label>
            <div class="flex gap-2">
              <div class="flex-1 relative">
                  <input 
                    type="text" 
                  v-model="studentSearchQuery"
                  @input="handleStudentSearch"
                  placeholder="输入用户名或手机号搜索" 
                    class="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
                </div>
                </div>
              </div>
              
          <!-- 搜索结果列表 -->
          <div v-if="searchResults.length > 0" class="mb-6">
            <div class="text-sm font-medium text-gray-700 mb-2">搜索结果</div>
            <div class="border rounded-md divide-y">
              <div v-for="user in searchResults" :key="user.id" class="p-4 flex items-center justify-between hover:bg-gray-50">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                    <img v-if="user.avatar" :src="user.avatar" :alt="user.username" class="w-full h-full object-cover" />
                    <span v-else class="material-icons text-gray-400">person</span>
                </div>
              <div>
                    <div class="font-medium">{{ user.username }}</div>
                    <div class="text-sm text-gray-500">{{ user.student_id }}</div>
                </div>
              </div>
                <button 
                  class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                  @click="selectStudent(user)"
                  :disabled="selectedSearchStudents.includes(user.id)"
                >
                  {{ selectedSearchStudents.includes(user.id) ? '已选择' : '选择' }}
                </button>
                </div>
                </div>
              </div>
              
          <!-- 已选择的学生列表 -->
          <div v-if="selectedSearchStudents.length > 0" class="mb-6">
            <div class="text-sm font-medium text-gray-700 mb-2">已选择的学生</div>
            <div class="border rounded-md divide-y">
              <div v-for="user in selectedSearchStudents" :key="user.id" class="p-4 flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                    <img v-if="user.avatar" :src="user.avatar" :alt="user.username" class="w-full h-full object-cover" />
                    <span v-else class="material-icons text-gray-400">person</span>
                </div>
              <div>
                    <div class="font-medium">{{ user.username }}</div>
                    <div class="text-sm text-gray-500">{{ user.student_id }}</div>
                </div>
              </div>
                    <button 
                  class="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                  @click="removeSelectedStudent(user)"
                    >
                  移除
                    </button>
                  </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="mt-8 flex justify-end gap-3 pt-5 border-t border-gray-200">
            <button 
              type="button" 
              @click="showAddStudentModal = false"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button 
              @click="addSelectedStudents"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              :disabled="selectedSearchStudents.length === 0"
            >
              添加学生
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 新建班级模态框 -->
    <div v-if="showAddClassModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-md w-full shadow-xl" @click.stop>
        <div class="bg-blue-50 p-4 border-b border-blue-100 flex justify-between items-center">
          <div class="flex items-center gap-2">
            <span class="material-icons text-blue-600" style="font-size: 20px;">group_add</span>
            <h2 class="text-lg font-bold text-gray-800">新建班级</h2>
          </div>
          <button @click="showAddClassModal = false" class="text-gray-500 hover:text-gray-700">
            <span class="material-icons">close</span>
          </button>
        </div>
        <form @submit.prevent="addClass" class="p-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">班级名称 <span class="text-red-500">*</span></label>
              <input type="text" v-model="newClass.name" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入班级名称" required />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">班级简介</label>
              <textarea v-model="newClass.desc" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="可选：填写班级简介"></textarea>
            </div>
          </div>
          <div class="mt-6 flex justify-end gap-2">
            <button type="button" @click="showAddClassModal = false" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">确定</button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- 迁出学生确认模态框 -->
    <div v-if="showRemoveModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-sm w-full shadow-xl" @click.stop>
        <div class="bg-red-50 p-4 border-b border-red-100 flex items-center gap-2">
          <span class="material-icons text-red-600">warning</span>
          <h2 class="text-lg font-bold text-gray-800">确认迁出学生</h2>
        </div>
        <div class="p-6 text-gray-700">
          确认要将学生 <span class="font-bold text-red-600">{{ studentToRemove?.name }}</span>（学号：{{ studentToRemove?.studentId }}）从班级迁出吗？
        </div>
        <div class="flex justify-end gap-2 px-6 pb-6">
          <button @click="closeRemoveModal" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
          <button @click="removeStudent" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">确认迁出</button>
        </div>
      </div>
    </div>
    
    <!-- 班级设置模态框 -->
    <div v-if="showClassSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-2xl w-full shadow-xl" style="height:600px;display:flex;flex-direction:column;" @click.stop>
        <div class="flex justify-between items-center border-b p-4 bg-gray-50">
          <div class="flex items-center gap-2">
            <span class="material-icons text-blue-600">settings</span>
            <span class="text-lg font-bold text-gray-800">班级设置</span>
          </div>
          <button @click="closeClassSettingsModal" class="text-gray-400 hover:text-gray-700">
            <span class="material-icons">close</span>
          </button>
        </div>
        <div class="p-8 space-y-8 flex-1 overflow-y-auto">
          <!-- 通用设置 -->
          <div>
            <h3 class="text-base font-semibold mb-4">通用设置</h3>
            <div class="space-y-6">
              <div class="flex items-center justify-start gap-4">
                <label class="font-medium text-gray-700">允许学生加入</label>
                <el-switch v-model="classSettings.allowJoin" active-color="#409EFF" inactive-color="#dcdfe6" />
              </div>
              <transition name="collapse-fade-smooth">
                <div v-if="classSettings.allowJoin" class="bg-gray-50 rounded px-6 py-4 flex items-center gap-4 justify-start">
                  <div class="flex items-center gap-2">
                    <label class="text-gray-700">加入班级需要教师验证</label>
                    <el-tooltip content="开启后，学生通过邀请码/二维码加班，需要教师同意后才可进入" placement="top">
                      <span class="material-icons text-gray-400 text-base cursor-pointer">info</span>
                    </el-tooltip>
                  </div>
                  <el-switch v-model="classSettings.needTeacherVerify" active-color="#409EFF" inactive-color="#dcdfe6" />
                </div>
              </transition>
              <div class="flex items-center gap-4 justify-start">
                <label class="font-medium text-gray-700">允许学生退课</label>
                <el-switch v-model="classSettings.allowQuit" active-color="#409EFF" inactive-color="#dcdfe6" />
              </div>
              <div class="flex items-center gap-4 justify-start">
                <label class="font-medium text-gray-700">开启结课模式</label>
                <el-switch v-model="classSettings.finishMode" active-color="#409EFF" inactive-color="#dcdfe6" />
              </div>
              <p class="text-xs text-gray-500 mt-2">学生进入结课模式，学习行为不会产生统计数据的增加，班级将显示在列表最后</p>
            </div>
          </div>
          <!-- 高级设置（可折叠，简化） -->
          <div>
            <div class="flex items-center gap-4 justify-start">
              <h3 class="text-base font-semibold mb-4">高级设置</h3>
              <span @click="toggleAdvancedSettings" class="material-icons text-base cursor-pointer mb-2">{{ showAdvancedSettings ? 'expand_less' : 'expand_more' }}</span>
            </div>
            <transition name="collapse-fade-smooth">
              <div v-if="showAdvancedSettings" class="mt-4 space-y-6 border-t pt-4">
                <!-- 开放班级报名 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4">开放班级报名</label>
                  <el-switch v-model="classSettings.openApply" active-color="#409EFF" inactive-color="#dcdfe6" />
                  <span class="ml-3 text-xs text-gray-500">开启后，课程内容将公开。若要保护课程版权，可开启知识共享保护协议。</span>
                </div>
                <!-- 班级开课时间 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4">班级开课时间</label>
                  <el-date-picker
                    v-model="classSettings.startDate"
                    type="date"
                    placeholder="请选择开始时间"
                    :disabled="!classSettings.openApply"
                    class="mr-2"
                    size="small"
                    style="width: 150px;"
                  />
                  <span class="mx-2 text-gray-400">—</span>
                  <el-date-picker
                    v-model="classSettings.endDate"
                    type="date"
                    placeholder="请选择结束时间"
                    :disabled="!classSettings.openApply"
                    class="mr-2"
                    size="small"
                    style="width: 150px;"
                  />
                </div>
                <span class="text-xs text-gray-400">非开放时间段内班级将进入结课模式，学生无法完成任务点、作业、章节测验等。若延长开课结束时间，将自动关闭结课模式</span>
                <!-- 班级所属学期 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4" style="width: 110px;">班级所属学期</label>
                  <el-select v-model="classSettings.term" placeholder="请选择学期" style="width: 180px !important;">
                    <el-option label="2024秋季学期" value="2024秋季学期" />
                    <el-option label="2025春季学期" value="2025春季学期" />
                    <el-option label="2025秋季学期" value="2025秋季学期" />
                  </el-select>
                </div>
                <!-- 设置班级上限人数 -->
                <div class="flex items-center mb-2">
                  <label class="font-medium text-gray-700 mr-4">设置班级上限人数</label>
                  <el-input v-model.number="classSettings.maxStudents" style="width: 120px;" size="small" />
                </div>
                <!-- 3个开关项 -->
                <div class="flex flex-wrap gap-8 mt-4">
                  <div class="flex items-center gap-2">
                    <span class="text-gray-700 text-sm">忽略视频拖拽及窗口切换</span>
                    <el-switch v-model="classSettings.ignoreVideoDrag" size="small" />
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-gray-700 text-sm">显示第三方答疑</span>
                    <el-switch v-model="classSettings.showThirdPartyQA" size="small" />
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-gray-700 text-sm">对学生隐藏班级</span>
                    <el-switch v-model="classSettings.hideClassForStudents" size="small" />
                  </div>
                </div>
              </div>
            </transition>
          </div>
          <div style="background-color: #f5f7fa;padding: 10px;border-radius: 5px;">
            <h3 class="text-base font-semibold mb-1">生效班级</h3>
            <span class="text-xs text-gray-400">开放班级报名不支持保存至其他班级</span>
            <div class="mt-2 mb-2 flex items-center gap-2">
              <input type="checkbox" id="select-all-classes" :checked="allClassesChecked" @change="toggleAllClasses" class="form-checkbox text-blue-600" />
              <label for="select-all-classes" class="text-gray-700">全部班级</label>
            </div>
            <div class="grid grid-cols-3 gap-x-6 gap-y-2">
              <div v-for="cls in classList" :key="cls.id" class="flex items-center gap-2">
                <input type="checkbox" :id="'effective-class-' + cls.id" :value="cls.id" v-model="selectedEffectiveClasses" class="form-checkbox text-blue-600" />
                <label :for="'effective-class-' + cls.id" class="text-gray-700">{{ cls.name }}</label>
              </div>
            </div>
          </div>
          <div class="flex justify-end gap-2 pt-4 border-t">
            <button @click="closeClassSettingsModal" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
            <button @click="saveClassSettings" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">保存</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 班级管理模态框 -->
    <div v-if="showManageClassModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10 p-4">
      <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] shadow-xl flex flex-col" @click.stop>
        <!-- 模态框头部 -->
        <div class="bg-blue-50 p-6 border-b border-blue-100 flex justify-between items-center sticky top-0 z-10">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="material-icons text-blue-600">settings</span>
            </div>
            <h2 class="text-xl font-bold text-gray-800">班级管理</h2>
          </div>
          <button @click="showManageClassModal = false" class="text-gray-500 hover:text-gray-700 transition-colors">
            <span class="material-icons text-xl">close</span>
          </button>
        </div>
        
        <!-- 模态框内容 -->
        <div class="p-6 flex-1 overflow-y-auto">
          <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-3">
              <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2" @click="onCreateClass">
                <span class="material-icons text-sm">add</span> 新建班级
              </button>
              <div class="relative">
                <input
                  type="text"
                  v-model="manageClassSearchQuery"
                  placeholder="搜索班级"
                  class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
              </div>
            </div>
            <span class="text-gray-600">共 {{ filteredManageClassList.length }} 个</span>
          </div>

          <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="w-12 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      class="rounded text-blue-600 focus:ring-blue-500"
                      :checked="allManageClassesSelected"
                      @change="toggleSelectAllManageClasses"
                    />
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    班级名称
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学生人数
                  </th>
                   <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建人
                  </th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th scope="col" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="cls in filteredManageClassList" :key="cls.id" class="hover:bg-gray-50">
                  <td class="px-2 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      class="rounded text-blue-600 focus:ring-blue-500"
                      :checked="selectedManageClasses.includes(cls.id)"
                      @change="toggleManageClassSelection(cls.id)"
                    />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {{ cls.name }}
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ getStudentCountByClass(cls.id) }}
                  </td>
                   <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ teachers[cls.head_teacher] || '未知' }}
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(cls.created_at) }}
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900 mr-2" @click="renameClass(cls)">重命名</button>
                    <button class="text-red-600 hover:text-red-900" @click="showDeleteClassConfirm(cls)">删除</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

        </div>
        
        <!-- 模态框底部（可选） -->
        <div class="mt-auto flex justify-end gap-3 pt-5 border-t border-gray-200 p-6">
            <button 
              type="button" 
              @click="showManageClassModal = false"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              关闭
            </button>
          </div>

      </div>
    </div>

    <!-- 重命名班级模态框 -->
    <div v-if="showRenameClassModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-sm w-full shadow-xl" @click.stop>
        <div class="bg-blue-50 p-4 border-b border-blue-100 flex items-center gap-2">
          <span class="material-icons text-blue-600">edit</span>
          <h2 class="text-lg font-bold text-gray-800">重命名班级</h2>
        </div>
        <div class="p-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">新班级名称 <span class="text-red-500">*</span></label>
          <input
            type="text"
            v-model="newClassName"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请输入新班级名称"
            required
          />
        </div>
        <div class="flex justify-end gap-2 px-6 pb-6">
          <button type="button" @click="showRenameClassModal = false" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
          <button type="button" @click="saveRenameClass" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">保存</button>
        </div>
      </div>
    </div>

    <!-- 删除班级确认模态框 -->
    <div v-if="showDeleteClassModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-sm w-full shadow-xl" @click.stop>
        <div class="bg-red-50 p-4 border-b border-red-100 flex items-center gap-2">
          <span class="material-icons text-red-600">warning</span>
          <h2 class="text-lg font-bold text-gray-800">确认删除班级</h2>
        </div>
        <div class="p-6 text-gray-700">
          确认要删除班级 <span class="font-bold text-red-600">{{ classToDelete?.name }}</span> 吗？此操作不可恢复。
        </div>
        <div class="flex justify-end gap-2 px-6 pb-6">
          <button @click="closeDeleteClassModal" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">取消</button>
          <button @click="confirmDeleteClass" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">确认删除</button>
        </div>
      </div>
    </div>

    <!-- 邀请码模态框 -->
    <div v-if="showInvitationCodeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg max-w-md w-full shadow-xl" @click.stop>
        <div class="bg-green-50 p-4 border-b border-green-100 flex justify-between items-center">
          <div class="flex items-center gap-2">
            <span class="material-icons text-green-600">qr_code</span>
            <h2 class="text-lg font-bold text-gray-800">班级邀请码</h2>
          </div>
          <button @click="showInvitationCodeModal = false" class="text-gray-500 hover:text-gray-700">
            <span class="material-icons">close</span>
          </button>
        </div>
        <div class="p-6">
          <div class="text-center mb-4">
            <div class="text-sm text-gray-600 mb-2">扫描二维码或使用邀请码加入班级</div>
            <div class="text-2xl font-bold text-gray-800 mb-4">{{ invitationCode }}</div>
            <div class="flex justify-center mb-4">
              <canvas ref="qrCodeCanvas"></canvas>
            </div>
            <div class="flex justify-center gap-2">
              <button 
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-1"
                @click="copyInvitationCode"
              >
                <span class="material-icons text-sm">content_copy</span>
                复制邀请码
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { organizationApi } from '@/api/organization'
import { usersApi } from '@/api/users'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash'
import QRCode from 'qrcode'
import { useRoute } from 'vue-router'
import defaultUserAvatar from '@/assets/images/avatars/user_avatar.jpg'

const route = useRoute()

// 学生状态选项卡
const tabs = [
  { label: '全部学生', value: 'all' },
  { label: '活跃', value: 'active' },
  { label: '较少活动', value: 'less-active' },
  { label: '未激活', value: 'inactive' }
]
const currentTab = ref('all')

// 搜索和筛选
const classSearchQuery = ref('')
const studentSearchQuery = ref('')
const showFilters = ref(false)
const selectedFilters = ref({
  classes: [],
  statuses: []
})

// 模态框状态
const showAddStudentModal = ref(false)
const showAddClassModal = ref(false)
const showManageClassModal = ref(false)

// 切换筛选器
const toggleFilter = (category, value) => {
  if (selectedFilters.value[category].includes(value)) {
    selectedFilters.value[category] = selectedFilters.value[category].filter(item => item !== value)
  } else {
    selectedFilters.value[category].push(value)
  }
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    'active': '活跃',
    'less-active': '较少活动',
    'inactive': '未激活'
  }
  return statusMap[status] || status
}

// 模拟学生数据改为实际数据
const students = ref([])

// 班级学生数量统计
const classStudentCounts = ref({})

// 更新班级学生数量
const updateClassStudentCount = async (classId) => {
  try {
    const response = await usersApi.getClassStudents(classId)
    classStudentCounts.value[classId] = response.length
  } catch (error) {
    console.error('获取班级学生数量失败:', error)
    classStudentCounts.value[classId] = 0
  }
}

// 获取班级学生数量
const getStudentCountByClass = (classId) => {
  return classStudentCounts.value[classId] || 0
}

// 加载班级学生列表
const loadClassStudents = async () => {
  if (!selectedClass.value) return
  
  try {
    const response = await usersApi.getClassStudents(selectedClass.value.id)
    students.value = response.map(student => ({
      id: student.id,
      name: student.user.username,
      email: student.user.email,
      avatarUrl: student.user.avatar || defaultUserAvatar,
      studentId: student.student_id,
      className: selectedClass.value.name,
      major: student.major?.name || '未知',
      status: student.status || 'inactive',
      averageGrade: student.average_grade || null
    }))
    
    // 更新当前班级的学生数量
    classStudentCounts.value[selectedClass.value.id] = response.length
  } catch (error) {
    console.error('获取班级学生列表失败:', error)
    ElMessage.error('获取班级学生列表失败')
  }
}

// 表格选择和分页
const selectedStudents = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const goToPage = ref(1)

// 切换学生选择
const toggleStudentSelection = (studentId) => {
  if (selectedStudents.value.includes(studentId)) {
    selectedStudents.value = selectedStudents.value.filter(id => id !== studentId)
  } else {
    selectedStudents.value.push(studentId)
  }
}

// 全选/取消全选
const toggleSelectAll = (e) => {
  if (e.target.checked) {
    selectedStudents.value = filteredStudents.value.map(student => student.id)
  } else {
    selectedStudents.value = []
  }
}

// 班级搜索过滤
const filteredClassList = computed(() => {
  if (!classSearchQuery.value) return classList.value
  const query = classSearchQuery.value.toLowerCase()
  return classList.value.filter(cls => cls.name.toLowerCase().includes(query))
})

// 根据过滤条件筛选学生
const filteredStudents = computed(() => {
  let result = students.value

  // 按标签筛选
  if (currentTab.value !== 'all') {
    result = result.filter(student => student.status === currentTab.value)
  }

  // 按选中的筛选器筛选
  if (selectedFilters.value.statuses.length > 0) {
    result = result.filter(student => {
      const statusLabel = getStatusLabel(student.status)
      return selectedFilters.value.statuses.includes(statusLabel)
    })
  }

  // 按搜索关键词筛选
  if (studentSearchQuery.value) {
    const query = studentSearchQuery.value.toLowerCase()
    result = result.filter(student => 
      student.name.toLowerCase().includes(query) ||
      student.studentId.toLowerCase().includes(query) ||
      student.email.toLowerCase().includes(query)
    )
  }

  return result
})

// 分页后的学生列表
const paginatedStudents = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredStudents.value.slice(startIndex, startIndex + pageSize.value)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredStudents.value.length / pageSize.value) || 1
})

// 显示页码
const displayedPageNumbers = computed(() => {
  const result = []
  const maxDisplayedPages = 5

  if (totalPages.value <= maxDisplayedPages) {
    for (let i = 1; i <= totalPages.value; i++) {
      result.push(i)
    }
  } else {
    // 总是显示第一页
    result.push(1)
    
    // 添加当前页附近的页码
    let startPage = Math.max(2, currentPage.value - 1)
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1)
    
    // 处理省略号
    if (startPage > 2) {
      result.push('...')
    }
    
    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      result.push(i)
    }
    
    // 处理省略号
    if (endPage < totalPages.value - 1) {
      result.push('...')
    }
    
    // 总是显示最后一页
    result.push(totalPages.value)
  }

  return result
})

// 跳转到指定页面
const jumpToPage = () => {
  if (goToPage.value >= 1 && goToPage.value <= totalPages.value) {
    currentPage.value = goToPage.value
  } else {
    goToPage.value = currentPage.value
  }
}

// 选中的学生详情
const selectedStudent = ref(null)

// 查看学生详情
const viewStudentDetails = (student) => {
  selectedStudent.value = { ...student }
}

// 显示学生迁出模态框
const showRemoveStudentModal = (student) => {
  studentToRemove.value = student
  showRemoveModal.value = true
}

// 班级列表数据
const classList = ref([])
const selectedClass = ref(null)

// 获取班级列表
const fetchClassList = async () => {
  try {
    const courseId = route.params.courseId // 从路由参数获取课程ID
    const response = await organizationApi.getClassGroups(courseId)
    classList.value = response.results
    
    // 获取所有班级的教师信息
    const teacherIds = [...new Set(response.results.map(cls => cls.head_teacher).filter(Boolean))]
    await Promise.all(teacherIds.map(id => fetchTeacherInfo(id)))
    
    // 获取所有班级的学生数量
    await Promise.all(response.results.map(cls => updateClassStudentCount(cls.id)))
    
    if (classList.value.length > 0) {
      selectedClass.value = classList.value[0]
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
  }
}

// 新建班级
const newClass = ref({
  name: '',
  desc: ''
})

// 重置新班级表单
const resetNewClassForm = () => {
  newClass.value = {
    name: '',
    desc: ''
  }
}

// 打开新建班级模态框
const onCreateClass = () => {
  resetNewClassForm()
  showAddClassModal.value = true
}

// 打开班级管理模态框
const onManageClass = async () => {
  // 重置管理相关的状态
  manageClassSearchQuery.value = ''
  selectedManageClasses.value = []
  
  // 重新获取班级列表
  await fetchClassList()
  
  showManageClassModal.value = true
}

// 新建班级
const addClass = async () => {
  try {
    const courseId = route.params.courseId // 从路由参数获取课程ID
    const response = await organizationApi.createClassGroup({
      name: newClass.value.name,
      description: newClass.value.desc,
      course_id: courseId
    })
    
    // 在班级列表的开头添加新班级，并选中它
    classList.value.unshift(response)
    selectedClass.value = response
    currentPage.value = 1
  
    // 关闭模态框并重置表单
    showAddClassModal.value = false
    resetNewClassForm()
    
    ElMessage.success('创建班级成功')
  } catch (error) {
    console.error('创建班级失败:', error)
    ElMessage.error('创建班级失败')
  }
}

// 重命名班级
const saveRenameClass = async () => {
  if (!classToRename.value || !newClassName.value.trim()) {
    ElMessage.warning('班级名称不能为空')
    return
  }

  try {
    await organizationApi.updateClassGroup(classToRename.value.id, {
      name: newClassName.value.trim()
    })
    
    // 更新本地数据
    const index = classList.value.findIndex(c => c.id === classToRename.value.id)
    if (index !== -1) {
      classList.value[index].name = newClassName.value.trim()
    }

    // 重置状态并关闭模态框
    showRenameClassModal.value = false
    classToRename.value = null
    newClassName.value = ''
    
    ElMessage.success('重命名成功')
  } catch (error) {
    console.error('重命名失败:', error)
    ElMessage.error('重命名失败')
  }
}

// 删除班级
const deleteClass = async (classId) => {
  try {
    const courseId = route.params.courseId // 从路由参数获取课程ID
    await organizationApi.deleteClassGroup(classId, { course_id: courseId })
    
    // 从列表中移除被删除的班级
    classList.value = classList.value.filter(c => c.id !== classId)
    
    // 如果删除的是当前选中的班级，则选中第一个班级
    if (selectedClass.value && selectedClass.value.id === classId) {
      selectedClass.value = classList.value[0] || null
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 在组件挂载时获取班级列表和学生列表
onMounted(async () => {
  await fetchClassList()
  if (selectedClass.value) {
    await loadClassStudents()
  }
})

// 脚本部分
const showRemoveModal = ref(false)
const studentToRemove = ref(null)

const closeRemoveModal = () => {
  showRemoveModal.value = false
  studentToRemove.value = null
}

const removeStudent = async () => {
  if (!studentToRemove.value || !selectedClass.value) return

  try {
    await usersApi.removeStudentsFromClass(
      selectedClass.value.id,
      [studentToRemove.value.id]
    )

    // 重新加载学生列表
    await loadClassStudents()
    
    // 更新班级学生数量
    await updateClassStudentCount(selectedClass.value.id)
    
    ElMessage.success('学生迁出成功')
    closeRemoveModal()
  } catch (error) {
    console.error('迁出学生失败:', error)
    ElMessage.error('迁出学生失败')
  }
}

const onClassSettings = () => {
  showClassSettingsModal.value = true
}

// 脚本部分
const showClassSettingsModal = ref(false)
const showAdvancedSettings = ref(false)
const classSettings = ref({
  allowJoin: true,
  needTeacherVerify: false,
  allowQuit: true,
  finishMode: false,
  openApply: false,
  maxStudents: 200,
  startDate: new Date().toISOString().slice(0, 10),
  endDate: new Date().toISOString().slice(0, 10),
  term: '2024秋季学期',
  ignoreVideoDrag: false,
  showThirdPartyQA: false,
  hideClassForStudents: false
})

const closeClassSettingsModal = () => {
  showClassSettingsModal.value = false
}
const toggleAdvancedSettings = () => {
  showAdvancedSettings.value = !showAdvancedSettings.value
}
const saveClassSettings = () => {
  // TODO: 可对接API保存
  console.log('保存班级设置', classSettings.value)
  showClassSettingsModal.value = false
}

// 在<script setup>中添加:
// const selectedEffectiveClasses = ref(classList.value.map(cls => cls.id)); // 默认全选或根据业务调整
const selectedEffectiveClasses = ref([]);

const allClassesChecked = computed(() => selectedEffectiveClasses.value.length === classList.value.length && classList.value.length > 0)
const toggleAllClasses = () => {
  if (allClassesChecked.value) {
    selectedEffectiveClasses.value = []
  } else {
    selectedEffectiveClasses.value = classList.value.map(cls => cls.id)
  }
}

const manageClassSearchQuery = ref('');
const selectedManageClasses = ref([]);

const filteredManageClassList = computed(() => {
  if (!manageClassSearchQuery.value) return classList.value;
  const query = manageClassSearchQuery.value.toLowerCase();
  return classList.value.filter(cls => cls.name.toLowerCase().includes(query));
});

const allManageClassesSelected = computed(() => {
  return filteredManageClassList.value.length > 0 && selectedManageClasses.value.length === filteredManageClassList.value.length;
});

const toggleManageClassSelection = (classId) => {
  if (selectedManageClasses.value.includes(classId)) {
    selectedManageClasses.value = selectedManageClasses.value.filter(id => id !== classId);
  } else {
    selectedManageClasses.value.push(classId);
  }
};

const toggleSelectAllManageClasses = () => {
  if (allManageClassesSelected.value) {
    selectedManageClasses.value = [];
  } else {
    selectedManageClasses.value = filteredManageClassList.value.map(cls => cls.id);
  }
};

const renameClass = (cls) => {
  console.log('重命名班级:', cls);
  // TODO: Implement rename class logic (e.g., show a rename modal)
  classToRename.value = cls;
  newClassName.value = cls.name; // Initialize with current name
  showRenameClassModal.value = true;
};

// New state for rename class modal
const showRenameClassModal = ref(false);
const classToRename = ref(null);
const newClassName = ref('');

// 选择班级
const selectClass = async (cls) => {
  // 如果点击的是当前选中的班级，不做任何操作
  if (selectedClass.value && selectedClass.value.id === cls.id) {
    return
  }
  
  // 更新选中的班级
  selectedClass.value = cls
  
  // 加载班级学生列表
  await loadClassStudents()
  
  // 重置分页到第一页
  currentPage.value = 1
  
  // 重置筛选条件
  currentTab.value = 'all'
  selectedFilters.value = {
    classes: [],
    statuses: []
  }
  
  // 重置搜索
  classSearchQuery.value = ''
  
  // 重置学生选择
  selectedStudents.value = []
}

// 删除班级相关状态
const showDeleteClassModal = ref(false)
const classToDelete = ref(null)

// 打开删除班级确认框
const showDeleteClassConfirm = (cls) => {
  classToDelete.value = cls
  showDeleteClassModal.value = true
}

// 关闭删除班级确认框
const closeDeleteClassModal = () => {
  showDeleteClassModal.value = false
  classToDelete.value = null
}

// 确认删除班级
const confirmDeleteClass = async () => {
  if (!classToDelete.value) return
  await deleteClass(classToDelete.value.id)
  closeDeleteClassModal()
}

// 在 script setup 部分添加日期格式化函数
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 在 script setup 部分添加教师信息相关的变量和函数
const teachers = ref({}) // 存储教师信息的映射

// 获取教师信息
const fetchTeacherInfo = async (teacherId) => {
  if (!teacherId) return '未知'
  if (teachers.value[teacherId]) return teachers.value[teacherId]
  
  try {
    const response = await usersApi.getTeacherInfo(teacherId)
    teachers.value[teacherId] = response.user.alias
    return response.user.alias
  } catch (error) {
    console.error('获取教师信息失败:', error)
    return '未知'
  }
}

// 学生搜索相关
const searchResults = ref([])
const selectedSearchStudents = ref([])
const selectedStudentIds = computed(() => selectedSearchStudents.value.map(s => s.id))

// 使用lodash的debounce函数来延迟搜索请求
const handleStudentSearch = debounce(async () => {
  if (!studentSearchQuery.value) {
    searchResults.value = []
    return
  }

  try {
    const response = await usersApi.searchUserByQuery(studentSearchQuery.value)
    searchResults.value = response || []
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  }
}, 300)

// 选择学生
const selectStudent = (user) => {
  if (!selectedStudentIds.value.includes(user.id)) {
    selectedSearchStudents.value.push(user)
  }
}

// 移除已选择的学生
const removeSelectedStudent = (user) => {
  selectedSearchStudents.value = selectedSearchStudents.value.filter(s => s.id !== user.id)
}

// 添加选中的学生到班级后重新加载学生列表
const addSelectedStudents = async () => {
  if (!selectedClass.value) {
    ElMessage.warning('请先选择班级')
    return
  }

  try {
    await usersApi.addUsersToClass(
      selectedClass.value.id,
      selectedSearchStudents.value.map(s => s.id)
    )
    
    // 重新加载学生列表
    await loadClassStudents()
    
    // 更新当前班级的学生数量
    await updateClassStudentCount(selectedClass.value.id)
    
    // 清空选择并关闭模态框
    selectedSearchStudents.value = []
    searchResults.value = []
    studentSearchQuery.value = ''
    showAddStudentModal.value = false
    
    ElMessage.success('添加学生成功')
  } catch (error) {
    console.error('添加学生失败:', error)
    ElMessage.error('添加学生失败')
  }
}

// 邀请码相关
const showInvitationCodeModal = ref(false)
const invitationCode = ref('')
const qrCodeCanvas = ref(null)

// 获取班级邀请码
const getInvitationCode = async () => {
  if (!selectedClass.value) return
  
  try {
    const response = await organizationApi.getClassInvitationCode(selectedClass.value.id)
    invitationCode.value = response.code
      
    // 生成二维码
    if (qrCodeCanvas.value) {
      await QRCode.toCanvas(qrCodeCanvas.value, JSON.stringify(response), {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#ffffff'
        }
      })
    }
  } catch (error) {
    console.error('获取邀请码失败:', error)
    ElMessage.error('获取邀请码失败')
  }
}

// 复制邀请码
const copyInvitationCode = async () => {
  try {
    await navigator.clipboard.writeText(invitationCode.value)
    ElMessage.success('邀请码已复制到剪贴板')
  } catch (error) {
    console.error('复制邀请码失败:', error)
    ElMessage.error('复制邀请码失败')
  }
}

// 监听邀请码模态框的显示
watch(showInvitationCodeModal, async (newVal) => {
  if (newVal) {
    await getInvitationCode()
  }
})

</script> 

<style scoped>
.collapse-fade-smooth-enter-active,
.collapse-fade-smooth-leave-active {
  transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1), padding 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.collapse-fade-smooth-enter-from,
.collapse-fade-smooth-leave-to {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.collapse-fade-smooth-enter-to,
.collapse-fade-smooth-leave-from {
  max-height: 500px;
  opacity: 1;
  padding-top: 1rem;
  padding-bottom: 1rem;
}
</style>