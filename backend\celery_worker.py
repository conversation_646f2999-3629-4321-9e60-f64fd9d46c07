#!/usr/bin/env python
"""
Celery Worker启动脚本
启动命令: python celery_worker.py
"""

import os
import sys
from pathlib import Path

def setup_django():
    """
    设置Django环境
    """
    print("正在设置Django环境...")
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

    try:
        import django
        django.setup()
        print("Django环境设置成功")
    except Exception as e:
        print(f"设置Django环境时出错: {e}")
        print("如果不使用Django模型，可以修改任务代码以避免导入Django模型")
        sys.exit(1)

def start_celery_worker():
    """
    启动Celery Worker进程
    """
    print("正在启动Celery Worker...")

    # 设置Django环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

    # 直接导入项目的Celery应用
    from zhkt.config import celery_app

    print("成功导入Celery应用")

    # 直接使用celery_app的worker_main方法启动
    print("Celery Worker 启动成功！")
    print("Worker 配置:")
    print(f"  - 日志级别: info")
    print(f"  - 线程池: threads")
    print(f"  - 并发数: 3")
    print("按 Ctrl+C 停止Worker")

    # 启动Worker
    celery_app.worker_main([
        'worker',
        '--loglevel=info',
        '--pool=threads',
        '--concurrency=3',
        '--hostname=ai-education-worker@%h'
    ])
        

if __name__ == "__main__":
    # 获取当前脚本所在的目录
    current_dir = Path(__file__).parent.absolute()
    os.chdir(current_dir)
    # 设置Django环境
    setup_django()

    # 启动Celery Worker
    start_celery_worker()