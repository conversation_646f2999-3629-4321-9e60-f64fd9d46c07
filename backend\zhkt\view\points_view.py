from rest_framework import permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.db import transaction
from django.utils import timezone
from zhkt.utils.file_utils import FileUtils
from django.conf import settings
from .base_model_view import BaseModelViewSet
from zhkt.serializers import (
    PointsRecordSerializer,
    ProductSerializer,
    OrderSerializer,
)
from zhkt.entitys import (
    PointsRecord, 
    Product,
    Order,
)
from django.db.models import Q
import os
import uuid
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class IsAdminOrReadOnly(permissions.BasePermission):
    """
    自定义权限类：
    - 管理员可以执行所有操作
    - 其他用户只能执行只读操作
    """
    def has_permission(self, request, view):
        # 允许所有用户进行只读操作
        if request.method in permissions.SAFE_METHODS:
            return True
        # 只允许管理员进行修改操作
        return request.user.is_staff

class ProductPagination(PageNumberPagination):
    page_size = 5
    page_size_query_param = 'page_size'
    max_page_size = 10000

# 积分相关视图集
class PointsRecordViewSet(BaseModelViewSet):
    queryset = PointsRecord.objects.all()
    serializer_class = PointsRecordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的积分记录"""
        return self.queryset.filter(student=self.request.user.student_profile)

class ProductViewSet(BaseModelViewSet):
    queryset = Product.objects.filter(deleted_at__isnull=True)
    serializer_class = ProductSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminOrReadOnly]
    pagination_class = ProductPagination

    def get_queryset(self):
        """获取商品列表，非管理员只能看到已上架商品"""
        queryset = self.queryset
        
        # 非管理员只能看到已上架商品
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_active=True)
            
        # 搜索
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(description__icontains=search)
            )
            
        # 分类筛选
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
            
        # 排序
        sort = self.request.query_params.get('sort')
        if sort and sort == 'points':
            queryset = queryset.order_by('-points_price')  
        else:
            queryset = queryset.order_by('-created_at')
            
        return queryset

    @action(detail=False, methods=['get'])
    def hot(self, request):
        """获取热门商品"""
        # 这里可以根据订单量等数据来确定热门商品
        hot_products = self.get_queryset().filter(is_active=True).order_by('-created_at')[:4]
        serializer = self.get_serializer(hot_products, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def upload_image(self, request, pk=None):
        """上传商品图片"""
        # 检查用户认证
        if not request.user.is_authenticated:
            logger.warning(f"Unauthorized upload attempt from IP: {request.META.get('REMOTE_ADDR')}")
            return Response({
                'error': '未授权访问',
                'detail': '请先登录'
            }, status=status.HTTP_401_UNAUTHORIZED)
            
        # 检查用户权限
        if not request.user.is_staff:
            logger.warning(f"Permission denied for user {request.user.username} attempting to upload product image")
            return Response({
                'error': '权限不足',
                'detail': '只有管理员可以上传商品图片'
            }, status=status.HTTP_403_FORBIDDEN)
            
        try:
            file = request.FILES.get('file')
            
            if not file:
                return Response({
                    'error': '未找到文件',
                    'detail': '请选择要上传的图片文件'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证文件类型
            allowed_types = ['image/jpeg', 'image/png']
            if file.content_type not in allowed_types:
                return Response({
                    'error': '不支持的文件类型',
                    'detail': '只支持 JPG/PNG 格式的图片'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证文件大小（最大2MB）
            if file.size > 2 * 1024 * 1024:
                return Response({
                    'error': '文件过大',
                    'detail': '文件大小不能超过2MB'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 生成安全的文件名
            file_ext = Path(file.name).suffix
            safe_filename = f"{uuid.uuid4().hex}{file_ext}"
            
            # 保存文件到临时目录
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'product_images')
            os.makedirs(temp_dir, exist_ok=True)
            
            temp_file_path = os.path.join(temp_dir, safe_filename)
            
            try:
                # 保存上传的文件到临时目录
                with open(temp_file_path, 'wb+') as destination:
                    for chunk in file.chunks():
                        destination.write(chunk)
                
                # 使用FileUtils保存图片文件到OSS
                image_path = FileUtils.save_image_file(temp_file_path, 'product_images')
                
                # 获取商品对象并更新图片
                product = self.get_object()
                
                # 如果之前有图片，尝试删除旧图片
                if product.image:
                    try:
                        FileUtils.delete_file(product.image)
                    except Exception as e:
                        # 记录错误但不影响新图片上传
                        print(f"删除旧图片失败: {str(e)}")
                
                # 更新商品图片URL
                product.image = FileUtils.get_file_url(image_path)
                product.save(update_fields=['image'])
                
                return Response({
                    'url': product.image,
                    'path': image_path
                })
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                    except Exception as e:
                        print(f"清理临时文件失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"Error uploading product image: {str(e)}", exc_info=True)
            return Response({
                'error': '上传失败',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def perform_destroy(self, instance):
        """软删除商品"""
        instance.deleted_at = timezone.now()
        instance.save()

class OrderViewSet(BaseModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的订单"""
        return self.queryset.filter(student=self.request.user.student_profile)

    def create(self, request, *args, **kwargs):
        """创建订单（兑换商品）"""
        # 获取商品信息
        product_id = request.data.get('product_id')
        try:
            product = Product.objects.get(id=product_id, is_active=True)
        except Product.DoesNotExist:
            return Response({'error': '商品不存在'}, status=status.HTTP_404_NOT_FOUND)

        student = request.user.student

        # 检查积分是否足够
        if student.points < product.points_price:
            return Response({'error': '积分不足'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查库存是否足够
        if product.stock <= 0:
            return Response({'error': '商品库存不足'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # 创建订单
                order = Order.objects.create(
                    student=student,
                    product=product,
                    points_cost=product.points_price,
                    status='PENDING'
                )

                # 扣除积分
                student.points -= product.points_price
                student.save()

                # 减少库存
                product.stock -= 1
                product.save()

                # 创建积分记录
                PointsRecord.objects.create(
                    student=student,
                    points=-product.points_price,
                    record_type='EXCHANGE',
                    description=f'兑换商品：{product.name}'
                )

                serializer = self.get_serializer(order)
                return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)