<template>
  <StudentLayout 
    pageTitle="作业详情" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    
    <!-- 作业信息卡片 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-4">
        <div class="flex-col-md-row">
            <div>
                <h1 class="text-xl font-bold text-gray-900 mb-2">{{ assignment.title }}</h1>
            </div>
            <div class="mt-4 md:mt-0">
                <span :class="['px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full', getStatusClass(assignment.status)]">
                    {{ getStatusText(assignment.status) }}
                </span>
            </div>
        </div>
      </div>
      
      <div style="display: flex; justify-content: space-between; align-items: center; width: 75%;" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">book</span>
          <div>
            <p class="text-xs text-gray-500">所属课程</p>
            <p class="text-sm font-medium">{{ assignment.courseName }}</p>
          </div>
        </div>
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">person</span>
          <div>
            <p class="text-xs text-gray-500">授课教师</p>
            <p class="text-sm font-medium">{{ assignment.collegeName + '-' +  assignment.teacherTitle + '[' + assignment.teacherName+']' }}</p>
          </div>
        </div>
        <div class="flex items-center">
          <span class="material-icons text-blue-500 mr-2">event</span>
          <div>
            <p class="text-xs text-gray-500">截止时间</p>
            <p class="text-sm font-medium">{{ formatDate(assignment.end_time) }}</p>
          </div>
        </div>
      </div>
      
      <div class="border-t border-gray-200 pt-4">
        <h2 class="text-lg font-medium text-gray-900 mb-3">作业说明</h2>
        <div class="prose max-w-none text-gray-700 mb-4">
          <p>{{ assignment.description }}</p>
        </div>
      </div>
    </div>

    <!-- 题目区 -->
    <div v-if="assignment.questions && assignment.questions.length > 0" class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">题目列表</h2>
      <div class="space-y-6">
        <div v-for="(question, index) in assignment.questions" :key="question.id" class="border-b border-gray-200 pb-6 last:border-b-0">
          <div class="flex items-start">
            <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full mr-3">{{ index + 1 }}</span>
            <div class="flex-1">
              <h3 class="text-base font-medium text-gray-900 mb-2">{{ question.question_detail.title }}</h3>
              <div class="prose max-w-none text-gray-700 mb-4" v-html="question.question_detail.content"></div>
              
              <!-- 答题区域 -->
              <div class="mt-4">
                <!-- 填空题 -->
                <textarea
                  v-if="question.question_detail.question_type === 'fill_blank' || question.question_detail.question_type === 'short_answer'"
                  v-model="answers[question.id]"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  :placeholder="'请输入您的答案'"
                ></textarea>

                <!-- 判断题 -->
                <el-select v-else-if="question.question_detail.question_type === 'true_false'"
                  v-model="answers[question.id]" placeholder="请选择答案">
                  <el-option
                    v-for="option in trueFalseOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  ></el-option>
                </el-select>
                
                <!-- 选择题选项 -->
                <div v-else-if="question.question_detail.question_type === 'single_choice' || question.question_detail.question_type === 'multiple_choice'" 
                  class="space-y-2">
                  <label v-for="option in question.question_detail.options" :key="option.id" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
                    <input
                      :type="question.question_detail.question_type==='multiple_choice' ? 'checkbox' : 'radio'"
                      :name="'question_' + question.id"
                      :value="option.id"
                      v-model="answers[question.id]"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    >
                    <span class="text-gray-700">{{ option.content }}</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 提交区 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">作业提交</h2>
      <div class="flex items-center justify-between">
        <div class="flex items-center text-gray-600">
          <span class="material-icons mr-2 text-blue-500">info</span>
          <span>提交后将不能再修改，请确认您的答案无误</span>
        </div>
        <div class="flex space-x-3">
          <button 
            v-if="assignment.status==='pending' || assignment.status==='overdue'"
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none"
            @click="saveDraft"
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? '保存中...' : '保存草稿' }}
          </button>
          <router-link to="/student/assignments" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none">
            返回
          </router-link>
          <button 
            v-if="assignment.status==='pending' || assignment.status==='overdue'"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" 
            @click="handleSubmit" 
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? '提交中...' : '提交作业' }}
          </button>
          <button 
            v-if="assignment.status==='submitted' || assignment.status==='late'"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" 
            @click="handleSubmit" 
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? '提交中...' : '重新提交' }}
          </button>
        </div>
      </div>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { formatDate } from '@/utils/date'
import { getHomeworkDetail, submitHomework, getStudentStatus } from '@/api/homework'
import { courseApi } from '@/api/course'
import { ElMessage } from 'element-plus'

const studentStore = useStudentStore()
const route = useRoute()
const router = useRouter()
const assignmentId = route.params.id

// 作业数据
const assignment = ref({
  title: '',
  description: '',
  end_time: '',
  publish_date: '',
  status: 'pending',
  questions: [],
  courseName: '',
  collegeName: '',
  teacherTitle: '',
  teacherName: '',
  has_submitted: null
})

// 判断题答案选项
const trueFalseOptions = [
  { value: '正确', label: '正确' },
  { value: '错误', label: '错误' }
]

// 学生答案
const answers = ref({})

// 提交状态
const isSubmitting = ref(false)

// 获取作业详情
const fetchAssignment = async () => {
  try {
    const response = await getHomeworkDetail(assignmentId)
    const course = await courseApi.getCourseById(response.course)
    response.courseName = course.name
    response.collegeName = course.teacher.college_name
    response.teacherTitle = course.teacher.title
    response.teacherName = course.teacher.user.alias
    assignment.value = response
    
    // 获取提交状态和答案
    const statusResponse = await getStudentStatus(assignmentId)
    if (statusResponse.has_submitted) {
      // 如果已提交，使用提交的答案
      answers.value = statusResponse.answers
      if(statusResponse.status){
        assignment.value.status = statusResponse.status
        assignment.value.has_submitted = statusResponse.has_submitted
      }
    } else {
      // 如果未提交，检查是否有保存的草稿
      const savedAnswers = localStorage.getItem(`assignment_${assignmentId}_draft`)
      if (savedAnswers) {
        answers.value = JSON.parse(savedAnswers)
      }
    }
  } catch (error) {
    console.error('获取作业详情失败:', error)
    ElMessage.error('获取作业详情失败')
  }
}

const getStatusClass = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'submitted':
      return 'bg-blue-100 text-blue-800'
    case 'late':
      return 'bg-red-100 text-red-800'
    case 'graded':
      return 'bg-green-100 text-green-800'
    case 'overdue':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'pending':
      return '待提交'
    case 'submitted':
      return '已提交'
    case 'late':
      return '迟交'
    case 'graded':
      return '已批改'
    case 'overdue':
      return '已逾期'
    default:
      return status
  }
}

// 保存草稿
const saveDraft = () => {
  try {
    localStorage.setItem(`assignment_${assignmentId}_draft`, JSON.stringify(answers.value))
    ElMessage.success('草稿保存成功')
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  }
}

// 提交作业
const handleSubmit = async () => {
  if (isSubmitting.value) return
  
  // 检查是否所有题目都已作答
  const unansweredQuestions = assignment.value.questions.filter(q => !answers.value[q.id])
  if (unansweredQuestions.length > 0) {
    ElMessage.warning(`还有 ${unansweredQuestions.length} 道题目未作答`)
    return
  }
  
  isSubmitting.value = true
  try {
    // 构建提交数据
    const submissionData = {
      homework_id: assignmentId,
      content: JSON.stringify({
        answers: Object.entries(answers.value).map(([questionId, answer]) => ({
          question_id: parseInt(questionId),
          answer: answer
        }))
      })
    }
    
    await submitHomework(submissionData)
    
    // 清除草稿
    localStorage.removeItem(`assignment_${assignmentId}_draft`)
    
    ElMessage.success('作业提交成功')
    
    // 跳转到作业列表页
    router.push('/student/assignments')
  } catch (error) {
    console.error('提交作业失败:', error)
    ElMessage.error('提交作业失败')
  } finally {
    isSubmitting.value = false
  }
}

onMounted(() => {
  fetchAssignment()
})
</script>

<style scoped>
/* 代码格式化样式 */
pre {
  background-color: #f5f5f5;
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
}

code {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  color: #333;
}

/* 选项悬停效果 */
.option-item:hover {
  background-color: #f0f9ff;
  cursor: pointer;
}

/* 已选择的选项 */
.option-item.selected {
  background-color: #dbeafe;
  border-color: #3b82f6;
}

/* 编辑器样式 */
.code-editor {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  line-height: 1.5;
  tab-size: 4;
}

.flex-col-md-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
</style> 