<template>
  <div class="h-full flex flex-col bg-white border-r transition-all duration-300" :class="{ 'w-16': isCollapsed, 'w-64': !isCollapsed }">
    <!-- Course Info -->
    <div class="flex items-center p-4 border-b border-gray-200">
      <div class="flex-1 min-w-0" v-if="!isCollapsed">
        <h2 class="text-lg font-semibold text-gray-800 truncate">{{ courseName }}</h2>
        <p class="text-sm text-gray-500 truncate">{{ courseCode }}</p>
      </div>
      <button 
        @click="$emit('toggle-collapse')" 
        class="p-1 rounded-lg hover:bg-gray-100 focus:outline-none"
        :title="isCollapsed ? '展开菜单' : '收起菜单'"
      >
        <span class="material-icons text-gray-500">
          {{ isCollapsed ? 'chevron_right' : 'chevron_left' }}
        </span>
      </button>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 overflow-y-auto p-4">
      <ul class="space-y-2">
        <li v-for="item in menuItems" :key="item.name">
          <router-link
            :to="{ 
              name: item.name, 
              params: { courseId }, 
              query: { 
                name: courseName,
                code: courseCode,
                teacherName: teacherName,
                teacherAvatar: teacherAvatar
              }
            }"
            class="flex items-center px-3 py-2 rounded-lg transition-colors duration-150"
            :class="[
              isActive(item.name) 
                ? 'bg-blue-50 text-blue-600' 
                : 'text-gray-700 hover:bg-gray-100',
              isCollapsed ? 'justify-center' : ''
            ]"
          >
            <span class="material-icons" :class="isCollapsed ? '' : 'mr-3'">{{ item.icon }}</span>
            <span v-if="!isCollapsed">{{ item.title }}</span>
          </router-link>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  },
  courseName: {
    type: String,
    required: true
  },
  courseCode: {
    type: String,
    default: ''
  },
  courseId: {
    type: [String, Number],
    required: true
  },
  teacherName: {
    type: String,
    default: ''
  },
  teacherAvatar: {
    type: String,
    default: ''
  }
})

const route = useRoute()

// Menu items configuration
const menuItems = [
  { name: 'course-chapter', title: '章节', icon: 'menu_book' },
  { name: 'course-class', title: '班级', icon: 'groups' },
  { name: 'course-question-bank', title: '题库', icon: 'quiz' },
  { name: 'course-homework', title: '作业', icon: 'assignment' },
  { name: 'course-grades', title: '成绩', icon: 'grade' }
]

// Check if menu item is active
const isActive = (routeName) => {
  return route.name === routeName
}
</script>

<style scoped>
.router-link-active {
  background-color: rgb(239 246 255);
  color: rgb(37 99 235);
}
</style> 