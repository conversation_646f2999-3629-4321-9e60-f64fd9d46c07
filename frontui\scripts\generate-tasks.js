#!/usr/bin/env node

/**
 * generate-tasks.js
 * 任务文件生成脚本
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// 读取tasks.json文件
try {
  const tasksPath = path.join(projectRoot, 'tasks', 'tasks.json');
  const tasksDir = path.join(projectRoot, 'tasks');
  
  console.log('读取tasks.json文件...');
  const tasksData = JSON.parse(fs.readFileSync(tasksPath, 'utf8'));
  
  if (!tasksData.tasks || !Array.isArray(tasksData.tasks)) {
    throw new Error('tasks.json格式不正确，缺少tasks数组');
  }
  
  console.log(`找到${tasksData.tasks.length}个任务，开始生成任务文件...`);
  
  // 确保tasks目录存在
  if (!fs.existsSync(tasksDir)) {
    fs.mkdirSync(tasksDir, { recursive: true });
  }
  
  // 生成每个任务的文件
  tasksData.tasks.forEach(task => {
    const taskId = task.id;
    const fileName = `task_${taskId.toString().padStart(3, '0')}.txt`;
    const filePath = path.join(tasksDir, fileName);
    
    // 格式化依赖项
    const dependenciesText = task.dependencies && task.dependencies.length > 0 
      ? task.dependencies.join(', ') 
      : 'None';
    
    // 创建任务文件内容
    const content = `# Task ID: ${taskId}
# Title: ${task.title}
# Status: ${task.status}
# Dependencies: ${dependenciesText}
# Priority: ${task.priority}
# Description: ${task.description}
# Details:
${task.details}

# Test Strategy:
${task.testStrategy}
`;
    
    // 写入文件
    fs.writeFileSync(filePath, content);
    console.log(`已生成: ${fileName}`);
  });
  
  console.log('所有任务文件已生成完成!');
  
} catch (error) {
  console.error('生成任务文件时出错:', error);
  process.exit(1);
} 