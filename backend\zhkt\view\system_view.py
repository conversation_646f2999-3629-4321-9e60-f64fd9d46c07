from rest_framework import permissions
from .base_model_view import BaseModelViewSet
from zhkt.serializers import (
    RoleSerializer,
    DeptSerializer,
    MenuSerializer,
)
from zhkt.entitys import (
    Role,
    Dept,
    Menu,
)

class RoleViewSet(BaseModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAuthenticated]

class DeptViewSet(BaseModelViewSet):
    queryset = Dept.objects.all()
    serializer_class = DeptSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Dept.objects.filter(deleted_at__isnull=True).order_by('order', 'id')
        parent_id = self.request.query_params.get('parent_id', None)
        if parent_id is not None:
            queryset = queryset.filter(parent_id=parent_id)
        return queryset

class MenuViewSet(BaseModelViewSet):
    queryset = Menu.objects.all()
    serializer_class = MenuSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Menu.objects.filter(deleted_at__isnull=True).order_by('order', 'id')
        parent_id = self.request.query_params.get('parent_id', None)
        if parent_id is not None:
            queryset = queryset.filter(parent_id=parent_id)
        return queryset