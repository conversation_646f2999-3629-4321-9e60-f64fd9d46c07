import os
import uuid
import subprocess
from typing import Optional, List, Union
from datetime import datetime
import oss2

from .. import config


class FileUtils:
    """文件处理工具类"""
    
    @staticmethod
    def extract_video_frame(video_path: str, frame_number: int = 3) -> str:
        """
        从视频中截取指定帧作为图片
        
        参数:
        - video_path: 视频文件路径
        - frame_number: 要截取的帧数，默认第3帧
        - sub_dir: 子目录名称
        
        返回:
        - 截取的图片的相对路径
        """
        if not os.path.exists(video_path):
            raise ValueError("视频文件不存在")
        
        # 创建临时图片文件路径
        temp_image_path = f"{video_path}_frame_{frame_number}.png"
        
        try:
            # 使用ffmpeg截取指定帧
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-vf', f'select=eq(n\\,{frame_number})',
                '-vframes', '1',
                '-y',  # 覆盖输出文件
                temp_image_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"ffmpeg执行失败: {result.stderr}")
            
            if not os.path.exists(temp_image_path):
                raise Exception("截取帧失败，未生成图片文件")
            
            # 保存截取的图片
            saved_path = FileUtils.save_image_file(temp_image_path)
            
            return saved_path
            
        except Exception as e:
            raise Exception(f"视频截帧失败: {str(e)}")
        finally:
            # 清理临时文件
            if os.path.exists(temp_image_path):
                try:
                    os.remove(temp_image_path)
                except:
                    pass
    
    @staticmethod
    def save_upload_file(
        file_path: str, 
        directory: str, 
        allowed_extensions: List[str] = None,
        max_size_mb: int = None,
        rename: bool = True
    ) -> str:
        """
        保存上传的文件，支持本地存储和OSS存储
        
        参数:
        - file_path: 上传文件的本地路径
        - directory: 保存目录
        - allowed_extensions: 允许的文件扩展名列表
        - max_size_mb: 文件最大大小 (MB)
        - rename: 是否使用UUID重命名文件
        
        返回:
        - str: 文件的相对路径，不包含uploads前缀，用于存储到数据库
        """
        if file_path is None or not os.path.exists(file_path):
            raise ValueError("未提供有效的文件路径")
        
        # 创建年月子目录
        now = datetime.now()
        year_month = now.strftime("%Y%m")
        sub_directory = os.path.join(directory, year_month)
        
        # 获取文件扩展名
        ext = os.path.splitext(os.path.basename(file_path))[1]
        file_ext = ext[1:].lower() if ext else ""
        
        # 验证文件类型
        if allowed_extensions and file_ext not in allowed_extensions:
            raise ValueError(f"不支持的文件类型，仅支持: {', '.join(allowed_extensions)}")
        
        # 验证文件大小
        if max_size_mb:
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            if file_size_mb > max_size_mb:
                raise ValueError(f"文件过大，最大允许 {max_size_mb}MB")
        
        # 生成文件名
        if rename:
            filename = f"{uuid.uuid4()}{ext}"
        else:
            filename = os.path.basename(file_path)
        
        # 完整的文件路径（统一使用正斜杠）
        dest_file_path = os.path.join(sub_directory, filename).replace("\\", "/")
        
        # 读取文件内容
        with open(file_path, "rb") as f:
            content = f.read()
        
        if config.OSS_ENABLED:
            # OSS上传
            try:
                auth = oss2.Auth(config.OSS_ACCESS_KEY_ID, config.OSS_ACCESS_KEY_SECRET)
                bucket = oss2.Bucket(auth, config.OSS_ENDPOINT, config.OSS_BUCKET_NAME)
                
                # 上传到OSS
                bucket.put_object(dest_file_path, content)
                
                # 返回OSS的访问路径
                return dest_file_path
            except Exception as e:
                raise ValueError(f"OSS上传失败: {str(e)}")
        else:
            # 本地存储
            try:
                # 确保目录存在
                local_path = os.path.join(config.UPLOAD_BASE_DIR, dest_file_path)
                os.makedirs(os.path.dirname(local_path), exist_ok=True)
                
                # 保存文件
                with open(local_path, "wb") as f:
                    f.write(content)
                
                # 返回用于数据库存储的相对路径
                return dest_file_path
            except Exception as e:
                raise ValueError(f"文件保存失败: {str(e)}")

    @staticmethod
    def save_audio_file(file_path: str) -> str:
        """保存音频文件"""
        return FileUtils.save_upload_file(
            file_path=file_path,
            directory=config.AUDIO_UPLOAD_DIR,
            allowed_extensions=config.ALLOWED_AUDIO_EXTENSIONS,
            max_size_mb=config.MAX_AUDIO_FILE_SIZE
        )

    @staticmethod
    def save_image_file(file_path: str, sub_dir: str = None) -> str:
        """
        保存图片文件
        
        参数:
        - file_path: 上传的图片文件路径
        - sub_dir: 子目录名称，如"avatar"等
        
        返回:
        - 保存后的相对路径
        """
        # 基础图片目录
        directory = config.IMAGE_UPLOAD_DIR
        
        # 如果指定了子目录，拼接到图片目录下
        if sub_dir:
            directory = os.path.join(directory, sub_dir)
            
        return FileUtils.save_upload_file(
            file_path=file_path,
            directory=directory,
            allowed_extensions=config.ALLOWED_IMAGE_EXTENSIONS,
            max_size_mb=config.MAX_IMAGE_FILE_SIZE
        )
    
    @staticmethod
    def save_video_file(file_path: str, sub_dir: str = None) -> str:
        """保存视频文件"""
        directory = config.VIDEO_UPLOAD_DIR
        if sub_dir:
            directory = os.path.join(directory, sub_dir)
            
        return FileUtils.save_upload_file(
            file_path=file_path,
            directory=directory,
            allowed_extensions=config.ALLOWED_VIDEO_EXTENSIONS,
            max_size_mb=config.MAX_VIDEO_FILE_SIZE
        )
    
    @staticmethod
    def save_knowledge_file(file_path: str, sub_dir: str = None) -> str:
        """保存知识库文件"""
        directory = config.KNOWLEDGE_UPLOAD_DIR
        if sub_dir:
            directory = os.path.join(directory, sub_dir)
            
        return FileUtils.save_upload_file(
            file_path=file_path,
            directory=directory,
            allowed_extensions=config.ALLOWED_KNOWLEDGE_EXTENSIONS,
            max_size_mb=config.MAX_KNOWLEDGE_FILE_SIZE
        )
        
    @staticmethod
    def save_txt_file(file_path: str, sub_dir: str = None) -> str:
        """
        保存TXT文本文件
        
        参数:
        - file_path: 上传的文本文件路径
        
        返回:
        - 保存后的相对路径
        """
        directory = config.TXT_UPLOAD_DIR
        if sub_dir:
            directory = os.path.join(directory, sub_dir)
            
        return FileUtils.save_upload_file(
            file_path=file_path,
            directory=directory,
            allowed_extensions=config.ALLOWED_TXT_EXTENSIONS,
            max_size_mb=config.MAX_TXT_FILE_SIZE
        )
    
    @staticmethod
    def save_json_file(file_path: str, sub_dir: str = None) -> str:
        """
        保存JSON文件
        
        参数:
        - file_path: 上传的JSON文件路径
        
        返回:
        - 保存后的相对路径
        """
        directory = config.JSON_UPLOAD_DIR
        if sub_dir:
            directory = os.path.join(directory, sub_dir)
            
        return FileUtils.save_upload_file(
            file_path=file_path,
            directory=directory,
            allowed_extensions=config.ALLOWED_JSON_EXTENSIONS,
            max_size_mb=config.MAX_JSON_FILE_SIZE
        )
    
    @staticmethod
    def save_html_file(file_path: str, sub_dir: str = None) -> str:
        """
        保存HTML文件
        
        参数:
        - file_path: 上传的HTML文件路径
        
        返回:
        - 保存后的相对路径
        """
        directory = config.HTML_UPLOAD_DIR
        if sub_dir:
            directory = os.path.join(directory, sub_dir)
            
        return FileUtils.save_upload_file(
            file_path=file_path,
            directory=directory,
            allowed_extensions=config.ALLOWED_HTML_EXTENSIONS,
            max_size_mb=config.MAX_HTML_FILE_SIZE
        )

    @staticmethod
    def get_file_url(file_path: str, full_url: bool = True) -> str:
        """
        获取文件的URL路径
        
        参数:
        - file_path: 文件路径（存储在数据库中的路径）
        - full_url: 是否返回完整URL（包含域名），默认为True
        
        返回:
        - 文件的URL路径，可以是相对路径或完整URL
        """
        if not file_path:
            return ""

        if config.OSS_ENABLED:
            # OSS文件URL
            if full_url:
                return f"{config.OSS_BASE_URL}/{file_path}"
            else:
                return file_path
        else:
            # 本地文件URL
            if full_url:
                return f"{config.BASE_URL}/uploads/{file_path}"
            else:
                return file_path
                
    @staticmethod
    def download_from_oss(file_path: str, local_save_path: Optional[str] = None) -> Union[bytes, str]:
        """
        从OSS下载文件
        
        参数:
        - file_path: OSS中的文件路径
        - local_save_path: 本地保存路径，如果不提供则返回文件内容
        
        返回:
        - 如果提供local_save_path，返回保存的本地文件路径
        - 如果不提供local_save_path，返回文件内容的字节数据
        """
        if not config.OSS_ENABLED:
            raise ValueError("OSS未启用，无法下载文件")
            
        try:
            # 创建OSS客户端
            auth = oss2.Auth(config.OSS_ACCESS_KEY_ID, config.OSS_ACCESS_KEY_SECRET)
            bucket = oss2.Bucket(auth, config.OSS_ENDPOINT, config.OSS_BUCKET_NAME)
            
            # 检查文件是否存在
            if not bucket.object_exists(file_path):
                raise ValueError(f"OSS中不存在文件: {file_path}")
            
            # 下载文件
            if local_save_path:
                # 确保目录存在
                os.makedirs(os.path.dirname(local_save_path), exist_ok=True)
                
                # 下载到本地文件
                bucket.get_object_to_file(file_path, local_save_path)
                return local_save_path
            else:
                # 下载到内存
                result = bucket.get_object(file_path)
                return result.read()
                
        except oss2.exceptions.NoSuchKey:
            raise ValueError(f"OSS中不存在文件: {file_path}")
        except Exception as e:
            raise ValueError(f"从OSS下载文件失败: {str(e)}")
            

    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """
        删除文件
        
        参数:
        - file_path: 文件相对路径
        
        返回:
        - 是否删除成功
        """
        if not file_path:
            return False
            
        if config.OSS_ENABLED:
            # 删除OSS文件
            try:
                auth = oss2.Auth(config.OSS_ACCESS_KEY_ID, config.OSS_ACCESS_KEY_SECRET)
                bucket = oss2.Bucket(auth, config.OSS_ENDPOINT, config.OSS_BUCKET_NAME)
                bucket.delete_object(file_path)
                return True
            except Exception as e:
                print(f"删除OSS文件失败: {str(e)}")
                return False
        else:
            # 删除本地文件
            try:
                local_path = os.path.join(config.UPLOAD_BASE_DIR, file_path)
                if os.path.exists(local_path):
                    os.remove(local_path)
                    return True
                return False
            except Exception as e:
                print(f"删除本地文件失败: {str(e)}")
                return False