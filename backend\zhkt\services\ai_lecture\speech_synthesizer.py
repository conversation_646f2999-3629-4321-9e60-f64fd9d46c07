# -*- coding: utf-8 -*-
"""
语音合成服务
负责语音脚本生成、TTS合成、语音内容管理等功能
"""

import logging
import os
import re
import subprocess
import tempfile
from typing import Any, Dict, List

from ...entitys.ai_lecture import (AILectureChapter, AILectureKeyPoint,
                                    AILectureSpeechContent, AILectureSpeechStyle,
                                    AILectureSubtitle)
from ...prompt.ai_lecture_prompts import AILecturePromptManager
from ...utils.deepseek_api import DeepSeekAPI
from ...utils.doubao_tts import DoubaoTTS
from .exceptions import SpeechSynthesisException
from .file_manager import FileManager

logger = logging.getLogger(__name__)

# AI模型配置
AI_MODEL = "deepseek-chat"


class SpeechSynthesizer:
    """语音合成器 - 负责语音脚本生成和TTS合成"""
    
    def __init__(self):
        self.deepseek_api = DeepSeekAPI()
        self.tts_engine = DoubaoTTS()
        self.prompt_manager = AILecturePromptManager()
        self.file_manager = FileManager()
        self.logger = logger

    def get_speech_style_template(self, style_code: str) -> str:
        """
        根据风格代码获取对应的语音提示词模板
        
        Args:
            style_code: 风格代码
            
        Returns:
            str: 提示词模板
            
        Raises:
            SpeechSynthesisException: 获取失败时抛出
        """
        try:
            style_record = AILectureSpeechStyle.objects.filter(
                style_code=style_code,
                deleted_at__isnull=True
            ).first()

            if style_record and style_record.prompt_template:
                self.logger.info(f"成功获取风格 {style_code} 的提示词模板")
                return style_record.prompt_template
            else:
                error_msg = f"未找到风格 {style_code} 的提示词模板"
                self.logger.error(error_msg)
                raise SpeechSynthesisException(error_msg)
                
        except Exception as e:
            if isinstance(e, SpeechSynthesisException):
                raise e
            error_msg = f"获取风格 {style_code} 的提示词模板失败: {e}"
            self.logger.error(error_msg)
            raise SpeechSynthesisException(error_msg)

    def get_previous_speech_context(self, key_point: AILectureKeyPoint, context_count: int = 2) -> str:
        """
        获取前面几个要点的语音内容，用于保持语音连续性
        
        Args:
            key_point: 当前要点对象
            context_count: 获取前几个要点的上下文
            
        Returns:
            str: 前置语音上下文
        """
        try:
            chapter = key_point.chapter
            current_order = key_point.point_order
            
            previous_points = AILectureKeyPoint.objects.filter(
                chapter=chapter,
                point_order__lt=current_order,
                deleted_at__isnull=True
            ).order_by('-point_order')[:context_count]
            
            if not previous_points.exists():
                return ""
            
            context_parts = []
            for i, prev_point in enumerate(reversed(previous_points)):
                speech_contents = AILectureSpeechContent.objects.filter(
                    key_point=prev_point,
                    deleted_at__isnull=True
                ).order_by('sentence_order')
                
                if speech_contents.exists():
                    speech_text = " ".join([content.speech_script for content in speech_contents])
                    context_label = f"前{context_count - i}个要点" if context_count > 1 else "前一个要点"
                    context_parts.append(f"【{context_label}讲述内容】：{speech_text}")
            
            return "\n".join(context_parts) if context_parts else ""
            
        except Exception as e:
            self.logger.warning(f"获取前置语音内容失败: {e}")
            return ""

    def generate_speech_script_standard(self, key_points: str, chapter_title: str, 
                                      chapter_text: str, style_code: str = 'classroom', 
                                      previous_context: str = "") -> str:
        """
        根据指定风格生成标准语音讲稿
        
        Args:
            key_points: 要点内容
            chapter_title: 章节标题
            chapter_text: 章节文本
            style_code: 风格代码
            previous_context: 前置上下文
            
        Returns:
            str: 生成的语音脚本
            
        Raises:
            SpeechSynthesisException: 生成失败时抛出
        """
        try:
            prompt_template = self.get_speech_style_template(style_code)
            
            if previous_context:
                enhanced_prompt = f"""
{prompt_template}

重要：为保持讲课的连续性，请参考以下前面的讲述内容：

{previous_context}

请在生成本次讲稿时：
1. 适当呼应前面的内容，然后直接进入讲解
2. 避免重复前面已经讲过的内容
3. 确保逻辑连贯，前后呼应
4. 保持讲课的自然流畅性

输出要求：
- 只输出纯净的口播内容，不要包含任何markdown格式符号
- 不要使用加粗、斜体、列表符号等格式
- 直接输出可以朗读的讲稿文本
- 语言自然流畅，适合口语表达
"""
                prompt = enhanced_prompt.format(
                    chapter_title=chapter_title,
                    points=key_points,
                    chapter_text=chapter_text
                )
            else:
                # 为没有上下文的情况也添加输出要求
                enhanced_template = f"""
{prompt_template}

输出要求：
- 只输出纯净的口播内容，不要包含任何markdown格式符号
- 不要使用加粗、斜体、列表符号等格式
- 直接输出可以朗读的讲稿文本
- 语言自然流畅，适合口语表达
"""
                prompt = enhanced_template.format(
                    chapter_title=chapter_title,
                    points=key_points,
                    chapter_text=chapter_text
                )

            system_messages = self.prompt_manager.get_system_messages()
            messages = [
                DeepSeekAPI.create_system_message(system_messages['speech_expert']),
                DeepSeekAPI.create_user_message(prompt)
            ]
            
            speech_script = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            return speech_script.strip()
            
        except Exception as e:
            error_msg = f"生成标准语音脚本失败: {e}"
            self.logger.error(error_msg)
            raise SpeechSynthesisException(error_msg)

    def generate_speech_script_with_html(self, key_points: str, chapter_title: str, 
                                       chapter_text: str, html_code: str, 
                                       style_code: str = 'classroom', 
                                       previous_context: str = "") -> str:
        """
        根据HTML内容生成语音讲稿 - 以HTML内容为主，章节内容为辅
        
        Args:
            key_points: 要点内容
            chapter_title: 章节标题
            chapter_text: 章节文本
            html_code: HTML页面代码
            style_code: 风格代码
            previous_context: 前置上下文
            
        Returns:
            str: 生成的语音脚本
            
        Raises:
            SpeechSynthesisException: 生成失败时抛出
        """
        try:
            prompt_template = self.get_speech_style_template(style_code)
            
            # 安全地构建基础提示词，避免HTML大括号冲突
            base_prompt = self._build_safe_prompt_template(
                prompt_template, chapter_title, key_points, chapter_text
            )
            
            # 构建HTML特定的提示词部分
            html_section = f"""
核心任务：根据HTML页面内容进行精准讲解

主要依据 - HTML页面展示内容：
{html_code}

辅助参考 - 章节背景信息（仅作补充理解）：
章节背景：{chapter_text}

生成讲稿的重要原则：
1. 以HTML页面内容为绝对核心：讲稿必须与页面展示的信息完全匹配
2. 按页面层次结构讲解：遵循HTML页面的信息组织方式和视觉层次
3. 章节内容仅作背景参考：只在需要补充解释概念时才引用
4. 突出页面重点信息：重点讲解HTML中突出显示的关键内容
5. 保持讲解的完整性：确保涵盖页面的主要信息点
6. 语言自然流畅：让听众感觉是在看着页面听讲解

输出要求：
- 只输出纯净的口播内容，不要包含任何markdown格式符号
- 不要使用加粗、斜体、列表符号等格式
- 直接输出可以朗读的讲稿文本
- 语言自然流畅，适合口语表达
"""
            
            # 根据是否有前文上下文构建完整提示词
            if previous_context:
                context_section = f"""
重要：为保持讲课的连续性，请参考以下前面的讲述内容：

{previous_context}

生成讲稿时请注意：
1. 适当呼应前面的内容：自然过渡，避免重复
2. 保持逻辑连贯性：确保讲解流畅自然
3. 关注页面重点信息：突出HTML中的关键要点和结构
"""
                prompt = f"{base_prompt}\n{context_section}\n{html_section}"
            else:
                prompt = f"{base_prompt}\n{html_section}"

            system_messages = self.prompt_manager.get_system_messages()
            messages = [
                DeepSeekAPI.create_system_message(system_messages['speech_expert']),
                DeepSeekAPI.create_user_message(prompt)
            ]
            
            speech_script = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            return speech_script.strip()
            
        except Exception as e:
            error_msg = f"基于HTML生成语音脚本失败: {e}"
            self.logger.error(error_msg)
            raise SpeechSynthesisException(error_msg)

    def _build_safe_prompt_template(self, template: str, chapter_title: str, 
                                  points: str, chapter_text: str) -> str:
        """
        安全地构建提示词模板，避免HTML中的大括号导致格式化错误
        使用字典映射方式进行替换，更安全和清晰
        
        Args:
            template: 模板字符串
            chapter_title: 章节标题
            points: 要点内容
            chapter_text: 章节文本
            
        Returns:
            str: 安全构建的提示词
        """
        # 定义替换映射
        replacements = {
            "{chapter_title}": chapter_title,
            "{points}": points,
            "{chapter_text}": chapter_text
        }
        
        # 逐个进行安全替换
        result = template
        for placeholder, value in replacements.items():
            if placeholder in result:
                result = result.replace(placeholder, value)
        
        return result

    def split_text_into_sentences(self, text: str) -> List[str]:
        """
        按中文句号、问号、感叹号、英文句号等分句
        
        Args:
            text: 待分句的文本
            
        Returns:
            List[str]: 分句结果列表
        """
        sentences = re.split(r'(?<=[。！？!?])', text)
        return [sentence.strip() for sentence in sentences if sentence.strip()]

    def is_only_punctuation_and_whitespace(self, text: str) -> bool:
        """
        检查文本是否只包含标点符号和空白字符
        
        Args:
            text: 待检查的文本
            
        Returns:
            bool: 是否只包含标点符号和空白字符
        """
        if not text or not text.strip():
            return True
        
        text_no_whitespace = re.sub(r'\s+', '', text)
        if not text_no_whitespace:
            return True
        
        # 检查是否只包含非文字字符（标点符号等）
        punctuation_only = re.match(r'^[^\w\u4e00-\u9fff]+$', text_no_whitespace)
        return punctuation_only is not None

    def synthesize_audio_content(self, text: str, voice_type: str = "BV001_streaming", 
                               encoding: str = "mp3", speed_ratio: float = 1.0, 
                               loudness_ratio: float = 1.0) -> bytes:
        """
        合成音频内容
        
        Args:
            text: 待合成的文本
            voice_type: 语音类型
            encoding: 音频编码格式
            speed_ratio: 语速比例
            loudness_ratio: 音量比例
            
        Returns:
            bytes: 音频数据
            
        Raises:
            SpeechSynthesisException: 合成失败时抛出
        """
        try:
            audio_data = self.tts_engine.synthesize(
                text=text,
                voice_type=voice_type,
                encoding=encoding,
                speed_ratio=speed_ratio,
                loudness_ratio=loudness_ratio
            )
            return audio_data
            
        except Exception as e:
            error_msg = f"音频合成失败: {e}"
            self.logger.error(error_msg)
            raise SpeechSynthesisException(error_msg)

    def create_speech_from_split(self, key_point: AILectureKeyPoint, page_data: Dict[str, Any], 
                                 source_audio_path: str, chapter: AILectureChapter,
                                 subtitle_data: List[Dict[str, Any]], language: str = 'zh'):
        """
        根据页面数据中的时间戳分割音频，并创建语音内容。
        
        Args:
            key_point: 要点对象
            page_data: 页面数据，包含时间戳和内容摘要
            source_audio_path: 源音频文件路径
            chapter: 章节对象
            subtitle_data: 对应页面的字幕数据列表
            language: 语言代码，支持 'zh', 'en', 'vi', 'id'
            
        Returns:
            List: 创建的AILectureSpeechContent对象列表
        """
        start_timestamp = page_data.get('start_timestamp')
        end_timestamp = page_data.get('end_timestamp')

        # if not (start_timestamp and end_timestamp):
        #     self.logger.warning(f"页面 {page_data.get('page_number')} 缺少时间戳，跳过音频分割。")
        #     return []

        temp_output_path = None
        try:
            temp_dir = tempfile.gettempdir()
            temp_output_filename = f"split_{chapter.id}_{key_point.id}_{language}.wav"
            temp_output_path = os.path.join(temp_dir, temp_output_filename)

            # FFmpeg可以直接使用秒或H:M:S格式的时间戳
            command = [
                'ffmpeg', '-i', str(source_audio_path),
                '-ss', str(start_timestamp), '-to', str(end_timestamp),
                '-c', 'copy', '-y', str(temp_output_path)
            ]
            self.logger.info(f"执行ffmpeg命令分割音频: {' '.join(command)} (语言: {language})")
            
            subprocess.run(command, check=True, capture_output=True, text=True)

            with open(temp_output_path, 'rb') as f_audio:
                audio_content = f_audio.read()

            audio_storage_path = self.file_manager.save_audio_speech_file(
                audio_content, chapter.chapter_title, key_point.point_order, 1
            )

            speech_content = AILectureSpeechContent.objects.create(
                key_point=key_point,
                sentence_order=1,
                speech_script=page_data.get('content_summary', ''),
                audio_file_path=audio_storage_path,
                language_code=language  # 设置语言代码
            )
            
            # 保存字幕数据
            self.save_subtitles_for_speech_contents([speech_content], page_data, subtitle_data, language)

            key_point.audio_status = 'completed'
            key_point.save()
            self.logger.info(f"成功为要点 {key_point.id} 分割并保存音频 (语言: {language})。")
            
            return [speech_content]

        except FileNotFoundError:
            self.logger.error("ffmpeg 命令未找到。请确保 ffmpeg 已安装并在系统 PATH 中。")
            key_point.audio_status = 'not_gen'
            key_point.save()
            raise SpeechSynthesisException("ffmpeg未安装或未在PATH中")
        except subprocess.CalledProcessError as e:
            self.logger.error(f"ffmpeg 执行失败，返回码: {e.returncode}，输出: {e.stderr}")
            key_point.audio_status = 'not_gen'
            key_point.save()
            raise SpeechSynthesisException(f"ffmpeg执行失败: {e.stderr}")
        finally:
            if temp_output_path and os.path.exists(temp_output_path):
                os.remove(temp_output_path)

    def save_subtitles_for_speech_contents(self, speech_contents: List[AILectureSpeechContent], 
                                           page_data: Dict[str, Any], 
                                           subtitle_data: List[Dict[str, Any]], language: str = 'zh'):
        """
        为语音内容保存字幕数据
        
        Args:
            speech_contents: 语音内容对象列表 (当前逻辑只处理单个)
            page_data: 页面数据，包含页面的开始时间戳 (秒)
            subtitle_data: 对应此页面的字幕数据列表
            language: 语言代码
        """
        try:
            if not subtitle_data or not speech_contents:
                self.logger.warning("没有字幕数据或语音内容，跳过字幕保存")
                return
            
            # 当前逻辑只为一个分割后的音频片段创建一条语音内容记录
            speech_content = speech_contents[0]
            
            # 获取页面的开始时间（秒），用于计算字幕的相对时间
            # 此处假定 orchestrator 已经将时间戳转换为秒(float)并更新了 page_data
            page_start_time_sec = page_data.get('start_timestamp', 0)

            for subtitle in subtitle_data:
                # 字幕时间戳是相对于整个章节音频的绝对时间
                # 需要减去当前页面分割点的开始时间，转换为分割后音频的相对时间
                absolute_start_time = subtitle.get('start_time_seconds', 0)
                absolute_end_time = subtitle.get('end_time_seconds', 0)
                
                relative_start_time = absolute_start_time - page_start_time_sec
                relative_end_time = absolute_end_time - page_start_time_sec
                
                # 确保时间不为负（处理微小误差）
                relative_start_time = max(0, relative_start_time)
                relative_end_time = max(0, relative_end_time)

                AILectureSubtitle.objects.create(
                    speech_content=speech_content,
                    start_time=int(relative_start_time * 1000),  # 转换为毫秒
                    end_time=int(relative_end_time * 1000),      # 转换为毫秒
                    subtitle_text=subtitle.get('text', ''),
                    language_code=language  # 设置语言代码
                )
            
            self.logger.info(f"已为语音内容 {speech_content.id} 保存 {len(subtitle_data)} 条字幕")
        except Exception as e:
            self.logger.error(f"保存字幕数据失败: {str(e)}") 