# 项目介绍
这是一个智慧课堂项目。
## 项目结构
- backend: 后端代码
  - cd backend 
  - python manage.py runserver
  - 项目api swagger 文档: http://localhost:8000/swagger
- frontui: 前端代码
  - cd frontui
  - npm install
  - npm run dev
## 技术架构
- 后端: Django + Django Rest Framework + MySQL + JWT
- 前端: Vue3 + Element-Plus ...
## 功能模块介绍
  请参照docs文件夹下的各个模块md文件
## 部署linux问题说明
- 部署到linux有些包不兼容删除了python包poppt poword pypiwin32 pywin32 pywinauto
- 注释了speech_design_service.py

