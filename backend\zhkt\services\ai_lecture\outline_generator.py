# -*- coding: utf-8 -*-
"""
大纲生成服务
负责文档大纲的生成、验证和结构分析
"""

import concurrent.futures
import json
import logging
import re
from typing import List, Tuple, Dict

from tenacity import retry, stop_after_attempt, wait_fixed

from ...prompt.ai_lecture_prompts import AILecturePromptManager
from ...utils.deepseek_api import DeepSeekAPI
from .exceptions import OutlineGenerationException

logger = logging.getLogger(__name__)

# AI模型配置
AI_MODEL = "deepseek-chat"


class OutlineGenerator:
    """大纲生成器 - 负责文档大纲的生成和验证"""
    
    def __init__(self):
        self.deepseek_api = DeepSeekAPI()
        self.prompt_manager = AILecturePromptManager()
        self.logger = logger

    def split_text_into_logical_chunks(self, text: str, max_chunk_size: int = 26000) -> List[str]:
        """
        将文本分割成逻辑块，保持段落完整性
        
        Args:
            text: 待分割的文本
            max_chunk_size: 每块最大字符数
            
        Returns:
            List[str]: 分割后的文本块列表
        """
        text_paragraphs = text.split("\n")
        text_chunks = []
        current_chunk = ""
        
        for paragraph in text_paragraphs:
            # 检查添加当前段落后是否超出限制
            if len(current_chunk) + len(paragraph) + 1 < max_chunk_size:
                current_chunk += (paragraph + "\n")
            else:
                # 保存当前块（如果不为空）
                if current_chunk:
                    text_chunks.append(current_chunk.strip())
                
                # 处理超长段落
                if len(paragraph) > max_chunk_size:
                    # 将超长段落进一步分割
                    for i in range(0, len(paragraph), max_chunk_size):
                        text_chunks.append(paragraph[i:i + max_chunk_size])
                    current_chunk = ""
                else:
                    current_chunk = paragraph + "\n"
        
        # 添加最后一个块
        if current_chunk:
            text_chunks.append(current_chunk.strip())
        
        return text_chunks

    def generate_outline_for_text_chunk(self, chunk_info: Tuple[int, str]) -> Tuple[int, str]:
        """
        为单个文本块生成大纲
        
        Args:
            chunk_info: (块索引, 文本内容)的元组
            
        Returns:
            Tuple[int, str]: (块索引, 大纲内容)
            
        Raises:
            OutlineGenerationException: 生成失败时抛出
        """
        chunk_index, chunk_text = chunk_info
        
        try:
            system_prompt, user_prompt = self.prompt_manager.get_document_analysis_prompts(chunk_text)
            messages = [
                DeepSeekAPI.create_system_message(system_prompt),
                DeepSeekAPI.create_user_message(user_prompt)
            ]
            
            outline_content = self.deepseek_api.chat(messages=messages, model=AI_MODEL)
            self.logger.info(f"文本块 {chunk_index + 1} 的大纲生成完成")
            return chunk_index, outline_content
            
        except Exception as e:
            error_msg = f"处理文本块 {chunk_index + 1} 时出错: {e}"
            self.logger.error(error_msg)
            return chunk_index, f"错误：无法为此文本块生成大纲。原因：{str(e)}"

    def generate_outline_chunks_concurrently(self, document_content: List, max_workers: int = None) -> List[str]:
        """
        并发生成文档各部分的大纲
        
        Args:
            document_content: 文档内容列表
            max_workers: 最大并发数
            
        Returns:
            List[str]: 各部分大纲列表
            
        Raises:
            OutlineGenerationException: 生成失败时抛出
        """
        try:
            # 构建完整文档文本
            full_document_text = ''
            for content_item in document_content:
                if (content_item.get("type") == "text" and 
                    content_item.get("text") is not None and 
                    content_item.get("text").strip() != "" and 
                    content_item.get("page_idx") is not None):
                    page_number = content_item.get('page_idx') + 1
                    text_content = content_item.get('text')
                    full_document_text += f"[页码:{page_number}] {text_content} \n"
            
            self.logger.info(f"开始将文档分割成文本块...")
            text_chunks = self.split_text_into_logical_chunks(full_document_text)
            self.logger.info(f"文档已分割成 {len(text_chunks)} 个文本块")
            
            # 准备并发任务数据
            chunk_tasks = [(i, chunk) for i, chunk in enumerate(text_chunks)]
            outline_results = [None] * len(text_chunks)
            
            # 并发生成大纲
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_chunk = {
                    executor.submit(self.generate_outline_for_text_chunk, chunk_info): chunk_info[0]
                    for chunk_info in chunk_tasks if chunk_info[1].strip()
                }
                
                for future in concurrent.futures.as_completed(future_to_chunk):
                    chunk_index, outline_content = future.result()
                    outline_results[chunk_index] = f"## 来自文本块 {chunk_index + 1} 的大纲\n{outline_content}"
            
            # 过滤空结果并返回
            return [result for result in outline_results if result is not None]
            
        except Exception as e:
            error_msg = f"并发生成大纲失败: {e}"
            self.logger.error(error_msg)
            raise OutlineGenerationException(error_msg)

    def validate_chapter_structure(self, chapter_outline: List[Dict]) -> bool:
        """
        验证章节大纲的结构有效性
        
        Args:
            chapter_outline: 章节大纲列表
            
        Returns:
            bool: 是否有效
        """
        if not chapter_outline or len(chapter_outline) == 0:
            self.logger.error("章节大纲为空")
            return False
        
        try:
            page_ranges = []
            
            # 基础字段验证
            for chapter in chapter_outline:
                start_page = chapter.get("start")
                end_page = chapter.get("stop")
                chapter_title = chapter.get("chapter", "未知章节")
                
                if start_page is None or end_page is None:
                    self.logger.error(f"章节 '{chapter_title}' 缺少必要的页码字段")
                    return False
                
                if not isinstance(start_page, int) or not isinstance(end_page, int):
                    self.logger.error(f"章节 '{chapter_title}' 页码格式错误: start={start_page}, stop={end_page}")
                    return False
                    
                if start_page <= 0 or end_page <= 0:
                    self.logger.error(f"章节 '{chapter_title}' 页码必须大于0: start={start_page}, stop={end_page}")
                    return False
                    
                if start_page > end_page:
                    self.logger.error(f"章节 '{chapter_title}' 起始页不能大于结束页: start={start_page}, stop={end_page}")
                    return False
                    
                page_ranges.append((start_page, end_page, chapter_title))
            
            # 页码重叠和间隙检查
            page_ranges.sort(key=lambda x: x[0])  # 按起始页排序
            
            for i in range(1, len(page_ranges)):
                prev_start, prev_end, prev_title = page_ranges[i - 1]
                curr_start, curr_end, curr_title = page_ranges[i]
                
                # 检查重叠
                if curr_start <= prev_end:
                    self.logger.error(
                        f"发现页码重叠: 章节'{prev_title}'({prev_start}-{prev_end}) "
                        f"与 章节'{curr_title}'({curr_start}-{curr_end})"
                    )
                    return False
                
                # 检查间隙是否过大
                page_gap = curr_start - prev_end - 1
                if page_gap > 3:
                    self.logger.error(
                        f"发现页码间隙过大: 章节'{prev_title}'({prev_start}-{prev_end}) "
                        f"与 章节'{curr_title}'({curr_start}-{curr_end})，间隙={page_gap}页"
                    )
                    return False
            
            # 章节数量检查
            chapter_count = len(page_ranges)
            if chapter_count < 2:
                self.logger.error(f"章节数量过少: {chapter_count}")
                return False
                
            if chapter_count > 20:
                self.logger.error(f"章节数量过多: {chapter_count}")
                return False
            
            self.logger.info(f"章节大纲结构验证通过，共{chapter_count}个章节")
            return True
            
        except Exception as e:
            self.logger.error(f"验证章节大纲时发生异常: {e}")
            return False

    def analyze_outline_structure_errors(self, chapter_outline: List[Dict]) -> str:
        """
        分析章节大纲的具体结构错误
        
        Args:
            chapter_outline: 章节大纲列表
            
        Returns:
            str: 错误详情描述
        """
        if not chapter_outline:
            return "大纲为空"
        
        error_messages = []
        page_ranges = []
        
        # 收集基础错误
        for i, chapter in enumerate(chapter_outline):
            start_page = chapter.get("start")
            end_page = chapter.get("stop")
            chapter_title = chapter.get("chapter", f"章节{i+1}")
            
            if start_page is None or end_page is None:
                error_messages.append(f"章节'{chapter_title}'缺少页码信息")
                continue
            
            if not isinstance(start_page, int) or not isinstance(end_page, int):
                error_messages.append(f"章节'{chapter_title}'页码格式错误: start={start_page}, stop={end_page}")
                continue
            
            if start_page > end_page:
                error_messages.append(f"章节'{chapter_title}'起始页大于结束页: {start_page} > {end_page}")
                continue
            
            page_ranges.append((start_page, end_page, chapter_title))
        
        # 检查页码重叠
        page_ranges.sort(key=lambda x: x[0])
        for i in range(1, len(page_ranges)):
            prev_start, prev_end, prev_title = page_ranges[i - 1]
            curr_start, curr_end, curr_title = page_ranges[i]
            
            if curr_start <= prev_end:
                error_messages.append(
                    f"页码重叠: '{prev_title}'({prev_start}-{prev_end}) "
                    f"与 '{curr_title}'({curr_start}-{curr_end})"
                )
        
        return "; ".join(error_messages) if error_messages else "未知结构错误"

    @retry(stop=stop_after_attempt(6), wait=wait_fixed(2), reraise=True)
    def _generate_and_validate_outline(self, combined_outline: str) -> List[Dict]:
        """
        使用AI生成并验证大纲，带重试机制。
        如果验证失败，将引发异常以触发重试。
        """
        self.logger.info("尝试生成并验证大纲...")
        system_prompt, user_prompt = self.prompt_manager.get_outline_merge_prompts(combined_outline)
        messages = [
            DeepSeekAPI.create_system_message(system_prompt),
            DeepSeekAPI.create_user_message(user_prompt)
        ]
        final_outline_response = self.deepseek_api.chat(messages=messages, model=AI_MODEL)

        # 提取JSON内容
        try:
            json_pattern = r"```json\n(.*?)\n```"
            match = re.search(json_pattern, final_outline_response, re.DOTALL)
            if match:
                chapter_outline_str = match.group(1)
                chapter_outline = json.loads(chapter_outline_str)
            else:
                chapter_outline = json.loads(final_outline_response)
        except json.JSONDecodeError as e:
            self.logger.error(f"无法解析AI返回的JSON: {final_outline_response}. Error: {e}")
            raise OutlineGenerationException("AI返回的不是有效的JSON格式") from e

        # 验证大纲结构
        if not self.validate_chapter_structure(chapter_outline):
            error_details = self.analyze_outline_structure_errors(chapter_outline)
            self.logger.warning(f"AI生成的大纲结构验证失败。错误: {error_details}. 将进行重试...")
            raise OutlineGenerationException(f"AI生成的大纲结构验证失败: {error_details}")

        self.logger.info("大纲生成并验证成功。")
        return chapter_outline

    def generate_document_outline_with_ai(self, document, document_content_list: List) -> Dict[str, any]:
        """
        使用AI生成文档大纲（完整版本）

        Args:
            document: AI讲课文档对象
            document_content_list: 文档内容列表

        Returns:
            Dict[str, any]: 包含大纲生成结果的字典
        """
        try:
            self.logger.info(f"开始使用AI为文档 {document.title} 生成大纲")

            # 生成各部分大纲
            outline_parts = self.generate_outline_chunks_concurrently(document_content_list)

            if not outline_parts:
                return {'status': 'fail', 'error': '未能生成任何大纲内容'}

            # 合并大纲内容
            combined_outline = "\n\n".join(outline_parts)

            try:
                # 使用带重试机制的方法生成和验证大纲
                chapter_outline = self._generate_and_validate_outline(combined_outline)
            except Exception as e:
                self.logger.error(f"经过多次重试后，大纲生成仍然失败: {e}", exc_info=True)
                return {'status': 'fail', 'error': f"AI生成的大纲结构验证失败，请检查文档内容或稍后重试。错误详情: {e}"}

            self.logger.info(f"文档 {document.title} AI大纲生成完成，共 {len(chapter_outline)} 个章节")

            return {
                'status': 'success',
                'chapters': chapter_outline,
                'message': f'AI文档大纲生成完成，共 {len(chapter_outline)} 个章节'
            }

        except Exception as e:
            error_msg = f"AI生成文档大纲失败: {e}"
            self.logger.error(error_msg, exc_info=True)
            return {'status': 'fail', 'error': error_msg}