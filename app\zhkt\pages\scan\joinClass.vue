<template>
  <view class="join-class-container">
    <view class="result-area">
      <!-- 加载中图标 -->
      <view v-if="joinStatus === 'loading'" class="loading">
        <uni-load-more status="loading" />
      </view>
      
      <!-- 状态文本 -->
      <text class="status-text">{{statusText}}</text>
      
      <!-- 错误信息 -->
      <text v-if="errorMsg" class="error-text">{{errorMsg}}</text>
    </view>
    
    <!-- 底部按钮 -->
    <view class="bottom-btn">
      <button @click="handleBack" type="primary">返回</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request'

export default {
  data() {
    return {
      joinStatus: 'loading', // loading, success, fail
      statusText: '正在加入班级...',
      errorMsg: '',
      code: ''
    }
  },
  
  onLoad(options) {
    if (!options.code) {
      this.handleJoinFail('邀请码不能为空')
      return
    }
    this.code = options.code
    this.joinClass()
  },
  
  methods: {
    async joinClass() {
      try {
        const that = this;
        setTimeout(async () => {
            const response = await request({
                url: '/class-groups/join_by_code/',
                method: 'POST',
                data: { code: that.code}
            })
            console.log('joinClass->',response)
            if (response.error) {
                that.handleJoinFail(response.error || '加入班级失败')
            } else {
                that.handleJoinSuccess()
            }
        }, 2000)
      } catch (error) {
        console.error('发生未知异常:', error)
        that.handleJoinFail('发生未知异常,请稍后重试!')
      }
    },
    
    handleJoinSuccess() {
      this.joinStatus = 'success'
      this.statusText = '成功加入班级！'
      this.errorMsg = ''
    },
    
    handleJoinFail(msg) {
      this.joinStatus = 'fail'
      this.statusText = '加入班级失败'
      this.errorMsg = msg
    },
    
    handleBack() {
        // 成功后返回首页
        uni.switchTab({
            url: '/pages/index/index'
        })
        // 失败后返回上一页
        //uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.join-class-container {
  min-height: 90vh;
  padding: 0rpx;
  display: flex;
  flex-direction: column;
  
  .result-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .status-icon {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 40rpx;
    }
    
    .loading {
      margin-bottom: 40rpx;
    }
    
    .status-text {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .error-text {
      font-size: 28rpx;
      color: #ff4d4f;
      text-align: center;
    }
  }
  
  .bottom-btn {
    button {
      margin-left: auto;
      margin-right: auto;
      width: 90%;
    }
  }
}
</style>
