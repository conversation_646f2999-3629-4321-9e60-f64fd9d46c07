import request from '@/utils/request'
import { useAuthStore } from '@/stores/auth'

/**
 * 教学设计相关API
 */
export const lessonPlanApi = {
  /**
   * 上传教材并创建教学设计
   * @param {FormData} formData 请求参数
   * @param {File} formData.file 教材文件
   * @param {string} formData.lessonTemplate 教案模板，例如："标准教学模板"、"PBL问题导向模板"等
   * @param {string} formData.teachingStyle 教学风格，例如："平衡型（理论与实践并重）"等
   * @param {string} [formData.specificRequirements] 具体需求说明
   * @returns {Promise} 包含创建结果的Promise
   */
  createLessonPlan(formData) {
    return request({
      url: '/lesson-plan/create/',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 保存生成的教学设计
   * @param {Object} data 请求参数
   * @param {string} data.taskId 任务ID
   * @param {string} data.title 教学设计标题
   * @param {number} data.subjectId 学科ID
   * @returns {Promise} 包含保存结果的Promise
   */
  saveLessonPlan(data) {
    return request({
      url: '/lesson-plan/save/',
      method: 'post',
      data
    })
  },
  
  /**
   * 获取教学设计列表
   * @param {Object} params 查询参数
   * @param {number} [params.subject_id] 学科ID，可选
   * @param {number} [params.page=1] 页码，默认1
   * @param {number} [params.page_size=10] 每页大小，默认10
   * @returns {Promise} 包含教学设计列表的Promise
   */
  getLessonPlans(params) {
    return request({
      url: '/lesson-plan/list/',
      method: 'get',
      params
    })
  },
  
  /**
   * 获取教学设计详情
   * @param {number} id 教学设计ID
   * @returns {Promise} 包含教学设计详情的Promise
   */
  getLessonPlan(id) {
    return request({
      url: `/lesson-plan/${id}/detail/`,
      method: 'get'
    })
  },
  
  /**
   * 更新教学设计
   * @param {number} id 教学设计ID
   * @param {Object} data 更新数据
   * @param {string} [data.title] 教学设计标题，可选
   * @param {number} [data.subject_id] 学科ID，可选
   * @param {string} [data.content] 教学设计内容，可选
   * @param {string} [data.lesson_template] 教案模板，可选
   * @param {string} [data.teaching_style] 教学风格，可选
   * @param {string} [data.specific_requirements] 具体要求，可选
   * @returns {Promise} 包含更新结果的Promise
   */
  updateLessonPlan(id, data) {
    return request({
      url: `/lesson-plan/${id}/update/`,
      method: 'put',
      data
    })
  },
  
  /**
   * 删除教学设计
   * @param {number} id 教学设计ID
   * @returns {Promise} 包含删除结果的Promise
   */
  deleteLessonPlan(id) {
    return request({
      url: `/lesson-plan/${id}/delete/`,
      method: 'delete'
    })
  },
  
  /**
   * 获取教学设计任务状态
   * @param {string} taskId 任务ID
   * @returns {Promise} 包含任务状态的Promise
   */
  getTaskStatus(taskId) {
    return request({
      url: `/lesson-plan/status/${taskId}/`,
      method: 'get'
    })
  },
  
  /**
   * 获取流式生成的教学设计URL
   * @param {string} taskId 任务ID
   * @returns {string} 流式接口URL
   */
  getLessonPlanStreamUrl(taskId) {
    return `/api/lesson-plan/stream/${taskId}/`
  },

  /**
   * 获取流式生成的教学设计内容
   * @param {string} taskId 任务ID
   * @returns {Promise<Response>} fetch API响应
   */
  getLessonPlanStream(taskId) {
    const authStore = useAuthStore()
    const url = `/api/lesson-plan/stream/${taskId}/`
    
    console.log('请求教学设计流:', url)
    
    return fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Accept': '*/*'
      },
      cache: 'no-cache',
      credentials: 'same-origin',
      mode: 'cors'
    }).then(response => {
      console.log('收到教学设计流响应:', response)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response
    }).catch(error => {
      console.error('教学设计流请求失败:', error)
      throw error
    })
  }
} 