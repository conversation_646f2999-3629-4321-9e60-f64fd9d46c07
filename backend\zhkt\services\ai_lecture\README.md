# AI讲课服务模块结构

## 📁 目录结构
```
backend/zhkt/services/ai_lecture/
├── __init__.py                     # 模块初始化
├── orchestrator.py                 # 🎼 业务流程编排器
├── document_processor.py           # 📄 文档处理服务
├── outline_generator.py            # 📋 大纲生成服务  
├── content_creator.py              # 🎨 内容创建服务
├── speech_synthesizer.py           # 🎤 语音合成服务
├── subtitle_analyzer.py            # 🎬 字幕分析服务
├── file_manager.py                 # 📁 文件管理服务
└── exceptions.py                   # ❌ 自定义异常类
```

## 🎯 重构原则

### 1. 单一职责原则
- 每个服务类只负责一个具体的业务领域
- 避免功能交叉和耦合

### 2. 命名优化
- 类名：使用动词+名词的组合，更清晰表达功能
- 方法名：使用动词开头，表明具体操作
- 变量名：使用有意义的描述性名称

### 3. 职责分离
- **Orchestrator**: 只负责流程编排和协调
- **各具体服务**: 只负责自己领域的业务逻辑
- **异常处理**: 统一的异常定义和处理

## 📊 服务职责划分

| 服务类 | 主要职责 | 核心方法 |
|--------|----------|----------|
| DocumentProcessor | 文档解析、格式转换 | process_to_pdf(), extract_content() |
| OutlineGenerator | 大纲生成、结构分析 | generate_chapter_outline(), validate_structure() |
| ContentCreator | 内容增强、HTML生成 | enhance_with_knowledge(), create_html_slides() |
| SpeechSynthesizer | 语音脚本、TTS合成 | generate_speech_script(), synthesize_audio() |
| SubtitleAnalyzer | 字幕转录、页面分割建议 | transcribe_audio_to_subtitles(), suggest_page_divisions_with_timestamps() |
| FileManager | 文件存储、路径管理 | save_file(), delete_files(), get_file_path() |

## 🔄 数据流设计

```
文档上传 → DocumentProcessor → OutlineGenerator → ContentCreator → SpeechSynthesizer
                                      ↓                    ↓               ↓
                                FileManager ←――――――――――――――――――――――――――――――
``` 