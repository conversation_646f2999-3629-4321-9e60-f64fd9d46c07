<template>
  <el-dialog
    :model-value="show"
    @update:model-value="$emit('update:show', $event)"
    width="50%"
    :show-close="true"
    @close="close"
  >
    <template #header>
      <div class="flex items-center">
        <el-icon class="mr-2 text-xl text-blue-500"><Edit /></el-icon>
        <span class="text-xl font-medium">编辑课程</span>
      </div>
    </template>

    <form @submit.prevent="handleSubmit" class="mt-4">
      <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-3">
        <!-- 课程名称 -->
        <div class="sm:col-span-3">
          <label for="name" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Reading /></el-icon>
            课程名称
          </label>
          <div class="mt-2">
            <el-input
              v-model="formData.name"
              placeholder="请输入课程名称"
              :prefix-icon="Reading"
              required
            />
          </div>
        </div>

        <!-- 所属学院 -->
        <div>
          <label for="college" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><School /></el-icon>
            所属学院
          </label>
          <div class="mt-2">
            <el-select
              v-model="formData.college"
              placeholder="选择学院"
              class="w-full"
              required
            >
              <el-option
                v-for="college in colleges"
                :key="college.id"
                :label="college.name"
                :value="college.name"
              />
            </el-select>
          </div>
        </div>

        <!-- 课程类型 -->
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Collection /></el-icon>
            课程类型
          </label>
          <div class="mt-2">
            <el-select
              v-model="formData.subject"
              placeholder="选择课程类型"
              class="w-full"
              required
            >
              <el-option label="数学类" value="math">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">calculate</span>
                  数学类
                </span>
              </el-option>
              <el-option label="编程类" value="programming">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">code</span>
                  编程类
                </span>
              </el-option>
              <el-option label="物理类" value="physics">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">science</span>
                  物理类
                </span>
              </el-option>
              <el-option label="化学类" value="chemistry">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">biotech</span>
                  化学类
                </span>
              </el-option>
              <el-option label="数据库类" value="database">
                <span class="flex items-center">
                  <span class="material-symbols-outlined mr-1">database</span>
                  数据库类
                </span>
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 授课教师 -->
        <div>
          <label for="teacher" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><User /></el-icon>
            授课教师
          </label>
          <div class="mt-2">
            <el-input
              v-model="formData.teacher"
              placeholder="请输入教师姓名"
              :prefix-icon="User"
              required
            />
          </div>
        </div>

        <!-- 开始日期 -->
        <div>
          <label for="startDate" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Calendar /></el-icon>
            开始日期
          </label>
          <div class="mt-2">
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              placeholder="选择开始日期"
              format="YYYY-MM-DD"
              class="w-full"
              required
            />
          </div>
        </div>

        <!-- 课程时长 -->
        <div>
          <label for="duration" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Timer /></el-icon>
            课程时长
          </label>
          <div class="mt-2">
            <el-input-number
              v-model="formData.duration"
              :min="1"
              :max="52"
              class="w-full"
              required
            />
          </div>
        </div>

        <!-- 班级数量 -->
        <div>
          <label for="classCount" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Avatar /></el-icon>
            班级数量
          </label>
          <div class="mt-2">
            <el-input-number
              v-model="formData.classCount"
              :min="1"
              class="w-full"
              required
            />
          </div>
        </div>

        <!-- 课程状态 -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 flex items-center">
            <el-icon class="mr-1 text-blue-500"><Flag /></el-icon>
            课程状态
          </label>
          <div class="mt-2">
            <el-select
              v-model="formData.status"
              placeholder="选择课程状态"
              class="w-full"
              required
            >
              <el-option label="计划中" value="planned">
                <span class="flex items-center">
                  <el-icon class="mr-1"><Clock /></el-icon>
                  计划中
                </span>
              </el-option>
              <el-option label="进行中" value="active">
                <span class="flex items-center">
                  <el-icon class="mr-1"><VideoPlay /></el-icon>
                  进行中
                </span>
              </el-option>
              <el-option label="已结束" value="ended">
                <span class="flex items-center">
                  <el-icon class="mr-1"><CircleCheck /></el-icon>
                  已结束
                </span>
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </form>

    <template #footer>
      <div class="flex justify-end gap-2">
        <el-button @click="close">
          <el-icon class="mr-1"><Close /></el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit">
          <el-icon class="mr-1"><Check /></el-icon>
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import {
  Edit,
  Reading,
  School,
  Collection,
  User,
  Calendar,
  Timer,
  Avatar,
  Flag,
  Clock,
  VideoPlay,
  CircleCheck,
  Check,
  Close
} from '@element-plus/icons-vue'

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  course: {
    type: Object,
    required: true
  },
  colleges: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['close', 'submit', 'update:show'])

const formData = ref({
  name: '',
  college: '',
  subject: '',
  teacher: '',
  startDate: '',
  duration: 16,
  classCount: 1,
  status: 'planned'
})

// 监听课程数据变化，更新表单
watch(() => props.course, (newCourse) => {
  if (newCourse) {
    formData.value = { ...newCourse }
  }
}, { immediate: true })

const handleSubmit = () => {
  emit('submit', { ...formData.value })
}

const close = () => {
  emit('close')
}
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200');

.el-input-number .el-input__wrapper {
  padding-left: 11px;
  padding-right: 11px;
}

.material-symbols-outlined {
  font-size: 20px;
  line-height: 1;
  vertical-align: middle;
}

.el-icon {
  vertical-align: middle;
}

.el-button .el-icon {
  margin-right: 4px;
}

@media screen and (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
  }
}
</style> 