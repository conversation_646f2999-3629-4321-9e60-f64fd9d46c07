<template>
  <TeacherLayout
    :userName="teacherStore.teacherData.name"
    :userAvatar="teacherStore.teacherData.avatar"
    pageTitle="课程管理"
    activePage="course-management"
  >
    <div class="space-y-6">
      <!-- 功能区 -->
      <div class="flex flex-wrap justify-between items-center gap-4 mb-2">
        <div class="flex gap-3">
          <button 
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2"
            @click="showCreateNewCourseModal"
          >
            <i class="fas fa-plus text-sm"></i>
            创建新课程
          </button>
   
        </div>
        <div class="flex gap-3">
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery"
              placeholder="搜索课程..." 
              class="border border-gray-300 rounded-md pl-9 pr-4 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <span class="material-icons absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">search</span>
          </div>
          <div class="relative">
            <button 
              @click="showFilters = !showFilters"
              class="border border-gray-300 rounded-md px-4 py-2 flex items-center gap-2 hover:bg-gray-50"
            >
            <span class="material-icons text-gray-600">filter_list</span>
            筛选
            </button>
            <div v-if="showFilters" class="absolute right-0 top-full mt-2 bg-white shadow-lg rounded-md p-4 w-64 z-10">
              <!-- 筛选选项将在这里 -->
              <div class="space-y-3">
                <div class="font-medium">专业</div>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="major in majorOptions" 
                    :key="major.id"
                    class="px-3 py-1 rounded-full text-sm cursor-pointer"
                    :class="selectedFilters.majors.includes(major.id) ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'"
                    @click="toggleFilter('majors', major.id)"
                  >
                    {{ major.name }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类筛选区 -->
      <div class="flex border-b border-gray-200">
        <button 
          v-for="tab in tabs" 
          :key="tab.value"
          @click="currentTab = tab.value"
          class="py-3 px-6 font-medium relative"
          :class="currentTab === tab.value ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'"
        >
          {{ tab.label }}
          <span 
            v-if="currentTab === tab.value" 
            class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"
          ></span>
        </button>
      </div>

      <!-- 课程列表 -->
      <div v-loading="courseLoading" 
        element-loading-text="正在加载课程数据..." 
        element-loading-background="rgba(255, 255, 255, 0.9)"
        v-if="courseLoading" class="flex flex-col items-center justify-center py-16">
        <div class="w-40 h-40 mb-4 flex items-center justify-center bg-gray-100 rounded-full">
        </div>
      </div>
      
      <div v-else-if="filteredCourses.length === 0" class="flex flex-col items-center justify-center py-16">
        <div class="w-40 h-40 mb-4 flex items-center justify-center bg-gray-100 rounded-full">
          <i class="fas fa-folder-open text-6xl text-gray-400"></i>
        </div>
        <p class="text-gray-500">暂无课程，请点击"创建新课程"按钮开始</p>
      </div>
      
      <!-- 列表视图 (表格样式) -->
      <div v-else class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                课程信息
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                专业
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学生人数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                评分
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                进度
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建日期
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建人
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="course in filteredCourses" :key="course.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10 cursor-pointer" @click="openVideoPlayer(course)">
                    <img 
                      class="h-10 w-10 rounded-md object-cover" 
                      :src="course.coverImage" 
                      :alt="course.name"
                      @error="handleImageError"
                    />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-blue-600 cursor-pointer" @click="openCourse(course)">
                      <span class="hover:underline">{{ course.name }}</span>
                      <span v-if="course.code" class="text-xs text-gray-500 ml-2">({{ course.code }})</span>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      <span class="font-medium">{{ course.teacher }}</span> | 
                      <span class="font-medium">{{ course.college }}</span>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ course.major }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ course.studentCount }}/{{ course.planCount }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="course.average_rating" class="text-sm text-gray-900">
                  <el-rate
                    v-model="course.average_rating"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value}"
                  />
                  <div class="mt-1 text-gray-500">({{ course.rating_count }}人评价)</div>
                </div>
                <div v-else class="text-sm text-gray-400">暂无评分</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="w-36">
                  <div class="h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      class="h-full bg-blue-500 rounded-full"
                      :style="{ width: `${course.progress}%` }"
                    ></div>
                  </div>
                  <div class="flex justify-between mt-1 text-xs text-gray-500">
                    <span>{{ course.currentWeek }}/{{ course.totalWeeks }}周</span>
                    <span>{{ course.progress }}%</span>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDate(course.createdAt || course.startDate) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ course.creator || course.teacher }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-md"
                  :class="{
                    'bg-green-100 text-green-800': course.status === 'published',
                    'bg-red-100 text-red-800': course.status === 'archived'
                  }"
                >
                  {{ getStatusLabel(course.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <template v-if="course.status === 'published'">
                    <button @click="editCourse(course)" class="text-blue-600 hover:text-blue-900">编辑</button>
                    <button @click="showCourseAnalysis(course)" class="text-green-600 hover:text-green-900">分析</button>
                    <button @click="confirmChangeStatus(course.id, 'archived')" class="text-red-600 hover:text-red-900">下架</button>
                  </template>
                  
                  <template v-else-if="course.status === 'archived'">
                    <button @click="editCourse(course)" class="text-blue-600 hover:text-blue-900">编辑</button>
                    <button @click="confirmChangeStatus(course.id, 'published')" class="text-green-600 hover:text-green-900">上架</button>
                    <button @click="deleteCourse(course.id)" class="text-red-600 hover:text-red-900">删除</button>
                  </template>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- 创建新课程模态框 -->
    <el-config-provider :locale="zhCN">
      <el-dialog
        v-model="showCreateModal"
        :title="isEditMode ? '编辑课程' : '创建新课程'"
        width="85%"
        :before-close="closeCreateModal"
        class="course-create-dialog"
        top="2vh"
        :close-on-click-modal="false"
      >
        <template #header>
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="material-icons text-blue-600">{{ isEditMode ? 'edit' : 'add' }}</span>
            </div>
            <h2 class="text-xl font-bold text-gray-800">{{ isEditMode ? '编辑课程' : '创建新课程' }}</h2>
          </div>
        </template>
        
        <form @submit.prevent="createCourse" class="pt-0" enctype="multipart/form-data">
          <!-- 步骤导航栏 -->
          <div class="mb-5">
            <el-steps :active="activeStep" finish-status="success" process-status="process" simple class="compact-steps">
              <el-step title="课程信息" description="基本信息与封面"></el-step>
              <el-step title="课程概述" description="课程详细介绍"></el-step>
              <el-step title="章节管理" description="创建课程章节结构"></el-step>
              <el-step title="视频上传" description="为课时上传视频内容"></el-step>
            </el-steps>
          </div>

          <!-- 第一步：课程基本信息 -->
          <div v-if="activeStep === 1" class="step-content">
            <div class="course-info-container">
              <el-row :gutter="40">
                <el-col :span="16">
                  <!-- 左侧表单，占16/24宽度 -->
                  <div class="form-content">
                    <el-form-item label="课程名称" required class="mb-4">
                      <el-input 
                        v-model="newCourse.name" 
                        placeholder="例如：电子商务数据分析"
                        prefix-icon="book"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item label="课程编码" required class="mb-4">
                      <el-input 
                        v-model="newCourse.code" 
                        placeholder="例如：CS101"
                        prefix-icon="code"
                        style="width: 100%"
                      />
                    </el-form-item>

                    <el-form-item label="积&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;分" required class="mb-4">
                      <el-input-number
                        v-model="newCourse.pointsCost"
                        :min="0"
                        :max="999"
                        placeholder="请输入积分"
                        style="width: 100%"
                      />
                    </el-form-item>

                    <el-form-item label="计划人数" required class="mb-4">
                      <el-input-number
                        v-model="newCourse.planCount"
                        :min="0"
                        :max="999"
                        placeholder="请输入计划人数"
                        style="width: 100%"
                      />
                    </el-form-item>
                    
                    <el-form-item label="课程类型" required class="mb-4">
                      <el-select 
                        v-model="newCourse.courseType" 
                        placeholder="请选择课程类型"
                        style="width: 100%"
                      >
                        <el-option label="必修课" value="REQUIRED" />
                        <el-option label="选修课" value="ELECTIVE" />
                      </el-select>
                    </el-form-item>
                    
                    <el-row :gutter="20" class="mb-4">
                  <el-col :span="12">
                    <el-form-item label="老师" required class="my-4">
                      <el-input 
                        v-model="newCourse.teacher" 
                            disabled
                            placeholder="当前教师"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="学院" required class="my-4">
                      <el-select 
                        v-model="newCourse.college" 
                        placeholder="选择学院"
                        style="width: 100%"
                        filterable
                      >
                        <el-option
                          v-for="college in collegeOptions"
                              :key="college.id"
                              :label="college.name"
                              :value="college.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!--
                <el-form-item label="备注" class="mb-4">
                  <el-input
                        v-model="newCourse.notes"
                    type="textarea"
                        :rows="3"
                        placeholder="可选：添加课程备注信息..."
                  />
                </el-form-item>
                -->
                    <el-form-item label="专业" required class="mb-4">
                      <el-radio-group v-model="majorType" class="mb-2">
                    <el-radio :label="'all'">全部</el-radio>
                    <el-radio :label="'custom'">自定义</el-radio>
                  </el-radio-group>
                  
                      <div v-if="majorType === 'custom'" class="mt-2">
                    <el-checkbox-group v-model="newCourse.major">
                          <div class="grid grid-cols-3 gap-y-2">
                        <el-checkbox 
                          v-for="(major, index) in filteredMajors" 
                          :key="major.id" 
                          :label="major.id"
                        >
                          {{ major.name }}
                        </el-checkbox>
                      </div>
                    </el-checkbox-group>
                  </div>
                </el-form-item>
                    
                    <el-form-item class="mb-0">
                      <el-card class="box-card p-3">
                        <el-checkbox v-model="newCourse.publishImmediately">
                          <span class="font-medium">创建后立即上架</span>
                          <p class="text-sm text-gray-500 mt-1">课程将立即对学生可见</p>
                        </el-checkbox>
                      </el-card>
                    </el-form-item>
              </div>
            </el-col>
            
                <el-col :span="8">
                  <!-- 右侧上传区，占8/24宽度 -->
                  <div class="cover-upload-container bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium mb-4">课程封面</h3>
                  <el-upload
                    class="avatar-uploader"
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="handleCoverImageUpload"
                  >
                      <div v-if="newCourse.coverImagePreview" class="cover-preview">
                        <img :src="newCourse.coverImagePreview" class="w-full h-full object-cover rounded-lg" />
                        <div class="cover-actions absolute bottom-2 right-2">
                          <el-button 
                            type="danger" 
                            circle 
                            size="small" 
                            @click.stop="removeCoverImage"
                            class="bg-white bg-opacity-80"
                          >
                            <span class="material-icons">delete</span>
                          </el-button>
                        </div>
                      </div>
                      <div v-else class="upload-placeholder flex flex-col items-center justify-center p-10 border-2 border-dashed border-gray-300 rounded-lg bg-white cursor-pointer h-64">
                        <span class="material-icons text-6xl text-blue-400 mb-3">cloud_upload</span>
                        <p class="text-base text-gray-500">点击上传课程封面图片</p>
                        <p class="text-sm text-gray-400 mt-2">推荐尺寸: 800x400px, 最大2MB</p>
                    </div>
                  </el-upload>
                    
                    <div class="cover-tips mt-6 bg-blue-50 p-4 rounded-lg">
                      <h4 class="text-base font-medium text-blue-700 flex items-center mb-2">
                        <span class="material-icons mr-2 text-blue-600">info</span>
                        封面图片建议
                      </h4>
                      <ul class="text-sm text-blue-700 list-disc list-inside space-y-2">
                        <li>选择与课程内容相关的高清图片</li>
                        <li>保持图片比例为2:1以获得最佳显示效果</li>
                        <li>避免使用文字过多的图片</li>
                        <li>确保图片清晰且内容易于识别</li>
                      </ul>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          
          <!-- 第二步：课程概述 -->
          <div v-if="activeStep === 2" class="step-content">
            <div class="course-overview-container pb-4">
              <CourseOverview
                :course-id="editingCourseId"
                :overview="newCourse.overview"
                :can-edit="true"
                @update:overview="handleOverviewUpdate"
              />
            </div>
          </div>
          
          <!-- 第三步：章节管理 -->
          <div v-if="activeStep === 3" class="step-content">
            <div class="chapters-container">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium">课程章节管理</h3>
                <el-button 
                  type="primary" 
                  @click="addChapter"
                  class="flex items-center add-button"
                  size="large"
                >
                  <span class="material-icons mr-1">add</span>
                  <span class="font-medium">添加章节</span>
                </el-button>
              </div>

              <!-- 章节列表 -->
              <div class="chapter-list space-y-6">
                <el-card v-for="(chapter, chapterIndex) in newCourse.chapters" :key="'chapter-' + chapterIndex" class="chapter-card">
                  <template #header>
                    <div class="chapter-header flex items-center justify-between">
                      <div class="flex items-center space-x-4">
                        <div class="chapter-index w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center font-medium text-lg">
                          {{ chapterIndex + 1 }}
                        </div>
                        <el-input 
                          v-model="chapter.title" 
                          placeholder="请输入章节标题"
                          class="chapter-title-input"
                          style="width: 300px;"
                        />
                      </div>
                      <div class="chapter-actions flex space-x-3">
                        <el-button 
                          type="primary" 
                          @click="addLesson(chapter)"
                          class="flex items-center add-lesson-btn"
                        >
                          <span class="material-icons mr-1">video_library</span>
                          <span class="font-medium">添加课时</span>
                        </el-button>
                        <el-button 
                          @click="moveChapter(chapterIndex, -1)"
                          :disabled="chapterIndex === 0"
                          class="flex items-center"
                        >
                          <span class="material-icons text-sm">arrow_upward</span>
                        </el-button>
                        <el-button 
                          @click="moveChapter(chapterIndex, 1)"
                          :disabled="chapterIndex === newCourse.chapters.length - 1"
                          class="flex items-center"
                        >
                          <span class="material-icons text-sm">arrow_downward</span>
                        </el-button>
                        <el-button 
                          type="danger"
                          @click="removeChapter(chapterIndex)"
                          class="flex items-center"
                        >
                          <span class="material-icons text-sm">delete</span>
                        </el-button>
                      </div>
                    </div>
                  </template>
                  
                  <!-- 课时列表 -->
                  <div class="lessons-list space-y-3 mt-2">
                    <div v-if="chapter.lessons.length === 0" class="text-gray-400 text-center py-6 bg-gray-50 rounded-lg">
                      <span class="material-icons text-4xl text-gray-300 mb-2">video_library</span>
                      <p>暂无课时，请点击"添加课时"按钮</p>
                    </div>
                    <el-card 
                      v-for="(lesson, lessonIndex) in chapter.lessons" 
                      :key="'lesson-' + lessonIndex"
                      class="lesson-item border border-gray-200 rounded-lg shadow-sm"
                      shadow="hover"
                    >
                      <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 flex-1">
                          <div class="lesson-index w-8 h-8 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center text-sm font-medium">
                            {{ lessonIndex + 1 }}
                          </div>
                          <el-input 
                            v-model="lesson.title" 
                            placeholder="请输入课时标题"
                            class="lesson-title-input"
                            style="max-width: 400px;"
                          />
                          <div class="video-status flex items-center ml-4">
                            <el-tag
                              :type="lesson.videoFile ? 'success' : 'warning'"
                              size="small"
                              effect="light"
                              class="flex items-center"
                            >
                              <span class="material-icons text-sm mr-1">{{ lesson.videoFile ? 'check_circle' : 'warning' }}</span>
                              {{ lesson.videoFile ? '已上传视频' : '未上传视频' }}
                            </el-tag>
                          </div>
                        </div>
                        <div class="lesson-actions flex space-x-2">
                          <el-tooltip content="上传视频将在第三步进行" placement="top" effect="light" v-if="!lesson.videoFile">
                            <span></span>
                          </el-tooltip>
                          <el-button 
                            size="small" 
                            @click="moveLesson(chapter, lessonIndex, -1)"
                            :disabled="lessonIndex === 0"
                            class="flex items-center"
                          >
                            <span class="material-icons text-sm">arrow_upward</span>
                          </el-button>
                          <el-button 
                            size="small" 
                            @click="moveLesson(chapter, lessonIndex, 1)"
                            :disabled="lessonIndex === chapter.lessons.length - 1"
                            class="flex items-center"
                          >
                            <span class="material-icons text-sm">arrow_downward</span>
                          </el-button>
                          <el-button 
                            size="small" 
                            type="danger"
                            @click="removeLesson(chapter, lessonIndex)"
                            class="flex items-center"
                          >
                            <span class="material-icons text-sm">delete</span>
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </div>
                </el-card>
                
                <div v-if="newCourse.chapters.length === 0" class="empty-chapter-list bg-gray-50 p-12 rounded-lg text-center">
                  <span class="material-icons text-6xl text-gray-300 mb-4">auto_stories</span>
                  <p class="text-gray-500 text-lg mb-6">暂无章节，请点击"添加章节"按钮开始创建课程内容</p>
                  <el-button type="primary" @click="addChapter" class="add-button" size="large">
                    <span class="material-icons mr-1">add</span>
                    <span class="font-medium">添加章节</span>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 第四步：视频上传 -->
          <div v-if="activeStep === 4" class="step-content">
            <div class="video-upload-container">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium">课时视频上传管理</h3>
              </div>
              
              <el-tabs v-if="newCourse.chapters.length > 0" tab-position="left" class="video-tabs" style="min-height: 400px;">
                <el-tab-pane 
                  v-for="(chapter, chapterIndex) in newCourse.chapters" 
                  :key="'tab-' + chapterIndex"
                  :label="chapter.title || `第${chapterIndex + 1}章`"
                >
                  <div v-if="chapter.lessons.length === 0" class="text-center py-12 bg-gray-50 rounded-lg">
                    <span class="material-icons text-5xl text-gray-300 mb-3">video_library</span>
                    <p class="text-gray-500 mb-4">该章节暂无课时</p>
                    <el-button @click="activeStep = 3">返回添加课时</el-button>
                  </div>
                  
                  <div v-else class="lesson-video-list space-y-6">
                    <el-card 
                      v-for="(lesson, lessonIndex) in chapter.lessons" 
                      :key="'video-' + lessonIndex"
                      class="video-card"
                      shadow="hover"
                    >
                      <div class="flex items-start justify-between">
                        <div class="flex items-start space-x-4 flex-1">
                          <div class="lesson-index w-8 h-8 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center text-sm font-medium">
                            {{ lessonIndex + 1 }}
                          </div>
                          <div class="flex-1">
                            <h4 class="text-lg font-medium mb-2">{{ lesson.title || `课时 ${lessonIndex + 1}` }}</h4>
                            
                            <div v-if="lesson.videoFile" class="video-uploaded">
                              <div class="flex items-center mb-3">
                                <span class="material-icons text-green-500 mr-2">check_circle</span>
                                <span class="text-green-600 font-medium">视频已上传</span>
                              </div>
                              
                              <div class="flex items-start gap-4">
                                <div class="video-preview bg-gray-900 rounded-lg overflow-hidden w-64 h-36 relative cursor-pointer" @click="previewVideo(lesson)">
                                  <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="material-icons text-white text-6xl hover:scale-110 transition-transform">play_circle</span>
                                  </div>
                                  <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white p-2 text-sm flex justify-between">
                                    <span>{{ lesson.videoFile.name }}</span>
                                  </div>
                                </div>
                                
                                <div class="mt-0">
                                  <div class="text-sm text-gray-600 mb-3 max-w-md">
                                    <p><span class="font-medium">文件名:</span> {{ lesson.videoFile.name }}</p>
                                    <!--<p><span class="font-medium">大小:</span> {{ formatFileSize(lesson.videoFile.size) }}</p>-->
                                    <p><span class="font-medium">格式:</span> {{ lesson.videoFile.type || '视频' }}</p>
                                  </div>
                                  <div class="flex space-x-3">
                                    <el-button 
                                      size="small"
                                      @click="previewVideo(lesson)"
                                      class="flex items-center"
                                      type="primary"
                                    >
                                      <span class="material-icons text-sm mr-1">play_arrow</span>
                                      播放
                                    </el-button>
                                    <!--
                                    <el-button 
                                      size="small"
                                      @click="showUploadLessonVideo(chapter, lessonIndex)"
                                      class="flex items-center"
                                    >
                                      <span class="material-icons text-sm mr-1">refresh</span>
                                      重新上传
                                    </el-button>
                                    -->
                                    <el-button 
                                      size="small"
                                      type="danger"
                                      plain
                                      @click="removeLessonVideo(chapter, lessonIndex)"
                                      class="flex items-center"
                                    >
                                      <span class="material-icons text-sm mr-1">delete</span>
                                      删除视频
                                    </el-button>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <div v-else class="video-not-uploaded">
                              <div class="flex items-center mb-3">
                                <span class="material-icons text-orange-500 mr-2">warning</span>
                                <span class="text-orange-600 font-medium">尚未上传视频</span>
                              </div>
                              
                              <div class="flex justify-end">
                                <el-button 
                                  type="primary"
                                  @click="showUploadLessonVideo(chapter, lessonIndex)"
                                  class="flex items-center justify-center mx-auto"
                                >
                                  <span class="material-icons text-sm mr-1">cloud_upload</span>
                                  上传视频
                                </el-button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </div>
                </el-tab-pane>
              </el-tabs>
              
              <div v-else class="empty-chapter-list bg-gray-50 p-12 rounded-lg text-center">
                <span class="material-icons text-6xl text-gray-300 mb-4">video_library</span>
                <p class="text-gray-500 text-lg mb-6">尚未创建任何章节与课时</p>
                <el-button type="primary" @click="activeStep = 3">创建章节与课时</el-button>
              </div>
            </div>
          </div>
          
          <!-- 课程视频上传预览对话框 -->
          <el-dialog
            v-model="showVideoUploadDialog"
            title="上传课时视频"
            width="550px"
            append-to-body
            destroy-on-close
          >
            <div class="video-upload-dialog p-4">
              <div class="text-center mb-6">
                <h3 class="text-lg font-medium mb-2">
                  {{ currentChapter?.title || '章节' }} - 
                  {{ currentLesson?.title || '课时' }}
                </h3>
                <p class="text-gray-600">请选择要上传的视频文件</p>
              </div>
              
                  <div class="video-uploader">
                    <el-upload
                      action="#"
                      :auto-upload="false"
                      :on-change="handleVideoFileSelect"
                      :show-file-list="false"
                      :limit="1"
                      class="course-video-upload"
                      :accept="uploadProps.accept"
                      drag
                    >
                      <div v-if="!currentVideoPreview" class="upload-placeholder p-12">
                        <el-icon class="el-icon--upload"><span class="material-icons text-5xl text-blue-400 mb-3">cloud_upload</span></el-icon>
                        <div class="el-upload__text">
                          <span class="text-base">拖拽文件至此区域或<em>点击上传</em></span>
                          <p class="text-sm text-gray-400 mt-2">支持mp4, mov格式, 最大1G</p>
                        </div>
                      </div>
                      <div v-else class="video-preview p-6 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                          <div class="video-thumbnail bg-black w-16 h-16 rounded flex items-center justify-center mr-4">
                            <span class="material-icons text-white text-2xl">play_circle</span>
                          </div>
                          <div class="video-info flex-1">
                            <div class="video-name text-base font-medium text-gray-800 mb-1 truncate">{{ currentVideo?.name }}</div>
                            <div class="video-size text-sm text-gray-500">{{ formatFileSize(currentVideo?.size) }}</div>
                          </div>
                          <button 
                            type="button"
                            @click.stop="removeCurrentVideo"
                            class="video-remove-btn p-2 rounded-full hover:bg-red-100"
                          >
                            <span class="material-icons text-red-500">delete</span>
                          </button>
                        </div>
                      </div>
                    </el-upload>
                  </div>
                  
                  <div class="flex justify-end gap-3 mt-8">
                    <el-button @click="showVideoUploadDialog = false">取消</el-button>
                    <el-button 
                      type="primary" 
                      :disabled="!currentVideo || isUploading"
                      :loading="isUploading"
                      @click="handleLessonVideoUpload"
                    >
                      {{ isUploading ? '上传中...' : '确认上传' }}
                    </el-button>
                  </div>
                </div>
              </el-dialog>
              
              <!-- 视频预览对话框 -->
              <el-dialog
                v-model="showVideoPreviewDialog"
                title="视频预览"
                width="800px"
                append-to-body
                destroy-on-close
                class="video-preview-dialog"
              >
                <div class="video-preview-container p-2 bg-black rounded-lg">
                  <video 
                    v-if="previewingLesson && previewingLesson.videoFile" 
                    ref="videoPlayer"
                    controls
                    class="w-full aspect-video"
                  >
                    <source :src="getVideoUrl(previewingLesson)" type="video/mp4">
                    您的浏览器不支持视频播放
                  </video>
                  <div v-else class="flex items-center justify-center bg-gray-800 text-white p-10 rounded">
                    <span class="material-icons text-3xl mr-2">error</span>
                    <span>无法播放视频</span>
                  </div>
                </div>
                <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h3 class="font-medium text-lg mb-2">{{ previewingLesson?.title || '课时视频' }}</h3>
                  <p class="text-gray-500 text-sm">
                    <span>{{ formatFileSize(previewingLesson?.videoFile?.size || 0) }}</span>
                    <span class="mx-2">|</span>
                    <span>{{ previewingLesson?.videoFile?.type || '视频文件' }}</span>
                  </p>
                </div>
              </el-dialog>
              
              <!-- 步骤导航按钮 -->
              <div class="mt-8 flex justify-end border-t border-gray-200 pt-6 gap-3">
                <el-button @click="closeCreateModal">
                  <span class="material-icons mr-1">close</span>
                  取消
                </el-button>
                
                <el-button v-if="activeStep > 1" @click="activeStep--">
                  <span class="material-icons mr-1">arrow_back</span>
                  上一步
                </el-button>
                
                <el-button v-if="activeStep < 4" type="primary" @click="goToNextStep">
                  下一步
                  <span class="material-icons ml-1">arrow_forward</span>
                </el-button>
                
                <el-button v-if="activeStep === 4" type="success" native-type="submit">
                  <span class="material-icons mr-1">check</span>
                  完成创建
                </el-button>
              </div>
            </form>
          </el-dialog>
        </el-config-provider>

        <!-- 视频播放器组件 -->
        <CourseVideoPlayer 
          v-model="showVideoModal" 
          :course="currentCourse"
          @close="currentCourse = null"
        />

        <!-- 课程分析模态框 -->
        <el-dialog
          v-model="showAnalysisModal"
          title="课程数据分析"
          width="85%"
          class="analysis-dialog"
          top="5vh"
          :destroy-on-close="true"
        >
          <template #header>
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <span class="material-icons text-green-600">insights</span>
              </div>
              <div>
                <h2 class="text-xl font-bold text-gray-800">《{{ analyzingCourse?.name || '课程' }}》数据分析</h2>
                <p class="text-sm text-gray-500">最近更新: {{ formatDate(new Date()) }}</p>
              </div>
            </div>
          </template>

          <div class="analysis-content">
            <!-- 概览卡片 -->
            <div class="grid grid-cols-4 gap-4 mb-6">
              <div v-for="(stat, index) in overviewStats" :key="index" 
                   class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm flex items-center gap-3">
                <div class="w-12 h-12 rounded-full flex items-center justify-center" :class="stat.bgColor">
                  <span class="material-icons text-xl" :class="stat.iconColor">{{ stat.icon }}</span>
                </div>
                <div>
                  <div class="text-2xl font-bold">{{ stat.value }}</div>
                  <div class="text-sm text-gray-500">{{ stat.label }}</div>
                </div>
              </div>
            </div>

            <!-- 学生参与情况 -->
            <div class="grid grid-cols-2 gap-6 mb-6">
              <!-- 学生学习状态 -->
              <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <h3 class="text-lg font-medium mb-4 flex items-center">
                  <span class="material-icons text-blue-500 mr-2">people</span>
                  学生学习状态分布
                </h3>
                <div class="h-80">
                  <canvas ref="studentStatusChart"></canvas>
                </div>
              </div>

              <!-- 课程参与趋势 -->
              <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <h3 class="text-lg font-medium mb-4 flex items-center">
                  <span class="material-icons text-purple-500 mr-2">trending_up</span>
                  课程参与趋势 (近30天)
                </h3>
                <div class="h-80">
                  <canvas ref="engagementTrendChart"></canvas>
                </div>
              </div>
            </div>

            <!-- 章节完成情况 -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-6">
              <h3 class="text-lg font-medium mb-4 flex items-center">
                <span class="material-icons text-orange-500 mr-2">menu_book</span>
                章节完成情况
              </h3>
              <div class="h-64">
                <canvas ref="chapterCompletionChart"></canvas>
              </div>
            </div>

            <!-- 互动数据和学习时长 -->
            <div class="grid grid-cols-2 gap-6">
              <!-- 互动数据 -->
              <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <h3 class="text-lg font-medium mb-4 flex items-center">
                  <span class="material-icons text-red-500 mr-2">forum</span>
                  互动数据统计
                </h3>
                <div class="h-64">
                  <canvas ref="interactionChart"></canvas>
                </div>
              </div>
              
              <!-- 学习时长分布 -->
              <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <h3 class="text-lg font-medium mb-4 flex items-center">
                  <span class="material-icons text-green-500 mr-2">schedule</span>
                  学习时长分布（小时）
                </h3>
                <div class="h-64">
                  <canvas ref="studyTimeChart"></canvas>
                </div>
              </div>
            </div>
          </div>
        </el-dialog>
    </TeacherLayout>
  </template>

  <script setup>
  import { ref, computed, watch, nextTick, onMounted } from 'vue'
  import { useTeacherStore } from '@/stores/teacher'
  import TeacherLayout from '@/components/layout/TeacherLayout.vue'
  import teacherAvatar from '@/assets/images/avatars/teacher1.png'
  import CourseVideoPlayer from './CourseVideoPlayer.vue'
  import CourseOverview from '@/components/course/CourseOverview.vue'
  import Chart from 'chart.js/auto'
  import { organizationApi } from '@/api/organization'
  import { usersApi } from '@/api/users'
  import { courseApi } from '@/api/course'
  import { resourceApi } from '@/api/resource'
  import { lessonApi } from '@/api/lesson'
  // Element Plus imports
  import { 
    ElDialog, 
    ElSteps, 
    ElStep, 
    ElRow, 
    ElCol, 
    ElFormItem, 
    ElInput, 
    ElSelect, 
    ElOption,
    ElDatePicker, 
    ElCheckboxGroup, 
    ElCheckbox, 
    ElUpload, 
    ElButton, 
    ElCard, 
    ElRadioGroup, 
    ElRadio, 
    ElDivider,
    ElConfigProvider,
    ElTooltip,
    ElTabs,
    ElTabPane,
    ElSwitch,
    ElInputNumber,
    ElTag,
    ElMessage,
    ElMessageBox,
    ElRate,
  } from 'element-plus'
  import zhCN from 'element-plus/es/locale/lang/zh-cn'

  // 引入学生数据存储
  const teacherStore = useTeacherStore()

  // 课程状态选项卡
  const tabs = [
    { label: '全部课程', value: 'all' },
    { label: '已上架', value: 'published' },
    { label: '已下架', value: 'archived' }
  ]
  const currentTab = ref('all')

  // 搜索和筛选
  const searchQuery = ref('')
  const showFilters = ref(false)
  const selectedFilters = ref({
    majors: []
  })

  // 课程创建 - 设置表单步骤
  const activeStep = ref(1)

  // 切换筛选器
  const toggleFilter = (category, value) => {
    if (selectedFilters.value[category].includes(value)) {
      selectedFilters.value[category] = selectedFilters.value[category].filter(item => item !== value)
    } else {
      selectedFilters.value[category].push(value)
    }
  }

  // 模拟课程数据
  const courses = ref([])
  const courseLoading = ref(false)

  // 筛选后的课程
  const filteredCourses = computed(() => {
    let result = courses.value

    // 按标签筛选
    if (currentTab.value !== 'all') {
      result = result.filter(course => course.status === currentTab.value)
    }

    // 按搜索关键词筛选
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(course => 
        course.name.toLowerCase().includes(query) || 
        course.term.toLowerCase().includes(query) ||
        course.major.toLowerCase().includes(query)
      )
    }

    // 按选中的筛选器筛选
    if (selectedFilters.value.majors.length > 0) {
      result = result.filter(course => {
        // 检查课程的专业是否包含任何选中的专业类别
        const courseMajors = course.major.split('、')
        return selectedFilters.value.majors.some(selectedMajor => 
          courseMajors.includes(selectedMajor)
        )
      })
    }
    return result
  })

  // 获取状态标签
  const getStatusLabel = (status) => {
    const statusMap = {
      'published': '已上架',
      'archived': '已下架'
    }
    return statusMap[status] || status
  }

  // 创建课程相关
  const showCreateModal = ref(false)
  const majorOptions = ref([])
  const majorType = ref('all') // 控制专业选择方式

  // 学院选项
  const collegeOptions = ref([])
  const filteredMajors = ref([]) // 用于存储当前学院下的专业

  // 新课程表单
  const newCourse = ref({
    name: '',
    code: '',
    courseType: 'REQUIRED',
    term: '',
    major: [],
    coverImage: null,
    coverImagePreview: null,
    coverImageName: '', // 添加文件名字段
    notes: '',
    publishImmediately: false,
    teacher: teacherStore.teacherData.name,
    teacherId: teacherStore.teacherData.id, // 使用当前登录教师的ID
    college: '',
    chapters: [],
    duration: 20,
    difficultyLevel: 'intermediate',
    planCount: 0,
    pointsCost: 0,
    overview: {}
  })

  // 当前编辑的视频信息
  const showVideoUploadDialog = ref(false)
  const currentChapter = ref(null)
  const currentLessonIndex = ref(-1)
  const currentLesson = ref(null)
  const currentVideo = ref(null)
  const currentVideoPreview = ref(null)
  const isUploading = ref(false)  // 添加上传状态控制

  // 章节与课时管理
  const addChapter = async () => {
    try {
      const chapterData = {
        course: newCourse.value.id,
        title: `第${newCourse.value.chapters.length + 1}章`,
        description: '',
        order: newCourse.value.chapters.length + 1
      }
      
      const response = await courseApi.createChapter(chapterData)
      if (response) {
        newCourse.value.chapters.push({
          id: response.id,
          title: response.title,
          description: response.description,
          order: response.order,
          lessons: []
        })
        ElMessage.success('章节创建成功')
      }
    } catch (error) {
      console.error('创建章节失败:', error)
      ElMessage.error('创建章节失败：' + (error.response?.data?.error || error.message))
    }
  }

  const removeChapter = async (index) => {
    try {
      const chapter = newCourse.value.chapters[index]
      if (confirm(`确定要删除 "${chapter.title}" 及其所有课时吗？`)) {
        await courseApi.deleteChapter(chapter.id)
        newCourse.value.chapters.splice(index, 1)
        ElMessage.success('章节删除成功')
      }
    } catch (error) {
      console.error('删除章节失败:', error)
      ElMessage.error('删除章节失败：' + (error.response?.data?.error || error.message))
    }
  }

  const moveChapter = async (index, direction) => {
    try {
      const newIndex = index + direction
      if (newIndex >= 0 && newIndex < newCourse.value.chapters.length) {
        const chapter = newCourse.value.chapters[index]
        const targetChapter = newCourse.value.chapters[newIndex]
        
        // 交换两个章节的顺序
        const tempOrder = chapter.order
        chapter.order = targetChapter.order
        targetChapter.order = tempOrder
        
        // 更新后端
        await courseApi.updateChapter(chapter.id, { order: chapter.order })
        await courseApi.updateChapter(targetChapter.id, { order: targetChapter.order })
        
        // 更新前端显示
        const temp = newCourse.value.chapters[index]
        newCourse.value.chapters[index] = newCourse.value.chapters[newIndex]
        newCourse.value.chapters[newIndex] = temp
      }
    } catch (error) {
      console.error('移动章节失败:', error)
      ElMessage.error('移动章节失败：' + (error.response?.data?.error || error.message))
    }
  }

  const addLesson = async (chapter) => {
    try {
      const lessonData = {
        chapter: chapter.id,  // 直接传递章节ID
        title: `课时 ${chapter.lessons.length + 1}`,
        description: '',
        order: chapter.lessons.length + 1
      }
      
      const response = await lessonApi.createLesson(lessonData)
      if (response) {
        chapter.lessons.push({
          id: response.id,
          title: response.title,
          description: response.description,
          order: response.order,
          video_url: response.video_url,
          duration: response.duration
        })
        ElMessage.success('课时创建成功')
      }
    } catch (error) {
      console.error('创建课时失败:', error)
      ElMessage.error('创建课时失败：' + (error.response?.data?.error || error.message))
    }
  }

  const removeLesson = async (chapter, index) => {
    try {
      const lesson = chapter.lessons[index]
      if (confirm(`确定要删除 "${lesson.title}" 吗？`)) {
        await lessonApi.deleteLesson(lesson.id)
        chapter.lessons.splice(index, 1)
        ElMessage.success('课时删除成功')
      }
    } catch (error) {
      console.error('删除课时失败:', error)
      ElMessage.error('删除课时失败：' + (error.response?.data?.error || error.message))
    }
  }

  const moveLesson = (chapter, index, direction) => {
    const newIndex = index + direction
    if (newIndex >= 0 && newIndex < chapter.lessons.length) {
      const temp = chapter.lessons[index]
      chapter.lessons[index] = chapter.lessons[newIndex]
      chapter.lessons[newIndex] = temp
    }
  }

  const showUploadLessonVideo = async (chapter, lessonIndex) => {
    currentChapter.value = chapter
    currentLessonIndex.value = lessonIndex
    currentLesson.value = chapter.lessons[lessonIndex]
    showVideoUploadDialog.value = true
  }

  const handleVideoFileSelect = (file) => {
    currentVideo.value = file.raw
    currentVideoPreview.value = URL.createObjectURL(file.raw)
  }

  const handleLessonVideoUpload = async () => {
    if (!currentVideo.value) {
      ElMessage.warning('请先选择视频文件')
      return
    }

    try {
      isUploading.value = true
      const response = await lessonApi.uploadLessonVideo(currentLesson.value.id, currentVideo.value)
      if (response) {
        currentLesson.value.videoFile = {
            name: response.title,
            size: 0,
            type: 'video/mp4'
        }
        currentLesson.value.video_url = response.url
        ElMessage.success('视频上传成功')
        showVideoUploadDialog.value = false
      }
    } catch (error) {
      console.error('上传视频失败:', error)
      ElMessage.error('上传视频失败：' + (error.response?.data?.error || error.message))
    } finally {
      isUploading.value = false
    }
  }

  const removeCurrentVideo = (e) => {
    e.stopPropagation()
    currentVideo.value = null
    currentVideoPreview.value = null
  }

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '未设置';
    
    let date;
    if (typeof dateString === 'string') {
      date = new Date(dateString);
    } else {
      date = dateString;
    }
    
    if (date instanceof Date && !isNaN(date)) {
      return date.toLocaleDateString('zh-CN');
    }
    return '未设置';
  }

  // 显示创建新课程的模态框
  const showCreateNewCourseModal = () => {
    activeStep.value = 1
    // 重置表单
    resetNewCourseForm()
    // 设置为创建模式
    isEditMode.value = false
    editingCourseId.value = null
    
    // 设置默认值 - 在表单重置后执行
    setTimeout(() => {
      newCourse.value.teacher = teacherStore.teacherData.name
      newCourse.value.teacherId = teacherStore.teacherData.id // 添加这一行
      newCourse.value.college = teacherStore.teacherData.department
      console.log('Default college set to:', newCourse.value.college)
      console.log('Default teacherId set to:', newCourse.value.teacherId)
    }, 10)
    
    // 显示模态框
    showCreateModal.value = true
  }

  // 重置新课程表单
  const resetNewCourseForm = () => {
    newCourse.value = {
      name: '',
      code: '',
      courseType: 'REQUIRED',
      major: [],
      coverImage: null,
      coverImagePreview: null,
      coverImageName: '', // 添加文件名字段
      notes: '',
      description: '',
      publishImmediately: false,
      teacher: '',
      college: '',
      chapters: [],
      duration: 20,
      difficultyLevel: 'intermediate',
      planCount: 50,
      pointsCost: 20,
      overview: {}
    }
    learningGoals.value = ['']
    majorType.value = 'all'
    isEditMode.value = false
    editingCourseId.value = null
  }

  // 处理封面图片上传
  const handleCoverImageUpload = async (file) => {
    if (file) {
      try {
        // 创建 FormData 对象
        const formData = new FormData()
        formData.append('file', file.raw || file)

        // 如果是编辑模式，上传到对应的课程
        if (isEditMode.value && editingCourseId.value) {
          const response = await courseApi.uploadCourseCover(editingCourseId.value, formData)
          console.log('上传封面图片成功:', response)
          newCourse.value.coverImage = response.url
          newCourse.value.coverImagePreview = response.url
        } else {
          // 如果是创建模式，先保存到临时预览
          newCourse.value.coverImage = file.raw || file
          const reader = new FileReader()
          reader.onload = (e) => {
            newCourse.value.coverImagePreview = e.target.result
          }
          reader.readAsDataURL(file.raw || file)
        }
      } catch (error) {
        console.error('上传封面图片失败:', error)
        ElMessage.error('上传封面图片失败：' + (error.response?.data?.error || error.message))
      }
    }
  }

  // 修改课程
  const editCourse = async (course) => {
    activeStep.value = 1
    resetNewCourseForm()
    try {
      // 加载课程详情
      const response = await courseApi.getCourseById(course.id)
      const courseData = response
      
      // 加载章节数据
      const chaptersResponse = await courseApi.getChaptersByCourse(course.id)
      const chapters = chaptersResponse.results
      
      // 加载每个章节的课时数据
      const chaptersWithLessons = await Promise.all(chapters.map(async (chapter) => {
        const lessons = await getLessonsByChapter(chapter.id)
        return {
          ...chapter,
          lessons
        }
      }))
      
      // 填充表单数据
      newCourse.value = {
        ...newCourse.value,
        id: courseData.id,
        name: courseData.name,
        code: courseData.code,
        courseType: courseData.course_type,
        term: courseData.term || '',
        major: courseData.major ? courseData.major.map(m => m.id) : [],
        coverImage: courseData.cover_image,
        coverImagePreview: courseData.cover_image,
        notes: courseData.notes || '',
        description: courseData.description || '',
        publishImmediately: courseData.status === 'published',
        teacher: courseData.teacher.title,
        teacherId: courseData.teacher.id,
        college: courseData.teacher.college,
        chapters: chaptersWithLessons,
        duration: courseData.duration || 20,
        difficultyLevel: courseData.difficulty_level || 'intermediate',
        planCount: courseData.plan_count || 0,
        pointsCost: courseData.pointsCost || 0,
        overview: courseData.overview || {}
      }

      console.log('课程数据',newCourse.value);
      
      // 设置为编辑模式
      isEditMode.value = true
      editingCourseId.value = course.id
      
      // 显示模态框
      showCreateModal.value = true
    } catch (error) {
      console.error('加载课程数据失败:', error)
      ElMessage.error('加载课程数据失败')
    }
  }

  // 标记是否处于编辑模式
  const isEditMode = ref(false)
  const editingCourseId = ref(null)

  // 监听学院选择变化
  watch(() => newCourse.value.college, (newCollegeId) => {
    if (majorType.value === 'custom') {
      filterMajorsByCollege(newCollegeId)
    } else {
      filteredMajors.value = majorOptions.value
    }
    // 清空已选择的专业
    newCourse.value.major = []
  })

  // 监听专业类型变化
  watch(() => majorType.value, (newType) => {
    if (newType === 'all') {
      // 选择全部时，显示所有专业
      filteredMajors.value = majorOptions.value
    } else {
      // 选择自定义时，根据学院筛选专业
      filterMajorsByCollege(newCourse.value.college)
    }
    // 清空已选择的专业
    newCourse.value.major = []
  })

  // 创建/更新课程
  const createCourse = async () => {
    try {
      // 验证表单
      if (!newCourse.value.name) {
        ElMessage.error('请输入课程名称')
        return
      }
      if (!newCourse.value.code) {
        ElMessage.error('请输入课程代码')
        return
      }
      if (!newCourse.value.teacherId) {
        ElMessage.error('请选择教师')
        return
      }
      if (!newCourse.value.coverImage) {
        ElMessage.error('请上传课程封面')
        return
      }

      // 创建FormData对象
      const formData = new FormData()
      formData.append('name', newCourse.value.name)
      formData.append('code', newCourse.value.code)
      formData.append('course_type', newCourse.value.courseType)
      formData.append('term', newCourse.value.term)
      formData.append('description', newCourse.value.description)
      formData.append('teacher_id', newCourse.value.teacherId)
      formData.append('plan_count', newCourse.value.planCount || 0)
      formData.append('pointsCost', newCourse.value.pointsCost || 0)
      
      // 处理专业选择
      let majorIds = []
      if (majorType.value === 'custom' && newCourse.value.major && newCourse.value.major.length > 0) {
        majorIds = newCourse.value.major.map(id => {
          const numId = parseInt(id)
          return isNaN(numId) ? 0 : numId
        }).filter(id => id > 0)
      }
      
      majorIds.forEach(id => {
        formData.append('major_ids', id)
      })

      let response
      if (isEditMode.value && editingCourseId.value) {
        // 编辑模式
        response = await courseApi.updateCourse(editingCourseId.value, formData)
        
        // 更新课程概述
        if (newCourse.value.overview) {
          await courseApi.updateCourseOverview(editingCourseId.value, {
            key_points: newCourse.value.overview.key_points,
            important_points: newCourse.value.overview.important_points,
            notes: newCourse.value.overview.notes
          })
        }
      } else {
        // 创建模式
        response = await courseApi.createCourse(formData)
        
        // 创建课程概述
        if (newCourse.value.overview) {
          await courseApi.updateCourseOverview(response.id, {
            key_points: newCourse.value.overview.key_points,
            important_points: newCourse.value.overview.important_points,
            notes: newCourse.value.overview.notes
          })
        }
      }

      if (response) {
        const courseId = response.id
        
        // 更新章节关联
        for (const chapter of newCourse.value.chapters) {
          try {
            if (chapter.id) {
              // 更新现有章节
              await courseApi.updateChapter(chapter.id, {
                course: courseId,
                title: chapter.title,
                order: chapter.order
              })
            } else {
              // 创建新章节
              const chapterResponse = await courseApi.createChapter({
                course: courseId,
                title: chapter.title,
                description: chapter.description,
                order: chapter.order
              })
              chapter.id = chapterResponse.id
            }
            
            // 更新课时关联
            for (const lesson of chapter.lessons) {
              if (lesson.id) {
                // 更新现有课时
                await lessonApi.updateLesson(lesson.id, {
                  chapter: chapter.id,
                  title: lesson.title,
                  description: lesson.description,
                  order: lesson.order
                })
              } else {
                // 创建新课时
                await lessonApi.createLesson({
                  chapter: chapter.id,
                  title: lesson.title,
                  description: lesson.description,
                  order: lesson.order
                })
              }
              if(isEditMode.value && editingCourseId.value){
                console.log('编辑模式不需要在前端更新课程id,已在上传视频后端自动关联');
              }else{
                // 根据章节id和课时id更新资源表的course_id字段
                const resource = await resourceApi.getResourceByChapterAndLesson({
                  "chapter_id":chapter.id, 
                  "lesson_id": lesson.id
                })
                if (resource) {
                  await resourceApi.partialUpdateResource(resource.id, {
                    course: courseId
                  })
                }
              }
            }
          } catch (error) {
            console.error('更新章节关联失败:', error)
            ElMessage.error(`更新章节"${chapter.title}"关联失败: ${error.message}`)
          }
        }
        
        ElMessage.success(isEditMode.value ? '课程更新成功' : '课程创建成功')
        activeStep.value = 1
        resetNewCourseForm()
        showCreateModal.value = false
        loadCourses()
      }
    } catch (error) {
      console.error(isEditMode.value ? '更新课程失败:' : '创建课程失败:', error)
      ElMessage.error((isEditMode.value ? '更新' : '创建') + '课程失败：' + (error.response?.data?.error || error.message))
    }
  }

  // 加载课程列表
  const loadCourses = async () => {
    courseLoading.value = true
    try {
      if(!teacherStore.teacherData.id) {
        await teacherStore.fetchCurrentTeacher()
      }

      const [coursesResponse, teachersResponse] = await Promise.all([
        courseApi.getCourses({ teacher_id: teacherStore.teacherData.id }),
        usersApi.getTeachers()
      ])
      
      // 创建教师和学院的映射
      const teachersMap = new Map(teachersResponse.results.map(teacher => [teacher.id, teacher]))
      const collegesMap = new Map(collegeOptions.value.map(college => [college.id, college]))
      
      // 确保数据格式正确
      courses.value = coursesResponse.results.map(course => {
        // 获取教师信息
        const teacher = teachersMap.get(course.teacher.id)
        const teacherName = teacher ? teacher.title : '未知教师'
        const creatorName = teacher ? teacher.title : '未知创建者'
        // 获取学院信息
        const college = teacher ? collegesMap.get(teacher.college) : null
        const collegeName = college ? college.name : '未知学院'
      
        // 获取专业名称列表
        const majorNames = course.major.map(major => major.name).join('、')
        
        return {
          id: course.id,
          name: course.name,
          code: course.code,
          courseType: course.course_type,
          term: course.term || '',
          startDate: course.start_date,
          endDate: course.end_date,
          createdAt: course.created_at,
          creator: creatorName,
          coverImage: course.cover_image || '/src/assets/images/courses/default-course.jpg',
          status: course.status,
          studentCount: course.student_count || 0,
          planCount: course.plan_count || 0,
          average_rating: course.average_rating,
          rating_count: course.rating_count,
          major: majorNames,
          currentWeek: course.current_week || 0,
          totalWeeks: course.total_weeks || 0,
          progress: course.progress || 0,
          teacher: teacherName,
          college: collegeName,
          description: course.description || '',
          hasVideo: course.has_video || false,
          chapters: course.chapters || [],
          pointsCost: course.pointsCost || 0
        }
      })
    } catch (error) {
      console.error('加载课程列表失败:', error)
      ElMessage.error('加载课程列表失败')
    } finally {
      courseLoading.value = false
    }
  }

  // 确认改变课程状态
  const confirmChangeStatus = async (courseId, newStatus) => {
    const course = courses.value.find(c => c.id === courseId)
    if (!course) return

    let message = ''
    let title = ''
    if (newStatus === 'published') {
      title = '上架课程'
      message = `确定要上架课程"${course.name}"吗？上架后课程将对符合条件的学生可见。`
    } else if (newStatus === 'archived') {
      title = '下架课程'
      message = `确定要下架课程"${course.name}"吗？下架后学生将无法看到此课程。`
    }

    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await changeStatus(courseId, newStatus)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('操作失败:', error)
        ElMessage.error('操作失败，请重试')
      }
    }
  }

  // 修改changeStatus函数
  const changeStatus = async (courseId, newStatus) => {
    try {
      const course = courses.value.find(c => c.id === courseId)
      if (!course) return

      // 使用PATCH方法只更新status字段
      await courseApi.partialUpdateCourse(courseId, { status: newStatus })
      
      // 更新本地数据
      course.status = newStatus
      ElMessage.success(newStatus === 'published' ? '课程已上架' : '课程已下架')
    } catch (error) {
      console.error('更新课程状态失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }

  // 修改deleteCourse函数
  const deleteCourse = async (courseId) => {
    const course = courses.value.find(c => c.id === courseId)
    if (!course) return

    try {
      await ElMessageBox.confirm(
        `确定要删除课程"${course.name}"吗？此操作无法撤销。`,
        '删除课程',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      await courseApi.deleteCourse(courseId)
      courses.value = courses.value.filter(c => c.id !== courseId)
      ElMessage.success('课程已删除')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除课程失败:', error)
        ElMessage.error('删除失败，请重试')
      }
    }
  }

  // 视频播放相关
  const showVideoModal = ref(false)
  const currentCourse = ref(null)

  // 打开视频播放器
  const openVideoPlayer = async (course) => {
    // 加载章节数据
    const chaptersResponse = await courseApi.getChaptersByCourse(course.id)
    const chapters = chaptersResponse.results
    // 加载每个章节的课时数据
    const chaptersWithLessons = await Promise.all(chapters.map(async (chapter) => {
        const lessons = await getLessonsByChapter(chapter.id)
        return {
          ...chapter,
          lessons
        }
    }))
    course.chapters = chaptersWithLessons
    course.hasVideo = true
    currentCourse.value = course
    showVideoModal.value = true
  }

  // 步骤导航
  const goToNextStep = () => {
    // 验证当前步骤的输入
    if (activeStep.value === 1) {
      // 验证第一步：课程基本信息
      if (!newCourse.value.name.trim()) {
        alert('请输入课程名称')
        return
      }
      if (!newCourse.value.teacher.trim()) {
        alert('请输入老师姓名')
        return
      }
      if (!newCourse.value.college) {
        alert('请选择学院')
        return
      }
    } else if (activeStep.value === 2) {
      // 验证第二步：课程概述 - 移除描述长度检查，可以为空
      // 不再强制检查描述长度
    } else if (activeStep.value === 3) {
      // 验证第三步：章节结构
      if (newCourse.value.chapters.length === 0) {
        alert('请至少添加一个章节')
        return
      }
      
      // 检查是否有章节缺少标题
      const untitledChapter = newCourse.value.chapters.find(chapter => !chapter.title.trim())
      if (untitledChapter) {
        alert('所有章节必须有标题')
        return
      }
      
      // 检查是否有章节缺少课时
      const emptyChapter = newCourse.value.chapters.find(chapter => chapter.lessons.length === 0)
      if (emptyChapter) {
        alert(`章节 "${emptyChapter.title}" 没有任何课时，请添加课时`)
        return
      }
      
      // 检查是否有课时缺少标题
      for (const chapter of newCourse.value.chapters) {
        const untitledLesson = chapter.lessons.find(lesson => !lesson.title.trim())
        if (untitledLesson) {
          alert(`章节 "${chapter.title}" 中有课时缺少标题`)
          return
        }
      }
    }
    
    // 移动到下一步
    activeStep.value++
  }

  // 移除封面图片
  const removeCoverImage = () => {
    newCourse.value.coverImage = null
    newCourse.value.coverImagePreview = null
  }

  // 移除课时视频
  const removeLessonVideo = async (chapter, lessonIndex) => {
    try {
      if (confirm('确定要删除该视频吗？')) {
        await resourceApi.deleteResource(chapter.lessons[lessonIndex].videoFile.id)
        chapter.lessons[lessonIndex].videoFile = null
        chapter.lessons[lessonIndex].videoPreview = null
        ElMessage.success('视频删除成功')
      }
    } catch (error) {
      console.error('删除视频失败:', error)
      ElMessage.error('删除视频失败：' + (error.response?.data?.error || error.message))
    }
  }

  // 富文本编辑器相关
  const richEditor = ref(null)
  const showPreview = ref(true)
  const learningGoals = ref([''])

  // 格式化课程描述为HTML
  const formattedDescription = computed(() => {
    if (!newCourse.value || !newCourse.value.description) return ''
    
    // 简单的Markdown转HTML（实际项目可能需要使用专门的库如Marked.js等）
    let html = newCourse.value.description
      // 转换换行为<br>
      .replace(/\n/g, '<br>')
      // 转换标题
      .replace(/# (.*?)(?:\n|$)/g, '<h1>$1</h1>')
      .replace(/## (.*?)(?:\n|$)/g, '<h2>$1</h2>')
      .replace(/### (.*?)(?:\n|$)/g, '<h3>$1</h3>')
      // 转换粗体
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // 转换斜体
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // 转换链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="text-blue-500 hover:underline">$1</a>')
  
    return html
  })

  // 应用文本格式化
  const applyTextFormat = (format) => {
    const textarea = richEditor.value
    if (!textarea) {
      console.warn('richEditor not found')
      return
    }
    
    // Ensure description exists to avoid potential errors
    if (!newCourse.value.description) {
      newCourse.value.description = ''
    }
    
    const start = textarea.selectionStart || 0
    const end = textarea.selectionEnd || 0
    const selectedText = newCourse.value.description.substring(start, end)
    
    let formattedText = ''
    let cursorPosition = end
    
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`
        cursorPosition = start + formattedText.length
        break
      case 'italic':
        formattedText = `*${selectedText}*`
        cursorPosition = start + formattedText.length
        break
      case 'underline':
        formattedText = `<u>${selectedText}</u>`
        cursorPosition = start + formattedText.length
        break
      case 'h1':
        formattedText = `# ${selectedText}`
        cursorPosition = start + formattedText.length
        break
      case 'h2':
        formattedText = `## ${selectedText}`
        cursorPosition = start + formattedText.length
        break
      case 'h3':
        formattedText = `### ${selectedText}`
        cursorPosition = start + formattedText.length
        break
      case 'ul':
        formattedText = selectedText.split('\n').map(line => `- ${line}`).join('\n')
        cursorPosition = start + formattedText.length
        break
      case 'ol':
        formattedText = selectedText.split('\n').map((line, index) => `${index + 1}. ${line}`).join('\n')
        cursorPosition = start + formattedText.length
        break
      case 'code':
        formattedText = `\`\`\`\n${selectedText}\n\`\`\``
        cursorPosition = start + formattedText.length
        break
      case 'link':
        const linkText = selectedText || '链接文字'
        formattedText = `[${linkText}](https://example.com)`
        cursorPosition = start + formattedText.length
        break
      case 'image':
        const imageAlt = selectedText || '图片描述'
        formattedText = `![${imageAlt}](https://example.com/image.jpg)`
        cursorPosition = start + formattedText.length
        break
      default:
        return
    }
    
    // 更新文本
    newCourse.value.description = 
      newCourse.value.description.substring(0, start) + 
      formattedText + 
      newCourse.value.description.substring(end)
    
    // 下一个事件循环更新焦点，确保DOM已更新
    setTimeout(() => {
      if (textarea) {
        textarea.focus()
        textarea.setSelectionRange(cursorPosition, cursorPosition)
      }
    }, 0)
  }

  // 添加学习目标
  const addLearningGoal = () => {
    learningGoals.value.push('')
  }

  // 移除学习目标
  const removeLearningGoal = (index) => {
    learningGoals.value.splice(index, 1)
  }

  // 关闭创建新课程模态框
  const closeCreateModal = () => {
    showCreateModal.value = false
    // Reset activeStep to ensure the dialog starts at step 1 when reopened
    activeStep.value = 1
  }

  // 视频预览对话框
  const showVideoPreviewDialog = ref(false)
  const previewingLesson = ref(null)
  const videoPlayer = ref(null)

  // 获取视频URL
  const getVideoUrl = (lesson) => {
    // if (videoFile && videoFile instanceof File) {
    //   return URL.createObjectURL(lesson.video_url)
    // }
    return lesson.video_url
  }

  // 预览视频
  const previewVideo = (lesson) => {
    if (lesson && lesson.videoFile) {
      previewingLesson.value = lesson
      showVideoPreviewDialog.value = true
      
      // 关闭对话框时清理URL
      nextTick(() => {
        const cleanupUrl = () => {
          if (previewingLesson.value && previewingLesson.value.videoFile) {
            //URL.revokeObjectURL(getVideoUrl(previewingLesson.value))
          }
        }
        
        // 监听对话框关闭事件
        watch(showVideoPreviewDialog, (newVal) => {
          if (!newVal) {
            cleanupUrl()
          }
        })
      })
    } else {
      ElMessage.warning('无法播放视频，请先上传')
    }
  }

  // 课程分析相关
  const showAnalysisModal = ref(false)
  const analyzingCourse = ref(null)
  const studentStatusChart = ref(null)
  const engagementTrendChart = ref(null)
  const chapterCompletionChart = ref(null)
  const interactionChart = ref(null)
  const studyTimeChart = ref(null)

  // 图表实例
  let studentStatusChartInstance = null
  let engagementTrendChartInstance = null
  let chapterCompletionChartInstance = null
  let interactionChartInstance = null
  let studyTimeChartInstance = null

  // 概览统计数据
  const overviewStats = computed(() => {
    if (!analyzingCourse.value) return []
    
    const course = analyzingCourse.value
    const completedCount = Math.floor(course.studentCount * 0.35)
    const inProgressCount = Math.floor(course.studentCount * 0.45)
    const notStartedCount = course.studentCount - completedCount - inProgressCount
    
    return [
      {
        label: '学生总数',
        value: course.studentCount,
        icon: 'groups',
        bgColor: 'bg-blue-100',
        iconColor: 'text-blue-600'
      },
      {
        label: '已完成学习',
        value: completedCount,
        icon: 'task_alt',
        bgColor: 'bg-green-100',
        iconColor: 'text-green-600'
      },
      {
        label: '学习中',
        value: inProgressCount,
        icon: 'play_circle',
        bgColor: 'bg-yellow-100',
        iconColor: 'text-yellow-600'
      },
      {
        label: '未开始学习',
        value: notStartedCount,
        icon: 'hourglass_empty',
        bgColor: 'bg-gray-100',
        iconColor: 'text-gray-600'
      }
    ]
  })

  // 显示课程分析
  const showCourseAnalysis = (course) => {
    analyzingCourse.value = course
    showAnalysisModal.value = true
    
    // 使用nextTick确保DOM已更新
    nextTick(() => {
      renderAnalysisCharts()
    })
  }

  // 渲染分析图表
  const renderAnalysisCharts = () => {
    if (!analyzingCourse.value) return
    
    // 清除之前的图表实例
    if (studentStatusChartInstance) studentStatusChartInstance.destroy()
    if (engagementTrendChartInstance) engagementTrendChartInstance.destroy()
    if (chapterCompletionChartInstance) chapterCompletionChartInstance.destroy()
    if (interactionChartInstance) interactionChartInstance.destroy()
    if (studyTimeChartInstance) studyTimeChartInstance.destroy()
    
    // 渲染学生状态分布图表
    renderStudentStatusChart()
    
    // 渲染参与趋势图表
    renderEngagementTrendChart()
    
    // 渲染章节完成情况图表
    renderChapterCompletionChart()
    
    // 渲染互动数据图表
    renderInteractionChart()
    
    // 渲染学习时长分布图表
    renderStudyTimeChart()
  }

  // 渲染学生状态分布图表
  const renderStudentStatusChart = () => {
    const ctx = studentStatusChart.value.getContext('2d')
    const course = analyzingCourse.value
    
    const completedCount = Math.floor(course.studentCount * 0.35)
    const inProgressCount = Math.floor(course.studentCount * 0.45)
    const notStartedCount = course.studentCount - completedCount - inProgressCount
    
    studentStatusChartInstance = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['已完成学习', '学习中', '未开始学习'],
        datasets: [{
          data: [completedCount, inProgressCount, notStartedCount],
          backgroundColor: ['#22c55e', '#eab308', '#d1d5db'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              boxWidth: 15,
              padding: 15
            }
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const label = context.label || ''
                const value = context.raw || 0
                const total = context.dataset.data.reduce((acc, curr) => acc + curr, 0)
                const percentage = Math.round((value / total) * 100)
                return `${label}: ${value}人 (${percentage}%)`
              }
            }
          }
        },
        cutout: '60%'
      }
    })
  }

  // 渲染参与趋势图表
  const renderEngagementTrendChart = () => {
    const ctx = engagementTrendChart.value.getContext('2d')
    
    // 生成过去30天的日期标签
    const labels = []
    for (let i = 29; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      labels.push(`${date.getMonth() + 1}/${date.getDate()}`)
    }
    
    // 生成随机的参与数据
    const activeUsersData = []
    const viewsData = []
    
    // 假设存在一个周末活跃度减少的模式
    for (let i = 0; i < 30; i++) {
      const dayOfWeek = (new Date().getDay() - i + 30) % 7 // 0 = 周日, 6 = 周六
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
      
      const baseActiveUsers = analyzingCourse.value.studentCount * (isWeekend ? 0.15 : 0.3)
      activeUsersData.push(Math.floor(baseActiveUsers + Math.random() * 15))
      
      const baseViews = analyzingCourse.value.studentCount * (isWeekend ? 0.3 : 0.6)
      viewsData.push(Math.floor(baseViews + Math.random() * 20))
    }
    
    engagementTrendChartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [
          {
            label: '活跃学生',
            data: activeUsersData,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.3,
            fill: true
          },
          {
            label: '内容访问量',
            data: viewsData,
            borderColor: '#8b5cf6',
            backgroundColor: 'rgba(139, 92, 246, 0.05)',
            tension: 0.3,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            position: 'top',
            labels: {
              boxWidth: 12,
              usePointStyle: true
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            },
            ticks: {
              maxRotation: 0,
              autoSkip: true,
              maxTicksLimit: 10
            }
          },
          y: {
            beginAtZero: true,
            grid: {
              borderDash: [2, 4]
            }
          }
        }
      }
    })
  }

  // 渲染章节完成情况图表
  const renderChapterCompletionChart = () => {
    const ctx = chapterCompletionChart.value.getContext('2d')
    
    // 模拟章节数据
    const chapterLabels = []
    const completionData = []
    
    // 创建标签和随机完成率
    const chapterCount = analyzingCourse.value.chapters?.length || 5
    for (let i = 1; i <= chapterCount; i++) {
      // 基于章节顺序递减完成率
      const completionRate = Math.max(5, Math.floor(90 - (i - 1) * (80 / chapterCount)))
      
      const chapterName = analyzingCourse.value.chapters?.[i-1]?.title || `第${i}章`
      chapterLabels.push(chapterName)
      completionData.push(completionRate)
    }
    
    chapterCompletionChartInstance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: chapterLabels,
        datasets: [{
          label: '完成率 (%)',
          data: completionData,
          backgroundColor: completionData.map(value => {
            if (value >= 80) return 'rgba(34, 197, 94, 0.7)'
            if (value >= 50) return 'rgba(234, 179, 8, 0.7)'
            return 'rgba(239, 68, 68, 0.7)'
          }),
          borderWidth: 0,
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: (value) => `${value}%`
            }
          }
        }
      }
    })
  }

  // 渲染互动数据图表
  const renderInteractionChart = () => {
    const ctx = interactionChart.value.getContext('2d')
    
    // 创建互动数据
    const studentCount = analyzingCourse.value.studentCount
    const likeCount = Math.floor(studentCount * 0.75 + Math.random() * 20)
    const favoriteCount = Math.floor(studentCount * 0.4 + Math.random() * 15)
    const questionCount = Math.floor(studentCount * 0.25 + Math.random() * 10)
    const discussionCount = Math.floor(studentCount * 0.6 + Math.random() * 25)
    const commentCount = Math.floor(studentCount * 0.35 + Math.random() * 20)
    
    interactionChartInstance = new Chart(ctx, {
      type: 'radar',
      data: {
        labels: ['点赞数', '收藏数', '提问数', '讨论参与', '评论数'],
        datasets: [{
          label: '互动量',
          data: [likeCount, favoriteCount, questionCount, discussionCount, commentCount],
          backgroundColor: 'rgba(99, 102, 241, 0.2)',
          borderColor: 'rgb(99, 102, 241)',
          pointBackgroundColor: 'rgb(99, 102, 241)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgb(99, 102, 241)'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        elements: {
          line: {
            borderWidth: 2
          }
        },
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          r: {
            beginAtZero: true,
            ticks: {
              display: false
            }
          }
        }
      }
    })
  }

  // 渲染学习时长分布图表
  const renderStudyTimeChart = () => {
    const ctx = studyTimeChart.value.getContext('2d')
    
    // 学习时长分布
    const labels = ['<1', '1-3', '3-5', '5-10', '>10']
    const data = [
      Math.floor(analyzingCourse.value.studentCount * 0.15), // <1小时
      Math.floor(analyzingCourse.value.studentCount * 0.25), // 1-3小时
      Math.floor(analyzingCourse.value.studentCount * 0.35), // 3-5小时
      Math.floor(analyzingCourse.value.studentCount * 0.2),  // 5-10小时
      Math.floor(analyzingCourse.value.studentCount * 0.05)  // >10小时
    ]
    
    studyTimeChartInstance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: '学生人数',
          data: data,
          backgroundColor: [
            'rgba(239, 68, 68, 0.7)',
            'rgba(249, 115, 22, 0.7)',
            'rgba(234, 179, 8, 0.7)',
            'rgba(16, 185, 129, 0.7)',
            'rgba(59, 130, 246, 0.7)'
          ],
          borderWidth: 0,
          borderRadius: 4
        }]
      },
      options: {
        indexAxis: 'y',
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const value = context.raw || 0
                const total = context.dataset.data.reduce((acc, curr) => acc + curr, 0)
                const percentage = Math.round((value / total) * 100)
                return `${value}人 (${percentage}%)`
              }
            }
          }
        },
        scales: {
          x: {
            beginAtZero: true,
            grid: {
              borderDash: [2, 4]
            }
          },
          y: {
            grid: {
              display: false
            }
          }
        }
      }
    })
  }

  // 加载学院和专业数据
  const loadOrganizationData = async () => {
    try {
      const [collegesResponse, majorsResponse] = await Promise.all([
        organizationApi.getColleges(),
        organizationApi.getMajors()
      ])
      
      collegeOptions.value = collegesResponse.results
      majorOptions.value = majorsResponse.results
    } catch (error) {
      console.error('加载组织数据失败:', error)
      ElMessage.error('加载学院和专业数据失败')
    }
  }

  // 根据选择的学院筛选专业
  const filterMajorsByCollege = (collegeId) => {
    if (!collegeId) {
      filteredMajors.value = []
      return
    }
    filteredMajors.value = majorOptions.value.filter(major => major.college === collegeId)
  }

  // 在组件挂载时加载数据
  onMounted(() => {
    loadOrganizationData()
    loadCourses()
  })

  // 添加图片加载错误处理函数
  const handleImageError = (event) => {
    event.target.src = '/src/assets/images/courses/default-course.jpg'
  }

  // 修改上传组件的配置
  const uploadProps = {
    accept: 'video/mp4,video/webm,video/ogg',
    showFileList: false
  }

  // 获取课时列表
  const getLessonsByChapter = async (chapterId) => {
    try {
      const response = await lessonApi.getLessons(chapterId)
      const lessons = response.results || []
      
      // 获取每个课时的资源数据
      const lessonsWithResources = await Promise.all(lessons.map(async (lesson) => {
        try {
          const resourceResponse = await resourceApi.getResourcesByLesson({ lesson_id: lesson.id, resource_type: 'VIDEO' })
          const resources = resourceResponse.results || []
          
          // 如果有视频资源，更新课时的video_url
          const videoResource = resources.find(r => r.resource_type === 'VIDEO')
          if (videoResource) {
            lesson.video_url = "https://clkj-ai-education.oss-cn-shenzhen.aliyuncs.com/"+videoResource.file
            lesson.videoFile = {
              id: videoResource.id,
              name: videoResource.title || '视频文件',
              size: videoResource.size || 0,
              type: 'video/mp4'
            }
          }
          
          return lesson
        } catch (error) {
          console.error('获取课时资源失败:', error)
          return lesson
        }
      }))
      
      return lessonsWithResources
    } catch (error) {
      console.error('获取课时列表失败:', error)
      return []
    }
  }

  // 处理课程概述更新
  const handleOverviewUpdate = (updatedOverview) => {
    console.log('课程概述更新:', updatedOverview)
    if (updatedOverview) {
      newCourse.value = {
        ...newCourse.value,
        overview: updatedOverview
      }
    }
  }

  // 在 script setup 部分添加 openCourse 方法
  const openCourse = (course) => {
    const routeUrl = `/course/${course.id}/chapter?name=${encodeURIComponent(course.name)}&code=${encodeURIComponent(course.code || '')}&teacherName=${encodeURIComponent(teacherStore.teacherData.name)}&teacherAvatar=${encodeURIComponent(teacherStore.teacherData.avatar)}`
    window.open(routeUrl, '_blank')
  }
  </script>

  <style scoped>
  .course-create-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px;
    background-color: #ebf5ff;
    border-bottom: 1px solid #e0e7ff;
  }

  .course-create-dialog :deep(.el-dialog__body) {
    padding: 24px;
  }

  .compact-steps :deep(.el-step__main) {
    padding-top: 4px;
    padding-bottom: 4px;
    min-height: auto;
  }

  .compact-steps :deep(.el-step__title) {
    font-size: 14px;
    line-height: 1.2;
  }

  .compact-steps :deep(.el-step__description) {
    font-size: 12px;
    padding-bottom: 0;
    margin-top: 2px;
  }

  .compact-steps :deep(.el-step) {
    padding-bottom: 0px;
  }

  .form-content :deep(.el-form-item__label) {
    padding-bottom: 4px;
  }

  .avatar-uploader :deep(.el-upload),
  .course-video-upload :deep(.el-upload) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    text-align: center;
    width: 100%;
  }

  .avatar-uploader :deep(.el-upload:hover),
  .course-video-upload :deep(.el-upload:hover) {
    border-color: #409eff;
    background-color: #f5f7fa;
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    gap: 8px;
  }

  .avatar {
    max-height: 160px;
    display: block;
    margin: 0 auto;
    border-radius: 6px;
  }

  .video-preview {
    position: relative;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .video-thumbnail {
    width: 80px;
    height: 60px;
    background-color: #e0e7ff;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .video-play-icon {
    font-size: 30px;
    color: #3b82f6;
  }

  .video-info {
    display: flex;
    flex-direction: column;
    text-align: left;
    flex-grow: 1;
    min-width: 0;
  }

  .video-name {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .video-size {
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
  }

  .video-remove-btn {
    background-color: #fee2e2;
    color: #ef4444;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
  }

  .video-remove-btn:hover {
    background-color: #fecaca;
  }

  .video-remove-btn .material-icons {
    font-size: 18px;
  }

  /* Custom styling for Element Plus steps */
  :deep(.el-steps) {
    margin-bottom: 24px;
  }

  :deep(.el-step__title) {
    font-weight: 500;
  }

  :deep(.el-step__head.is-process) {
    color: #2563eb;
    border-color: #2563eb;
  }

  :deep(.el-step__title.is-process) {
    color: #2563eb;
  }

  /* Fix alignment of form fields */
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-input__wrapper),
  :deep(.el-select .el-input__wrapper) {
    height: 40px;
    line-height: 40px;
  }

  /* Make select component match input height */
  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-select .el-input) {
    height: 40px;
  }

  /* 视频播放器样式 */
  .video-player-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 16px 20px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .video-player-dialog :deep(.el-dialog__body) {
    padding: 24px;
  }

  .video-player-container {
    position: relative;
  }

  .no-video-message {
    background-color: #f1f5f9;
    border-radius: 8px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .add-lesson-btn {
    font-weight: 500 !important;
    font-size: 0.95rem !important;
  }

  .add-lesson-btn :deep(.material-icons) {
    font-size: 1.1rem !important;
  }

  .add-lesson-btn, .add-button {
    font-weight: 500 !important;
  }

  .add-lesson-btn :deep(.material-icons),
  .add-button :deep(.material-icons) {
    font-size: 1.1rem !important;
  }

  .el-button.add-button {
    padding: 10px 16px;
  }

  .chapter-actions .el-button,
  .lesson-actions .el-button {
    font-size: 14px;
  }

  /* 分析模态框样式 */
  .analysis-dialog :deep(.el-dialog__header) {
    margin: 0;
    padding: 20px;
    background-color: #f0fdf4;
    border-bottom: 1px solid #dcfce7;
  }

  .analysis-dialog :deep(.el-dialog__body) {
    padding: 24px;
    background-color: #f9fafb;
  }

  .analysis-content {
    margin-bottom: 16px;
  }

  /* 使图表在小屏幕上更加响应式 */
  @media (max-width: 1280px) {
    .analysis-content .grid-cols-4 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 16px;
    }
    
    .analysis-content .grid-cols-2 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
  </style>