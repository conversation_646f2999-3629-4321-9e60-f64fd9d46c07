<template>
  <el-dialog
    :title="isEdit ? '编辑商品' : '添加商品'"
    v-model="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="product-form"
    >
      <el-form-item label="商品名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入商品名称" />
      </el-form-item>
      
      <el-form-item label="商品描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入商品描述"
        />
      </el-form-item>
      
      <el-form-item label="积分价格" prop="points_price">
        <el-input-number
          v-model="form.points_price"
          :min="1"
          :max="99999"
          placeholder="请输入积分价格"
        />
      </el-form-item>
      
      <el-form-item label="库存数量" prop="stock">
        <el-input-number
          v-model="form.stock"
          :min="0"
          :max="99999"
          placeholder="请输入库存数量"
        />
      </el-form-item>
      
      <el-form-item label="商品类别" prop="category">
        <el-select v-model="form.category" placeholder="请选择商品类别">
          <el-option label="实物商品" value="physical" />
          <el-option label="虚拟商品" value="virtual" />
          <el-option label="学习资源" value="learning" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="商品图片" prop="image">
        <el-upload
          ref="uploadRef"
          class="product-image-uploader"
          :action="`/api/products/${currentProductId}/upload_image/`"
          :show-file-list="false"
          :on-success="handleImageSuccess"
          :on-error="handleImageError"
          :on-progress="handleImageProgress"
          :before-upload="beforeImageUpload"
          :disabled="!currentProductId"
          v-bind="uploadConfig"
        >
          <el-image 
            v-if="form.image" 
            :src="getProductImage(form.image)" 
            class="uploaded-image"
            fit="cover"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else-if="uploadProgress" class="upload-progress">
            <el-progress type="circle" :percentage="uploadProgress" />
          </div>
          <el-icon v-else class="upload-icon"><Plus /></el-icon>
          <template #tip>
            <div class="el-upload__tip">
              只能上传jpg/png文件，且不超过2MB
            </div>
          </template>
        </el-upload>
        <div v-if="form.image" class="image-actions">
          <el-button type="danger" link @click="handleRemoveImage">
            删除图片
          </el-button>
        </div>
      </el-form-item>
      
      <el-form-item label="是否上架" prop="is_active">
        <el-switch v-model="form.is_active" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { Plus, Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import defaultProductImage from '@/assets/images/products/coffee.jpg'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: () => null
  }
})

const emit = defineEmits(['update:show', 'submit'])

const dialogVisible = ref(false)
const formRef = ref(null)
const isEdit = ref(false)
const uploadRef = ref(null)
const currentProductId = ref(null)
const submitting = ref(false)

const form = ref({
  name: '',
  description: '',
  points_price: 1,
  stock: 0,
  category: 'physical',
  image: '',
  is_active: true
})

const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' }
  ],
  points_price: [
    { required: true, message: '请输入积分价格', trigger: 'blur' },
    { type: 'number', min: 1, message: '积分价格必须大于0', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存数量不能小于0', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品类别', trigger: 'change' }
  ]
}

// 新增上传进度状态
const uploadProgress = ref(0)

// 获取认证信息
const getAuthHeaders = () => {
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  const token = localStorage.getItem('token') // 假设token存储在localStorage中
  
  return {
    'X-CSRFToken': csrfToken || '',
    'Authorization': token ? `Bearer ${token}` : '',
  }
}

// 修改el-upload组件的配置
const uploadConfig = {
  headers: getAuthHeaders(),
  withCredentials: true, // 确保发送cookies
}

// 监听show属性变化
watch(() => props.show, (val) => {
  dialogVisible.value = val
  if (val && props.product) {
    isEdit.value = true
    form.value = { ...props.product }
    currentProductId.value = props.product.id
  } else {
    isEdit.value = false
    form.value = {
      name: '',
      description: '',
      points_price: 1,
      stock: 0,
      category: 'physical',
      image: '',
      is_active: true
    }
    currentProductId.value = null
  }
})

// 监听对话框关闭
watch(dialogVisible, (val) => {
  emit('update:show', val)
})

// 图片上传前的验证
const beforeImageUpload = (file) => {
  const isJpgOrPng = ['image/jpeg', 'image/png'].includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJpgOrPng) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  uploadProgress.value = 0
  return true
}

// 图片上传进度处理
const handleImageProgress = (event) => {
  uploadProgress.value = Math.round(event.percent)
}

// 图片上传成功的回调
const handleImageSuccess = (res) => {
  uploadProgress.value = 100
  setTimeout(() => {
    uploadProgress.value = 0
    form.value.image = res.url
  }, 300)
}

// 图片上传失败的回调
const handleImageError = (error, file, fileList) => {
  uploadProgress.value = 0
  let errorMessage = '图片上传失败，请重试'
  
  if (error.status === 401) {
    errorMessage = '未授权，请重新登录'
  } else if (error.response) {
    // 尝试从响应中获取详细错误信息
    const responseData = error.response.data
    errorMessage = responseData.detail || responseData.error || errorMessage
  }
  
  ElMessage.error(errorMessage)
  console.error('Upload error:', error)
}

// 删除图片
const handleRemoveImage = () => {
  form.value.image = ''
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    submitting.value = true
    const valid = await formRef.value.validate()
    
    if (valid) {
      // 提交基本信息
      const formData = { ...form.value }
      const tempImage = formData.image
      delete formData.image // 移除图片字段，后续单独处理
      
      const result = await new Promise((resolve) => {
        emit('submit', formData, resolve)
      })
      
      // 如果是新增商品，保存ID用于后续上传图片
      if (!isEdit.value && result?.id) {
        currentProductId.value = result.id
        
        // 如果有待上传的图片，触发上传
        if (uploadRef.value && tempImage) {
          const uploadFiles = uploadRef.value.uploadFiles
          if (uploadFiles && uploadFiles.length > 0) {
            uploadRef.value.submit()
          }
        }
      }
      
      handleClose()
    }
  } finally {
    submitting.value = false
  }
}

// 获取商品图片
const getProductImage = (image) => {
  return image || defaultProductImage
}
</script>

<style scoped>
.product-form {
  padding: 20px;
}

.product-image-uploader {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.product-image-uploader:hover {
  border-color: var(--el-color-primary);
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.uploaded-image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}

.upload-progress {
  width: 178px;
  height: 178px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--el-fill-color-lighter);
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.image-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.image-actions {
  margin-top: 8px;
  text-align: center;
}

.el-upload__tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 8px;
}

.product-image-uploader :deep(.el-upload) {
  cursor: pointer;
}

.product-image-uploader :deep(.el-upload.is-disabled) {
  cursor: not-allowed;
}
</style> 