<template>
  <view class="points-container">
    <!-- 积分卡片 -->
    <view class="points-card">
      <view class="points-info">
        <text class="label">我的积分</text>
        <text class="value">{{ myPoints }}</text>
      </view>
      <view class="card-actions">
        <view class="action-item" @tap="navigateTo('/pages/points/mall')">
          <uni-icons type="shop" size="24" color="#fff"></uni-icons>
          <text>积分商城</text>
        </view>
        <view class="action-item" @tap="navigateTo('/pages/points/record')">
          <uni-icons type="list" size="24" color="#fff"></uni-icons>
          <text>兑换记录</text>
        </view>
      </view>
    </view>

    <!-- 积分明细 -->
    <view class="points-list">
      <view class="list-header">
        <text class="title">积分明细</text>
        <view class="filter">
          <picker 
            mode="selector" 
            :range="filterOptions" 
            :value="currentFilterIndex"
            @change="handleFilterChange"
          >
            <text>{{ filterOptions[currentFilterIndex] }}</text>
            <uni-icons type="down" size="14" color="#666"></uni-icons>
          </picker>
        </view>
      </view>

      <scroll-view 
        class="list-content"
        scroll-y
        @scrolltolower="loadMore"
        :refresher-enabled="true"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
      >
        <view 
          class="points-item"
          v-for="item in pointsList"
          :key="item.id"
        >
          <view class="item-info">
            <text class="type">{{ item.type }}</text>
            <text class="time">{{ formatDate(item.createTime) }}</text>
          </view>
          <view class="item-points" :class="{ plus: item.points > 0 }">
            {{ item.points > 0 ? '+' : '' }}{{ item.points }}
          </view>
        </view>

        <!-- 加载状态 -->
        <uni-load-more :status="loadMoreStatus" :content-text="loadMoreText"></uni-load-more>

        <!-- 空状态 -->
        <view class="empty-state" v-if="pointsList.length === 0">
          <image src="/static/images/empty.png" mode="aspectFit"></image>
          <text>暂无积分记录</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { formatDate } from '@/utils/date'

export default {
  data() {
    return {
      filterOptions: ['全部', '获取', '消费'],
      currentFilterIndex: 0,
      page: 1,
      isRefreshing: false,
      loadMoreStatus: 'more',
      loadMoreText: {
        contentdown: '上拉加载更多',
        contentrefresh: '加载中...',
        contentnomore: '没有更多了'
      }
    }
  },

  computed: {
    ...mapState('points', ['myPoints', 'pointsRecord']),

    pointsList() {
      if (this.currentFilterIndex === 0) return this.pointsRecord
      return this.pointsRecord.filter(item => {
        return this.currentFilterIndex === 1 ? item.points > 0 : item.points < 0
      })
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    ...mapActions('points', ['getMyPoints', 'getPointsRecord']),

    // 初始化页面
    async initPage() {
      this.loadMoreStatus = 'loading'
      try {
        await Promise.all([
          this.getMyPoints(),
          this.loadPointsRecord()
        ])
      } catch (error) {
        this.$toast.error('加载失败')
      }
      this.loadMoreStatus = 'more'
    },

    // 加载积分记录
    async loadPointsRecord() {
      try {
        await this.getPointsRecord({
          page: this.page,
          limit: 20
        })
      } catch (error) {
        throw error
      }
    },

    // 切换筛选
    handleFilterChange(e) {
      this.currentFilterIndex = e.detail.value
    },

    // 下拉刷新
    async onRefresh() {
      this.isRefreshing = true
      this.page = 1
      try {
        await this.initPage()
        this.$toast.success('刷新成功')
      } catch (error) {
        this.$toast.error('刷新失败')
      }
      this.isRefreshing = false
    },

    // 加载更多
    async loadMore() {
      if (this.loadMoreStatus !== 'more') return
      this.loadMoreStatus = 'loading'
      this.page++
      try {
        await this.loadPointsRecord()
      } catch (error) {
        this.$toast.error('加载失败')
        this.page--
      }
      this.loadMoreStatus = 'more'
    },

    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({ url })
    },
  }
}
</script>

<style lang="scss">
.points-container {
  min-height: 100vh;
  background: #f5f5f5;

  .points-card {
    background: linear-gradient(to right, #007AFF, #00BFFF);
    padding: 40rpx 30rpx;
    color: #fff;

    .points-info {
      margin-bottom: 40rpx;

      .label {
        font-size: 28rpx;
        opacity: 0.9;
      }

      .value {
        font-size: 80rpx;
        font-weight: bold;
        margin-top: 20rpx;
        display: block;
      }
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-left: 40rpx;
        opacity: 0.9;

        uni-icons {
          margin-bottom: 10rpx;
        }

        text {
          font-size: 24rpx;
        }
      }
    }
  }

  .points-list {
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    margin-top: -20rpx;
    min-height: 800rpx;

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #eee;

      .title {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
      }

      .filter {
        picker {
          display: flex;
          align-items: center;

          text {
            font-size: 26rpx;
            color: #666;
            margin-right: 10rpx;
          }
        }
      }
    }

    .list-content {
      height: calc(100vh - 400rpx);

      .points-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        border-bottom: 1rpx solid #eee;

        .item-info {
          .type {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 10rpx;
            display: block;
          }

          .time {
            font-size: 24rpx;
            color: #999;
          }
        }

        .item-points {
          font-size: 32rpx;
          color: #ff5a5f;
          font-weight: bold;

          &.plus {
            color: #3cc51f;
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 100rpx 0;

        image {
          width: 200rpx;
          height: 200rpx;
          margin-bottom: 20rpx;
        }

        text {
          font-size: 28rpx;
          color: #999;
        }
      }
    }
  }
}
</style> 