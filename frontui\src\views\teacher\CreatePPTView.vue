<template>
  <TeacherLayout :userName="teacherData.name" :userAvatar="teacherData.avatar" pageTitle="创建新PPT"
    activePage="content-creation" activeSubPage="ppt">
    <div class="bg-white p-6 rounded-lg shadow-md mb-3 mt-0">
      <div class="text-2xl font-bold mb-4"
        style="padding-bottom: 10px; border: 1px solid #fff; border-radius: 20px; padding: 10px; background-color: #fff;">
        <!-- 当有用户发送过消息时显示对话区域 -->
        <div v-if="hasUserMessage" class="conversation-content mb-4" style="height: 450px; overflow-y: auto;">
          <div v-for="(msg, idx) in conversation" :key="idx" :class="msg.role === 'user' ? 'text-right' : 'text-left'"
            style="display: flex; align-items: start; margin-bottom: 16px;">
            <!-- AI头像 -->
            <div v-if="msg.role === 'ai'" class="avatar-ai-center flex-shrink-0 mr-3">
              <svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg"
                style="width: 1.8rem; height: 1.8rem; vertical-align: middle;">
                <path id="path"
                  d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z"
                  fill-rule="nonzero" fill="#4D6BFE"></path>
              </svg>
            </div>
            <div
              :class="['inline-block', 'px-3', 'py-2', 'rounded-lg', 'mb-1.5', 'max-w-3xl', msg.role === 'user' ? 'ml-auto bg-blue-100' : 'bg-white', 'text-base']">
              <!-- 用户消息直接显示 -->
              <span v-if="msg.role === 'user'" class="text-base">{{ msg.content }}</span>
              <!-- AI消息检查是否包含markdown代码块，使用markdown-it渲染 -->
              <div v-else class="text-base">
                <div v-if="msg.content.includes('```')">
                  <!-- 将消息拆分为普通文本和代码块部分 -->
                  <div v-for="(part, pIdx) in parseMessageWithCodeBlocks(msg.content)" :key="`part-${idx}-${pIdx}`">
                    <!-- 普通文本部分 -->
                    <div v-if="part.type === 'text'" v-html="md.render(part.content)" class="text-base"></div>
                    <!-- 代码块部分，显示原始的markdown -->
                    <pre v-else
                      class="markdown-code bg-gray-800 text-white p-2 rounded my-1.5 overflow-x-auto text-sm"><code>{{ part.content }}</code></pre>
                  </div>
                </div>
                <div v-else v-html="md.render(msg.content)"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 当没有用户发送过消息时显示欢迎信息 -->
        <div v-else class="ai-welcome-box" style="text-align:center; margin: 150px 0;">
          <div class="ai-welcome-title"
            style="font-size: 1.6rem; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; color: #000;">
            <!-- <svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 3.5rem; height: 3.5rem; margin-right: 0.7rem; vertical-align: middle;">
              <path id="path" d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z" fill-rule="nonzero" fill="#4D6BFE"></path>
            </svg> -->
            我是AIPPT，您的智能课件助手！
          </div>
          <div class="ai-welcome-desc" style="color: #333; font-size: 0.9rem; margin-bottom: 45px;">
            上传教案/知识库，自动生成逻辑清晰的PPT，智能匹配教学模板，一键美化排版，立即体验智能PPT创作！
          </div>
        </div>
      </div>
      <div class="input-area-container">
        <div class="input-area-box">
          <span class="input-placeholder" v-if="!inputMessage">给 PPT AI 发送消息</span>

          <textarea v-model="inputMessage" class="input-textarea" rows="1" @keyup.enter="handleSendClick"
            placeholder=""></textarea>
        </div>
        <div class="input-bottom-row">
          <div class="input-functions">
            <el-button v-for="func in functions" :key="func.type" size="small" @click="toggleFunction(func.type)"
              class="min-w-fit">
              <span v-if="func.type === 'deep'" class="mr-1" style="display:inline-flex;align-items:center;">
                <svg width="17" height="17" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M2.656 17.344c-1.016-1.015-1.15-2.75-.313-4.925.325-.825.73-1.617 1.205-2.365L3.582 10l-.033-.054c-.5-.799-.91-1.596-1.206-2.365-.836-2.175-.703-3.91.313-4.926.56-.56 1.364-.86 2.335-.86 1.425 0 3.168.636 4.957 1.756l.053.034.053-.034c1.79-1.12 3.532-1.757 4.957-1.757.972 0 1.776.3 2.335.86 1.014 1.015 1.148 2.752.312 4.926a13.892 13.892 0 0 1-1.206 2.365l-.034.054.034.053c.5.8.91 1.596 1.205 2.365.837 2.175.704 3.911-.311 4.926-.56.56-1.364.861-2.335.861-1.425 0-3.168-.637-4.957-1.757L10 16.415l-.053.033c-1.79 1.12-3.532 1.757-4.957 1.757-.972 0-1.776-.3-2.335-.86zm13.631-4.399c-.187-.488-.429-.988-.71-1.492l-.075-.132-.092.12a22.075 22.075 0 0 1-3.968 3.968l-.12.093.132.074c1.308.734 2.559 1.162 3.556 1.162.563 0 1.006-.138 1.298-.43.3-.3.436-.774.428-1.346-.008-.575-.159-1.264-.449-2.017zm-6.345 1.65l.058.042.058-.042a19.881 19.881 0 0 0 4.551-4.537l.043-.058-.043-.058a20.123 20.123 0 0 0-2.093-2.458 19.732 19.732 0 0 0-2.458-2.08L10 5.364l-.058.042A19.883 19.883 0 0 0 5.39 9.942L5.348 10l.042.059c.631.874 1.332 1.695 2.094 2.457a19.74 19.74 0 0 0 2.458 2.08zm6.366-10.902c-.293-.293-.736-.431-1.298-.431-.998 0-2.248.429-3.556 1.163l-.132.074.12.092a21.938 21.938 0 0 1 3.968 3.968l.092.12.074-.132c.282-.504.524-1.004.711-1.492.29-.753.442-1.442.45-2.017.007-.572-.129-1.045-.429-1.345zM3.712 7.055c.202.514.44 1.013.712 1.493l.074.13.092-.119a21.94 21.94 0 0 1 3.968-3.968l.12-.092-.132-.074C7.238 3.69 5.987 3.262 4.99 3.262c-.563 0-1.006.138-1.298.43-.3.301-.436.774-.428 1.346.007.575.159 1.264.448 2.017zm0 5.89c-.29.753-.44 1.442-.448 2.017-.008.572.127 1.045.428 1.345.293.293.736.431 1.298.431.997 0 2.247-.428 3.556-1.162l.131-.074-.12-.093a21.94 21.94 0 0 1-3.967-3.968l-.093-.12-.074.132a11.712 11.712 0 0 0-.71 1.492z"
                    fill="currentColor" stroke="currentColor" stroke-width=".1"></path>
                  <path d="M10.706 11.704A1.843 1.843 0 0 1 8.155 10a1.845 1.845 0 1 1 2.551 1.704z" fill="currentColor"
                    stroke="currentColor" stroke-width=".2"></path>
                </svg>
              </span>
              <span v-else-if="func.type === 'web'" class="mr-1" style="display:inline-flex;align-items:center;">
                <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="10" cy="10" r="9" stroke="currentColor" stroke-width="1.8"></circle>
                  <path d="M10 1c1.657 0 3 4.03 3 9s-1.343 9-3 9M10 19c-1.657 0-3-4.03-3-9s1.343-9 3-9M1 10h18"
                    stroke="currentColor" stroke-width="1.8"></path>
                </svg>
              </span>
              <el-icon v-else class="mr-1" style="font-size:18px;">
                <component :is="func.icon" />
              </el-icon>
              {{ func.label }}
            </el-button>

            <!-- 学科选择下拉框 -->
            <div class="flex items-center subject-wrapper ml-4">
              <span class="text-sm text-gray-500 mr-4" style="display:inline-block;float:left;">学科:</span>
              <el-select v-model="selectedSubject" placeholder="选择学科" size="small" class="subject-select"
                :loading="loadingSubjects" style="display:inline-block;float:left;margin-left:4px;">
                <el-option v-for="subject in subjectList" :key="subject.id" :label="subject.name" :value="subject.id" />
                <template #empty>
                  <div class="flex items-center justify-center p-2 text-gray-500 text-sm">
                    <el-icon v-if="loadingSubjects" class="mr-1 animate-spin">
                      <Loading />
                    </el-icon>
                    <span>{{ loadingSubjects ? '加载学科中...' : '暂无学科' }}</span>
                  </div>
                </template>
              </el-select>
            </div>
          </div>

          <!-- 添加选中文档和教案显示区域 -->
          <div class="selected-items">
            <!-- 显示选中的知识库文档 -->
            <div v-if="selectedDocuments.length > 0" class="selected-documents">
              <div class="selected-items-title">已选择知识库文档：</div>
              <div class="selected-items-content">
                <div v-for="docId in selectedDocuments" :key="docId" class="selected-document-tag">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <el-tooltip :content="getDocumentName(docId)" placement="top">
                    <span class="truncated-filename">{{ truncateFileName(getDocumentName(docId), 15) }}</span>
                  </el-tooltip>
                  <span class="remove-btn" @click="toggleDocument(docId)">
                    <el-icon>
                      <Close />
                    </el-icon>
                  </span>
                </div>
              </div>
            </div>

            <!-- 显示导入的教案 -->
            <div v-if="importedLessonPlan" class="selected-documents">
              <div class="selected-items-title">已导入教案：</div>
              <div class="selected-items-content">
                <div class="selected-document-tag">
                  <el-icon>
                    <Document />
                  </el-icon>
                  {{ importedLessonPlan.name }}
                  <div class="lesson-plan-info">
                    <span class="text-xs text-gray-500 ml-2">{{ formatFileSize(importedLessonPlan.size) }}</span>
                    <!-- <span class="text-xs text-blue-500 ml-2">{{ importedLessonPlan.points }}个知识点</span> -->
                  </div>
                  <span class="remove-btn" @click="removeImportedPlan">
                    <el-icon>
                      <Close />
                    </el-icon>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <el-tooltip v-if="!canSendMessage" content="请输入问题或选择知识库文档/教案" placement="top">
              <span>
                <button class="input-btn send-btn custom-send-btn disabled-send-btn" disabled>
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="12" fill="#4D6BFE" />
                    <g transform="rotate(-90 12 12)">
                      <path d="M8 12h8M14 8l4 4-4 4" stroke="#fff" stroke-width="1.2" stroke-linecap="round"
                        stroke-linejoin="round" />
                    </g>
                  </svg>
                </button>
              </span>
            </el-tooltip>
            <button v-else class="input-btn send-btn custom-send-btn" @click="generateWithAI">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="12" fill="#4D6BFE" />
                <g transform="rotate(-90 12 12)">
                  <path d="M8 12h8M14 8l4 4-4 4" stroke="#fff" stroke-width="1.2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </g>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>


    <!-- 知识库弹窗（完整迁移新版UI） -->
    <el-dialog v-model="showKnowledgeModal" title="选择知识库文档" width="1000px" height="400px" :center="true"
      :destroy-on-close="false" :close-on-click-modal="false">
      <div class="knowledge-selector">
        <!-- 添加知识库使用说明提示 -->
        <div class="mb-3 p-2 bg-orange-50 border-l-4 border-orange-400 text-orange-800">
          <div class="flex items-center">
            <el-icon class="mr-2 text-orange-500">
              <WarningFilled />
            </el-icon>
            <div>
              <p class="font-medium text-sm">温馨提示</p>
              <p class="text-xs">选择知识库文档时，必须上传教案文件才能生成PPT。请先上传教案，再选择知识库文档。</p>
            </div>
          </div>
        </div>

        <!-- 选项卡导航 -->
        <div class="flex border-b border-gray-200 mb-3">
          <button v-for="tab in ['categories', 'datasets', 'documents']" :key="tab" @click="knowledgeTab = tab"
            class="py-2 px-6 font-medium relative"
            :class="knowledgeTab === tab ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'">
            {{ tab === 'categories' ? '知识库分类' : tab === 'datasets' ? '数据集' : '文档' }}
            <span v-if="knowledgeTab === tab" class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"></span>
          </button>
        </div>
        <!-- 搜索和筛选 -->
        <div class="mb-3 flex space-x-4">
          <div class="flex-1">
            <div class="relative flex items-center w-full border rounded-md overflow-hidden">
              <el-icon class="flex-shrink-0 ml-3 text-gray-400" style="font-size: 16px;">
                <Search />
              </el-icon>
              <input v-model="searchQuery" type="text" placeholder="搜索..."
                class="w-full px-3 py-1.5 outline-none border-none" />
            </div>
          </div>
        </div>
        <!-- 内容区域 -->
        <div class="modal-content-container" style="height: 30vh; overflow-y: auto;">
          <!-- 知识分类列表 -->
          <div v-if="knowledgeTab === 'categories'" class="grid grid-cols-4 gap-4">
            <div v-if="loadingCategories" class="col-span-4 text-center py-8">
              <span class="material-icons animate-spin text-blue-500 text-2xl">sync</span>
              <p class="mt-2 text-gray-500">加载分类中...</p>
            </div>
            <div v-else-if="categories.length === 0" class="col-span-4 text-center py-8">
              <el-icon class="text-gray-400 text-4xl mb-2">
                <InfoFilled />
              </el-icon>
              <p class="text-gray-500">暂无知识分类</p>
            </div>
            <div v-for="category in categories" :key="category.id" @click="selectCategory(category.id)"
              :class="['border rounded-md p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200', selectedCategory === category.id ? 'border-blue-500 bg-blue-50' : '']">
              <div class="font-medium mb-1">{{ category.name }}</div>
              <div class="text-sm text-gray-500">{{ category.description || '暂无描述' }}</div>
            </div>
          </div>
          <!-- 数据集列表 -->
          <div v-else-if="knowledgeTab === 'datasets'" class="grid grid-cols-4 gap-4">
            <div v-if="loadingDatasets" class="col-span-4 text-center py-8">
              <span class="material-icons animate-spin text-blue-500 text-2xl">sync</span>
              <p class="mt-2 text-gray-500">加载数据集中...</p>
            </div>
            <div v-else-if="datasets.length === 0" class="col-span-4 text-center py-8">
              <el-icon class="text-gray-400 text-4xl mb-2">
                <InfoFilled />
              </el-icon>
              <p class="text-gray-500">暂无数据集</p>
            </div>
            <div v-for="dataset in datasets" :key="dataset.id" @click="loadDocuments(dataset.id)"
              class="border rounded-md p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200">
              <div class="font-medium mb-1">{{ dataset.name }}</div>
              <div class="text-sm text-gray-500">{{ dataset.description || '暂无描述' }}</div>
              <div class="text-xs text-gray-400 mt-2">文档数: {{ dataset.count || 0 }}</div>
            </div>
          </div>
          <!-- 文档列表 -->
          <div v-else-if="knowledgeTab === 'documents'" class="grid grid-cols-4 gap-4">
            <div v-if="loadingDocuments" class="col-span-4 text-center py-8">
              <span class="material-icons animate-spin text-blue-500 text-2xl">sync</span>
              <p class="mt-2 text-gray-500">加载文档中...</p>
            </div>
            <div v-else-if="documents.length === 0" class="col-span-4 text-center py-8">
              <el-icon class="text-gray-400 text-4xl mb-2">
                <InfoFilled />
              </el-icon>
              <p class="text-gray-500">暂无文档</p>
            </div>
            <div v-for="doc in documents" :key="doc.id" @click="toggleDocument(doc.id)"
              :class="['border rounded-md p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-200', selectedDocuments.includes(doc.id) ? 'border-blue-500 bg-blue-50' : '']">
              <div class="font-medium truncate mb-1">{{ doc.name }}</div>
              <div class="flex justify-between text-xs text-gray-400 mt-2">
                <span>{{ doc.type || '未知类型' }}</span>
                <span>{{ formatFileSize(doc.size) }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 知识库错误提示 -->
        <div v-if="knowledgeError" class="mt-4 p-3 bg-red-50 border-l-2 border-red-400 text-red-700 text-sm">
          <span class="flex items-center"><el-icon class="mr-2">
              <WarningFilled />
            </el-icon> {{ knowledgeError }}</span>
        </div>
        <!-- 选择统计 -->
        <div class="p-4 bg-gray-50 border-t border-gray-200 mt-4">
          <div v-if="selectedDocuments.length > 0" class="mb-2">
            <div class="text-sm font-medium mb-2">已选择:</div>
            <div class="flex flex-wrap gap-2">
              <span class="text-sm font-medium text-gray-500">文档 ({{ selectedDocuments.length }}):</span>
              <span v-for="id in selectedDocuments" :key="id"
                class="inline-flex items-center px-3 py-1 rounded text-sm font-medium bg-green-100 text-green-800">
                {{ getDocumentName(id) }}
                <button @click.stop="toggleDocument(id)" class="ml-2 text-green-500 hover:text-green-700">
                  <el-icon>
                    <Close />
                  </el-icon>
                </button>
              </span>
            </div>
          </div>
          <div v-else class="text-center text-gray-500">
            未选择任何文档
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button @click="showKnowledgeModal = false">取消</el-button>
          <el-button type="primary" @click="showKnowledgeModal = false">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 教案弹窗 -->
    <ImportLessonPlanModal v-if="showLessonPlanModal" @close="showLessonPlanModal = false"
      @import="handleLessonPlanImport" />
  </TeacherLayout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import TeacherLayout from '@/components/layout/TeacherLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import ImportLessonPlanModal from '@/components/modals/ImportLessonPlanModal.vue'
import axios from 'axios'
import { pptApi } from '@/api/ppt'
import { knowledgeApi } from '@/api/knowledge'
import MarkdownIt from 'markdown-it'
import { ElMessage } from 'element-plus'

// 初始化markdown-it实例
const md = new MarkdownIt({
  html: true,          // 启用HTML标签
  breaks: true,        // 转换'\n'为<br>
  linkify: true,       // 自动将URL文本转为链接
  typographer: true    // 启用一些语言中立的替换+引号
})

// 导入 Element Plus 图标
import {
  HomeFilled,
  Document,
  Plus,
  Delete,
  ArrowUp,
  ArrowDown,
  Upload,
  Search,
  Close,
  Check,
  Refresh,
  Download,
  ArrowRight,
  Edit,
  DocumentAdd,
  FolderOpened,
  InfoFilled,
  Connection,
  WarningFilled,
  Picture,
  SetUp,
  Loading
} from '@element-plus/icons-vue'

// Router
const router = useRouter()

// Teacher Data
const teacherData = ref({
  name: '李教授',
  avatar: teacherAvatar,
  department: '计算机科学与技术学院',
  title: '副教授'
})

// Form data
const pptTitle = ref('')
const pptDescription = ref('')
const pptSubject = ref('')
const pptGrade = ref('')
const aiPrompt = ref('')
const isGenerating = ref(false)
const newTag = ref('')
const selectedTags = ref([])
const slides = ref([])

// 对话相关
const inputMessage = ref('')
const conversation = ref([])

// 添加markdown预览状态变量
const showMarkdownPreview = ref(false)

const handleSendClick = () => {
  const msg = inputMessage.value.trim()
  if (!msg) return

  // 直接调用generateWithAI方法
  generateWithAI()
}

// 任务状态相关
const currentTaskId = ref(null)
const progressPercentage = ref(0)
const progressMessage = ref('正在初始化...')
const currentSlideInfo = ref('')

// Computed properties
const isFormValid = computed(() => {
  return pptTitle.value.trim() !== '' &&
    pptSubject.value !== '' &&
    slides.value.length > 0
})

// 添加计算属性判断是否可以发送消息
const canSendMessage = computed(() => {
  // 如果选择了知识库但没有教案，不允许发送
  if (selectedDocuments.value.length > 0 && !importedLessonPlan.value) {
    return false;
  }

  return inputMessage.value.trim() !== '' ||
    (selectedDocuments.value.length > 0 && importedLessonPlan.value) ||
    importedLessonPlan.value !== null
})

// Methods

const addTag = () => {
  if (newTag.value.trim() && !selectedTags.value.includes(newTag.value.trim())) {
    selectedTags.value.push(newTag.value.trim())
    newTag.value = ''
  }
}

const removeTag = (index) => {
  selectedTags.value.splice(index, 1)
}

const addNewSlide = () => {
  slides.value.push({
    title: `幻灯片 ${slides.value.length + 1}`,
    sections: [] // 空的sections数组，结构与后端返回的一致
  })
}

const removeSlide = (index) => {
  slides.value.splice(index, 1)
}

const moveSlide = (oldIndex, newIndex) => {
  if (newIndex >= 0 && newIndex < slides.value.length) {
    const slide = slides.value.splice(oldIndex, 1)[0]
    slides.value.splice(newIndex, 0, slide)
  }
}

const saveDraft = async () => {
  if (!pptTitle.value.trim()) {
    showNotificationMessage('请输入PPT标题')
    return
  }

  if (slides.value.length === 0) {
    showNotificationMessage('请至少添加一张幻灯片')
    return
  }

  try {
    const response = await pptApi.saveDraft({
      title: pptTitle.value,
      slides: slides.value,
      settings: {
        teaching_duration: selectedTeachingDuration.value,
        teaching_style: selectedTeachingStyle.value,
        specific_requirements: specificRequirements.value
      }
    })

    if (response && response.code === 0) {
      showNotificationMessage('PPT草稿保存成功')
    } else {
      throw new Error(response?.message || '保存失败')
    }
  } catch (error) {
    console.error('保存PPT草稿失败:', error)
    showNotificationMessage('保存PPT草稿失败: ' + (error.message || '未知错误'))
  }
}

const previewPPT = () => {
  // 预览PPT逻辑
  alert('PPT预览功能待实现')
}

// 添加错误状态变量
const errorMessage = ref('')

// 添加Mock测试模式标记
const mockMarkdownTest = ref(false)

// 优化generateWithAI方法
const generateWithAI = async () => {
  // 重置错误信息
  errorMessage.value = ''

  // 准备用户输入内容用于后续使用
  const userInputContent = inputMessage.value.trim();

  // 测试模式：如果在开发环境中使用特定命令激活测试
  if (process.env.NODE_ENV !== 'production' && userInputContent === '!test-markdown') {
    mockMarkdownTest.value = true;
    console.log('激活Markdown测试模式');

    // 添加测试消息到对话
    conversation.value.push({ role: 'user', content: '测试Markdown接收功能' });
    conversation.value.push({ role: 'ai', content: '开始测试Markdown接收...' });

    // 清空输入框
    inputMessage.value = '';

    // 模拟接收到markdown数据
    setTimeout(() => {
      // 模拟Markdown内容
      const testMarkdown = `# 测试PPT标题

## 第一章 简介
### 简介
- 这是一个测试内容
- 用于验证markdown是否正确接收

## 第二章 主要内容
### 第一节
- 主要内容第一节
### 第二节
- 主要内容第二节
  
## 第三章 总结
### 总结
- 这是测试总结内容`;

      console.log('模拟接收到Markdown数据:', testMarkdown.length);

      // 保存markdown内容
      taskMarkdown.value = testMarkdown;

      // 从markdown解析slides信息
      updateSlidesFromMarkdown(testMarkdown);

      // 添加Markdown预览到对话
      const markdownPreview = testMarkdown.split('\n').slice(0, 15).join('\n') +
        (testMarkdown.split('\n').length > 15 ? '\n...(更多内容已省略)' : '');

      conversation.value.push({
        role: 'ai',
        content: `已生成PPT内容预览:\n\n${markdownPreview}`
      });

      conversation.value.push({
        role: 'ai',
        content: `PPT内容已完整生成，共${slides.value.length}张幻灯片，可点击下方按钮继续`
      });

      showNotificationMessage('测试完成：PPT内容已成功生成');
    }, 2000);

    return;
  }

  // 非测试模式下的原有代码
  // 先将用户输入添加到对话中
  if (userInputContent) {
    conversation.value.push({ role: 'user', content: userInputContent })
  } else if (importedLessonPlan.value || selectedDocuments.value.length > 0) {
    // 即使没有输入，但有上传教案或选择知识库，也添加一条提示消息
    if (importedLessonPlan.value) {
      conversation.value.push({ role: 'user', content: `请基于我上传的教案"${importedLessonPlan.value.name}"生成PPT` })
    } else {
      conversation.value.push({ role: 'user', content: `请基于选择的知识库文档生成PPT` })
    }
  }

  // 添加一条初始化消息，即使没有用户输入也显示
  conversation.value.push({ role: 'ai', content: '正在准备生成PPT...' })

  // 确保滚动到最新消息
  nextTick(() => {
    const container = document.querySelector('.conversation-content')
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  })

  // 清空输入框
  inputMessage.value = ''

  // 如果没有选择知识库文档和没有上传教案，仅输入框有内容，直接跳转到编辑器
  if (
    selectedDocuments.value.length === 0 &&
    !importedLessonPlan.value &&
    userInputContent !== ''
  ) {
    // 如果标题为空，使用用户输入的文本作为标题
    if (!pptTitle.value || pptTitle.value.trim() === '') {
      // 如果输入内容太长，截取一部分作为标题
      pptTitle.value = userInputContent.length > 10 
        ? userInputContent.substring(0, 10) + '...'
        : userInputContent;
    }
    
    // 跳转到DocmeePPTEditorView.vue，传递markdown参数
    console.log('直接跳转到文多多编辑器');
    router.push({
      path: '/teacher/ppt/editor',
      query: {
        editMode: 'false',
        markdown: encodeURIComponent(userInputContent),
        title: pptTitle.value || '',
        subjectId: selectedSubject.value || ''
      }
    })
    return
  }

  // 新增检查：如果选择了知识库但没有上传教案，则提示错误
  if (selectedDocuments.value.length > 0 && !importedLessonPlan.value) {
    errorMessage.value = '选择知识库时必须上传教案文件'
    showNotificationMessage('选择知识库时必须上传教案文件')

    // 在对话中添加错误提示
    conversation.value.push({ role: 'ai', content: '选择知识库时必须上传教案文件，请上传教案后再试' })
    return
  }

  // 检查是否有足够的内容生成PPT
  if (
    selectedDocuments.value.length === 0 &&
    !importedLessonPlan.value &&
    !userInputContent
  ) {
    errorMessage.value = '请提供教案内容、上传知识库文档或输入具体要求'
    showNotificationMessage('请提供教案内容、上传知识库文档或输入具体要求')

    // 在对话中添加错误提示
    conversation.value.push({ role: 'ai', content: '请提供教案内容、上传知识库文档或输入具体要求' })
    return
  }

  isGenerating.value = true
  isAnalyzing.value = true
  showSuccessNotice.value = false

  try {
    console.log('准备提交数据...');

    // 检查是否有文件
    if (importedLessonPlan.value && importedLessonPlan.value.file) {
      console.log('检测到教案文件:', importedLessonPlan.value.name);
      console.log('文件类型:', importedLessonPlan.value.file.type);
      console.log('文件大小:', importedLessonPlan.value.file.size);
    } else {
      console.log('没有教案文件，将使用输入的内容');
    }

    // 使用新的 FormData 实例
    const formData = new FormData();

    // 首先添加文件 - 确保字段名为 'file'
    if (importedLessonPlan.value && importedLessonPlan.value.file) {
      // 直接添加文件对象，不要修改文件名
      formData.append('file', importedLessonPlan.value.file);
    }

    // 添加输入框内容作为具体要求
    if (userInputContent) {
      formData.append('specific_requirements', userInputContent);
      formData.append('content', userInputContent);
    }

    // 添加其他参数
    formData.append('teaching_duration', selectedTeachingDuration.value);
    formData.append('teaching_style', selectedTeachingStyle.value);

    // 添加文档ID
    if (selectedDocuments.value.length > 0) {
      const docIds = JSON.stringify(selectedDocuments.value);
      formData.append('document_ids', docIds);
      console.log('添加知识库文档IDs:', selectedDocuments.value, '序列化后:', docIds);
    }

    // 调试：打印所有表单字段
    console.log('==== FormData 内容 ====');
    for (let [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`${key}: File(${value.name}, ${value.type}, ${value.size} bytes)`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    // 调用API
    console.log('正在调用pptApi.generatePPT...');

    // 使用pptApi中定义的方法发送请求
    const response = await pptApi.generatePPT(formData);

    console.log('API返回结果:', response);

    // 处理响应
    if (response && (response.code === 0 || response.code === 200)) {
      const result = response.data;

      // 保存任务ID，开始流式处理
      if (result.task_id) {
        console.log('获取到任务ID，开始流式处理:', result.task_id);
        currentTaskId.value = result.task_id;

        // 直接使用流式处理
        handleStreamResponse(result.task_id);
      } else {
        console.error('未获取到有效的任务ID');
        errorMessage.value = '未获取到有效的任务ID';
        isGenerating.value = false;
        isAnalyzing.value = false;

        // 重置知识库状态
        resetKnowledgeState();
      }
    } else {
      throw new Error(response?.message || '生成PPT失败');
    }
  } catch (error) {
    console.error('生成PPT失败:', error);
    errorMessage.value = '生成PPT失败: ' + (error.message || '未知错误');
    showNotificationMessage('生成PPT失败: ' + (error.message || '未知错误'));

    // 在对话中添加错误消息
    conversation.value.push({ role: 'ai', content: '生成PPT失败: ' + (error.message || '未知错误') });

    isGenerating.value = false;
    isAnalyzing.value = false;

    // 重置知识库状态
    resetKnowledgeState();
  }
}

// Import Lesson Plan
const showImportModal = ref(false)
const importedLessonPlan = ref(null)

const handleLessonPlanImport = (lessonPlan) => {
  // Handle lesson plan import
  importedLessonPlan.value = lessonPlan
}

const removeImportedPlan = () => {
  // Remove imported lesson plan
  importedLessonPlan.value = null
}

// 功能列表
const functions = [
  { type: 'deep', label: '知识库', icon: 'Reading' },
  { type: 'web', label: '教案', icon: 'Search' }
]

// Upload file
const uploadInput = ref(null)
const selectedTeachingDuration = ref('45')
const selectedTeachingStyle = ref('balanced')
const specificRequirements = ref('')

const showNotificationMessage = (message) => {
  notificationMessage.value = message
  showNotification.value = true
  setTimeout(() => {
    showNotification.value = false
  }, 3000)
}

const handleFileUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    alert('文件大小不能超过10MB')
    return
  }

  // 将文件信息存储到导入的教案中
  importedLessonPlan.value = {
    name: file.name,
    points: Math.floor(Math.random() * 20) + 30, // 随机生成30-50个知识点，实际项目中应该计算实际知识点
    size: file.size,
    file: file // 保存文件对象
  }

  showNotificationMessage('教案文件上传成功')

  // 清空文件输入框
  if (uploadInput.value) {
    uploadInput.value.value = ''
  }
}

// 生命周期钩子
onMounted(() => {
  // 加载知识库分类
  loadKnowledgeCategories()

  // 加载学科列表
  fetchSubjects()

  // 尝试恢复之前的编辑状态
  try {
    const savedData = localStorage.getItem('pptEditorData')
    if (savedData) {
      const data = JSON.parse(savedData)
      slides.value = data.slides || []
      importedLessonPlan.value = data.importedLessonPlan
      if (data.settings) {
        selectedTeachingDuration.value = data.settings.teachingDuration
        selectedTeachingStyle.value = data.settings.teachingStyle
        specificRequirements.value = data.settings.specificRequirements
      }
    }
  } catch (error) {
    console.error('恢复编辑状态失败:', error)
  }
})

// Notification
const showNotification = ref(false)
const notificationMessage = ref('')

const showSuccessNotice = ref(false)
const isAnalyzing = ref(false)

// 添加新的副标题和内容组合
const addSection = (index) => {
  slides.value[index].sections.push({
    subtitle: '', // 与后端返回的字段保持一致
    content: ''   // 与后端返回的字段保持一致
  })
}

// 删除副标题和内容组合
const removeSection = (slideIndex, sectionIndex) => {
  slides.value[slideIndex].sections.splice(sectionIndex, 1)
}

// 拖拽文件上传逻辑
const dragover = ref(false)
const onFileDrop = (event) => {
  event.preventDefault()
  const files = event.dataTransfer.files
  handleFileUpload({ target: { files } })
}

// 知识库文档相关
const knowledgeTab = ref('categories')
const searchQuery = ref('')
const filterType = ref('')
const filterSubject = ref('')
const selectedDatasets = ref([])
const selectedDocuments = ref([])
const loadingCategories = ref(false)
const loadingDatasets = ref(false)
const loadingDocuments = ref(false)
const knowledgeError = ref('')

// 知识库数据
const categories = ref([])
const datasets = ref([])
const documents = ref([])
const selectedCategory = ref(null)

// 学科相关
const subjectList = ref([])
const selectedSubject = ref(null)
const loadingSubjects = ref(false)

// 获取学科列表
const fetchSubjects = async () => {
  loadingSubjects.value = true;
  try {
    const response = await pptApi.getSubjects()
    if (response.code === 200 || response.code === 0) {
      subjectList.value = response.data || []
      // 默认选择第一个学科
      if (subjectList.value.length > 0 && !selectedSubject.value) {
        selectedSubject.value = subjectList.value[0].id
      }
    } else {
      console.error('获取学科列表失败:', response.message)
    }
  } catch (error) {
    console.error('获取学科列表失败:', error)
  } finally {
    loadingSubjects.value = false;
  }
}

// 修改loadKnowledgeCategories函数以确保实际调用API
const loadKnowledgeCategories = async () => {
  if (loadingCategories.value) return false; // 防重复调用
  loadingCategories.value = true;
  knowledgeError.value = '';
  try {
    console.log('正在获取知识库分类数据...');
    const response = await knowledgeApi.getAllCategories();
    console.log('知识库分类数据:', response);
    if (response.code === 0 || response.code === 200) {
      categories.value = response.data || [];
      return true;
    } else {
      knowledgeError.value = `获取知识库分类失败: ${response.message || '未知错误'}`;
      ElMessage.error(knowledgeError.value);
      console.error(knowledgeError.value);
      return false;
    }
  } catch (error) {
    knowledgeError.value = `获取知识库分类出错: ${error.message || '未知错误'}`;
    ElMessage.error(knowledgeError.value);
    console.error('获取知识库分类出错:', error);
    return false;
  } finally {
    loadingCategories.value = false;
  }
}

// 修改selectCategory函数
const selectCategory = async (categoryId) => {
  if (selectedCategory.value === categoryId) {
    selectedCategory.value = null
  } else {
    selectedCategory.value = categoryId
    await loadDatasets(categoryId)
  }
}

// 修改loadDatasets函数确保实际调用API
const loadDatasets = async (categoryId) => {
  if (!categoryId || loadingDatasets.value) return false;
  loadingDatasets.value = true;
  knowledgeError.value = '';
  datasets.value = [];
  try {
    console.log(`正在获取分类ID ${categoryId} 的数据集...`);
    const response = await knowledgeApi.getDatasetsByCategory(categoryId);
    console.log('数据集数据:', response);
    if (response.code === 0 || response.code === 200) {
      datasets.value = response.data || [];
      knowledgeTab.value = 'datasets';
      return true;
    } else {
      knowledgeError.value = `获取知识库数据集失败: ${response.message || '未知错误'}`;
      ElMessage.warning(knowledgeError.value);
      console.error(knowledgeError.value);
      return false;
    }
  } catch (error) {
    knowledgeError.value = `获取知识库数据集出错: ${error.message || '未知错误'}`;
    ElMessage.error(knowledgeError.value);
    console.error('获取知识库数据集出错:', error);
    return false;
  } finally {
    loadingDatasets.value = false;
  }
}

// 修改loadDocuments函数确保实际调用API
const loadDocuments = async (datasetId) => {
  if (!datasetId || loadingDocuments.value) return false;
  loadingDocuments.value = true;
  knowledgeError.value = '';
  try {
    console.log(`正在获取数据集ID ${datasetId} 的文档...`);
    const response = await knowledgeApi.getDocumentsByDataset(datasetId);
    console.log('文档数据:', response);
    if (response.code === 0 || response.code === 200) {
      // 修改这里：从response.data.list获取文档数组
      documents.value = response.data.list || [];
      console.log(`成功加载${documents.value.length}个文档，第一个文档名称:`, documents.value[0]?.name);
      knowledgeTab.value = 'documents';
      return true;
    } else {
      knowledgeError.value = `获取知识库文档失败: ${response.message || '未知错误'}`;
      ElMessage.warning(knowledgeError.value);
      console.error(knowledgeError.value);
      return false;
    }
  } catch (error) {
    knowledgeError.value = `获取知识库文档出错: ${error.message || '未知错误'}`;
    ElMessage.error(knowledgeError.value);
    console.error('获取知识库文档出错:', error);
    return false;
  } finally {
    loadingDocuments.value = false;
  }
}

// 切换选择文档
const toggleDocument = (docId) => {
  // 如果没有上传教案，显示提示并阻止选择
  if (!importedLessonPlan.value && !selectedDocuments.value.includes(docId)) {
    ElMessage({
      message: '选择知识库文档必须先上传教案，请先上传教案文件',
      type: 'warning',
      duration: 2500,
      showClose: true
    });

    // 显示模态框引导用户上传教案
    setTimeout(() => {
      showLessonPlanModal.value = true;
    }, 1000);
    return;
  }

  const index = selectedDocuments.value.indexOf(docId);
  if (index === -1) {
    selectedDocuments.value.push(docId);
    // 添加成功反馈
    ElMessage({
      message: '已添加文档到选择列表',
      type: 'success',
      duration: 1000
    });
  } else {
    selectedDocuments.value.splice(index, 1);
    // 添加移除反馈
    ElMessage({
      message: '已从选择列表移除文档',
      type: 'info',
      duration: 1000
    });
  }
};

// 获取数据集名称
const getDatasetName = (id) => {
  const dataset = datasets.value.find(d => d.id === id)
  return dataset ? dataset.name : id
}

// 获取文档名称
const getDocumentName = (id) => {
  const doc = documents.value.find(d => d.id === id)
  return doc ? doc.name : id
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听知识库选项卡变化
watch(knowledgeTab, (newTab) => {
  // 切换到分类tab时，重新加载分类数据
  if (newTab === 'categories' && categories.value.length === 0) {
    loadKnowledgeCategories()
  }
})

// 监听选中的数据集变化
watch(selectedDatasets, (newDatasets) => {
  // 当新选择数据集时，自动加载其文档
  if (newDatasets.length > 0) {
    const latestDatasetId = newDatasets[newDatasets.length - 1]
    loadDocuments(latestDatasetId)
  }
})

// 控制知识库选择弹窗的显示
const showKnowledgeModal = ref(false)
const showLessonPlanModal = ref(false)

const toggleFunction = (type) => {
  if (type === 'deep') {
    showKnowledgeModal.value = true
  } else if (type === 'web') {
    showLessonPlanModal.value = true
  }
}

// 定义编辑状态对象
const editingStates = ref({})

// 设置编辑状态
const setEditingState = (id, isEditing) => {
  if (isGenerating.value) return
  editingStates.value[id] = isEditing
}

// 文多多AiPPT相关
const showDocmeeModal = ref(false)
const docmeeEditorRef = ref(null)
const exportedMarkdown = ref('')
const docmeeEditMode = ref(false)
const docmeePptId = ref('')

// 处理文多多AiPPT编辑器模态框的成功事件
const handleDocmeeSuccess = (message) => {
  console.log('DocmeePPT事件成功:', message)
  showNotificationMessage('PPT内容生成成功')
}

// 处理文多多AiPPT编辑器模态框的错误事件
const handleDocmeeError = (errorMsg) => {
  console.error('DocmeePPT错误:', errorMsg)
  errorMessage.value = errorMsg
}

// 处理文多多AiPPT编辑器模态框的关闭事件
const handleDocmeeClose = () => {
  console.log('DocmeePPT模态框已关闭')
}

// 处理文多多AiPPT生成完毕扣费事件
const handleDocmeeCharge = async (data) => {
  try {
    console.log('处理PPT生成扣费事件:', data)
    if (data && data.pptId) {
      // 保存生成的PPT ID，以便后续编辑
      docmeePptId.value = data.pptId

      // 获取当前选择的学科ID，默认使用第一个可用学科
      const subjectId = selectedSubject.value

      if (!subjectId) {
        console.error('保存PPT ID失败: 未选择学科')
        errorMessage.value = '保存PPT失败: 未选择学科'
        return
      }

      // 保存PPT ID到数据库
      const response = await pptApi.savePptId({
        ppt_id: data.pptId,
        title: pptTitle.value || '新建PPT',
        subject_id: subjectId,
        description: specificRequirements.value || null
      })

      if (response && response.code === 200) {
        console.log('PPT ID保存成功:', response.data)
        showNotificationMessage('PPT保存成功')

        // 切换到编辑模式
        docmeeEditMode.value = true
      } else {
        console.error('PPT ID保存失败:', response.message)
        errorMessage.value = `保存PPT失败: ${response.message || '未知错误'}`
      }
    }
  } catch (error) {
    console.error('保存PPT ID失败:', error)
    errorMessage.value = `保存PPT失败: ${error.message || '未知错误'}`
  }
}

// 添加用于存储任务返回的markdown的响应式变量
const taskMarkdown = ref('')

// 添加goToDocmeeEditor方法
const goToDocmeeEditor = async (pptId) => {
  if (pptId) {
    // 编辑模式
    // 跳转到编辑器页面，携带必要的查询参数
    router.push({
      path: '/teacher/ppt/editor',
      query: {
        editMode: 'true',
        pptId: pptId,
        title: pptTitle.value,
        subjectId: selectedSubject.value
      }
    })
    return
  }

  // 创建模式
  if (!pptTitle.value.trim()) {
    // 如果没有标题，使用当前时间作为默认标题
    pptTitle.value = '电商市场数据分析' + new Date().toLocaleString()
  }

  // 直接使用taskMarkdown，不做任何转换
  const markdownContent = taskMarkdown.value || ''

  console.log('准备跳转到编辑器，标题:', pptTitle.value)
  console.log('Markdown内容长度:', markdownContent.length)

  // 跳转到编辑器页面
  router.push({
    path: '/teacher/ppt/editor',
    query: {
      editMode: 'false',
      title: pptTitle.value,
      subjectId: selectedSubject.value,
      description: specificRequirements.value,
      markdown: encodeURIComponent(markdownContent)
    }
  })
}

// 添加方法解析大纲并更新slides数组
const updateSlidesFromMarkdown = (content) => {
  try {
    if (!content) return;

    // 解析markdown格式（## 为主标题，### 为子标题）
    const lines = content.split('\n').filter(line => line.trim());
    const newSlides = [];

    let currentSlide = null;

    for (const line of lines) {
      // 检查是否为主标题行（格式如 "## 标题"）
      const mainTitleMatch = line.match(/^##\s+(.+)$/);
      if (mainTitleMatch) {
        // 如果已有当前slide，先添加到数组
        if (currentSlide) {
          newSlides.push(currentSlide);
        }

        // 创建新的slide
        currentSlide = {
          title: mainTitleMatch[1],
          sections: []
        };
        continue;
      }

      // 检查是否为子标题行（格式如 "### 子标题"）
      const subTitleMatch = line.match(/^###\s+(.+)$/);
      if (subTitleMatch && currentSlide) {
        currentSlide.sections.push({
          subtitle: subTitleMatch[1],
          content: ''
        });
      }
    }

    // 添加最后一个slide
    if (currentSlide) {
      newSlides.push(currentSlide);
    }

    // 更新slides
    if (newSlides.length > 0) {
      slides.value = newSlides;
      // 更新当前幻灯片信息
      currentSlideInfo.value = `已生成 ${slides.value.length} 张幻灯片`;
    }
  } catch (error) {
    console.error('解析内容失败:', error);
  }
}

// 解析包含代码块的消息
const parseMessageWithCodeBlocks = (message) => {
  // 如果消息中包含"完整内容:"和代码块，特殊处理
  if (message.includes('完整内容:') && message.includes('```')) {
    const parts = []

    // 分离常规文本和代码块
    const splitIndex = message.indexOf('完整内容:')
    if (splitIndex >= 0) {
      // 添加常规文本部分
      parts.push({
        type: 'text',
        content: message.substring(0, splitIndex + '完整内容:'.length)
      })

      // 添加代码块部分（原始markdown）
      const codeStart = message.indexOf('```', splitIndex)
      const codeEnd = message.lastIndexOf('```')

      if (codeStart >= 0 && codeEnd > codeStart) {
        // 提取代码块内容，不包括```标记
        const codeContent = message.substring(codeStart + 3, codeEnd).trim()
        parts.push({
          type: 'code',
          content: codeContent
        })
      }
    }

    return parts
  }

  // 常规消息直接返回文本部分
  return [{
    type: 'text',
    content: message
  }]
}

// 添加一个变量用于存储当前生成流
const currentGenerationText = ref('');

// 修改处理流式返回的函数
const handleStreamResponse = async (taskId) => {
  try {
    console.log('开始流式获取任务进度，任务ID:', taskId);

    // 使用API方法获取流式响应
    const response = await pptApi.getPPTStream(taskId)

    // 创建Reader
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')

    // 添加AI消息到对话，初始化生成流
    currentGenerationText.value = '';
    conversation.value.push({
      role: 'ai',
      content: '正在生成PPT内容，请稍候...'
    })

    // 添加计数器，用于调试
    let chunkCount = 0;
    let markdownReceived = false;

    // 用于打字机效果的变量
    let typewriterQueue = [];
    let isTyping = false;

    // 打字机效果函数
    const typewriterEffect = async (text) => {
      if (isTyping) {
        // 如果正在打字，将新文本加入队列
        typewriterQueue.push(text);
        return;
      }

      isTyping = true;

      // 获取最后一条AI消息
      const lastMsg = conversation.value[conversation.value.length - 1];
      if (!lastMsg || lastMsg.role !== 'ai') {
        // 如果没有AI消息，创建一个
        conversation.value.push({
          role: 'ai',
          content: ''
        });
      }

      // 逐字添加文本，模拟打字效果
      for (let i = 0; i < text.length; i++) {
        // 更新最后一条AI消息的内容
        const lastMsg = conversation.value[conversation.value.length - 1];
        if (lastMsg && lastMsg.role === 'ai') {
          // 添加一个字符
          lastMsg.content += text[i];

          // 确保滚动到最新消息
          await nextTick();
          const container = document.querySelector('.conversation-content');
          if (container) {
            container.scrollTop = container.scrollHeight;
          }

          // 添加随机延迟，模拟真实打字速度
          // 10-30ms的随机延迟
          await new Promise(resolve => setTimeout(resolve, Math.random() * 20 + 10));
        }
      }

      isTyping = false;

      // 处理队列中的下一个文本
      if (typewriterQueue.length > 0) {
        const nextText = typewriterQueue.shift();
        typewriterEffect(nextText);
      }
    };

    // 处理流式响应
    while (true) {
      const { done, value } = await reader.read()

      if (done) {
        console.log('流处理完成，共接收', chunkCount, '个数据块，markdown接收状态:', markdownReceived);
        // 流处理完成，标记状态
        isGenerating.value = false
        isAnalyzing.value = false
        showSuccessNotice.value = true


        // 如果没有收到markdown内容，添加提示消息
        if (!markdownReceived && taskMarkdown.value === '') {
          conversation.value.push({
            role: 'ai',
            content: '注意：未接收到完整的PPT内容，可能生成过程中出现了问题。'
          });
        } else if (markdownReceived) {
          // 添加完成消息
          conversation.value.push({
            role: 'ai',
            content: `PPT内容已完整生成，共${slides.value.length}张幻灯片，点击下方按钮继续`
          });

          // 自动打开文多多编辑器
          nextTick(() => {
            goToDocmeeEditor()
          })
        }

        break
      }

      // 解码二进制数据
      const chunk = decoder.decode(value, { stream: true })
      chunkCount++;

      try {
        // 处理SSE格式的数据
        chunk.split('\n\n').forEach(line => {
          if (line.startsWith('data: ')) {
            try {
              const dataStr = line.substring(6) // 去掉 'data: ' 前缀

              if (dataStr === '[DONE]') {
                console.log('收到流结束标记');
                return  // 流结束标记
              }

              const data = JSON.parse(dataStr)
              console.log('解析SSE数据类型:', Object.keys(data));

              // 处理流式生成的chunk内容
              if (data.chunk) {
                // 使用打字机效果显示内容
                typewriterEffect(data.chunk);

                // 同时累加到当前生成文本中(用于内部追踪)
                currentGenerationText.value += data.chunk;
              }

              // 处理完整markdown内容
              if (data.markdown) {
                // 添加调试日志
                console.log('收到markdown数据，长度:', data.markdown.length, '开头内容:', data.markdown.substring(0, 50) + '...');
                markdownReceived = true;

                // 保存markdown内容
                taskMarkdown.value = data.markdown

                // 从markdown提取标题，如果还没有设置标题
                if (!pptTitle.value) {
                  const titleMatch = data.markdown.match(/^# (.+)$/m);
                  if (titleMatch && titleMatch[1]) {
                    pptTitle.value = titleMatch[1];
                  } else {
                    // 尝试找第一个标题
                    const firstTitleMatch = data.markdown.match(/\n## (.+)$/m);
                    if (firstTitleMatch && firstTitleMatch[1]) {
                      pptTitle.value = firstTitleMatch[1];
                    } else {
                      pptTitle.value = '生成的PPT';
                    }
                  }
                }

                // 从markdown解析slides信息
                updateSlidesFromMarkdown(data.markdown);

                // 发送成功通知
                showNotificationMessage('PPT内容已成功生成，准备跳转到编辑器');
              }

              // 处理进度通知
              if (data.progress) {
                console.log('收到进度通知:', data.progress);
              }

              // 处理章节完成通知
              if (data.section_complete) {
                console.log('章节完成:', data.section_complete);

                // 可以添加章节完成的视觉反馈
                typewriterEffect(`\n\n已完成: ${data.section_complete.title} - ${data.section_complete.subtitle}\n`);
              }

              // 处理幻灯片完成通知
              if (data.slide_complete) {
                console.log('幻灯片完成:', data.slide_complete);

                // 可以添加幻灯片完成的视觉反馈
                typewriterEffect(`\n\n✅ 幻灯片 "${data.slide_complete.title}" 已完成\n\n`);
              }

              // 处理错误信息
              if (data.error) {
                console.error('收到错误信息:', data.error);
                // 直接添加错误消息，不使用打字机效果
                conversation.value.push({
                  role: 'ai',
                  content: `生成失败: ${data.error}`
                });

                isGenerating.value = false
                isAnalyzing.value = false
                errorMessage.value = data.error
              }
            } catch (e) {
              console.error('解析SSE数据出错:', e, '原始数据:', line.substring(6));
            }
          }
        })
      } catch (chunkError) {
        console.error('处理数据块时出错:', chunkError);
      }
    }
  } catch (error) {
    console.error('流式请求失败:', error)
    conversation.value.push({
      role: 'ai',
      content: `连接失败: ${error.message || '未知错误'}`
    });
    isGenerating.value = false
    isAnalyzing.value = false
    errorMessage.value = error.message || '未知错误'
  }
}

// 测试markdown接收函数
const testMarkdownNow = () => {
  console.log('手动测试Markdown接收');
  mockMarkdownTest.value = true;

  // 添加测试消息到对话
  conversation.value.push({ role: 'ai', content: '开始手动测试Markdown接收...' });

  // 模拟接收到markdown数据
  setTimeout(() => {
    // 模拟Markdown内容
    const testMarkdown = `# 手动测试PPT标题

## 第一章 引言
### 引言
- 这是手动测试内容
- 用于验证markdown是否正确接收

## 第二章 主要内容
### 第一节
- 主要内容第一节
### 第二节
- 主要内容第二节
  
## 第三章 总结
### 总结
- 这是测试总结内容`;

    console.log('模拟接收到Markdown数据:', testMarkdown.length);

    // 保存markdown内容
    taskMarkdown.value = testMarkdown;

    // 从markdown解析slides信息
    updateSlidesFromMarkdown(testMarkdown);

    // 添加Markdown预览到对话
    const markdownPreview = testMarkdown.split('\n').slice(0, 15).join('\n') +
      (testMarkdown.split('\n').length > 15 ? '\n...(更多内容已省略)' : '');

    conversation.value.push({
      role: 'ai',
      content: `已生成PPT内容预览:\n\n${markdownPreview}`
    });

    conversation.value.push({
      role: 'ai',
      content: `PPT内容已完整生成，共${slides.value.length}张幻灯片，可点击下方按钮继续`
    });

    showNotificationMessage('手动测试完成：PPT内容已成功生成');
  }, 1000);
}

// 判断是否有用户发送过消息
const hasUserMessage = computed(() => {
  return conversation.value.some(msg => msg.role === 'user')
})

// 添加文件名截断方法
const truncateFileName = (filename, maxLength) => {
  if (!filename) return '';
  if (filename.length <= maxLength) return filename;

  const extension = filename.split('.').pop();
  const name = filename.substring(0, filename.length - extension.length - 1);

  if (extension) {
    // 保留文件扩展名，名称部分截断
    const truncatedName = name.substring(0, maxLength - extension.length - 3) + '...';
    return `${truncatedName}.${extension}`;
  } else {
    // 没有扩展名的情况
    return name.substring(0, maxLength - 3) + '...';
  }
}

// 添加知识库状态重置函数
const resetKnowledgeState = () => {
  // 重置已选择的文档列表
  selectedDocuments.value = [];
  // 可以选择是否重置其他状态，根据需求决定
  // selectedCategory.value = null;
  // knowledgeTab.value = 'categories';

  console.log('已重置知识库选择状态');
};

// 监听学科变化
watch(selectedSubject, (newSubject, oldSubject) => {
  if (newSubject !== oldSubject && newSubject) {
    console.log('已选择学科:', newSubject);
    // 可以在这里添加其他逻辑，如获取与学科相关的推荐模板等
    ElMessage({
      message: `已选择学科: ${subjectList.value.find(s => s.id === newSubject)?.name || ''}`,
      type: 'success',
      duration: 1500
    });
  }
})

</script>

<style scoped>
/* 添加到已有的style标签内的最前面 */
:deep(.el-dialog) {
  margin-top: calc(15vh - 60px) !important;
}

.success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #34D399;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1100;
  transform: translateY(-100px);
  opacity: 0;
  transition: all 0.5s ease;
}

.show-notification {
  transform: translateY(0);
  opacity: 1;
}

.slide-container {
  transition: all 0.3s ease;
}

.slide-container:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.slide-content {
  min-height: 140px;
  resize: vertical;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 拖拽上传区域样式 */
.border-dashed {
  border-style: dashed;
}

.border-dashed:hover {
  border-color: #3B82F6;
}

/* 内联编辑样式 */
input.border-dashed,
textarea.border-dashed {
  transition: all 0.2s ease;
}

input:hover.border-dashed,
textarea:hover.border-dashed {
  border-bottom-color: #e5e7eb;
}

input:focus.border-dashed,
textarea:focus.border-dashed {
  border-bottom-color: #3B82F6;
}

textarea {
  resize: vertical;
  min-height: 60px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.input-area-container {
  width: 100%;
  background-color: #f3f4f6;
  border-radius: 25px;
}

.input-area-box {
  position: relative;
  background: #f3f4f6;
  border-radius: 24px;
  min-height: 72px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: none;
  border: none;
}

.input-textarea {
  flex: 1;
  border: none;
  background: #f3f4f6;
  outline: none;
  resize: none;
  font-size: 18px;
  color: #555;
  padding: 15px 0 5px 0;
  min-height: 58px;
  max-height: 120px;
  line-height: 1.5;
}

.input-placeholder {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #bfc2c9;
  font-size: 17px;
  pointer-events: none;
}

.input-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: #bfc2c9;
  margin-left: 8px;
  transition: color 0.2s;
}

.input-btn:hover {
  color: #2680EB;
}

.attachment-btn {
  margin-right: 4px;
}

.send-btn,
.custom-send-btn,
.disabled-send-btn {
  width: 50px;
  height: 50px;
  min-width: 50px;
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 0;
  outline: none;
  box-shadow: none;
}

.send-btn {
  background: #e5e8ef;
  color: #bfc2c9;
  margin-left: 8px;
  transition: background 0.2s, color 0.2s;
}

.send-btn:hover {
  background: #2680EB;
  color: #fff;
}

.disabled-send-btn {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: #e5e8ef !important;
  color: #bfc2c9 !important;
}

.input-functions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  padding-left: 8px;
}

/* 添加选中文档显示区域的样式 */
.selected-documents {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-document-tag {
  display: inline-flex;
  align-items: center;
  background-color: #e8f0fe;
  border: 1px solid #d0e1fd;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 14px;
  color: #4b5563;
  gap: 6px;
}

.selected-document-tag .remove-btn {
  cursor: pointer;
  color: #6b7280;
  display: inline-flex;
  align-items: center;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s;
}

.selected-document-tag .remove-btn:hover {
  background-color: #d1e5fd;
  color: #4b5563;
}

.input-bottom-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 8px;
  min-height: 72px;
}

.input-bottom-row>div:last-child {
  display: flex;
  align-items: center;
  gap: 2px !important;
  /* padding: 0 10px 5px 0; */
}

.input-bottom-row>div:last-child>button {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 18px !important;
  background: none;
  border: none;
}

.input-bottom-row>div:last-child>button svg {
  width: 32px;
  height: 32px;
  display: block;
}

.custom-send-btn {
  background: none;
  border: none;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-bottom-row>div:last-child>.attachment-btn {
  width: 36px;
  height: 36px;
}

.input-bottom-row>div:last-child>.attachment-btn svg {
  width: 24px;
  height: 24px;
}

.input-functions .el-button {
  border-radius: 18px !important;
  margin-right: -10px !important;
}

.input-bottom-row>div:last-child>button:active,
.input-functions .el-button:active,
.custom-send-btn:active {
  background-color: #dbeafe !important;
}

.custom-send-btn:active svg circle,
.custom-send-btn:focus svg circle {
  fill: #dbeafe !important;
}

:deep(.el-button .el-button__ripple) {
  background-color: transparent !important;
}

.conversation-content {
  background: transparent !important;
}

.shadow-sm {
  box-shadow: none !important;
  padding: 0 20px;
  background-color: #eff6ff;
  color: #000;
  padding: 10px 0;
}

.bg-blue-100 {
  /* max-width: 400px; */
}

/* 消息气泡背景色去除 */
.bg-white {
  /* background: transparent !important; */
}

.avatar-ai-center {
  border: 1px solid #d5e4ff;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  margin-bottom: 10px;
}

:deep(.el-card__body) {
  padding: 0px !important;
}

:deep(.el-card__header) {
  border-bottom: none !important;
  padding: 0px !important;
}

:deep(.p-6) {
  padding-bottom: 20px !important;
}

/* 添加选中项目的样式 */
.selected-items {
  display: flex;
  flex-direction: row;
  gap: 12px;
  padding: 0 8px;
  max-width: 800px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selected-items-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
  line-height: 35px;
}

.selected-items-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-document-tag {
  display: inline-flex;
  align-items: center;
  background-color: #e8f0fe;
  border: 1px solid #d0e1fd;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 14px;
  color: #4b5563;
  gap: 6px;
}

.lesson-plan-info {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  border-left: 1px solid #d0e1fd;
  padding-left: 8px;
}

.selected-document-tag .remove-btn {
  cursor: pointer;
  color: #6b7280;
  display: inline-flex;
  align-items: center;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s;
}

.selected-document-tag .remove-btn:hover {
  background-color: #d1e5fd;
  color: #4b5563;
}

/* Modern Generate Button Styles */
.modern-generate-btn {
  width: 100%;
  max-width: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 18px 0;
  font-size: 1.18rem;
  font-weight: 700;
  border: none;
  border-radius: 24px;
  background: linear-gradient(90deg, #4D6BFE 0%, #7C3AED 100%);
  color: #fff;
  box-shadow: 0 4px 18px 0 rgba(76, 110, 255, 0.18);
  transition: background 0.2s, box-shadow 0.2s, transform 0.15s;
  cursor: pointer;
  margin: 0 auto;
  letter-spacing: 0.02em;
  gap: 12px;
}

.modern-generate-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #5B7CFF 0%, #8B5CF6 100%);
  box-shadow: 0 8px 24px 0 rgba(76, 110, 255, 0.22);
  transform: translateY(-2px) scale(1.02);
}

.modern-generate-btn:active:not(:disabled) {
  background: linear-gradient(90deg, #3B5BFE 0%, #6D28D9 100%);
  box-shadow: 0 2px 8px 0 rgba(76, 110, 255, 0.12);
  transform: scale(0.98);
}

.modern-generate-btn--disabled,
.modern-generate-btn:disabled {
  background: linear-gradient(90deg, #d1d5db 0%, #e5e7eb 100%);
  color: #a1a1aa;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.7;
}

.modern-generate-btn .el-icon {
  font-size: 1.5em;
  display: flex;
  align-items: center;
}

.modern-spin {
  animation: modern-spin 1s linear infinite;
  font-size: 1.5em;
  display: flex;
  align-items: center;
}

@keyframes modern-spin {
  100% {
    transform: rotate(360deg);
  }
}

/* 添加样式以支持markdown渲染 */
.markdown-code {
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  line-height: 1.5;
  font-size: 0.9em;
}

:deep(h1) {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.6em 0 0.3em 0;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.2em;
  color: #24292e;
}

:deep(h2) {
  font-size: 1.3em;
  font-weight: bold;
  margin: 0.6em 0 0.3em 0;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.2em;
  color: #2c3e50;
}

:deep(h3) {
  font-size: 1.2em;
  font-weight: bold;
  margin: 0.5em 0 0.2em 0;
  color: #374151;
}

:deep(h4) {
  font-size: 1.1em;
  font-weight: bold;
  margin: 0.4em 0 0.2em 0;
  color: #4b5563;
}

:deep(ul),
:deep(ol) {
  padding-left: 1.2em;
  margin: 0.4em 0;
}

:deep(li) {
  margin: 0.2em 0;
  position: relative;
  list-style-type: disc;
  font-size: 0.95em;
}

:deep(li p) {
  margin: 0.15em 0;
  display: inline-block;
}

:deep(p) {
  margin: 0.4em 0;
  line-height: 1.5;
  font-size: 0.95em;
}

:deep(code) {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.15em 0.3em;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

:deep(pre code) {
  background-color: transparent;
  padding: 0;
  display: block;
  overflow-x: auto;
  font-size: 0.9em;
}

:deep(blockquote) {
  border-left: 3px solid #ddd;
  padding-left: 0.8em;
  margin-left: 0;
  color: #666;
  background-color: #f8f8f8;
  padding: 0.4em 0.8em;
  border-radius: 0 3px 3px 0;
  font-size: 0.95em;
}

/* 增加滚动条内部内容的最大宽度，避免内容被压缩 */
.conversation-content>div>div {
  max-width: 90%;
  word-break: break-word;
}

/* 调整AI消息的样式 */
.conversation-content .bg-gray-100 {
  max-width: 90%;
  padding: 10px 14px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.avatar-ai-center {
  border: 1px solid #d5e4ff;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

.conversation-content {
  padding: 20px;
}

/* 添加消息气泡样式 */
.rounded-lg {
  border-radius: 12px;
}

/* 用户消息样式 */
.text-right .rounded-lg {
  background-color: #eff6ff;
  margin-left: auto;
}

/* AI消息样式 */
.text-left .rounded-lg {
  background-color: transparent;
}

.truncated-filename {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

/* 添加学科选择下拉框样式 */
.subject-select {
  min-width: 100px;
}

:deep(.subject-select .el-input__wrapper) {
  border-radius: 18px !important;
}

:deep(.subject-select .el-select__popper) {
  border-radius: 12px;
}

/* 确保学科标签和选择框正确并排显示 */
.subject-wrapper {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.subject-wrapper span {
  display: inline-block;
}
</style>