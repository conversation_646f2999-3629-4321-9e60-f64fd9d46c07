# -*- coding: utf-8 -*-
"""
AI讲课服务模块
提供文档处理、大纲生成、内容创建、语音合成等功能
"""

from .orchestrator import AILectureOrchestrator
from .document_processor import DocumentProcessor  
from .outline_generator import OutlineGenerator
from .content_creator import ContentCreator
from .speech_synthesizer import SpeechSynthesizer
from .file_manager import FileManager
from .subtitle_analyzer import SubtitleAnalyzer
from .exceptions import (
    AILectureException,
    DocumentProcessingException,
    OutlineGenerationException,
    ContentCreationException,
    SpeechSynthesisException,
    FileOperationException
)

__all__ = [
    'AILectureOrchestrator',
    'DocumentProcessor',
    'OutlineGenerator', 
    'ContentCreator',
    'SpeechSynthesizer',
    'FileManager',
    'SubtitleAnalyzer',
    'AILectureException',
    'DocumentProcessingException',
    'OutlineGenerationException',
    'ContentCreationException',
    'SpeechSynthesisException',
    'FileOperationException'
] 