import traceback
from typing import Dict, List, Any, Generator
import copy
import json
import random

from ..entitys.course import Lesson

class VideoAssistantService:
    """视频助手服务类，提供视频对话相关的业务逻辑"""

    @staticmethod
    def get_video_dialogues(video_id: str) -> list[Any] | list | Any:
        """
        获取视频的预设对话，从3套对话中随机选择一套
        
        Args:
            video_id (str): 视频ID
            
        Returns:
            List[Dict]: 包含视频随机选择的一套对话数据
        """
        try:
            lesson = Lesson.objects.get(id=video_id)
            
            if not lesson.mock_dialogue_data:
                return []
                
            # 检查数据结构是否为新结构（包含dialogue_sets）
            if isinstance(lesson.mock_dialogue_data, dict) and 'dialogue_sets' in lesson.mock_dialogue_data:
                dialogue_sets = lesson.mock_dialogue_data.get('dialogue_sets', [])
                
                # 过滤掉空的对话集
                valid_dialogue_sets = [s for s in dialogue_sets if s]
                
                if not valid_dialogue_sets:
                    return []
                    
                # 随机选择一套对话返回
                random_index = random.randint(0, len(valid_dialogue_sets) - 1)
                print(f"从{len(valid_dialogue_sets)}套对话中随机选择第{random_index + 1}套")
                return valid_dialogue_sets[random_index]
            else:
                # 兼容旧的数据结构
                return lesson.mock_dialogue_data
        except Lesson.DoesNotExist:
            # 如果找不到对应的课时，返回空列表
            return []
        except Exception as e:
            traceback.print_exc()
            raise e
            
    @staticmethod
    def get_video_navigation(video_id: str) -> Dict[str, Any]:
        """
        获取视频的导航数据（关键点和摘要）
        
        Args:
            video_id (str): 视频ID
            
        Returns:
            Dict[str, Any]: 包含视频导航数据，但已移除video_info字段
        """
        try:
            lesson = Lesson.objects.get(id=video_id)
            
            if lesson.mock_navigation_data:
                # 使用深拷贝避免修改原始数据
                navigation_data = copy.deepcopy(lesson.mock_navigation_data)

                return navigation_data
            
            # 如果数据不存在，返回空的导航数据结构
            return {"navigation": {"key_points": [], "summary": ""}}
        except Lesson.DoesNotExist:
            # 如果找不到对应的课时，返回空的导航数据结构
            return {"navigation": {"key_points": [], "summary": ""}}
        except Exception as e:
            traceback.print_exc()
            raise e
            
    @staticmethod
    def get_video_teacher_info(video_id: str) -> Dict[str, Any]:
        """
        获取视频关联的教师信息
        
        Args:
            video_id (str): 视频ID
            
        Returns:
            Dict[str, Any]: 包含视频教师信息的数据
        """
        try:
            lesson = Lesson.objects.get(id=video_id)
            
            # 获取章节信息
            chapter = lesson.chapter
            if not chapter:
                return {"teacher": None}
                
            # 获取课程信息
            course = chapter.course
            if not course:
                return {"teacher": None}
                
            # 获取教师信息
            teacher = course.teacher
            if not teacher:
                return {"teacher": None}
                
            # 获取用户信息
            user = teacher.user
            
            # 返回教师信息
            teacher_info = {
                "id": teacher.id,
                "teacher_name": teacher.title,
                "introduction": teacher.introduction,
                "avatar": user.avatar.url if user.avatar else None,
                "college": teacher.college.name if teacher.college else None
            }
            
            return {"teacher": teacher_info}
        except Lesson.DoesNotExist:
            # 如果找不到对应的课时，返回空的教师数据
            return {"teacher": None}
        except Exception as e:
            traceback.print_exc()
            raise e

    @staticmethod
    def chat_with_ai_stream(message: str, history: List[Dict] = None, video_id: str = None, current_video_time: str = None) -> Generator[str, None, None]:
        """
        与AI助手进行流式对话
        
        Args:
            message (str): 用户消息
            history (List[Dict], optional): 历史消息列表，包含role和content字段
            video_id (str, optional): 视频ID，用于获取字幕数据作为上下文
            current_video_time (str, optional): 当前视频播放时间，格式为"MM:SS"
        
        Returns:
            Generator: 生成AI响应的流
        """
        try:
            from ..utils.deepseek_api import DeepSeekAPI
            
            api = DeepSeekAPI()
            messages = []
            
            # 添加系统提示语
            system_message = "你是一位专业的AI学习助手，帮助学生理解和学习课程内容。请给出简洁、准确、有帮助的回答。"
            
            # 如果提供了视频ID，获取字幕数据作为上下文
            if video_id:
                try:
                    lesson = Lesson.objects.get(id=video_id)
                    if lesson.subtitle_data:
                        # 提取字幕文本并格式化为上下文
                        subtitle_texts = []
                        for subtitle in lesson.subtitle_data:
                            if isinstance(subtitle, dict) and 'text' in subtitle:
                                time_info = f"[{subtitle.get('start_time_str', '')} - {subtitle.get('end_time_str', '')}]"
                                subtitle_texts.append(f"{time_info} {subtitle['text']}")
                        
                        if subtitle_texts:
                            subtitle_context = "以下是视频内容的字幕：\n" + "\n".join(subtitle_texts)
                            system_message += f"""\n\n{subtitle_context}\n\n指导原则:
1. 保持回答简洁，控制在150字以内
2. 使用第一人称回答，像真人教师一样交流
3. 偶尔可以使用表情符号增加亲和力
4. 当不确定答案时，承认不确定性并提供与视频内容相关的建议
5. 请使用时间导航命令帮助学生跳转到视频的相关部分，格式为 : " [跳转至 MM:SS] " 
6. 避免过于机械的回复，加入一些口语化的表达;
你的角色是王老师；
你需要根据视频内容回答问题，并使用视频内容作为参考资料。
当前学生学习视频的时间到达了{current_video_time}
请直接回答问题，开头不要增加 xxx： （比如  王老师： ）
"""
                except Lesson.DoesNotExist:
                    pass
                except Exception as e:
                    print(f"获取字幕数据失败: {str(e)}")
            
            messages.append(api.create_system_message(system_message))

            # 添加历史消息
            if history:
                for msg in history:
                    role = msg.get('role', '')
                    content = msg.get('content', '')
                    
                    if role == 'user':
                        messages.append(api.create_user_message(content))
                    elif role == 'assistant':
                        messages.append(api.create_assistant_message(content))
            
            
            
            # 使用流式API获取响应
            for chunk in api.chat_stream(messages):
                if chunk:
                    yield f"data: {json.dumps({'content': chunk.get('content')})}\n\n"
            
        except Exception as e:
            error_msg = str(e)
            traceback_info = traceback.format_exc()
            print(traceback_info)
            
            # 返回错误消息
            yield f"data: {json.dumps({'error': error_msg})}\n\n"
