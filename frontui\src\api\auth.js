import { post, get } from '@/utils/request'

// 认证相关API
export const authApi = {
  // 登录
  login: (username, password) => {
    return post('users/login/', { username, password })
  },

  // 注册
  register: (data) => {
    return post('users/', data)
  },
  
  // 刷新token
  refresh: (refreshToken) => {
    return post('token/refresh/', { refresh: refreshToken })
  },
  
  // 获取用户信息
  getUserInfo: () => {
    return get('users/me/')
  }
} 