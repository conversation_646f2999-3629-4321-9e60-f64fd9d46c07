from django.utils import timezone
from rest_framework.exceptions import ValidationError

from ..entitys import AILectureDocument, AILectureChapter, AILectureKeyPoint
from ..entitys import AILectureSpeechContent, AILectureHtmlContent, AILectureSpeechStyle
from ..utils import temp_file_utils
from ..utils.file_utils import FileUtils
from ..tasks.ai_lecture_tasks import process_ai_lecture_chapter_task, process_ai_lecture_chapter_with_style_task, \
    process_ai_outline_with_first_chapter_task, process_ai_outline_with_first_chapter_gemini_task, \
    process_chapter_gemini_audio_task
from ..config import DEFAULT_LANGUAGE


class BookshelfService:
    @staticmethod
    def get_user_bookshelf(user_id, language=DEFAULT_LANGUAGE):
        """
        获取用户的书架文档列表，按语言筛选
        """
        return AILectureDocument.objects.filter(
            user_id=user_id, 
            deleted_at__isnull=True,
            language_code=language
        ).order_by('-created_at')

    @staticmethod
    def add_document_to_bookshelf(user_id, upload_file, style_code='classroom', language=DEFAULT_LANGUAGE):
        """
        将文档添加到书架并设置讲课风格和语言
        
        参数:
            user_id: 用户ID
            upload_file: 上传的文件对象
            style_code: 讲课风格代码，默认为'classroom'(课堂风格)
            language: 文档语言，默认为系统默认语言
            
        返回:
            AILectureDocument: 创建的文档对象
        """
        # 使用自定义临时文件工具保存上传内容
        suffix = '.' + upload_file.name.split('.')[-1] if '.' in upload_file.name else ''
        ext = upload_file.name.split('.')[-1].lower() if '.' in upload_file.name else ''
        if ext not in ['pdf', 'docx']:
            raise ValidationError('只支持上传pdf和docx文件')
        temp_path = temp_file_utils.get_temp_filepath(suffix)
        with open(temp_path, 'wb+') as tmp:
            for chunk in upload_file.chunks():
                tmp.write(chunk)
        file_path = FileUtils.save_knowledge_file(temp_path)
        file_type = ext
        doc = AILectureDocument.objects.create(
            user_id=user_id,
            title=upload_file.name,
            file_path=file_path,
            file_type=file_type,
            speech_style=style_code,  # 设置文档的讲课风格，将影响AI生成的讲课内容的风格
            language_code=language     # 设置文档的语言
        )
        # 删除临时文件
        temp_file_utils.clean_temp_file(temp_path)
        # 调用Celery任务进行AI讲课文档处理（生成大纲和第一章内容）
        process_ai_outline_with_first_chapter_gemini_task.delay(doc.id)
        return doc

    @staticmethod
    def delete_document_from_bookshelf(user_id, doc_id):
        """
        从书架中删除文档（逻辑删除并物理删除相关文件）
        同时级联逻辑删除与该文档相关的所有数据：章节、要点、HTML内容和语音内容
        并物理删除所有相关文件
        """
        doc = AILectureDocument.objects.get(id=doc_id, user_id=user_id)
        now = timezone.now()
        
        # 收集需要物理删除的文件路径
        files_to_delete = []
        
        # 添加文档主文件、内容列表文件和封面图片
        if doc.file_path:
            files_to_delete.append(doc.file_path)
        if doc.content_list_file_path:
            files_to_delete.append(doc.content_list_file_path)
        if doc.cover_image_path:
            files_to_delete.append(doc.cover_image_path)
        
        # 1. 先获取所有相关章节
        chapters = AILectureChapter.objects.filter(document=doc, deleted_at__isnull=True)
        chapter_ids = [chapter.id for chapter in chapters]
        
        # 2. 获取所有相关要点
        key_points = AILectureKeyPoint.objects.filter(chapter_id__in=chapter_ids, deleted_at__isnull=True)
        key_point_ids = [kp.id for kp in key_points]
        
        # 3. 获取并逻辑删除HTML内容，同时收集HTML文件路径
        html_contents = AILectureHtmlContent.objects.filter(
            key_point_id__in=key_point_ids, 
            deleted_at__isnull=True
        )
        
        for html_content in html_contents:
            if html_content.html_file_path:
                files_to_delete.append(html_content.html_file_path)
        
        html_contents.update(deleted_at=now)
        
        # 4. 获取并逻辑删除语音内容，同时收集音频文件路径
        speech_contents = AILectureSpeechContent.objects.filter(
            key_point_id__in=key_point_ids, 
            deleted_at__isnull=True
        )
        
        for speech_content in speech_contents:
            if speech_content.audio_file_path:
                files_to_delete.append(speech_content.audio_file_path)
        
        speech_contents.update(deleted_at=now)
        
        # 5. 逻辑删除要点
        key_points.update(deleted_at=now)
        
        # 6. 逻辑删除章节
        chapters.update(deleted_at=now)
        
        # 7. 最后逻辑删除文档本身
        doc.deleted_at = now
        doc.save()
        
        # 8. 物理删除所有收集到的文件
        deletion_results = []
        for file_path in files_to_delete:
            try:
                result = FileUtils.delete_file(file_path)
                deletion_results.append((file_path, result))
            except Exception as e:
                # 记录删除失败，但不中断流程
                deletion_results.append((file_path, False))
                print(f"删除文件失败: {file_path}, 错误: {str(e)}")
        
        return doc, deletion_results

    @staticmethod
    def delete_documents_from_bookshelf(user_id, doc_ids):
        """
        批量删除书架文档（逻辑删除并物理删除相关文件）
        """
        if not doc_ids:
            raise ValidationError('未提供要删除的文档ID')
        
        # 验证所有文档都属于当前用户
        docs = AILectureDocument.objects.filter(
            id__in=doc_ids, 
            user_id=user_id, 
            deleted_at__isnull=True
        )
        
        found_ids = [str(doc.id) for doc in docs]
        not_found_ids = [str(doc_id) for doc_id in doc_ids if str(doc_id) not in found_ids]
        
        if not_found_ids:
            raise ValidationError(f"以下文档不存在或不属于当前用户: {', '.join(not_found_ids)}")
        
        now = timezone.now()
        all_files_to_delete = []
        deleted_docs = []
        
        # 批量处理所有文档
        for doc in docs:
            try:
                # 收集每个文档的文件路径
                files_to_delete = []
                
                # 添加文档主文件、内容列表文件和封面图片
                if doc.file_path:
                    files_to_delete.append(doc.file_path)
                if doc.content_list_file_path:
                    files_to_delete.append(doc.content_list_file_path)
                if doc.cover_image_path:
                    files_to_delete.append(doc.cover_image_path)
                
                # 1. 获取所有相关章节
                chapters = AILectureChapter.objects.filter(document=doc, deleted_at__isnull=True)
                chapter_ids = [chapter.id for chapter in chapters]
                
                # 2. 获取所有相关要点
                key_points = AILectureKeyPoint.objects.filter(chapter_id__in=chapter_ids, deleted_at__isnull=True)
                key_point_ids = [kp.id for kp in key_points]
                
                # 3. 收集HTML文件路径并逻辑删除HTML内容
                html_contents = AILectureHtmlContent.objects.filter(
                    key_point_id__in=key_point_ids, 
                    deleted_at__isnull=True
                )
                
                for html_content in html_contents:
                    if html_content.html_file_path:
                        files_to_delete.append(html_content.html_file_path)
                
                html_contents.update(deleted_at=now)
                
                # 4. 收集音频文件路径并逻辑删除语音内容
                speech_contents = AILectureSpeechContent.objects.filter(
                    key_point_id__in=key_point_ids, 
                    deleted_at__isnull=True
                )
                
                for speech_content in speech_contents:
                    if speech_content.audio_file_path:
                        files_to_delete.append(speech_content.audio_file_path)
                
                speech_contents.update(deleted_at=now)
                
                # 5. 逻辑删除要点
                key_points.update(deleted_at=now)
                
                # 6. 逻辑删除章节
                chapters.update(deleted_at=now)
                
                # 7. 最后逻辑删除文档本身
                doc.deleted_at = now
                doc.save()
                
                deleted_docs.append(doc)
                all_files_to_delete.extend(files_to_delete)
                
            except Exception as e:
                print(f"处理文档 {doc.id} 失败: {str(e)}")
                continue
        
        # 8. 批量物理删除所有收集到的文件
        deletion_results = []
        for file_path in all_files_to_delete:
            try:
                result = FileUtils.delete_file(file_path)
                deletion_results.append((file_path, result))
            except Exception as e:
                # 记录删除失败，但不中断流程
                deletion_results.append((file_path, False))
                print(f"删除文件失败: {file_path}, 错误: {str(e)}")
        
        # 统计删除结果
        total_files = len(deletion_results)
        failed_files = [path for path, result in deletion_results if not result]
        
        return {
            'deleted_count': len(deleted_docs),
            'deleted_ids': [doc.id for doc in deleted_docs],
            'total_files_processed': total_files,
            'failed_files_count': len(failed_files),
            'failed_files': failed_files
        }

    @staticmethod
    def get_document_chapters(doc_id, language=DEFAULT_LANGUAGE):
        """
        获取文档的章节和分页信息，每章返回标题、起始页、结束页。
        按语言筛选章节。
        返回：[{chapter_title, start_page, end_page, chapter_order}]
        """
        try:
            # 获取文档信息
            document = AILectureDocument.objects.get(id=doc_id, deleted_at__isnull=True)
            document_title = document.title
            
            chapters = AILectureChapter.objects.filter(
                document_id=doc_id, 
                deleted_at__isnull=True,
                language_code=language
            ).order_by('chapter_order')
            
            result = []
            for chapter in chapters:
                result.append({
                    'chapter_id': chapter.id,
                    'chapter_title': chapter.chapter_title,
                    'start_page': chapter.start_page,
                    'end_page': chapter.end_page,
                    'chapter_order': chapter.chapter_order,
                    'status': chapter.status,  # 添加状态字段
                    'document_title': document_title  # 添加文档标题
                })
            return result
        except AILectureDocument.DoesNotExist:
            return []

    @staticmethod
    def get_chapter_key_points(chapter_id, language=DEFAULT_LANGUAGE):
        """
        获取指定章节下的所有要点（分页用），按语言筛选
        返回：[{key_point_id, title, point_order, audio_status}]
        """
        key_points = AILectureKeyPoint.objects.filter(
            chapter_id=chapter_id, 
            deleted_at__isnull=True,
            language_code=language
        ).order_by('point_order')
        
        return [
            {
                'key_point_id': kp.id,
                'title': kp.original_point[:30],
                'point_order': kp.point_order,
                'audio_status': kp.audio_status  # 添加音频状态
            } for kp in key_points
        ]

    @staticmethod
    def get_html_content_by_page(doc_id, page=None, key_point_id=None, language=DEFAULT_LANGUAGE):
        """
        获取指定文档第page页或指定要点的HTML内容，按语言筛选
        """
        if key_point_id:
            try:
                kp = AILectureKeyPoint.objects.get(
                    id=key_point_id, 
                    chapter__document_id=doc_id, 
                    deleted_at__isnull=True, 
                    chapter__deleted_at__isnull=True,
                    language_code=language
                )
            except AILectureKeyPoint.DoesNotExist:
                return None, None
        else:
            key_points = AILectureKeyPoint.objects.filter(
                chapter__document_id=doc_id, 
                deleted_at__isnull=True, 
                chapter__deleted_at__isnull=True,
                language_code=language
            ).order_by('chapter__chapter_order', 'point_order')
            
            if page is None or page < 1 or page > key_points.count():
                return None, None
            kp = key_points[page-1]
            
        html_obj = getattr(kp, 'html_content', None)
        if not html_obj or not html_obj.html_file_path:
            return None, None
        try:
            html_bytes = FileUtils.download_from_oss(html_obj.html_file_path)
            html_str = html_bytes.decode('utf-8') if isinstance(html_bytes, bytes) else html_bytes
        except Exception as e:
            html_str = f'<div style="color:red">HTML加载失败: {e}</div>'
        return html_str, kp.id

    @staticmethod
    def get_key_point_audios(key_point_id, language=DEFAULT_LANGUAGE):
        """
        获取指定要点下所有音频及字幕，按顺序返回，按语言筛选
        返回：[{audio_url, text, sentence_order}]
        """
        audios = AILectureSpeechContent.objects.filter(
            key_point_id=key_point_id, 
            deleted_at__isnull=True,
            audio_file_path__isnull=False,  # 确保音频文件路径不为空
            language_code=language
        ).exclude(audio_file_path='').order_by('sentence_order')  # 排除空字符串
        
        result = []
        for audio in audios:
            if audio.audio_file_path:  # 双重检查音频文件路径存在
                result.append({
                    'audio_url': FileUtils.get_file_url(audio.audio_file_path),
                    'text': audio.speech_script,
                    'sentence_order': audio.sentence_order,
                    'speech_id': audio.id  # 添加语音ID，用于获取字幕
                })
        return result

    @staticmethod
    def get_speech_subtitles(speech_content_id, language=DEFAULT_LANGUAGE):
        """
        获取指定音频内容的字幕列表，按语言筛选
        返回：[{start_time, end_time, subtitle_text}]
        """
        from ..entitys.ai_lecture import AILectureSubtitle
        
        try:
            # 查询对应的字幕数据
            subtitles = AILectureSubtitle.objects.filter(
                speech_content_id=speech_content_id,
                deleted_at__isnull=True,
                language_code=language
            ).order_by('start_time')
            
            result = []
            for subtitle in subtitles:
                result.append({
                    'start_time': subtitle.start_time,  # 毫秒
                    'end_time': subtitle.end_time,      # 毫秒
                    'text': subtitle.subtitle_text
                })
                
            return result
        except Exception as e:
            print(f"获取字幕失败: {str(e)}")
            return []

    @staticmethod
    def check_chapter_status(chapter_id):
        """
        检查章节生成状态
        返回: {status, key_points_count} 
        - key_points_count: 音频已完成的要点数
        """
        try:
            chapter = AILectureChapter.objects.get(id=chapter_id, deleted_at__isnull=True)
            
            # 获取该章节下的所有要点
            all_key_points = AILectureKeyPoint.objects.filter(
                chapter_id=chapter_id, deleted_at__isnull=True
            )
            
            # 统计音频已完成的要点数
            audio_completed_count = 0
            
            for kp in all_key_points:
                # 检查是否存在有效的HTML内容
                has_html = AILectureHtmlContent.objects.filter(
                    key_point=kp, 
                    html_file_path__isnull=False
                ).exclude(html_file_path='').exists()
                
                # 使用要点的音频状态字段
                if kp.audio_status == 'completed' and has_html:
                    audio_completed_count += 1
            
            return {
                'status': chapter.status,
                'key_points_count': audio_completed_count,
                'total_key_points': len(all_key_points)
            }
        except AILectureChapter.DoesNotExist:
            return {
                'status': 'not_found',
                'key_points_count': 0,
                'total_key_points': 0
            }

    @staticmethod
    def trigger_chapter_generation(chapter_id):
        """
        触发章节内容生成
        返回: {success, message, task_id}
        """
        try:
            chapter = AILectureChapter.objects.get(id=chapter_id, deleted_at__isnull=True)
            
            # 如果已经完成或正在生成，不需要再次触发
            if chapter.status == 'completed':
                return {
                    'success': True,
                    'message': '章节内容已生成完成',
                    'task_id': None
                }
            
            # 如果正在生成中，返回正在生成的状态
            if chapter.status == 'gen':
                return {
                    'success': True,
                    'message': '章节内容正在生成中',
                    'task_id': None
                }
            
            # 更新状态为生成中
            chapter.status = 'gen'
            chapter.save()
            
            # 触发生成任务
            task = process_chapter_gemini_audio_task.delay(chapter_id)
            
            return {
                'success': True,
                'message': '已触发章节内容生成',
                'task_id': task.id
            }
        except AILectureChapter.DoesNotExist:
            return {
                'success': False,
                'message': '章节不存在',
                'task_id': None
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'触发生成失败: {str(e)}',
                'task_id': None
            }

    @staticmethod
    def get_document_style(doc_id):
        """获取文档当前的语音风格"""
        try:
            doc = AILectureDocument.objects.get(id=doc_id, deleted_at__isnull=True)
            return doc.speech_style
        except AILectureDocument.DoesNotExist:
            return 'classroom'  # 默认风格

    @staticmethod
    def get_available_styles():
        """获取所有可用的语音风格"""
        try:
            styles = AILectureSpeechStyle.objects.filter(deleted_at__isnull=True).order_by('created_at')
            return {style.style_code: style.style_name for style in styles}
        except Exception:
            # 如果数据库中没有风格数据，返回默认风格
            return {
                'classroom': '播客风格'
            }

    @staticmethod
    def get_available_styles_with_descriptions():
        """获取所有可用的语音风格及其描述"""
        try:
            styles = AILectureSpeechStyle.objects.filter(deleted_at__isnull=True).order_by('created_at')
            result = {}
            
            for style in styles:
                result[style.style_code] = {
                    'name': style.style_name,
                    'description': style.description
                }
            
            # 如果数据库中没有数据，至少返回一个默认风格
            if not result:
                result = {
                    'classroom': {
                        'name': '播客风格',
                        'description': '标准教学模式，适合课堂讲授'
                    }
                }
                
            return result
        except Exception:
            # 如果出现异常，返回默认风格
            return {
                'classroom': {
                    'name': '播客风格',
                    'description': '标准教学模式，适合课堂讲授'
                }
            }

    @staticmethod
    def update_document_style(doc_id, style_code):
        """更新文档的语音风格"""
        try:
            # 验证风格是否存在
            if not AILectureSpeechStyle.objects.filter(style_code=style_code, deleted_at__isnull=True).exists():
                # 如果数据库中没有，检查是否是默认支持的风格
                default_styles = ['classroom', 'grumpy', 'storytelling', 'novel', 'memorial']
                if style_code not in default_styles:
                    return False, "无效的风格代码"
            
            doc = AILectureDocument.objects.get(id=doc_id, deleted_at__isnull=True)
            doc.speech_style = style_code
            doc.save()
            return True, "风格更新成功"
        except AILectureDocument.DoesNotExist:
            return False, "文档不存在"
        except Exception as e:
            return False, f"更新失败: {str(e)}"

    @staticmethod
    def trigger_chapter_generation_with_style(chapter_id, style_code):
        """
        使用指定风格重新生成章节的音频内容（仅重新生成音频，不重新生成HTML）
        返回: {success, message, task_id}
        """
        try:
            chapter = AILectureChapter.objects.get(id=chapter_id, deleted_at__isnull=True)
            
            # 先更新文档的风格
            success, message = BookshelfService.update_document_style(chapter.document.id, style_code)
            if not success:
                return {
                    'success': False,
                    'message': message,
                    'task_id': None
                }
            
            # 更新章节状态为生成中
            chapter.status = 'gen'
            chapter.save()
            
            # 触发重新生成音频任务（使用新的风格Celery任务）
            task = process_ai_lecture_chapter_with_style_task.delay(chapter_id, style_code)
            
            return {
                'success': True,
                'message': f'已开始使用 {style_code} 风格重新生成音频内容',
                'task_id': task.id
            }
        except AILectureChapter.DoesNotExist:
            return {
                'success': False,
                'message': '章节不存在',
                'task_id': None
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'操作失败: {str(e)}',
                'task_id': None
            }

    @staticmethod
    def check_document_outline_status(doc_id):
        """
        检查文档大纲生成状态
        返回: {
            'status': 'generating'|'completed'|'not_found', 
            'has_chapters': bool,
            'chapters_count': int,
            'first_chapter_status': str|None
        }
        """
        try:
            doc = AILectureDocument.objects.get(id=doc_id, deleted_at__isnull=True)
            
            # 检查是否有章节（即大纲是否已生成）
            chapters = AILectureChapter.objects.filter(document=doc, deleted_at__isnull=True)
            chapters_count = chapters.count()
            
            if chapters_count == 0:
                # 没有章节，说明大纲还在生成中或还未开始生成
                return {
                    'status': 'generating',
                    'has_chapters': False,
                    'chapters_count': 0,
                    'first_chapter_status': None
                }
            else:
                # 有章节，大纲已生成完成
                first_chapter = chapters.order_by('chapter_order').first()
                return {
                    'status': 'completed',
                    'has_chapters': True,
                    'chapters_count': chapters_count,
                    'first_chapter_status': first_chapter.status if first_chapter else None
                }
                
        except AILectureDocument.DoesNotExist:
            return {
                'status': 'not_found',
                'has_chapters': False,
                'chapters_count': 0,
                'first_chapter_status': None
            }

    @staticmethod
    def get_user_bookshelf_with_status(user_id, language=DEFAULT_LANGUAGE):
        """
        获取用户书架文档列表，包含生成状态信息，按语言筛选
        """
        docs = AILectureDocument.objects.filter(
            user_id=user_id, 
            deleted_at__isnull=True,
            language_code=language
        ).order_by('-created_at')
        
        data = []
        
        for doc in docs:
            # 检查大纲生成状态
            outline_status = BookshelfService.check_document_outline_status(doc.id)
            
            data.append({
                "id": doc.id,
                "name": doc.title,
                "type": doc.file_type,
                "cover_image_path": FileUtils.get_file_url(doc.cover_image_path),
                "file_path": doc.file_path,
                "created_at": doc.created_at,
                "language_code": doc.language_code,  # 添加语言代码字段到返回数据
                "outline_status": outline_status['status'],  # 'generating' 或 'completed'
                "has_chapters": outline_status['has_chapters'],
                "chapters_count": outline_status['chapters_count']
            })
        
        return data 