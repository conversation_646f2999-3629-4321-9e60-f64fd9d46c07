// 导入学生相关视图
import StudentDashboard from '@/views/student/DashboardView.vue'
import StudentCourses from '@/views/student/CoursesView.vue'
import AssignmentsView from '@/views/student/AssignmentsView.vue'
import CourseContentView from '@/views/student/CourseContentView.vue'
import KnowledgeBaseView from '@/views/student/KnowledgeBaseView.vue'
import AiAssistantView from '@/views/student/AiAssistantView.vue'
import PersonalCenterView from '@/views/student/PersonalCenterView.vue'
import StudentSettingsView from '@/views/student/StudentSettingsView.vue'
import StudentProfileView from '@/views/student/StudentProfileView.vue'
import PointsMallView from '@/views/student/PointsMallView.vue'
import AiLearningView from '@/views/student/AiLearningView.vue'
import TodayLearnView from '@/views/student/TodayLearnView.vue'
import BookshelfView from '@/views/student/BookshelfView.vue'
import StudentHtmlPreviewView from '@/views/student/StudentHtmlPreviewView.vue'
import AssignmentDetailView from '@/views/student/AssignmentDetailView.vue'
import AssignmentGradingView from '@/views/student/AssignmentGradingView.vue'
import AssignmentSubmissionView from '@/views/student/AssignmentSubmissionView.vue'
// 学生相关路由配置
const studentRoutes = [
  {
    path: '/student',
    name: 'student',
    component: StudentDashboard,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/dashboard',
    name: 'student-dashboard',
    component: StudentDashboard,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/courses',
    name: 'student-courses',
    component: StudentCourses,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/assignments',
    name: 'student-assignments',
    component: AssignmentsView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/assignment/:id',
    name: 'student-assignment-detail',
    component: AssignmentDetailView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/course/:id',
    name: 'student-course-content',
    component: CourseContentView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/ai-assistant',
    name: 'student-ai-assistant',
    component: AiAssistantView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/knowledge-base',
    name: 'student-knowledge-base',
    component: KnowledgeBaseView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/ai-learning',
    name: 'ai-learning',
    component: AiLearningView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/today-learn',
    name: 'student-today-learn',
    component: TodayLearnView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/bookshelf',
    name: 'student-bookshelf',
    component: BookshelfView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/bookshelf/:docId/preview',
    name: 'student-html-preview',
    component: StudentHtmlPreviewView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/personal-center',
    name: 'student-personal-center',
    component: PersonalCenterView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/points-mall',
    name: 'student-points-mall',
    component: PointsMallView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/settings',
    name: 'student-settings',
    component: StudentSettingsView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/profile',
    name: 'profile',
    component: StudentProfileView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/settings',
    name: 'settings',
    component: StudentSettingsView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/assignments/:id/submission',
    name: 'student-assignment-submission',
    component: AssignmentSubmissionView,
    meta: { requiresAuth: true, userType: 'student' }
  },
  {
    path: '/student/assignments/:id/grading',
    name: 'student-assignment-grading',
    component: AssignmentGradingView,
    meta: { requiresAuth: true, userType: 'student' }
  },
]

export default studentRoutes 