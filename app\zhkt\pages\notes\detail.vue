<template>
  <view class="container">
    <!-- 笔记内容 -->
    <view class="note-content card">
      <view class="note-header">
        <text class="title">{{ noteInfo.title }}</text>
        <view class="meta">
          <text class="time">{{ formatDate(noteInfo.created_at) }}</text>
        </view>
      </view>
      
      <rich-text class="content" :nodes="noteInfo.content"></rich-text>
      
      <view class="tags" v-if="noteInfo.tags && noteInfo.tags.length">
        <text class="tag" v-for="tag in noteInfo.tags" :key="tag">{{ tag }}</text>
      </view>
      <!--
      <view class="attachments" v-if="noteInfo.attachments && noteInfo.attachments.length">
        <text class="section-title">附件</text>
        <view class="attachment-list">
          <view 
            class="attachment-item"
            v-for="attachment in noteInfo.attachments"
            :key="attachment.id"
            @click="previewFile(attachment)"
          >
            <uni-icons :type="getFileIcon(attachment.type)" size="24" color="#666"></uni-icons>
            <text class="file-name">{{ attachment.name }}</text>
          </view>
        </view>
      </view>
      -->
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-group">
        <button class="action-btn" @click="shareNote">
          <uni-icons type="redo" size="24" color="#666"></uni-icons>
          <text>分享</text>
        </button>
      </view>
      <button class="edit-btn" @click="editNote">编辑笔记</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import request from '@/utils/request'
import { formatDate } from '@/utils/date'

// 笔记信息
const noteInfo = ref({
  id: '',
  title: '',
  courseName: '',
  createTime: '',
  content: '',
  tags: [],
  attachments: [],
  isFavorite: false
})

// 获取笔记详情
const getNoteDetail = async () => {
  try {
    const response = await request({
      url: `/notes/${noteInfo.value.id}/`,
      method: 'GET'
    })
    noteInfo.value = response
  } catch (error) {
    console.log(error);
    uni.showToast({
      title: '获取笔记详情失败',
      icon: 'none'
    })
  }
}

// 获取文件图标
const getFileIcon = (fileType) => {
  const iconMap = {
    'image': 'image',
    'pdf': 'pdf',
    'word': 'file',
    'excel': 'file',
    'ppt': 'file',
    'video': 'videocam',
    'audio': 'sound'
  }
  return iconMap[fileType] || 'file'
}

// 预览文件
const previewFile = (file) => {
  if (file.type === 'image') {
    uni.previewImage({
      urls: [file.url]
    })
  } else {
    uni.downloadFile({
      url: file.url,
      success: (res) => {
        uni.openDocument({
          filePath: res.tempFilePath,
          success: () => {
            console.log('打开文档成功')
          }
        })
      }
    })
  }
}

// 分享笔记
const shareNote = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: noteInfo.value.title,
    summary: noteInfo.value.content.substring(0, 100),
    success: () => {
      console.log('分享成功')
    },
    fail: () => {
      console.log('分享失败')
    }
  })
}

// 编辑笔记
const editNote = () => {
  uni.navigateTo({
    url: `/pages/notes/edit?id=${noteInfo.value.id}`
  })
}

// 添加 onLoad 生命周期钩子
onLoad((options) => {
  noteInfo.value.id = options.id
  getNoteDetail()
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.note-content {
  .note-header {
    margin-bottom: 30rpx;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .meta {
      display: flex;
      justify-content: space-between;
      
      .course {
        font-size: 28rpx;
        color: #666;
      }
      
      .time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .content {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    margin-bottom: 30rpx;
  }
  
  .tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30rpx;
    
    .tag {
      font-size: 24rpx;
      color: #666;
      background: #f5f5f5;
      padding: 6rpx 20rpx;
      border-radius: 20rpx;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
    }
  }
  
  .attachments {
    .section-title {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    
    .attachment-list {
      .attachment-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        background: #f5f5f5;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        
        .file-name {
          font-size: 28rpx;
          color: #333;
          margin-left: 20rpx;
        }
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .action-group {
    display: flex;
    
    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 40rpx;
      background: none;
      padding: 0;
      
      &::after {
        border: none;
      }
      
      text {
        font-size: 24rpx;
        color: #666;
        margin-top: 10rpx;
      }
    }
  }
  
  .edit-btn {
    width: 200rpx;
    height: 80rpx;
    background: #3cc51f;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style> 