# Generated by Django 3.2.20 on 2025-07-25 14:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0070_alter_user_avatar'),
    ]

    operations = [
        migrations.AddField(
            model_name='ailecturechapter',
            name='language_code',
            field=models.CharField(choices=[('zh', '中文'), ('en', '英文'), ('vi', '越南语'), ('id', '印尼语')], default='zh', help_text='章节的语言', max_length=10, verbose_name='语言'),
        ),
        migrations.AddField(
            model_name='ailecturedocument',
            name='language_code',
            field=models.CharField(choices=[('zh', '中文'), ('en', '英文'), ('vi', '越南语'), ('id', '印尼语')], default='zh', help_text='文档的语言', max_length=10, verbose_name='语言'),
        ),
        migrations.AddField(
            model_name='ailecturehtmlcontent',
            name='language_code',
            field=models.CharField(choices=[('zh', '中文'), ('en', '英文'), ('vi', '越南语'), ('id', '印尼语')], default='zh', help_text='HTML内容的语言', max_length=10, verbose_name='语言'),
        ),
        migrations.AddField(
            model_name='ailecturekeypoint',
            name='language_code',
            field=models.CharField(choices=[('zh', '中文'), ('en', '英文'), ('vi', '越南语'), ('id', '印尼语')], default='zh', help_text='要点的语言', max_length=10, verbose_name='语言'),
        ),
        migrations.AddField(
            model_name='ailecturespeechcontent',
            name='language_code',
            field=models.CharField(choices=[('zh', '中文'), ('en', '英文'), ('vi', '越南语'), ('id', '印尼语')], default='zh', help_text='语音内容的语言', max_length=10, verbose_name='语言'),
        ),
        migrations.AddField(
            model_name='ailecturesubtitle',
            name='language_code',
            field=models.CharField(choices=[('zh', '中文'), ('en', '英文'), ('vi', '越南语'), ('id', '印尼语')], default='zh', help_text='字幕的语言', max_length=10, verbose_name='语言'),
        ),
    ]
