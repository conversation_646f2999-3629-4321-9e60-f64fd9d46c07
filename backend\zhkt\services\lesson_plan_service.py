import json
import os
import time
import traceback
import uuid
from typing import Dict, Any, Generator

from ..entitys.lesson_plan import LessonPlan
from ..entitys.subject import Subject
from ..entitys.user import User
from ..utils.deepseek_api import DeepSeekAPI
from ..utils.document_utils import DocumentReader
from ..utils.file_utils import FileUtils
from ..utils.temp_file_utils import clean_temp_file
# 导入通用临时文件工具
from ..utils.temp_file_utils import get_temp_filepath


class LessonPlanService:
    """教学设计服务类，提供教学设计相关的业务逻辑"""
    
    # 将prompt从实例变量改为类变量
    design_lesson_plans_prompt = """
    角色设定（Role Definition）
你是一位专业的教案设计师，具备教育学理论基础和学科知识整合能力，擅长将教学要素进行结构化呈现。拥有5年以上课程设计经验，熟悉K12到高等教育不同阶段的教学框架设计。
任务描述（Task Specification）
根据用户提供的教学资料，系统化整理出包含以下核心模块的完整教案框架：
教学目标的三级分类（知识/技能/素养）
教学重点与难点的辩证分析
教学方法与手段的适配组合
教学内容的逻辑分层与时间分配
任务步骤（Task Steps）
信息提取与分层处理
识别原始资料中的教学目标表述，区分认知维度（记忆/理解/应用）
标注学科核心概念与跨学科关联点，生成概念关系图谱
重点难点解构分析
运用布鲁姆分类法对知识点进行难度评级
对比学生前备知识与新授内容的认知跨度，识别潜在障碍点
建立重点与难点之间的逻辑关联模型
教学策略适配
根据内容特性选择教学法（项目式/探究式/翻转课堂）
设计多媒体组合方案（传统教具/数字工具/实验器材）
制定差异化教学路径（基础层/提高层/拓展层）
教学过程优化
将教学内容分解为15-20分钟的教学单元
设计形成性评价节点（课堂测验/小组互评/即时反馈）
构建"导入-建构-应用-迁移"的完整教学闭环
验证与迭代
检查各环节时间分配的合理性（黄金教学时段利用）
确保教学手段与目标达成度的匹配性
测试难点突破策略的有效性
约束条件（Constraints）
必须严格区分事实性知识、概念性知识和程序性知识
每个教学环节需标注预计时长（精确到5分钟单位）
禁止使用模糊表述（如"基本掌握"），需量化描述（如"能独立完成3种循环结构的转换"）
保持认知负荷理论中的内在/外在负荷平衡
响应格式（Response Format）
markdown
# 教学目标  
[分级图标] 知识目标：  
- 认知维度分类  
- 具体能力描述  

[分级图标] 技能目标：  
- 操作技能层级  
- 问题解决策略  

[分级图标] 素养目标：  
- 学科核心素养  
- 跨学科能力  

# 教学重点难点  
[优先级图标] 教学重点：  
- 学科大概念  
- 关键能力节点  

[难度图标] 教学难点：  
- 认知冲突点  
- 常见误区分析  

# 教学方法和手段  
[策略图标] 方法论组合：  
- 主导教学方法  
- 辅助教学策略  

[工具图标] 技术整合：  
- 传统教具清单  
- 数字工具配置  

# 教学内容及过程  
[阶段图标] 教学阶段（精确时段）：  
1. 子环节名称（时间分配）  
   - 具体实施步骤  
   - 师生互动设计  
   - 形成性评价方式  
示例和指导（Examples and Guidance）
优质教案特征：
教学目标使用"条件+行为+标准"三维表述（如：给定数据集，学生能运用Pandas库在15分钟内完成数据清洗，准确率达90%）
重点难点呈现采用"认知冲突-解决策略-验证标准"结构
教学过程包含至少3种互动形式（个人探究/小组协作/全班讨论）
常见错误警示：
避免将教学手段直接等同于教学方法（如将"使用PPT"误作教学策略）
防止时间分配出现"头重脚轻"（导入环节不超过总时长15%）
禁止将教学难点简单等同于"学生基础差"等非技术性归因
不要使用markdown格式，不能把内容放到```markdown ``` 里面。
请直接输出内容，不要输出任何解释。
下面是教材的内容：
    """

    # 模拟存储任务状态的字典，实际应用中应使用数据库或缓存
    _tasks = {}
    
    @classmethod
    def create_lesson_plan(cls, file, lesson_template: str, teaching_style: str, 
                          specific_requirements: str = '', user_id=None) -> Dict[str, Any]:
        """
        创建教学设计
        
        Args:
            file: 教材文件
            lesson_template: 教案模板
            teaching_style: 教学风格
            specific_requirements: 具体需求说明
            user_id: 用户ID
            
        Returns:
            Dict: 包含任务ID和初始状态
        """
        try:
            # 生成唯一任务ID
            task_id = str(uuid.uuid4())
            
            # 获取文件名和扩展名
            file_name = file.name
            file_extension = os.path.splitext(file_name)[1].lower()
            
            # 解析文件内容
            file_content = cls._parse_document_content(file)[:50000]
            
            # 使用通用方法获取临时文件路径
            temp_file_path = get_temp_filepath(file_extension)
            
            # 写入上传的文件内容
            with open(temp_file_path, 'wb') as temp_file:
                for chunk in file.chunks():
                    temp_file.write(chunk)
            
            try:
                # 使用FileUtils保存教材文件到OSS或本地存储
                material_file_path = FileUtils.save_knowledge_file(temp_file_path)
                
                # 删除临时文件
                clean_temp_file(temp_file_path)
            except Exception as e:
                # 确保删除临时文件
                clean_temp_file(temp_file_path)
                
                raise Exception(f"保存教材文件失败: {str(e)}")
            
            # 创建任务状态
            cls._tasks[task_id] = {
                'status': 'processing',
                'progress': 10,
                'message': '正在分析教材内容...',
                'data': {
                    'file_name': file_name,
                    'file_extension': file_extension,
                    'lesson_template': lesson_template,
                    'teaching_style': teaching_style,
                    'specific_requirements': specific_requirements,
                    'user_id': user_id,
                    'content_length': len(file_content) if file_content else 0,
                    'content': file_content,  # 保存解析的内容用于生成
                    'lesson_plan': None,  # 将来存放生成的教学设计
                    'material_file_path': material_file_path  # 保存教材文件路径
                },
                'created_at': time.time()
            }
            
            return {
                'task_id': task_id,
                'status': 'processing',
                'message': '教学设计生成任务已创建',
                'data': {
                    'file_name': file_name,
                    'content_length': len(file_content) if file_content else 0,
                    'material_file_path': material_file_path
                }
            }
            
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"创建教学设计失败: {str(e)}")
    
    @classmethod
    def _parse_document_content(cls, file) -> str:
        """
        解析文档内容
        
        Args:
            file: 上传的文件对象
            
        Returns:
            str: 文档内容
        """
        # 获取文件扩展名
        file_extension = os.path.splitext(file.name)[1].lower()
        
        # 目前仅支持Word文档
        if file_extension not in ['.doc', '.docx']:
            raise Exception(f"暂不支持的文件格式: {file_extension}，目前仅支持Word文档(.doc, .docx)")
        
        # 使用通用方法获取临时文件路径
        temp_file_path = get_temp_filepath(file_extension)
        
        # 写入上传的文件内容
        with open(temp_file_path, 'wb') as temp_file:
            for chunk in file.chunks():
                temp_file.write(chunk)
        
        try:
            # 使用DocumentReader解析Word文档
            content = DocumentReader.read_word(temp_file_path)
            
            # 删除临时文件
            clean_temp_file(temp_file_path)
                
            return content
        except Exception as e:
            # 确保删除临时文件
            clean_temp_file(temp_file_path)
            
            raise Exception(f"解析文档失败: {str(e)}")
    
    @classmethod
    def get_task_status(cls, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        if task_id not in cls._tasks:
            return {
                'status': 'not_found',
                'message': '任务不存在'
            }
        
        return cls._tasks[task_id]
    
    @classmethod
    def generate_lesson_plan_stream(cls, task_id: str) -> Generator[str, None, None]:
        """
        流式生成教学设计
        
        Args:
            task_id: 任务ID
            
        Returns:
            Generator: 生成内容的流
        """
        # 检查任务是否存在
        if task_id not in cls._tasks:
            yield f"data: {json.dumps({'error': '任务不存在'})}\n\n"
            return
        
        task = cls._tasks[task_id]
        file_content = task['data']['content']
        lesson_template = task['data']['lesson_template']
        teaching_style = task['data']['teaching_style']
        specific_requirements = task['data']['specific_requirements']
        
        try:
            # 构建完整的prompt
            prompt = cls.design_lesson_plans_prompt + file_content
            if specific_requirements:
                prompt += f"\n\n特殊要求：{specific_requirements}"
            if lesson_template != "标准教学模板":
                prompt += f"\n\n请按照{lesson_template}的风格设计"
            if teaching_style != "平衡型（理论与实践并重）":
                prompt += f"\n\n请以{teaching_style}为主要教学风格"
            
            # 创建DeepSeek API实例
            deepseek_api = DeepSeekAPI()
            
            # 使用流式API生成内容
            messages = [{"role": "user", "content": prompt}]
            
            # 保存完整内容
            complete_content = ""
            
            # 使用DeepSeek API获取流式响应
            for chunk in deepseek_api.chat_stream(messages):
                if chunk:
                    content = chunk.get('content', '')
                    complete_content += content
                    # 实时更新任务状态中的内容
                    task['data']['lesson_plan'] = complete_content
                    yield f"data: {json.dumps({'content': content})}\n\n"
            
            # 更新任务状态
            task['status'] = 'completed'
            task['progress'] = 100
            task['message'] = '教学设计生成完成'
            # 确保最终内容被保存
            task['data']['lesson_plan'] = complete_content
            
            # 发送完成标记
            yield f"data: {json.dumps({'content': '', 'status': 'completed'})}\n\n"
            
        except Exception as e:
            # 记录错误详情
            error_msg = str(e)
            traceback_info = traceback.format_exc()
            print(traceback_info)
            
            # 更新任务状态
            task['status'] = 'failed'
            task['message'] = f'生成失败: {error_msg}'

            # 返回错误消息
            yield f"data: {json.dumps({'error': error_msg})}\n\n"
    
    @classmethod
    def save_lesson_plan(cls, task_id: str, title: str, subject_id: int) -> Dict[str, Any]:
        """
        保存生成的教学设计
        
        Args:
            task_id: 任务ID
            title: 教学设计标题
            subject_id: 学科ID
            
        Returns:
            Dict: 保存结果
        """
        try:
            # 检查任务是否存在
            if task_id not in cls._tasks:
                return {
                    'success': False,
                    'message': '任务不存在'
                }
            
            # 获取任务信息
            task = cls._tasks[task_id]
            
            # 检查任务状态
            if task.get('status') != 'completed':
                return {
                    'success': False,
                    'message': '教学设计生成尚未完成，当前状态：' + task.get('status', 'unknown')
                }
            
            # 获取生成的内容
            complete_content = task.get('data', {}).get('lesson_plan')
            
            # 检查是否有生成的内容
            if not complete_content:
                return {
                    'success': False,
                    'message': '教学设计内容为空，请重新生成'
                }
            
            # 获取任务相关信息
            lesson_template = task['data']['lesson_template']
            teaching_style = task['data']['teaching_style']
            specific_requirements = task['data']['specific_requirements']
            user_id = task['data']['user_id']
            material_file_path = task['data']['material_file_path']
            
            # 使用通用方法获取临时文件路径
            temp_file_path = get_temp_filepath('.txt')
            
            with open(temp_file_path, 'w', encoding='utf-8') as temp_file:
                temp_file.write(complete_content)
            
            try:
                # 保存教学设计内容文件
                content_file_path = FileUtils.save_txt_file(temp_file_path)
                
                # 删除临时文件
                clean_temp_file(temp_file_path)
                
                # 获取用户和学科对象
                user = User.objects.get(id=user_id)
                subject = Subject.objects.get(id=subject_id)
                
                # 创建教学设计记录
                lesson_plan = LessonPlan.objects.create(
                    title=title,
                    subject=subject,
                    user=user,
                    content_file_path=content_file_path,
                    lesson_template=lesson_template,
                    teaching_style=teaching_style,
                    specific_requirements=specific_requirements,
                    material_file_path=material_file_path
                )
                
                # 更新任务状态，标记为已保存
                task['status'] = 'saved'
                task['message'] = '教学设计已保存到数据库'
                
                return {
                    'success': True,
                    'message': '教学设计保存成功',
                    'data': {
                        'id': lesson_plan.id,
                        'title': lesson_plan.title,
                        'subject': lesson_plan.subject.name,
                        'content_file_path': lesson_plan.content_file_path,
                        'material_file_path': lesson_plan.material_file_path
                    }
                }
                
            except Exception as e:
                # 确保删除临时文件
                clean_temp_file(temp_file_path)
                    
                raise Exception(f"保存教学设计内容失败: {str(e)}")
                
        except Exception as e:
            traceback.print_exc()
            return {
                'success': False,
                'message': f"保存教学设计失败: {str(e)}"
            }
    
    @classmethod
    def get_lesson_plan(cls, lesson_plan_id: int, user_id: int = None) -> Dict[str, Any]:
        """
        获取教学设计详情
        
        Args:
            lesson_plan_id: 教学设计ID
            user_id: 用户ID，用于权限验证
            
        Returns:
            Dict: 教学设计详情
        """
        try:
            # 获取教学设计
            try:
                lesson_plan = LessonPlan.objects.get(id=lesson_plan_id)
            except LessonPlan.DoesNotExist:
                return {
                    'success': False,
                    'message': '教学设计不存在'
                }
            
            # 权限检查（可选）
            if user_id and lesson_plan.user.id != user_id:
                return {
                    'success': False,
                    'message': '无权限查看此教学设计'
                }
            
            # 获取教学设计内容
            content = ""
            if lesson_plan.content_file_path:
                try:
                    # 如果文件存在OSS或本地，读取内容
                    content_bytes = FileUtils.download_from_oss(lesson_plan.content_file_path) if hasattr(FileUtils, 'download_from_oss') else b""
                    if content_bytes:
                        content = content_bytes.decode('utf-8')
                except Exception as e:
                    content = f"无法加载内容: {str(e)}"
            
            # 返回教学设计详情
            return {
                'success': True,
                'data': {
                    'id': lesson_plan.id,
                    'title': lesson_plan.title,
                    'subject': {
                        'id': lesson_plan.subject.id,
                        'name': lesson_plan.subject.name
                    },
                    'user': {
                        'id': lesson_plan.user.id,
                        'name': lesson_plan.user.username if hasattr(lesson_plan.user, 'username') else ''
                    },
                    'content': content,
                    'content_file_path': lesson_plan.content_file_path,
                    'material_file_path': lesson_plan.material_file_path,
                    'lesson_template': lesson_plan.lesson_template,
                    'teaching_style': lesson_plan.teaching_style,
                    'specific_requirements': lesson_plan.specific_requirements,
                    'created_at': lesson_plan.created_at.strftime('%Y-%m-%d %H:%M:%S') if lesson_plan.created_at else '',
                    'updated_at': lesson_plan.updated_at.strftime('%Y-%m-%d %H:%M:%S') if lesson_plan.updated_at else ''
                }
            }
            
        except Exception as e:
            traceback.print_exc()
            return {
                'success': False,
                'message': f"获取教学设计失败: {str(e)}"
            }
    
    @classmethod
    def get_lesson_plans(cls, user_id: int, subject_id: int = None, search: str = None, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        获取教学设计列表
        
        Args:
            user_id: 用户ID，筛选指定用户的教学设计（必填）
            subject_id: 学科ID，筛选指定学科的教学设计
            search: 搜索关键词，在标题和需求说明中搜索
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 教学设计列表
        """
        try:
            # 构建查询条件
            filters = {}
            
            # 强制使用用户ID筛选，确保只能查看自己的数据
            filters['user_id'] = user_id
            
            if subject_id:
                filters['subject_id'] = subject_id
            
            # 只查询未删除的记录
            filters['deleted_at__isnull'] = True
            
            # 执行基础查询
            query = LessonPlan.objects.filter(**filters)
            
            # 添加搜索功能
            if search:
                from django.db.models import Q
                # 搜索标题和需求说明字段
                query = query.filter(
                    Q(title__icontains=search) | 
                    Q(specific_requirements__icontains=search)
                )
            
            # 计算总数
            total = query.count()
            
            # 分页处理
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            
            # 排序并获取当前页数据
            lesson_plans = query.order_by('-created_at')[start_index:end_index]
            
            # 构建返回结果
            result = []
            for plan in lesson_plans:
                result.append({
                    'id': plan.id,
                    'title': plan.title,
                    'subject': {
                        'id': plan.subject.id,
                        'name': plan.subject.name
                    },
                    'lesson_template': plan.lesson_template,
                    'teaching_style': plan.teaching_style,
                    'specific_requirements': plan.specific_requirements,
                    'created_at': plan.created_at.strftime('%Y-%m-%d %H:%M:%S') if plan.created_at else '',
                    'updated_at': plan.updated_at.strftime('%Y-%m-%d %H:%M:%S') if plan.updated_at else ''
                })
            
            return {
                'success': True,
                'data': {
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'list': result
                }
            }
            
        except Exception as e:
            traceback.print_exc()
            return {
                'success': False,
                'message': f"获取教学设计列表失败: {str(e)}"
            }
    
    @classmethod
    def update_lesson_plan(cls, lesson_plan_id: int, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新教学设计
        
        Args:
            lesson_plan_id: 教学设计ID
            user_id: 用户ID，用于权限验证
            data: 更新的数据
            
        Returns:
            Dict: 更新结果
        """
        try:
            # 获取教学设计
            try:
                lesson_plan = LessonPlan.objects.get(id=lesson_plan_id)
            except LessonPlan.DoesNotExist:
                return {
                    'success': False,
                    'message': '教学设计不存在'
                }
            
            # 权限检查
            if lesson_plan.user.id != user_id:
                return {
                    'success': False,
                    'message': '无权限修改此教学设计'
                }
            
            # 更新基本信息
            if 'title' in data:
                lesson_plan.title = data['title']
            
            if 'subject_id' in data:
                try:
                    subject = Subject.objects.get(id=data['subject_id'])
                    lesson_plan.subject = subject
                except Subject.DoesNotExist:
                    return {
                        'success': False,
                        'message': '学科不存在'
                    }
            
            if 'lesson_template' in data:
                lesson_plan.lesson_template = data['lesson_template']
            
            if 'teaching_style' in data:
                lesson_plan.teaching_style = data['teaching_style']
            
            if 'specific_requirements' in data:
                lesson_plan.specific_requirements = data['specific_requirements']
            
            # 处理内容更新
            if 'content' in data:
                # 使用通用方法获取临时文件路径
                temp_file_path = get_temp_filepath('.txt')
                
                with open(temp_file_path, 'w', encoding='utf-8') as temp_file:
                    temp_file.write(data['content'])
                
                try:
                    # 保存教学设计内容文件
                    content_file_path = FileUtils.save_txt_file(temp_file_path)
                    
                    # 删除临时文件
                    clean_temp_file(temp_file_path)
                    
                    # 删除旧文件
                    if lesson_plan.content_file_path:
                        FileUtils.delete_file(lesson_plan.content_file_path)
                    
                    # 更新文件路径
                    lesson_plan.content_file_path = content_file_path
                    
                except Exception as e:
                    # 确保删除临时文件
                    clean_temp_file(temp_file_path)
                        
                    raise Exception(f"保存教学设计内容失败: {str(e)}")
            
            # 保存更新
            lesson_plan.save()
            
            return {
                'success': True,
                'message': '教学设计更新成功',
                'data': {
                    'id': lesson_plan.id,
                    'title': lesson_plan.title
                }
            }
            
        except Exception as e:
            traceback.print_exc()
            return {
                'success': False,
                'message': f"更新教学设计失败: {str(e)}"
            }
    
    @classmethod
    def delete_lesson_plan(cls, lesson_plan_id: int, user_id: int) -> Dict[str, Any]:
        """
        删除教学设计（软删除）
        
        Args:
            lesson_plan_id: 教学设计ID
            user_id: 用户ID，用于权限验证
            
        Returns:
            Dict: 删除结果
        """
        try:
            # 获取教学设计
            try:
                lesson_plan = LessonPlan.objects.get(id=lesson_plan_id)
            except LessonPlan.DoesNotExist:
                return {
                    'success': False,
                    'message': '教学设计不存在'
                }
            
            # 权限检查
            if lesson_plan.user.id != user_id:
                return {
                    'success': False,
                    'message': '无权限删除此教学设计'
                }
            
            # 软删除
            from django.utils import timezone
            lesson_plan.deleted_at = timezone.now()
            lesson_plan.save()
            
            return {
                'success': True,
                'message': '教学设计删除成功'
            }
            
        except Exception as e:
            traceback.print_exc()
            return {
                'success': False,
                'message': f"删除教学设计失败: {str(e)}"
            } 