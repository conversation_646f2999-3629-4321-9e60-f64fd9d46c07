import request from '@/utils/request'

/**
 * 数字人管理相关 API
 * 对应后端 DigitalHumanViewSet 中的接口
 */
export const digitalHumanApi = {
  /**
   * 获取数字人列表
   * @param {Object} params { skip, limit, search }
   */
  getDigitalHumans(params = {}) {
    return request({
      url: '/digital-human/list/',
      method: 'get',
      params
    })
  },

  /**
   * 获取可用数字人列表（仅限已激活状态）
   * 用于视频生成等需要可用数字人的场景
   * @param {Object} params { skip, limit, search }
   */
  getActiveDigitalHumans(params = {}) {
    return request({
      url: '/digital-human/active-list/',
      method: 'get',
      params
    })
  },

  /**
   * 获取数字人详情
   * @param {Number|String} id 数字人ID
   */
  getDigitalHumanDetail(id) {
    return request({
      url: `/digital-human/${id}/detail/`,
      method: 'get'
    })
  },

  /**
   * 创建数字人
   * @param {Object} payload { name, description, avatar, video }
   */
  createDigitalHuman(payload) {
    const formData = new FormData()
    // 确保只添加有值的字段
    Object.entries(payload).forEach(([k, v]) => {
      if (v !== null && v !== undefined && v !== '') {
        formData.append(k, v)
      }
    })
    
    return request({
      url: '/digital-human/create/',
      method: 'post',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 更新数字人
   * @param {Number|String} id 数字人ID
   * @param {Object} payload { name, description, avatar }
   */
  updateDigitalHuman(id, payload) {
    const formData = new FormData()
    // 确保只添加有值的字段
    Object.entries(payload).forEach(([k, v]) => {
      if (v !== null && v !== undefined && v !== '') {
        formData.append(k, v)
      }
    })
    
    return request({
      url: `/digital-human/${id}/update/`,
      method: 'put',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 删除数字人
   * @param {Number|String} id 数字人ID
   */
  deleteDigitalHuman(id) {
    return request({
      url: `/digital-human/${id}/delete/`,
      method: 'delete'
    })
  },

  /**
   * 获取系统预设数字人列表
   */
  getSystemPresets() {
    return request({
      url: '/digital-human/system-presets/',
      method: 'get'
    })
  },

  /**
   * 使用系统预设创建数字人
   * @param {Object} payload { preset_id, name, description }
   */
  useSystemPreset(payload) {
    const formData = new FormData()
    Object.entries(payload).forEach(([k, v]) => {
      if (v !== null && v !== undefined && v !== '') {
        formData.append(k, v)
      }
    })
    
    return request({
      url: '/digital-human/use-preset/',
      method: 'post',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 为数字人创建新的化身
   * @param {Number|String} digitalHumanId 数字人ID
   * @param {Object} payload { name, description, avatar, video }
   */
  createAvatar(digitalHumanId, payload) {
    const formData = new FormData()
    Object.entries(payload).forEach(([k, v]) => {
      if (v !== null && v !== undefined && v !== '') {
        formData.append(k, v)
      }
    })
    
    return request({
      url: `/digital-human/${digitalHumanId}/create-avatar/`,
      method: 'post',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  /**
   * 获取数字人的所有化身
   * @param {Number|String} digitalHumanId 数字人ID
   */
  getAvatars(digitalHumanId) {
    return request({
      url: `/digital-human/${digitalHumanId}/avatars/`,
      method: 'get'
    })
  },


} 