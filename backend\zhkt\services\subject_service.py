import traceback
from typing import List, Dict, Any

from ..entitys.subject import Subject

class SubjectService:
    """学科服务类，提供学科相关的业务逻辑"""
    
    @classmethod
    def get_subject_list(cls) -> List[Dict[str, Any]]:
        """
        获取学科列表
        
        Returns:
            List: 学科列表
        """
        try:
            # 获取所有启用的顶级学科
            subjects = Subject.objects.filter(
                status=True,
                deleted_at__isnull=True,
                level=1
            ).order_by('sort_order')
            
            # 转换为字典列表
            result = []
            for subject in subjects:
                result.append({
                    'id': subject.id,
                    'name': subject.name,
                    'code': subject.code,
                    'icon': subject.icon
                })
                
            return result
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取学科列表失败: {str(e)}")
    
    @classmethod
    def get_subject_by_id(cls, subject_id: int) -> Subject:
        """
        根据ID获取学科
        
        Args:
            subject_id: 学科ID
            
        Returns:
            Subject: 学科对象
        """
        try:
            subject = Subject.objects.filter(
                id=subject_id,
                status=True,
                deleted_at__isnull=True
            ).first()
            
            if not subject:
                raise Exception(f"学科ID {subject_id} 不存在")
                
            return subject
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取学科失败: {str(e)}")
    
    @classmethod
    def get_subject_tree(cls) -> List[Dict[str, Any]]:
        """
        获取学科树形结构
        
        Returns:
            List: 学科树形结构
        """
        try:
            # 获取所有启用的一级学科
            root_subjects = Subject.objects.filter(
                status=True,
                deleted_at__isnull=True,
                level=1
            ).order_by('sort_order')
            
            # 递归构建树形结构
            def build_tree(parent_id=None, level=1):
                tree = []
                subjects = Subject.objects.filter(
                    status=True,
                    deleted_at__isnull=True,
                    level=level,
                    parent_id=parent_id
                ).order_by('sort_order')
                
                for subject in subjects:
                    node = {
                        'id': subject.id,
                        'name': subject.name,
                        'code': subject.code,
                        'icon': subject.icon,
                        'level': subject.level
                    }
                    
                    # 递归获取子节点
                    children = build_tree(subject.id, level + 1)
                    if children:
                        node['children'] = children
                    
                    tree.append(node)
                    
                return tree
            
            # 构建树形结构
            return build_tree()
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"获取学科树形结构失败: {str(e)}") 