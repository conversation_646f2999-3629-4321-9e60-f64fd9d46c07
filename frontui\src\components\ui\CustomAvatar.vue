<template>
  <div class="custom-avatar" :class="[`avatar-${type}`, { 'avatar-instructor': isInstructor }]">
    <img v-if="src" :src="src" :alt="alt || name" class="avatar-image" />
    <div v-else class="avatar-placeholder">
      <span class="avatar-text">{{ initials }}</span>
    </div>
    <div v-if="isInstructor" class="avatar-badge">
      <el-icon><School /></el-icon>
    </div>
  </div>
</template>

<script>
import { School } from '@element-plus/icons-vue'

export default {
  name: 'CustomAvatar',
  components: {
    School
  },
  props: {
    src: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: 'User'
    },
    alt: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'default',
      validator: (value) => {
        return ['default', 'primary', 'success', 'warning', 'danger', 'info', 'instructor', 'student', 'assistant'].includes(value)
      }
    },
    isInstructor: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    initials() {
      if (!this.name) return '?';
      
      const nameParts = this.name.split(' ');
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      
      return this.name.substring(0, 2).toUpperCase();
    }
  }
}
</script>

<style scoped>
.custom-avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f7fa;
  overflow: hidden;
  border: 2px solid transparent;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: bold;
}

.avatar-text {
  font-size: 16px;
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background-color: var(--el-color-success);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border: 2px solid white;
}

/* Avatar types */
.avatar-primary {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.avatar-success {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
}

.avatar-warning {
  background-color: var(--el-color-warning-light-9);
  border-color: var(--el-color-warning);
}

.avatar-danger {
  background-color: var(--el-color-danger-light-9);
  border-color: var(--el-color-danger);
}

.avatar-info {
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info);
}

.avatar-instructor {
  border-color: var(--el-color-success);
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.avatar-student {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.avatar-assistant {
  border-color: var(--el-color-warning);
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
}
</style> 