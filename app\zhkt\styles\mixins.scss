/* 文本省略 */
@mixin text-ellipsis($line: 1) {
  @if $line == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    overflow: hidden;
  }
}

/* flex布局 */
@mixin flex($direction: row, $justify: flex-start, $align: center) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
}

/* 绝对定位居中 */
@mixin absolute-center($position: both) {
  position: absolute;
  @if $position == both {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $position == horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $position == vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}

/* 多行文本省略 */
@mixin multi-ellipsis($line: 2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  overflow: hidden;
}

/* 阴影效果 */
@mixin box-shadow($level: normal) {
  @if $level == light {
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  } @else if $level == normal {
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  } @else if $level == heavy {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  }
}

/* 渐变背景 */
@mixin gradient-bg($direction: to right, $start-color: #007AFF, $end-color: #00BFFF) {
  background: linear-gradient($direction, $start-color, $end-color);
}

/* 安全区域适配 */
@mixin safe-area-inset($position: bottom) {
  padding-#{$position}: constant(safe-area-inset-#{$position});
  padding-#{$position}: env(safe-area-inset-#{$position});
} 