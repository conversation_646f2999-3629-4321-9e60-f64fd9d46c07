<template>
  <view class="container">
    <!-- 标题输入 -->
    <view class="title-input">
      <input 
        type="text" 
        v-model="noteForm.title" 
        placeholder="请输入笔记标题"
        maxlength="50"
      />
    </view>
    
    <!-- 课程选择 -->
    <view class="course-select">
      <text class="label">所属课程</text>
      <picker 
        :range="courses" 
        range-key="name"
        :value="courseIndex"
        @change="onCourseChange"
      >
        <view class="picker">
          <text>{{ courses[courseIndex]?.name || '请选择课程' }}</text>
          <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
        </view>
      </picker>
    </view>
    
    <!-- 富文本编辑器 -->
    <view class="editor-container">
      <editor 
        id="editor"
        class="editor"
        :placeholder="'请输入笔记内容'"
        @ready="onEditorReady"
        @input="onEditorInput"
      ></editor>
      
      <view class="toolbar">
        <view class="tool-item" @click="execCommand('bold')">
          <uni-icons type="font" size="24" color="#666"></uni-icons>
        </view>
        <view class="tool-item" @click="execCommand('italic')">
          <uni-icons type="font" size="24" color="#666"></uni-icons>
        </view>
        <view class="tool-item" @click="insertImage">
          <uni-icons type="image" size="24" color="#666"></uni-icons>
        </view>
        <view class="tool-item" @click="insertList('ordered')">
          <uni-icons type="list" size="24" color="#666"></uni-icons>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button class="save-btn" @click="saveNote" :disabled="saving">
        {{ saving ? '保存中...' : '保存笔记' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'
import { useUserStore } from '@/store/modules/user'
import { onLoad } from '@dcloudio/uni-app'

const userStore = useUserStore()

// 编辑器实例
let editorCtx = null

// 课程列表
const courses = ref([])
const courseIndex = ref(0)

// 笔记表单
const noteForm = ref({
  id: '',
  title: '',
  course: '',
  content: ''
})

// 保存状态
const saving = ref(false)

// 获取课程列表
const getCourses = async () => {
  try {
    const response = await request({
      url: `/students/${userStore.userInfo.role_info.id}/courses/`,
      method: 'GET'
    })
    courses.value = response.results
  } catch (error) {
    uni.showToast({
      title: '获取课程列表失败',
      icon: 'none'
    })
  }
}

// 获取笔记详情
const getNoteDetail = async (noteId) => {
  if (!noteId) return
  
  try {
    const response = await request({
      url: `/notes/${noteId}/`,
      method: 'GET'
    })
    noteForm.value = response
    
    // 设置课程索引
    const index = courses.value.findIndex(course => course.id === response.course)
    if (index !== -1) {
      courseIndex.value = index
    }
    
    // 设置编辑器内容
    editorCtx.setContents({
      html: response.content
    })
  } catch (error) {
    console.log(error);
    uni.showToast({
      title: '获取笔记详情失败',
      icon: 'none'
    })
  }
}

// 编辑器准备就绪
const onEditorReady = () => {
  uni.createSelectorQuery()
    .select('#editor')
    .context((res) => {
      editorCtx = res.context
    })
    .exec()
}

// 编辑器内容变化
const onEditorInput = (e) => {
  noteForm.value.content = e.detail.html
}

// 执行编辑器命令
const execCommand = (name) => {
  editorCtx.format(name)
}

// 插入图片
const insertImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      uploadImage(tempFilePath)
    }
  })
}

// 上传图片
const uploadImage = async (filePath) => {
  try {
    const response = await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: '/api/upload/',
        filePath: filePath,
        name: 'file',
        success: (res) => {
          resolve(JSON.parse(res.data))
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
    
    editorCtx.insertImage({
      src: response.url,
      width: '100%'
    })
  } catch (error) {
    uni.showToast({
      title: '上传图片失败',
      icon: 'none'
    })
  }
}

// 插入列表
const insertList = (type) => {
  editorCtx.insertList({
    type
  })
}

// 课程选择变化
const onCourseChange = (e) => {
  courseIndex.value = e.detail.value
  noteForm.value.course = courses.value[courseIndex.value].id
}

// 保存笔记
const saveNote = async () => {
  if (!noteForm.value.title) {
    uni.showToast({
      title: '请输入笔记标题',
      icon: 'none'
    })
    return
  }
  
  if (!noteForm.value.course) {
    uni.showToast({
      title: '请选择所属课程',
      icon: 'none'
    })
    return
  }
  
  try {
    saving.value = true
    const url = noteForm.value.id ? `/notes/${noteForm.value.id}/` : '/notes/'
    const method = noteForm.value.id ? 'PUT' : 'POST'
    
    await request({
      url,
      method,
      data: noteForm.value
    })
    
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    uni.navigateBack()
  } catch (error) {
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none'
    })
  } finally {
    saving.value = false
  }
}

// 页面加载
onLoad((options) => {
  getCourses()
  if (options.id) {
    noteForm.value.id = options.id
    uni.createSelectorQuery()
      .select('#editor')
      .context((res) => {
        editorCtx = res.context
        getNoteDetail(options.id)
      })
      .exec()
  }
})
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 120rpx;
}

.title-input {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  
  input {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.course-select {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  
  .label {
    font-size: 28rpx;
    color: #333;
    margin-right: 20rpx;
  }
  
  .picker {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.editor-container {
  .editor {
    min-height: 400rpx;
    padding: 30rpx;
  }
  
  .toolbar {
    display: flex;
    padding: 20rpx;
    border-top: 1rpx solid #eee;
    border-bottom: 1rpx solid #eee;
    
    .tool-item {
      padding: 10rpx 20rpx;
    }
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  
  .save-btn {
    flex: 1;
    height: 80rpx;
    background: #3cc51f;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.5;
    }
  }
}
</style> 