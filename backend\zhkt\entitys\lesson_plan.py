from django.db import models
from django.utils.translation import gettext_lazy as _
from .subject import Subject
from .user import User


class LessonPlan(models.Model):
    """教学设计模型"""
    title = models.CharField(_('教学设计标题'), max_length=200)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, related_name='lesson_plans', verbose_name=_('关联学科'))
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='lesson_plans', verbose_name=_('用户'))
    content_file_path = models.CharField(_('教学设计内容文件路径'), max_length=255, null=True, blank=True)
    lesson_template = models.CharField(_('教案模板'), max_length=50, default='标准教学模板')
    teaching_style = models.CharField(_('教学风格'), max_length=50, default='平衡型（理论与实践并重）')
    specific_requirements = models.TextField(_('具体要求'), null=True, blank=True)
    material_file_path = models.CharField(_('上传的教材文件路径'), max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        db_table = 'zhkt_lesson_plan'
        verbose_name = _('教学设计')
        verbose_name_plural = _('教学设计')

    def __str__(self):
        return self.title 