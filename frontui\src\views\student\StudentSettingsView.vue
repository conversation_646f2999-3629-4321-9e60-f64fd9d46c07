<template>
  <StudentLayout 
    pageTitle="账号设置" 
    :userName="studentStore.userFullName"
    :userAvatar="studentStore.studentData.avatar"
    :userPoints="studentStore.studentData.points">
    
    <div class="space-y-4">
      <!-- 压缩版个人资料卡片 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-4">
          <div class="flex items-start">
            <!-- 头像 -->
            <el-avatar 
              :size="70" 
              :src="studentStore.studentData.avatar"
              class="border-2 border-white shadow-md mr-4" />
            
            <!-- 基本信息 -->
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h2 class="text-xl font-semibold text-gray-800">{{ studentStore.userFullName }}</h2>
              </div>
              
              <!-- 学生信息色块 -->
              <div class="flex flex-wrap gap-2 mb-2">
                <span class="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">学号：{{ studentStore.studentData.student_id }}</span>
                <span class="px-3 py-1.5 bg-green-100 text-green-800 rounded-md text-sm font-medium">{{ academicInfo.college }}</span>
                <span class="px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium">{{ academicInfo.major }}</span>
                <span class="px-3 py-1.5 bg-purple-100 text-purple-800 rounded-md text-sm font-medium">{{ academicInfo.className }}</span>
              </div>
            </div>
          </div>

          <!-- 数据分析指标 - 水平布局 -->
          <div class="flex mt-3 pt-3 border-t gap-3">
            <!-- 学习时长与排名 -->
            <div class="flex-1">
              <div class="bg-blue-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-blue-800 flex items-center mb-1">
                  <el-icon class="text-blue-600 mr-2"><Timer /></el-icon>
                  学习时长
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-blue-900 mb-1">
                    {{ studentAnalytics.totalLearningHours }}小时
                  </div>
                  <div class="text-sm text-blue-700 bg-blue-100 rounded-full px-3 py-0.5 inline-block">
                    排名: {{ studentAnalytics.classRank }}/{{ studentAnalytics.classTotal }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 课程完成情况 -->
            <div class="flex-1">
              <div class="bg-green-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-green-800 flex items-center mb-1">
                  <el-icon class="text-green-600 mr-2"><Notebook /></el-icon>
                  课程完成
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-green-900 mb-1">
                    {{ studentAnalytics.completedCourses }}/{{ studentAnalytics.totalCourses }}
                  </div>
                  <div class="text-sm text-green-700 bg-green-100 rounded-full px-3 py-0.5 inline-block">
                    证书: {{ studentAnalytics.certificatesEarned }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 积分与成就 -->
            <div class="flex-1">
              <div class="bg-purple-50 rounded-lg p-3 h-full">
                <div class="text-base font-medium text-purple-800 flex items-center mb-1">
                  <el-icon class="text-purple-600 mr-2"><Medal /></el-icon>
                  积分与成就
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold text-purple-900 mb-1">
                    {{ studentStore.studentData.points.toLocaleString() }}
                  </div>
                  <div class="text-sm text-purple-700 bg-purple-100 rounded-full px-3 py-0.5 inline-block">
                    连续: {{ studentAnalytics.continuousLearningDays }}天
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人信息管理选项卡 -->
      <el-card shadow="hover">
        <el-tabs v-model="activeTab" tab-position="left" style="min-height: 500px">
          <!-- 账号安全管理 -->
          <el-tab-pane label="账号安全" name="security">
            <h3 class="text-xl font-medium text-gray-800 mb-4">账号安全</h3>
            <el-collapse accordion>
              <!-- 密码修改 -->
              <el-collapse-item name="password">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Lock /></el-icon>
                    <span>密码修改</span>
                  </div>
                </template>
                <el-form 
                  ref="passwordForm" 
                  :model="passwordData" 
                  :rules="passwordRules" 
                  label-width="120px">
                  <el-form-item label="当前密码" prop="currentPassword">
                    <el-input v-model="passwordData.currentPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item label="新密码" prop="newPassword">
                    <el-input v-model="passwordData.newPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item label="确认新密码" prop="confirmPassword">
                    <el-input v-model="passwordData.confirmPassword" type="password" show-password />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="changePassword">修改密码</el-button>
                    <el-button @click="resetPasswordForm">取消</el-button>
                  </el-form-item>
                </el-form>
              </el-collapse-item>

              <!-- 多因素认证 -->
              <el-collapse-item name="mfa">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Key /></el-icon>
                    <span>多因素认证</span>
                  </div>
                </template>
                <div class="p-4">
                  <p class="mb-4">多因素认证可以提高账号安全性，防止未授权访问。</p>
                  <el-switch
                    v-model="securitySettings.mfaEnabled"
                    active-text="已启用"
                    inactive-text="未启用"
                    @change="toggleMFA"
                  />
                  <div v-if="securitySettings.mfaEnabled" class="mt-4 bg-gray-50 p-4 rounded">
                    <p class="mb-2 font-medium">已绑定设备：</p>
                    <el-tag class="mr-2 mb-2">手机验证器</el-tag>
                    <p class="text-sm text-gray-600 mb-2">上次验证时间：2023-09-15 14:30</p>
                    <el-button size="small" type="primary">重新配置</el-button>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 账号绑定 -->
              <el-collapse-item name="binding">
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Link /></el-icon>
                    <span>安全验证绑定</span>
                  </div>
                </template>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">手机绑定</h4>
                      <p class="text-sm text-gray-600">当前绑定：{{ securitySettings.phone }}</p>
                    </div>
                    <el-button type="primary" plain size="small">修改</el-button>
                  </div>
                  <div class="flex justify-between items-center">
                    <div>
                      <h4 class="font-medium">邮箱绑定</h4>
                      <p class="text-sm text-gray-600">当前绑定：{{ securitySettings.email }}</p>
                    </div>
                    <el-button type="primary" plain size="small">修改</el-button>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-tab-pane>

          <!-- 社交账号绑定 -->
          <el-tab-pane label="社交账号绑定" name="social">
            <h3 class="text-xl font-medium text-gray-800 mb-4">社交账号绑定</h3>
            <el-card shadow="never" class="mb-4">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="text-green-600 text-xl mr-2"><ChatDotSquare /></el-icon>
                  <span>微信账号</span>
                </div>
              </template>
              <div class="flex justify-between items-center">
                <div>
                  <p v-if="socialAccounts.wechat.bound" class="text-gray-600">
                    已绑定账号：{{ socialAccounts.wechat.nickname }}
                  </p>
                  <p v-else class="text-gray-600">未绑定微信账号</p>
                </div>
                <el-button type="success" plain v-if="!socialAccounts.wechat.bound">绑定</el-button>
                <el-button type="info" plain v-else>解绑</el-button>
              </div>
            </el-card>

            <el-card shadow="never">
              <template #header>
                <div class="flex items-center">
                  <el-icon class="text-blue-600 text-xl mr-2"><ChatRound /></el-icon>
                  <span>QQ账号</span>
                </div>
              </template>
              <div class="flex justify-between items-center">
                <div>
                  <p v-if="socialAccounts.qq.bound" class="text-gray-600">
                    已绑定账号：{{ socialAccounts.qq.nickname }}
                  </p>
                  <p v-else class="text-gray-600">未绑定QQ账号</p>
                </div>
                <div v-if="!socialAccounts.qq.bound">
                  <el-button type="primary" class="mr-2" size="small">
                    <el-icon class="mr-1"><Plus /></el-icon>扫码绑定
                  </el-button>
                  <el-popover
                    placement="top"
                    :width="200"
                    trigger="click">
                    <template #reference>
                      <el-button type="info" plain size="small">其他方式</el-button>
                    </template>
                    <div class="p-2">
                      <div class="mb-3 font-medium text-center">选择绑定方式</div>
                      <div class="space-y-2">
                        <el-button type="primary" size="small" block>
                          <el-icon class="mr-1"><Key /></el-icon>账号密码绑定
                        </el-button>
                        <el-button type="success" size="small" block>
                          <el-icon class="mr-1"><Link /></el-icon>手机号关联绑定
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
                <el-button type="info" plain v-else>解绑</el-button>
              </div>
            </el-card>
          </el-tab-pane>

          <!-- 通知设置功能 -->
          <el-tab-pane label="通知设置" name="notifications">
            <h3 class="text-xl font-medium text-gray-800 mb-4">通知设置</h3>
            <div class="space-y-6">
              <!-- 学习通知设置 -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-lg mb-3">学习通知</h4>
                <el-form>
                  <el-form-item label="课程通知">
                    <el-switch v-model="notificationSettings.course" />
                    <span class="ml-2 text-sm text-gray-600">接收课程更新、作业发布等通知</span>
                  </el-form-item>
                  <el-form-item label="学习提醒">
                    <el-switch v-model="notificationSettings.study" />
                    <span class="ml-2 text-sm text-gray-600">接收每日学习提醒、学习计划通知</span>
                  </el-form-item>
                  <el-form-item label="作业截止">
                    <el-switch v-model="notificationSettings.assignment" />
                    <span class="ml-2 text-sm text-gray-600">接收作业截止日期提醒</span>
                  </el-form-item>
                  <el-form-item label="成绩通知">
                    <el-switch v-model="notificationSettings.grades" />
                    <span class="ml-2 text-sm text-gray-600">接收成绩公布、点评反馈通知</span>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 社交通知设置 -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-lg mb-3">社交通知</h4>
                <el-form>
                  <el-form-item label="讨论区通知">
                    <el-switch v-model="notificationSettings.discussion" />
                    <span class="ml-2 text-sm text-gray-600">接收讨论区回复、提及等通知</span>
                  </el-form-item>
                  <el-form-item label="私信通知">
                    <el-switch v-model="notificationSettings.messages" />
                    <span class="ml-2 text-sm text-gray-600">接收私信消息通知</span>
                  </el-form-item>
                  <el-form-item label="活动通知">
                    <el-switch v-model="notificationSettings.events" />
                    <span class="ml-2 text-sm text-gray-600">接收线上线下活动邀请、提醒通知</span>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 系统通知设置 -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-lg mb-3">系统通知</h4>
                <el-form>
                  <el-form-item label="系统更新">
                    <el-switch v-model="notificationSettings.system" />
                    <span class="ml-2 text-sm text-gray-600">接收平台功能更新、维护公告等通知</span>
                  </el-form-item>
                  <el-form-item label="推荐通知">
                    <el-switch v-model="notificationSettings.recommendations" />
                    <span class="ml-2 text-sm text-gray-600">接收课程推荐、学习资源推荐等通知</span>
                  </el-form-item>
                  <el-form-item label="账号安全">
                    <el-switch v-model="notificationSettings.security" disabled />
                    <span class="ml-2 text-sm text-gray-600">接收账号安全相关通知（不可关闭）</span>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 通知方式设置 -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-lg mb-3">通知方式</h4>
                <el-form>
                  <el-form-item label="站内通知">
                    <el-switch v-model="notificationMethods.inApp" disabled />
                    <span class="ml-2 text-sm text-gray-600">接收平台内通知（不可关闭）</span>
                  </el-form-item>
                  <el-form-item label="邮件通知">
                    <el-switch v-model="notificationMethods.email" />
                    <span class="ml-2 text-sm text-gray-600">通过邮件接收通知</span>
                  </el-form-item>
                  <el-form-item label="短信通知">
                    <el-switch v-model="notificationMethods.sms" />
                    <span class="ml-2 text-sm text-gray-600">通过短信接收重要通知</span>
                  </el-form-item>
                  <el-form-item label="微信通知">
                    <el-switch v-model="notificationMethods.wechat" :disabled="!socialAccounts.wechat.bound" />
                    <span class="ml-2 text-sm text-gray-600">
                      通过微信接收通知
                      <template v-if="!socialAccounts.wechat.bound">
                        （请先绑定微信账号）
                      </template>
                    </span>
                  </el-form-item>
                </el-form>
              </div>

              <el-button type="primary" @click="saveNotificationSettings">保存通知设置</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </StudentLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useStudentStore } from '@/stores/student'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Lock, 
  Key, 
  Link, 
  Plus,
  ChatDotSquare,
  ChatRound,
  Timer,
  Notebook,
  Medal
} from '@element-plus/icons-vue'

// 引入学生数据存储
const studentStore = useStudentStore()

// 当前活动选项卡
const activeTab = ref('security') // Default to security tab

// 学籍信息数据 (用于顶部卡片) - 保持不变
const academicInfo = reactive({
  college: '计算机科学与技术学院',
  major: '计算机科学与技术',
  className: '计算机2023-2班',
  educationLength: 4, // Keep for potential use in card, though not currently displayed
  studyType: '全日制', // Keep for potential use in card
  enrollmentDate: '2023-09-01', // Keep for potential use in card
  expectedGraduationDate: '2027-07-01', // Keep for potential use in card
  status: '在读' // Keep for potential use in card
})

// 安全设置
const passwordForm = ref(null)
const passwordData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能小于8个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, 
      message: '密码必须包含大小写字母、数字和特殊字符', trigger: 'blur' 
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== passwordData.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

const securitySettings = reactive({
  mfaEnabled: true,
  phone: '138****8000',
  email: 's****<EMAIL>'
})

// 社交账号
const socialAccounts = reactive({
  wechat: {
    bound: true,
    nickname: '学习小达人'
  },
  qq: {
    bound: false,
    nickname: ''
  }
})

// 学生数据分析
const studentAnalytics = reactive({
  totalLearningHours: 158.5,
  weeklyLearningHours: 12.5,
  classRank: 3,
  classTotal: 42,
  completedCourses: 5,
  totalCourses: 8,
  certificatesEarned: 3,
  medalLevel: 'silver',
  pointsToNextLevel: 250,
  continuousLearningDays: 15,
  dailyLearningData: [1.2, 0.8, 1.5, 2.0, 0.5, 1.8, 2.5, 1.0, 1.2, 0.0, 2.0, 2.2, 1.6, 2.4]
})

// 通知设置数据
const notificationSettings = reactive({
  // 学习通知
  course: true,
  study: true,
  assignment: true,
  grades: true,
  // 社交通知
  discussion: true,
  messages: true,
  events: false,
  // 系统通知
  system: false,
  recommendations: false,
  security: true, // 不可关闭
})

// 通知方式
const notificationMethods = reactive({
  inApp: true, // 不可关闭
  email: true,
  sms: false,
  wechat: socialAccounts.wechat.bound
})

// 方法
const changePassword = () => {
  passwordForm.value.validate((valid) => {
    if (valid) {
      ElMessageBox.confirm('确定要修改密码吗？修改后需要重新登录。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 实际应用中这里会调用API修改密码
        ElMessage.success('密码修改成功，请重新登录')
        resetPasswordForm()
      }).catch(() => {
        // 用户取消操作
      })
    }
  })
}

const resetPasswordForm = () => {
  passwordForm.value.resetFields()
}

const toggleMFA = (value) => {
  if (value) {
    ElMessage.success('已成功开启多因素认证')
  } else {
    ElMessageBox.confirm('关闭多因素认证将降低账号安全性，确定要关闭吗？', '安全提示', {
      confirmButtonText: '确定关闭',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      ElMessage.warning('已关闭多因素认证')
    }).catch(() => {
      securitySettings.mfaEnabled = true // 用户取消，恢复开启状态
    })
  }
}

// 保存通知设置
const saveNotificationSettings = () => {
  // 实际应用中，这里会调用API保存通知设置
  ElMessage.success('通知设置已保存')
  console.log('Notification settings saved:', JSON.parse(JSON.stringify(notificationSettings)))
  console.log('Notification methods saved:', JSON.parse(JSON.stringify(notificationMethods)))
}

onMounted(() => {
  // 在真实应用中，这里会从API加载个人信息数据
  console.log('账号设置页面已加载')
})
</script>

<style scoped>
/* 自定义样式 */
</style> 