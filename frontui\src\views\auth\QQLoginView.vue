<template>
  <div class="bg-gray-50 flex items-center justify-center min-h-screen">
    <div class="max-w-md w-full mx-auto">
      <el-card class="box-card">
        <!-- 头部 -->
        <div class="p-6 header-bg text-center">
          <h1 class="text-2xl font-bold text-white">智慧课堂</h1>
          <p class="text-white mt-2">知识改变命运，智慧点亮未来</p>
        </div>
        
        <!-- QQ登录区域 -->
        <div class="p-6 text-center">
          <div class="mb-6 text-blue-500">
            <i class="fa-brands fa-qq text-blue-500 text-6xl"></i>
          </div>
          <h2 class="text-xl font-semibold text-gray-800 mb-6">QQ扫码登录</h2>
          
          <!-- QR码图片区域 -->
          <div class="mx-auto w-48 h-48 border-2 border-gray-200 rounded-md mb-4 flex items-center justify-center bg-white">
            <el-skeleton :loading="loading" animated>
              <template #template>
                <div class="text-center">
                  <el-skeleton-item variant="circle" style="width: 48px; height: 48px; margin: 0 auto;" />
                  <el-skeleton-item variant="text" style="width: 60%; margin: 16px auto;" />
                </div>
              </template>
              <template #default>
                <div class="flex flex-col items-center justify-center w-40 h-40 bg-gray-100 rounded">
                  <i class="fa-brands fa-qq text-blue-500 text-4xl"></i>
                  <p class="text-sm text-gray-500 mt-2">QQ扫码登录</p>
                </div>
              </template>
            </el-skeleton>
          </div>
          
          <!-- 提示文字 -->
          <p class="text-sm text-gray-600 mb-6">
            请使用QQ扫描二维码登录
            <br>
            <span class="text-xs text-gray-500">二维码有效期为2分钟</span>
          </p>
          
          <!-- 刷新按钮 -->
          <el-button 
            @click="refreshQRCode" 
            :icon="Refresh"
            plain
          >
            刷新二维码
          </el-button>
        </div>
        
        <!-- 其他登录方式 -->
        <div class="px-6 pb-6">
          <el-divider content-position="center">其他登录方式</el-divider>
          
          <div class="mt-4 flex space-x-4 justify-center">
            <router-link to="/auth/wechat-login">
              <el-button class="w-full">
                <i class="fa-brands fa-weixin text-green-600 text-2xl"></i>
              </el-button>
            </router-link>
            <router-link to="/auth/mobile-login">
              <el-button class="w-full">
                <el-icon><Iphone /></el-icon>
              </el-button>
            </router-link>
            <router-link to="/auth/login">
              <el-button class="w-full">
                <el-icon><User /></el-icon>
              </el-button>
            </router-link>
          </div>
        </div>
      </el-card>
      
      <!-- 页脚 -->
      <div class="mt-4 text-center">
        <p class="text-xs text-gray-500">
          © {{ currentYear }} 智慧课堂. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, Iphone, User } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(true)
const currentYear = computed(() => new Date().getFullYear())

// 加载二维码
onMounted(() => {
  // 模拟加载延迟
  setTimeout(() => {
    loading.value = false
    
    // 模拟15秒后登录成功
    setTimeout(() => {
      ElMessage.success('QQ登录成功！')
      ElMessage.info('目前仅有登录页面可用，请稍后尝试')
    }, 15000)
  }, 1500)
})

// 刷新二维码
const refreshQRCode = () => {
  loading.value = true
  
  ElMessage.success('二维码已刷新！')
  
  // 模拟刷新延迟
  setTimeout(() => {
    loading.value = false
    
    // 重新设置登录成功模拟
    setTimeout(() => {
      ElMessage.success('QQ登录成功！')
      ElMessage.info('目前仅有登录页面可用，请稍后尝试')
    }, 15000)
  }, 1000)
}
</script>

<style scoped>
.header-bg {
  background-color: #2680EB;
}

:deep(.el-button--primary) {
  background-color: #2680EB;
  border-color: #2680EB;
}

:deep(.el-button--primary:hover), :deep(.el-button--primary:focus) {
  background-color: #1a6bca;
  border-color: #1a6bca;
}
</style> 