import os
import traceback
import uuid
from typing import Optional, List, Tuple

from django.conf import settings
from django.core.files.uploadedfile import UploadedFile
from django.utils import timezone

from ..entitys.audio_model import AudioGenerationHistory, AudioModel
from ..utils.file_utils import FileUtils
from ..utils.fish_speech import generate_fish_speech, create_fish_model
from ..utils.doubao_tts import DoubaoTTS
from ..utils.temp_file_utils import get_temp_filepath, clean_temp_file


class AudioService:
    """音频服务类"""

    # ------------------------- 业务方法 ------------------------- #
    @staticmethod
    def upload_audio_file(user_id: int, file, name: str, description: str = "") -> AudioGenerationHistory:
        """
        上传音频文件

        Args:
            user_id: 用户ID
            file: 音频文件
            name: 音频名称
            description: 音频描述（可选）

        Returns:
            AudioGenerationHistory: 生成的音频记录
        """
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()

            user = User.objects.get(id=user_id)

            # 保存音频文件
            file_extension = os.path.splitext(file.name)[1]
            uuid_str = str(uuid.uuid4())
            file_name = f'{uuid_str}{file_extension}'
            file_path = os.path.join(settings.MEDIA_ROOT, 'audio', file_name)

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 保存文件
            with open(file_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)

            # 获取相对URL
            audio_url = os.path.join('audio', file_name)

            # 创建一个默认的系统语音模型或获取已有的
            system_voice_model, created = AudioModel.objects.get_or_create(
                type='system',
                name='系统默认语音',
                defaults={
                    'description': '系统默认语音模型',
                    'status': 'success'
                }
            )

            # 创建音频记录
            audio = AudioGenerationHistory.objects.create(
                name=name,
                text_content=description,
                audio_url=audio_url,
                voice_model=system_voice_model,
                status='success',
                user=user
            )

            return audio

        except Exception as e:
            traceback.print_exc()
            raise Exception(f"上传音频文件失败: {str(e)}")

    @staticmethod
    def create_audio_clone(
        user_id: int,
        name: str,
        description: Optional[str],
        audio_file: UploadedFile,
        avatar: Optional[UploadedFile] = None,
    ) -> AudioModel:
        """创建声音克隆"""
        file_ext = os.path.splitext(getattr(audio_file, 'name', ''))[1]
        temp_audio_path = get_temp_filepath(suffix=file_ext)
        # 写入上传音频内容到临时文件
        with open(temp_audio_path, 'wb') as tmp_f:
            for chunk in audio_file.chunks():
                tmp_f.write(chunk)

        temp_avatar_path = None
        try:
            if avatar:
                avatar_ext = os.path.splitext(getattr(avatar, 'name', ''))[1]
                temp_avatar_path = get_temp_filepath(suffix=avatar_ext)
                with open(temp_avatar_path, 'wb') as tmp_f:
                    for chunk in avatar.chunks():
                        tmp_f.write(chunk)

            # 调用 FishSpeech 创建模型
            model = create_fish_model(
                title=name,
                description=description or "",
                voice_file=temp_audio_path,
                cover_image=temp_avatar_path,
            )

            # 保存原始音频文件
            audio_rel_path = FileUtils.save_audio_file(temp_audio_path)

            # 从模型返回值中获取ID
            model_id = model.get('_id') if isinstance(model, dict) else getattr(model, 'id', None)
            
            # 生成克隆示例音频
            clone_text = "我是你的数字人声音，我们可以打造个性化数字人声线，无论是温柔亲切、沉稳专业，还是活泼灵动，都能精准定制。适用于虚拟助手、有声读物、品牌播报等场景，让你的声音更具辨识度与感染力。用科技还原真实，让沟通更有温度！"

            clone_temp_audio_path = get_temp_filepath(suffix='.wav')
            generate_fish_speech(
                text=clone_text,
                file_path=clone_temp_audio_path,
                reference_id=model_id,
            )
            clone_audio_rel_path = FileUtils.save_audio_file(clone_temp_audio_path)

            # 保存头像
            avatar_url = FileUtils.save_image_file(temp_avatar_path) if temp_avatar_path else None

            # 创建数据库记录
            from django.contrib.auth import get_user_model
            user = get_user_model().objects.get(id=user_id)
            voice_model = AudioModel.objects.create(
                name=name,
                description=description,
                type='custom',
                reference_id=model_id,
                avatar_url=avatar_url,
                audio_url=audio_rel_path,
                clone_audio_url=clone_audio_rel_path,
                status='success',
                user=user,
            )
            return voice_model
        finally:
            clean_temp_file(temp_audio_path)
            if temp_avatar_path:
                clean_temp_file(temp_avatar_path)
            if 'clone_temp_audio_path' in locals():
                clean_temp_file(clone_temp_audio_path)

    @staticmethod
    def get_user_audio_clones(
        user_id: int,
        skip: int = 0,
        limit: int = 10,
        search: Optional[str] = None
    ) -> Tuple[int, List[dict]]:
        """获取用户的声音克隆列表"""
        query = AudioModel.objects.filter(
            user_id=user_id,
            deleted_at__isnull=True
        )

        # 如果有搜索条件
        if search:
            query = query.filter(name__icontains=search)

        # 获取总数
        total = query.count()

        # 获取分页数据
        clones = query[skip:skip+limit]

        # 简化响应格式化
        result = []
        for clone in clones:
            result.append({
                'id': clone.id,
                'name': clone.name,
                'description': clone.description,
                'type': clone.type,
                'status': clone.status,
                'avatar_url': FileUtils.get_file_url(clone.avatar_url),
                'clone_audio_url': FileUtils.get_file_url(clone.clone_audio_url),
                'reference_id': clone.reference_id,
                'usage_count': clone.usage_count,
                'createdAt': clone.created_at,
            })

        return total, result

    @staticmethod
    def get_system_voice_presets() -> List[dict]:
        """获取系统预设声音列表"""
        system_voices = AudioModel.objects.filter(
            type='system',
            deleted_at__isnull=True
        )
        
        result = []
        for voice in system_voices:
            # 确保有示例音频
            sample_audio_url = voice.clone_audio_url
            if not sample_audio_url:
                # 生成示例音频
                sample_audio_url = AudioService.generate_system_voice_sample(voice.id)
            else:
                sample_audio_url = FileUtils.get_file_url(sample_audio_url)
                
            result.append({
                'id': voice.id,
                'name': voice.name,
                'description': voice.description,
                'type': voice.type,
                'category': voice.category or '系统',
                'reference_id': voice.reference_id,
                'usage_count': voice.usage_count,
                'createdAt': voice.created_at,
                'clone_audio_url': sample_audio_url,
            })
        
        return result

    @staticmethod
    def generate_system_voice_sample(voice_id: int) -> Optional[str]:
        """为系统预设声音生成示例音频"""
        try:
            voice_model = AudioModel.objects.get(id=voice_id, type='system', deleted_at__isnull=True)
            
            # 如果已经有示例音频，直接返回
            if voice_model.clone_audio_url:
                return FileUtils.get_file_url(voice_model.clone_audio_url)
            
            # 生成示例文本
            sample_text = f"您好，我是{voice_model.name}，这是语音合成演示。无论是教学内容、有声读物还是其他场景，我都能为您提供清晰自然的语音服务。"
            
            # 使用豆包TTS生成示例音频
            doubao_tts = DoubaoTTS()
            temp_audio_path = get_temp_filepath(suffix='.mp3')
            
            try:
                audio_data = doubao_tts.synthesize(
                    text=sample_text,
                    voice_type=voice_model.reference_id,
                    encoding='mp3'
                )
                
                # 保存到临时文件
                with open(temp_audio_path, 'wb') as f:
                    f.write(audio_data)
                
                # 保存到文件系统
                audio_rel_path = FileUtils.save_audio_file(temp_audio_path)
                
                # 更新数据库记录
                voice_model.clone_audio_url = audio_rel_path
                voice_model.save()
                
                return FileUtils.get_file_url(audio_rel_path)
                
            finally:
                clean_temp_file(temp_audio_path)
                
        except Exception as e:
            print(f"生成系统预设声音示例失败: {e}")
            return None

    @staticmethod
    def get_audio_by_id(audio_id: int, user_id: int) -> Optional[AudioGenerationHistory]:
        """
        根据ID获取音频

        Args:
            audio_id: 音频ID
            user_id: 用户ID

        Returns:
            Optional[AudioGenerationHistory]: 音频对象或None
        """
        try:
            return AudioGenerationHistory.objects.get(
                id=audio_id,
                user_id=user_id,
                deleted_at__isnull=True
            )
        except AudioGenerationHistory.DoesNotExist:
            return None

    @staticmethod
    def update_audio_clone(
        clone_id: int,
        user_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        avatar: Optional[UploadedFile] = None,
    ) -> bool:
        """更新声音克隆元数据"""
        try:
            voice_model = AudioModel.objects.get(id=clone_id, user_id=user_id, deleted_at__isnull=True)
        except AudioModel.DoesNotExist:
            return False

        if name:
            voice_model.name = name
        if description is not None:
            voice_model.description = description

        if avatar:
            avatar_ext = os.path.splitext(getattr(avatar, 'name', ''))[1]
            temp_avatar = get_temp_filepath(suffix=avatar_ext)
            with open(temp_avatar, 'wb') as tmp_f:
                for chunk in avatar.chunks():
                    tmp_f.write(chunk)
            try:
                voice_model.avatar_url = FileUtils.save_image_file(temp_avatar)
            finally:
                clean_temp_file(temp_avatar)

        voice_model.updated_at = timezone.now()
        voice_model.save()
        return True

    @staticmethod
    def delete_audio_clone(clone_id: int, user_id: int) -> bool:
        """删除声音克隆（软删除）"""
        try:
            voice_model = AudioModel.objects.get(
                id=clone_id,
                user_id=user_id,
                deleted_at__isnull=True
            )
        except AudioModel.DoesNotExist:
            return False

        # 先删除FishSpeech上的模型（如果有reference_id）
        if voice_model.reference_id:
            try:
                from zhkt.utils.fish_speech import delete_fish_model
                success = delete_fish_model(voice_model.reference_id)
                if success:
                    print(f"已成功删除FishSpeech模型: {voice_model.reference_id}")
                else:
                    print(f"删除FishSpeech模型失败: {voice_model.reference_id}")
            except Exception as e:
                print(f"删除FishSpeech模型时发生错误: {e}")
                # 即使FishSpeech删除失败，也继续删除本地记录

        # 删除相关文件
        try:
            # 删除原始音频文件
            if voice_model.audio_url:
                FileUtils.delete_file(voice_model.audio_url)
                
            # 删除克隆示例音频文件
            if voice_model.clone_audio_url:
                FileUtils.delete_file(voice_model.clone_audio_url)
                
            # 删除头像文件
            if voice_model.avatar_url:
                FileUtils.delete_file(voice_model.avatar_url)
        except Exception as e:
            print(f"删除声音克隆相关文件时发生错误: {e}")
            # 继续执行软删除

        # 软删除本地记录
        voice_model.deleted_at = timezone.now()
        voice_model.save()

        return True

    @staticmethod
    def generate_audio(
        user_id: int,
        title: str,
        text_content: str,
        audio_clone_id: int,
        speed: float = 1.0,
        model: str = 'speech-1.6',
        copywriting_id: Optional[int] = None,
    ) -> AudioGenerationHistory:
        """使用声音克隆生成音频"""
        from django.contrib.auth import get_user_model
        user = get_user_model().objects.get(id=user_id)
        try:
            voice_model = AudioModel.objects.get(id=audio_clone_id, deleted_at__isnull=True)
        except AudioModel.DoesNotExist:
            raise Exception("声音克隆模型不存在")

        gen = AudioGenerationHistory.objects.create(
            name=title,
            text_content=text_content,
            voice_model=voice_model,
            speed=speed,
            status='processing',
            user=user,
        )

        generated_path = get_temp_filepath(suffix='.mp3')
        try:
            # 判断是系统预设声音还是自定义声音
            if voice_model.type == 'system':
                # 使用豆包TTS
                doubao_tts = DoubaoTTS()
                audio_data = doubao_tts.synthesize(
                    text=text_content,
                    voice_type=voice_model.reference_id,
                    speed_ratio=speed,
                    encoding='mp3'
                )
                # 保存音频数据到临时文件
                with open(generated_path, 'wb') as f:
                    f.write(audio_data)
            else:
                # 使用FishSpeech生成自定义语音
                generated_path = get_temp_filepath(suffix='.wav')
                generate_fish_speech(
                    text=text_content, 
                    file_path=generated_path, 
                    reference_id=voice_model.reference_id, 
                    speed=speed,
                    model=model
                )
            
            audio_rel_path = FileUtils.save_audio_file(generated_path)
            gen.audio_url = audio_rel_path
            gen.status = 'success'
            gen.save()

            voice_model.usage_count += 1
            voice_model.save()
            return gen
        except Exception as e:
            gen.status = 'failed'
            gen.save()
            traceback.print_exc()
            raise Exception(f"生成音频失败: {e}")
        finally:
            clean_temp_file(generated_path)

    @staticmethod
    def get_user_generated_audios(
        user_id: int,
        skip: int = 0,
        limit: int = 10
    ) -> Tuple[int, List[AudioGenerationHistory]]:
        """
        获取用户生成的音频列表

        Args:
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数

        Returns:
            Tuple[int, List[AudioGenerationHistory]]: (总数, 音频列表)
        """
        # 获取总数
        total = AudioGenerationHistory.objects.filter(
            user_id=user_id,
            deleted_at__isnull=True
        ).count()

        # 获取分页数据
        audios = AudioGenerationHistory.objects.filter(
            user_id=user_id,
            deleted_at__isnull=True
        ).order_by('-created_at')[skip:skip+limit]

        return total, audios

    @staticmethod
    def delete_generated_audio(audio_id: int, user_id: int) -> bool:
        """删除生成的音频（软删除）"""
        try:
            audio = AudioGenerationHistory.objects.get(
                id=audio_id,
                user_id=user_id,
                deleted_at__isnull=True
            )
        except AudioGenerationHistory.DoesNotExist:
            return False
        
        # 删除音频文件
        try:
            if audio.audio_url:
                FileUtils.delete_file(audio.audio_url)
        except Exception as e:
            print(f"删除音频文件时发生错误: {e}")
            # 继续执行软删除
        
        # 软删除
        audio.deleted_at = timezone.now()
        audio.save()
        
        return True 