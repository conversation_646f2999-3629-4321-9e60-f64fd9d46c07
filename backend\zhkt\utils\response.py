from typing import Any, Optional, Union, Callable, Type, Dict
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import json
import functools
from django.core.paginator import Paginator
from rest_framework.response import Response
from rest_framework import status


class MySQLJSONEncoder(json.JSONEncoder):
    """MySQL数据类型的JSON编码器"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(obj, bytes):
            return obj.decode('utf-8')
        return super().default(obj)


class ResponseCode(Enum):
    """响应状态码枚举类"""
    SUCCESS = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500
    
    @classmethod
    def get_http_status(cls, code: int) -> int:
        """根据业务状态码获取HTTP状态码"""
        code_map = {
            cls.BAD_REQUEST.value: status.HTTP_400_BAD_REQUEST,
            cls.UNAUTHORIZED.value: status.HTTP_401_UNAUTHORIZED,
            cls.FORBIDDEN.value: status.HTTP_403_FORBIDDEN,
            cls.NOT_FOUND.value: status.HTTP_404_NOT_FOUND,
            cls.INTERNAL_ERROR.value: status.HTTP_500_INTERNAL_SERVER_ERROR
        }
        return code_map.get(code, status.HTTP_500_INTERNAL_SERVER_ERROR)


@dataclass
class ResponseResult:
    """标准响应结果类"""
    code: int
    message: str
    data: Optional[Any] = None
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().timestamp()

    @classmethod
    def success(cls, 
                data: Any = None, 
                message: str = "操作成功",
                http_status: int = status.HTTP_200_OK,
                to_response: bool = True) -> Union[Response, 'ResponseResult']:
        """
        成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            http_status: HTTP状态码
            to_response: 是否直接返回Response对象
            
        Returns:
            Response 或 ResponseResult 对象
        """
        result = cls(
            code=ResponseCode.SUCCESS.value,
            message=message,
            data=data
        )
        return Response(result.to_dict(), status=http_status) if to_response else result

    @classmethod
    def error(cls, 
              code: int = ResponseCode.INTERNAL_ERROR.value, 
              message: str = "操作失败", 
              data: Any = None,
              to_response: bool = True) -> Union[Response, 'ResponseResult']:
        """
        错误响应
        
        Args:
            code: 错误码
            message: 错误消息
            data: 错误数据
            to_response: 是否直接返回Response对象
            
        Returns:
            Response 或 ResponseResult 对象
        """
        result = cls(
            code=code,
            message=message,
            data=data
        )
        
        http_status = ResponseCode.get_http_status(code)
        return Response(result.to_dict(), status=http_status) if to_response else result
        
    @classmethod
    def paginate(cls, 
                queryset, 
                page: int = 1, 
                page_size: int = 10, 
                message: str = "获取成功",
                item_formatter: Callable = None,
                to_response: bool = True) -> Union[Response, 'ResponseResult']:
        """
        分页响应
        
        Args:
            queryset: 查询集
            page: 页码
            page_size: 每页数量
            message: 响应消息
            item_formatter: 对象转换函数，用于转换查询结果为可序列化对象
            to_response: 是否直接返回Response对象
            
        Returns:
            Response 或 ResponseResult 对象
        """
        paginator = Paginator(queryset, page_size)
        total = paginator.count
        page_obj = paginator.get_page(page)
        
        # 如果提供了转换函数，使用它来转换对象
        if item_formatter and callable(item_formatter):
            result_list = [item_formatter(item) for item in page_obj]
        else:
            result_list = list(page_obj)
            
        data = {
            "list": result_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": paginator.num_pages
        }
        
        return cls.success(data=data, message=message, to_response=to_response)

    def to_dict(self) -> dict:
        """
        转换为字典格式，支持MySQL的datetime类型
        :return: dict
        """
        result = {
            "code": self.code,
            "message": self.message,
            "data": self.data,
            "timestamp": self.timestamp
        }
        # 使用MySQLJSONEncoder处理特殊类型
        return json.loads(json.dumps(result, cls=MySQLJSONEncoder))

    def to_json(self) -> str:
        """
        转换为JSON字符串
        :return: str
        """
        return json.dumps(self.to_dict(), ensure_ascii=False)