# Generated by Django 3.2.20 on 2025-05-21 14:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0037_auto_20250521_0927'),
    ]

    operations = [
        migrations.AddField(
            model_name='ailecturedocument',
            name='user',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='lecture_documents', to='zhkt.user', verbose_name='所属用户'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='ailecturedocument',
            name='file_type',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='文件类型'),
        ),
        migrations.AlterField(
            model_name='ailecturedocument',
            name='total_pages',
            field=models.IntegerField(blank=True, null=True, verbose_name='总页数'),
        ),
    ]
