import CourseLayout from '@/components/layout/CourseLayout.vue'

// 课程相关页面组件
const CourseChapter = () => import('@/views/course/CourseChapter.vue')
const CourseClass = () => import('@/views/course/CourseClass.vue')
const CourseQuestionBank = () => import('@/views/course/CourseQuestionBank.vue')
const CourseHomework = () => import('@/views/course/CourseHomework.vue')
const CourseGrades = () => import('@/views/course/CourseGrades.vue')
const CourseDoGrade = () => import('@/views/course/CourseDoGrade.vue')

export const courseRoutes = {
  path: '/course/:courseId',
  component: CourseLayout,
  props: route => ({
    courseId: route.params.courseId,
    courseName: route.query.name || '课程详情',
    courseCode: route.query.code || '',
    userName: route.query.teacherName || '',
    userAvatar: route.query.teacherAvatar || ''
  }),
  meta: { requiresAuth: true, role: 'teacher' },
  children: [
    {
      path: '',
      redirect: to => ({ name: 'course-chapter', params: { courseId: to.params.courseId } })
    },
    {
      path: 'chapter',
      name: 'course-chapter',
      breadcrumbName: '章节',
      component: CourseChapter,
      meta: { title: '章节' }
    },
    {
      path: 'class',
      name: 'course-class',
      breadcrumbName: '班级',
      component: CourseClass,
      meta: { title: '班级' }
    },
    {
      path: 'questionbank',
      name: 'course-question-bank',
      breadcrumbName: '题库',
      component: CourseQuestionBank,
      meta: { title: '题库' }
    },
    {
      path: 'homework',
      name: 'course-homework',
      breadcrumbName: '作业',
      component: CourseHomework,
      meta: { title: '作业' }
    },
    {
      path: 'grades',
      name: 'course-grades',
      breadcrumbName: '成绩',
      component: CourseGrades,
      meta: { title: '成绩' }
    },
    {
      path: 'dograde/:homeworkId',
      name: 'course-dograde',
      breadcrumbName: '批改',
      component: CourseDoGrade,
      props: route => ({
        courseId: route.params.courseId,
        homeworkId: route.params.homeworkId
      }),
      meta: { title: '批改' }
    },
  ]
} 