<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-2/3 lg:w-1/2 max-h-screen overflow-y-auto">
      <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">导入教案内容</h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-500">
          <el-icon><Close /></el-icon>
        </button>
      </div>
      <div class="p-6">
        <!-- 上传文件面板 -->
        <div>
          <div 
            class="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-6 text-center bg-gray-50 hover:bg-gray-100 transition-colors"
            @dragover.prevent="isDragging = true"
            @dragleave.prevent="isDragging = false"
            @drop.prevent="handleFileDrop"
            :class="{ 'border-blue-500 bg-blue-50': isDragging }"
          >
            <div class="mb-4">
              <el-icon class="text-5xl text-blue-500"><Upload /></el-icon>
            </div>
            <h4 class="text-gray-700 font-medium text-lg mb-2">拖拽或点击上传教案文件</h4>
            <p class="text-sm text-gray-500 mb-4">支持Word、TXT格式</p>
            <input 
              type="file" 
              ref="fileInput"
              class="hidden" 
              accept=".pdf,.docx,.doc,.pptx,.ppt,.md,.txt"
              @change="handleFileUpload"
            >
            <label 
              for="upload-plan-file" 
              class="inline-flex items-center px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
              @click="$refs.fileInput.click()"
            >
              <el-icon class="mr-2"><Document /></el-icon>
              选择文件
            </label>
          </div>
          
          <!-- 上传文件预览 -->
          <div v-if="uploadedFile" class="border rounded-md p-4 bg-blue-50">
            <div class="flex justify-between items-center mb-3">
              <div class="flex items-center">
                <el-icon :class="fileIcon" class="mr-2"></el-icon>
                <span class="text-sm font-medium">{{ uploadedFile.name }}</span>
              </div>
              <button @click="removeUploadedFile" class="text-gray-500 hover:text-gray-700">
                <el-icon><Close /></el-icon>
              </button>
            </div>
            <!-- 分析进度条 -->
            <div v-if="isAnalyzing" class="mt-3">
              <div class="w-full bg-gray-200 rounded-full h-1.5">
                <div 
                  class="bg-blue-600 h-1.5 rounded-full" 
                  :style="{ width: `${analyzeProgress}%` }"
                ></div>
              </div>
              <div class="flex justify-between items-center mt-1 text-xs text-gray-500">
                <span>正在分析文档...</span>
                <span>{{ analyzeProgress }}%</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 底部按钮 -->
        <div class="mt-6 flex justify-end">
          <button 
            @click="$emit('close')" 
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mr-3"
          >
            取消
          </button>
          <button 
            @click="confirmImport"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            :class="{ 'opacity-50 cursor-not-allowed': !canImport }"
            :disabled="!canImport"
          >
            导入所选教案
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  Close,
  Document,
  Upload
} from '@element-plus/icons-vue'

// Data
const uploadedFile = ref(null)
const isAnalyzing = ref(false)
const analyzeProgress = ref(0)
const isDragging = ref(false)

const fileIcon = computed(() => {
  if (!uploadedFile.value) return ''
  
  const extension = uploadedFile.value.name.split('.').pop().toLowerCase()
  const baseClass = 'text-lg '
  
  switch(extension) {
    case 'pdf':
      return baseClass + 'text-red-500'
    case 'doc':
    case 'docx':
      return baseClass + 'text-blue-500'
    case 'ppt':
    case 'pptx':
      return baseClass + 'text-orange-500'
    case 'md':
    case 'txt':
      return baseClass + 'text-gray-500'
    default:
      return baseClass + 'text-gray-500'
  }
})

const canImport = computed(() => {
  return uploadedFile.value && !isAnalyzing.value
})

const handleFileUpload = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    alert('文件大小不能超过10MB')
    return
  }
  
  uploadedFile.value = file
  analyzeProgress.value = 0
}

const handleFileDrop = (event) => {
  isDragging.value = false
  const file = event.dataTransfer.files[0]
  if (!file) return
  
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    alert('文件大小不能超过10MB')
    return
  }

  // 检查文件类型
  const allowedTypes = ['.pdf', '.docx', '.doc', '.pptx', '.ppt', '.md', '.txt']
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
  if (!allowedTypes.includes(fileExtension)) {
    alert('不支持的文件格式')
    return
  }
  
  uploadedFile.value = file
  analyzeProgress.value = 0
}

const removeUploadedFile = () => {
  uploadedFile.value = null
  analyzeProgress.value = 0
  isAnalyzing.value = false
}

const analyzeFile = async () => {
  if (!uploadedFile.value || isAnalyzing.value) return
  
  isAnalyzing.value = true
  analyzeProgress.value = 0
  
  // 模拟分析进度
  const interval = setInterval(() => {
    if (analyzeProgress.value < 100) {
      analyzeProgress.value += 5
    } else {
      clearInterval(interval)
      isAnalyzing.value = false
    }
  }, 100)
}

const formatFileSize = (bytes) => {
  if (bytes < 1024) return bytes + ' B'
  else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB'
  else return (bytes / 1048576).toFixed(1) + ' MB'
}

const confirmImport = () => {
  if (uploadedFile.value) {
    emit('import', {
      name: uploadedFile.value.name,
      points: Math.floor(Math.random() * 20) + 30, // 随机生成30-50个知识点
      size: uploadedFile.value.size,
      file: uploadedFile.value // 添加文件对象，确保文件能传递给父组件
    })
  }
  emit('close')
}

// Emits
const emit = defineEmits(['close', 'import'])
</script>

<style scoped>
.max-h-screen {
  max-height: 90vh;
}
</style> 