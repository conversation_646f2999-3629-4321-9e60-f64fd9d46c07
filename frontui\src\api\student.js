import request from '@/utils/request'

/**
 * 获取学生信息
 * @param {number} id - 学生ID
 * @returns {Promise} 返回学生信息
 */
export function getStudentInfo(id) {
  return request({
    url: `/students/${id}/`,
    method: 'get'
  })
}

/**
 * 获取当前登录学生信息
 * @returns {Promise} 返回当前学生信息
 */
export function getCurrentStudentInfo() {
  return request({
    url: '/students/current/',
    method: 'get'
  })
}

/**
 * 获取学生课程列表
 * @param {number} id - 学生ID
 * @param {Object} params - 查询参数，包含分页信息
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页条数
 * @returns {Promise} 返回学生课程列表
 */
export function getStudentCourses(id, params = {}) {
  return request({
    url: `/students/${id}/courses/`,
    method: 'get',
    params
  })
}

/**
 * 获取学生学习统计数据
 * @returns {Promise} 返回学习统计数据
 */
export function getStudentLearningStats() {
  return request({
    url: '/students/learning_stats/',
    method: 'get'
  })
} 