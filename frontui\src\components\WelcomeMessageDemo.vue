<template>
  <div class="welcome-message-demo">
    <h3>AI助手欢迎消息多语言演示</h3>
    
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
      <button 
        v-for="lang in languages" 
        :key="lang.code"
        @click="switchLanguage(lang.code)"
        :class="{ active: currentLang === lang.code }"
        class="lang-btn"
      >
        {{ lang.name }}
      </button>
    </div>
    
    <!-- 欢迎消息展示 -->
    <div class="welcome-messages">
      <div class="teacher-info">
        <div class="teacher-avatar">
          <i class="material-icons">school</i>
        </div>
        <div class="teacher-name">{{ teacherName }}</div>
      </div>
      
      <div class="messages-container">
        <div 
          v-for="(message, index) in welcomeMessages" 
          :key="index"
          class="message-bubble"
        >
          <div class="message-header">
            <span class="sender-name">{{ teacherName }}</span>
          </div>
          <div class="message-content">{{ message }}</div>
        </div>
      </div>
    </div>
    
    <!-- 当前语言显示 -->
    <div class="current-lang-info">
      <p>当前语言: {{ currentLangName }}</p>
      <p>教师名称: {{ teacherName }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WelcomeMessageDemo',
  data() {
    return {
      currentLang: 'zh',
      languages: [
        { code: 'zh', name: '中文' },
        { code: 'en', name: 'English' },
        { code: 'vi', name: 'Tiếng Việt' },
        { code: 'id', name: 'Bahasa Indonesia' }
      ],
      
      // AI角色定义（简化版）
      aiRoles: {
        assistant: {
          multiLangNames: {
            zh: '王老师',
            en: 'Prof. Wang',
            vi: 'GS. Wang',
            id: 'Prof. Wang'
          }
        }
      }
    }
  },
  
  computed: {
    teacherName() {
      return this.aiRoles.assistant.multiLangNames[this.currentLang] || this.aiRoles.assistant.multiLangNames.zh
    },
    
    welcomeMessages() {
      const teacherName = this.teacherName
      
      const messages = {
        zh: [
          `欢迎来到智慧课堂！我是${teacherName}，您的AI学习助手，随时为您解答问题。`,
          '您可以向我咨询课程内容，提出学习疑问，或者讨论相关知识点。'
        ],
        en: [
          `Welcome to Smart Classroom! I'm ${teacherName}, your AI learning assistant, ready to answer your questions anytime.`,
          'You can ask me about course content, raise learning questions, or discuss related knowledge points.'
        ],
        vi: [
          `Chào mừng đến với Lớp học Thông minh! Tôi là ${teacherName}, trợ lý học tập AI của bạn, sẵn sàng trả lời câu hỏi của bạn bất cứ lúc nào.`,
          'Bạn có thể hỏi tôi về nội dung khóa học, đặt câu hỏi học tập, hoặc thảo luận về các điểm kiến thức liên quan.'
        ],
        id: [
          `Selamat datang di Kelas Pintar! Saya ${teacherName}, asisten belajar AI Anda, siap menjawab pertanyaan Anda kapan saja.`,
          'Anda dapat bertanya kepada saya tentang konten kursus, mengajukan pertanyaan belajar, atau mendiskusikan poin pengetahuan terkait.'
        ]
      }
      
      return messages[this.currentLang] || messages.zh
    },
    
    currentLangName() {
      const lang = this.languages.find(l => l.code === this.currentLang)
      return lang ? lang.name : '未知'
    }
  },
  
  methods: {
    switchLanguage(langCode) {
      this.currentLang = langCode
      console.log('切换语言到:', langCode, '教师名称:', this.teacherName)
    }
  }
}
</script>

<style scoped>
.welcome-message-demo {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.language-switcher {
  margin-bottom: 20px;
  text-align: center;
}

.lang-btn {
  margin: 0 5px;
  padding: 8px 16px;
  border: 1px solid #409eff;
  background: white;
  color: #409eff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.lang-btn:hover {
  background: #ecf5ff;
}

.lang-btn.active {
  background: #409eff;
  color: white;
}

.welcome-messages {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.teacher-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.teacher-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.teacher-avatar i {
  color: white;
  font-size: 20px;
}

.teacher-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message-bubble {
  background: white;
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #409eff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-header {
  margin-bottom: 5px;
}

.sender-name {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.message-content {
  color: #606266;
  line-height: 1.5;
  font-size: 14px;
}

.current-lang-info {
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.current-lang-info p {
  margin: 5px 0;
}
</style>
