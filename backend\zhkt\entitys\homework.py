from django.db import models
from django.utils.translation import gettext_lazy as _

class Homework(models.Model):
    """作业模型"""
    course = models.ForeignKey('Course', on_delete=models.CASCADE, related_name='homeworks')
    title = models.CharField(_('作业标题'), max_length=200)
    description = models.TextField(_('作业描述'))
    start_time = models.DateTimeField(_('开始时间'))
    end_time = models.DateTimeField(_('截止时间'))
    total_score = models.IntegerField(_('总分'))
    is_online = models.BooleanField(_('是否在线作业'), default=False)
    is_published = models.BooleanField(_('是否已发布'), default=False)
    is_graded = models.BooleanField(_('是否已发布成绩'), default=False)
    publish_date = models.DateTimeField(_('发布时间'), null=True, blank=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('作业')
        verbose_name_plural = _('作业')

    def __str__(self):
        return f"{self.course.name} - {self.title}"

class HomeworkQuestion(models.Model):
    """作业题目关联模型"""
    homework = models.ForeignKey(Homework, on_delete=models.CASCADE, related_name='homework_questions')
    question = models.ForeignKey('Question', on_delete=models.CASCADE, related_name='homework_questions')
    order = models.IntegerField(_('题目顺序'), default=0)
    score = models.DecimalField(_('分值'), max_digits=5, decimal_places=2)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('作业题目')
        verbose_name_plural = _('作业题目')
        ordering = ['order']

    def __str__(self):
        return f"{self.homework.title} - 题目{self.order}"

class Submission(models.Model):
    """作业提交模型"""
    STATUS_CHOICES = (
        ('submitted', '已提交'),
        ('late', '迟交'),
    )
    
    homework = models.ForeignKey(Homework, on_delete=models.CASCADE, related_name='submissions')
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='submissions')
    content = models.TextField(_('提交内容'))
    file = models.FileField(_('提交文件'), upload_to='homework_submissions/', blank=True)
    status = models.CharField(_('提交状态'), max_length=20, choices=STATUS_CHOICES, null=True, blank=True)
    submitted_at = models.DateTimeField(_('提交时间'), null=True, blank=True)
    score = models.DecimalField(_('得分'), max_digits=5, decimal_places=2, null=True, blank=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('作业提交')
        verbose_name_plural = _('作业提交')
        unique_together = ['homework', 'student']

    def __str__(self):
        return f"{self.student.user.username} - {self.homework.title}"

class Feedback(models.Model):
    """作业反馈模型"""
    submission = models.ForeignKey(Submission, on_delete=models.CASCADE, related_name='feedbacks')
    teacher = models.ForeignKey('Teacher', on_delete=models.CASCADE, related_name='feedbacks')
    content = models.TextField(_('反馈内容'))
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    deleted_at = models.DateTimeField(_('删除时间'), null=True, blank=True)

    class Meta:
        verbose_name = _('作业反馈')
        verbose_name_plural = _('作业反馈')

    def __str__(self):
        return f"Feedback for {self.submission}" 