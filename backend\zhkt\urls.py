# 自己手动创建的urls
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView
)
from .view import (
    PPTViewSet,
    SubjectViewSet,
    KnowledgeViewSet,
    LessonPlanViewSet,
    VideoAssistantViewSet,
    SpeechDesignViewSet,
    BookshelfViewSet,
    DBPoolTestView,
    AudioViewSet,
    CustomTokenObtainPairView,
    UserViewSet, 
    RoleViewSet, 
    StudentViewSet, 
    TeacherViewSet, 
    AdminViewSet,
    CollegeViewSet, 
    MajorViewSet, 
    ClassGroupViewSet,
    CourseViewSet, 
    ChapterViewSet, 
    ResourceViewSet,
    HomeworkViewSet, 
    SubmissionViewSet, 
    FeedbackViewSet,
    PointsRecordViewSet, 
    ProductViewSet, 
    OrderViewSet,
    AIChatViewSet, 
    AIChatMessageViewSet, 
    AIPromptViewSet,
    DeptViewSet, 
    MenuViewSet,
    LessonViewSet, 
    NoteViewSet,
    CommentViewSet, 
    CourseOverviewViewSet,
    QuestionViewSet,
    DigitalHumanViewSet
)
from .view.video_generation_view import VideoGenerationViewSet

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'users', UserViewSet)
router.register(r'roles', RoleViewSet)
router.register(r'students', StudentViewSet)
router.register(r'teachers', TeacherViewSet)
router.register(r'admins', AdminViewSet)
router.register(r'depts', DeptViewSet)
router.register(r'menus', MenuViewSet)
router.register(r'colleges', CollegeViewSet)
router.register(r'majors', MajorViewSet)
router.register(r'class-groups', ClassGroupViewSet, basename='class-group')
router.register(r'courses', CourseViewSet)
router.register(r'course-overviews', CourseOverviewViewSet)
router.register(r'chapters', ChapterViewSet)
router.register(r'resources', ResourceViewSet)
router.register(r'homeworks', HomeworkViewSet)
router.register(r'submissions', SubmissionViewSet)
router.register(r'feedbacks', FeedbackViewSet)
router.register(r'points-records', PointsRecordViewSet)
router.register(r'products', ProductViewSet)
router.register(r'orders', OrderViewSet)
router.register(r'ai-chats', AIChatViewSet)
router.register(r'ai-chat-messages', AIChatMessageViewSet)
router.register(r'ai-prompts', AIPromptViewSet)
router.register(r'knowledge', KnowledgeViewSet, basename='knowledge')
router.register(r'ppt', PPTViewSet, basename='ppt')
router.register(r'subjects', SubjectViewSet, basename='subjects')
router.register(r'lesson-plan', LessonPlanViewSet, basename='lesson-plan')
router.register(r'lessons', LessonViewSet)
router.register(r'video-assistant', VideoAssistantViewSet, basename='video-assistant')
router.register(r'notes', NoteViewSet, basename='note')
router.register(r'speech-design', SpeechDesignViewSet, basename='speech-design')
router.register(r'comments', CommentViewSet, basename='comment')
router.register(r'bookshelf', BookshelfViewSet, basename='bookshelf')
router.register(r'questions', QuestionViewSet, basename='question')
router.register(r'audio', AudioViewSet, basename='audio')
router.register(r'digital-human', DigitalHumanViewSet, basename='digital-human')
router.register(r'video-generation', VideoGenerationViewSet, basename='video-generation')

urlpatterns = [
    # JWT认证相关URL
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),  # 获取token
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),  # 刷新token
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),  # 验证token
    
    # 包含路由器生成的URL
    path('', include(router.urls)),
    
    # 数据库连接池测试视图
    path('db-pool-test/', DBPoolTestView.as_view(), name='db-pool-test'),
]