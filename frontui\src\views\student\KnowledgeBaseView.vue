<template>
  <StudentLayout
    :userName="studentData.name"
    :userAvatar="studentData.avatar"
    pageTitle="本地知识库"
    activePage="teaching-materials"
  >
    <div class="flex flex-col h-full" >
      <!-- 顶部搜索区 - 居中 -->
      <div class="p-4 bg-white border-b flex rounded-tl-lg rounded-tr-lg">
        <div class="relative w-full max-w-lg">
          <input 
            type="text" 
            v-model="searchQuery"
            placeholder="搜索资源..." 
            class="w-full border border-gray-300 rounded-md pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            @input="handleSearchInput"
            @keyup.enter="performSearch"
          />
          <i class="material-icons-outlined absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
            <template v-if="isSearching">hourglass_top</template>
            <template v-else>search</template>
          </i>
          <div v-if="searchQuery" class="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer" @click="clearSearch">
            <i class="material-icons-outlined text-gray-400 text-sm hover:text-gray-600">close</i>
          </div>
        </div>
      </div>
      
      <div class="flex flex-1 overflow-hidden">
        <!-- 左侧菜单 -->
        <div class="w-64 bg-white border-r p-4 overflow-y-auto rounded-bl-lg">
          <ul class="space-y-1">
            <!-- 最近和收藏 -->
            <li :class="['flex items-center px-3 py-2 rounded cursor-pointer text-sm', leftMenuActive === 'recent' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-100']" @click="selectLeftMenu('recent')">
              <i class="material-icons-outlined mr-2 text-xs">history</i> 
              <span>全部</span>
            </li>
            <li :class="['flex items-center px-3 py-2 rounded cursor-pointer text-sm', leftMenuActive === 'favorite' ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-100']" @click="selectLeftMenu('favorite')">
              <i class="material-icons-outlined mr-2 text-xs">star</i> 
              <span>收藏</span>
            </li>
            
            <!-- 动态渲染大分类 -->
            <template v-for="(category, index) in mainCategories" :key="category.id">
              <li class="mt-3" v-if="index === 0 || index > 0">
              <div 
                class="flex items-center justify-between px-3 py-2 cursor-pointer hover:bg-gray-100 rounded"
                  @click="togglePanel(category.category_type)"
              >
                <div class="flex items-center">
                    <i class="material-icons-outlined mr-2 text-xs">{{ category.category_type === 'personal' ? 'folder' : 'school' }}</i>
                    <span class="font-medium text-gray-700 text-sm">{{ category.name }}</span>
                </div>
                <div class="flex items-center">
                    <button v-if="openPanels[category.category_type]" class="text-blue-500 hover:text-blue-700 mr-1" @click.stop="showAddCategoryModal(category.category_type, $event)">
                    <i class="material-icons-outlined text-sm">add</i>
                  </button>
                    <i class="material-icons-outlined text-sm text-gray-500">{{ openPanels[category.category_type] ? 'expand_less' : 'expand_more' }}</i>
                </div>
              </div>
              
                <!-- 个人文档子分类 -->
                <ul v-if="category.category_type === 'personal' && openPanels[category.category_type]" class="ml-3 mt-1 space-y-1 animate-fadeIn">
                <li 
                  v-for="cat in myDocCategories" 
                  :key="cat.id" 
                  :class="['flex items-center justify-between px-2 py-1.5 rounded text-sm cursor-pointer group', 
                      leftMenuActive === `${category.category_type}-${cat.id}` ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-100']"
                    @click="selectLeftMenu(`${category.category_type}-${cat.id}`)"
                >
                  <span class="truncate">{{ cat.name }}</span>
                  <span class="flex items-center opacity-0 group-hover:opacity-100">
                      <button class="p-0.5 text-gray-400 hover:text-blue-600" @click.stop="editCategory(category.category_type, cat, $event)">
                      <i class="material-icons-outlined text-xs">edit</i>
                    </button>
                      <button class="p-0.5 text-gray-400 hover:text-red-600" @click.stop="deleteCategory(category.category_type, cat, $event)">
                      <i class="material-icons-outlined text-xs">delete</i>
                    </button>
                  </span>
                </li>
              </ul>
                
                <!-- 知识库子分类 -->
                <ul v-if="category.category_type === 'knowledge' && openPanels[category.category_type]" class="ml-3 mt-1 space-y-1 animate-fadeIn">
                <!-- 自定义知识库分类 -->
                <li 
                  v-for="cat in customKnowledgeCategories" 
                  :key="cat.id" 
                  :class="['flex items-center justify-between px-2 py-1.5 rounded text-sm cursor-pointer group', 
                      leftMenuActive === `${category.category_type}-${cat.id}` ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-100']"
                    @click="selectLeftMenu(`${category.category_type}-${cat.id}`)"
                >
                  <span class="truncate">{{ cat.name }}</span>
                  <span class="flex items-center opacity-0 group-hover:opacity-100">
                      <button class="p-0.5 text-gray-400 hover:text-blue-600" @click.stop="editCategory(category.category_type, cat, $event)">
                      <i class="material-icons-outlined text-xs">edit</i>
                    </button>
                      <button class="p-0.5 text-gray-400 hover:text-red-600" @click.stop="deleteCategory(category.category_type, cat, $event)">
                      <i class="material-icons-outlined text-xs">delete</i>
                    </button>
                  </span>
                </li>
              </ul>
            </li>
            </template>
          </ul>
        </div>
        
        <!-- 右侧主区 -->
        <div class="flex-1 flex flex-col overflow-hidden rounded-br-lg">
          <!-- 功能区+资源列表 -->
          <div 
            class="flex-1 flex flex-col overflow-auto"
            :class="{
              'space-y-6 p-4': !['recent', 'favorite'].includes(leftMenuActive),
              'space-y-2 p-4 pt-2': ['recent', 'favorite'].includes(leftMenuActive)
            }"
          >
            <!-- 功能区 -->
            <div class="flex flex-wrap justify-between items-center gap-4 mb-0">
              <!-- 仅当不是"最近"和"收藏"时显示这些按钮 -->
              <div class="flex gap-3" v-if="!['recent', 'favorite'].includes(leftMenuActive)">
                <button class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center gap-2 text-sm" @click="showUploadDialog">
                  <i class="material-icons-outlined text-xs">upload</i> 上传资源
                </button>
                
                <button 
                  class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md flex items-center gap-2 text-sm"
                  @click="deleteSelectedResources"
                  :disabled="selectedResources.length === 0"
                  :class="{ 'opacity-50 cursor-not-allowed': selectedResources.length === 0 }"
                >
                  <i class="material-icons-outlined text-xs">delete</i> 删除选中
                </button>
              </div>
              <!-- 当是"最近"和"收藏"时显示空占位符，保持布局一致性 -->
              <div v-else></div>
            </div>
            
            <!-- 资源列表 -->
            <div class="flex-1 flex flex-col">
              <!-- 动态标题 -->
              <div class="text-xl font-semibold text-gray-700 mb-4 px-2">
                {{ getDisplayTitle() }}
              </div>
              
              <!-- 资源表格 -->
              <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                <!-- 骨架屏加载状态 -->
                <div v-if="loading" class="p-4">
                  <div v-for="i in 3" :key="i" class="animate-pulse mb-4">
                    <div class="flex items-center space-x-4">
                      <div class="w-4 h-4 bg-gray-200 rounded"></div>
                      <div class="flex-1 py-1">
                        <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div class="h-4 bg-gray-200 rounded w-20"></div>
                      <div class="h-4 bg-gray-200 rounded w-20"></div>
                      <div class="h-4 bg-gray-200 rounded w-16"></div>
                      <div class="h-4 bg-gray-200 rounded w-24"></div>
                    </div>
                  </div>
                </div>

                <!-- 数据表格 -->
                <table v-else class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="w-12 px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input 
                          type="checkbox" 
                          @change="toggleSelectAllResources"
                          :checked="isAllResourcesSelected"
                          class="rounded text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                      <th scope="col" class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        资源名称
                      </th>
                      <th scope="col" class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        资源类型
                      </th>
                      <th scope="col" class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        解析状态
                      </th>
                      <th scope="col" class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        大小
                      </th>
                      <th scope="col" class="px-6 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        最近修改
                      </th>
                      <th scope="col" class="px-6 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(resource, index) in filteredResources" :key="index" class="hover:bg-gray-50 transition-colors duration-150">
                      <td class="px-3 py-3 whitespace-nowrap">
                        <input 
                          type="checkbox"
                          :checked="selectedResources.includes(resource.id)"
                          @change="toggleSelectResource(resource.id)"
                          class="rounded text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td class="px-6 py-3 whitespace-nowrap text-left">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 h-10 w-10 rounded-lg flex items-center justify-center" :class="getBgColorForType(resource.type)">
                            <i class="material-icons-outlined text-white text-lg">
                              {{ getIconForType(resource.type) }}
                            </i>
                          </div>
                          <div class="ml-3">
                            <div class="text-sm font-medium text-gray-900 flex items-center">
                              <span class="truncate max-w-xs">{{ resource.name }}</span>
                              <!-- 如果是最近解析的文档，显示"NEW"标签 -->
                              <span v-if="isRecentlyProcessed(resource)" class="ml-2 px-1.5 py-0.5 text-xs font-semibold bg-green-100 text-green-800 rounded">NEW</span>
                            </div>
                            <div class="text-xs text-gray-500 mt-0.5">
                              <span v-if="resource.chunk_count" class="inline-flex items-center mr-2">
                                <i class="material-icons-outlined text-xs mr-0.5">data_object</i> 
                                {{ resource.chunk_count }} 分块
                              </span>
                              <span v-if="resource.token_count" class="inline-flex items-center">
                                <i class="material-icons-outlined text-xs mr-0.5">token</i> 
                                {{ resource.token_count }} tokens
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-3 whitespace-nowrap">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full" 
                          :class="getTagColorForType(resource.type)">
                          {{ resource.type }}
                        </span>
                      </td>
                      <td class="px-6 py-3 whitespace-nowrap">
                        <div class="flex flex-col">
                          <div class="flex items-center">
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full mr-2" 
                          :class="getStatusColorClass(resource.parseStatus)">
                          {{ resource.parseStatus }}
                        </span>
                            <!-- 如果正在解析中，显示进度条 -->
                            <div v-if="resource.parseStatus === '解析中'" class="flex items-center">
                              <div class="w-16 bg-gray-200 rounded-full h-1.5 mr-2 overflow-hidden">
                                <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-300" :style="{ width: `${resource.progress * 100}%` }"></div>
                              </div>
                              <span class="text-xs text-gray-500">{{ Math.round(resource.progress * 100) }}%</span>
                            </div>
                          </div>
                          <!-- 显示解析耗时 -->
                          <div v-if="resource.process_duration && resource.parseStatus === '成功'" class="text-xs text-gray-500 mt-0.5">
                            解析耗时: {{ formatDuration(resource.process_duration) }}
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                        {{ formatFileSize(resource.size) }}
                      </td>
                      <td class="px-6 py-3 whitespace-nowrap">
                        <div class="text-sm text-gray-500">{{ resource.uploadTime }}</div>
                        <!-- 添加相对时间 -->
                        <div class="text-xs text-gray-400">{{ getRelativeTime(resource.uploadTime) }}</div>
                      </td>
                      <td class="px-6 py-3 whitespace-nowrap text-right text-sm font-medium">
                        <!-- 操作按钮 -->
                        <div class="flex justify-end space-x-2">
                          <!-- 解析按钮 -->
                          <button 
                            v-if="resource.parseStatus === '未解析' || resource.parseStatus === '失败'"
                            class="transition-colors duration-150 text-blue-600 hover:text-blue-900 hover:bg-blue-50 p-1 rounded-full" 
                            @click="parseDocument(resource)"
                            title="解析文档">
                            <i class="material-icons-outlined text-sm">data_object</i>
                          </button>
                          
                          <!-- 预览按钮 -->
                          <button 
                            class="transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 p-1 rounded-full" 
                            @click="previewResource(resource)"
                            title="预览文档">
                            <i class="material-icons-outlined text-sm">visibility</i>
                          </button>
                          
                          <!-- 下载按钮 -->
                          <button 
                            class="transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 p-1 rounded-full" 
                            @click="downloadResource(resource)"
                            title="下载文档">
                            <i class="material-icons-outlined text-sm">download</i>
                          </button>
                          
                          <!-- 收藏按钮 -->
                          <button 
                            :class="[resource.isFavorite ? 'text-yellow-500 hover:text-yellow-700' : 'text-gray-400 hover:text-yellow-500', 'transition-colors duration-150 hover:bg-gray-50 p-1 rounded-full']"
                            @click="toggleFavorite(resource)"
                            :title="resource.isFavorite ? '取消收藏' : '收藏文档'">
                            <i class="material-icons-outlined text-sm">
                              {{ resource.isFavorite ? 'star' : 'star_border' }}
                            </i>
                          </button>
                          
                          <!-- 删除按钮 -->
                          <button 
                            class="transition-colors duration-150 text-red-600 hover:text-red-900 hover:bg-red-50 p-1 rounded-full" 
                            @click="deleteResource(resource)"
                            title="删除文档">
                            <i class="material-icons-outlined text-sm">delete</i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <!-- 空状态优化 -->
                <div v-if="!loading && filteredResources.length === 0" class="py-16 flex flex-col items-center justify-center text-gray-500">
                  <i class="material-icons-outlined text-5xl mb-4 text-gray-300">folder_open</i>
                  <p class="text-lg font-medium mb-2">暂无资源</p>
                  <p class="text-sm text-gray-400 mb-4">在此分类下还没有上传任何文档</p>
                  <button 
                    v-if="!['recent', 'favorite'].includes(leftMenuActive)"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-150 flex items-center"
                    @click="showUploadDialog">
                    <i class="material-icons-outlined text-sm mr-1">upload</i>
                    上传文件
                  </button>
                </div>

                <!-- 分页优化 -->
                <div v-if="!loading && filteredResources.length > 0" class="px-6 py-4 flex items-center justify-between border-t border-gray-200 bg-gray-50">
                  <div class="flex-1 flex justify-between items-center">
                    <div>
                      <p class="text-sm text-gray-700">
                        显示 <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> 到 
                        <span class="font-medium">{{ Math.min(currentPage * pageSize, totalResources) }}</span> 条，
                        共 <span class="font-medium">{{ totalResources }}</span> 条记录
                      </p>
                    </div>
                    <div>
                      <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10, 20, 50]"
                        :total="totalResources"
                        layout=" prev, pager, next"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 上传文件对话框 -->
    <div v-if="showUploadModal" class="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-lg">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">上传资源</h3>
          <button @click="showUploadModal = false" class="text-gray-400 hover:text-gray-500">
            <i class="material-icons-outlined">close</i>
          </button>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">选择知识库</label>
              <select 
                v-model="uploadForm.folder" 
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="root" disabled>请选择知识库</option>
                <optgroup label="个人文档">
                  <option 
                    v-for="dataset in availableDatasets.filter(d => d.type === 'personal')" 
                    :key="dataset.id" 
                    :value="dataset.id"
                  >
                    {{ dataset.name }}
                  </option>
                </optgroup>
                <optgroup label="知识库">
                  <option 
                    v-for="dataset in availableDatasets.filter(d => d.type === 'knowledge')" 
                    :key="dataset.id" 
                    :value="dataset.id"
                  >
                    {{ dataset.name }}
                  </option>
                </optgroup>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">选择文件</label>
              <div 
                class="border-2 border-dashed border-gray-300 rounded-md px-6 pt-5 pb-6 flex flex-col items-center"
                @dragover.prevent
                @drop.prevent="onFileDrop"
              >
                <i class="material-icons-outlined text-4xl text-gray-400 mb-3">cloud_upload</i>
                <div class="flex text-sm text-gray-600">
                  <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                    <span>选择文件</span>
                    <input 
                      ref="fileInput"
                      type="file" 
                      multiple 
                      class="sr-only" 
                      @change="handleFileSelect"
                    />
                  </label>
                  <p class="pl-1">或拖放文件到此处</p>
                </div>
                <p class="text-xs text-gray-500 mt-1">
                  支持多个文件，每个文件不超过200MB
                </p>
              </div>
            </div>
            
            <div v-if="selectedFiles.length > 0">
              <h4 class="text-sm font-medium text-gray-700 mb-2">已选择的文件：{{ selectedFiles.length }}个</h4>
              <ul class="space-y-2 max-h-60 overflow-y-auto pr-2 custom-scrollbar">
                <li v-for="(file, index) in selectedFiles" :key="index" class="flex items-center justify-between text-sm">
                  <div class="flex items-center overflow-hidden">
                    <i class="material-icons-outlined mr-2 text-gray-500 flex-shrink-0">
                      {{ getIconForFile(file) }}
                    </i>
                    <span class="truncate max-w-[180px]">{{ file.name }}</span>
                    <span class="ml-2 text-xs text-gray-500 flex-shrink-0">{{ formatFileSize(file.size) }}</span>
                  </div>
                  <button @click="removeFile(index)" class="text-red-500 hover:text-red-700 flex-shrink-0">
                    <i class="material-icons-outlined text-sm">delete</i>
                  </button>
                </li>
              </ul>
              <div v-if="selectedFiles.length > 10" class="mt-2 text-xs text-gray-500">
                已选择{{ selectedFiles.length }}个文件
              </div>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
          <button 
            class="mr-3 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            @click="showUploadModal = false"
          >
            取消
          </button>
          <button 
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center"
            @click="uploadResource"
            :disabled="uploading || selectedFiles.length === 0 || uploadForm.folder === 'root'"
            :class="{ 'opacity-50 cursor-not-allowed': uploading || selectedFiles.length === 0 || uploadForm.folder === 'root' }"
          >
            <i class="material-icons-outlined mr-1 text-sm" v-if="uploading">hourglass_top</i>
            <span>{{ uploading ? '上传中...' : '上传' }}</span>
          </button>
        </div>
      </div>
    </div>
  
  <!-- 添加分类弹窗 -->
  <div v-if="showCategoryModal" class="fixed inset-0 z-50 flex items-start justify-center" @click="closeCategoryModal">
    <div class="absolute" :style="modalPosition" @click.stop>
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 w-64 overflow-hidden">
        <div class="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-base font-medium text-gray-800">{{ modalTitle }}</h3>
          <button @click="closeCategoryModal" class="text-gray-400 hover:text-gray-500">
            <i class="material-icons-outlined text-sm">close</i>
          </button>
        </div>
        <div class="p-4">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">分类名称</label>
            <input 
              type="text" 
              v-model="categoryInput" 
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              ref="categoryInputRef"
              @keyup.enter="confirmCategoryModal"
            />
          </div>
          <div class="flex justify-end space-x-2">
            <button @click="closeCategoryModal" class="px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded">
              取消
            </button>
            <button @click="confirmCategoryModal" class="px-3 py-1.5 text-sm bg-blue-600 text-white hover:bg-blue-700 rounded">
              确认
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 删除确认弹窗 -->
  <div v-if="showDeleteModal" class="fixed inset-0 z-50 flex items-start justify-center" @click="closeDeleteModal">
    <div class="absolute" :style="modalPosition" @click.stop>
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 w-64 overflow-hidden">
        <div class="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-base font-medium text-gray-800">删除确认</h3>
          <button @click="closeDeleteModal" class="text-gray-400 hover:text-gray-500">
            <i class="material-icons-outlined text-sm">close</i>
          </button>
        </div>
        <div class="p-4">
          <p class="mb-4 text-sm text-gray-600">确定要删除"{{ categoryToDelete?.name }}"分类吗？</p>
          <div class="flex justify-end space-x-2">
            <button @click="closeDeleteModal" class="px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded">
              取消
            </button>
            <button @click="confirmDeleteCategory" class="px-3 py-1.5 text-sm bg-red-600 text-white hover:bg-red-700 rounded">
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  </StudentLayout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import StudentLayout from '@/components/layout/StudentLayout.vue'
import teacherAvatar from '@/assets/images/avatars/teacher1.png'
import { knowledgeApi } from '@/api/knowledge' // 导入知识库API
import { ElMessage, ElMessageBox } from 'element-plus' // 引入Element Plus消息组件

import studentAvatar from '@/assets/images/avatars/student1.png'

// Student Data
const studentData = ref({
  name: '张同学',
  avatar: studentAvatar,
  department: '计算机科学与技术学院',
  grade: '2021级'
})

// 左侧菜单状态
const leftMenuActive = ref('recent') // 'recent', 'favorite', 'mydoc-x', 'knowledge-x'

// 主分类数据 - 从API获取
const mainCategories = ref([])

// 折叠面板状态 - 改为使用动态键
const openPanels = ref({})

// 搜索相关状态
const searchQuery = ref('')
const isSearching = ref(false)
const searchDebounceTimer = ref(null)
const isSearchMode = ref(false)
const searchResults = ref({
  list: [],
  total: 0,
  page: 1,
  page_size: 10,
  total_pages: 0
})

// 防抖处理函数
const debounce = (fn, delay) => {
  return function(...args) {
    if(searchDebounceTimer.value) clearTimeout(searchDebounceTimer.value)
    searchDebounceTimer.value = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 搜索输入处理 - 使用防抖
const handleSearchInput = debounce(() => {
  if (searchQuery.value.trim().length > 1) {
    performSearch()
  } else if (searchQuery.value.trim().length === 0) {
    exitSearchMode()
  }
}, 500)

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  try {
    isSearching.value = true
    isSearchMode.value = true
    
    // 调用API进行搜索
    const response = await knowledgeApi.searchDocuments(
      searchQuery.value,
      currentPage.value,
      pageSize.value
    )
    
    if (response.code === 200) {
      // 处理搜索结果
      searchResults.value = response.data
      
      // 更新展示的资源列表
      const docs = searchResults.value.list.map(doc => ({
        ...doc,
        dataset_name: doc.dataset_name || '搜索结果'
      }))
      
      resources.value = docs.map(doc => convertApiDocToResource(doc))
      
      // 更新分页数据
      totalResources.value = searchResults.value.total
      
      // 立即检查一次"解析中"文档的状态
      setTimeout(() => {
        updateRunningDocumentsStatus()
      }, 500)
    } else {
      ElMessage.error('搜索失败：' + response.message)
    }
  } catch (error) {
    console.error('搜索出错:', error)
    ElMessage.error('搜索失败')
  } finally {
    isSearching.value = false
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  exitSearchMode()
}

// 退出搜索模式
const exitSearchMode = () => {
  if (!isSearchMode.value) return
  
  isSearchMode.value = false
  searchResults.value = {
    list: [],
    total: 0,
    page: 1,
    page_size: 10,
    total_pages: 0
  }
  
  // 恢复原来的视图
  refreshCurrentView()
}

// 添加加载状态
const loading = ref(false)

// 我的文档分类 - 修改为从API动态获取
const myDocCategories = ref([])

// 知识库分类 - 修改为从API动态获取
const customKnowledgeCategories = ref([])

// 弹窗状态控制
const showCategoryModal = ref(false)
const showDeleteModal = ref(false)
const categoryInput = ref('')
const categoryInputRef = ref(null)
const modalTitle = ref('')
const modalPosition = ref({})
const modalMode = ref('') // 'add' 或 'edit'
const modalCategoryType = ref('') // 'personal' 或 'knowledge'
const categoryToEdit = ref(null)
const categoryToDelete = ref(null)

// 显示状态控制
const showUploadModal = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const fileInput = ref(null)

// 选择控制
const selectedResources = ref([])
const selectedFiles = ref([])
const selectedFilters = ref({
  types: [],
  subjects: []
})

// 上传表单
const uploadForm = ref({
  subject: '',
  folder: 'root',
  tags: '',
  description: ''
})

// 选项数据
const subjects = ref([
  '数据结构',
  '计算机网络',
  '人工智能',
  '软件工程',
  '操作系统',
  '数据库系统',
  '编译原理',
  '计算机组成原理'
])

const folders = ref([
  '课件',
  '教案',
  '习题',
  '参考资料',
  '实验指导',
  '教学视频'
])

// 模拟资源数据
const resources = ref([])

// 计算属性
const filteredResources = computed(() => {
  // 如果是搜索模式或在其他特定模式下，直接使用已加载的资源
  if (isSearchMode.value) {
    return resources.value;
  }
  
  let result = [...resources.value]
  
  // 筛选条件过滤
  if (selectedFilters.value.types.length > 0) {
    result = result.filter(resource => selectedFilters.value.types.includes(resource.type))
  }
  
  if (selectedFilters.value.subjects.length > 0) {
    result = result.filter(resource => selectedFilters.value.subjects.includes(resource.subject))
  }
  
  return result
})

const totalResources = ref(0)
// 从计算属性改为普通的ref变量，初始化为0
const totalPages = computed(() => Math.ceil(filteredResources.value.length / pageSize.value))

// 按类型统计资源数量
const countByType = (type) => {
  return resources.value.filter(resource => resource.type === type).length
}

// 文件类型图标
const getIconForType = (type) => {
  const iconMap = {
    '文档': 'description',
    '演示文稿': 'slideshow',
    '表格': 'table_chart',
    '图片': 'image',
    '视频': 'video_file',
    '音频': 'audio_file',
    '压缩包': 'folder_zip',
    '文本': 'text_snippet',
    '其他': 'insert_drive_file'
  }
  
  return iconMap[type] || 'insert_drive_file'
}

// 获取标签颜色
const getTagColorForType = (type) => {
  const colorMap = {
    '文档': 'bg-blue-100 text-blue-800',
    '演示文稿': 'bg-orange-100 text-orange-800',
    '表格': 'bg-green-100 text-green-800',
    '图片': 'bg-purple-100 text-purple-800',
    '视频': 'bg-red-100 text-red-800',
    '音频': 'bg-pink-100 text-pink-800',
    '压缩包': 'bg-gray-100 text-gray-800',
    '文本': 'bg-teal-100 text-teal-800',
    '其他': 'bg-gray-100 text-gray-800'
  }
  
  return colorMap[type] || 'bg-gray-100 text-gray-800'
}

// 获取文件图标
const getIconForFile = (file) => {
  return getIconForType(getTypeForFile(file))
}

// 从文件对象获取文件类型
const getTypeForFile = (file) => {
  if (!file || !file.name) return '';
  
  // 从文件名中提取扩展名
  const lastDotIndex = file.name.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  
  return file.name.substring(lastDotIndex + 1);
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 方法
const toggleFilter = (filterType, value) => {
  const index = selectedFilters.value[filterType].indexOf(value)
  if (index === -1) {
    selectedFilters.value[filterType].push(value)
  } else {
    selectedFilters.value[filterType].splice(index, 1)
  }
}

const toggleSelectResource = (resourceId) => {
  const index = selectedResources.value.indexOf(resourceId)
  if (index === -1) {
    selectedResources.value.push(resourceId)
  } else {
    selectedResources.value.splice(index, 1)
  }
}

const navigateToFolder = (index) => {
  // 导航到指定层级的文件夹
  currentPath.value = currentPath.value.slice(0, index + 1)
}

const handleFileSelect = (event) => {
  const files = event.target.files
  if (files) {
    for (let i = 0; i < files.length; i++) {
      selectedFiles.value.push(files[i])
    }
  }
}

const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
}

const uploadResource = async () => {
  // 验证是否选择了数据集
  if (!uploadForm.value.folder || uploadForm.value.folder === 'root') {
    ElMessage.warning('请选择要上传到的知识库')
    return
  }
  
  // 验证是否选择了文件
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }
  
  try {
    uploading.value = true
    
    // 创建FormData对象
    const formData = new FormData()
    
    // 添加文件
    for (const file of selectedFiles.value) {
      formData.append('files', file)
    }
    
    // 如果有图标，添加图标信息
    if (uploadForm.value.icon) {
      formData.append('icon', uploadForm.value.icon)
    }
    
    // 获取选择的数据集ID
    const datasetId = uploadForm.value.folder
    
    // 调用API上传文件
    const response = await knowledgeApi.uploadDocuments(datasetId, formData)
    
    if (response.code === 200) {
      ElMessage.success(`成功上传 ${response.data.count} 个文件`)
      
      // 获取上传的文档ID列表，用于后续解析
      const documentIds = response.data.document_ids
      
      // 自动解析文档
      if (documentIds && documentIds.length > 0) {
        try {
          // 调用解析API
          const parseResponse = await knowledgeApi.parseDocuments(datasetId, documentIds)
          
          if (parseResponse.code === 200) {
            ElMessage.success('文档已开始解析，请稍后查看结果')
            
            // 开始监控解析进度
            startParseProgressMonitor(datasetId, documentIds)
          } else {
            ElMessage.warning('文档上传成功，但解析启动失败：' + parseResponse.message)
          }
        } catch (parseError) {
          console.error('解析文档出错:', parseError)
          ElMessage.warning('文档上传成功，但解析过程出错')
        }
      }
      
      // 重置上传表单和文件列表
      resetUploadForm()
      
      // 关闭上传对话框
      showUploadModal.value = false
      
      // 刷新当前视图
      refreshCurrentView()
    } else {
      ElMessage.error('上传文件失败：' + response.message)
    }
  } catch (error) {
    console.error('上传文件出错:', error)
    ElMessage.error('上传文件失败')
  } finally {
    uploading.value = false
  }
}

// 重置上传表单
const resetUploadForm = () => {
  uploadForm.value = {
    subject: '',
    folder: 'root',
    tags: '',
    description: '',
    icon: ''
  }
  
  selectedFiles.value = []
  
  // 重置文件输入
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 监控解析进度
const progressTimer = ref(null)
const startParseProgressMonitor = (datasetId, documentIds) => {
  // 清除现有定时器
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
  }
  
  // 设置新定时器，每5秒查询一次进度
  progressTimer.value = setInterval(async () => {
    try {
      // 获取文档状态
      const response = await knowledgeApi.getDocumentsStatus(datasetId, documentIds)
      
      if (response.code === 200) {
        const docs = response.data
        
        // 检查是否所有文档都解析完成
        const allDone = docs.every(doc => doc.run === 'DONE' || doc.run === 'CANCEL')
        
        if (allDone) {
          // 所有文档解析完成，停止监控
          clearInterval(progressTimer.value)
          progressTimer.value = null
          
          // 刷新当前视图
          refreshCurrentView()
          
          ElMessage.success('所有文档解析完成')
        }
      }
    } catch (error) {
      console.error('获取解析进度出错:', error)
    }
  }, 5000) // 改为5秒
}

// 新增: 启动文档状态监控
const startDocumentStatusMonitor = () => {
  // 清除现有定时器
  if (documentStatusTimer.value) {
    clearInterval(documentStatusTimer.value)
  }
  
  // 设置新定时器，每5秒检查一次当前页面的"解析中"文档
  documentStatusTimer.value = setInterval(() => {
    updateRunningDocumentsStatus()
  }, 5000)
}

// 新增: 更新当前页面中所有"解析中"状态的文档
const updateRunningDocumentsStatus = async () => {
  // 检查当前页面是否有文档
  if (resources.value.length === 0) return
  
  try {
    // 按数据集分组收集所有"解析中"状态的文档
    const runningDocs = new Map() // Map<datasetId, Array<documentId>>
    
    resources.value.forEach(resource => {
      // 只关注"解析中"状态的文档
      if (resource.run === 'RUNNING') {
        if (!runningDocs.has(resource.dataset_id)) {
          runningDocs.set(resource.dataset_id, [])
        }
        runningDocs.get(resource.dataset_id).push(resource.id)
      }
    })
    
    // 如果没有"解析中"的文档，不需要发送请求
    if (runningDocs.size === 0) return
    
    // 对每个数据集的解析中文档发送请求
    for (const [datasetId, documentIds] of runningDocs.entries()) {
      // 获取文档最新状态
      const response = await knowledgeApi.getDocumentsStatus(datasetId, documentIds)
      
      if (response.code === 200) {
        const updatedDocs = response.data
        
        // 更新本地文档状态
        resources.value = resources.value.map(resource => {
          const updatedDoc = updatedDocs.find(doc => doc.id === resource.id)
          if (updatedDoc) {
            return {
              ...resource,
              run: updatedDoc.run,
              progress: updatedDoc.progress || 0,
              chunk_count: updatedDoc.chunk_count,
              token_count: updatedDoc.token_count,
              process_duration: updatedDoc.process_duration,
              parseStatus: getParseStatus(updatedDoc.run)
            }
          }
          return resource
        })
        
        // 检查是否所有正在解析的文档都已完成
        const allDone = documentIds.every(docId => {
          const doc = updatedDocs.find(d => d.id === docId)
          return doc && (doc.run === 'DONE' || doc.run === 'CANCEL')
        })
        
        // 如果当前数据集的所有文档都解析完成，发送通知
        if (allDone && documentIds.length > 0) {
          ElMessage.success(`数据集下的文档解析已全部完成`)
        }
      }
    }
  } catch (error) {
    console.error('更新文档状态出错:', error)
  }
}

// 刷新当前视图
const refreshCurrentView = () => {
  if (leftMenuActive.value === 'recent') {
    loadRecentDocuments()
  } else if (leftMenuActive.value === 'favorite') {
    loadFavoriteDocuments()
  } else if (leftMenuActive.value.startsWith('personal-') || leftMenuActive.value.startsWith('knowledge-')) {
    const id = leftMenuActive.value.split('-')[1]
    loadDocumentsByDataset(id)
  }
  
  // 刷新后立即检查一次当前页面的"解析中"文档状态
  setTimeout(() => {
    updateRunningDocumentsStatus()
  }, 500) // 短暂延时，等待文档加载完成
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
    progressTimer.value = null
  }
  
  // 添加新定时器的清除
  if (documentStatusTimer.value) {
    clearInterval(documentStatusTimer.value)
    documentStatusTimer.value = null
  }
})

// 收藏文档
const toggleFavorite = async (resource) => {
  try {
    // 使用API切换收藏状态
    const categoryId = 1 // 默认使用ID为1的分类，可以根据实际情况修改
    const response = await knowledgeApi.toggleFavorite(resource.id)
    
    if (response.code === 200) {
      // 更新本地收藏状态
      resource.isFavorite = !resource.isFavorite
      
      // 显示操作结果
      ElMessage.success(response.data.message || (resource.isFavorite ? '收藏成功' : '取消收藏成功'))
    } else {
      ElMessage.error('操作收藏失败：' + response.message)
    }
  } catch (error) {
    console.error('操作收藏出错:', error)
    ElMessage.error('操作收藏失败')
  }
}

// 添加获取显示标题的方法
function getDisplayTitle() {
  // 如果是搜索模式，显示搜索结果信息
  if (isSearchMode.value && searchQuery.value) {
    const count = searchResults.value.total || 0;
    return `"${searchQuery.value}" 的搜索结果 (${count} 项)`;
  }
  
  if (leftMenuActive.value === 'recent') {
    return '最近';
  } else if (leftMenuActive.value === 'favorite') {
    return '收藏';
  } else if (leftMenuActive.value.startsWith('personal-')) {
    const categoryId = leftMenuActive.value.split('-')[1];
    const category = myDocCategories.value.find(c => c.id === categoryId);
    return category ? category.name : '我的文档';
  } else if (leftMenuActive.value.startsWith('knowledge-')) {
    const categoryId = leftMenuActive.value.split('-')[1];
    const category = customKnowledgeCategories.value.find(c => c.id === categoryId);
    return category ? category.name : '知识库';
  }
  return '全部资源';
}

// 分类管理
function showAddCategoryModal(type, event) {
  modalMode.value = 'add'
  modalCategoryType.value = type
  modalTitle.value = `添加${type === 'personal' ? '文档分类' : '知识库分类'}`
  categoryInput.value = ''
  
  showCategoryModal.value = true
  
  // 设置弹窗位置
  if (event) {
    const rect = event.target.getBoundingClientRect()
    modalPosition.value = {
      top: `${rect.bottom + 5}px`,
      left: `${rect.left}px`
    }
  }
  
  // 聚焦输入框
  setTimeout(() => {
    if (categoryInputRef.value) {
      categoryInputRef.value.focus()
    }
  }, 100)
}

function editCategory(type, category, event) {
  modalMode.value = 'edit'
  modalCategoryType.value = type
  modalTitle.value = `编辑${type === 'personal' ? '文档分类' : '知识库分类'}`
  categoryInput.value = category.name
  categoryToEdit.value = category
  
  showCategoryModal.value = true
  
  // 设置弹窗位置
  if (event) {
    const rect = event.target.getBoundingClientRect()
    modalPosition.value = {
      top: `${rect.bottom + 5}px`,
      left: `${rect.left}px`
    }
  }
  
  // 聚焦输入框
  setTimeout(() => {
    if (categoryInputRef.value) {
      categoryInputRef.value.focus()
    }
  }, 100)
}

function deleteCategory(type, category, event) {
  categoryToDelete.value = category
  modalCategoryType.value = type
  showDeleteModal.value = true
  
  // 设置弹窗位置
  if (event) {
    const rect = event.target.getBoundingClientRect()
    modalPosition.value = {
      top: `${rect.bottom + 5}px`,
      left: `${rect.left}px`
    }
  }
}

function closeCategoryModal() {
  showCategoryModal.value = false
  categoryToEdit.value = null
}

function closeDeleteModal() {
  showDeleteModal.value = false
  categoryToDelete.value = null
}

async function confirmCategoryModal() {
  if (!categoryInput.value.trim()) return
  
  try {
    loading.value = true
  
    if (modalMode.value === 'add') {
      // 添加分类 - 实际是创建数据集
      const categoryType = modalCategoryType.value
      
      // 确定要添加到哪个大分类
      let categoryId = null
      
      if (categoryType === 'personal') {
        // 获取"个人文档"大分类ID
        const personalCategory = mainCategories.value.find(c => c.category_type === 'personal')
        if (personalCategory) {
          categoryId = personalCategory.id
        }
      } else if (categoryType === 'knowledge') {
        // 获取"知识库"大分类ID
        const knowledgeCategory = mainCategories.value.find(c => c.category_type === 'knowledge')
        if (knowledgeCategory) {
          categoryId = knowledgeCategory.id
        }
      }
      
      if (!categoryId) {
        ElMessage.error('未找到对应的大分类')
        loading.value = false
        return
      }
      
      // 创建数据集
      const datasetData = {
        name: categoryInput.value.trim(),
        description: '通过前端创建的数据集',
        icon: 'Notebook' // 默认图标
      }
      
      const response = await knowledgeApi.createDataset(categoryId, datasetData)
      
      if (response.code === 200) {
        ElMessage.success('创建分类成功')
        
        // 重新获取分类数据
        fetchCategories()
      } else {
        ElMessage.error('创建分类失败：' + response.message)
      }
    } else if (modalMode.value === 'edit' && categoryToEdit.value) {
      // 编辑分类功能
      const datasetId = categoryToEdit.value.id
      const newName = categoryInput.value.trim()
      
      // 如果名称没有变化，则不需要更新
      if (newName === categoryToEdit.value.name) {
        ElMessage.info('分类名称未发生变化')
        loading.value = false
        closeCategoryModal()
        return
      }
      
      // 更新数据集信息
      const datasetData = {
        name: newName,
        description: categoryToEdit.value.description || '用户更新的数据集'
      }
      
      const response = await knowledgeApi.updateDataset(datasetId, datasetData)
      
      if (response.code === 200) {
        ElMessage.success(response.message || '更新分类成功')
        
        // 重新获取分类数据
        fetchCategories()
        
        // 如果当前正在查看这个分类，刷新页面数据
        if (leftMenuActive.value === `${modalCategoryType.value}-${datasetId}`) {
          // 更新左侧菜单显示的分类名称
          if (modalCategoryType.value === 'personal') {
            const category = myDocCategories.value.find(c => c.id === datasetId)
            if (category) {
              category.name = newName
            }
          } else if (modalCategoryType.value === 'knowledge') {
            const category = customKnowledgeCategories.value.find(c => c.id === datasetId)
            if (category) {
              category.name = newName
            }
          }
        }
      } else {
        ElMessage.error(`更新分类失败: ${response.message}`)
      }
    }
  } catch (error) {
    console.error('操作分类出错:', error)
    ElMessage.error('操作分类失败')
  } finally {
    loading.value = false
    closeCategoryModal()
  }
}

async function confirmDeleteCategory() {
  if (!categoryToDelete.value) return
  
  // 目前API中没有提供删除数据集的接口，这里只模拟实现，在后续版本中可以添加实际的删除功能
  try {
    loading.value = true
    
    // 调用API删除数据集
    const response = await knowledgeApi.deleteDataset(categoryToDelete.value.id)
    
    if (response.code === 200) {
      ElMessage.success(response.message || `成功删除分类 "${categoryToDelete.value.name}"`)
      
      // 如果删除的是当前选中的分类，重定向到"最近"
      if (leftMenuActive.value === `${modalCategoryType.value}-${categoryToDelete.value.id}`) {
        selectLeftMenu('recent')
      }
      
      // 重新加载分类数据
      fetchCategories()
    } else {
      ElMessage.error(`删除分类失败: ${response.message}`)
    }
  } catch (error) {
    console.error('删除分类出错:', error)
    ElMessage.error('删除分类失败')
  } finally {
    loading.value = false
    closeDeleteModal()
  }
}

// 获取解析状态的样式
const getStatusColorClass = (status) => {
  const colorMap = {
    '成功': 'bg-green-100 text-green-800',
    '失败': 'bg-red-100 text-red-800',
    '解析中': 'bg-yellow-100 text-yellow-800'
  }
  
  return colorMap[status] || 'bg-gray-100 text-gray-800'
}

// 获取知识库大分类
const fetchCategories = async () => {
  try {
    loading.value = true
    // 获取所有大分类
    const response = await knowledgeApi.getAllCategories()
    if (response.code === 200) {
      // 获取大分类数据
      const categories = response.data
      
      // 保存大分类数据
      mainCategories.value = categories
      
      // 初始化面板状态
      categories.forEach(cat => {
        if (!openPanels.value[cat.category_type]) {
          openPanels.value[cat.category_type] = false
        }
      })
      
      // 按类型分类
      const personalCategories = categories.filter(cat => cat.category_type === 'personal')
      const knowledgeCategories = categories.filter(cat => cat.category_type === 'knowledge')
      
      // 获取个人文档子分类
      await fetchPersonalDatasets(personalCategories)
      
      // 获取知识库子分类
      await fetchKnowledgeDatasets(knowledgeCategories)
    } else {
      ElMessage.error('获取知识库分类失败：' + response.message)
    }
  } catch (error) {
    console.error('获取知识库分类出错:', error)
    ElMessage.error('获取知识库分类数据失败')
  } finally {
    loading.value = false
  }
}

// 获取个人文档子分类
const fetchPersonalDatasets = async (personalCategories) => {
  if (personalCategories.length === 0) return
  
  try {
    // 获取个人文档子分类
    const response = await knowledgeApi.getPersonalDatasets()
    if (response.code === 200) {
      // 更新个人文档子分类数据
      myDocCategories.value = response.data.map(dataset => ({
        id: dataset.id,
        name: dataset.name,
        icon: dataset.icon,
        count: dataset.count || 0,
        categoryId: dataset.category_id,
        categoryType: 'personal'
      }))
    } else {
      ElMessage.error('获取个人文档子分类失败：' + response.message)
    }
  } catch (error) {
    console.error('获取个人文档子分类出错:', error)
  }
}

// 获取知识库子分类
const fetchKnowledgeDatasets = async (knowledgeCategories) => {
  if (knowledgeCategories.length === 0) return
  
  try {
    // 获取每个知识库大分类下的子分类
    let allDatasets = []
    for (const category of knowledgeCategories) {
      const datasetsResponse = await knowledgeApi.getDatasetsByCategory(category.id)
      if (datasetsResponse.code === 200) {
        // 处理该分类下的数据集
        const datasets = datasetsResponse.data.map(dataset => ({
          id: dataset.id,
          name: dataset.name,
          icon: dataset.icon,
          count: dataset.count || 0,
          categoryId: category.id,
          categoryType: category.category_type  // 使用大分类的category_type
        }))
        allDatasets = [...allDatasets, ...datasets]
      }
    }
    
    // 更新自定义知识库分类（数据集）
    customKnowledgeCategories.value = allDatasets
  } catch (error) {
    console.error('获取知识库子分类出错:', error)
  }
}

// 分页处理函数
const handleSizeChange = (value) => {
  pageSize.value = value
  // 当每页显示条数变化时，重置为第一页
  currentPage.value = 1
  // 重新加载数据
  if (isSearchMode.value) {
    performSearch()
  } else {
    refreshCurrentView()
  }
}

const handleCurrentChange = (value) => {
  currentPage.value = value
  // 重新加载数据
  if (isSearchMode.value) {
    performSearch()
  } else {
    refreshCurrentView()
  }
}

// 加载最近文档
const loadRecentDocuments = async () => {
  try {
    loading.value = true
    // 使用当前页码和每页条数获取所有文档
    const response = await knowledgeApi.getAllDocuments(currentPage.value, pageSize.value)
    if (response.code === 200) {
      // 处理文档数据，按时间排序
      const data = response.data
      
      // 文档可能没有dataset_name信息，尝试从文档ID查找数据集名称
      const docs = data.list.map(doc => {
        // 补充默认信息
        return {
          ...doc,
          dataset_name: doc.dataset_name || '最近文档'
        }
      })
      
      resources.value = docs.map(doc => convertApiDocToResource(doc))
      
      // 更新分页数据
      if (data.total !== undefined) {
        totalResources.value = data.total
      }
      
      // 立即检查一次"解析中"文档的状态
      setTimeout(() => {
        updateRunningDocumentsStatus()
      }, 500)
    } else {
      ElMessage.error('获取最近文档失败：' + response.message)
    }
  } catch (error) {
    console.error('获取最近文档出错:', error)
    ElMessage.error('获取最近文档数据失败')
  } finally {
    loading.value = false
  }
}

// 加载收藏文档
const loadFavoriteDocuments = async () => {
  try {
    loading.value = true
    // 使用当前页码和每页条数获取收藏文档
    const response = await knowledgeApi.getFavorites(currentPage.value, pageSize.value)
    if (response.code === 200) {
      // 处理文档数据
      const data = response.data
      const docs = data.list.map(doc => {
        return {
          ...doc,
          dataset_name: doc.dataset_name || '收藏文档'
        }
      })
      
      resources.value = docs.map(doc => convertApiDocToResource(doc, true))
      
      // 更新分页数据
      if (data.total !== undefined) {
        totalResources.value = data.total
      }
      
      // 立即检查一次"解析中"文档的状态
      setTimeout(() => {
        updateRunningDocumentsStatus()
      }, 500)
    } else {
      ElMessage.error('获取收藏文档失败：' + response.message)
    }
  } catch (error) {
    console.error('获取收藏文档出错:', error)
    ElMessage.error('获取收藏文档数据失败')
  } finally {
    loading.value = false
  }
}

// 根据数据集ID加载文档
const loadDocumentsByDataset = async (datasetId) => {
  try {
    loading.value = true
    
    // 获取当前选中的数据集信息，用于后续补充文档数据
    let currentDataset = null
    if (leftMenuActive.value.startsWith('personal-')) {
      currentDataset = myDocCategories.value.find(d => d.id === datasetId)
    } else if (leftMenuActive.value.startsWith('knowledge-')) {
      currentDataset = customKnowledgeCategories.value.find(d => d.id === datasetId)
    }
    
    // 使用当前页码和每页条数获取数据集下的文档
    const response = await knowledgeApi.getDocumentsByDataset(datasetId, currentPage.value, pageSize.value)
    if (response.code === 200) {
      // 处理文档数据，添加所属数据集信息
      const data = response.data
      
      // 处理后端API返回格式的可能差异
      const documentList = data.list || data
      resources.value = documentList.map(doc => {
        // 补充数据集信息
        const enhancedDoc = { 
          ...doc,
          dataset_id: datasetId,
          dataset_name: currentDataset ? currentDataset.name : '未知数据集'
        }
        return convertApiDocToResource(enhancedDoc)
      })
      
      // 更新分页数据
      if (data.total !== undefined) {
        totalResources.value = data.total
      } else {
        // 如果后端API没有返回分页信息，则使用列表长度
        totalResources.value = resources.value.length
      }
      
      // 立即检查一次"解析中"文档的状态
      setTimeout(() => {
        updateRunningDocumentsStatus()
      }, 500)
    } else {
      ElMessage.error('获取数据集文档失败：' + response.message)
    }
  } catch (error) {
    console.error('获取数据集文档出错:', error)
    ElMessage.error('获取数据集文档数据失败')
  } finally {
    loading.value = false
  }
}

// 将API返回的文档数据转换为资源数据格式
const convertApiDocToResource = (doc, isFavorite = false) => {
  // 使用API返回的图标，如果没有则根据文件类型生成
  const icon = doc.icon || getDocumentIcon(doc.type).icon
  
  // 根据文件扩展名获取友好的文档类型名称
  const displayType = getDisplayTypeFromExtension(doc.type)
  
  return {
    id: doc.id,
    name: doc.name,
    type: displayType, // 显示友好的类型名称，而不是文件扩展名
    subject: doc.dataset_name || '未分类', // 如果API返回中没有dataset_name，需要后端补充
    size: doc.size || 0,
    uploadTime: doc.created_at || new Date().toLocaleString(),
    tags: [], // API目前没有返回标签
    folder: doc.dataset_name || '默认文件夹', // 如果API返回中没有dataset_name，需要后端补充
    lastUsed: doc.created_at, // 使用创建时间作为最后使用时间
    parseStatus: getParseStatus(doc.run),
    dataset_id: doc.dataset_id || '', // 使用可选的dataset_id，如果API返回中没有此字段需要补充
    isFavorite: doc.is_favorite !== undefined ? doc.is_favorite : isFavorite, // 优先使用API返回的收藏状态
    icon: icon,
    progress: doc.progress || 0,
    run: doc.run || 'UNSTART',
    // 添加API返回的其他字段，以便在UI中使用
    chunk_count: doc.chunk_count,
    token_count: doc.token_count,
    process_duration: doc.process_duration
  }
}

// 根据文件扩展名获取友好的文档类型名称
const getDisplayTypeFromExtension = (extension) => {
  if (!extension) return '未知';
  
  // 去掉前导点号并转换为小写
  extension = extension.toLowerCase().replace(/^\./, '');
  
  // 文件类型映射
  const typeMap = {
    'doc': '文档',
    'docx': '文档',
    'pdf': '文档',
    'txt': '文本',
    'rtf': '文档',
    'md': '文档',
    
    'xls': '表格',
    'xlsx': '表格',
    'csv': '表格',
    
    'ppt': '演示文稿',
    'pptx': '演示文稿',
    
    'jpg': '图片',
    'jpeg': '图片',
    'png': '图片',
    'gif': '图片',
    'bmp': '图片',
    'svg': '图片',
    
    'mp4': '视频',
    'avi': '视频',
    'mov': '视频',
    'wmv': '视频',
    'flv': '视频',
    'mkv': '视频',
    
    'mp3': '音频',
    'wav': '音频',
    'ogg': '音频',
    'flac': '音频',
    'aac': '音频',
    
    'zip': '压缩包',
    'rar': '压缩包',
    '7z': '压缩包',
    'tar': '压缩包',
    'gz': '压缩包'
  };
  
  return typeMap[extension] || '其他';
}

// 获取解析状态文本
const getParseStatus = (run) => {
  switch (run) {
    case 'DONE': return '成功'
    case 'RUNNING': return '解析中'
    case 'UNSTART': return '未解析'
    case 'CANCEL': return '失败'
    default: return '未知'
  }
}

// 根据文档类型获取图标
const getDocumentIcon = (fileType) => {
  // 转为小写并去除前导点号
  const type = (fileType || '').toLowerCase().replace(/^\./, '')
  
  // 文档类型到图标的映射
  const iconMapping = {
    // 文档类型
    'pdf': 'Document',
    'doc': 'Document',
    'docx': 'Document',
    'txt': 'Document',
    'md': 'Document',
    
    // 表格类型
    'xls': 'Collection',
    'xlsx': 'Collection',
    'csv': 'Collection',
    
    // 演示文稿
    'ppt': 'Monitor',
    'pptx': 'Monitor',
    
    // 图像类型
    'jpg': 'Picture',
    'jpeg': 'Picture',
    'png': 'Picture',
    'gif': 'Picture',
    
    // 代码类型
    'py': 'Cpu',
    'js': 'Cpu',
    'java': 'Cpu',
    'c': 'Cpu',
    'cpp': 'Cpu',
    
    // 链接类型
    'url': 'Link',
    'html': 'Link',
    'htm': 'Link'
  }
  
  return { icon: iconMapping[type] || 'Document' }
}

// 页面加载时获取分类数据
onMounted(() => {
  // 获取知识库分类数据
  fetchCategories()
  
  // 初始加载最近文档
  loadRecentDocuments()
  
  // 启动文档状态监控
  startDocumentStatusMonitor()
})

// 显示上传对话框
const showUploadDialog = () => {
  showUploadModal.value = true
}

// 上传状态
const uploading = ref(false)

// 添加文档状态监控定时器
const documentStatusTimer = ref(null)

// 处理文件拖放
const onFileDrop = (event) => {
  event.preventDefault()
  const droppedFiles = event.dataTransfer.files
  
  if (droppedFiles) {
    for (let i = 0; i < droppedFiles.length; i++) {
      selectedFiles.value.push(droppedFiles[i])
    }
  }
}

// 可用的数据集列表，用于上传时选择
const availableDatasets = computed(() => {
  const personalDatasets = myDocCategories.value.map(cat => ({
    id: cat.id,
    name: cat.name,
    type: 'personal'
  }))
  
  const knowledgeDatasets = customKnowledgeCategories.value.map(cat => ({
    id: cat.id,
    name: cat.name,
    type: 'knowledge'
  }))
  
  return [...personalDatasets, ...knowledgeDatasets]
})

// 解析文档
const parseDocument = async (resource) => {
  try {
    ElMessage.info('正在启动文档解析...')
    
    // 调用解析API
    const response = await knowledgeApi.parseDocuments(resource.dataset_id, [resource.id])
    
    if (response.code === 200) {
      ElMessage.success('文档开始解析，请稍候查看结果')
      
      // 更新资源状态
      resource.parseStatus = '解析中'
      resource.progress = 0
      
      // 监控解析进度
      startParseProgressMonitor(resource.dataset_id, [resource.id])
    } else {
      ElMessage.error('启动文档解析失败：' + response.message)
    }
  } catch (error) {
    console.error('解析文档出错:', error)
    ElMessage.error('解析文档失败')
  }
}

// 下载资源
const downloadResource = (resource) => {
  // 调用API获取文件URL
  knowledgeApi.getDocumentFileUrl(resource.id)
    .then(response => {
      if (response.code === 200 && response.data && response.data.file_url) {
        // 创建一个隐藏的a标签用于下载
        const downloadLink = document.createElement('a');
        downloadLink.href = response.data.file_url;
        // 使用原始文件名
        downloadLink.download = resource.name;
        // 触发点击下载
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      } else {
        ElMessage.error('获取文件下载链接失败');
      }
    })
    .catch(error => {
      console.error('下载文件出错:', error);
      ElMessage.error('下载文件失败');
    });
}

// 删除资源
const deleteResource = async (resource) => {
  try {
    // 确认提示
    await ElMessageBox.confirm(
      `确定要删除文档 "${resource.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    // 调用删除API - 使用批量删除API传入单个ID
    const response = await knowledgeApi.batchDeleteDocuments(resource.dataset_id, [resource.id]);
    
    if (response.code === 200) {
      ElMessage.success('文档删除成功');
      
      // 从当前资源列表中移除被删除的文档
      resources.value = resources.value.filter(doc => doc.id !== resource.id);
    } else {
      ElMessage.error(`删除文档失败: ${response.message}`);
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除，不做任何处理
      return;
    }
    console.error('删除文档出错:', error);
    ElMessage.error('删除文档失败');
  }
}

// 获取背景颜色类名
const getBgColorForType = (type) => {
  const colorMap = {
    '文档': 'bg-blue-600',
    '演示文稿': 'bg-orange-600',
    '表格': 'bg-green-600',
    '图片': 'bg-purple-600',
    '视频': 'bg-red-600',
    '音频': 'bg-pink-600',
    '压缩包': 'bg-gray-600',
    '文本': 'bg-teal-600',
    '其他': 'bg-gray-600'
  }
  
  return colorMap[type] || 'bg-gray-600'
}

// 判断文档是否为最近处理
const isRecentlyProcessed = (resource) => {
  if (!resource.created_at) return false;
  const now = new Date();
  const processDate = new Date(resource.created_at);
  // 24小时内处理的文档视为新文档
  return (now - processDate) < 24 * 60 * 60 * 1000;
}

// 格式化时间间隔
const formatDuration = (seconds) => {
  if (!seconds) return '0秒';
  
  // 对秒数进行取整处理
  seconds = Math.round(seconds);
  
  if (seconds < 60) return `${seconds}秒`;
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes < 60) {
    return remainingSeconds ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0 && remainingSeconds === 0) {
    return `${hours}小时`;
  } else if (remainingSeconds === 0) {
    return `${hours}小时${remainingMinutes}分钟`;
  } else {
    return `${hours}小时${remainingMinutes}分${remainingSeconds}秒`;
  }
}

// 获取相对时间
const getRelativeTime = (dateString) => {
  if (!dateString) return '';
  
  const now = new Date();
  const date = new Date(dateString);
  const diffInMs = now - date;
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    if (diffInHours === 0) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      if (diffInMinutes === 0) {
        return '刚刚';
      }
      return `${diffInMinutes}分钟前`;
    }
    return `${diffInHours}小时前`;
  } else if (diffInDays === 1) {
    return '昨天';
  } else if (diffInDays < 7) {
    return `${diffInDays}天前`;
  } else if (diffInDays < 30) {
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks}周前`;
  } else if (diffInDays < 365) {
    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths}月前`;
  } else {
    const diffInYears = Math.floor(diffInDays / 365);
    return `${diffInYears}年前`;
  }
}

// 全选/取消全选资源
const isAllResourcesSelected = computed(() => {
  return filteredResources.value.length > 0 && 
         filteredResources.value.every(resource => selectedResources.value.includes(resource.id));
});

const toggleSelectAllResources = () => {
  if (isAllResourcesSelected.value) {
    // 取消全选
    selectedResources.value = [];
  } else {
    // 全选
    selectedResources.value = filteredResources.value.map(resource => resource.id);
  }
}

// 文档预览功能
const previewResource = (resource) => {
  // 调用API获取文件URL
  knowledgeApi.getDocumentFileUrl(resource.id)
    .then(response => {
      if (response.code === 200 && response.data && response.data.file_url) {
        // 打开新窗口预览文件
        window.open(response.data.file_url, '_blank');
      } else {
        ElMessage.error('获取文件预览链接失败');
      }
    })
    .catch(error => {
      console.error('预览文件出错:', error);
      ElMessage.error('预览文件失败');
    });
}

// 删除选中资源
const deleteSelectedResources = async () => {
  if (selectedResources.value.length === 0) {
    ElMessage.warning('请先选择要删除的文档')
    return
  }
  
  try {
    // 确认提示
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedResources.value.length} 个文档吗？`,
      '批量删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 获取当前选中资源所属的数据集ID
    // 注意：批量删除要求文档必须属于同一个数据集
    const datasetIds = new Set()
    const selectedDocs = resources.value.filter(resource => selectedResources.value.includes(resource.id))
    
    for (const doc of selectedDocs) {
      if (doc.dataset_id) {
        datasetIds.add(doc.dataset_id)
      }
    }
    
    // 验证是否都属于同一个数据集
    if (datasetIds.size === 0) {
      ElMessage.error('无法获取文档所属的数据集信息')
      return
    } else if (datasetIds.size > 1) {
      ElMessage.error('批量删除仅支持同一数据集下的文档')
      return
    }
    
    // 获取数据集ID
    const datasetId = Array.from(datasetIds)[0]
    
    // 调用批量删除API
    const response = await knowledgeApi.batchDeleteDocuments(datasetId, selectedResources.value)
    
    if (response.code === 200) {
      ElMessage.success(`成功删除 ${response.data.count || selectedResources.value.length} 个文档`)
      
      // 从当前资源列表中移除被删除的文档
      if (response.data.deleted_ids) {
        resources.value = resources.value.filter(doc => !response.data.deleted_ids.includes(doc.id))
      } else {
        resources.value = resources.value.filter(doc => !selectedResources.value.includes(doc.id))
      }
      
      // 清空选中状态
      selectedResources.value = []
    } else {
      ElMessage.error(`批量删除文档失败: ${response.message}`)
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除，不做任何处理
      return
    }
    console.error('批量删除文档出错:', error)
    ElMessage.error('批量删除文档失败')
  }
}

// 切换面板展开/折叠状态
function togglePanel(panel) {
  if (!openPanels.value[panel]) {
    openPanels.value[panel] = true
  } else {
    openPanels.value[panel] = !openPanels.value[panel]
  }
}

function selectLeftMenu(id) { 
  // 切换菜单时重置分页参数
  currentPage.value = 1
  
  leftMenuActive.value = id
  
  // 退出搜索模式
  if (isSearchMode.value) {
    exitSearchMode()
  }
  
  // 根据选择加载相应数据
  if (id === 'recent') {
    // 加载最近文档
    loadRecentDocuments()
  } else if (id === 'favorite') {
    // 加载收藏文档
    loadFavoriteDocuments()
  } else if (id.startsWith('personal-')) {
    // 加载个人文档子分类下的数据
    const datasetId = id.split('-')[1]
    loadDocumentsByDataset(datasetId)
  } else if (id.startsWith('knowledge-')) {
    // 加载知识库子分类下的数据
    const datasetId = id.split('-')[1]
    loadDocumentsByDataset(datasetId)
  }
}
</script>

<style>
.animate-fadeIn {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 6px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a0a0a0;
}
</style> 