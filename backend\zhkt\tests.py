# 主函数用于测试
import os

from zhkt.utils.mineru_utils import MineruPDFUtils

if __name__ == "__main__":
    try:
        print("=" * 50)
        print("Mineru PDF文本提取工具测试")
        print("=" * 50)
        print("请输入要解析的PDF文件路径(完整路径):")
        file_path = input("> ").strip()

        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            exit(1)

        print(f"开始处理文件: {os.path.basename(file_path)}")
        print("解析中，请稍候...")

        # 初始化工具类
        mineru = MineruPDFUtils()

        # 获取文本内容
        text_content = mineru.get_text_from_pdf(
            file_path=file_path,
            is_ocr=True
        )

        print("\n" + "=" * 50)
        print("提取结果:")
        print("-" * 50)

        print(text_content)

        # 询问是否保存文本
        print("\n是否将提取的文本保存到文件? (y/n):")
        save_choice = input("> ").strip().lower()

        if save_choice == 'y':
            output_path = os.path.splitext(file_path)[0] + "_extracted.txt"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)
            print(f"文本已保存到: {output_path}")

        print("测试完成!")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}") 