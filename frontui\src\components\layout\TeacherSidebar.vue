<template>
  <div class="h-full pt-0 mt-0 flex flex-col bg-white border-r w-64 transition-all duration-300" :class="{ 'collapsed': isCollapsed }">
    <!-- Logo and App Title -->
    <div class="flex items-center justify-center h-16 px-4 bg-blue-600">
      <router-link to="/teacher/dashboard" class="flex items-center gap-2">
        <div class="flex items-center justify-center h-8 w-8 rounded-full text-blue-600">
          <img src="/img/zhLogo.png" alt="智慧课堂Logo" class="h-8 w-8 object-contain rounded-full" />
        </div>
        <span class="font-bold text-xl text-white" :class="{ 'hidden': isCollapsed }">智慧课堂</span>
      </router-link>
    </div>

    <!-- Navigation Links -->
    <div class="pt-0 mt-0 flex-1 overflow-y-auto">
      <nav class="px-2 py-4 space-y-1">
        <!-- Regular menu items -->
        <template v-for="link in navLinks" :key="link.id">
          <!-- Items with submenu -->
          <div v-if="link.hasSubMenu" class="nav-item-with-submenu">
            <div 
              class="flex items-center px-2 py-2 text-sm font-medium rounded-md justify-between cursor-pointer" 
              :class="isActiveMenu(link.id) ? 'text-white bg-blue-600' : 'text-gray-600 hover:bg-gray-100'"
              @click="toggleSubmenu(link.id)"
            >
              <div class="flex items-center sidebar-link-icon" :class="isCollapsed ? 'justify-center' : ''">
                <el-icon :class="`text-lg ${isCollapsed ? 'mx-auto' : 'mr-3'}`">
                  <component :is="link.icon" />
                </el-icon>
                <span :class="{ 'hidden': isCollapsed }">{{ link.text }}</span>
              </div>
              <el-icon v-if="!isCollapsed" class="text-sm transition-transform" :class="{ 'transform rotate-180': isSubmenuOpen(link.id) }">
                <ArrowDown />
              </el-icon>
            </div>
            
            <!-- 子菜单项 -->
            <div v-if="isSubmenuOpen(link.id) && !isCollapsed" 
                class="ml-8 mt-1 space-y-1 submenu relative z-30">
              <template v-for="subItem in link.subMenuItems" :key="subItem.id">
                <button
                  type="button"
                  @click.stop="directNavigate(subItem.path, subItem.text, subItem.id)"
                  class="w-full text-left flex items-center px-2 py-1.5 text-sm rounded-md cursor-pointer relative z-40 pointer-events-auto focus:outline-none focus:ring-2 focus:ring-blue-500"
                  :class="isActiveSubMenu(subItem) ? 'text-white bg-blue-600 font-medium' : 'text-gray-600 hover:bg-gray-50'"
                >
                  <div class="sidebar-link-icon">
                    <el-icon class="text-xs mr-2" :class="isActiveSubMenu(subItem) ? 'text-white' : 'text-gray-400'">
                      <MoreFilled />
                    </el-icon>
                  </div>
                  {{ subItem.text }}
                </button>
              </template>
            </div>
          </div>
          
          <!-- Regular menu items without submenu -->
          <router-link 
            v-else
            :to="link.path" 
            class="flex items-center px-2 py-2 text-sm font-medium rounded-md" 
            :class="isActiveMenu(link.id) ? 'text-white bg-blue-600' : 'text-gray-600 hover:bg-gray-100'"
            @click="clearActiveSubmenu"
          >
            <div class="flex items-center sidebar-link-icon" :class="isCollapsed ? 'justify-center' : ''">
              <el-icon :class="`text-lg ${isCollapsed ? 'mx-auto' : 'mr-3'}`">
                <component :is="link.icon" />
              </el-icon>
              <span :class="{ 'hidden': isCollapsed }">{{ link.text }}</span>
            </div>
          </router-link>
        </template>
      </nav>
    </div>

    <!-- Toggle Button -->
    <div class="p-2 border-t border-gray-200">
      <button class="w-full text-gray-600 hover:text-gray-800 text-sm py-1 sidebar-toggle-btn" @click="toggleSidebar">
        <template v-if="!isCollapsed">
          <el-icon class="mr-2"><ArrowLeft /></el-icon>收起菜单
        </template>
        <template v-else>
          <el-icon class="mx-auto"><ArrowRight /></el-icon>
        </template>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  House, 
  Notebook,
  User,
  List,
  GoldMedal,
  Edit,
  Folder,
  Service,
  DataLine,
  School,
  ArrowLeft,
  ArrowRight,
  ArrowDown,
  MoreFilled,
  ChatDotRound
} from '@element-plus/icons-vue'

// Navigation configuration - based on teacher-navigation.js
const navLinks = [
  { id: 'dashboard', path: '/teacher/dashboard', icon: 'House', text: '首页' },
  { id: 'course-management', path: '/teacher/course-management', icon: 'Notebook', text: '课程管理' },
  // { id: 'class-list', path: '/teacher/class-list', icon: 'List', text: '班级管理' },
  // { id: 'student-management', path: '/teacher/student-management', icon: 'User', text: '学生管理' },
  // { id: 'question-bank', path: '/teacher/question-bank', icon: 'Edit', text: '题库管理' },
  // { id: 'assignment-management', path: '/teacher/assignment-management', icon: 'List', text: '作业管理' },
  // { id: 'grade-management', path: '/teacher/grade-management', icon: 'GoldMedal', text: '成绩管理' },
  { id: 'teaching-materials', path: '/teacher/knowledge-base', icon: 'Folder', text: '知识库' },
  { id: 'ai-assistant', path: '/teacher/ai-assistant', icon: 'ChatDotRound', text: 'AI问答' },
  { 
    id: 'content-creation', 
    path: '/teacher/content-creation', 
    icon: 'Edit', 
    text: '工具箱', 
    hasSubMenu: true,
    subMenuItems: [
      // { id: 'textbook', path: '/teacher/textbook-projects', text: '教材制作' },
      { id: 'lesson-plan', path: '/teacher/lesson-plan-projects', text: '教学设计' },
      { id: 'ppt', path: '/teacher/content-ppt-projects', text: 'PPT制作' },
      { id: 'script', path: '/teacher/content-script-projects', text: '讲稿内容' },
      { id: 'my-digital-teacher', path: '/teacher/digital-teacher-management', text: '数字形象' },
      { id: 'my-voice', path: '/teacher/voice-management', text: 'AI语音' },
      { id: 'video', path: '/teacher/content-video-projects', text: '视频生成' },
      { id: 'video-course', path: '/teacher/video-course', text: '视频管理' }
    ]
  }
]

const route = useRoute()
const router = useRouter()
const props = defineProps({
  isCollapsed: Boolean
})
const emit = defineEmits(['toggleCollapse'])

// 使用 localStorage 保存菜单展开状态
const getStoredOpenSubmenus = () => {
  try {
    const stored = localStorage.getItem('teacherSidebarOpenSubmenus')
    return stored ? JSON.parse(stored) : []
  } catch (e) {
    console.error('Error loading stored submenus:', e)
    return []
  }
}

// 获取存储的活动菜单状态
const getStoredActiveMenu = () => {
  try {
    return {
      activeMainMenu: localStorage.getItem('teacherSidebarActiveMainMenu') || '',
      activeSubMenu: localStorage.getItem('teacherSidebarActiveSubMenu') || ''
    }
  } catch (e) {
    console.error('Error loading active menu state:', e)
    return { activeMainMenu: '', activeSubMenu: '' }
  }
}

const openSubmenus = ref(getStoredOpenSubmenus())
const activeMenuState = ref(getStoredActiveMenu())

// 保存菜单展开状态到 localStorage
const saveOpenSubmenus = () => {
  try {
    localStorage.setItem('teacherSidebarOpenSubmenus', JSON.stringify(openSubmenus.value))
  } catch (e) {
    console.error('Error saving submenu state:', e)
  }
}

// 保存活动菜单状态
const saveActiveMenuState = (mainMenuId = null, subMenuId = null) => {
  try {
    if (mainMenuId !== null) {
      localStorage.setItem('teacherSidebarActiveMainMenu', mainMenuId)
      activeMenuState.value.activeMainMenu = mainMenuId
    }
    
    if (subMenuId !== null) {
      localStorage.setItem('teacherSidebarActiveSubMenu', subMenuId)
      activeMenuState.value.activeSubMenu = subMenuId
    }
  } catch (e) {
    console.error('Error saving active menu state:', e)
  }
}

// 清除活动子菜单状态
const clearActiveSubmenu = () => {
  try {
    localStorage.setItem('teacherSidebarActiveSubMenu', '')
    activeMenuState.value.activeSubMenu = ''
    console.log('Cleared active submenu state')
  } catch (e) {
    console.error('Error clearing active submenu state:', e)
  }
}

// Toggle sidebar collapsed state
const toggleSidebar = () => {
  emit('toggleCollapse')
}

// Check if a menu is active
const isActiveMenu = (menuId) => {
  // 首先检查当前路由是否直接匹配此菜单
  const path = route.path
  const menu = navLinks.find(link => link.id === menuId)
  if (!menu) return false

  // 直接 path 匹配
  if (menu.path && path === menu.path) {
    return true
  }
  
  // 子路由匹配
  if (menu.path && menu.path !== '/' && path.startsWith(menu.path + '/')) {
    return true
  }

  // 使用路由元数据检查父级关系
  if (route.meta && route.meta.parentMenu === menuId) {
    return true
  }

  // 检查记忆状态 - 优先使用存储的选中状态
  if (activeMenuState.value.activeMainMenu === menuId) {
    return true
  }

  // 子菜单匹配
  if (menu.hasSubMenu) {
    // 检查是否有匹配的子菜单
    const hasActiveSubItem = menu.subMenuItems.some(subItem => 
      path === subItem.path || path.startsWith(subItem.path + '/') || 
      activeMenuState.value.activeSubMenu === subItem.id
    )
    
    if (hasActiveSubItem) {
      return true
    }
  }

  return false
}

// Check if a submenu item is active
const isActiveSubMenu = (subItem) => {
  const path = route.path
  
  // 优先检查本地存储的状态 - 保持页面内操作的选中状态
  if (activeMenuState.value.activeSubMenu === subItem.id) {
    return true
  }
  
  // 路径匹配检查
  if (path === subItem.path || (subItem.path !== '/' && path.startsWith(subItem.path + '/'))) {
    // 更新存储状态
    const parentMenu = navLinks.find(link => 
      link.hasSubMenu && link.subMenuItems.some(item => item.id === subItem.id)
    )
    if (parentMenu) {
      saveActiveMenuState(parentMenu.id, subItem.id)
    }
    return true
  }
  
  // 使用路由元数据检查
  if (route.meta && route.meta.parentPath === subItem.path) {
    // 更新存储状态
    const parentMenu = navLinks.find(link => 
      link.hasSubMenu && link.subMenuItems.some(item => item.id === subItem.id)
    )
    if (parentMenu) {
      saveActiveMenuState(parentMenu.id, subItem.id)
    }
    return true
  }
  
  return false
}

// Check if submenu should be shown
const isSubmenuOpen = (menuId) => {
  return openSubmenus.value.includes(menuId)
}

// Toggle submenu
const toggleSubmenu = (menuId) => {
  console.log('Toggling submenu:', menuId)
  if (openSubmenus.value.includes(menuId)) {
    openSubmenus.value = openSubmenus.value.filter(id => id !== menuId)
  } else {
    openSubmenus.value.push(menuId)
  }
  // 保存菜单展开状态
  saveOpenSubmenus()
}

// Direct navigation without using router-link
const directNavigate = (path, label, subItemId) => {
  console.log('Direct navigating to:', path, 'Label:', label)
  
  // 找到包含此子菜单项的父菜单
  const parentMenu = navLinks.find(link => 
    link.hasSubMenu && link.subMenuItems.some(item => item.id === subItemId)
  )
  
  // 保存活动菜单状态
  if (parentMenu) {
    saveActiveMenuState(parentMenu.id, subItemId)
    
    // 确保子菜单打开
    if (!openSubmenus.value.includes(parentMenu.id)) {
      openSubmenus.value.push(parentMenu.id)
      saveOpenSubmenus()
    }
  }
  
  // Use router.push for SPA navigation
  router.push(path).catch(err => {
    console.error('Navigation error:', err)
  })
}

// 修改 watch，在路由变化时检查是否需要清除菜单状态
watch(() => route.path, (newPath, oldPath) => {
  // 检查新路由是否为主菜单路由
  const isMainMenuPath = navLinks.some(link => 
    !link.hasSubMenu && (newPath === link.path || newPath.startsWith(link.path + '/'))
  )
  
  // 如果是导航到主菜单，清除子菜单状态
  if (isMainMenuPath) {
    clearActiveSubmenu()
  }
  
  // 找出哪些菜单需要基于新路径展开
  navLinks.forEach(link => {
    if (link.hasSubMenu) {
      const isActive = link.subMenuItems.some(subItem => 
        newPath === subItem.path || newPath.startsWith(subItem.path + '/')
      )
      
      if (isActive && !openSubmenus.value.includes(link.id)) {
        // 打开包含活动路由的子菜单
        openSubmenus.value.push(link.id)
        saveOpenSubmenus()
      }
    }
  })
  
  // 更新活动菜单状态 - 基于新路径
  let foundMatch = false
  
  // 检查子菜单匹配
  for (const link of navLinks) {
    if (link.hasSubMenu) {
      for (const subItem of link.subMenuItems) {
        if (newPath === subItem.path || newPath.startsWith(subItem.path + '/')) {
          saveActiveMenuState(link.id, subItem.id)
          foundMatch = true
          break
        }
      }
      if (foundMatch) break
    }
  }
  
  // 如果没有子菜单匹配，检查主菜单匹配
  if (!foundMatch) {
    for (const link of navLinks) {
      if (newPath === link.path || (link.path !== '/' && newPath.startsWith(link.path + '/'))) {
        saveActiveMenuState(link.id, null)
        clearActiveSubmenu()
        break
      }
    }
  }
}, { immediate: true })

onMounted(() => {
  // 打开与当前路由匹配的子菜单项，但保持其他已展开的子菜单状态
  navLinks.forEach(link => {
    if (link.hasSubMenu) {
      const hasActiveSubItem = link.subMenuItems.some(subItem => 
        route.path === subItem.path
      )
      
      if (hasActiveSubItem && !openSubmenus.value.includes(link.id)) {
        openSubmenus.value.push(link.id)
        saveOpenSubmenus()
      }
      
      // 查找并设置初始活动菜单
      link.subMenuItems.forEach(subItem => {
        // 检查路径匹配
        if (route.path === subItem.path || 
            (subItem.path !== '/' && route.path.startsWith(subItem.path + '/')) || 
            (route.meta && route.meta.parentPath === subItem.path)) {
          saveActiveMenuState(link.id, subItem.id)
        }
      })
    } else if (link.path && (route.path === link.path || 
               (link.path !== '/' && route.path.startsWith(link.path + '/')) ||
               (route.meta && route.meta.parentMenu === link.id))) {
      // 设置主菜单活动状态
      saveActiveMenuState(link.id, null)
      // 清除子菜单选中状态，当主菜单被激活时
      clearActiveSubmenu()
    }
  })
})
</script>

<style scoped>
.collapsed {
  width: 64px !important;
}

.submenu {
  transition: max-height 0.3s ease;
}

.collapsed .sidebar-link-icon {
  margin-left: auto !important;
  margin-right: auto !important;
  display: flex;
  justify-content: center;
}
</style> 