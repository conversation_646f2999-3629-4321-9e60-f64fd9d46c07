# Generated by Django 3.2.20 on 2025-05-10 18:47

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('zhkt', '0029_auto_20250509_1128'),
    ]

    operations = [
        migrations.AddField(
            model_name='course',
            name='average_rating',
            field=models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(5.0)], verbose_name='平均评分'),
        ),
        migrations.AddField(
            model_name='course',
            name='rating_count',
            field=models.PositiveIntegerField(default=0, verbose_name='评分人数'),
        ),
        migrations.CreateModel(
            name='CourseRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(5.0)], verbose_name='评分')),
                ('comment', models.TextField(blank=True, verbose_name='评价')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='zhkt.course', verbose_name='课程')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_ratings', to='zhkt.student', verbose_name='学生')),
            ],
            options={
                'verbose_name': '课程评分',
                'verbose_name_plural': '课程评分',
                'unique_together': {('course', 'student')},
            },
        ),
    ]
